#### [AlifeApi.WebApi](index.md 'index')
### [AlifeApi.WebApi.Schedule](AlifeApi.WebApi.Schedule.md 'AlifeApi.WebApi.Schedule')

## SchedulesModel Class

```csharp
public class SchedulesModel
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; SchedulesModel
### Properties

<a name='AlifeApi.WebApi.Schedule.SchedulesModel.ClassType'></a>

## SchedulesModel.ClassType Property

排程服務類別名稱

```csharp
public string ClassType { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.WebApi.Schedule.SchedulesModel.CycleSeconds'></a>

## SchedulesModel.CycleSeconds Property

排程等待秒數

```csharp
public int CycleSeconds { get; set; }
```

#### Property Value
[System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')

<a name='AlifeApi.WebApi.Schedule.SchedulesModel.Enabld'></a>

## SchedulesModel.Enabld Property

是否啟用

```csharp
public bool Enabld { get; set; }
```

#### Property Value
[System.Boolean](https://docs.microsoft.com/en-us/dotnet/api/System.Boolean 'System.Boolean')

<a name='AlifeApi.WebApi.Schedule.SchedulesModel.Purpose'></a>

## SchedulesModel.Purpose Property

排程用途

```csharp
public string Purpose { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')