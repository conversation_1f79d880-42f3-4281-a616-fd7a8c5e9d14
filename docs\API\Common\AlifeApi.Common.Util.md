#### [AlifeApi.Common](index.md 'index')

## AlifeApi.Common.Util Namespace

| Classes | |
| :--- | :--- |
| [DateOnlyJsonConverter](AlifeApi.Common.Util.DateOnlyJsonConverter.md 'AlifeApi.Common.Util.DateOnlyJsonConverter') | DateOnly 的 JSON 轉換器 |
| [JsonUtils](AlifeApi.Common.Util.JsonUtils.md 'AlifeApi.Common.Util.JsonUtils') | 用於序列化和反序列化 JSON 工具 |
| [NullableDateOnlyJsonConverter](AlifeApi.Common.Util.NullableDateOnlyJsonConverter.md 'AlifeApi.Common.Util.NullableDateOnlyJsonConverter') | Nullable DateOnly 的 JSON 轉換器 |
| [RandomUtils](AlifeApi.Common.Util.RandomUtils.md 'AlifeApi.Common.Util.RandomUtils') | 產生亂數數值工具 |
| [ValueMapper&lt;TSource,TDesc&gt;](AlifeApi.Common.Util.ValueMapper_TSource,TDesc_.md 'AlifeApi.Common.Util.ValueMapper<TSource,TDesc>') | 提供來源與目標物件之間的值映射 |
| [ZipUtils](AlifeApi.Common.Util.ZipUtils.md 'AlifeApi.Common.Util.ZipUtils') | |
| [ZipUtils.FileList](AlifeApi.Common.Util.ZipUtils.FileList.md 'AlifeApi.Common.Util.ZipUtils.FileList') | 內部類別，用於傳遞檔名和檔案內容 |
