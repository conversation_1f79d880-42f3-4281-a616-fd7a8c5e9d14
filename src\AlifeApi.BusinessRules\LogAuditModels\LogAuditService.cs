﻿//using System.Globalization;
//using AlifeApi.BusinessRules.Infrastructure;
//using AlifeApi.Common.Util;
//using AlifeApi.DataAccess.ProjectContext;
//using Microsoft.EntityFrameworkCore;
//using Microsoft.EntityFrameworkCore.Internal;

//namespace AlifeApi.BusinessRules.LogAuditModels
//{
//    /// <summary>
//    /// 日誌稽核管理 Service
//    /// </summary>
//    public class LogAuditService : ServiceBase<ProjectContext>
//    {
//        /// <summary>
//        /// Initializes a new instance of the <see cref="LogAuditService"/> class.
//        /// </summary>
//        /// <param name="serviceProvider">The service provider.</param>
//        /// <param name="dbContext">The database context.</param>
//        public LogAuditService(IServiceProvider serviceProvider, ProjectContext dbContext) : base(serviceProvider, dbContext)
//        {
//        }

//        /// <summary>
//        /// 使用者紀錄按月統計
//        /// </summary>
//        /// <param name="input">The input.</param>
//        /// <returns>統計結果</returns>
//        /// <exception cref="ArgumentNullException">input</exception>
//        public async Task<PagedListOutput<LogAuditMonthStatisticsItemGetOutput>> GetLogAuditMonthStatisticsAsync(
//            LogAuditMonthStatisticsGetInput input)
//        {
//            ArgumentNullException.ThrowIfNull(input, nameof(input));

//            IQueryable<LogAuditMonthStatisticsItemGetOutput> queruable = Db.SysUserRecord.AsNoTracking()
//                .WhereIf(input.StartDate.HasValue, x => x.RecordTime >= input.StartDate)
//                .WhereIf(
//                    input.EndDate.HasValue,
//                    () =>
//                    {
//                        DateTime nextForEndDate = input.EndDate.Value.AddDays(1);
//                        return x => x.RecordTime < nextForEndDate;
//                    }
//                )
//                .WhereIf(input.DeptIds?.Any() == true, x => input.DeptIds.Contains(x.DeptId))
//                .GroupBy(x => new { x.DeptId, x.RecordTime.Year, x.RecordTime.Month })
//                .Select(x => new LogAuditMonthStatisticsItemGetOutput
//                {
//                    Month = x.Key.Year.ToString() + "-" + x.Key.Month.ToString().PadLeft(2, '0'),
//                    DeptId = x.Key.DeptId,
//                    DeptName = x.First().Dept.DeptName,
//                    RecordCount = x.Count(),
//                    UserCount = x.Select(y => y.UserId).Distinct().Count()
//                });

//            if (input.SortOrderInfos.Any(x => x.SortField.Equals("Month", StringComparison.OrdinalIgnoreCase)))
//            {
//                // 排序有 Month 會死，所以轉成 IEnumerable
//                return queruable.AsEnumerable().ToPagedListOutput(input);
//            }

//            return await queruable.ToPagedListOutputAsync(input);
//        }

//        /// <summary>
//        /// 使用者紀錄詳細資料
//        /// </summary>
//        /// <param name="input">The input.</param>
//        /// <returns></returns>
//        /// <exception cref="ArgumentNullException">input</exception>
//        public async Task<PagedListOutput<LogAuditDetailGetOutput>> GetLogAuditDetailsAsync(
//            LogAuditDetailsGetInput input)
//        {
//            ArgumentNullException.ThrowIfNull(input, nameof(input));

//            PagedListOutput<LogAuditDetailGetOutput> output = await Db.SysUserRecord.AsNoTracking()
//                .WhereIf(
//                    !string.IsNullOrEmpty(input.Month),
//                    () =>
//                    {
//                        DateTime date = DateTime.ParseExact(input.Month, "yyyy-MM", CultureInfo.InvariantCulture);
//                        return x => x.RecordTime.Year == date.Year && x.RecordTime.Month == date.Month;
//                    }
//                )
//                .WhereIf(!string.IsNullOrEmpty(input.Dept), x => x.DeptId == input.Dept)
//                .LeftJoin(Db.SysCode, x => new { Type = "Grade", Code = x.GradeCode }, x => new { x.Type, x.Code }, (r, s) => new { UserRecord = r, GradeName = s.CodeDesc })
//                .LeftJoin(Db.SysCode, x => new { Type = "FunctionName", Code = x.UserRecord.RecordEvent }, x => new { x.Type, x.Code }, (r, e) => new { r.UserRecord, r.GradeName, FunctionName = e.CodeDesc })
//                .Select(x => new LogAuditDetailGetOutput
//                {
//                    UserName = x.UserRecord.User.Name,
//                    DeptId = x.UserRecord.DeptId,
//                    DeptName = x.UserRecord.Dept.DeptName,
//                    GradeCode = x.UserRecord.GradeCode,
//                    GradeName = x.GradeName,
//                    IP = x.UserRecord.Ip,
//                    RecordTime = x.UserRecord.RecordTime,
//                    InputData = JsonStringFormat(x.UserRecord.InputData),
//                    FunctionName = x.FunctionName ?? x.UserRecord.RecordEvent
//                })
//                .ToPagedListOutputAsync(input);

//            return output;
//        }

//        private static string JsonStringFormat(string str)
//        {
//            if (string.IsNullOrEmpty(str))
//            {
//                return str;
//            }

//            str = str.Substring(str.IndexOf('{'), str.LastIndexOf('}') - str.IndexOf('{') + 1);

//            try
//            {
//                object obj = JsonUtils.Deserialize<object>(str);
//                return JsonUtils.Serialize(obj, true);
//            }
//            catch
//            {
//                return str;
//            }
//        }
//    }
//}
