#### [<PERSON>feApi.WebApi](index.md 'index')
### [AlifeApi.WebApi.Controllers](AlifeApi.WebApi.Controllers.md 'AlifeApi.WebApi.Controllers')

## UnifiedSalesController Class

統一銷售管理控制器  
提供房屋和車位的統一查詢與管理功能

```csharp
public class UnifiedSalesController : AlifeApi.WebApi.Controllers.AuthenticatedController
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; [Microsoft.AspNetCore.Mvc.ControllerBase](https://docs.microsoft.com/en-us/dotnet/api/Microsoft.AspNetCore.Mvc.ControllerBase 'Microsoft.AspNetCore.Mvc.ControllerBase') &#129106; [Microsoft.AspNetCore.Mvc.Controller](https://docs.microsoft.com/en-us/dotnet/api/Microsoft.AspNetCore.Mvc.Controller 'Microsoft.AspNetCore.Mvc.Controller') &#129106; [AlifeApi.WebApi.Controllers.BasicController](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.WebApi.Controllers.BasicController 'AlifeApi.WebApi.Controllers.BasicController') &#129106; [AlifeApi.WebApi.Controllers.AuthenticatedController](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.WebApi.Controllers.AuthenticatedController 'AlifeApi.WebApi.Controllers.AuthenticatedController') &#129106; UnifiedSalesController
### Methods

<a name='AlifeApi.WebApi.Controllers.UnifiedSalesController.GetAvailableItems(AlifeApi.BusinessRules.CommonModels.UnifiedSalesQueryInput)'></a>

## UnifiedSalesController.GetAvailableItems(UnifiedSalesQueryInput) Method

查詢可售物件列表

```csharp
public System.Threading.Tasks.Task<Microsoft.AspNetCore.Mvc.IActionResult> GetAvailableItems(AlifeApi.BusinessRules.CommonModels.UnifiedSalesQueryInput input);
```
#### Parameters

<a name='AlifeApi.WebApi.Controllers.UnifiedSalesController.GetAvailableItems(AlifeApi.BusinessRules.CommonModels.UnifiedSalesQueryInput).input'></a>

`input` [AlifeApi.BusinessRules.CommonModels.UnifiedSalesQueryInput](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.BusinessRules.CommonModels.UnifiedSalesQueryInput 'AlifeApi.BusinessRules.CommonModels.UnifiedSalesQueryInput')

查詢條件（狀態會自動設為可售）

#### Returns
[System.Threading.Tasks.Task&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')[Microsoft.AspNetCore.Mvc.IActionResult](https://docs.microsoft.com/en-us/dotnet/api/Microsoft.AspNetCore.Mvc.IActionResult 'Microsoft.AspNetCore.Mvc.IActionResult')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')  
狀態為可售的房屋和車位列表

<a name='AlifeApi.WebApi.Controllers.UnifiedSalesController.GetReservedItems(AlifeApi.BusinessRules.CommonModels.UnifiedSalesQueryInput)'></a>

## UnifiedSalesController.GetReservedItems(UnifiedSalesQueryInput) Method

查詢保留物件列表

```csharp
public System.Threading.Tasks.Task<Microsoft.AspNetCore.Mvc.IActionResult> GetReservedItems(AlifeApi.BusinessRules.CommonModels.UnifiedSalesQueryInput input);
```
#### Parameters

<a name='AlifeApi.WebApi.Controllers.UnifiedSalesController.GetReservedItems(AlifeApi.BusinessRules.CommonModels.UnifiedSalesQueryInput).input'></a>

`input` [AlifeApi.BusinessRules.CommonModels.UnifiedSalesQueryInput](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.BusinessRules.CommonModels.UnifiedSalesQueryInput 'AlifeApi.BusinessRules.CommonModels.UnifiedSalesQueryInput')

查詢條件（狀態會自動設為保留）

#### Returns
[System.Threading.Tasks.Task&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')[Microsoft.AspNetCore.Mvc.IActionResult](https://docs.microsoft.com/en-us/dotnet/api/Microsoft.AspNetCore.Mvc.IActionResult 'Microsoft.AspNetCore.Mvc.IActionResult')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')  
狀態為保留的房屋和車位列表

<a name='AlifeApi.WebApi.Controllers.UnifiedSalesController.GetUnifiedSalesList(AlifeApi.BusinessRules.CommonModels.UnifiedSalesQueryInput)'></a>

## UnifiedSalesController.GetUnifiedSalesList(UnifiedSalesQueryInput) Method

查詢統一銷售物件列表

```csharp
public System.Threading.Tasks.Task<Microsoft.AspNetCore.Mvc.IActionResult> GetUnifiedSalesList(AlifeApi.BusinessRules.CommonModels.UnifiedSalesQueryInput input);
```
#### Parameters

<a name='AlifeApi.WebApi.Controllers.UnifiedSalesController.GetUnifiedSalesList(AlifeApi.BusinessRules.CommonModels.UnifiedSalesQueryInput).input'></a>

`input` [AlifeApi.BusinessRules.CommonModels.UnifiedSalesQueryInput](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.BusinessRules.CommonModels.UnifiedSalesQueryInput 'AlifeApi.BusinessRules.CommonModels.UnifiedSalesQueryInput')

查詢條件

#### Returns
[System.Threading.Tasks.Task&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')[Microsoft.AspNetCore.Mvc.IActionResult](https://docs.microsoft.com/en-us/dotnet/api/Microsoft.AspNetCore.Mvc.IActionResult 'Microsoft.AspNetCore.Mvc.IActionResult')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')  
統一的銷售物件列表，包含房屋和車位

<a name='AlifeApi.WebApi.Controllers.UnifiedSalesController.GetUnifiedSalesStatistics(string)'></a>

## UnifiedSalesController.GetUnifiedSalesStatistics(string) Method

查詢案場銷售統計

```csharp
public System.Threading.Tasks.Task<Microsoft.AspNetCore.Mvc.IActionResult> GetUnifiedSalesStatistics(string siteCode);
```
#### Parameters

<a name='AlifeApi.WebApi.Controllers.UnifiedSalesController.GetUnifiedSalesStatistics(string).siteCode'></a>

`siteCode` [System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

案場編號

#### Returns
[System.Threading.Tasks.Task&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')[Microsoft.AspNetCore.Mvc.IActionResult](https://docs.microsoft.com/en-us/dotnet/api/Microsoft.AspNetCore.Mvc.IActionResult 'Microsoft.AspNetCore.Mvc.IActionResult')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')  
案場的房屋和車位銷售統計資料