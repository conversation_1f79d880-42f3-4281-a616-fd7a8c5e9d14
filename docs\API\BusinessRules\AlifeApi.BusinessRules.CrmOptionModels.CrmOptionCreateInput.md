#### [AlifeApi.BusinessRules](index.md 'index')
### [AlifeApi.BusinessRules.CrmOptionModels](AlifeApi.BusinessRules.CrmOptionModels.md 'AlifeApi.BusinessRules.CrmOptionModels')

## CrmOptionCreateInput Class

CRM選項建立輸入模型

```csharp
public class CrmOptionCreateInput
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; CrmOptionCreateInput
### Properties

<a name='AlifeApi.BusinessRules.CrmOptionModels.CrmOptionCreateInput.CrmOptionTypeId'></a>

## CrmOptionCreateInput.CrmOptionTypeId Property

CRM選項類型ID

```csharp
public long CrmOptionTypeId { get; set; }
```

#### Property Value
[System.Int64](https://docs.microsoft.com/en-us/dotnet/api/System.Int64 'System.Int64')

<a name='AlifeApi.BusinessRules.CrmOptionModels.CrmOptionCreateInput.IsActive'></a>

## CrmOptionCreateInput.IsActive Property

是否啟用

```csharp
public bool IsActive { get; set; }
```

#### Property Value
[System.Boolean](https://docs.microsoft.com/en-us/dotnet/api/System.Boolean 'System.Boolean')

<a name='AlifeApi.BusinessRules.CrmOptionModels.CrmOptionCreateInput.OptionValue'></a>

## CrmOptionCreateInput.OptionValue Property

選項值

```csharp
public string OptionValue { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.CrmOptionModels.CrmOptionCreateInput.SiteCode'></a>

## CrmOptionCreateInput.SiteCode Property

案場代碼

```csharp
public string SiteCode { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.CrmOptionModels.CrmOptionCreateInput.SortOrder'></a>

## CrmOptionCreateInput.SortOrder Property

排序順序

```csharp
public int SortOrder { get; set; }
```

#### Property Value
[System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')