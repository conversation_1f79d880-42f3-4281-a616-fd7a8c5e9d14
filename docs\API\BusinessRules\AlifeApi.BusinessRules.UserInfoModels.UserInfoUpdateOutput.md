#### [AlifeApi.BusinessRules](index.md 'index')
### [AlifeApi.BusinessRules.UserInfoModels](AlifeApi.BusinessRules.UserInfoModels.md 'AlifeApi.BusinessRules.UserInfoModels')

## UserInfoUpdateOutput Class

使用者資料異動結果

```csharp
public class UserInfoUpdateOutput :
AlifeApi.BusinessRules.Infrastructure.IApiMessage
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; UserInfoUpdateOutput

Implements [IApiMessage](AlifeApi.BusinessRules.Infrastructure.IApiMessage.md 'AlifeApi.BusinessRules.Infrastructure.IApiMessage')

### See Also
- [IApiMessage](AlifeApi.BusinessRules.Infrastructure.IApiMessage.md 'AlifeApi.BusinessRules.Infrastructure.IApiMessage')
### Properties

<a name='AlifeApi.BusinessRules.UserInfoModels.UserInfoUpdateOutput.Response'></a>

## UserInfoUpdateOutput.Response Property

Gets the response.

```csharp
public System.Enum Response { get; set; }
```

Implements [Response](AlifeApi.BusinessRules.Infrastructure.IApiMessage.md#AlifeApi.BusinessRules.Infrastructure.IApiMessage.Response 'AlifeApi.BusinessRules.Infrastructure.IApiMessage.Response')

#### Property Value
[System.Enum](https://docs.microsoft.com/en-us/dotnet/api/System.Enum 'System.Enum')