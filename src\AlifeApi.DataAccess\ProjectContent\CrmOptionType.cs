﻿using System;
using System.Collections.Generic;

namespace AlifeApi.DataAccess.ProjectContent
{
    /// <summary>
    /// CRM中可由案場自訂的下拉選單類型
    /// </summary>
    public partial class CrmOptionType
    {
        /// <summary>
        /// 主鍵，選項類型唯一識別碼
        /// </summary>
        public long CrmOptionTypeId { get; set; }
        /// <summary>
        /// 選項類型名稱 (例如: 需求坪數, 需求格局, 預算範圍)
        /// </summary>
        public string TypeName { get; set; }
        /// <summary>
        /// 此選項類型的描述，方便後台管理人員理解
        /// </summary>
        public string Description { get; set; }
        /// <summary>
        /// 建立者，記錄創建此資料的員工編號。
        /// </summary>
        public string CreatedUserInfoId { get; set; }
        /// <summary>
        /// 創建時間，記錄資料創建的時間。
        /// </summary>
        public DateTime CreateTime { get; set; }
        /// <summary>
        /// 更新者，記錄最後更新此資料的員工編號。
        /// </summary>
        public string UpdatedUserInfoId { get; set; }
        /// <summary>
        /// 更新時間，記錄資料最後更新的時間。
        /// </summary>
        public DateTime UpdateTime { get; set; }
    }
}
