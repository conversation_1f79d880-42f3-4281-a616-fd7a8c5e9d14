#nullable enable
using System;
using System.Collections.Generic;

namespace AlifeApi.BusinessRules.ReviewModels
{
    /// <summary>
    /// 審核流程更新輸入資料
    /// </summary>
    public class ReviewTaskUpdateInput
    {
        /// <summary>
        /// 任務名稱
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// 任務描述
        /// </summary>
        public string Description { get; set; }

        /// <summary>
        /// 任務狀態，可選值為 enable（啟用）、disable（停用）、deleted（刪除）
        /// </summary>
        public string? Status { get; set; }

        /// <summary>
        /// 截止日期
        /// </summary>
        public DateTime? Deadline { get; set; }

        /// <summary>
        /// 審核步驟清單
        /// </summary>
        public List<ReviewStepInput> Steps { get; set; }
    }
} 
