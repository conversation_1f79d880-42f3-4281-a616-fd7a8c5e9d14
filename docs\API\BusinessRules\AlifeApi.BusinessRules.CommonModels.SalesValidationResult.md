#### [AlifeApi.BusinessRules](index.md 'index')
### [AlifeApi.BusinessRules.CommonModels](AlifeApi.BusinessRules.CommonModels.md 'AlifeApi.BusinessRules.CommonModels')

## SalesValidationResult Class

銷售驗證結果

```csharp
public class SalesValidationResult
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; SalesValidationResult
### Properties

<a name='AlifeApi.BusinessRules.CommonModels.SalesValidationResult.IsValid'></a>

## SalesValidationResult.IsValid Property

是否通過驗證

```csharp
public bool IsValid { get; set; }
```

#### Property Value
[System.Boolean](https://docs.microsoft.com/en-us/dotnet/api/System.Boolean 'System.Boolean')

<a name='AlifeApi.BusinessRules.CommonModels.SalesValidationResult.Messages'></a>

## SalesValidationResult.Messages Property

驗證訊息

```csharp
public System.Collections.Generic.List<string> Messages { get; set; }
```

#### Property Value
[System.Collections.Generic.List&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Collections.Generic.List-1 'System.Collections.Generic.List`1')[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Collections.Generic.List-1 'System.Collections.Generic.List`1')