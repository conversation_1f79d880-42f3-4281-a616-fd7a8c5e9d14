﻿using System;
using System.Collections.Generic;

namespace AlifeApi.DataAccess.ProjectContext
{
    /// <summary>
    /// 組織部門資料
    /// </summary>
    public partial class UserDept
    {
        public UserDept()
        {
            InverseParentDept = new HashSet<UserDept>();
            SysProblemReport = new HashSet<SysProblemReport>();
            SysUserRecord = new HashSet<SysUserRecord>();
            UserDeptPermission = new HashSet<UserDeptPermission>();
            UserInfo = new HashSet<UserInfo>();
        }

        /// <summary>
        /// 部門代碼
        /// </summary>
        public string Id { get; set; }

        /// <summary>
        /// 部門名稱
        /// </summary>
        public string DeptName { get; set; }

        /// <summary>
        /// 上層部門代碼
        /// </summary>
        public string ParentDeptId { get; set; }

        /// <summary>
        /// 部門主管員工編號
        /// </summary>
        public string LeaderUserId { get; set; }

        /// <summary>
        /// 是否刪除
        /// </summary>
        public bool IsDisabled { get; set; }

        /// <summary>
        /// 建立者
        /// </summary>
        public string CreatedUserId { get; set; }

        /// <summary>
        /// 建立時間
        /// </summary>
        public DateTime CreatedTime { get; set; }

        /// <summary>
        /// 更新者
        /// </summary>
        public string UpdatedUserId { get; set; }

        /// <summary>
        /// 更新時間
        /// </summary>
        public DateTime UpdatedTime { get; set; }

        public virtual UserInfo LeaderUser { get; set; }

        public virtual UserDept ParentDept { get; set; }

        public virtual ICollection<UserDept> InverseParentDept { get; set; }

        public virtual ICollection<SysProblemReport> SysProblemReport { get; set; }

        public virtual ICollection<SysUserRecord> SysUserRecord { get; set; }

        public virtual ICollection<UserDeptPermission> UserDeptPermission { get; set; }

        public virtual ICollection<UserInfo> UserInfo { get; set; }

    }
}
