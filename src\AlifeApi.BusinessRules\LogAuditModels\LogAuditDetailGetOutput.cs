﻿using System.Text.Json.Serialization;

namespace AlifeApi.BusinessRules.LogAuditModels
{
    /// <summary>
    /// 日誌稽核明細
    /// </summary>
    public class LogAuditDetailGetOutput
    {
        /// <summary>
        /// 使用者
        /// </summary>
        public string UserName { get; set; }

        /// <summary>
        /// 部門代碼
        /// </summary>
        [JsonPropertyName("Dept")]
        public string DeptId { get; set; }

        /// <summary>
        /// 部門名稱
        /// </summary>
        public string DeptName { get; set; }

        /// <summary>
        /// 職稱代碼
        /// </summary>
        [JsonPropertyName("Grade")]
        public string GradeCode { get; set; }

        /// <summary>
        /// 職稱名稱
        /// </summary>
        public string GradeName { get; set; }

        /// <summary>
        /// IP位址
        /// </summary>
        public string IP { get; set; }

        /// <summary>
        /// 查詢時間
        /// </summary>
        [JsonPropertyName("RecordDateTime")]
        public DateTime RecordTime { get; set; }

        /// <summary>
        /// 查詢條件
        /// </summary>
        public string InputData { get; set; }

        /// <summary>
        /// 事件
        /// </summary>
        public string FunctionName { get; set; }
    }
}
