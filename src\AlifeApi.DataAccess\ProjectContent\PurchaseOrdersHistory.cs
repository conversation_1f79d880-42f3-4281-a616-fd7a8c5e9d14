﻿using System;
using System.Collections.Generic;

namespace AlifeApi.DataAccess.ProjectContent
{
    /// <summary>
    /// 買賣預定單歷史記錄表，主要追蹤業務狀態變更（售、足、簽、請、領）
    /// </summary>
    public partial class PurchaseOrdersHistory
    {
        /// <summary>
        /// 歷史記錄唯一識別碼 (主鍵, 自動遞增)
        /// </summary>
        public int HistoryId { get; set; }
        /// <summary>
        /// 關聯的訂單ID
        /// </summary>
        public int OrderId { get; set; }
        /// <summary>
        /// 操作類型 (例如: STATUS_CHANGE, DATE_UPDATE, CREATE, MODIFY)
        /// </summary>
        public string ActionType { get; set; }
        /// <summary>
        /// 變更前的訂單狀態
        /// </summary>
        public string OldStatus { get; set; }
        /// <summary>
        /// 變更後的訂單狀態
        /// </summary>
        public string NewStatus { get; set; }
        /// <summary>
        /// 詳細記錄內容或備註說明
        /// </summary>
        public string ContentRecord { get; set; }
        /// <summary>
        /// 執行此操作的使用者ID
        /// </summary>
        public string CreatedUserInfoId { get; set; }
        /// <summary>
        /// 操作執行時間
        /// </summary>
        public DateTime CreatedTime { get; set; }
    }
}
