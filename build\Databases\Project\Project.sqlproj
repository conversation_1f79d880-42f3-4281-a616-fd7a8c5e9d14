﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003" ToolsVersion="4.0">
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <Name>Project</Name>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectVersion>4.1</ProjectVersion>
    <ProjectGuid>{a8af5a51-6ece-4314-a99d-f29e22a5acfa}</ProjectGuid>
    <DSP>Microsoft.Data.Tools.Schema.Sql.Sql160DatabaseSchemaProvider</DSP>
    <OutputType>Database</OutputType>
    <RootPath>
    </RootPath>
    <RootNamespace>Project</RootNamespace>
    <AssemblyName>Project</AssemblyName>
    <ModelCollation>1028,CI</ModelCollation>
    <DefaultFileStructure>BySchemaAndSchemaType</DefaultFileStructure>
    <DeployToDatabase>True</DeployToDatabase>
    <TargetFrameworkVersion>v4.7.2</TargetFrameworkVersion>
    <TargetLanguage>CS</TargetLanguage>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <SqlServerVerification>False</SqlServerVerification>
    <IncludeCompositeObjects>True</IncludeCompositeObjects>
    <TargetDatabaseSet>True</TargetDatabaseSet>
    <DefaultCollation>Chinese_Taiwan_Stroke_CI_AS</DefaultCollation>
    <DefaultFilegroup>PRIMARY</DefaultFilegroup>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <OutputPath>bin\Release\</OutputPath>
    <BuildScriptName>$(MSBuildProjectName).sql</BuildScriptName>
    <TreatWarningsAsErrors>False</TreatWarningsAsErrors>
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <DefineDebug>false</DefineDebug>
    <DefineTrace>true</DefineTrace>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <OutputPath>bin\Debug\</OutputPath>
    <BuildScriptName>$(MSBuildProjectName).sql</BuildScriptName>
    <TreatWarningsAsErrors>false</TreatWarningsAsErrors>
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <DefineDebug>true</DefineDebug>
    <DefineTrace>true</DefineTrace>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup>
    <VisualStudioVersion Condition="'$(VisualStudioVersion)' == ''">11.0</VisualStudioVersion>
    <!-- Default to the v11.0 targets path if the targets file for the current VS version is not found -->
    <SSDTExists Condition="Exists('$(MSBuildExtensionsPath)\Microsoft\VisualStudio\v$(VisualStudioVersion)\SSDT\Microsoft.Data.Tools.Schema.SqlTasks.targets')">True</SSDTExists>
    <VisualStudioVersion Condition="'$(SSDTExists)' == ''">11.0</VisualStudioVersion>
  </PropertyGroup>
  <Import Condition="'$(SQLDBExtensionsRefPath)' != ''" Project="$(SQLDBExtensionsRefPath)\Microsoft.Data.Tools.Schema.SqlTasks.targets" />
  <Import Condition="'$(SQLDBExtensionsRefPath)' == ''" Project="$(MSBuildExtensionsPath)\Microsoft\VisualStudio\v$(VisualStudioVersion)\SSDT\Microsoft.Data.Tools.Schema.SqlTasks.targets" />
  <ItemGroup>
    <Folder Include="Properties" />
    <Folder Include="dbo\" />
    <Folder Include="dbo\Tables\" />
    <Folder Include="Migrations" />
    <Folder Include="SeedData" />
  </ItemGroup>
  <ItemGroup>
    <Build Include="dbo\Tables\SYS_RoleGroup.sql" />
    <Build Include="dbo\Tables\SYS_ProblemReport.sql" />
    <Build Include="dbo\Tables\SYS_Code.sql" />
    <Build Include="dbo\Tables\UserPasswordHistory.sql" />
    <Build Include="dbo\Tables\UserDept.sql" />
    <Build Include="dbo\Tables\SYS_Type.sql" />
    <Build Include="dbo\Tables\SYS_RoleGroupPermission.sql" />
    <Build Include="dbo\Tables\SYS_SystemSetting.sql" />
    <Build Include="dbo\Tables\SYS_MenuFunc.sql" />
    <Build Include="dbo\Tables\SYS_UserRecord.sql" />
    <Build Include="dbo\Tables\UserInfo.sql" />
    <Build Include="dbo\Tables\SYS_RoleGroupUser.sql" />
    <Build Include="dbo\Tables\UserGradePermission.sql" />
    <Build Include="dbo\Tables\UserGrade.sql" />
    <Build Include="dbo\Tables\UserDeptPermission.sql" />
    <Build Include="dbo\Tables\SYS_ScheduleHistory.sql" />
    <Build Include="dbo\Tables\SYS_ProjectVersion.sql" />
    <Build Include="dbo\Tables\SYS_BulletinsClickRecord.sql" />
    <Build Include="dbo\Tables\SYS_BulletinsAttachDownloadRecord.sql" />
    <Build Include="dbo\Tables\SYS_BulletinsAttach.sql" />
    <Build Include="dbo\Tables\SYS_Bulletins.sql" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Migrations\v0.0.0.sql" />
    <None Include="SeedData\Script.sql" />
  </ItemGroup>
</Project>