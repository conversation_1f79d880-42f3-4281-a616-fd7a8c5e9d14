#### [AlifeApi.BusinessRules](index.md 'index')
### [AlifeApi.BusinessRules.DailyReportModels](AlifeApi.BusinessRules.DailyReportModels.md 'AlifeApi.BusinessRules.DailyReportModels')

## DailyReportService Class

日報統計服務

```csharp
public class DailyReportService : AlifeApi.BusinessRules.Infrastructure.ServiceBase<AlifeApi.DataAccess.ProjectContent.alifeContext>
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; [AlifeApi.Common.InfrastructureBase](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.Common.InfrastructureBase 'AlifeApi.Common.InfrastructureBase') &#129106; [AlifeApi.BusinessRules.Infrastructure.ServiceBase&lt;](AlifeApi.BusinessRules.Infrastructure.ServiceBase_TDbContext_.md 'AlifeApi.BusinessRules.Infrastructure.ServiceBase<TDbContext>')[AlifeApi.DataAccess.ProjectContent.alifeContext](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.DataAccess.ProjectContent.alifeContext 'AlifeApi.DataAccess.ProjectContent.alifeContext')[&gt;](AlifeApi.BusinessRules.Infrastructure.ServiceBase_TDbContext_.md 'AlifeApi.BusinessRules.Infrastructure.ServiceBase<TDbContext>') &#129106; DailyReportService
### Constructors

<a name='AlifeApi.BusinessRules.DailyReportModels.DailyReportService.DailyReportService(System.IServiceProvider,AlifeApi.DataAccess.ProjectContent.alifeContext)'></a>

## DailyReportService(IServiceProvider, alifeContext) Constructor

建構函數

```csharp
public DailyReportService(System.IServiceProvider serviceProvider, AlifeApi.DataAccess.ProjectContent.alifeContext dbContext);
```
#### Parameters

<a name='AlifeApi.BusinessRules.DailyReportModels.DailyReportService.DailyReportService(System.IServiceProvider,AlifeApi.DataAccess.ProjectContent.alifeContext).serviceProvider'></a>

`serviceProvider` [System.IServiceProvider](https://docs.microsoft.com/en-us/dotnet/api/System.IServiceProvider 'System.IServiceProvider')

<a name='AlifeApi.BusinessRules.DailyReportModels.DailyReportService.DailyReportService(System.IServiceProvider,AlifeApi.DataAccess.ProjectContent.alifeContext).dbContext'></a>

`dbContext` [AlifeApi.DataAccess.ProjectContent.alifeContext](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.DataAccess.ProjectContent.alifeContext 'AlifeApi.DataAccess.ProjectContent.alifeContext')
### Methods

<a name='AlifeApi.BusinessRules.DailyReportModels.DailyReportService.CalculatePeriodSummary(System.DateTime,System.DateTime,System.Collections.Generic.List_AlifeApi.BusinessRules.DailyReportModels.DailyReportSummary_)'></a>

## DailyReportService.CalculatePeriodSummary(DateTime, DateTime, List<DailyReportSummary>) Method

計算期間總計統計

```csharp
private static AlifeApi.BusinessRules.DailyReportModels.DailyReportPeriodSummary CalculatePeriodSummary(System.DateTime startDate, System.DateTime endDate, System.Collections.Generic.List<AlifeApi.BusinessRules.DailyReportModels.DailyReportSummary> reports);
```
#### Parameters

<a name='AlifeApi.BusinessRules.DailyReportModels.DailyReportService.CalculatePeriodSummary(System.DateTime,System.DateTime,System.Collections.Generic.List_AlifeApi.BusinessRules.DailyReportModels.DailyReportSummary_).startDate'></a>

`startDate` [System.DateTime](https://docs.microsoft.com/en-us/dotnet/api/System.DateTime 'System.DateTime')

<a name='AlifeApi.BusinessRules.DailyReportModels.DailyReportService.CalculatePeriodSummary(System.DateTime,System.DateTime,System.Collections.Generic.List_AlifeApi.BusinessRules.DailyReportModels.DailyReportSummary_).endDate'></a>

`endDate` [System.DateTime](https://docs.microsoft.com/en-us/dotnet/api/System.DateTime 'System.DateTime')

<a name='AlifeApi.BusinessRules.DailyReportModels.DailyReportService.CalculatePeriodSummary(System.DateTime,System.DateTime,System.Collections.Generic.List_AlifeApi.BusinessRules.DailyReportModels.DailyReportSummary_).reports'></a>

`reports` [System.Collections.Generic.List&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Collections.Generic.List-1 'System.Collections.Generic.List`1')[DailyReportSummary](AlifeApi.BusinessRules.DailyReportModels.DailyReportSummary.md 'AlifeApi.BusinessRules.DailyReportModels.DailyReportSummary')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Collections.Generic.List-1 'System.Collections.Generic.List`1')

#### Returns
[DailyReportPeriodSummary](AlifeApi.BusinessRules.DailyReportModels.DailyReportPeriodSummary.md 'AlifeApi.BusinessRules.DailyReportModels.DailyReportPeriodSummary')

<a name='AlifeApi.BusinessRules.DailyReportModels.DailyReportService.GetChineseDayOfWeek(System.DateTime)'></a>

## DailyReportService.GetChineseDayOfWeek(DateTime) Method

取得中文星期顯示

```csharp
private static string GetChineseDayOfWeek(System.DateTime date);
```
#### Parameters

<a name='AlifeApi.BusinessRules.DailyReportModels.DailyReportService.GetChineseDayOfWeek(System.DateTime).date'></a>

`date` [System.DateTime](https://docs.microsoft.com/en-us/dotnet/api/System.DateTime 'System.DateTime')

#### Returns
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.DailyReportModels.DailyReportService.GetDailyCustomerDetailsAsync(string,System.DateTime,System.DateTime)'></a>

## DailyReportService.GetDailyCustomerDetailsAsync(string, DateTime, DateTime) Method

取得當日客戶明細

```csharp
private System.Threading.Tasks.Task<System.Collections.Generic.List<AlifeApi.BusinessRules.DailyReportModels.DailyCustomerDetail>> GetDailyCustomerDetailsAsync(string siteCode, System.DateTime reportDate, System.DateTime nextDate);
```
#### Parameters

<a name='AlifeApi.BusinessRules.DailyReportModels.DailyReportService.GetDailyCustomerDetailsAsync(string,System.DateTime,System.DateTime).siteCode'></a>

`siteCode` [System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.DailyReportModels.DailyReportService.GetDailyCustomerDetailsAsync(string,System.DateTime,System.DateTime).reportDate'></a>

`reportDate` [System.DateTime](https://docs.microsoft.com/en-us/dotnet/api/System.DateTime 'System.DateTime')

<a name='AlifeApi.BusinessRules.DailyReportModels.DailyReportService.GetDailyCustomerDetailsAsync(string,System.DateTime,System.DateTime).nextDate'></a>

`nextDate` [System.DateTime](https://docs.microsoft.com/en-us/dotnet/api/System.DateTime 'System.DateTime')

#### Returns
[System.Threading.Tasks.Task&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')[System.Collections.Generic.List&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Collections.Generic.List-1 'System.Collections.Generic.List`1')[DailyCustomerDetail](AlifeApi.BusinessRules.DailyReportModels.DailyCustomerDetail.md 'AlifeApi.BusinessRules.DailyReportModels.DailyCustomerDetail')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Collections.Generic.List-1 'System.Collections.Generic.List`1')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')

<a name='AlifeApi.BusinessRules.DailyReportModels.DailyReportService.GetDailyReportAsync(AlifeApi.BusinessRules.DailyReportModels.DailyReportQueryInput)'></a>

## DailyReportService.GetDailyReportAsync(DailyReportQueryInput) Method

取得單日日報統計

```csharp
public System.Threading.Tasks.Task<AlifeApi.BusinessRules.DailyReportModels.DailyReportOutput> GetDailyReportAsync(AlifeApi.BusinessRules.DailyReportModels.DailyReportQueryInput input);
```
#### Parameters

<a name='AlifeApi.BusinessRules.DailyReportModels.DailyReportService.GetDailyReportAsync(AlifeApi.BusinessRules.DailyReportModels.DailyReportQueryInput).input'></a>

`input` [DailyReportQueryInput](AlifeApi.BusinessRules.DailyReportModels.DailyReportQueryInput.md 'AlifeApi.BusinessRules.DailyReportModels.DailyReportQueryInput')

查詢條件

#### Returns
[System.Threading.Tasks.Task&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')[DailyReportOutput](AlifeApi.BusinessRules.DailyReportModels.DailyReportOutput.md 'AlifeApi.BusinessRules.DailyReportModels.DailyReportOutput')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')  
日報統計結果

<a name='AlifeApi.BusinessRules.DailyReportModels.DailyReportService.GetDailyReportListAsync(AlifeApi.BusinessRules.DailyReportModels.DailyReportPeriodQueryInput)'></a>

## DailyReportService.GetDailyReportListAsync(DailyReportPeriodQueryInput) Method

取得期間日報統計列表

```csharp
public System.Threading.Tasks.Task<AlifeApi.BusinessRules.DailyReportModels.DailyReportListOutput> GetDailyReportListAsync(AlifeApi.BusinessRules.DailyReportModels.DailyReportPeriodQueryInput input);
```
#### Parameters

<a name='AlifeApi.BusinessRules.DailyReportModels.DailyReportService.GetDailyReportListAsync(AlifeApi.BusinessRules.DailyReportModels.DailyReportPeriodQueryInput).input'></a>

`input` [DailyReportPeriodQueryInput](AlifeApi.BusinessRules.DailyReportModels.DailyReportPeriodQueryInput.md 'AlifeApi.BusinessRules.DailyReportModels.DailyReportPeriodQueryInput')

查詢條件

#### Returns
[System.Threading.Tasks.Task&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')[DailyReportListOutput](AlifeApi.BusinessRules.DailyReportModels.DailyReportListOutput.md 'AlifeApi.BusinessRules.DailyReportModels.DailyReportListOutput')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')  
期間日報統計

<a name='AlifeApi.BusinessRules.DailyReportModels.DailyReportService.GetDateRange(System.DateTime,System.DateTime)'></a>

## DailyReportService.GetDateRange(DateTime, DateTime) Method

產生日期範圍

```csharp
private static System.Collections.Generic.List<System.DateTime> GetDateRange(System.DateTime startDate, System.DateTime endDate);
```
#### Parameters

<a name='AlifeApi.BusinessRules.DailyReportModels.DailyReportService.GetDateRange(System.DateTime,System.DateTime).startDate'></a>

`startDate` [System.DateTime](https://docs.microsoft.com/en-us/dotnet/api/System.DateTime 'System.DateTime')

<a name='AlifeApi.BusinessRules.DailyReportModels.DailyReportService.GetDateRange(System.DateTime,System.DateTime).endDate'></a>

`endDate` [System.DateTime](https://docs.microsoft.com/en-us/dotnet/api/System.DateTime 'System.DateTime')

#### Returns
[System.Collections.Generic.List&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Collections.Generic.List-1 'System.Collections.Generic.List`1')[System.DateTime](https://docs.microsoft.com/en-us/dotnet/api/System.DateTime 'System.DateTime')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Collections.Generic.List-1 'System.Collections.Generic.List`1')