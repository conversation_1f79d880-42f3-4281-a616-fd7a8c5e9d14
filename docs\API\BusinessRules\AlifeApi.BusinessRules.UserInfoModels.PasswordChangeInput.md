#### [AlifeApi.BusinessRules](index.md 'index')
### [AlifeApi.BusinessRules.UserInfoModels](AlifeApi.BusinessRules.UserInfoModels.md 'AlifeApi.BusinessRules.UserInfoModels')

## PasswordChangeInput Class

變更密碼 Request 資料模型

```csharp
public class PasswordChangeInput
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; PasswordChangeInput
### Properties

<a name='AlifeApi.BusinessRules.UserInfoModels.PasswordChangeInput.ChangePw'></a>

## PasswordChangeInput.ChangePw Property

修改密碼

```csharp
public string ChangePw { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.UserInfoModels.PasswordChangeInput.UserId'></a>

## PasswordChangeInput.UserId Property

使用者帳號唯一識別值

```csharp
public string UserId { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')