﻿using System;
using System.Collections.Generic;

namespace AlifeApi.DataAccess.ProjectContent
{
    /// <summary>
    /// 業主基本資料表
    /// </summary>
    public partial class Owner
    {
        /// <summary>
        /// 業主唯一識別碼 (自動增長)
        /// </summary>
        public int OwnerId { get; set; }
        /// <summary>
        /// 公司名稱
        /// </summary>
        public string CompanyName { get; set; }
        /// <summary>
        /// 負責人姓名
        /// </summary>
        public string ResponsiblePerson { get; set; }
        /// <summary>
        /// 公司登記電話
        /// </summary>
        public string CompanyPhone { get; set; }
        /// <summary>
        /// 公司登記地址
        /// </summary>
        public string CompanyAddress { get; set; }
        /// <summary>
        /// 郵件通訊地址 (若與公司地址不同)
        /// </summary>
        public string MailingAddress { get; set; }
        /// <summary>
        /// 主要聯絡窗口人員姓名
        /// </summary>
        public string ContactPerson { get; set; }
        /// <summary>
        /// 聯絡電子郵件地址
        /// </summary>
        public string Email { get; set; }
        /// <summary>
        /// 人別 (值為 法人 或 自然人)
        /// </summary>
        public string PersonType { get; set; }
        /// <summary>
        /// 證號 (依人別決定是 統一編號 或 身分證ID)
        /// </summary>
        public string IdentificationNumber { get; set; }
        /// <summary>
        /// 主要聯絡電話
        /// </summary>
        public string ContactPhone1 { get; set; }
        /// <summary>
        /// 次要聯絡電話 (備用)
        /// </summary>
        public string ContactPhone2 { get; set; }
    }
}
