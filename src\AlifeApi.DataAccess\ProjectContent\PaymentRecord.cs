﻿using System;
using System.Collections.Generic;

namespace AlifeApi.DataAccess.ProjectContent
{
    /// <summary>
    /// 記錄針對某一筆 PurchaseOrder 的分期收款明細。
    /// </summary>
    public partial class PaymentRecord
    {
        /// <summary>
        /// 收款記錄唯一識別碼 (主鍵, 自動遞增)。
        /// </summary>
        public int PaymentRecordId { get; set; }
        /// <summary>
        /// 關聯的訂單 ID (參考 PurchaseOrders 表)。
        /// </summary>
        public int OrderId { get; set; }
        /// <summary>
        /// 實際收款的日期 (或預計收款日)。
        /// </summary>
        public DateOnly PaymentDate { get; set; }
        /// <summary>
        /// 本次收款的金額 (新台幣)。
        /// </summary>
        public decimal Amount { get; set; }
        /// <summary>
        /// 本次收款的款項類別 (例如: 定金, 簽約金, 工程款 - 建議關聯 SYS_Code)。
        /// </summary>
        public string PaymentType { get; set; }
        /// <summary>
        /// 本次收款的方式 (例如: 現金, 匯款, 支票 - 建議關lungen SYS_Code)。
        /// </summary>
        public string PaymentMethod { get; set; }
        /// <summary>
        /// 支票號碼 (如果付款方式是支票)。
        /// </summary>
        public string CheckNumber { get; set; }
        /// <summary>
        /// 支票到期日 (如果付款方式是支票)。
        /// </summary>
        public DateOnly? CheckDueDate { get; set; }
        /// <summary>
        /// 匯款銀行或支票開立銀行。
        /// </summary>
        public string BankName { get; set; }
        /// <summary>
        /// 匯款帳號末五碼或支票帳號。
        /// </summary>
        public string BankAccountNumber { get; set; }
        /// <summary>
        /// 相關檔案 (如收據掃描檔、匯款單) 的儲存路徑。
        /// </summary>
        public string AttachmentPath { get; set; }
        /// <summary>
        /// 收款備註 (例如：特定期數說明)。
        /// </summary>
        public string Remarks { get; set; }
        /// <summary>
        /// 標記此筆款項是否已確認入帳/兌現 (True/False)。
        /// </summary>
        public bool? IsReceived { get; set; }
        /// <summary>
        /// 本次收款可能產生的手續費 (例如刷卡手續費，可選)。
        /// </summary>
        public decimal? HandlingFee { get; set; }
        /// <summary>
        /// 該筆款項是否已轉交給建設公司 (True/False，可選)。
        /// </summary>
        public bool? TransferredToDeveloper { get; set; }
        /// <summary>
        /// 記錄創建時間 (TIMESTAMP WITHOUT TIME ZONE)。
        /// </summary>
        public DateTime CreatedTime { get; set; }
        /// <summary>
        /// 記錄最後更新時間 (TIMESTAMP WITHOUT TIME ZONE)。
        /// </summary>
        public DateTime UpdatedTime { get; set; }
        /// <summary>
        /// 建立此收款記錄的人員ID (參考 UserInfo 表)。
        /// </summary>
        public string CreatedUserInfoId { get; set; }
        /// <summary>
        /// 最後更新此收款記錄的人員ID (參考 UserInfo 表)。
        /// </summary>
        public string UpdatedUserInfoId { get; set; }
    }
}
