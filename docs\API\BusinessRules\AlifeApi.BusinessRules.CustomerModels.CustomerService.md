#### [AlifeApi.BusinessRules](index.md 'index')
### [AlifeApi.BusinessRules.CustomerModels](AlifeApi.BusinessRules.CustomerModels.md 'AlifeApi.BusinessRules.CustomerModels')

## CustomerService Class

客戶服務

```csharp
public class CustomerService : AlifeApi.BusinessRules.Infrastructure.ServiceBase<AlifeApi.DataAccess.ProjectContent.alifeContext>
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; [AlifeApi.Common.InfrastructureBase](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.Common.InfrastructureBase 'AlifeApi.Common.InfrastructureBase') &#129106; [AlifeApi.BusinessRules.Infrastructure.ServiceBase&lt;](AlifeApi.BusinessRules.Infrastructure.ServiceBase_TDbContext_.md 'AlifeApi.BusinessRules.Infrastructure.ServiceBase<TDbContext>')[AlifeApi.DataAccess.ProjectContent.alifeContext](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.DataAccess.ProjectContent.alifeContext 'AlifeApi.DataAccess.ProjectContent.alifeContext')[&gt;](AlifeApi.BusinessRules.Infrastructure.ServiceBase_TDbContext_.md 'AlifeApi.BusinessRules.Infrastructure.ServiceBase<TDbContext>') &#129106; CustomerService
### Constructors

<a name='AlifeApi.BusinessRules.CustomerModels.CustomerService.CustomerService(System.IServiceProvider,AlifeApi.DataAccess.ProjectContent.alifeContext,AlifeApi.BusinessRules.Infrastructure.IImageStorageService,Microsoft.Extensions.Configuration.IConfiguration)'></a>

## CustomerService(IServiceProvider, alifeContext, IImageStorageService, IConfiguration) Constructor

建構函數

```csharp
public CustomerService(System.IServiceProvider serviceProvider, AlifeApi.DataAccess.ProjectContent.alifeContext dbContext, AlifeApi.BusinessRules.Infrastructure.IImageStorageService imageStorageService, Microsoft.Extensions.Configuration.IConfiguration configuration);
```
#### Parameters

<a name='AlifeApi.BusinessRules.CustomerModels.CustomerService.CustomerService(System.IServiceProvider,AlifeApi.DataAccess.ProjectContent.alifeContext,AlifeApi.BusinessRules.Infrastructure.IImageStorageService,Microsoft.Extensions.Configuration.IConfiguration).serviceProvider'></a>

`serviceProvider` [System.IServiceProvider](https://docs.microsoft.com/en-us/dotnet/api/System.IServiceProvider 'System.IServiceProvider')

<a name='AlifeApi.BusinessRules.CustomerModels.CustomerService.CustomerService(System.IServiceProvider,AlifeApi.DataAccess.ProjectContent.alifeContext,AlifeApi.BusinessRules.Infrastructure.IImageStorageService,Microsoft.Extensions.Configuration.IConfiguration).dbContext'></a>

`dbContext` [AlifeApi.DataAccess.ProjectContent.alifeContext](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.DataAccess.ProjectContent.alifeContext 'AlifeApi.DataAccess.ProjectContent.alifeContext')

<a name='AlifeApi.BusinessRules.CustomerModels.CustomerService.CustomerService(System.IServiceProvider,AlifeApi.DataAccess.ProjectContent.alifeContext,AlifeApi.BusinessRules.Infrastructure.IImageStorageService,Microsoft.Extensions.Configuration.IConfiguration).imageStorageService'></a>

`imageStorageService` [IImageStorageService](AlifeApi.BusinessRules.Infrastructure.IImageStorageService.md 'AlifeApi.BusinessRules.Infrastructure.IImageStorageService')

<a name='AlifeApi.BusinessRules.CustomerModels.CustomerService.CustomerService(System.IServiceProvider,AlifeApi.DataAccess.ProjectContent.alifeContext,AlifeApi.BusinessRules.Infrastructure.IImageStorageService,Microsoft.Extensions.Configuration.IConfiguration).configuration'></a>

`configuration` [Microsoft.Extensions.Configuration.IConfiguration](https://docs.microsoft.com/en-us/dotnet/api/Microsoft.Extensions.Configuration.IConfiguration 'Microsoft.Extensions.Configuration.IConfiguration')
### Methods

<a name='AlifeApi.BusinessRules.CustomerModels.CustomerService.CreateCustomerAsync(AlifeApi.BusinessRules.CustomerModels.CustomerInput)'></a>

## CustomerService.CreateCustomerAsync(CustomerInput) Method

新增客戶

```csharp
public System.Threading.Tasks.Task<string> CreateCustomerAsync(AlifeApi.BusinessRules.CustomerModels.CustomerInput input);
```
#### Parameters

<a name='AlifeApi.BusinessRules.CustomerModels.CustomerService.CreateCustomerAsync(AlifeApi.BusinessRules.CustomerModels.CustomerInput).input'></a>

`input` [CustomerInput](AlifeApi.BusinessRules.CustomerModels.CustomerInput.md 'AlifeApi.BusinessRules.CustomerModels.CustomerInput')

新增客戶輸入資料

#### Returns
[System.Threading.Tasks.Task&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')

<a name='AlifeApi.BusinessRules.CustomerModels.CustomerService.DeleteCustomerAsync(string)'></a>

## CustomerService.DeleteCustomerAsync(string) Method

刪除客戶

```csharp
public System.Threading.Tasks.Task DeleteCustomerAsync(string customerId);
```
#### Parameters

<a name='AlifeApi.BusinessRules.CustomerModels.CustomerService.DeleteCustomerAsync(string).customerId'></a>

`customerId` [System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

客戶ID

#### Returns
[System.Threading.Tasks.Task](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task 'System.Threading.Tasks.Task')

<a name='AlifeApi.BusinessRules.CustomerModels.CustomerService.DeleteCustomerRecordAsync(string,int)'></a>

## CustomerService.DeleteCustomerRecordAsync(string, int) Method

刪除特定客戶的單筆訪談紀錄

```csharp
public System.Threading.Tasks.Task DeleteCustomerRecordAsync(string customerId, int recordId);
```
#### Parameters

<a name='AlifeApi.BusinessRules.CustomerModels.CustomerService.DeleteCustomerRecordAsync(string,int).customerId'></a>

`customerId` [System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

客戶ID

<a name='AlifeApi.BusinessRules.CustomerModels.CustomerService.DeleteCustomerRecordAsync(string,int).recordId'></a>

`recordId` [System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')

訪談紀錄ID

#### Returns
[System.Threading.Tasks.Task](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task 'System.Threading.Tasks.Task')

<a name='AlifeApi.BusinessRules.CustomerModels.CustomerService.GetCustomerByIdAsync(string)'></a>

## CustomerService.GetCustomerByIdAsync(string) Method

根據客戶ID取得單一客戶資訊

```csharp
public System.Threading.Tasks.Task<AlifeApi.BusinessRules.CustomerModels.CustomerOutput> GetCustomerByIdAsync(string customerId);
```
#### Parameters

<a name='AlifeApi.BusinessRules.CustomerModels.CustomerService.GetCustomerByIdAsync(string).customerId'></a>

`customerId` [System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

客戶ID

#### Returns
[System.Threading.Tasks.Task&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')[CustomerOutput](AlifeApi.BusinessRules.CustomerModels.CustomerOutput.md 'AlifeApi.BusinessRules.CustomerModels.CustomerOutput')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')  
客戶詳細資訊

<a name='AlifeApi.BusinessRules.CustomerModels.CustomerService.GetCustomerListAsync(AlifeApi.BusinessRules.CustomerModels.CustomerQueryInput)'></a>

## CustomerService.GetCustomerListAsync(CustomerQueryInput) Method

取得客戶列表 (分頁，摘要資訊)

```csharp
public System.Threading.Tasks.Task<AlifeApi.BusinessRules.Infrastructure.PagedListOutput<AlifeApi.BusinessRules.CustomerModels.CustomerListOutput>> GetCustomerListAsync(AlifeApi.BusinessRules.CustomerModels.CustomerQueryInput input);
```
#### Parameters

<a name='AlifeApi.BusinessRules.CustomerModels.CustomerService.GetCustomerListAsync(AlifeApi.BusinessRules.CustomerModels.CustomerQueryInput).input'></a>

`input` [CustomerQueryInput](AlifeApi.BusinessRules.CustomerModels.CustomerQueryInput.md 'AlifeApi.BusinessRules.CustomerModels.CustomerQueryInput')

查詢條件

#### Returns
[System.Threading.Tasks.Task&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')[AlifeApi.BusinessRules.Infrastructure.PagedListOutput&lt;](AlifeApi.BusinessRules.Infrastructure.PagedListOutput_TDetail_.md 'AlifeApi.BusinessRules.Infrastructure.PagedListOutput<TDetail>')[CustomerListOutput](AlifeApi.BusinessRules.CustomerModels.CustomerListOutput.md 'AlifeApi.BusinessRules.CustomerModels.CustomerListOutput')[&gt;](AlifeApi.BusinessRules.Infrastructure.PagedListOutput_TDetail_.md 'AlifeApi.BusinessRules.Infrastructure.PagedListOutput<TDetail>')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')  
分頁的客戶列表 (摘要)

<a name='AlifeApi.BusinessRules.CustomerModels.CustomerService.UpdateCustomerAsync(string,AlifeApi.BusinessRules.CustomerModels.CustomerInput)'></a>

## CustomerService.UpdateCustomerAsync(string, CustomerInput) Method

更新客戶資訊

```csharp
public System.Threading.Tasks.Task UpdateCustomerAsync(string customerId, AlifeApi.BusinessRules.CustomerModels.CustomerInput input);
```
#### Parameters

<a name='AlifeApi.BusinessRules.CustomerModels.CustomerService.UpdateCustomerAsync(string,AlifeApi.BusinessRules.CustomerModels.CustomerInput).customerId'></a>

`customerId` [System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

客戶ID

<a name='AlifeApi.BusinessRules.CustomerModels.CustomerService.UpdateCustomerAsync(string,AlifeApi.BusinessRules.CustomerModels.CustomerInput).input'></a>

`input` [CustomerInput](AlifeApi.BusinessRules.CustomerModels.CustomerInput.md 'AlifeApi.BusinessRules.CustomerModels.CustomerInput')

更新客戶輸入資料

#### Returns
[System.Threading.Tasks.Task](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task 'System.Threading.Tasks.Task')