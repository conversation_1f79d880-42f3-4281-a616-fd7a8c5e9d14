using System;
using System.ComponentModel.DataAnnotations;

namespace AlifeApi.BusinessRules.DailyReportModels
{
    /// <summary>
    /// 日報查詢輸入DTO
    /// </summary>
    public class DailyReportQueryInput
    {
        /// <summary>
        /// 案場編號
        /// </summary>
        [Required(ErrorMessage = "案場編號為必填")]
        [StringLength(20, ErrorMessage = "案場編號長度不可超過20字元")]
        public string SiteCode { get; set; } = null!;

        /// <summary>
        /// 查詢日期
        /// </summary>
        [Required(ErrorMessage = "查詢日期為必填")]
        public DateTime ReportDate { get; set; }
    }

    /// <summary>
    /// 日報期間查詢輸入DTO
    /// </summary>
    public class DailyReportPeriodQueryInput
    {
        /// <summary>
        /// 案場編號
        /// </summary>
        [Required(ErrorMessage = "案場編號為必填")]
        [StringLength(20, ErrorMessage = "案場編號長度不可超過20字元")]
        public string SiteCode { get; set; } = null!;

        /// <summary>
        /// 開始日期
        /// </summary>
        [Required(ErrorMessage = "開始日期為必填")]
        public DateTime StartDate { get; set; }

        /// <summary>
        /// 結束日期
        /// </summary>
        [Required(ErrorMessage = "結束日期為必填")]
        public DateTime EndDate { get; set; }

        /// <summary>
        /// 頁碼
        /// </summary>
        [Range(1, int.MaxValue, ErrorMessage = "頁碼必須大於0")]
        public int Page { get; set; } = 1;

        /// <summary>
        /// 每頁筆數
        /// </summary>
        [Range(1, 100, ErrorMessage = "每頁筆數必須在1-100之間")]
        public int PageSize { get; set; } = 20;
    }

    /// <summary>
    /// 日報統計結果輸出DTO
    /// </summary>
    public class DailyReportOutput
    {
        /// <summary>
        /// 案場編號
        /// </summary>
        public string SiteCode { get; set; } = null!;

        /// <summary>
        /// 案場名稱
        /// </summary>
        public string? SiteName { get; set; }

        /// <summary>
        /// 日報日期
        /// </summary>
        public DateTime ReportDate { get; set; }

        /// <summary>
        /// 星期幾
        /// </summary>
        public string DayOfWeek { get; set; } = null!;

        /// <summary>
        /// 來客數
        /// </summary>
        public int VisitorCount { get; set; }

        /// <summary>
        /// 來電數
        /// </summary>
        public int CallCount { get; set; }

        /// <summary>
        /// 留單數
        /// </summary>
        public int LeadCount { get; set; }

        /// <summary>
        /// 成交數
        /// </summary>
        public int TransactionCount { get; set; }

        /// <summary>
        /// 來客轉換率 (留單數/來客數)
        /// </summary>
        public decimal VisitorConversionRate { get; set; }

        /// <summary>
        /// 來電轉換率 (留單數/來電數)
        /// </summary>
        public decimal CallConversionRate { get; set; }

        /// <summary>
        /// 成交轉換率 (成交數/留單數)
        /// </summary>
        public decimal TransactionConversionRate { get; set; }

        /// <summary>
        /// 當日客戶明細
        /// </summary>
        public List<DailyCustomerDetail> CustomerDetails { get; set; } = new();
    }

    /// <summary>
    /// 日報期間統計列表輸出DTO
    /// </summary>
    public class DailyReportListOutput
    {
        /// <summary>
        /// 總筆數
        /// </summary>
        public int TotalCount { get; set; }

        /// <summary>
        /// 日報統計列表
        /// </summary>
        public List<DailyReportSummary> Reports { get; set; } = new();

        /// <summary>
        /// 期間總計統計
        /// </summary>
        public DailyReportPeriodSummary PeriodSummary { get; set; } = new();
    }

    /// <summary>
    /// 日報摘要資訊
    /// </summary>
    public class DailyReportSummary
    {
        /// <summary>
        /// 日報日期
        /// </summary>
        public DateTime ReportDate { get; set; }

        /// <summary>
        /// 星期幾
        /// </summary>
        public string DayOfWeek { get; set; } = null!;

        /// <summary>
        /// 來客數
        /// </summary>
        public int VisitorCount { get; set; }

        /// <summary>
        /// 來電數
        /// </summary>
        public int CallCount { get; set; }

        /// <summary>
        /// 留單數
        /// </summary>
        public int LeadCount { get; set; }

        /// <summary>
        /// 成交數
        /// </summary>
        public int TransactionCount { get; set; }

        /// <summary>
        /// 來客轉換率
        /// </summary>
        public decimal VisitorConversionRate { get; set; }

        /// <summary>
        /// 來電轉換率
        /// </summary>
        public decimal CallConversionRate { get; set; }

        /// <summary>
        /// 成交轉換率
        /// </summary>
        public decimal TransactionConversionRate { get; set; }
    }

    /// <summary>
    /// 日報期間總計統計
    /// </summary>
    public class DailyReportPeriodSummary
    {
        /// <summary>
        /// 開始日期
        /// </summary>
        public DateTime StartDate { get; set; }

        /// <summary>
        /// 結束日期
        /// </summary>
        public DateTime EndDate { get; set; }

        /// <summary>
        /// 總來客數
        /// </summary>
        public int TotalVisitorCount { get; set; }

        /// <summary>
        /// 總來電數
        /// </summary>
        public int TotalCallCount { get; set; }

        /// <summary>
        /// 總留單數
        /// </summary>
        public int TotalLeadCount { get; set; }

        /// <summary>
        /// 總成交數
        /// </summary>
        public int TotalTransactionCount { get; set; }

        /// <summary>
        /// 平均來客轉換率
        /// </summary>
        public decimal AverageVisitorConversionRate { get; set; }

        /// <summary>
        /// 平均來電轉換率
        /// </summary>
        public decimal AverageCallConversionRate { get; set; }

        /// <summary>
        /// 平均成交轉換率
        /// </summary>
        public decimal AverageTransactionConversionRate { get; set; }
    }

    /// <summary>
    /// 日報客戶明細
    /// </summary>
    public class DailyCustomerDetail
    {
        /// <summary>
        /// 客戶ID
        /// </summary>
        public int CustomerId { get; set; }

        /// <summary>
        /// 客戶姓名
        /// </summary>
        public string? CustomerName { get; set; }

        /// <summary>
        /// 聯絡電話
        /// </summary>
        public string? PhoneNumber { get; set; }

        /// <summary>
        /// 客戶來源
        /// </summary>
        public string? LeadSource { get; set; }

        /// <summary>
        /// 記錄類型 (來客/來電/留單/成交)
        /// </summary>
        public string RecordType { get; set; } = null!;

        /// <summary>
        /// 記錄時間
        /// </summary>
        public DateTime RecordTime { get; set; }

        /// <summary>
        /// 負責人員
        /// </summary>
        public string? HandledBy { get; set; }

        /// <summary>
        /// 備註
        /// </summary>
        public string? Notes { get; set; }
    }
} 
