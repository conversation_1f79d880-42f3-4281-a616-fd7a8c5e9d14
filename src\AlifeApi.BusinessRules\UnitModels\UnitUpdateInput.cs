using System;
using System.ComponentModel.DataAnnotations;

namespace AlifeApi.BusinessRules.UnitModels
{
    /// <summary>
    /// 房屋單位更新輸入模型
    /// </summary>
    public class UnitUpdateInput
    {
        public int? FloorId { get; set; }
        public string? UnitNumber { get; set; }
        public string? UnitType { get; set; }
        public string? Layout { get; set; }
        public string? Orientation { get; set; }
        public decimal? MainArea { get; set; }
        public decimal? AuxiliaryArea { get; set; }
        public decimal? BalconyArea { get; set; }
        public decimal? AwningArea { get; set; }
        public decimal? SmallPublicArea { get; set; }
        public decimal? LargePublicArea { get; set; }
        public decimal? PublicAreaShare { get; set; }
        public decimal? TotalArea { get; set; }
        public decimal? ListPrice { get; set; }
        public decimal? MinimumPrice { get; set; }
        public decimal? TransactionPrice { get; set; }
        public string? Status { get; set; }
        public string? Remarks { get; set; }
    }
}
