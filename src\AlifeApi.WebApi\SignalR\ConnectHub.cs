﻿using System.Net;
using AlifeApi.BusinessRules.LoginModels;
using AlifeApi.WebApi.Util;
using Microsoft.AspNetCore.Http.Features;
using Microsoft.AspNetCore.SignalR;

namespace AlifeApi.WebApi.SignalR
{
    public class ConnectHub : Hub
    {
        private readonly JwtHelper jwtHelper;
        public static readonly List<UserStatus> userConnections = new();
        private readonly LoginServicePg _loginService;

        public ConnectHub(JwtHelper jwtHelper, LoginServicePg loginService)
        {
            this.jwtHelper = jwtHelper ?? throw new ArgumentNullException(nameof(jwtHelper));
            _loginService = loginService ?? throw new ArgumentNullException(nameof(loginService));
        }


        public async Task RegisterUser(string jwt)
        {
            try
            {
                (string? userId, DateTime? LoginOutTime) = jwtHelper.GetTokenInfo(jwt);
                var connectionId = Context.ConnectionId;

                if (!string.IsNullOrEmpty(userId) && !userConnections.Any(x => x.ConnectionId == connectionId))
                {
                    // 如果該使用者已經有連接，則踢出先前的連接
                    var user = userConnections.FirstOrDefault(x => x.UserId == userId);
                    if (user != null)
                    {
                        userConnections.Remove(user);
                        await Clients.Client(user.ConnectionId).SendAsync("ForceDisconnect");
                    }

                    // 註冊新的連接
                    userConnections.Add(new UserStatus() { UserId = userId, ConnectionId = connectionId, LogoutTime = LoginOutTime });
                    // 同時發送在線人數和在線清單
                    await SendOnlineUsers();
                }
                else
                {
                    Context.Abort();
                }
            }
            catch (Exception ex)
            {
                Context.Abort();
            }           
        }       
        public async Task<IEnumerable<UserStatus>> GetOnlineUsers()
        {
            return userConnections;
        }

        public async Task<int> GetOnlineUsersCount()
        {
            return userConnections.Count;
        }

        private async Task SendOnlineUsers()
        {
            var onlineUsers = await GetOnlineUsers();
            var onlineUsersCount = await GetOnlineUsersCount();

            // 發送在線清單和在線人數給所有客戶端
            await Clients.All.SendAsync("OnlineUsersUpdated", onlineUsers, onlineUsersCount);

            // 發送在線清單和在線人數給所有客戶端
            await Clients.All.SendAsync("OnlineUsersCountUpdated", onlineUsersCount);
        }

        public override async Task OnDisconnectedAsync(Exception exception)
        {
            var userId = userConnections.FirstOrDefault(x => x.ConnectionId == Context.ConnectionId);

            //if (userId != null)
            //{
            //    var feature = Context.Features.Get<IHttpConnectionFeature>();
            //    var remoteAddress = feature.RemoteIpAddress;
            //    await _loginService.UserAutoLogOut(userId.UserId, remoteAddress.ToString());
            //    userConnections.Remove(userId);

            //    // 同時發送在線人數和在線清單
            //    await SendOnlineUsers();
            //}

            await base.OnDisconnectedAsync(exception);
        }
    }
}
