#### [AlifeApi.DataAccess](index.md 'index')
### [LogcontextPg](LogcontextPg.md 'LogcontextPg')

## SysApiLog Class

API 日誌記錄表

```csharp
public class SysApiLog
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; SysApiLog
### Properties

<a name='LogcontextPg.SysApiLog.ActionName'></a>

## SysApiLog.ActionName Property

執行方法名稱

```csharp
public string ActionName { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='LogcontextPg.SysApiLog.ControllerName'></a>

## SysApiLog.ControllerName Property

控制器名稱

```csharp
public string ControllerName { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='LogcontextPg.SysApiLog.Exception'></a>

## SysApiLog.Exception Property

錯誤異常

```csharp
public string Exception { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='LogcontextPg.SysApiLog.ExecutedTime'></a>

## SysApiLog.ExecutedTime Property

操作時間

```csharp
public System.DateTime ExecutedTime { get; set; }
```

#### Property Value
[System.DateTime](https://docs.microsoft.com/en-us/dotnet/api/System.DateTime 'System.DateTime')

<a name='LogcontextPg.SysApiLog.Headers'></a>

## SysApiLog.Headers Property

請求標頭

```csharp
public string Headers { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='LogcontextPg.SysApiLog.Id'></a>

## SysApiLog.Id Property

0

```csharp
public long Id { get; set; }
```

#### Property Value
[System.Int64](https://docs.microsoft.com/en-us/dotnet/api/System.Int64 'System.Int64')

<a name='LogcontextPg.SysApiLog.InputData'></a>

## SysApiLog.InputData Property

輸入資料

```csharp
public string InputData { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='LogcontextPg.SysApiLog.OutputData'></a>

## SysApiLog.OutputData Property

輸出資料

```csharp
public string OutputData { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='LogcontextPg.SysApiLog.Seconds'></a>

## SysApiLog.Seconds Property

執行時間（秒）

```csharp
public decimal Seconds { get; set; }
```

#### Property Value
[System.Decimal](https://docs.microsoft.com/en-us/dotnet/api/System.Decimal 'System.Decimal')

<a name='LogcontextPg.SysApiLog.SessionId'></a>

## SysApiLog.SessionId Property

Session ID

```csharp
public string SessionId { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='LogcontextPg.SysApiLog.Source'></a>

## SysApiLog.Source Property

請求來源

```csharp
public string Source { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='LogcontextPg.SysApiLog.System'></a>

## SysApiLog.System Property

系統名稱

```csharp
public string System { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')