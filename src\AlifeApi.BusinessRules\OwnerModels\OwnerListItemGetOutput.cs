namespace AlifeApi.BusinessRules.OwnerModels
{
    /// <summary>
    /// 業主列表項目輸出模型
    /// </summary>
    public class OwnerListItemGetOutput
    {
        /// <summary>
        /// 業主唯一識別碼
        /// </summary>
        public int OwnerId { get; set; }

        /// <summary>
        /// 公司名稱
        /// </summary>
        public string CompanyName { get; set; }

        /// <summary>
        /// 負責人姓名
        /// </summary>
        public string? ResponsiblePerson { get; set; }

        /// <summary>
        /// 主要聯絡窗口人員姓名
        /// </summary>
        public string? ContactPerson { get; set; }

        /// <summary>
        /// 主要聯絡電話
        /// </summary>
        public string? ContactPhone1 { get; set; }

        /// <summary>
        /// 人別 (值為 法人 或 自然人)
        /// </summary>
        public string PersonType { get; set; }

        /// <summary>
        /// 證號 (依人別決定是 統一編號 或 身分證ID)
        /// </summary>
        public string IdentificationNumber { get; set; }
    }
} 
