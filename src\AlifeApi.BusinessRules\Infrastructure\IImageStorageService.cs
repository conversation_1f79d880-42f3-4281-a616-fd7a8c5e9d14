using Microsoft.AspNetCore.Http;
using System.Threading.Tasks;

namespace AlifeApi.BusinessRules.Infrastructure
{
    /// <summary>
    /// 圖片儲存服務介面
    /// </summary>
    public interface IImageStorageService
    {
        /// <summary>
        /// 儲存 Base64 編碼的圖片
        /// </summary>
        /// <param name="imageBase64">Base64 編碼的圖片字串</param>
        /// <param name="customerId">客戶 ID (用於檔案命名)</param>
        /// <returns>儲存路徑 (檔案名稱)</returns>
        Task<string> SaveImageAsync(string imageBase64, string customerId);

        /// <summary>
        /// 刪除圖片
        /// </summary>
        /// <param name="imagePath">圖片路徑 (檔案名稱)</param>
        Task DeleteImageAsync(string imagePath);
    }
} 
