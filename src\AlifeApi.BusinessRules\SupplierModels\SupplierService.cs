﻿using AlifeApi.BusinessRules.Infrastructure;
using AlifeApi.DataAccess.ProjectContent;
using AlifeApi.DataAccess;
using Microsoft.EntityFrameworkCore;
using System;
using System.Linq;
using System.Threading.Tasks;
using System.Collections.Generic;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Configuration;
using System.IO;
using Microsoft.AspNetCore.Http;

namespace AlifeApi.BusinessRules.SupplierModels
{
    public class SupplierService : ServiceBase<alifeContext>
    {
        private readonly ILogger<SupplierService> _logger;
        private readonly IConfiguration _configuration;
        private readonly string _supplierFilesBasePath;

        public SupplierService(
            IServiceProvider serviceProvider, 
            alifeContext dbContext, 
            ILogger<SupplierService> logger, 
            IConfiguration configuration)
            : base(serviceProvider, dbContext)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _configuration = configuration ?? throw new ArgumentNullException(nameof(configuration));
            
            var configuredPath = _configuration["FilePath"];
            if (string.IsNullOrWhiteSpace(configuredPath))
            {
                _logger.LogError("FilePath is not configured in appsettings.json");
                _supplierFilesBasePath = Path.Combine(Directory.GetCurrentDirectory(), "DefaultLocalFiles", "Suppliers"); 
            }
            else
            {
                _supplierFilesBasePath = Path.Combine(configuredPath, "Suppliers");
            }
            
            Directory.CreateDirectory(_supplierFilesBasePath); 
        }

        /// <summary>
        /// 建立供應商
        /// </summary>
        public async Task<int> CreateSupplierAsync(SupplierCreateInput input)
        {
            ArgumentNullException.ThrowIfNull(input);

            if (string.IsNullOrWhiteSpace(input.Name))
            {
                throw new ArgumentException("供應商名稱不可為空。", nameof(input.Name));
            }

            var supplier = new Supplier
            {
                Name = input.Name,
                PersonType = input.PersonType,
                ContactPerson = input.ContactPerson,
                IdType = input.IdType,
                IdNumber = input.IdNumber,
                CompanyPhone = input.CompanyPhone,
                Address = input.Address,
                ContactName = input.ContactName,
                ContactPhone1 = input.ContactPhone1,
                ContactPhone2 = input.ContactPhone2,
                Email = input.Email,
                BankName = input.BankName,
                BankBranch = input.BankBranch,
                AccountName = input.AccountName,
                CreatedUserInfoId = CurrentUser.UserId,
                UpdatedUserInfoId = CurrentUser.UserId,
                CreatedTime = DateTime.Now,
                UpdatedTime = DateTime.Now
            };

            using var transaction = await Db.Database.BeginTransactionAsync();
            try
            {
                Db.Suppliers.Add(supplier);
                await Db.SaveChangesAsync();

                if (input.ActualFiles != null && input.ActualFiles.Any())
                {
                    string supplierSpecificPath = Path.Combine(_supplierFilesBasePath, supplier.SupplierId.ToString());
                    Directory.CreateDirectory(supplierSpecificPath);

                    for (int i = 0; i < input.ActualFiles.Count; i++)
                    {
                        var actualFile = input.ActualFiles[i];
                        if (actualFile == null || actualFile.Length == 0) continue;

                        SupplierFileInput fileMetadata = null;
                        if (input.Files != null && input.Files.Any())
                        {
                            fileMetadata = input.Files.FirstOrDefault(mf => 
                                (!string.IsNullOrWhiteSpace(mf.FileName) && mf.FileName == actualFile.FileName) || 
                                (!string.IsNullOrWhiteSpace(mf.FilePath) && mf.FilePath == actualFile.FileName)
                                );
                        }
                        if (fileMetadata == null && input.Files != null && input.Files.Count == input.ActualFiles.Count && i < input.Files.Count)
                        {
                            fileMetadata = input.Files[i];
                        }

                        string uniqueServerFileName = Guid.NewGuid().ToString() + Path.GetExtension(actualFile.FileName);
                        string fullServerPath = Path.Combine(supplierSpecificPath, uniqueServerFileName);

                        using (var stream = new FileStream(fullServerPath, FileMode.Create))
                        {
                            await actualFile.CopyToAsync(stream);
                        }

                        var supplierFileEntity = new SupplierFile
                        {
                            SupplierId = supplier.SupplierId,
                            FormType = fileMetadata?.FormType ?? "UPLOADED_FILE",
                            FileName = actualFile.FileName,
                            FilePath = fullServerPath,
                            Remark = fileMetadata?.Remark,
                            Agent = fileMetadata?.Agent,
                            CreatedUserInfoId = CurrentUser.UserId,
                            UpdatedUserInfoId = CurrentUser.UserId,
                            CreatedTime = DateTime.Now,
                            UpdatedTime = DateTime.Now
                        };
                        Db.SupplierFiles.Add(supplierFileEntity);
                    }
                    await Db.SaveChangesAsync();
                }

                await transaction.CommitAsync();
                return supplier.SupplierId;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "建立供應商失敗");
                await transaction.RollbackAsync();
                throw;
            }
        }

        /// <summary>
        /// 取得供應商列表 (分頁)
        /// </summary>
        public async Task<PagedListOutput<SupplierListOutput>> GetSupplierListAsync(SupplierQueryInput input)
        {
            ArgumentNullException.ThrowIfNull(input);

            var query = Db.Suppliers.AsNoTracking();

            if (!string.IsNullOrWhiteSpace(input.Name))
                query = query.Where(s => s.Name.Contains(input.Name));
            if (!string.IsNullOrWhiteSpace(input.PersonType))
                query = query.Where(s => s.PersonType.Contains(input.PersonType));
            if (!string.IsNullOrWhiteSpace(input.IdNumber))
                query = query.Where(s => s.IdNumber.Contains(input.IdNumber));
            if (!string.IsNullOrWhiteSpace(input.ContactName))
                query = query.Where(s => s.ContactName.Contains(input.ContactName));
            if (!string.IsNullOrWhiteSpace(input.ContactPhone))
                query = query.Where(s => s.ContactPhone1.Contains(input.ContactPhone) || s.ContactPhone2.Contains(input.ContactPhone));

            var projectedQuery = query
                .OrderByDescending(s => s.UpdatedTime)
                .Select(s => new SupplierListOutput
                {
                    SupplierId = s.SupplierId,
                    Name = s.Name,
                    PersonType = s.PersonType,
                    ContactPerson = s.ContactPerson,
                    IdNumber = s.IdNumber,
                    CompanyPhone = s.CompanyPhone,
                    ContactName = s.ContactName,
                    ContactPhone1 = s.ContactPhone1,
                    FileCount = s.SupplierFiles.Count(), 
                    UpdatedTime = s.UpdatedTime,
                    UpdatedUserName = Db.UserInfos.FirstOrDefault(u => u.UserInfoId == s.UpdatedUserInfoId).Name 
                });

            var pagedResult = await projectedQuery.ToPagedListOutputAsync(input);
            return pagedResult;
        }

        /// <summary>
        /// 根據ID取得供應商詳細資訊
        /// </summary>
        public async Task<SupplierOutput> GetSupplierByIdAsync(int supplierId)
        {
            var supplier = await Db.Suppliers
                .AsNoTracking()
                .Include(s => s.SupplierFiles)
                .FirstOrDefaultAsync(s => s.SupplierId == supplierId);

            if (supplier == null) return null;

            var userIds = new HashSet<string>();
            if (!string.IsNullOrEmpty(supplier.CreatedUserInfoId)) userIds.Add(supplier.CreatedUserInfoId);
            if (!string.IsNullOrEmpty(supplier.UpdatedUserInfoId)) userIds.Add(supplier.UpdatedUserInfoId);
            foreach (var file in supplier.SupplierFiles)
            {
                if (!string.IsNullOrEmpty(file.CreatedUserInfoId)) userIds.Add(file.CreatedUserInfoId);
                if (!string.IsNullOrEmpty(file.UpdatedUserInfoId)) userIds.Add(file.UpdatedUserInfoId);
            }

            var userNames = new Dictionary<string, string>();
            if (userIds.Any())
            {
                userNames = await Db.UserInfos
                    .Where(u => userIds.Contains(u.UserInfoId))
                    .AsNoTracking()
                    .ToDictionaryAsync(u => u.UserInfoId, u => u.Name);
            }

            return new SupplierOutput
            {
                SupplierId = supplier.SupplierId,
                Name = supplier.Name,
                PersonType = supplier.PersonType,
                ContactPerson = supplier.ContactPerson,
                IdType = supplier.IdType,
                IdNumber = supplier.IdNumber,
                CompanyPhone = supplier.CompanyPhone,
                Address = supplier.Address,
                ContactName = supplier.ContactName,
                ContactPhone1 = supplier.ContactPhone1,
                ContactPhone2 = supplier.ContactPhone2,
                Email = supplier.Email,
                BankName = supplier.BankName,
                BankBranch = supplier.BankBranch,
                AccountName = supplier.AccountName,
                CreatedUserInfoId = supplier.CreatedUserInfoId,
                CreatedUserName = !string.IsNullOrEmpty(supplier.CreatedUserInfoId) && userNames.TryGetValue(supplier.CreatedUserInfoId, out var cuName) ? cuName : null,
                UpdatedUserInfoId = supplier.UpdatedUserInfoId,
                UpdatedUserName = !string.IsNullOrEmpty(supplier.UpdatedUserInfoId) && userNames.TryGetValue(supplier.UpdatedUserInfoId, out var uuName) ? uuName : null,
                CreatedTime = supplier.CreatedTime,
                UpdatedTime = supplier.UpdatedTime,
                Files = supplier.SupplierFiles.Select(f => new SupplierFileOutput
                {
                    SupplierFileId = f.SupplierFileId,
                    SupplierId = f.SupplierId,
                    FormType = f.FormType,
                    FileName = f.FileName,
                    FilePath = f.FilePath,
                    Remark = f.Remark,
                    Agent = f.Agent,
                    CreatedUserInfoId = f.CreatedUserInfoId,
                    CreatedUserName = !string.IsNullOrEmpty(f.CreatedUserInfoId) && userNames.TryGetValue(f.CreatedUserInfoId, out var fcName) ? fcName : null,
                    UpdatedUserInfoId = f.UpdatedUserInfoId,
                    UpdatedUserName = !string.IsNullOrEmpty(f.UpdatedUserInfoId) && userNames.TryGetValue(f.UpdatedUserInfoId, out var fuName) ? fuName : null,
                    CreatedTime = f.CreatedTime,
                    UpdatedTime = f.UpdatedTime
                }).ToList()
            };
        }

        /// <summary>
        /// 更新供應商
        /// </summary>
        public async Task UpdateSupplierAsync(int supplierId, SupplierUpdateInput input)
        {
            ArgumentNullException.ThrowIfNull(input);

            var supplier = await Db.Suppliers
                .Include(s => s.SupplierFiles)
                .FirstOrDefaultAsync(s => s.SupplierId == supplierId);

            if (supplier == null)
            {
                throw new KeyNotFoundException($"找不到ID為 {supplierId} 的供應商");
            }

            supplier.Name = input.Name;
            supplier.PersonType = input.PersonType;
            supplier.ContactPerson = input.ContactPerson;
            supplier.IdType = input.IdType;
            supplier.IdNumber = input.IdNumber;
            supplier.CompanyPhone = input.CompanyPhone;
            supplier.Address = input.Address;
            supplier.ContactName = input.ContactName;
            supplier.ContactPhone1 = input.ContactPhone1;
            supplier.ContactPhone2 = input.ContactPhone2;
            supplier.Email = input.Email;
            supplier.BankName = input.BankName;
            supplier.BankBranch = input.BankBranch;
            supplier.AccountName = input.AccountName;
            supplier.UpdatedUserInfoId = CurrentUser.UserId;
            supplier.UpdatedTime = DateTime.Now;

            using var transaction = await Db.Database.BeginTransactionAsync();
            try
            {
                var existingDbFilesMap = supplier.SupplierFiles.ToDictionary(f => f.SupplierFileId);
                var processedFileIds = new HashSet<int>();
                var newFileEntities = new List<SupplierFile>();
                var filesToRemoveFromDb = new List<SupplierFile>();
                
                string supplierSpecificPath = Path.Combine(_supplierFilesBasePath, supplier.SupplierId.ToString());
                // 確保在添加新文件之前目錄存在
                if (input.ActualFiles != null && input.ActualFiles.Any(af => af != null && af.Length > 0) || 
                    (input.Files != null && input.Files.Any(mf => mf.SupplierFileId == 0)))
                {
                    Directory.CreateDirectory(supplierSpecificPath);
                }

                var actualFilesMap = (input.ActualFiles ?? new List<IFormFile>())
                                    .Where(af => af != null && af.Length > 0)
                                    .GroupBy(af => af.FileName) // 使用 GroupBy 處理潛在的同名新文件，取第一個
                                    .ToDictionary(g => g.Key, g => g.First());

                if (input.Files != null)
                {
                    foreach (var fileMetadata in input.Files)
                    {
                        if (fileMetadata.SupplierFileId != 0) // 現有檔案的元數據更新
                        {
                            if (existingDbFilesMap.TryGetValue(fileMetadata.SupplierFileId, out var dbFileToUpdate))
                            {
                                dbFileToUpdate.FormType = fileMetadata.FormType;
                                dbFileToUpdate.Remark = fileMetadata.Remark;
                                dbFileToUpdate.Agent = fileMetadata.Agent;
                                // FileName 和 FilePath 通常不應在此處通過元數據更新，它們由文件本身決定
                                // 如果需要更新 FileName (例如用戶可以重命名顯示名稱)，則需要更複雜的邏輯
                                dbFileToUpdate.UpdatedUserInfoId = CurrentUser.UserId;
                                dbFileToUpdate.UpdatedTime = DateTime.Now;
                                processedFileIds.Add(dbFileToUpdate.SupplierFileId);
                            }
                            else
                            {
                                _logger.LogWarning($"更新供應商 (ID: {supplierId}) 時，提供的檔案中繼資料 SupplierFileId: {fileMetadata.SupplierFileId} 在資料庫中找不到。");
                            }
                        }
                        else // 新檔案 (SupplierFileId == 0)
                        {
                            if (string.IsNullOrWhiteSpace(fileMetadata.FileName))
                            {
                                _logger.LogWarning($"更新供應商 (ID: {supplierId}) 時，新檔案的中繼資料缺少 FileName。");
                                continue;
                            }

                            if (actualFilesMap.TryGetValue(fileMetadata.FileName, out var actualFile))
                            {
                                string uniqueServerFileName = Guid.NewGuid().ToString() + Path.GetExtension(actualFile.FileName);
                                string fullServerPath = Path.Combine(supplierSpecificPath, uniqueServerFileName);

                                using (var stream = new FileStream(fullServerPath, FileMode.Create))
                                {
                                    await actualFile.CopyToAsync(stream);
                                }

                                var newFileEntity = new SupplierFile
                                {
                                    SupplierId = supplier.SupplierId,
                                    FormType = fileMetadata.FormType,
                                    FileName = actualFile.FileName, // 使用實際上傳的檔名
                                    FilePath = fullServerPath,    // 儲存伺服器上的完整路徑
                                    Remark = fileMetadata.Remark,
                                    Agent = fileMetadata.Agent,
                                    CreatedUserInfoId = CurrentUser.UserId,
                                    UpdatedUserInfoId = CurrentUser.UserId,
                                    CreatedTime = DateTime.Now,
                                    UpdatedTime = DateTime.Now
                                };
                                newFileEntities.Add(newFileEntity);
                                actualFilesMap.Remove(fileMetadata.FileName); // 從map中移除，避免重複處理
                            }
                            else
                            {
                                _logger.LogWarning($"更新供應商 (ID: {supplierId}) 時，中繼資料指定的 FileName '{fileMetadata.FileName}' 在 ActualFiles 中找不到對應的實際檔案。");
                            }
                        }
                    }
                }

                // 識別需要從資料庫刪除的舊檔案
                foreach (var existingFile in supplier.SupplierFiles)
                {
                    if (!processedFileIds.Contains(existingFile.SupplierFileId))
                    {
                        filesToRemoveFromDb.Add(existingFile);
                    }
                }

                if (filesToRemoveFromDb.Any())
                {
                    Db.SupplierFiles.RemoveRange(filesToRemoveFromDb);
                }
                if (newFileEntities.Any())
                {
                    await Db.SupplierFiles.AddRangeAsync(newFileEntities);
                }
                
                // 更新供應商主資訊 (EF Core 會自動追蹤已修改的 dbFileToUpdate)
                Db.Suppliers.Update(supplier); 
                await Db.SaveChangesAsync();

                // 在資料庫成功儲存後，刪除實體檔案
                foreach (var fileToDelete in filesToRemoveFromDb)
                {
                    try
                    {
                        if (File.Exists(fileToDelete.FilePath))
                        {
                            File.Delete(fileToDelete.FilePath);
                            _logger.LogInformation($"已刪除舊的供應商檔案: {fileToDelete.FilePath}");
                        }
                    }
                    catch (IOException ioEx)
                    {
                        _logger.LogWarning(ioEx, $"無法刪除舊的供應商檔案: {fileToDelete.FilePath}");
                        // 可考慮是否需要更進一步的錯誤處理，例如記錄到一個待處理列表
                    }
                }

                await transaction.CommitAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"更新供應商 (ID: {supplierId}) 失敗");
                await transaction.RollbackAsync();
                throw;
            }
        }

        /// <summary>
        /// 刪除供應商
        /// </summary>
        public async Task DeleteSupplierAsync(int supplierId)
        {
            var supplier = await Db.Suppliers
                .Include(s => s.SupplierFiles)
                .FirstOrDefaultAsync(s => s.SupplierId == supplierId);

            if (supplier == null)
            {
                _logger.LogWarning($"嘗試刪除不存在的供應商 (ID: {supplierId})");
                return;
            }

            string supplierDirectoryPath = Path.Combine(_supplierFilesBasePath, supplier.SupplierId.ToString());

            using var transaction = await Db.Database.BeginTransactionAsync();
            try
            {
                if (supplier.SupplierFiles.Any())
                {
                    Db.SupplierFiles.RemoveRange(supplier.SupplierFiles);
                }
                Db.Suppliers.Remove(supplier);
                await Db.SaveChangesAsync();

                if (Directory.Exists(supplierDirectoryPath))
                {
                    try
                    {
                        Directory.Delete(supplierDirectoryPath, recursive: true);
                        _logger.LogInformation($"Deleted supplier directory: {supplierDirectoryPath}");
                    }
                    catch (IOException ioEx)
                    {
                        _logger.LogWarning(ioEx, $"Could not delete supplier directory: {supplierDirectoryPath}");
                    }
                }
                
                await transaction.CommitAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"刪除供應商 (ID: {supplierId}) 失敗");
                await transaction.RollbackAsync();
                throw;
            }
        }
    }
} 
