#### [AlifeApi.BusinessRules](index.md 'index')

## AlifeApi.BusinessRules.UserInfoModels Namespace

| Classes | |
| :--- | :--- |
| [PasswordChangeInput](AlifeApi.BusinessRules.UserInfoModels.PasswordChangeInput.md 'AlifeApi.BusinessRules.UserInfoModels.PasswordChangeInput') | 變更密碼 Request 資料模型 |
| [PasswordManager](AlifeApi.BusinessRules.UserInfoModels.PasswordManager.md 'AlifeApi.BusinessRules.UserInfoModels.PasswordManager') | 密碼邏輯 |
| [RoleGroupOutput](AlifeApi.BusinessRules.UserInfoModels.RoleGroupOutput.md 'AlifeApi.BusinessRules.UserInfoModels.RoleGroupOutput') | 角色群組輸出資料 |
| [UserInfoCreateInput](AlifeApi.BusinessRules.UserInfoModels.UserInfoCreateInput.md 'AlifeApi.BusinessRules.UserInfoModels.UserInfoCreateInput') | 新增人員request資料模型 |
| [UserInfoCreateInputPg](AlifeApi.BusinessRules.UserInfoModels.UserInfoCreateInputPg.md 'AlifeApi.BusinessRules.UserInfoModels.UserInfoCreateInputPg') | 使用者新增輸入資料 (PostgreSQL版本) |
| [UserInfoDropdownInput](AlifeApi.BusinessRules.UserInfoModels.UserInfoDropdownInput.md 'AlifeApi.BusinessRules.UserInfoModels.UserInfoDropdownInput') | 使用者下拉選單查詢條件 |
| [UserInfoDropdownOutput](AlifeApi.BusinessRules.UserInfoModels.UserInfoDropdownOutput.md 'AlifeApi.BusinessRules.UserInfoModels.UserInfoDropdownOutput') | |
| [UserInfoListItemGetOutput](AlifeApi.BusinessRules.UserInfoModels.UserInfoListItemGetOutput.md 'AlifeApi.BusinessRules.UserInfoModels.UserInfoListItemGetOutput') | |
| [UserInfoListItemGetOutputPg](AlifeApi.BusinessRules.UserInfoModels.UserInfoListItemGetOutputPg.md 'AlifeApi.BusinessRules.UserInfoModels.UserInfoListItemGetOutputPg') | 使用者列表項目輸出資料 (PostgreSQL版本) |
| [UserInfoServicePg](AlifeApi.BusinessRules.UserInfoModels.UserInfoServicePg.md 'AlifeApi.BusinessRules.UserInfoModels.UserInfoServicePg') | |
| [UserInfoStatusUpdateInputPg](AlifeApi.BusinessRules.UserInfoModels.UserInfoStatusUpdateInputPg.md 'AlifeApi.BusinessRules.UserInfoModels.UserInfoStatusUpdateInputPg') | 更新使用者狀態輸入資料 (PostgreSQL版本) |
| [UserInfoUpdateInput](AlifeApi.BusinessRules.UserInfoModels.UserInfoUpdateInput.md 'AlifeApi.BusinessRules.UserInfoModels.UserInfoUpdateInput') | 人員帳號更新request資料模型 |
| [UserInfoUpdateInputPg](AlifeApi.BusinessRules.UserInfoModels.UserInfoUpdateInputPg.md 'AlifeApi.BusinessRules.UserInfoModels.UserInfoUpdateInputPg') | 使用者更新輸入資料 (PostgreSQL版本) |
| [UserInfoUpdateOutput](AlifeApi.BusinessRules.UserInfoModels.UserInfoUpdateOutput.md 'AlifeApi.BusinessRules.UserInfoModels.UserInfoUpdateOutput') | 使用者資料異動結果 |
