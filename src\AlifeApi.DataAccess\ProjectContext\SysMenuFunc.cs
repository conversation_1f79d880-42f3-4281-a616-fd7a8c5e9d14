﻿using System;
using System.Collections.Generic;

namespace AlifeApi.DataAccess.ProjectContext
{
    /// <summary>
    /// 系統選單資料
    /// </summary>
    public partial class SysMenuFunc
    {
        public SysMenuFunc()
        {
            SysRoleGroupPermission = new HashSet<SysRoleGroupPermission>();
            UserDeptPermission = new HashSet<UserDeptPermission>();
            UserGradePermission = new HashSet<UserGradePermission>();
        }

        /// <summary>
        /// 系統名稱
        /// </summary>
        public string System { get; set; }

        /// <summary>
        /// 系統項目識別編號
        /// </summary>
        public string FuncId { get; set; }

        /// <summary>
        /// 系統項目名稱
        /// </summary>
        public string FuncName { get; set; }

        /// <summary>
        /// 項目排序
        /// </summary>
        public int FuncOrder { get; set; }

        /// <summary>
        /// 項目路徑
        /// </summary>
        public string ParentFuncId { get; set; }

        public virtual ICollection<SysRoleGroupPermission> SysRoleGroupPermission { get; set; }

        public virtual ICollection<UserDeptPermission> UserDeptPermission { get; set; }

        public virtual ICollection<UserGradePermission> UserGradePermission { get; set; }

    }
}
