﻿{
   "CodeGenerationMode": 2,
   "ContextClassName": "ProjectContext",
   "ContextNamespace": null,
   "DefaultDacpacSchema": null,
   "FilterSchemas": false,
   "IncludeConnectionString": false,
   "ModelNamespace": null,
   "OutputContextPath": null,
   "OutputPath": "ProjectContext",
   "PreserveCasingWithRegex": true,
   "ProjectRootNamespace": "AlifeApi.DataAccess",
   "Schemas": null,
   "SelectedHandlebarsLanguage": 0,
   "SelectedToBeGenerated": 0,
   "Tables": [
      {
         "Name": "[dbo].[SYS_Bulletins]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[SYS_BulletinsAttach]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[SYS_BulletinsAttachDownloadRecord]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[SYS_BulletinsClickRecord]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[SYS_Code]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[SYS_MenuFunc]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[SYS_ProblemReport]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[SYS_ProjectVersion]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[SYS_RoleGroup]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[SYS_RoleGroupPermission]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[SYS_RoleGroupUser]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[SYS_ScheduleHistory]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[SYS_SystemSetting]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[SYS_Type]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[SYS_UserRecord]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[UserDept]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[UserDeptPermission]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[UserGrade]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[UserGradePermission]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[UserInfo]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[UserPasswordHistory]",
         "ObjectType": 0
      }
   ],
   "UiHint": "lab-ky-module95.Basic.dbo",
   "UncountableWords": null,
   "UseBoolPropertiesWithoutDefaultSql": false,
   "UseDatabaseNames": false,
   "UseDateOnlyTimeOnly": false,
   "UseDbContextSplitting": false,
   "UseFluentApiOnly": true,
   "UseHandleBars": true,
   "UseHierarchyId": false,
   "UseInflector": false,
   "UseLegacyPluralizer": false,
   "UseManyToManyEntity": true,
   "UseNoDefaultConstructor": false,
   "UseNoObjectFilter": false,
   "UseNodaTime": false,
   "UseNullableReferences": false,
   "UseSchemaFolders": false,
   "UseSpatial": false,
   "UseT4": false
}