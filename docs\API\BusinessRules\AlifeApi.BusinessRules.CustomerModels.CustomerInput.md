#### [AlifeApi.BusinessRules](index.md 'index')
### [AlifeApi.BusinessRules.CustomerModels](AlifeApi.BusinessRules.CustomerModels.md 'AlifeApi.BusinessRules.CustomerModels')

## CustomerInput Class

客戶資料輸入 DTO

```csharp
public class CustomerInput
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; CustomerInput
### Properties

<a name='AlifeApi.BusinessRules.CustomerModels.CustomerInput.Address'></a>

## CustomerInput.Address Property

地址

```csharp
public string Address { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.CustomerModels.CustomerInput.Birthday'></a>

## CustomerInput.Birthday Property

生日

```csharp
public System.Nullable<System.DateOnly> Birthday { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.DateOnly](https://docs.microsoft.com/en-us/dotnet/api/System.DateOnly 'System.DateOnly')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

<a name='AlifeApi.BusinessRules.CustomerModels.CustomerInput.Budget'></a>

## CustomerInput.Budget Property

預算範圍

```csharp
public string Budget { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.CustomerModels.CustomerInput.City'></a>

## CustomerInput.City Property

縣市

```csharp
public string City { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.CustomerModels.CustomerInput.CustomerRecords'></a>

## CustomerInput.CustomerRecords Property

要新增或更新的客戶訪談紀錄列表

```csharp
public System.Collections.Generic.List<AlifeApi.BusinessRules.CustomerModels.CustomerRecordInput> CustomerRecords { get; set; }
```

#### Property Value
[System.Collections.Generic.List&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Collections.Generic.List-1 'System.Collections.Generic.List`1')[CustomerRecordInput](AlifeApi.BusinessRules.CustomerModels.CustomerRecordInput.md 'AlifeApi.BusinessRules.CustomerModels.CustomerRecordInput')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Collections.Generic.List-1 'System.Collections.Generic.List`1')

<a name='AlifeApi.BusinessRules.CustomerModels.CustomerInput.District'></a>

## CustomerInput.District Property

區域

```csharp
public string District { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.CustomerModels.CustomerInput.Email'></a>

## CustomerInput.Email Property

電子信箱

```csharp
public string Email { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.CustomerModels.CustomerInput.Gender'></a>

## CustomerInput.Gender Property

性別

```csharp
public string Gender { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.CustomerModels.CustomerInput.ImageBase64'></a>

## CustomerInput.ImageBase64 Property

客戶圖片 (Base64 編碼字串)

```csharp
public string ImageBase64 { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.CustomerModels.CustomerInput.LeadSource'></a>

## CustomerInput.LeadSource Property

得知管道 (客戶來源)

```csharp
public string LeadSource { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.CustomerModels.CustomerInput.Name'></a>

## CustomerInput.Name Property

客戶姓名

```csharp
public string Name { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.CustomerModels.CustomerInput.Occupation'></a>

## CustomerInput.Occupation Property

職業

```csharp
public string Occupation { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.CustomerModels.CustomerInput.PhoneNumber'></a>

## CustomerInput.PhoneNumber Property

電話

```csharp
public string PhoneNumber { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.CustomerModels.CustomerInput.PurchaseConditions'></a>

## CustomerInput.PurchaseConditions Property

備註 (對應 Customer.PurchaseConditions)

```csharp
public string PurchaseConditions { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.CustomerModels.CustomerInput.RequiredLayout'></a>

## CustomerInput.RequiredLayout Property

需求格局

```csharp
public string RequiredLayout { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.CustomerModels.CustomerInput.RequiredPingArea'></a>

## CustomerInput.RequiredPingArea Property

需求坪數

```csharp
public string RequiredPingArea { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.CustomerModels.CustomerInput.SiteCode'></a>

## CustomerInput.SiteCode Property

案場代碼

```csharp
public string SiteCode { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')