#### [AlifeApi.BusinessRules](index.md 'index')
### [AlifeApi.BusinessRules.WeeklyReportModels](AlifeApi.BusinessRules.WeeklyReportModels.md 'AlifeApi.BusinessRules.WeeklyReportModels')

## ParkingSalesStatistics Class

車位銷售統計

```csharp
public class ParkingSalesStatistics
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; ParkingSalesStatistics
### Properties

<a name='AlifeApi.BusinessRules.WeeklyReportModels.ParkingSalesStatistics.AvailableAmount'></a>

## ParkingSalesStatistics.AvailableAmount Property

可售金額

```csharp
public decimal AvailableAmount { get; set; }
```

#### Property Value
[System.Decimal](https://docs.microsoft.com/en-us/dotnet/api/System.Decimal 'System.Decimal')

<a name='<PERSON>feApi.BusinessRules.WeeklyReportModels.ParkingSalesStatistics.AvailableSpaces'></a>

## ParkingSalesStatistics.AvailableSpaces Property

可售車位數

```csharp
public int AvailableSpaces { get; set; }
```

#### Property Value
[System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')

<a name='AlifeApi.BusinessRules.WeeklyReportModels.ParkingSalesStatistics.SalesRate'></a>

## ParkingSalesStatistics.SalesRate Property

銷售率

```csharp
public decimal SalesRate { get; set; }
```

#### Property Value
[System.Decimal](https://docs.microsoft.com/en-us/dotnet/api/System.Decimal 'System.Decimal')

<a name='AlifeApi.BusinessRules.WeeklyReportModels.ParkingSalesStatistics.SignedTotal'></a>

## ParkingSalesStatistics.SignedTotal Property

已簽約總數

```csharp
public int SignedTotal { get; set; }
```

#### Property Value
[System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')

<a name='AlifeApi.BusinessRules.WeeklyReportModels.ParkingSalesStatistics.SignedTotalTransactionAmount'></a>

## ParkingSalesStatistics.SignedTotalTransactionAmount Property

已簽約總成交金額

```csharp
public decimal SignedTotalTransactionAmount { get; set; }
```

#### Property Value
[System.Decimal](https://docs.microsoft.com/en-us/dotnet/api/System.Decimal 'System.Decimal')

<a name='AlifeApi.BusinessRules.WeeklyReportModels.ParkingSalesStatistics.SoldAmount'></a>

## ParkingSalesStatistics.SoldAmount Property

已售金額

```csharp
public decimal SoldAmount { get; set; }
```

#### Property Value
[System.Decimal](https://docs.microsoft.com/en-us/dotnet/api/System.Decimal 'System.Decimal')

<a name='AlifeApi.BusinessRules.WeeklyReportModels.ParkingSalesStatistics.SoldSpaces'></a>

## ParkingSalesStatistics.SoldSpaces Property

已售車位數

```csharp
public int SoldSpaces { get; set; }
```

#### Property Value
[System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')

<a name='AlifeApi.BusinessRules.WeeklyReportModels.ParkingSalesStatistics.UnsoldAmount'></a>

## ParkingSalesStatistics.UnsoldAmount Property

未售金額

```csharp
public decimal UnsoldAmount { get; set; }
```

#### Property Value
[System.Decimal](https://docs.microsoft.com/en-us/dotnet/api/System.Decimal 'System.Decimal')

<a name='AlifeApi.BusinessRules.WeeklyReportModels.ParkingSalesStatistics.UnsoldRate'></a>

## ParkingSalesStatistics.UnsoldRate Property

未售率

```csharp
public decimal UnsoldRate { get; set; }
```

#### Property Value
[System.Decimal](https://docs.microsoft.com/en-us/dotnet/api/System.Decimal 'System.Decimal')

<a name='AlifeApi.BusinessRules.WeeklyReportModels.ParkingSalesStatistics.UnsoldSpaces'></a>

## ParkingSalesStatistics.UnsoldSpaces Property

未售車位數

```csharp
public int UnsoldSpaces { get; set; }
```

#### Property Value
[System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')