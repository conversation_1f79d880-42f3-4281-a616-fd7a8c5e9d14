#### [AlifeApi.DataAccess](index.md 'index')
### [AlifeApi.DataAccess.ProjectContext](AlifeApi.DataAccess.ProjectContext.md 'AlifeApi.DataAccess.ProjectContext')

## UserDeptPermission Class

```csharp
public class UserDeptPermission
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; UserDeptPermission
### Properties

<a name='AlifeApi.DataAccess.ProjectContext.UserDeptPermission.DeptId'></a>

## UserDeptPermission.DeptId Property

部門識別編號

```csharp
public string DeptId { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContext.UserDeptPermission.FuncId'></a>

## UserDeptPermission.FuncId Property

系統項目識別編號

```csharp
public string FuncId { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContext.UserDeptPermission.System'></a>

## UserDeptPermission.System Property

系統名稱

```csharp
public string System { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')