﻿using AlifeApi.BusinessRules.Infrastructure;
using AlifeApi.BusinessRules.ProjectVersionModels;
using AlifeApi.DataAccess.ProjectContent;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AlifeApi.BusinessRules.SecuritySettingModels
{
    public class SecuritySettingService : ServiceBase<alifeContext>
    {
        public SecuritySettingService(IServiceProvider serviceProvider, alifeContext dbContext) : base(serviceProvider, dbContext)
        {
        }

        public async Task<List<SecuritySettingModel>> GetSecuritySettingListAsync()
        {
            return await Db.SysSystemSettings.AsNoTracking().AsQueryable().Where(x => x.Type == "SecuritySetting").Select(x => new SecuritySettingModel()
            {
                Key = x.Key,
                Value = x.Value,
                Name = x.Name,
                Enabled = (bool)x.IsEnabled
            }).ToListAsync();
        }

        public async Task UpdateSecuritySettingAsync(List<SecuritySettingModel> input)
        {
            ArgumentNullException.ThrowIfNull(input, nameof(input));

            foreach (var cc in input)
            {
                var setting = Db.SysSystemSettings.AsNoTracking().AsQueryable().FirstOrDefault(x => x.Type == "SecuritySetting" && x.Key == cc.Key);
                if (setting != null)
                {
                    setting.Value = cc.Value;
                    setting.IsEnabled = cc.Enabled;
                    setting.Name = cc.Name;
                    setting.UpdatedTime = DateTime.Now;
                    setting.UpdatedUserInfoId = CurrentUser.UserId;
                    Db.Entry(setting).State = EntityState.Modified;
                }
                else
                {
                    Db.SysSystemSettings.Add(new SysSystemSetting()
                    {
                        Type = "SecuritySetting",
                        Key = cc.Key,
                        Value = cc.Value,
                        Name = cc.Name,
                        IsEnabled = cc.Enabled,
                        CreatedTime = DateTime.Now,
                        CreatedUserInfoId = CurrentUser.UserId,
                        UpdatedTime = DateTime.Now,
                        UpdatedUserInfoId = CurrentUser.UserId
                    });
                }
            }

            await Db.SaveChangesAsync();
        }
    }
}
