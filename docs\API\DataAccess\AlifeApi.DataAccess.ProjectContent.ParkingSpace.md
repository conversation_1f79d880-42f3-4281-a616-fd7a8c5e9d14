#### [AlifeApi.DataAccess](index.md 'index')
### [AlifeApi.DataAccess.ProjectContent](AlifeApi.DataAccess.ProjectContent.md 'AlifeApi.DataAccess.ProjectContent')

## ParkingSpace Class

停車位基本資訊表 - 僅儲存車位的物理特性和位置資訊，銷售相關資訊由 Units 表管理

```csharp
public class ParkingSpace
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; ParkingSpace
### Properties

<a name='AlifeApi.DataAccess.ProjectContent.ParkingSpace.BuildingId'></a>

## ParkingSpace.BuildingId Property

所屬建築物識別碼 (參考 Buildings 表)。

```csharp
public int BuildingId { get; set; }
```

#### Property Value
[System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')

<a name='AlifeApi.DataAccess.ProjectContent.ParkingSpace.CreatedTime'></a>

## ParkingSpace.CreatedTime Property

資料創建時間 (TIMESTAMP WITHOUT TIME ZONE)。

```csharp
public System.DateTime CreatedTime { get; set; }
```

#### Property Value
[System.DateTime](https://docs.microsoft.com/en-us/dotnet/api/System.DateTime 'System.DateTime')

<a name='AlifeApi.DataAccess.ProjectContent.ParkingSpace.CreatedUserInfoId'></a>

## ParkingSpace.CreatedUserInfoId Property

建立者 (參考 UserInfo 表)。

```csharp
public string CreatedUserInfoId { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContent.ParkingSpace.Dimensions'></a>

## ParkingSpace.Dimensions Property

車位尺寸 (例如 "250x550cm")。

```csharp
public string Dimensions { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContent.ParkingSpace.FloorId'></a>

## ParkingSpace.FloorId Property

所在樓層識別碼 (**參考 Floors 表中 FloorType 為 '停車場' 的樓層ID**, e.g., P1F, B1)。

```csharp
public int FloorId { get; set; }
```

#### Property Value
[System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')

<a name='AlifeApi.DataAccess.ProjectContent.ParkingSpace.Location'></a>

## ParkingSpace.Location Property

車位詳細位置描述 (例如 "靠近電梯", "角落位置")

```csharp
public string Location { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContent.ParkingSpace.ParkingSpaceId'></a>

## ParkingSpace.ParkingSpaceId Property

停車位唯一識別碼 (主鍵, 自動遞增)。

```csharp
public int ParkingSpaceId { get; set; }
```

#### Property Value
[System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')

<a name='AlifeApi.DataAccess.ProjectContent.ParkingSpace.Remarks'></a>

## ParkingSpace.Remarks Property

備註 (例如 "柱子較多", "出入較便利")

```csharp
public string Remarks { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContent.ParkingSpace.SiteCode'></a>

## ParkingSpace.SiteCode Property

所屬案場編號 (參考 Sites 表)。

```csharp
public string SiteCode { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContent.ParkingSpace.SpaceNumber'></a>

## ParkingSpace.SpaceNumber Property

車位編號 (例如 "A01", "B15", "50")

```csharp
public string SpaceNumber { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContent.ParkingSpace.SpaceType'></a>

## ParkingSpace.SpaceType Property

車位類型 (例如 "坡道平面", "機械", "大型", "小型", "殘障" - 建議關聯 SYS_Code)

```csharp
public string SpaceType { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContent.ParkingSpace.UpdatedTime'></a>

## ParkingSpace.UpdatedTime Property

資料最後更新時間 (TIMESTAMP WITHOUT TIME ZONE)。

```csharp
public System.DateTime UpdatedTime { get; set; }
```

#### Property Value
[System.DateTime](https://docs.microsoft.com/en-us/dotnet/api/System.DateTime 'System.DateTime')

<a name='AlifeApi.DataAccess.ProjectContent.ParkingSpace.UpdatedUserInfoId'></a>

## ParkingSpace.UpdatedUserInfoId Property

更新者 (參考 UserInfo 表)。

```csharp
public string UpdatedUserInfoId { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')