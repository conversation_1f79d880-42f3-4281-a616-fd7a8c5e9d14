#### [AlifeApi.DataAccess](index.md 'index')
### [AlifeApi.DataAccess.ProjectContent](AlifeApi.DataAccess.ProjectContent.md 'AlifeApi.DataAccess.ProjectContent')

## Department Class

部門資料表，用於儲存每個公司的部門資訊。

```csharp
public class Department
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; Department
### Properties

<a name='AlifeApi.DataAccess.ProjectContent.Department.CompanyId'></a>

## Department.CompanyId Property

公司編號，主鍵的一部分，對應 Company 表的 CompanyId。

```csharp
public string CompanyId { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContent.Department.CreatedTime'></a>

## Department.CreatedTime Property

創建時間，記錄資料創建的時間。

```csharp
public System.DateTime CreatedTime { get; set; }
```

#### Property Value
[System.DateTime](https://docs.microsoft.com/en-us/dotnet/api/System.DateTime 'System.DateTime')

<a name='AlifeApi.DataAccess.ProjectContent.Department.CreatedUserInfoId'></a>

## Department.CreatedUserInfoId Property

建立者，記錄創建此資料的員工編號。

```csharp
public string CreatedUserInfoId { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContent.Department.DepartmentId'></a>

## Department.DepartmentId Property

部門編號，主鍵的一部分，用於唯一識別部門。

```csharp
public string DepartmentId { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContent.Department.Name'></a>

## Department.Name Property

部門名稱。

```csharp
public string Name { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContent.Department.UpdatedTime'></a>

## Department.UpdatedTime Property

更新時間，記錄資料最後更新的時間。

```csharp
public System.DateTime UpdatedTime { get; set; }
```

#### Property Value
[System.DateTime](https://docs.microsoft.com/en-us/dotnet/api/System.DateTime 'System.DateTime')

<a name='AlifeApi.DataAccess.ProjectContent.Department.UpdatedUserInfoId'></a>

## Department.UpdatedUserInfoId Property

更新者，記錄最後更新此資料的員工編號。

```csharp
public string UpdatedUserInfoId { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')