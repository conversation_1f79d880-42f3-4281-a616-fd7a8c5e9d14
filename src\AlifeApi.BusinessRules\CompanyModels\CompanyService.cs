using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using AlifeApi.BusinessRules.Infrastructure;
using AlifeApi.DataAccess.ProjectContent;
using Microsoft.EntityFrameworkCore;

namespace AlifeApi.BusinessRules.CompanyModels
{
    /// <summary>
    /// 公司服務
    /// </summary>
    public class CompanyService : ServiceBase<alifeContext>
    {
        /// <summary>
        /// 建構函數
        /// </summary>
        public CompanyService(IServiceProvider serviceProvider, alifeContext dbContext) 
            : base(serviceProvider, dbContext)
        {
        }

        /// <summary>
        /// 取得公司下拉選單
        /// </summary>
        /// <returns>公司下拉選單列表</returns>
        public async Task<List<CompanyDropdownOutput>> GetCompanyDropdownListAsync()
        {
            return await Db.Companies
                .OrderBy(x => x.Name)
                .Select(x => new CompanyDropdownOutput
                {
                    Name = x.Name,
                    Value = x.CompanyId
                })
                .ToListAsync();
        }
    }
} 
