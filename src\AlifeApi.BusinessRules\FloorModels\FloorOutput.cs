using System;

namespace AlifeApi.BusinessRules.FloorModels
{
    /// <summary>
    /// 樓層詳細輸出模型
    /// </summary>
    public class FloorOutput
    {
        /// <summary>
        /// 樓層唯一識別碼
        /// </summary>
        public int FloorId { get; set; }

        /// <summary>
        /// 建築物ID
        /// </summary>
        public int BuildingId { get; set; }

        /// <summary>
        /// 案場代碼
        /// </summary>
        public string SiteCode { get; set; } = null!;

        /// <summary>
        /// 樓層標示
        /// </summary>
        public string FloorLabel { get; set; } = null!;

        /// <summary>
        /// 樓層數值
        /// </summary>
        public int FloorLevel { get; set; }

        /// <summary>
        /// 樓層類型
        /// </summary>
        public string FloorType { get; set; } = null!;

        /// <summary>
        /// 樓層高度
        /// </summary>
        public decimal? FloorHeight { get; set; }

        /// <summary>
        /// 備註
        /// </summary>
        public string? Remarks { get; set; }

        /// <summary>
        /// 建立時間
        /// </summary>
        public DateTime CreatedTime { get; set; }

        /// <summary>
        /// 更新時間
        /// </summary>
        public DateTime UpdatedTime { get; set; }

        /// <summary>
        /// 建立者 UserInfoId
        /// </summary>
        public string CreatedUserInfoId { get; set; } = null!;

        /// <summary>
        /// 更新者 UserInfoId
        /// </summary>
        public string UpdatedUserInfoId { get; set; } = null!;

        /// <summary>
        /// 建立者名稱 (選填)
        /// </summary>
        public string? CreatedUserName { get; set; }

        /// <summary>
        /// 更新者名稱 (選填)
        /// </summary>
        public string? UpdatedUserName { get; set; }
    }
} 
