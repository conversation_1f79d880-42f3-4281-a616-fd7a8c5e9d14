﻿using System.ComponentModel.DataAnnotations;
using AlifeApi.Common.Util;

namespace AlifeApi.BusinessRules.BulletinsModels
{
    public class BulletinAttachmenFileModel
    {
        /// <summary>
        /// 公告附件唯一識別值
        /// </summary>
        [Required]
        [Range(1, int.MaxValue, ErrorMessage = "請輸入大於0的整數")]
        public int BLA_Id { get; set; }

        /// <summary>
        /// 公告附件檔案名稱(須附副檔名)
        /// </summary>
        [MaxLength(300, ErrorMessage = "檔案名稱不能超過300個字符")]
        public string FileName { get; set; }

        /// <summary>
        /// 附件
        /// </summary>
        [LogIgnore]
        public byte[] File { get; set; }
    }
}
