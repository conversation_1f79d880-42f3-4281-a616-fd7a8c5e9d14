#### [AlifeApi.WebApi](index.md 'index')
### [AlifeApi.WebApi.Controllers](AlifeApi.WebApi.Controllers.md 'AlifeApi.WebApi.Controllers')

## ParkingSpacesController Class

停車位管理

```csharp
public class ParkingSpacesController : AlifeApi.WebApi.Controllers.AuthenticatedController
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; [Microsoft.AspNetCore.Mvc.ControllerBase](https://docs.microsoft.com/en-us/dotnet/api/Microsoft.AspNetCore.Mvc.ControllerBase 'Microsoft.AspNetCore.Mvc.ControllerBase') &#129106; [Microsoft.AspNetCore.Mvc.Controller](https://docs.microsoft.com/en-us/dotnet/api/Microsoft.AspNetCore.Mvc.Controller 'Microsoft.AspNetCore.Mvc.Controller') &#129106; [AlifeApi.WebApi.Controllers.BasicController](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.WebApi.Controllers.BasicController 'AlifeApi.WebApi.Controllers.BasicController') &#129106; [AlifeApi.WebApi.Controllers.AuthenticatedController](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.WebApi.Controllers.AuthenticatedController 'AlifeApi.WebApi.Controllers.AuthenticatedController') &#129106; ParkingSpacesController
### Methods

<a name='AlifeApi.WebApi.Controllers.ParkingSpacesController.CreateParkingSpace(AlifeApi.BusinessRules.ParkingSpaceModels.ParkingSpaceCreateInput)'></a>

## ParkingSpacesController.CreateParkingSpace(ParkingSpaceCreateInput) Method

新增停車位

```csharp
public System.Threading.Tasks.Task<Microsoft.AspNetCore.Mvc.ActionResult<int>> CreateParkingSpace(AlifeApi.BusinessRules.ParkingSpaceModels.ParkingSpaceCreateInput input);
```
#### Parameters

<a name='AlifeApi.WebApi.Controllers.ParkingSpacesController.CreateParkingSpace(AlifeApi.BusinessRules.ParkingSpaceModels.ParkingSpaceCreateInput).input'></a>

`input` [AlifeApi.BusinessRules.ParkingSpaceModels.ParkingSpaceCreateInput](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.BusinessRules.ParkingSpaceModels.ParkingSpaceCreateInput 'AlifeApi.BusinessRules.ParkingSpaceModels.ParkingSpaceCreateInput')

停車位建立輸入資料

#### Returns
[System.Threading.Tasks.Task&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')[Microsoft.AspNetCore.Mvc.ActionResult&lt;](https://docs.microsoft.com/en-us/dotnet/api/Microsoft.AspNetCore.Mvc.ActionResult-1 'Microsoft.AspNetCore.Mvc.ActionResult`1')[System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/Microsoft.AspNetCore.Mvc.ActionResult-1 'Microsoft.AspNetCore.Mvc.ActionResult`1')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')  
新增的停車位ID

<a name='AlifeApi.WebApi.Controllers.ParkingSpacesController.DeleteParkingSpace(int)'></a>

## ParkingSpacesController.DeleteParkingSpace(int) Method

刪除停車位

```csharp
public System.Threading.Tasks.Task<Microsoft.AspNetCore.Mvc.ActionResult> DeleteParkingSpace(int parkingSpaceId);
```
#### Parameters

<a name='AlifeApi.WebApi.Controllers.ParkingSpacesController.DeleteParkingSpace(int).parkingSpaceId'></a>

`parkingSpaceId` [System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')

停車位ID

#### Returns
[System.Threading.Tasks.Task&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')[Microsoft.AspNetCore.Mvc.ActionResult](https://docs.microsoft.com/en-us/dotnet/api/Microsoft.AspNetCore.Mvc.ActionResult 'Microsoft.AspNetCore.Mvc.ActionResult')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')  
NoContent

<a name='AlifeApi.WebApi.Controllers.ParkingSpacesController.GetParkingSpace(int)'></a>

## ParkingSpacesController.GetParkingSpace(int) Method

根據停車位ID取得詳細資訊

```csharp
public System.Threading.Tasks.Task<Microsoft.AspNetCore.Mvc.ActionResult<AlifeApi.BusinessRules.ParkingSpaceModels.ParkingSpaceOutput>> GetParkingSpace(int parkingSpaceId);
```
#### Parameters

<a name='AlifeApi.WebApi.Controllers.ParkingSpacesController.GetParkingSpace(int).parkingSpaceId'></a>

`parkingSpaceId` [System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')

停車位ID

#### Returns
[System.Threading.Tasks.Task&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')[Microsoft.AspNetCore.Mvc.ActionResult&lt;](https://docs.microsoft.com/en-us/dotnet/api/Microsoft.AspNetCore.Mvc.ActionResult-1 'Microsoft.AspNetCore.Mvc.ActionResult`1')[AlifeApi.BusinessRules.ParkingSpaceModels.ParkingSpaceOutput](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.BusinessRules.ParkingSpaceModels.ParkingSpaceOutput 'AlifeApi.BusinessRules.ParkingSpaceModels.ParkingSpaceOutput')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/Microsoft.AspNetCore.Mvc.ActionResult-1 'Microsoft.AspNetCore.Mvc.ActionResult`1')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')  
停車位詳細資訊

<a name='AlifeApi.WebApi.Controllers.ParkingSpacesController.GetParkingSpaces(AlifeApi.BusinessRules.ParkingSpaceModels.ParkingSpaceQueryInput)'></a>

## ParkingSpacesController.GetParkingSpaces(ParkingSpaceQueryInput) Method

取得停車位列表 (分頁)

```csharp
public System.Threading.Tasks.Task<Microsoft.AspNetCore.Mvc.ActionResult<AlifeApi.BusinessRules.Infrastructure.PagedListOutput<AlifeApi.BusinessRules.ParkingSpaceModels.ParkingSpaceListOutput>>> GetParkingSpaces(AlifeApi.BusinessRules.ParkingSpaceModels.ParkingSpaceQueryInput input);
```
#### Parameters

<a name='AlifeApi.WebApi.Controllers.ParkingSpacesController.GetParkingSpaces(AlifeApi.BusinessRules.ParkingSpaceModels.ParkingSpaceQueryInput).input'></a>

`input` [AlifeApi.BusinessRules.ParkingSpaceModels.ParkingSpaceQueryInput](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.BusinessRules.ParkingSpaceModels.ParkingSpaceQueryInput 'AlifeApi.BusinessRules.ParkingSpaceModels.ParkingSpaceQueryInput')

查詢條件

#### Returns
[System.Threading.Tasks.Task&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')[Microsoft.AspNetCore.Mvc.ActionResult&lt;](https://docs.microsoft.com/en-us/dotnet/api/Microsoft.AspNetCore.Mvc.ActionResult-1 'Microsoft.AspNetCore.Mvc.ActionResult`1')[AlifeApi.BusinessRules.Infrastructure.PagedListOutput&lt;](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.BusinessRules.Infrastructure.PagedListOutput-1 'AlifeApi.BusinessRules.Infrastructure.PagedListOutput`1')[AlifeApi.BusinessRules.ParkingSpaceModels.ParkingSpaceListOutput](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.BusinessRules.ParkingSpaceModels.ParkingSpaceListOutput 'AlifeApi.BusinessRules.ParkingSpaceModels.ParkingSpaceListOutput')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.BusinessRules.Infrastructure.PagedListOutput-1 'AlifeApi.BusinessRules.Infrastructure.PagedListOutput`1')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/Microsoft.AspNetCore.Mvc.ActionResult-1 'Microsoft.AspNetCore.Mvc.ActionResult`1')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')  
分頁的停車位列表

<a name='AlifeApi.WebApi.Controllers.ParkingSpacesController.UpdateParkingSpace(int,AlifeApi.BusinessRules.ParkingSpaceModels.ParkingSpaceUpdateInput)'></a>

## ParkingSpacesController.UpdateParkingSpace(int, ParkingSpaceUpdateInput) Method

更新停車位

```csharp
public System.Threading.Tasks.Task<Microsoft.AspNetCore.Mvc.ActionResult> UpdateParkingSpace(int parkingSpaceId, AlifeApi.BusinessRules.ParkingSpaceModels.ParkingSpaceUpdateInput input);
```
#### Parameters

<a name='AlifeApi.WebApi.Controllers.ParkingSpacesController.UpdateParkingSpace(int,AlifeApi.BusinessRules.ParkingSpaceModels.ParkingSpaceUpdateInput).parkingSpaceId'></a>

`parkingSpaceId` [System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')

停車位ID

<a name='AlifeApi.WebApi.Controllers.ParkingSpacesController.UpdateParkingSpace(int,AlifeApi.BusinessRules.ParkingSpaceModels.ParkingSpaceUpdateInput).input'></a>

`input` [AlifeApi.BusinessRules.ParkingSpaceModels.ParkingSpaceUpdateInput](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.BusinessRules.ParkingSpaceModels.ParkingSpaceUpdateInput 'AlifeApi.BusinessRules.ParkingSpaceModels.ParkingSpaceUpdateInput')

停車位更新輸入資料

#### Returns
[System.Threading.Tasks.Task&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')[Microsoft.AspNetCore.Mvc.ActionResult](https://docs.microsoft.com/en-us/dotnet/api/Microsoft.AspNetCore.Mvc.ActionResult 'Microsoft.AspNetCore.Mvc.ActionResult')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')  
NoContent