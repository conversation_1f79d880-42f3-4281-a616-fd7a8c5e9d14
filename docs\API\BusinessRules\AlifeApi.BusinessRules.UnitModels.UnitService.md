#### [AlifeApi.BusinessRules](index.md 'index')
### [AlifeApi.BusinessRules.UnitModels](AlifeApi.BusinessRules.UnitModels.md 'AlifeApi.BusinessRules.UnitModels')

## UnitService Class

房屋單位服務

```csharp
public class UnitService : AlifeApi.BusinessRules.Infrastructure.ServiceBase<AlifeApi.DataAccess.ProjectContent.alifeContext>
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; [AlifeApi.Common.InfrastructureBase](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.Common.InfrastructureBase 'AlifeApi.Common.InfrastructureBase') &#129106; [AlifeApi.BusinessRules.Infrastructure.ServiceBase&lt;](AlifeApi.BusinessRules.Infrastructure.ServiceBase_TDbContext_.md 'AlifeApi.BusinessRules.Infrastructure.ServiceBase<TDbContext>')[AlifeApi.DataAccess.ProjectContent.alifeContext](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.DataAccess.ProjectContent.alifeContext 'AlifeApi.DataAccess.ProjectContent.alifeContext')[&gt;](AlifeApi.BusinessRules.Infrastructure.ServiceBase_TDbContext_.md 'AlifeApi.BusinessRules.Infrastructure.ServiceBase<TDbContext>') &#129106; UnitService
### Methods

<a name='AlifeApi.BusinessRules.UnitModels.UnitService.BatchChangeUnitStatusAsync(System.Collections.Generic.List_int_,string,System.Nullable_int_)'></a>

## UnitService.BatchChangeUnitStatusAsync(List<int>, string, Nullable<int>) Method

批次變更房屋單位狀態 (用於訂單相關操作)

```csharp
public System.Threading.Tasks.Task BatchChangeUnitStatusAsync(System.Collections.Generic.List<int> unitIds, string newStatus, System.Nullable<int> orderId=null);
```
#### Parameters

<a name='AlifeApi.BusinessRules.UnitModels.UnitService.BatchChangeUnitStatusAsync(System.Collections.Generic.List_int_,string,System.Nullable_int_).unitIds'></a>

`unitIds` [System.Collections.Generic.List&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Collections.Generic.List-1 'System.Collections.Generic.List`1')[System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Collections.Generic.List-1 'System.Collections.Generic.List`1')

房屋單位ID列表

<a name='AlifeApi.BusinessRules.UnitModels.UnitService.BatchChangeUnitStatusAsync(System.Collections.Generic.List_int_,string,System.Nullable_int_).newStatus'></a>

`newStatus` [System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

新狀態

<a name='AlifeApi.BusinessRules.UnitModels.UnitService.BatchChangeUnitStatusAsync(System.Collections.Generic.List_int_,string,System.Nullable_int_).orderId'></a>

`orderId` [System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

關聯的訂單ID

#### Returns
[System.Threading.Tasks.Task](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task 'System.Threading.Tasks.Task')

<a name='AlifeApi.BusinessRules.UnitModels.UnitService.ChangeUnitStatusAsync(int,string,System.Nullable_int_)'></a>

## UnitService.ChangeUnitStatusAsync(int, string, Nullable<int>) Method

變更房屋單位狀態

```csharp
public System.Threading.Tasks.Task ChangeUnitStatusAsync(int unitId, string newStatus, System.Nullable<int> orderId=null);
```
#### Parameters

<a name='AlifeApi.BusinessRules.UnitModels.UnitService.ChangeUnitStatusAsync(int,string,System.Nullable_int_).unitId'></a>

`unitId` [System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')

房屋單位ID

<a name='AlifeApi.BusinessRules.UnitModels.UnitService.ChangeUnitStatusAsync(int,string,System.Nullable_int_).newStatus'></a>

`newStatus` [System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

新狀態

<a name='AlifeApi.BusinessRules.UnitModels.UnitService.ChangeUnitStatusAsync(int,string,System.Nullable_int_).orderId'></a>

`orderId` [System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

關聯的訂單ID (如果有)

#### Returns
[System.Threading.Tasks.Task](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task 'System.Threading.Tasks.Task')

<a name='AlifeApi.BusinessRules.UnitModels.UnitService.CreateUnitAsync(AlifeApi.BusinessRules.UnitModels.UnitCreateInput)'></a>

## UnitService.CreateUnitAsync(UnitCreateInput) Method

建立房屋單位

```csharp
public System.Threading.Tasks.Task<int> CreateUnitAsync(AlifeApi.BusinessRules.UnitModels.UnitCreateInput input);
```
#### Parameters

<a name='AlifeApi.BusinessRules.UnitModels.UnitService.CreateUnitAsync(AlifeApi.BusinessRules.UnitModels.UnitCreateInput).input'></a>

`input` [UnitCreateInput](AlifeApi.BusinessRules.UnitModels.UnitCreateInput.md 'AlifeApi.BusinessRules.UnitModels.UnitCreateInput')

房屋單位建立輸入資料

#### Returns
[System.Threading.Tasks.Task&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')[System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')  
新建房屋單位的ID

<a name='AlifeApi.BusinessRules.UnitModels.UnitService.DeleteUnitAsync(int)'></a>

## UnitService.DeleteUnitAsync(int) Method

刪除房屋單位

```csharp
public System.Threading.Tasks.Task DeleteUnitAsync(int unitId);
```
#### Parameters

<a name='AlifeApi.BusinessRules.UnitModels.UnitService.DeleteUnitAsync(int).unitId'></a>

`unitId` [System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')

房屋單位ID

#### Returns
[System.Threading.Tasks.Task](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task 'System.Threading.Tasks.Task')

<a name='AlifeApi.BusinessRules.UnitModels.UnitService.GetAvailableUnitsAsync(AlifeApi.BusinessRules.UnitModels.UnitQueryInput)'></a>

## UnitService.GetAvailableUnitsAsync(UnitQueryInput) Method

取得可售房屋列表 (專門用於銷售管理)

```csharp
public System.Threading.Tasks.Task<AlifeApi.BusinessRules.Infrastructure.PagedListOutput<AlifeApi.BusinessRules.UnitModels.UnitListOutput>> GetAvailableUnitsAsync(AlifeApi.BusinessRules.UnitModels.UnitQueryInput input);
```
#### Parameters

<a name='AlifeApi.BusinessRules.UnitModels.UnitService.GetAvailableUnitsAsync(AlifeApi.BusinessRules.UnitModels.UnitQueryInput).input'></a>

`input` [UnitQueryInput](AlifeApi.BusinessRules.UnitModels.UnitQueryInput.md 'AlifeApi.BusinessRules.UnitModels.UnitQueryInput')

查詢條件

#### Returns
[System.Threading.Tasks.Task&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')[AlifeApi.BusinessRules.Infrastructure.PagedListOutput&lt;](AlifeApi.BusinessRules.Infrastructure.PagedListOutput_TDetail_.md 'AlifeApi.BusinessRules.Infrastructure.PagedListOutput<TDetail>')[UnitListOutput](AlifeApi.BusinessRules.UnitModels.UnitListOutput.md 'AlifeApi.BusinessRules.UnitModels.UnitListOutput')[&gt;](AlifeApi.BusinessRules.Infrastructure.PagedListOutput_TDetail_.md 'AlifeApi.BusinessRules.Infrastructure.PagedListOutput<TDetail>')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')  
可售房屋列表

<a name='AlifeApi.BusinessRules.UnitModels.UnitService.GetReservedUnitsAsync(AlifeApi.BusinessRules.UnitModels.UnitQueryInput)'></a>

## UnitService.GetReservedUnitsAsync(UnitQueryInput) Method

取得保留房屋列表 (專門用於銷售管理)

```csharp
public System.Threading.Tasks.Task<AlifeApi.BusinessRules.Infrastructure.PagedListOutput<AlifeApi.BusinessRules.UnitModels.UnitListOutput>> GetReservedUnitsAsync(AlifeApi.BusinessRules.UnitModels.UnitQueryInput input);
```
#### Parameters

<a name='AlifeApi.BusinessRules.UnitModels.UnitService.GetReservedUnitsAsync(AlifeApi.BusinessRules.UnitModels.UnitQueryInput).input'></a>

`input` [UnitQueryInput](AlifeApi.BusinessRules.UnitModels.UnitQueryInput.md 'AlifeApi.BusinessRules.UnitModels.UnitQueryInput')

查詢條件

#### Returns
[System.Threading.Tasks.Task&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')[AlifeApi.BusinessRules.Infrastructure.PagedListOutput&lt;](AlifeApi.BusinessRules.Infrastructure.PagedListOutput_TDetail_.md 'AlifeApi.BusinessRules.Infrastructure.PagedListOutput<TDetail>')[UnitListOutput](AlifeApi.BusinessRules.UnitModels.UnitListOutput.md 'AlifeApi.BusinessRules.UnitModels.UnitListOutput')[&gt;](AlifeApi.BusinessRules.Infrastructure.PagedListOutput_TDetail_.md 'AlifeApi.BusinessRules.Infrastructure.PagedListOutput<TDetail>')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')  
保留房屋列表

<a name='AlifeApi.BusinessRules.UnitModels.UnitService.GetUnitByIdAsync(int)'></a>

## UnitService.GetUnitByIdAsync(int) Method

根據ID取得房屋單位詳細資料

```csharp
public System.Threading.Tasks.Task<AlifeApi.BusinessRules.UnitModels.UnitOutput?> GetUnitByIdAsync(int unitId);
```
#### Parameters

<a name='AlifeApi.BusinessRules.UnitModels.UnitService.GetUnitByIdAsync(int).unitId'></a>

`unitId` [System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')

房屋單位ID

#### Returns
[System.Threading.Tasks.Task&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')[UnitOutput](AlifeApi.BusinessRules.UnitModels.UnitOutput.md 'AlifeApi.BusinessRules.UnitModels.UnitOutput')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')  
房屋單位詳細資料

<a name='AlifeApi.BusinessRules.UnitModels.UnitService.GetUnitListAsync(AlifeApi.BusinessRules.UnitModels.UnitQueryInput)'></a>

## UnitService.GetUnitListAsync(UnitQueryInput) Method

取得指定樓層的房屋單位列表 (分頁)

```csharp
public System.Threading.Tasks.Task<AlifeApi.BusinessRules.Infrastructure.PagedListOutput<AlifeApi.BusinessRules.UnitModels.UnitListOutput>> GetUnitListAsync(AlifeApi.BusinessRules.UnitModels.UnitQueryInput input);
```
#### Parameters

<a name='AlifeApi.BusinessRules.UnitModels.UnitService.GetUnitListAsync(AlifeApi.BusinessRules.UnitModels.UnitQueryInput).input'></a>

`input` [UnitQueryInput](AlifeApi.BusinessRules.UnitModels.UnitQueryInput.md 'AlifeApi.BusinessRules.UnitModels.UnitQueryInput')

查詢條件

#### Returns
[System.Threading.Tasks.Task&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')[AlifeApi.BusinessRules.Infrastructure.PagedListOutput&lt;](AlifeApi.BusinessRules.Infrastructure.PagedListOutput_TDetail_.md 'AlifeApi.BusinessRules.Infrastructure.PagedListOutput<TDetail>')[UnitListOutput](AlifeApi.BusinessRules.UnitModels.UnitListOutput.md 'AlifeApi.BusinessRules.UnitModels.UnitListOutput')[&gt;](AlifeApi.BusinessRules.Infrastructure.PagedListOutput_TDetail_.md 'AlifeApi.BusinessRules.Infrastructure.PagedListOutput<TDetail>')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')  
分頁的房屋單位列表

<a name='AlifeApi.BusinessRules.UnitModels.UnitService.GetUnitSalesStatisticsAsync(string)'></a>

## UnitService.GetUnitSalesStatisticsAsync(string) Method

取得房屋銷售統計

```csharp
public System.Threading.Tasks.Task<global::UnitSalesStatistics> GetUnitSalesStatisticsAsync(string siteCode);
```
#### Parameters

<a name='AlifeApi.BusinessRules.UnitModels.UnitService.GetUnitSalesStatisticsAsync(string).siteCode'></a>

`siteCode` [System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

案場編號

#### Returns
[System.Threading.Tasks.Task&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')[UnitSalesStatistics](https://docs.microsoft.com/en-us/dotnet/api/UnitSalesStatistics 'UnitSalesStatistics')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')  
銷售統計資料

<a name='AlifeApi.BusinessRules.UnitModels.UnitService.UpdateUnitAsync(int,AlifeApi.BusinessRules.UnitModels.UnitUpdateInput)'></a>

## UnitService.UpdateUnitAsync(int, UnitUpdateInput) Method

更新房屋單位資訊

```csharp
public System.Threading.Tasks.Task UpdateUnitAsync(int unitId, AlifeApi.BusinessRules.UnitModels.UnitUpdateInput input);
```
#### Parameters

<a name='AlifeApi.BusinessRules.UnitModels.UnitService.UpdateUnitAsync(int,AlifeApi.BusinessRules.UnitModels.UnitUpdateInput).unitId'></a>

`unitId` [System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')

房屋單位ID

<a name='AlifeApi.BusinessRules.UnitModels.UnitService.UpdateUnitAsync(int,AlifeApi.BusinessRules.UnitModels.UnitUpdateInput).input'></a>

`input` [UnitUpdateInput](AlifeApi.BusinessRules.UnitModels.UnitUpdateInput.md 'AlifeApi.BusinessRules.UnitModels.UnitUpdateInput')

房屋單位更新輸入資料

#### Returns
[System.Threading.Tasks.Task](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task 'System.Threading.Tasks.Task')