﻿using System;
using System.Collections.Generic;

namespace AlifeApi.DataAccess.ProjectContext
{
    /// <summary>
    /// 使用者操作紀錄
    /// </summary>
    public partial class SysUserRecord
    {
        /// <summary>
        /// 流水號
        /// </summary>
        public long Id { get; set; }

        /// <summary>
        /// 使用者
        /// </summary>
        public string UserId { get; set; }

        /// <summary>
        /// 使用者部門
        /// </summary>
        public string DeptId { get; set; }

        /// <summary>
        /// 職位
        /// </summary>
        public string GradeCode { get; set; }

        /// <summary>
        /// 紀錄時間
        /// </summary>
        public DateTime RecordTime { get; set; }

        /// <summary>
        /// 紀錄API
        /// </summary>
        public string RecordEvent { get; set; }

        /// <summary>
        /// 輸入資料
        /// </summary>
        public string InputData { get; set; }

        /// <summary>
        /// IP
        /// </summary>
        public string Ip { get; set; }

        public virtual UserDept Dept { get; set; }

        public virtual UserInfo User { get; set; }

    }
}
