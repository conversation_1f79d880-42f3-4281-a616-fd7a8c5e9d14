﻿using System.Text.Json.Serialization;

namespace AlifeApi.BusinessRules.CustomerModels
{
    /// <summary>
    /// 客戶列表輸出 DTO (摘要資訊)
    /// </summary>
    public class CustomerListOutput
    {
        /// <summary>
        /// 客戶ID
        /// </summary>
        [JsonPropertyName("CustomerId")]
        public string CustomerId { get; set; }

        /// <summary>
        /// 客戶姓名
        /// </summary>
        [JsonPropertyName("Name")]
        public string Name { get; set; }

        /// <summary>
        /// 電話
        /// </summary>
        [JsonPropertyName("PhoneNumber")]
        public string PhoneNumber { get; set; }

        /// <summary>
        /// 電子信箱
        /// </summary>
        [JsonPropertyName("Email")]
        public string Email { get; set; }

        /// <summary>
        /// 完整地址
        /// </summary>
        [JsonPropertyName("FullAddress")]
        public string FullAddress { get; set; }

        /// <summary>
        /// 職業
        /// </summary>
        [JsonPropertyName("Occupation")]
        public string Occupation { get; set; }

        /// <summary>
        /// 案場代碼
        /// </summary>
        [JsonPropertyName("SiteCode")]
        public string SiteCode { get; set; }

        /// <summary>
        /// 創建日期
        /// </summary>
        [JsonPropertyName("CreatedTime")]
        public DateTime CreatedTime { get; set; }

    }
} 
