﻿using System.Text.Json.Serialization;

namespace AlifeApi.BusinessRules.UserInfoModels
{
    /// <summary>
    /// 使用者下拉選單查詢條件
    /// </summary>
    public class UserInfoDropdownInput
    {
        /// <summary>
        /// 部門ID
        /// </summary>
        [JsonPropertyName("DepartmentId")]
        public string DepartmentId { get; set; } = string.Empty;

        /// <summary>
        /// 職稱ID
        /// </summary>
        [Json<PERSON>ropertyName("JobTitleId")]
        public string JobTitleId { get; set; } = string.Empty;

        /// <summary>
        /// 角色ID
        /// </summary>
        [J<PERSON><PERSON>ropertyName("RoleGroupId")]
        public string RoleGroupId { get; set; } = string.Empty;

        /// <summary>
        /// 使用者姓名
        /// </summary>
        [JsonPropertyName("Name")]
        public string Name { get; set; } = string.Empty;
    }
}
