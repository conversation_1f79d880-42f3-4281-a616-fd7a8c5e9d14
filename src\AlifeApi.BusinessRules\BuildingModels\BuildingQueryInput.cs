using AlifeApi.BusinessRules.Infrastructure;

namespace AlifeApi.BusinessRules.BuildingModels
{
    /// <summary>
    /// 建築物列表查詢輸入模型
    /// </summary>
    public class BuildingQueryInput : PagedListInput
    {
        /// <summary>
        /// 案場代碼 (篩選)
        /// </summary>
        public string? SiteCode { get; set; }

        /// <summary>
        /// 建築物名稱 (模糊查詢)
        /// </summary>
        public string? BuildingName { get; set; }

        /// <summary>
        /// 建築類型 (篩選)
        /// </summary>
        public string? BuildingType { get; set; }
    }
} 
