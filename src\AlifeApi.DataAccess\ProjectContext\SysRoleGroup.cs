﻿using System;
using System.Collections.Generic;

namespace AlifeApi.DataAccess.ProjectContext
{
    /// <summary>
    /// 角色群組資料
    /// </summary>
    public partial class SysRoleGroup
    {
        public SysRoleGroup()
        {
            SysRoleGroupPermission = new HashSet<SysRoleGroupPermission>();
            SysRoleGroupUser = new HashSet<SysRoleGroupUser>();
        }

        /// <summary>
        /// 系統名稱
        /// </summary>
        public string System { get; set; }

        /// <summary>
        /// 角色群組識別編號
        /// </summary>
        public string RoleGroupId { get; set; }

        /// <summary>
        /// 角色群組名稱
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// 是否為管理者
        /// </summary>
        public bool IsAdmin { get; set; }

        /// <summary>
        /// 建立時間
        /// </summary>
        public string CreatedUserId { get; set; }

        /// <summary>
        /// 建立者
        /// </summary>
        public DateTime CreatedTime { get; set; }

        /// <summary>
        /// 更新時間
        /// </summary>
        public string UpdatedUserId { get; set; }

        /// <summary>
        /// 更新者
        /// </summary>
        public DateTime UpdatedTime { get; set; }

        public virtual ICollection<SysRoleGroupPermission> SysRoleGroupPermission { get; set; }

        public virtual ICollection<SysRoleGroupUser> SysRoleGroupUser { get; set; }

    }
}
