using System.ComponentModel.DataAnnotations;

namespace AlifeApi.BusinessRules.ReviewModels
{
    /// <summary>
    /// 審核歷史記錄輸入模型
    /// </summary>
    public class ReviewHistoryInput
    {
        /// <summary>
        /// 審核任務ID
        /// </summary>
        [Required]
        public int TaskId { get; set; }

        /// <summary>
        /// 審核步驟ID
        /// </summary>
        public int? StepId { get; set; }

        /// <summary>
        /// 操作類型
        /// </summary>
        [Required]
        public string Action { get; set; }

        /// <summary>
        /// 評論內容
        /// </summary>
        public string Comment { get; set; }
    }
} 
