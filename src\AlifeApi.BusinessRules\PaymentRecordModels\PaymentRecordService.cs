using AlifeApi.BusinessRules.Infrastructure;
using AlifeApi.DataAccess.ProjectContent;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace AlifeApi.BusinessRules.PaymentRecordModels
{
    /// <summary>
    /// 收款記錄服務
    /// </summary>
    public class PaymentRecordService : ServiceBase<alifeContext>
    {
        public PaymentRecordService(IServiceProvider serviceProvider, alifeContext dbContext)
            : base(serviceProvider, dbContext)
        {
        }

        /// <summary>
        /// 建立收款記錄
        /// </summary>
        /// <param name="input">收款記錄建立輸入資料</param>
        /// <returns>新建收款記錄的ID</returns>
        public async Task<int> CreatePaymentRecordAsync(PaymentRecordCreateInput input)
        {
            ArgumentNullException.ThrowIfNull(input);

            // 驗證 OrderId 是否存在
            if (!await Db.PurchaseOrders.AnyAsync(po => po.OrderId == input.OrderId))
            {
                throw new Exception($"指定的訂單ID '{input.OrderId}' 不存在。");
            }

            var paymentRecord = new PaymentRecord
            {
                OrderId = input.OrderId,
                PaymentDate = input.PaymentDate,
                Amount = input.Amount,
                PaymentType = input.PaymentType,
                PaymentMethod = input.PaymentMethod,
                AttachmentPath = input.AttachmentPath,
                Remarks = input.Remarks,
                HandlingFee = input.HandlingFee,
                TransferredToDeveloper = input.TransferredToDeveloper ?? false,
                CreatedTime = DateTime.Now,
                UpdatedTime = DateTime.Now,
                CreatedUserInfoId = CurrentUser.UserId,
                UpdatedUserInfoId = CurrentUser.UserId
            };

            Db.PaymentRecords.Add(paymentRecord);
            await Db.SaveChangesAsync();

            // 可選：更新訂單的已付定金等資訊 (如果需要)
            // await UpdateOrderPaymentInfoAsync(input.OrderId);

            return paymentRecord.PaymentRecordId;
        }

        /// <summary>
        /// 取得指定訂單的收款記錄列表 (分頁)
        /// </summary>
        /// <param name="input">查詢條件 (必須包含 OrderId)</param>
        /// <returns>分頁的收款記錄列表</returns>
        public async Task<PagedListOutput<PaymentRecordListOutput>> GetPaymentRecordListAsync(PaymentRecordQueryInput input)
        {
            ArgumentNullException.ThrowIfNull(input);

            var query = Db.PaymentRecords
                          .Where(pr => pr.OrderId == input.OrderId);

            // 篩選 PaymentDate 範圍
            if (input.PaymentDateStart.HasValue)
            {
                query = query.Where(pr => pr.PaymentDate >= input.PaymentDateStart.Value);
            }
            if (input.PaymentDateEnd.HasValue)
            {
                query = query.Where(pr => pr.PaymentDate <= input.PaymentDateEnd.Value);
            }

            // 篩選 PaymentType
            if (!string.IsNullOrEmpty(input.PaymentType))
            {
                query = query.Where(pr => pr.PaymentType == input.PaymentType);
            }

            var projectedQuery = query
                .OrderByDescending(pr => pr.PaymentDate) // 通常按收款日期排序
                .Select(pr => new PaymentRecordListOutput
                {
                    PaymentRecordId = pr.PaymentRecordId,
                    OrderId = pr.OrderId,
                    PaymentDate = pr.PaymentDate,
                    Amount = pr.Amount,
                    PaymentType = pr.PaymentType,
                    PaymentMethod = pr.PaymentMethod,
                    Remarks = pr.Remarks,
                    CreatedTime = pr.CreatedTime,
                    CreatedUserInfoId = pr.CreatedUserInfoId,
                });

            var pagedResult = await projectedQuery.ToPagedListOutputAsync(input);

            if (pagedResult.Details.Any())
            {
                var userIds = pagedResult.Details.Select(pr => pr.CreatedUserInfoId).Where(id => !string.IsNullOrEmpty(id)).Distinct().ToList();
                if (userIds.Any())
                {
                    var users = await Db.UserInfos
                                        .Where(u => userIds.Contains(u.UserInfoId))
                                        .Select(u => new { u.UserInfoId, u.Name })
                                        .ToDictionaryAsync(u => u.UserInfoId, u => u.Name);

                    foreach (var prOutput in pagedResult.Details)
                    {
                        if (users.TryGetValue(prOutput.CreatedUserInfoId, out var userName))
                        {
                            prOutput.CreatedUserName = userName;
                        }
                    }
                }
            }

            return pagedResult;
        }

        /// <summary>
        /// 根據ID取得收款記錄詳細資料
        /// </summary>
        /// <param name="paymentRecordId">收款記錄ID</param>
        /// <returns>收款記錄詳細資料</returns>
        public async Task<PaymentRecordOutput?> GetPaymentRecordByIdAsync(int paymentRecordId)
        {
            var record = await Db.PaymentRecords
                .Where(pr => pr.PaymentRecordId == paymentRecordId)
                .Select(pr => new PaymentRecordOutput
                {
                    PaymentRecordId = pr.PaymentRecordId,
                    OrderId = pr.OrderId,
                    PaymentDate = pr.PaymentDate,
                    Amount = pr.Amount,
                    PaymentType = pr.PaymentType,
                    PaymentMethod = pr.PaymentMethod,
                    AttachmentPath = pr.AttachmentPath,
                    Remarks = pr.Remarks,
                    HandlingFee = pr.HandlingFee,
                    TransferredToDeveloper = pr.TransferredToDeveloper,
                    CreatedTime = pr.CreatedTime,
                    UpdatedTime = pr.UpdatedTime,
                    CreatedUserInfoId = pr.CreatedUserInfoId,
                    UpdatedUserInfoId = pr.UpdatedUserInfoId
                })
                .FirstOrDefaultAsync();

            if (record == null)
            {
                return null;
            }

            var orderNumber = await Db.PurchaseOrders.Where(po => po.OrderId == record.OrderId).Select(po => po.OrderNumber).FirstOrDefaultAsync();
            record.OrderNumber = orderNumber;

            var userIds = new[] { record.CreatedUserInfoId, record.UpdatedUserInfoId }.Where(id => !string.IsNullOrEmpty(id)).Distinct().ToList();
            if (userIds.Any())
            {
                var users = await Db.UserInfos
                                    .Where(ui => userIds.Contains(ui.UserInfoId))
                                    .Select(ui => new { ui.UserInfoId, ui.Name })
                                    .ToDictionaryAsync(ui => ui.UserInfoId, ui => ui.Name);

                if (users.TryGetValue(record.CreatedUserInfoId, out var createdName))
                    record.CreatedUserName = createdName;
                if (!string.IsNullOrEmpty(record.UpdatedUserInfoId) && users.TryGetValue(record.UpdatedUserInfoId, out var updatedName))
                    record.UpdatedUserName = updatedName;
            }

            return record;
        }

        /// <summary>
        /// 更新收款記錄資訊
        /// </summary>
        /// <param name="paymentRecordId">收款記錄ID</param>
        /// <param name="input">收款記錄更新輸入資料</param>
        public async Task UpdatePaymentRecordAsync(int paymentRecordId, PaymentRecordUpdateInput input)
        {
            ArgumentNullException.ThrowIfNull(input);

            var paymentRecord = await Db.PaymentRecords.FindAsync(paymentRecordId);

            if (paymentRecord == null)
            {
                throw new Exception($"找不到指定的收款記錄 (ID: {paymentRecordId})");
            }

            paymentRecord.PaymentDate = input.PaymentDate;
            paymentRecord.Amount = input.Amount;
            paymentRecord.PaymentType = input.PaymentType;
            paymentRecord.PaymentMethod = input.PaymentMethod;
            paymentRecord.AttachmentPath = input.AttachmentPath;
            paymentRecord.Remarks = input.Remarks;
            paymentRecord.HandlingFee = input.HandlingFee;
            paymentRecord.TransferredToDeveloper = input.TransferredToDeveloper ?? paymentRecord.TransferredToDeveloper;
            paymentRecord.UpdatedTime = DateTime.Now;
            paymentRecord.UpdatedUserInfoId = CurrentUser.UserId;

            await Db.SaveChangesAsync();

            // 可選：更新訂單的已付定金等資訊
            // await UpdateOrderPaymentInfoAsync(paymentRecord.OrderId);
        }

        /// <summary>
        /// 刪除收款記錄
        /// </summary>
        /// <param name="paymentRecordId">收款記錄ID</param>
        public async Task DeletePaymentRecordAsync(int paymentRecordId)
        {
            var paymentRecord = await Db.PaymentRecords.FindAsync(paymentRecordId);

            if (paymentRecord == null)
            {
                return; // 允許刪除不存在的紀錄
            }

            int orderId = paymentRecord.OrderId;

            Db.PaymentRecords.Remove(paymentRecord);
            await Db.SaveChangesAsync();

            // 可選：更新訂單的已付定金等資訊
            // await UpdateOrderPaymentInfoAsync(orderId);
        }

        /*
        // 可選：更新訂單付款資訊的輔助方法
        private async Task UpdateOrderPaymentInfoAsync(int orderId)
        {
            var order = await Db.PurchaseOrders.FindAsync(orderId);
            if (order != null)
            {
                var totalPaid = await Db.PaymentRecords
                                      .Where(pr => pr.OrderId == orderId)
                                      // .Where(pr => pr.PaymentType == "定金") // 或根據需要篩選特定款別
                                      .SumAsync(pr => pr.Amount);
                
                order.DepositPaidAmount = totalPaid; // 更新已付定金
                // order.DepositBalanceAmount = (order.DepositAmount ?? 0) - totalPaid;
                order.UpdatedTime = DateTime.Now;
                order.UpdatedUserInfoId = CurrentUser.UserId; // 可能需要一個系統帳號或特定標識
                await Db.SaveChangesAsync();
            }
        }
        */
    }
} 
