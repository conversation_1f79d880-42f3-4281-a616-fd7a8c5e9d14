using System.Text.Json.Serialization;

namespace AlifeApi.BusinessRules.CategoryModels
{
    /// <summary>
    /// 大分類下拉選單輸出
    /// </summary>
    public class LargeCategoryDropdownOutput
    {
        /// <summary>
        /// 大分類ID
        /// </summary>
        [Json<PERSON>ropertyName("LargeCategoryId")]
        public long LargeCategoryId { get; set; }

        /// <summary>
        /// 大分類名稱
        /// </summary>
        [JsonPropertyName("Name")]
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// 排序順序
        /// </summary>
        [JsonPropertyName("SortOrder")]
        public int SortOrder { get; set; }
    }
} 
