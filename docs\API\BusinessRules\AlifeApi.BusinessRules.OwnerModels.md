#### [AlifeApi.BusinessRules](index.md 'index')

## AlifeApi.BusinessRules.OwnerModels Namespace

| Classes | |
| :--- | :--- |
| [OwnerCreateInput](AlifeApi.BusinessRules.OwnerModels.OwnerCreateInput.md 'AlifeApi.BusinessRules.OwnerModels.OwnerCreateInput') | 建立業主輸入模型 |
| [OwnerGetOutput](AlifeApi.BusinessRules.OwnerModels.OwnerGetOutput.md 'AlifeApi.BusinessRules.OwnerModels.OwnerGetOutput') | 取得單一業主詳細資料輸出模型 |
| [OwnerListGetInput](AlifeApi.BusinessRules.OwnerModels.OwnerListGetInput.md 'AlifeApi.BusinessRules.OwnerModels.OwnerListGetInput') | 查詢業主列表輸入模型 |
| [OwnerListItemGetOutput](AlifeApi.BusinessRules.OwnerModels.OwnerListItemGetOutput.md 'AlifeApi.BusinessRules.OwnerModels.OwnerListItemGetOutput') | 業主列表項目輸出模型 |
| [OwnerService](AlifeApi.BusinessRules.OwnerModels.OwnerService.md 'AlifeApi.BusinessRules.OwnerModels.OwnerService') | 業主資料管理服務 |
| [OwnerUpdateInput](AlifeApi.BusinessRules.OwnerModels.OwnerUpdateInput.md 'AlifeApi.BusinessRules.OwnerModels.OwnerUpdateInput') | 更新業主輸入模型 |
| [OwnerUpdateOutput](AlifeApi.BusinessRules.OwnerModels.OwnerUpdateOutput.md 'AlifeApi.BusinessRules.OwnerModels.OwnerUpdateOutput') | 業主更新操作輸出模型 |
