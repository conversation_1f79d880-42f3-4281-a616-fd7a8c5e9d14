#### [AlifeApi.BusinessRules](index.md 'index')
### [AlifeApi.BusinessRules.CustomerModels](AlifeApi.BusinessRules.CustomerModels.md 'AlifeApi.BusinessRules.CustomerModels')

## CustomerListOutput Class

客戶列表輸出 DTO (摘要資訊)

```csharp
public class CustomerListOutput
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; CustomerListOutput
### Properties

<a name='AlifeApi.BusinessRules.CustomerModels.CustomerListOutput.CreatedTime'></a>

## CustomerListOutput.CreatedTime Property

創建日期

```csharp
public System.DateTime CreatedTime { get; set; }
```

#### Property Value
[System.DateTime](https://docs.microsoft.com/en-us/dotnet/api/System.DateTime 'System.DateTime')

<a name='AlifeApi.BusinessRules.CustomerModels.CustomerListOutput.CustomerId'></a>

## CustomerListOutput.CustomerId Property

客戶ID

```csharp
public string CustomerId { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.CustomerModels.CustomerListOutput.Email'></a>

## CustomerListOutput.Email Property

電子信箱

```csharp
public string Email { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.CustomerModels.CustomerListOutput.FullAddress'></a>

## CustomerListOutput.FullAddress Property

完整地址

```csharp
public string FullAddress { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.CustomerModels.CustomerListOutput.Name'></a>

## CustomerListOutput.Name Property

客戶姓名

```csharp
public string Name { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.CustomerModels.CustomerListOutput.Occupation'></a>

## CustomerListOutput.Occupation Property

職業

```csharp
public string Occupation { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.CustomerModels.CustomerListOutput.PhoneNumber'></a>

## CustomerListOutput.PhoneNumber Property

電話

```csharp
public string PhoneNumber { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.CustomerModels.CustomerListOutput.SiteCode'></a>

## CustomerListOutput.SiteCode Property

案場代碼

```csharp
public string SiteCode { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')