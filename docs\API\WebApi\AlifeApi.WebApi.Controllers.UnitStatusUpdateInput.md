#### [AlifeApi.WebApi](index.md 'index')
### [AlifeApi.WebApi.Controllers](AlifeApi.WebApi.Controllers.md 'AlifeApi.WebApi.Controllers')

## UnitStatusUpdateInput Class

房屋單位狀態更新輸入

```csharp
public class UnitStatusUpdateInput
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; UnitStatusUpdateInput
### Properties

<a name='AlifeApi.WebApi.Controllers.UnitStatusUpdateInput.Status'></a>

## UnitStatusUpdateInput.Status Property

新的銷售狀態

```csharp
public string Status { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')