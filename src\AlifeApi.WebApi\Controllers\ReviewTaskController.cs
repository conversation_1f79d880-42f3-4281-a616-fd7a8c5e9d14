﻿using AlifeApi.BusinessRules.Infrastructure;
using AlifeApi.BusinessRules.ReviewModels;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace AlifeApi.WebApi.Controllers
{
    /// <summary>
    /// 審核流程控制器
    /// </summary>
    public class ReviewTaskController : AuthenticatedController
    {
        private readonly ReviewTaskService _reviewTaskService;

        /// <summary>
        /// 建構函數
        /// </summary>
        /// <param name="reviewTaskService">審核流程服務</param>
        public ReviewTaskController(ReviewTaskService reviewTaskService)
        {
            _reviewTaskService = reviewTaskService;
        }

        /// <summary>
        /// 建立審核流程
        /// </summary>
        /// <param name="input">審核流程輸入資料</param>
        /// <returns>審核流程ID</returns>
        [HttpPost]
        public async Task<ActionResult<int>> CreateReviewTask(ReviewTaskInput input)
        {
            var taskId = await _reviewTaskService.CreateReviewTaskAsync(input);
            return Ok(taskId);
        }

        /// <summary>
        /// 取得審核流程清單
        /// </summary>
        /// <param name="input">查詢條件</param>
        /// <returns>審核流程清單</returns>
        /// <response code="200">成功取得審核流程清單</response>
        [HttpPost]
        public async Task<PagedListOutput<ReviewTaskOutput>> GetReviewTasksAsync(ReviewTaskListInput input)
            => await _reviewTaskService.GetReviewTasksAsync(input);

        /// <summary>
        /// 取得審核流程詳細資料
        /// </summary>
        /// <param name="taskId">審核流程ID</param>
        /// <returns>審核流程詳細資料</returns>
        [HttpGet("{taskId}")]
        public async Task<ActionResult<ReviewTaskDetailOutput>> GetReviewTaskDetail(int taskId)
        {
            var task = await _reviewTaskService.GetReviewTaskDetailAsync(taskId);
            if (task == null)
            {
                return NotFound();
            }
            return Ok(task);
        }

        /// <summary>
        /// 更新審核流程
        /// </summary>
        /// <param name="taskId">審核流程ID</param>
        /// <param name="input">審核流程更新輸入資料</param>
        /// <returns>審核流程ID</returns>
        [HttpPut("{taskId}")]
        public async Task<ActionResult<int>> UpdateReviewTask(int taskId, ReviewTaskUpdateInput input)
        {
            try
            {
                var updatedTaskId = await _reviewTaskService.UpdateReviewTaskAsync(taskId, input);
                return Ok(updatedTaskId);
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }

        /// <summary>
        /// 刪除審核流程
        /// </summary>
        /// <param name="taskId">審核流程ID</param>
        /// <returns></returns>
        [HttpDelete("{taskId}")]
        public async Task<ActionResult> DeleteReviewTask(int taskId)
        {
            try
            {
                await _reviewTaskService.DeleteReviewTaskAsync(taskId);
                return Ok();
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }

        /// <summary>
        /// 取得審核流程歷史記錄
        /// </summary>
        /// <param name="taskId">審核流程ID</param>
        /// <returns>歷史記錄清單</returns>
        [HttpGet("{taskId}/history")]
        public async Task<ActionResult<List<ReviewHistoryOutput>>> GetReviewHistory(int taskId)
        {
            try
            {
                var history = await _reviewTaskService.GetReviewHistoryAsync(taskId);
                return Ok(history);
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }

        /// <summary>
        /// 添加審核歷史記錄
        /// </summary>
        /// <param name="input">審核歷史記錄輸入資料</param>
        /// <returns></returns>
        [HttpPost("history")]
        public async Task<ActionResult> AddReviewHistory(ReviewHistoryInput input)
        {
            try
            {
                await _reviewTaskService.AddReviewHistoryAsync(input);
                return Ok();
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }

        /// <summary>
        /// 獲取下一步驟資訊
        /// </summary>
        /// <param name="taskId">審核流程ID</param>
        /// <returns>下一步驟資訊</returns>
        /// <response code="200">成功獲取下一步驟資訊</response>
        /// <response code="404">找不到指定的審核流程</response>
        [HttpGet("{taskId}/next-step")]
        [ProducesResponseType(typeof(ReviewNextStepOutput), 200)]
        [ProducesResponseType(404)]
        public async Task<ActionResult<ReviewNextStepOutput>> GetNextStep(int taskId)
        {
            var nextStep = await _reviewTaskService.GetNextStepAsync(taskId);
            if (nextStep == null)
            {
                return NotFound("沒有下一個待處理的步驟");
            }
            return Ok(nextStep);
        }

        /// <summary>
        /// 獲取審核人員清單
        /// </summary>
        /// <returns>審核人員清單</returns>
        /// <response code="200">成功獲取審核人員清單</response>
        [HttpGet("users")]
        [ProducesResponseType(typeof(List<ReviewUserOutput>), 200)]
        public async Task<ActionResult<List<ReviewUserOutput>>> GetReviewUsers()
        {
            var users = await _reviewTaskService.GetReviewUsersAsync();
            return Ok(users);
        }
    }
} 
