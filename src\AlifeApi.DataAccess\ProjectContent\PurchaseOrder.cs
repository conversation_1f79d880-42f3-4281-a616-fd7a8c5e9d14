﻿using System;
using System.Collections.Generic;

namespace AlifeApi.DataAccess.ProjectContent
{
    /// <summary>
    /// 儲存客戶預定或購買房屋單位與停車位的訂單詳細資訊 (交易記錄)。
    /// </summary>
    public partial class PurchaseOrder
    {
        /// <summary>
        /// 訂單唯一識別碼 (主鍵, 自動遞增)。
        /// </summary>
        public int OrderId { get; set; }
        /// <summary>
        /// 預定單編號 (可選, 應具備唯一性)。
        /// </summary>
        public string OrderNumber { get; set; }
        /// <summary>
        /// 訂單建立日期。
        /// </summary>
        public DateOnly OrderDate { get; set; }
        /// <summary>
        /// 建案編號 (參考 Sites 表)。
        /// </summary>
        public string SiteCode { get; set; }
        /// <summary>
        /// 買受人ID (參考 Customer 表)。
        /// </summary>
        public int CustomerId { get; set; }
        /// <summary>
        /// 代銷公司名稱。
        /// </summary>
        public string SalesAgencyName { get; set; }
        /// <summary>
        /// 代銷公司行動電話。
        /// </summary>
        public string SalesAgencyMobile { get; set; }
        /// <summary>
        /// 代銷公司市話。
        /// </summary>
        public string SalesAgencyLandline { get; set; }
        /// <summary>
        /// 代銷公司 E-mail。
        /// </summary>
        public string SalesAgencyEmail { get; set; }
        /// <summary>
        /// 購買的主要房屋單位 ID (參考 Units 表)，若僅購買車位則此欄位為 Null。
        /// </summary>
        public int? UnitId { get; set; }
        /// <summary>
        /// 土地持分面積 (坪)。
        /// </summary>
        public decimal? LandShareArea { get; set; }
        /// <summary>
        /// 本次訂單購買的停車位 ID 列表，以逗號分隔 (參考 ParkingSpaces 表)。
        /// </summary>
        public string PurchasedParkingSpaceIds { get; set; }
        /// <summary>
        /// 本次交易的房地售價 (新台幣，不含車位價)。
        /// </summary>
        public decimal? PropertyPrice { get; set; }
        /// <summary>
        /// 本次交易的車位售價 (新台幣，可能是多個車位的總和)。
        /// </summary>
        public decimal? ParkingSpacePrice { get; set; }
        /// <summary>
        /// 本次交易的總價 (新台幣 = PropertyPrice + ParkingSpacePrice)。
        /// </summary>
        public decimal TotalPrice { get; set; }
        /// <summary>
        /// 定金總額 (新台幣)。
        /// </summary>
        public decimal? DepositAmount { get; set; }
        /// <summary>
        /// 已付定金 (新台幣)。
        /// </summary>
        public decimal? DepositPaidAmount { get; set; }
        /// <summary>
        /// 定金付款方式 (建議使用 SYS_Code)。
        /// </summary>
        public string DepositPaymentMethod { get; set; }
        /// <summary>
        /// 定金收款人。
        /// </summary>
        public string DepositPayee { get; set; }
        /// <summary>
        /// 應補足定金的截止日期與時間。
        /// </summary>
        public DateTime? DepositDueDate { get; set; }
        /// <summary>
        /// 應補足的定金金額 (新台幣)。
        /// </summary>
        public decimal? DepositBalanceAmount { get; set; }
        /// <summary>
        /// 約定的簽約日期與時間。
        /// </summary>
        public DateTime? ContractSigningAppointment { get; set; }
        /// <summary>
        /// 簽約金 (新台幣)。
        /// </summary>
        public decimal? ContractSigningAmount { get; set; }
        /// <summary>
        /// 是否同意個人資料收集與利用 (True/False)。
        /// </summary>
        public bool? ConsentToDataUsage { get; set; }
        /// <summary>
        /// 訂單備註 (例如：特殊協議)。
        /// </summary>
        public string OrderRemarks { get; set; }
        /// <summary>
        /// 執行此訂單的銷售人員ID (參考 UserInfo 表)。
        /// </summary>
        public string SalespersonUserInfoId { get; set; }
        /// <summary>
        /// 銷售類型 (例如: &apos;委售&apos;, &apos;自售&apos; - 建議使用 SYS_Code)。
        /// </summary>
        public string SaleType { get; set; }
        /// <summary>
        /// 訂單本身的狀態 (例如: &apos;預訂中&apos;, &apos;已轉訂&apos;, &apos;已簽約&apos;, &apos;已取消&apos;, &apos;已作廢&apos; - 建議使用 SYS_Code)。
        /// </summary>
        public string Status { get; set; }
        /// <summary>
        /// 訂單記錄創建時間 (TIMESTAMP WITHOUT TIME ZONE)。
        /// </summary>
        public DateTime CreatedTime { get; set; }
        /// <summary>
        /// 訂單記錄最後更新時間 (TIMESTAMP WITHOUT TIME ZONE)。
        /// </summary>
        public DateTime UpdatedTime { get; set; }
        /// <summary>
        /// 建立者 (參考 UserInfo 表)。
        /// </summary>
        public string CreatedUserInfoId { get; set; }
        /// <summary>
        /// 更新者 (參考 UserInfo 表)。
        /// </summary>
        public string UpdatedUserInfoId { get; set; }
        /// <summary>
        /// 實際售出日期 (客戶確認購買意願的日期) - 狀態：售
        /// </summary>
        public DateOnly? SaleDate { get; set; }
        /// <summary>
        /// 足訂日期 (定金足額到位的實際日期) - 狀態：足
        /// </summary>
        public DateOnly? DepositFullPaidDate { get; set; }
        /// <summary>
        /// 實際簽約日期 (正式簽約完成的日期) - 狀態：簽
        /// </summary>
        public DateOnly? ContractSignedDate { get; set; }
        /// <summary>
        /// 退訂日期 (客戶退訂或訂單取消的日期)
        /// </summary>
        public DateOnly? CancellationDate { get; set; }
        /// <summary>
        /// 交屋日期 (房屋正式交付給客戶的日期)
        /// </summary>
        public DateOnly? HandoverDate { get; set; }
        /// <summary>
        /// 尾款繳清日期 (最後一筆款項收款完成的日期)
        /// </summary>
        public DateOnly? FinalPaymentDate { get; set; }
        /// <summary>
        /// 請款日期 (向業主請費用的日期) - 狀態：請
        /// </summary>
        public DateOnly? RequestDate { get; set; }
        /// <summary>
        /// 領款日期 (已向業主領到費用的日期) - 狀態：領
        /// </summary>
        public DateOnly? ReceiveDate { get; set; }
        /// <summary>
        /// 實價登錄申報日
        /// </summary>
        public DateOnly? PriceRegistrationSubmissionDate { get; set; }
    }
}
