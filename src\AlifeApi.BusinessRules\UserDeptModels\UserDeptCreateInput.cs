﻿using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;
using AlifeApi.Common.DataAnnotations;

namespace AlifeApi.BusinessRules.UserDeptModels
{
    /// <summary>
    /// 新增部門階層
    /// </summary>
    public class UserDeptCreateInput
    {
        /// <summary>
        /// 部門代號
        /// </summary>
        [JsonPropertyName("Dept")]
        [Required, MaxLength(20)]
        public string DeptId { get; set; }

        /// <summary>
        /// 部門代號名稱
        /// </summary>
        [RequiredForType(typeof(UserDeptCreateInput))]
        [MaxLength(10)]
        public string DeptName { get; set; }

        /// <summary>
        /// 上層部門代號
        /// </summary>
        [JsonPropertyName("ParentDept")]
        [MaxLength(20)]
        public string ParentDeptId { get; set; }

        /// <summary>
        /// 部門主管員工編號
        /// </summary>
        [<PERSON><PERSON>ength(15)]
        public string LeaderUserId { get; set; }

        /// <summary>
        /// 是否停用
        /// </summary>
        [RequiredForType(typeof(UserDeptCreateInput))]
        public bool? IsDisabled { get; set; }
    }
}
