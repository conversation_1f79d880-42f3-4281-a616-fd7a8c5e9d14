#### [AlifeApi.BusinessRules](index.md 'index')
### [AlifeApi.BusinessRules.UnitModels](AlifeApi.BusinessRules.UnitModels.md 'AlifeApi.BusinessRules.UnitModels')

## UnitListOutput Class

房屋單位列表輸出項目

```csharp
public class UnitListOutput
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; UnitListOutput
### Properties

<a name='AlifeApi.BusinessRules.UnitModels.UnitListOutput.BuildingId'></a>

## UnitListOutput.BuildingId Property

建築物ID

```csharp
public int BuildingId { get; set; }
```

#### Property Value
[System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')

<a name='AlifeApi.BusinessRules.UnitModels.UnitListOutput.BuildingName'></a>

## UnitListOutput.BuildingName Property

建築物名稱 (選填)

```csharp
public string? BuildingName { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.UnitModels.UnitListOutput.CreatedTime'></a>

## UnitListOutput.CreatedTime Property

建立時間

```csharp
public System.DateTime CreatedTime { get; set; }
```

#### Property Value
[System.DateTime](https://docs.microsoft.com/en-us/dotnet/api/System.DateTime 'System.DateTime')

<a name='AlifeApi.BusinessRules.UnitModels.UnitListOutput.FloorId'></a>

## UnitListOutput.FloorId Property

樓層ID

```csharp
public int FloorId { get; set; }
```

#### Property Value
[System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')

<a name='AlifeApi.BusinessRules.UnitModels.UnitListOutput.FloorLabel'></a>

## UnitListOutput.FloorLabel Property

樓層標示 (選填)

```csharp
public string? FloorLabel { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.UnitModels.UnitListOutput.Layout'></a>

## UnitListOutput.Layout Property

格局

```csharp
public string? Layout { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.UnitModels.UnitListOutput.ListPrice'></a>

## UnitListOutput.ListPrice Property

列表售價

```csharp
public System.Nullable<decimal> ListPrice { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.Decimal](https://docs.microsoft.com/en-us/dotnet/api/System.Decimal 'System.Decimal')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

<a name='AlifeApi.BusinessRules.UnitModels.UnitListOutput.SiteCode'></a>

## UnitListOutput.SiteCode Property

案場代碼

```csharp
public string SiteCode { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.UnitModels.UnitListOutput.Status'></a>

## UnitListOutput.Status Property

銷售狀態

```csharp
public string Status { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.UnitModels.UnitListOutput.TotalArea'></a>

## UnitListOutput.TotalArea Property

權狀總坪數

```csharp
public decimal TotalArea { get; set; }
```

#### Property Value
[System.Decimal](https://docs.microsoft.com/en-us/dotnet/api/System.Decimal 'System.Decimal')

<a name='AlifeApi.BusinessRules.UnitModels.UnitListOutput.UnitId'></a>

## UnitListOutput.UnitId Property

房屋單位唯一識別碼

```csharp
public int UnitId { get; set; }
```

#### Property Value
[System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')

<a name='AlifeApi.BusinessRules.UnitModels.UnitListOutput.UnitNumber'></a>

## UnitListOutput.UnitNumber Property

戶號

```csharp
public string UnitNumber { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.UnitModels.UnitListOutput.UnitType'></a>

## UnitListOutput.UnitType Property

單位類型

```csharp
public string UnitType { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')