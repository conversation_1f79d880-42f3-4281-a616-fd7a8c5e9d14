using AlifeApi.BusinessRules.CommonModels;
using AlifeApi.BusinessRules.Infrastructure;
using Microsoft.AspNetCore.Mvc;
using System.ComponentModel.DataAnnotations;

namespace AlifeApi.WebApi.Controllers
{
    /// <summary>
    /// 統一銷售管理控制器
    /// 提供房屋和車位的統一查詢與管理功能
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    public class UnifiedSalesController : AuthenticatedController
    {
        private readonly UnifiedSalesManagementService _unifiedSalesService;

        public UnifiedSalesController(UnifiedSalesManagementService unifiedSalesService)
        {
            _unifiedSalesService = unifiedSalesService;
        }

        /// <summary>
        /// 查詢統一銷售物件列表
        /// </summary>
        /// <param name="input">查詢條件</param>
        /// <returns>統一的銷售物件列表，包含房屋和車位</returns>
        [HttpPost("list")]
        [ProducesResponseType(typeof(PagedListOutput<UnifiedSalesItemOutput>), 200)]
        [ProducesResponseType(typeof(string), 400)]
        [ProducesResponseType(500)]
        public async Task<IActionResult> GetUnifiedSalesList([FromBody] UnifiedSalesQueryInput input)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                var result = await _unifiedSalesService.GetUnifiedSalesListAsync(input);
                return Ok(result);
            }
            catch (ArgumentException ex)
            {
                return BadRequest(ex.Message);
            }
            catch (Exception ex)
            {
                return StatusCode(500, $"查詢統一銷售物件列表時發生錯誤: {ex.Message}");
            }
        }

        /// <summary>
        /// 查詢可售物件列表
        /// </summary>
        /// <param name="input">查詢條件（狀態會自動設為可售）</param>
        /// <returns>狀態為可售的房屋和車位列表</returns>
        [HttpPost("available")]
        [ProducesResponseType(typeof(PagedListOutput<UnifiedSalesItemOutput>), 200)]
        [ProducesResponseType(typeof(string), 400)]
        [ProducesResponseType(500)]
        public async Task<IActionResult> GetAvailableItems([FromBody] UnifiedSalesQueryInput input)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                var result = await _unifiedSalesService.GetAvailableItemsAsync(input);
                return Ok(result);
            }
            catch (ArgumentException ex)
            {
                return BadRequest(ex.Message);
            }
            catch (Exception ex)
            {
                return StatusCode(500, $"查詢可售物件列表時發生錯誤: {ex.Message}");
            }
        }

        /// <summary>
        /// 查詢保留物件列表
        /// </summary>
        /// <param name="input">查詢條件（狀態會自動設為保留）</param>
        /// <returns>狀態為保留的房屋和車位列表</returns>
        [HttpPost("reserved")]
        [ProducesResponseType(typeof(PagedListOutput<UnifiedSalesItemOutput>), 200)]
        [ProducesResponseType(typeof(string), 400)]
        [ProducesResponseType(500)]
        public async Task<IActionResult> GetReservedItems([FromBody] UnifiedSalesQueryInput input)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                var result = await _unifiedSalesService.GetReservedItemsAsync(input);
                return Ok(result);
            }
            catch (ArgumentException ex)
            {
                return BadRequest(ex.Message);
            }
            catch (Exception ex)
            {
                return StatusCode(500, $"查詢保留物件列表時發生錯誤: {ex.Message}");
            }
        }

        /// <summary>
        /// 查詢案場銷售統計
        /// </summary>
        /// <param name="siteCode">案場編號</param>
        /// <returns>案場的房屋和車位銷售統計資料</returns>
        [HttpGet("statistics/{siteCode}")]
        [ProducesResponseType(typeof(UnifiedSalesStatistics), 200)]
        [ProducesResponseType(typeof(string), 400)]
        [ProducesResponseType(typeof(string), 404)]
        [ProducesResponseType(500)]
        public async Task<IActionResult> GetUnifiedSalesStatistics([FromRoute] [Required] string siteCode)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(siteCode))
                {
                    return BadRequest("案場編號不能為空");
                }

                var result = await _unifiedSalesService.GetUnifiedSalesStatisticsAsync(siteCode);
                
                if (result == null)
                {
                    return NotFound($"找不到案場編號 '{siteCode}' 的銷售統計資料");
                }

                return Ok(result);
            }
            catch (ArgumentException ex)
            {
                return BadRequest(ex.Message);
            }
            catch (Exception ex)
            {
                return StatusCode(500, $"查詢案場銷售統計時發生錯誤: {ex.Message}");
            }
        }
    }
} 
