﻿using System.ComponentModel.DataAnnotations;

namespace AlifeApi.BusinessRules.Infrastructure
{
    /// <summary>
    /// 停用/啟用資料的參數
    /// </summary>
    /// <typeparam name="T">記錄 ID 的類型</typeparam>
    public class RecordDisableInput<T>
        where T : struct
    {
        /// <summary>
        /// Gets or sets the identifier.
        /// </summary>
        [Required]
        public T? Id { get; set; }

        /// <summary>
        /// Gets or sets the is disabled.
        /// </summary>
        [Required]
        public bool? IsDisabled { get; set; }
    }
}
