﻿CREATE TABLE [dbo].[UserGradePermission] (
    [System]  VARCHAR (30) NOT NULL,
    [GradeId] VARCHAR (20) NOT NULL,
    [FuncId]  VARCHAR (50) NOT NULL,
    CONSTRAINT [PK_UserGradePermission] PRIMARY KEY CLUSTERED ([System] ASC, [GradeId] ASC, [FuncId] ASC),
    CONSTRAINT [FK_UserGradePermission_SYS_MenuFunc] FOREIGN KEY ([System], [FuncId]) REFERENCES [dbo].[SYS_MenuFunc] ([System], [FuncId]),
    CONSTRAINT [FK_UserGradePermission_UserGrade] FOREIGN KEY ([GradeId]) REFERENCES [dbo].[UserGrade] ([Id])
);


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'系統項目識別編號', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'UserGradePermission', @level2type = N'COLUMN', @level2name = N'FuncId';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'職稱識別編號', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'UserGradePermission', @level2type = N'COLUMN', @level2name = N'GradeId';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'系統名稱', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'UserGradePermission', @level2type = N'COLUMN', @level2name = N'System';

