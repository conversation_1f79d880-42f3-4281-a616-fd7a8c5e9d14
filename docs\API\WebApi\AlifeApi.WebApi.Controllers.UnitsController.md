#### [<PERSON>feApi.WebApi](index.md 'index')
### [AlifeApi.WebApi.Controllers](AlifeApi.WebApi.Controllers.md 'AlifeApi.WebApi.Controllers')

## UnitsController Class

房屋單位管理

```csharp
public class UnitsController : AlifeApi.WebApi.Controllers.AuthenticatedController
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; [Microsoft.AspNetCore.Mvc.ControllerBase](https://docs.microsoft.com/en-us/dotnet/api/Microsoft.AspNetCore.Mvc.ControllerBase 'Microsoft.AspNetCore.Mvc.ControllerBase') &#129106; [Microsoft.AspNetCore.Mvc.Controller](https://docs.microsoft.com/en-us/dotnet/api/Microsoft.AspNetCore.Mvc.Controller 'Microsoft.AspNetCore.Mvc.Controller') &#129106; [AlifeApi.WebApi.Controllers.BasicController](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.WebApi.Controllers.BasicController 'AlifeApi.WebApi.Controllers.BasicController') &#129106; [AlifeApi.WebApi.Controllers.AuthenticatedController](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.WebApi.Controllers.AuthenticatedController 'AlifeApi.WebApi.Controllers.AuthenticatedController') &#129106; UnitsController
### Methods

<a name='AlifeApi.WebApi.Controllers.UnitsController.CreateUnit(AlifeApi.BusinessRules.UnitModels.UnitCreateInput)'></a>

## UnitsController.CreateUnit(UnitCreateInput) Method

新增房屋單位

```csharp
public System.Threading.Tasks.Task<Microsoft.AspNetCore.Mvc.ActionResult<int>> CreateUnit(AlifeApi.BusinessRules.UnitModels.UnitCreateInput input);
```
#### Parameters

<a name='AlifeApi.WebApi.Controllers.UnitsController.CreateUnit(AlifeApi.BusinessRules.UnitModels.UnitCreateInput).input'></a>

`input` [AlifeApi.BusinessRules.UnitModels.UnitCreateInput](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.BusinessRules.UnitModels.UnitCreateInput 'AlifeApi.BusinessRules.UnitModels.UnitCreateInput')

房屋單位建立輸入資料

#### Returns
[System.Threading.Tasks.Task&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')[Microsoft.AspNetCore.Mvc.ActionResult&lt;](https://docs.microsoft.com/en-us/dotnet/api/Microsoft.AspNetCore.Mvc.ActionResult-1 'Microsoft.AspNetCore.Mvc.ActionResult`1')[System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/Microsoft.AspNetCore.Mvc.ActionResult-1 'Microsoft.AspNetCore.Mvc.ActionResult`1')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')  
新增的房屋單位ID

<a name='AlifeApi.WebApi.Controllers.UnitsController.DeleteUnit(int)'></a>

## UnitsController.DeleteUnit(int) Method

刪除房屋單位

```csharp
public System.Threading.Tasks.Task<Microsoft.AspNetCore.Mvc.ActionResult> DeleteUnit(int unitId);
```
#### Parameters

<a name='AlifeApi.WebApi.Controllers.UnitsController.DeleteUnit(int).unitId'></a>

`unitId` [System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')

房屋單位ID

#### Returns
[System.Threading.Tasks.Task&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')[Microsoft.AspNetCore.Mvc.ActionResult](https://docs.microsoft.com/en-us/dotnet/api/Microsoft.AspNetCore.Mvc.ActionResult 'Microsoft.AspNetCore.Mvc.ActionResult')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')  
NoContent

<a name='AlifeApi.WebApi.Controllers.UnitsController.GetUnit(int)'></a>

## UnitsController.GetUnit(int) Method

根據房屋單位ID取得詳細資訊

```csharp
public System.Threading.Tasks.Task<Microsoft.AspNetCore.Mvc.ActionResult<AlifeApi.BusinessRules.UnitModels.UnitOutput>> GetUnit(int unitId);
```
#### Parameters

<a name='AlifeApi.WebApi.Controllers.UnitsController.GetUnit(int).unitId'></a>

`unitId` [System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')

房屋單位ID

#### Returns
[System.Threading.Tasks.Task&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')[Microsoft.AspNetCore.Mvc.ActionResult&lt;](https://docs.microsoft.com/en-us/dotnet/api/Microsoft.AspNetCore.Mvc.ActionResult-1 'Microsoft.AspNetCore.Mvc.ActionResult`1')[AlifeApi.BusinessRules.UnitModels.UnitOutput](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.BusinessRules.UnitModels.UnitOutput 'AlifeApi.BusinessRules.UnitModels.UnitOutput')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/Microsoft.AspNetCore.Mvc.ActionResult-1 'Microsoft.AspNetCore.Mvc.ActionResult`1')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')  
房屋單位詳細資訊

<a name='AlifeApi.WebApi.Controllers.UnitsController.GetUnits(AlifeApi.BusinessRules.UnitModels.UnitQueryInput)'></a>

## UnitsController.GetUnits(UnitQueryInput) Method

取得房屋單位列表 (分頁)

```csharp
public System.Threading.Tasks.Task<Microsoft.AspNetCore.Mvc.ActionResult<AlifeApi.BusinessRules.Infrastructure.PagedListOutput<AlifeApi.BusinessRules.UnitModels.UnitListOutput>>> GetUnits(AlifeApi.BusinessRules.UnitModels.UnitQueryInput input);
```
#### Parameters

<a name='AlifeApi.WebApi.Controllers.UnitsController.GetUnits(AlifeApi.BusinessRules.UnitModels.UnitQueryInput).input'></a>

`input` [AlifeApi.BusinessRules.UnitModels.UnitQueryInput](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.BusinessRules.UnitModels.UnitQueryInput 'AlifeApi.BusinessRules.UnitModels.UnitQueryInput')

查詢條件

#### Returns
[System.Threading.Tasks.Task&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')[Microsoft.AspNetCore.Mvc.ActionResult&lt;](https://docs.microsoft.com/en-us/dotnet/api/Microsoft.AspNetCore.Mvc.ActionResult-1 'Microsoft.AspNetCore.Mvc.ActionResult`1')[AlifeApi.BusinessRules.Infrastructure.PagedListOutput&lt;](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.BusinessRules.Infrastructure.PagedListOutput-1 'AlifeApi.BusinessRules.Infrastructure.PagedListOutput`1')[AlifeApi.BusinessRules.UnitModels.UnitListOutput](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.BusinessRules.UnitModels.UnitListOutput 'AlifeApi.BusinessRules.UnitModels.UnitListOutput')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.BusinessRules.Infrastructure.PagedListOutput-1 'AlifeApi.BusinessRules.Infrastructure.PagedListOutput`1')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/Microsoft.AspNetCore.Mvc.ActionResult-1 'Microsoft.AspNetCore.Mvc.ActionResult`1')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')  
分頁的房屋單位列表

<a name='AlifeApi.WebApi.Controllers.UnitsController.UpdateUnit(int,AlifeApi.BusinessRules.UnitModels.UnitUpdateInput)'></a>

## UnitsController.UpdateUnit(int, UnitUpdateInput) Method

更新房屋單位

```csharp
public System.Threading.Tasks.Task<Microsoft.AspNetCore.Mvc.ActionResult> UpdateUnit(int unitId, AlifeApi.BusinessRules.UnitModels.UnitUpdateInput input);
```
#### Parameters

<a name='AlifeApi.WebApi.Controllers.UnitsController.UpdateUnit(int,AlifeApi.BusinessRules.UnitModels.UnitUpdateInput).unitId'></a>

`unitId` [System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')

房屋單位ID

<a name='AlifeApi.WebApi.Controllers.UnitsController.UpdateUnit(int,AlifeApi.BusinessRules.UnitModels.UnitUpdateInput).input'></a>

`input` [AlifeApi.BusinessRules.UnitModels.UnitUpdateInput](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.BusinessRules.UnitModels.UnitUpdateInput 'AlifeApi.BusinessRules.UnitModels.UnitUpdateInput')

房屋單位更新輸入資料

#### Returns
[System.Threading.Tasks.Task&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')[Microsoft.AspNetCore.Mvc.ActionResult](https://docs.microsoft.com/en-us/dotnet/api/Microsoft.AspNetCore.Mvc.ActionResult 'Microsoft.AspNetCore.Mvc.ActionResult')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')  
NoContent