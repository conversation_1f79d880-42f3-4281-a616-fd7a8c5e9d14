#### [AlifeApi.WebApi](index.md 'index')
### [AlifeApi.WebApi.Controllers](AlifeApi.WebApi.Controllers.md 'AlifeApi.WebApi.Controllers')

## UnitsController Class

房屋與車位銷售管理控制器

```csharp
public class UnitsController : AlifeApi.WebApi.Controllers.AuthenticatedController
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; [Microsoft.AspNetCore.Mvc.ControllerBase](https://docs.microsoft.com/en-us/dotnet/api/Microsoft.AspNetCore.Mvc.ControllerBase 'Microsoft.AspNetCore.Mvc.ControllerBase') &#129106; [Microsoft.AspNetCore.Mvc.Controller](https://docs.microsoft.com/en-us/dotnet/api/Microsoft.AspNetCore.Mvc.Controller 'Microsoft.AspNetCore.Mvc.Controller') &#129106; [AlifeApi.WebApi.Controllers.BasicController](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.WebApi.Controllers.BasicController 'AlifeApi.WebApi.Controllers.BasicController') &#129106; [AlifeApi.WebApi.Controllers.AuthenticatedController](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.WebApi.Controllers.AuthenticatedController 'AlifeApi.WebApi.Controllers.AuthenticatedController') &#129106; UnitsController
### Methods

<a name='AlifeApi.WebApi.Controllers.UnitsController.Create(AlifeApi.BusinessRules.UnitModels.UnitCreateInput)'></a>

## UnitsController.Create(UnitCreateInput) Method

建立新的房屋單位

```csharp
public System.Threading.Tasks.Task<Microsoft.AspNetCore.Mvc.IActionResult> Create(AlifeApi.BusinessRules.UnitModels.UnitCreateInput input);
```
#### Parameters

<a name='AlifeApi.WebApi.Controllers.UnitsController.Create(AlifeApi.BusinessRules.UnitModels.UnitCreateInput).input'></a>

`input` [AlifeApi.BusinessRules.UnitModels.UnitCreateInput](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.BusinessRules.UnitModels.UnitCreateInput 'AlifeApi.BusinessRules.UnitModels.UnitCreateInput')

#### Returns
[System.Threading.Tasks.Task&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')[Microsoft.AspNetCore.Mvc.IActionResult](https://docs.microsoft.com/en-us/dotnet/api/Microsoft.AspNetCore.Mvc.IActionResult 'Microsoft.AspNetCore.Mvc.IActionResult')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')

<a name='AlifeApi.WebApi.Controllers.UnitsController.Delete(int)'></a>

## UnitsController.Delete(int) Method

刪除房屋單位

```csharp
public System.Threading.Tasks.Task<Microsoft.AspNetCore.Mvc.IActionResult> Delete(int id);
```
#### Parameters

<a name='AlifeApi.WebApi.Controllers.UnitsController.Delete(int).id'></a>

`id` [System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')

#### Returns
[System.Threading.Tasks.Task&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')[Microsoft.AspNetCore.Mvc.IActionResult](https://docs.microsoft.com/en-us/dotnet/api/Microsoft.AspNetCore.Mvc.IActionResult 'Microsoft.AspNetCore.Mvc.IActionResult')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')

<a name='AlifeApi.WebApi.Controllers.UnitsController.Get(int)'></a>

## UnitsController.Get(int) Method

根據 ID 取得單一房屋單位

```csharp
public System.Threading.Tasks.Task<Microsoft.AspNetCore.Mvc.ActionResult<AlifeApi.BusinessRules.UnitModels.UnitOutput>> Get(int id);
```
#### Parameters

<a name='AlifeApi.WebApi.Controllers.UnitsController.Get(int).id'></a>

`id` [System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')

#### Returns
[System.Threading.Tasks.Task&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')[Microsoft.AspNetCore.Mvc.ActionResult&lt;](https://docs.microsoft.com/en-us/dotnet/api/Microsoft.AspNetCore.Mvc.ActionResult-1 'Microsoft.AspNetCore.Mvc.ActionResult`1')[AlifeApi.BusinessRules.UnitModels.UnitOutput](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.BusinessRules.UnitModels.UnitOutput 'AlifeApi.BusinessRules.UnitModels.UnitOutput')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/Microsoft.AspNetCore.Mvc.ActionResult-1 'Microsoft.AspNetCore.Mvc.ActionResult`1')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')

<a name='AlifeApi.WebApi.Controllers.UnitsController.GetUnits(AlifeApi.BusinessRules.UnitModels.UnitListInput)'></a>

## UnitsController.GetUnits(UnitListInput) Method

取得房屋單位列表 (分頁)

```csharp
public System.Threading.Tasks.Task<Microsoft.AspNetCore.Mvc.ActionResult<AlifeApi.BusinessRules.Infrastructure.PagedListOutput<AlifeApi.BusinessRules.UnitModels.UnitOutput>>> GetUnits(AlifeApi.BusinessRules.UnitModels.UnitListInput input);
```
#### Parameters

<a name='AlifeApi.WebApi.Controllers.UnitsController.GetUnits(AlifeApi.BusinessRules.UnitModels.UnitListInput).input'></a>

`input` [AlifeApi.BusinessRules.UnitModels.UnitListInput](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.BusinessRules.UnitModels.UnitListInput 'AlifeApi.BusinessRules.UnitModels.UnitListInput')

#### Returns
[System.Threading.Tasks.Task&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')[Microsoft.AspNetCore.Mvc.ActionResult&lt;](https://docs.microsoft.com/en-us/dotnet/api/Microsoft.AspNetCore.Mvc.ActionResult-1 'Microsoft.AspNetCore.Mvc.ActionResult`1')[AlifeApi.BusinessRules.Infrastructure.PagedListOutput&lt;](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.BusinessRules.Infrastructure.PagedListOutput-1 'AlifeApi.BusinessRules.Infrastructure.PagedListOutput`1')[AlifeApi.BusinessRules.UnitModels.UnitOutput](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.BusinessRules.UnitModels.UnitOutput 'AlifeApi.BusinessRules.UnitModels.UnitOutput')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.BusinessRules.Infrastructure.PagedListOutput-1 'AlifeApi.BusinessRules.Infrastructure.PagedListOutput`1')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/Microsoft.AspNetCore.Mvc.ActionResult-1 'Microsoft.AspNetCore.Mvc.ActionResult`1')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')

<a name='AlifeApi.WebApi.Controllers.UnitsController.Update(int,AlifeApi.BusinessRules.UnitModels.UnitUpdateInput)'></a>

## UnitsController.Update(int, UnitUpdateInput) Method

更新現有的房屋單位

```csharp
public System.Threading.Tasks.Task<Microsoft.AspNetCore.Mvc.IActionResult> Update(int id, AlifeApi.BusinessRules.UnitModels.UnitUpdateInput input);
```
#### Parameters

<a name='AlifeApi.WebApi.Controllers.UnitsController.Update(int,AlifeApi.BusinessRules.UnitModels.UnitUpdateInput).id'></a>

`id` [System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')

<a name='AlifeApi.WebApi.Controllers.UnitsController.Update(int,AlifeApi.BusinessRules.UnitModels.UnitUpdateInput).input'></a>

`input` [AlifeApi.BusinessRules.UnitModels.UnitUpdateInput](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.BusinessRules.UnitModels.UnitUpdateInput 'AlifeApi.BusinessRules.UnitModels.UnitUpdateInput')

#### Returns
[System.Threading.Tasks.Task&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')[Microsoft.AspNetCore.Mvc.IActionResult](https://docs.microsoft.com/en-us/dotnet/api/Microsoft.AspNetCore.Mvc.IActionResult 'Microsoft.AspNetCore.Mvc.IActionResult')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')