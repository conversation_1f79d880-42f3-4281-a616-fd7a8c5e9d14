#### [AlifeApi.BusinessRules](index.md 'index')
### [AlifeApi.BusinessRules.DailyReportModels](AlifeApi.BusinessRules.DailyReportModels.md 'AlifeApi.BusinessRules.DailyReportModels')

## DailyReportQueryInput Class

日報查詢輸入DTO

```csharp
public class DailyReportQueryInput
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; DailyReportQueryInput
### Properties

<a name='AlifeApi.BusinessRules.DailyReportModels.DailyReportQueryInput.ReportDate'></a>

## DailyReportQueryInput.ReportDate Property

查詢日期

```csharp
public System.DateTime ReportDate { get; set; }
```

#### Property Value
[System.DateTime](https://docs.microsoft.com/en-us/dotnet/api/System.DateTime 'System.DateTime')

<a name='AlifeApi.BusinessRules.DailyReportModels.DailyReportQueryInput.SiteCode'></a>

## DailyReportQueryInput.SiteCode Property

案場編號

```csharp
public string SiteCode { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')