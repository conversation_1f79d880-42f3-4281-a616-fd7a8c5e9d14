#### [AlifeApi.BusinessRules](index.md 'index')

## <PERSON>feApi.BusinessRules.ParkingSpaceModels Namespace

| Classes | |
| :--- | :--- |
| [ParkingSpaceBasicInfoOutput](AlifeApi.BusinessRules.ParkingSpaceModels.ParkingSpaceBasicInfoOutput.md 'AlifeApi.BusinessRules.ParkingSpaceModels.ParkingSpaceBasicInfoOutput') | 停車位基本資訊輸出模型 (用於 PurchaseOrderOutput) |
| [ParkingSpaceCreateInput](AlifeApi.BusinessRules.ParkingSpaceModels.ParkingSpaceCreateInput.md 'AlifeApi.BusinessRules.ParkingSpaceModels.ParkingSpaceCreateInput') | 停車位建立輸入模型 |
| [ParkingSpaceListOutput](AlifeApi.BusinessRules.ParkingSpaceModels.ParkingSpaceListOutput.md 'AlifeApi.BusinessRules.ParkingSpaceModels.ParkingSpaceListOutput') | 停車位列表輸出項目 |
| [ParkingSpaceOutput](AlifeApi.BusinessRules.ParkingSpaceModels.ParkingSpaceOutput.md 'AlifeApi.BusinessRules.ParkingSpaceModels.ParkingSpaceOutput') | 停車位詳細輸出模型 |
| [ParkingSpaceQueryInput](AlifeApi.BusinessRules.ParkingSpaceModels.ParkingSpaceQueryInput.md 'AlifeApi.BusinessRules.ParkingSpaceModels.ParkingSpaceQueryInput') | 停車位列表查詢輸入模型 |
| [ParkingSpaceService](AlifeApi.BusinessRules.ParkingSpaceModels.ParkingSpaceService.md 'AlifeApi.BusinessRules.ParkingSpaceModels.ParkingSpaceService') | 停車位服務 |
| [ParkingSpaceUpdateInput](AlifeApi.BusinessRules.ParkingSpaceModels.ParkingSpaceUpdateInput.md 'AlifeApi.BusinessRules.ParkingSpaceModels.ParkingSpaceUpdateInput') | 停車位更新輸入模型 |
