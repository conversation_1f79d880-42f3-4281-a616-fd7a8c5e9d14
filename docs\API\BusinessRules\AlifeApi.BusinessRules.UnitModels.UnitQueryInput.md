#### [AlifeApi.BusinessRules](index.md 'index')
### [AlifeApi.BusinessRules.UnitModels](AlifeApi.BusinessRules.UnitModels.md 'AlifeApi.BusinessRules.UnitModels')

## UnitQueryInput Class

房屋單位列表查詢輸入模型

```csharp
public class UnitQueryInput : AlifeApi.BusinessRules.Infrastructure.PagedListInput
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; [PagedListInput](AlifeApi.BusinessRules.Infrastructure.PagedListInput.md 'AlifeApi.BusinessRules.Infrastructure.PagedListInput') &#129106; UnitQueryInput
### Properties

<a name='AlifeApi.BusinessRules.UnitModels.UnitQueryInput.MaxTotalArea'></a>

## UnitQueryInput.MaxTotalArea Property

最大總坪數 (篩選)

```csharp
public System.Nullable<decimal> MaxTotalArea { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.Decimal](https://docs.microsoft.com/en-us/dotnet/api/System.Decimal 'System.Decimal')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

<a name='AlifeApi.BusinessRules.UnitModels.UnitQueryInput.MinTotalArea'></a>

## UnitQueryInput.MinTotalArea Property

最小總坪數 (篩選)

```csharp
public System.Nullable<decimal> MinTotalArea { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.Decimal](https://docs.microsoft.com/en-us/dotnet/api/System.Decimal 'System.Decimal')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

<a name='AlifeApi.BusinessRules.UnitModels.UnitQueryInput.Status'></a>

## UnitQueryInput.Status Property

狀態 (篩選)

```csharp
public string? Status { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.UnitModels.UnitQueryInput.UnitNumber'></a>

## UnitQueryInput.UnitNumber Property

戶號 (篩選)

```csharp
public string? UnitNumber { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')