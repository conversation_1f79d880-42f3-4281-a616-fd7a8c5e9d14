#### [AlifeApi.BusinessRules](index.md 'index')
### [AlifeApi.BusinessRules.CategoryModels](AlifeApi.BusinessRules.CategoryModels.md 'AlifeApi.BusinessRules.CategoryModels')

## MediumCategoryDropdownOutput Class

中分類下拉選單輸出

```csharp
public class MediumCategoryDropdownOutput
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; MediumCategoryDropdownOutput
### Properties

<a name='AlifeApi.BusinessRules.CategoryModels.MediumCategoryDropdownOutput.LargeCategoryId'></a>

## MediumCategoryDropdownOutput.LargeCategoryId Property

所屬大分類ID

```csharp
public long LargeCategoryId { get; set; }
```

#### Property Value
[System.Int64](https://docs.microsoft.com/en-us/dotnet/api/System.Int64 'System.Int64')

<a name='AlifeApi.BusinessRules.CategoryModels.MediumCategoryDropdownOutput.LargeCategoryName'></a>

## MediumCategoryDropdownOutput.LargeCategoryName Property

所屬大分類名稱

```csharp
public string LargeCategoryName { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.CategoryModels.MediumCategoryDropdownOutput.MediumCategoryId'></a>

## MediumCategoryDropdownOutput.MediumCategoryId Property

中分類ID

```csharp
public long MediumCategoryId { get; set; }
```

#### Property Value
[System.Int64](https://docs.microsoft.com/en-us/dotnet/api/System.Int64 'System.Int64')

<a name='AlifeApi.BusinessRules.CategoryModels.MediumCategoryDropdownOutput.Name'></a>

## MediumCategoryDropdownOutput.Name Property

中分類名稱

```csharp
public string Name { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.CategoryModels.MediumCategoryDropdownOutput.SortOrder'></a>

## MediumCategoryDropdownOutput.SortOrder Property

排序順序

```csharp
public int SortOrder { get; set; }
```

#### Property Value
[System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')