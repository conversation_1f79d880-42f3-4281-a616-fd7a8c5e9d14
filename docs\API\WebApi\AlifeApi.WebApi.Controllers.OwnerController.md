#### [<PERSON>feApi.WebApi](index.md 'index')
### [AlifeApi.WebApi.Controllers](AlifeApi.WebApi.Controllers.md 'AlifeApi.WebApi.Controllers')

## OwnerController Class

業主資料管理 API

```csharp
public class OwnerController : AlifeApi.WebApi.Controllers.AuthenticatedController
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; [Microsoft.AspNetCore.Mvc.ControllerBase](https://docs.microsoft.com/en-us/dotnet/api/Microsoft.AspNetCore.Mvc.ControllerBase 'Microsoft.AspNetCore.Mvc.ControllerBase') &#129106; [Microsoft.AspNetCore.Mvc.Controller](https://docs.microsoft.com/en-us/dotnet/api/Microsoft.AspNetCore.Mvc.Controller 'Microsoft.AspNetCore.Mvc.Controller') &#129106; [AlifeApi.WebApi.Controllers.BasicController](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.WebApi.Controllers.BasicController 'AlifeApi.WebApi.Controllers.BasicController') &#129106; [AlifeApi.WebApi.Controllers.AuthenticatedController](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.WebApi.Controllers.AuthenticatedController 'AlifeApi.WebApi.Controllers.AuthenticatedController') &#129106; OwnerController
### Constructors

<a name='AlifeApi.WebApi.Controllers.OwnerController.OwnerController(AlifeApi.BusinessRules.OwnerModels.OwnerService)'></a>

## OwnerController(OwnerService) Constructor

建構子

```csharp
public OwnerController(AlifeApi.BusinessRules.OwnerModels.OwnerService ownerService);
```
#### Parameters

<a name='AlifeApi.WebApi.Controllers.OwnerController.OwnerController(AlifeApi.BusinessRules.OwnerModels.OwnerService).ownerService'></a>

`ownerService` [AlifeApi.BusinessRules.OwnerModels.OwnerService](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.BusinessRules.OwnerModels.OwnerService 'AlifeApi.BusinessRules.OwnerModels.OwnerService')
### Methods

<a name='AlifeApi.WebApi.Controllers.OwnerController.CreateOwnerAsync(AlifeApi.BusinessRules.OwnerModels.OwnerCreateInput)'></a>

## OwnerController.CreateOwnerAsync(OwnerCreateInput) Method

建立新業主

```csharp
public System.Threading.Tasks.Task<Microsoft.AspNetCore.Mvc.ActionResult<AlifeApi.BusinessRules.OwnerModels.OwnerUpdateOutput>> CreateOwnerAsync(AlifeApi.BusinessRules.OwnerModels.OwnerCreateInput input);
```
#### Parameters

<a name='AlifeApi.WebApi.Controllers.OwnerController.CreateOwnerAsync(AlifeApi.BusinessRules.OwnerModels.OwnerCreateInput).input'></a>

`input` [AlifeApi.BusinessRules.OwnerModels.OwnerCreateInput](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.BusinessRules.OwnerModels.OwnerCreateInput 'AlifeApi.BusinessRules.OwnerModels.OwnerCreateInput')

建立業主輸入資料

#### Returns
[System.Threading.Tasks.Task&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')[Microsoft.AspNetCore.Mvc.ActionResult&lt;](https://docs.microsoft.com/en-us/dotnet/api/Microsoft.AspNetCore.Mvc.ActionResult-1 'Microsoft.AspNetCore.Mvc.ActionResult`1')[AlifeApi.BusinessRules.OwnerModels.OwnerUpdateOutput](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.BusinessRules.OwnerModels.OwnerUpdateOutput 'AlifeApi.BusinessRules.OwnerModels.OwnerUpdateOutput')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/Microsoft.AspNetCore.Mvc.ActionResult-1 'Microsoft.AspNetCore.Mvc.ActionResult`1')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')  
執行結果

<a name='AlifeApi.WebApi.Controllers.OwnerController.DeleteOwnerAsync(int)'></a>

## OwnerController.DeleteOwnerAsync(int) Method

刪除業主資料

```csharp
public System.Threading.Tasks.Task<Microsoft.AspNetCore.Mvc.ActionResult<AlifeApi.BusinessRules.OwnerModels.OwnerUpdateOutput>> DeleteOwnerAsync(int ownerId);
```
#### Parameters

<a name='AlifeApi.WebApi.Controllers.OwnerController.DeleteOwnerAsync(int).ownerId'></a>

`ownerId` [System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')

業主ID

#### Returns
[System.Threading.Tasks.Task&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')[Microsoft.AspNetCore.Mvc.ActionResult&lt;](https://docs.microsoft.com/en-us/dotnet/api/Microsoft.AspNetCore.Mvc.ActionResult-1 'Microsoft.AspNetCore.Mvc.ActionResult`1')[AlifeApi.BusinessRules.OwnerModels.OwnerUpdateOutput](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.BusinessRules.OwnerModels.OwnerUpdateOutput 'AlifeApi.BusinessRules.OwnerModels.OwnerUpdateOutput')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/Microsoft.AspNetCore.Mvc.ActionResult-1 'Microsoft.AspNetCore.Mvc.ActionResult`1')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')  
執行結果

<a name='AlifeApi.WebApi.Controllers.OwnerController.GetOwnerAsync(int)'></a>

## OwnerController.GetOwnerAsync(int) Method

取得單一業主詳細資料

```csharp
public System.Threading.Tasks.Task<Microsoft.AspNetCore.Mvc.ActionResult<AlifeApi.BusinessRules.OwnerModels.OwnerGetOutput>> GetOwnerAsync(int ownerId);
```
#### Parameters

<a name='AlifeApi.WebApi.Controllers.OwnerController.GetOwnerAsync(int).ownerId'></a>

`ownerId` [System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')

業主ID

#### Returns
[System.Threading.Tasks.Task&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')[Microsoft.AspNetCore.Mvc.ActionResult&lt;](https://docs.microsoft.com/en-us/dotnet/api/Microsoft.AspNetCore.Mvc.ActionResult-1 'Microsoft.AspNetCore.Mvc.ActionResult`1')[AlifeApi.BusinessRules.OwnerModels.OwnerGetOutput](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.BusinessRules.OwnerModels.OwnerGetOutput 'AlifeApi.BusinessRules.OwnerModels.OwnerGetOutput')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/Microsoft.AspNetCore.Mvc.ActionResult-1 'Microsoft.AspNetCore.Mvc.ActionResult`1')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')  
業主詳細資料

<a name='AlifeApi.WebApi.Controllers.OwnerController.GetOwnerListAsync(AlifeApi.BusinessRules.OwnerModels.OwnerListGetInput)'></a>

## OwnerController.GetOwnerListAsync(OwnerListGetInput) Method

取得業主列表 (分頁)

```csharp
public System.Threading.Tasks.Task<Microsoft.AspNetCore.Mvc.ActionResult<AlifeApi.BusinessRules.Infrastructure.PagedListOutput<AlifeApi.BusinessRules.OwnerModels.OwnerListItemGetOutput>>> GetOwnerListAsync(AlifeApi.BusinessRules.OwnerModels.OwnerListGetInput input);
```
#### Parameters

<a name='AlifeApi.WebApi.Controllers.OwnerController.GetOwnerListAsync(AlifeApi.BusinessRules.OwnerModels.OwnerListGetInput).input'></a>

`input` [AlifeApi.BusinessRules.OwnerModels.OwnerListGetInput](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.BusinessRules.OwnerModels.OwnerListGetInput 'AlifeApi.BusinessRules.OwnerModels.OwnerListGetInput')

查詢條件

#### Returns
[System.Threading.Tasks.Task&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')[Microsoft.AspNetCore.Mvc.ActionResult&lt;](https://docs.microsoft.com/en-us/dotnet/api/Microsoft.AspNetCore.Mvc.ActionResult-1 'Microsoft.AspNetCore.Mvc.ActionResult`1')[AlifeApi.BusinessRules.Infrastructure.PagedListOutput&lt;](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.BusinessRules.Infrastructure.PagedListOutput-1 'AlifeApi.BusinessRules.Infrastructure.PagedListOutput`1')[AlifeApi.BusinessRules.OwnerModels.OwnerListItemGetOutput](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.BusinessRules.OwnerModels.OwnerListItemGetOutput 'AlifeApi.BusinessRules.OwnerModels.OwnerListItemGetOutput')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.BusinessRules.Infrastructure.PagedListOutput-1 'AlifeApi.BusinessRules.Infrastructure.PagedListOutput`1')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/Microsoft.AspNetCore.Mvc.ActionResult-1 'Microsoft.AspNetCore.Mvc.ActionResult`1')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')  
業主列表

<a name='AlifeApi.WebApi.Controllers.OwnerController.UpdateOwnerAsync(int,AlifeApi.BusinessRules.OwnerModels.OwnerUpdateInput)'></a>

## OwnerController.UpdateOwnerAsync(int, OwnerUpdateInput) Method

更新業主資料

```csharp
public System.Threading.Tasks.Task<Microsoft.AspNetCore.Mvc.ActionResult<AlifeApi.BusinessRules.OwnerModels.OwnerUpdateOutput>> UpdateOwnerAsync(int ownerId, AlifeApi.BusinessRules.OwnerModels.OwnerUpdateInput input);
```
#### Parameters

<a name='AlifeApi.WebApi.Controllers.OwnerController.UpdateOwnerAsync(int,AlifeApi.BusinessRules.OwnerModels.OwnerUpdateInput).ownerId'></a>

`ownerId` [System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')

業主ID

<a name='AlifeApi.WebApi.Controllers.OwnerController.UpdateOwnerAsync(int,AlifeApi.BusinessRules.OwnerModels.OwnerUpdateInput).input'></a>

`input` [AlifeApi.BusinessRules.OwnerModels.OwnerUpdateInput](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.BusinessRules.OwnerModels.OwnerUpdateInput 'AlifeApi.BusinessRules.OwnerModels.OwnerUpdateInput')

更新業主輸入資料

#### Returns
[System.Threading.Tasks.Task&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')[Microsoft.AspNetCore.Mvc.ActionResult&lt;](https://docs.microsoft.com/en-us/dotnet/api/Microsoft.AspNetCore.Mvc.ActionResult-1 'Microsoft.AspNetCore.Mvc.ActionResult`1')[AlifeApi.BusinessRules.OwnerModels.OwnerUpdateOutput](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.BusinessRules.OwnerModels.OwnerUpdateOutput 'AlifeApi.BusinessRules.OwnerModels.OwnerUpdateOutput')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/Microsoft.AspNetCore.Mvc.ActionResult-1 'Microsoft.AspNetCore.Mvc.ActionResult`1')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')  
執行結果