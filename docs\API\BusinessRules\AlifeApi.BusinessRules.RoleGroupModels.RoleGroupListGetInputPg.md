#### [AlifeApi.BusinessRules](index.md 'index')
### [AlifeApi.BusinessRules.RoleGroupModels](AlifeApi.BusinessRules.RoleGroupModels.md 'AlifeApi.BusinessRules.RoleGroupModels')

## RoleGroupListGetInputPg Class

取得角色權限列表（PostgreSQL 版本）

```csharp
public class RoleGroupListGetInputPg : AlifeApi.BusinessRules.Infrastructure.PagedListInput
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; [PagedListInput](AlifeApi.BusinessRules.Infrastructure.PagedListInput.md 'AlifeApi.BusinessRules.Infrastructure.PagedListInput') &#129106; RoleGroupListGetInputPg
### Properties

<a name='AlifeApi.BusinessRules.RoleGroupModels.RoleGroupListGetInputPg.IncludeGlobalRoles'></a>

## RoleGroupListGetInputPg.IncludeGlobalRoles Property

是否包含全局角色（SiteCode 為 null 的角色）

```csharp
public bool IncludeGlobalRoles { get; set; }
```

#### Property Value
[System.Boolean](https://docs.microsoft.com/en-us/dotnet/api/System.Boolean 'System.Boolean')

<a name='AlifeApi.BusinessRules.RoleGroupModels.RoleGroupListGetInputPg.SiteCode'></a>

## RoleGroupListGetInputPg.SiteCode Property

案場代碼，對應 Sites 表的 SiteCode

```csharp
public string SiteCode { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')