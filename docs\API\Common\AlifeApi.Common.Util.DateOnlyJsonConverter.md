#### [AlifeApi.Common](index.md 'index')
### [AlifeApi.Common.Util](AlifeApi.Common.Util.md 'AlifeApi.Common.Util')

## DateOnlyJsonConverter Class

DateOnly 的 JSON 轉換器

```csharp
public class DateOnlyJsonConverter : System.Text.Json.Serialization.JsonConverter<System.DateOnly>
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; [System.Text.Json.Serialization.JsonConverter](https://docs.microsoft.com/en-us/dotnet/api/System.Text.Json.Serialization.JsonConverter 'System.Text.Json.Serialization.JsonConverter') &#129106; [System.Text.Json.Serialization.JsonConverter&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Text.Json.Serialization.JsonConverter-1 'System.Text.Json.Serialization.JsonConverter`1')[System.DateOnly](https://docs.microsoft.com/en-us/dotnet/api/System.DateOnly 'System.DateOnly')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Text.Json.Serialization.JsonConverter-1 'System.Text.Json.Serialization.JsonConverter`1') &#129106; DateOnlyJsonConverter
### Methods

<a name='AlifeApi.Common.Util.DateOnlyJsonConverter.Read(System.Text.Json.Utf8JsonReader,System.Type,System.Text.Json.JsonSerializerOptions)'></a>

## DateOnlyJsonConverter.Read(Utf8JsonReader, Type, JsonSerializerOptions) Method

讀取 JSON

```csharp
public override System.DateOnly Read(ref System.Text.Json.Utf8JsonReader reader, System.Type typeToConvert, System.Text.Json.JsonSerializerOptions options);
```
#### Parameters

<a name='AlifeApi.Common.Util.DateOnlyJsonConverter.Read(System.Text.Json.Utf8JsonReader,System.Type,System.Text.Json.JsonSerializerOptions).reader'></a>

`reader` [System.Text.Json.Utf8JsonReader](https://docs.microsoft.com/en-us/dotnet/api/System.Text.Json.Utf8JsonReader 'System.Text.Json.Utf8JsonReader')

<a name='AlifeApi.Common.Util.DateOnlyJsonConverter.Read(System.Text.Json.Utf8JsonReader,System.Type,System.Text.Json.JsonSerializerOptions).typeToConvert'></a>

`typeToConvert` [System.Type](https://docs.microsoft.com/en-us/dotnet/api/System.Type 'System.Type')

<a name='AlifeApi.Common.Util.DateOnlyJsonConverter.Read(System.Text.Json.Utf8JsonReader,System.Type,System.Text.Json.JsonSerializerOptions).options'></a>

`options` [System.Text.Json.JsonSerializerOptions](https://docs.microsoft.com/en-us/dotnet/api/System.Text.Json.JsonSerializerOptions 'System.Text.Json.JsonSerializerOptions')

#### Returns
[System.DateOnly](https://docs.microsoft.com/en-us/dotnet/api/System.DateOnly 'System.DateOnly')

<a name='AlifeApi.Common.Util.DateOnlyJsonConverter.Write(System.Text.Json.Utf8JsonWriter,System.DateOnly,System.Text.Json.JsonSerializerOptions)'></a>

## DateOnlyJsonConverter.Write(Utf8JsonWriter, DateOnly, JsonSerializerOptions) Method

寫入 JSON

```csharp
public override void Write(System.Text.Json.Utf8JsonWriter writer, System.DateOnly value, System.Text.Json.JsonSerializerOptions options);
```
#### Parameters

<a name='AlifeApi.Common.Util.DateOnlyJsonConverter.Write(System.Text.Json.Utf8JsonWriter,System.DateOnly,System.Text.Json.JsonSerializerOptions).writer'></a>

`writer` [System.Text.Json.Utf8JsonWriter](https://docs.microsoft.com/en-us/dotnet/api/System.Text.Json.Utf8JsonWriter 'System.Text.Json.Utf8JsonWriter')

<a name='AlifeApi.Common.Util.DateOnlyJsonConverter.Write(System.Text.Json.Utf8JsonWriter,System.DateOnly,System.Text.Json.JsonSerializerOptions).value'></a>

`value` [System.DateOnly](https://docs.microsoft.com/en-us/dotnet/api/System.DateOnly 'System.DateOnly')

<a name='AlifeApi.Common.Util.DateOnlyJsonConverter.Write(System.Text.Json.Utf8JsonWriter,System.DateOnly,System.Text.Json.JsonSerializerOptions).options'></a>

`options` [System.Text.Json.JsonSerializerOptions](https://docs.microsoft.com/en-us/dotnet/api/System.Text.Json.JsonSerializerOptions 'System.Text.Json.JsonSerializerOptions')