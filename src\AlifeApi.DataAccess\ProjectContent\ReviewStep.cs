﻿using System;
using System.Collections.Generic;

namespace AlifeApi.DataAccess.ProjectContent
{
    /// <summary>
    /// 審核步驟表，用於儲存每個審核任務的具體步驟資訊。
    /// </summary>
    public partial class ReviewStep
    {
        public ReviewStep()
        {
            NotificationSettings = new HashSet<NotificationSetting>();
            ReviewApprovers = new HashSet<ReviewApprover>();
        }

        /// <summary>
        /// 步驟流水號，主鍵，自動遞增，用於唯一識別審核步驟。
        /// </summary>
        public int StepId { get; set; }
        /// <summary>
        /// 任務流水號，對應 ReviewTasks 表的 TaskId。
        /// </summary>
        public int? TaskId { get; set; }
        /// <summary>
        /// 步驟名稱，描述步驟的具體內容。
        /// </summary>
        public string Name { get; set; }
        /// <summary>
        /// 步驟順序，用於定義步驟的執行順序。
        /// </summary>
        public int Sequence { get; set; }
        /// <summary>
        /// 時間限制，以小時為單位，指定步驟必須完成的時間。
        /// </summary>
        public int? TimeLimit { get; set; }
        /// <summary>
        /// 步驟狀態，可選值為 waiting（等待中）、active（進行中）、completed（已完成）、skipped（已跳過）、rejected（已拒絕）。
        /// </summary>
        public string Status { get; set; }

        public virtual ReviewTask Task { get; set; }
        public virtual ICollection<NotificationSetting> NotificationSettings { get; set; }
        public virtual ICollection<ReviewApprover> ReviewApprovers { get; set; }
    }
}
