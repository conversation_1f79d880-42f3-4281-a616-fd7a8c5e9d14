#### [AlifeApi.DataAccess](index.md 'index')

## AlifeApi.DataAccess.ProjectContent Namespace

| Classes | |
| :--- | :--- |
| [Building](AlifeApi.DataAccess.ProjectContent.Building.md 'AlifeApi.DataAccess.ProjectContent.Building') | 儲存案場內每一棟獨立建築的基本資訊。 |
| [Company](AlifeApi.DataAccess.ProjectContent.Company.md 'AlifeApi.DataAccess.ProjectContent.Company') | 公司資料表，用於儲存公司基本資訊。 |
| [CrmOptionType](AlifeApi.DataAccess.ProjectContent.CrmOptionType.md 'AlifeApi.DataAccess.ProjectContent.CrmOptionType') | CRM中可由案場自訂的下拉選單類型 |
| [Customer](AlifeApi.DataAccess.ProjectContent.Customer.md 'AlifeApi.DataAccess.ProjectContent.Customer') | 客戶基本資料表 |
| [CustomerRecord](AlifeApi.DataAccess.ProjectContent.CustomerRecord.md 'AlifeApi.DataAccess.ProjectContent.CustomerRecord') | 客戶互動或訪談記錄表 |
| [Department](AlifeApi.DataAccess.ProjectContent.Department.md 'AlifeApi.DataAccess.ProjectContent.Department') | 部門資料表，用於儲存每個公司的部門資訊。 |
| [EmailSendLog](AlifeApi.DataAccess.ProjectContent.EmailSendLog.md 'AlifeApi.DataAccess.ProjectContent.EmailSendLog') | 電子郵件發送記錄表，用於儲存系統發送電子郵件的記錄資訊。 |
| [Floor](AlifeApi.DataAccess.ProjectContent.Floor.md 'AlifeApi.DataAccess.ProjectContent.Floor') | 儲存建築物內每個樓層的資訊，包含住宅/商業樓層和停車場樓層。 |
| [JobTitle](AlifeApi.DataAccess.ProjectContent.JobTitle.md 'AlifeApi.DataAccess.ProjectContent.JobTitle') | 職位資料表，用於儲存每個公司的職位資訊。 |
| [LargeCategory](AlifeApi.DataAccess.ProjectContent.LargeCategory.md 'AlifeApi.DataAccess.ProjectContent.LargeCategory') | 商品大分類資料表 |
| [MediumCategory](AlifeApi.DataAccess.ProjectContent.MediumCategory.md 'AlifeApi.DataAccess.ProjectContent.MediumCategory') | 商品中分類資料表 |
| [NotificationSetting](AlifeApi.DataAccess.ProjectContent.NotificationSetting.md 'AlifeApi.DataAccess.ProjectContent.NotificationSetting') | 審核通知設定表，用於儲存每個審核步驟的通知設定資訊。 |
| [Owner](AlifeApi.DataAccess.ProjectContent.Owner.md 'AlifeApi.DataAccess.ProjectContent.Owner') | 業主基本資料表 |
| [ParkingSpace](AlifeApi.DataAccess.ProjectContent.ParkingSpace.md 'AlifeApi.DataAccess.ProjectContent.ParkingSpace') | 停車位基本資訊表 - 僅儲存車位的物理特性和位置資訊，銷售相關資訊由 Units 表管理 |
| [PaymentRecord](AlifeApi.DataAccess.ProjectContent.PaymentRecord.md 'AlifeApi.DataAccess.ProjectContent.PaymentRecord') | 記錄針對某一筆 PurchaseOrder 的分期收款明細。 |
| [ProjectExpense](AlifeApi.DataAccess.ProjectContent.ProjectExpense.md 'AlifeApi.DataAccess.ProjectContent.ProjectExpense') | 專案支出紀錄表，記錄每個案場的各項開銷 |
| [PurchaseOrder](AlifeApi.DataAccess.ProjectContent.PurchaseOrder.md 'AlifeApi.DataAccess.ProjectContent.PurchaseOrder') | 儲存客戶預定或購買房屋單位與停車位的訂單詳細資訊 (交易記錄)。 |
| [PurchaseOrdersHistory](AlifeApi.DataAccess.ProjectContent.PurchaseOrdersHistory.md 'AlifeApi.DataAccess.ProjectContent.PurchaseOrdersHistory') | 買賣預定單歷史記錄表，主要追蹤業務狀態變更（售、足、簽、請、領） |
| [ReviewApprover](AlifeApi.DataAccess.ProjectContent.ReviewApprover.md 'AlifeApi.DataAccess.ProjectContent.ReviewApprover') | 審核人員表，用於儲存每個審核步驟的負責人員資訊。 |
| [ReviewHistory](AlifeApi.DataAccess.ProjectContent.ReviewHistory.md 'AlifeApi.DataAccess.ProjectContent.ReviewHistory') | 審核歷史記錄表，用於儲存審核任務的歷史操作記錄。 |
| [ReviewStep](AlifeApi.DataAccess.ProjectContent.ReviewStep.md 'AlifeApi.DataAccess.ProjectContent.ReviewStep') | 審核步驟表，用於儲存每個審核任務的具體步驟資訊。 |
| [ReviewTask](AlifeApi.DataAccess.ProjectContent.ReviewTask.md 'AlifeApi.DataAccess.ProjectContent.ReviewTask') | 審核任務表，用於儲存審核任務的基本資訊，與特定案場關聯。 |
| [Site](AlifeApi.DataAccess.ProjectContent.Site.md 'AlifeApi.DataAccess.ProjectContent.Site') | 案場資料表，用於儲存案場的基本資訊、區域資料和合約相關資訊。 |
| [SiteCrmOption](AlifeApi.DataAccess.ProjectContent.SiteCrmOption.md 'AlifeApi.DataAccess.ProjectContent.SiteCrmOption') | 儲存每個案場在不同CRM選項類型下的具體選項值 |
| [SmallCategory](AlifeApi.DataAccess.ProjectContent.SmallCategory.md 'AlifeApi.DataAccess.ProjectContent.SmallCategory') | 商品小分類資料表 |
| [Supplier](AlifeApi.DataAccess.ProjectContent.Supplier.md 'AlifeApi.DataAccess.ProjectContent.Supplier') | 供應商資料表，用於儲存供應商的基本資訊 |
| [SupplierFile](AlifeApi.DataAccess.ProjectContent.SupplierFile.md 'AlifeApi.DataAccess.ProjectContent.SupplierFile') | 供應商檔案資料表，用於儲存與供應商相關的檔案資訊 |
| [SysCode](AlifeApi.DataAccess.ProjectContent.SysCode.md 'AlifeApi.DataAccess.ProjectContent.SysCode') | 系統代碼表，用於儲存系統中的標準化代碼資訊，例如狀態碼、類型碼等。 |
| [SysMenuFunc](AlifeApi.DataAccess.ProjectContent.SysMenuFunc.md 'AlifeApi.DataAccess.ProjectContent.SysMenuFunc') | 系統功能選單表，用於儲存系統的功能項目及其層級結構。 |
| [SysRoleGroup](AlifeApi.DataAccess.ProjectContent.SysRoleGroup.md 'AlifeApi.DataAccess.ProjectContent.SysRoleGroup') | 角色群組表，用於儲存系統中的角色群組資訊，支援與特定案場關聯。 |
| [SysRoleGroupPermission](AlifeApi.DataAccess.ProjectContent.SysRoleGroupPermission.md 'AlifeApi.DataAccess.ProjectContent.SysRoleGroupPermission') | 角色群組權限表，用於儲存角色群組對系統功能的權限設置。 |
| [SysRoleGroupUser](AlifeApi.DataAccess.ProjectContent.SysRoleGroupUser.md 'AlifeApi.DataAccess.ProjectContent.SysRoleGroupUser') | 角色群組使用者關聯表，用於儲存角色群組與使用者的關聯關係。 |
| [SysSystemSetting](AlifeApi.DataAccess.ProjectContent.SysSystemSetting.md 'AlifeApi.DataAccess.ProjectContent.SysSystemSetting') | 系統設定表，用於儲存系統的各種設定鍵值對資訊。 |
| [Unit](AlifeApi.DataAccess.ProjectContent.Unit.md 'AlifeApi.DataAccess.ProjectContent.Unit') | 儲存住宅/商業樓層上具體可銷售的獨立房屋單位(戶)的詳細資訊與狀態。**此表為必要**，對應銷控表主體格子。 |
| [UserDepartment](AlifeApi.DataAccess.ProjectContent.UserDepartment.md 'AlifeApi.DataAccess.ProjectContent.UserDepartment') | 員工部門關聯表，用於儲存員工與部門的關聯關係，支持員工隸屬多個部門。 |
| [UserInfo](AlifeApi.DataAccess.ProjectContent.UserInfo.md 'AlifeApi.DataAccess.ProjectContent.UserInfo') | 員工基本資料表，用於儲存員工的個人資訊、聯絡方式等相關資料，角色、部門、職位資訊透過關聯表記錄。 |
| [UserJobTitle](AlifeApi.DataAccess.ProjectContent.UserJobTitle.md 'AlifeApi.DataAccess.ProjectContent.UserJobTitle') | 員工職位關聯表，用於儲存員工與職位的關聯關係，支持員工擁有複數職位。 |
| [UserPasswordHistory](AlifeApi.DataAccess.ProjectContent.UserPasswordHistory.md 'AlifeApi.DataAccess.ProjectContent.UserPasswordHistory') | 密碼歷史記錄表，用於儲存員工的歷史密碼資訊。 |
| [UserPasswordResetCode](AlifeApi.DataAccess.ProjectContent.UserPasswordResetCode.md 'AlifeApi.DataAccess.ProjectContent.UserPasswordResetCode') | 密碼重置驗證碼表，用於儲存員工密碼重置時的驗證碼資訊。 |
