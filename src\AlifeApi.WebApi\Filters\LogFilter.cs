﻿using System.Text;
using AlifeApi.BusinessRules.LogModels;
using AlifeApi.BusinessRules.UserRecordModels;
using AlifeApi.Common.Util;
using AlifeApi.WebApi.Options;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Abstractions;
using Microsoft.AspNetCore.Mvc.Filters;
using Microsoft.AspNetCore.Mvc.ModelBinding;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using Microsoft.Extensions.Primitives;

namespace AlifeApi.WebApi.Filters
{
    public class LogFilter : IAsyncActionFilter, IAsyncExceptionFilter
    {
        private static readonly List<string> includeHeaderNames = new() {
            "Authorization", "personId",
            "accessToken", "description",
            "location", "personGroupId",
            "projectId", "domain",
            "source", "memberId", "fileName"
        };
        private readonly LogService logService;
        private readonly UserRecordService userRecordService;
        private readonly SystemOptions systemOptions;
        private DateTime startTime = DateTime.Now;
        private string inputData;

        public LogFilter(LogService logService, UserRecordService userRecordService, IOptionsSnapshot<SystemOptions> systemOptionsAccessor)
        {
            this.logService = logService ?? throw new ArgumentNullException(nameof(logService));
            this.userRecordService = userRecordService ?? throw new ArgumentNullException(nameof(userRecordService));
            systemOptions = systemOptionsAccessor?.Value ?? throw new ArgumentNullException(nameof(systemOptionsAccessor));
        }

        public async Task OnActionExecutionAsync(ActionExecutingContext context, ActionExecutionDelegate next)
        {
            startTime = DateTime.Now;
            string controllerName = context.HttpContext.Request.RouteValues["controller"].ToString();
            string actionName = context.HttpContext.Request.RouteValues["action"].ToString();

            StringBuilder inputBuilder = new();
            if (context.ActionArguments is not null)
            {
                List<string> fromServiceParams = new();
                foreach (ParameterDescriptor parameter in context.ActionDescriptor.Parameters)
                {
                    var bindingSource = parameter.BindingInfo.BindingSource;
                    if (bindingSource == BindingSource.Services)
                    {
                        fromServiceParams.Add(parameter.Name);
                    }
                }

                // 因為傳入的參數為多數，所以 ActionArguments 必須用迴圈將之取出
                foreach (var item in context.ActionArguments)
                {
                    if (!fromServiceParams.Contains(item.Key, StringComparer.OrdinalIgnoreCase))
                    {
                        // 取出傳入的內容並作 JSON 資料的處理
                        inputBuilder.Append($"{item.Key}:{JsonUtils.Serialize(item.Value, isForLogging: true)}.");
                    }
                }
            }

            inputData = inputBuilder.ToString();

            ActionExecutedContext actionExecutedContext = await next();

            string outputData = actionExecutedContext.Result is ObjectResult objectResult
                ? JsonUtils.Serialize(objectResult?.Value ?? "", isForLogging: true)
                : "";

            DateTime now = DateTime.Now;

            await logService.CreateApiLogAsync(new ApiLogCreateInput
            {
                SystemName = systemOptions.SystemName,
                Headers = GetHeadersString(context.HttpContext),
                Input = inputData,
                Output = outputData,
                ActionName = actionName ?? "",
                ControllerName = controllerName ?? "",
                StartTime = startTime,
                EndTime = now,
                Source = GetSourceHost(context.HttpContext),
                Exception = "",
                SessionId = context.HttpContext.Session?.Id
            });

            //await userRecordService.TryCreateUserRecordAsync(actionName ?? "", inputData);
        }

        private static string GetHeadersString(HttpContext httpContext)
        {
            var headers = httpContext.Request.Headers
                .Where(x => includeHeaderNames.Any(y => y.Equals(x.Key, StringComparison.OrdinalIgnoreCase)))
                .Select(x => new
                {
                    x.Key,
                    x.Value
                });

            return JsonUtils.Serialize(headers);
        }

        public async Task OnExceptionAsync(ExceptionContext context)
        {
            string controllerName = context.HttpContext.Request.RouteValues["controller"].ToString();
            string actionName = context.HttpContext.Request.RouteValues["action"].ToString();
            string hostIP = GetSourceHost(context.HttpContext);

            Dictionary<string, StringValues> headers = context.HttpContext.Request.Headers
                .Where(x => includeHeaderNames.Contains(x.Key))
                .ToDictionary(x => x.Key, x => x.Value);

            DateTime now = DateTime.Now;

            Exception exception = context.ExceptionDispatchInfo.SourceException
                ?? context.Exception;
            if (exception is DbUpdateException)
            {
                exception = exception.InnerException;
            }

            var exceptionObj = new
            {
                Message = exception.Message
                    ?? "",
                StrackTrace = exception.StackTrace
                    ?? ""
            };

            await logService.CreateApiLogAsync(new ApiLogCreateInput
            {
                SystemName = systemOptions.SystemName,
                Headers = GetHeadersString(context.HttpContext),
                Input = inputData,
                Output = "",
                ControllerName = controllerName ?? "",
                ActionName = actionName ?? "",
                StartTime = startTime,
                EndTime = now,
                Source = GetSourceHost(context.HttpContext),
                Exception = JsonUtils.Serialize(exceptionObj),
                SessionId = context.HttpContext.Session?.Id
            });
        }

        private static string GetSourceHost(HttpContext httpContext)
        {
            return httpContext.Request.Host.HasValue
                ? httpContext.Request.Host.Value
                    .Replace("http://", "").Replace("https://", "")
                    .Split('/')[0]
                : "";
        }
    }
}
