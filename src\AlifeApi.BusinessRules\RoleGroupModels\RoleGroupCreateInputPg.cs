﻿using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;

namespace AlifeApi.BusinessRules.RoleGroupModels
{
    /// <summary>
    /// 角色權限建立輸入(PostgreSQL 版本)
    /// </summary>
    public class RoleGroupCreateInputPg
    {
        /// <summary>
        /// 角色群組名稱
        /// </summary>
        [JsonPropertyName("Name")]
        [MaxLength(50)]
        [Required]
        public string Name { get; set; }

        /// <summary>
        /// 案場代碼，對應 Sites 表的 SiteCode
        /// </summary>
        [JsonPropertyName("SiteCode")]
        [MaxLength(50)]
        public string SiteCode { get; set; }

        /// <summary>
        /// 是否為管理者角色
        /// </summary>
        [JsonPropertyName("IsAdmin")]
        public bool IsAdmin { get; set; }

        /// <summary>
        /// 權限
        /// </summary>7
        public string Permissions { get; set; }

        /// <remarks>
        /// 前端會傳字串，只好這樣處理
        /// </remarks>
        public IEnumerable<string> FuncIds => Permissions?.Split(',').Distinct() ?? Enumerable.Empty<string>();

        /// <summary>
        /// 使用者清單
        /// </summary>
        [JsonPropertyName("UserIds")]
        public IEnumerable<string> UserIds { get; set; } = Enumerable.Empty<string>();
    }
} 
