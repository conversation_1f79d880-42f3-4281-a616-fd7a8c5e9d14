using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;

namespace AlifeApi.BusinessRules.DepartmentModels
{
    /// <summary>
    /// 更新部門輸入資料
    /// </summary>
    public class DepartmentUpdateInput
    {
        /// <summary>
        /// 公司ID (主鍵，不可變)
        /// </summary>
        [JsonPropertyName("CompanyId")]
        [Required(ErrorMessage = "公司ID 為必填項")]
        public string CompanyId { get; set; } // 主鍵通常不可更改

        /// <summary>
        /// 部門ID (主鍵，不可變)
        /// </summary>
        [JsonPropertyName("DepartmentId")]
        [Required(ErrorMessage = "部門ID 為必填項")]
        public string DepartmentId { get; set; } // 主鍵通常不可更改

        /// <summary>
        /// 部門名稱
        /// </summary>
        [JsonPropertyName("Name")]
        [Required(ErrorMessage = "部門名稱 為必填項")]
        [MaxLength(50, ErrorMessage = "部門名稱 最多 50 個字元")]
        public string Name { get; set; }
    }
} 
