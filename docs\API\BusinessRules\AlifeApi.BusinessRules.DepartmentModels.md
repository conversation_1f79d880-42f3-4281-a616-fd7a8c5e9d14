#### [AlifeApi.BusinessRules](index.md 'index')

## AlifeApi.BusinessRules.DepartmentModels Namespace

| Classes | |
| :--- | :--- |
| [DepartmentCreateInput](AlifeApi.BusinessRules.DepartmentModels.DepartmentCreateInput.md 'AlifeApi.BusinessRules.DepartmentModels.DepartmentCreateInput') | 新增部門輸入資料 |
| [DepartmentDropdownInput](AlifeApi.BusinessRules.DepartmentModels.DepartmentDropdownInput.md 'AlifeApi.BusinessRules.DepartmentModels.DepartmentDropdownInput') | 部門下拉選單輸入資料 |
| [DepartmentDropdownOutput](AlifeApi.BusinessRules.DepartmentModels.DepartmentDropdownOutput.md 'AlifeApi.BusinessRules.DepartmentModels.DepartmentDropdownOutput') | 部門下拉選單輸出資料 |
| [DepartmentListGetInput](AlifeApi.BusinessRules.DepartmentModels.DepartmentListGetInput.md 'AlifeApi.BusinessRules.DepartmentModels.DepartmentListGetInput') | 取得部門列表查詢條件 |
| [DepartmentOutput](AlifeApi.BusinessRules.DepartmentModels.DepartmentOutput.md 'AlifeApi.BusinessRules.DepartmentModels.DepartmentOutput') | 部門資料輸出 |
| [DepartmentService](AlifeApi.BusinessRules.DepartmentModels.DepartmentService.md 'AlifeApi.BusinessRules.DepartmentModels.DepartmentService') | 部門服務 |
| [DepartmentUpdateInput](AlifeApi.BusinessRules.DepartmentModels.DepartmentUpdateInput.md 'AlifeApi.BusinessRules.DepartmentModels.DepartmentUpdateInput') | 更新部門輸入資料 |
