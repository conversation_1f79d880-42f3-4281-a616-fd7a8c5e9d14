#### [AlifeApi.BusinessRules](index.md 'index')
### [AlifeApi.BusinessRules.OwnerModels](AlifeApi.BusinessRules.OwnerModels.md 'AlifeApi.BusinessRules.OwnerModels')

## OwnerService Class

業主資料管理服務

```csharp
public class OwnerService : AlifeApi.BusinessRules.Infrastructure.ServiceBase<AlifeApi.DataAccess.ProjectContent.alifeContext>
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; [AlifeApi.Common.InfrastructureBase](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.Common.InfrastructureBase 'AlifeApi.Common.InfrastructureBase') &#129106; [AlifeApi.BusinessRules.Infrastructure.ServiceBase&lt;](AlifeApi.BusinessRules.Infrastructure.ServiceBase_TDbContext_.md 'AlifeApi.BusinessRules.Infrastructure.ServiceBase<TDbContext>')[AlifeApi.DataAccess.ProjectContent.alifeContext](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.DataAccess.ProjectContent.alifeContext 'AlifeApi.DataAccess.ProjectContent.alifeContext')[&gt;](AlifeApi.BusinessRules.Infrastructure.ServiceBase_TDbContext_.md 'AlifeApi.BusinessRules.Infrastructure.ServiceBase<TDbContext>') &#129106; OwnerService
### Methods

<a name='AlifeApi.BusinessRules.OwnerModels.OwnerService.CreateOwnerAsync(AlifeApi.BusinessRules.OwnerModels.OwnerCreateInput)'></a>

## OwnerService.CreateOwnerAsync(OwnerCreateInput) Method

建立新業主

```csharp
public System.Threading.Tasks.Task<AlifeApi.BusinessRules.OwnerModels.OwnerUpdateOutput> CreateOwnerAsync(AlifeApi.BusinessRules.OwnerModels.OwnerCreateInput input);
```
#### Parameters

<a name='AlifeApi.BusinessRules.OwnerModels.OwnerService.CreateOwnerAsync(AlifeApi.BusinessRules.OwnerModels.OwnerCreateInput).input'></a>

`input` [OwnerCreateInput](AlifeApi.BusinessRules.OwnerModels.OwnerCreateInput.md 'AlifeApi.BusinessRules.OwnerModels.OwnerCreateInput')

建立業主輸入資料

#### Returns
[System.Threading.Tasks.Task&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')[OwnerUpdateOutput](AlifeApi.BusinessRules.OwnerModels.OwnerUpdateOutput.md 'AlifeApi.BusinessRules.OwnerModels.OwnerUpdateOutput')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')  
執行結果

<a name='AlifeApi.BusinessRules.OwnerModels.OwnerService.DeleteOwnerAsync(int)'></a>

## OwnerService.DeleteOwnerAsync(int) Method

刪除業主資料 (硬刪除)

```csharp
public System.Threading.Tasks.Task<AlifeApi.BusinessRules.OwnerModels.OwnerUpdateOutput> DeleteOwnerAsync(int ownerId);
```
#### Parameters

<a name='AlifeApi.BusinessRules.OwnerModels.OwnerService.DeleteOwnerAsync(int).ownerId'></a>

`ownerId` [System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')

業主ID

#### Returns
[System.Threading.Tasks.Task&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')[OwnerUpdateOutput](AlifeApi.BusinessRules.OwnerModels.OwnerUpdateOutput.md 'AlifeApi.BusinessRules.OwnerModels.OwnerUpdateOutput')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')  
執行結果

<a name='AlifeApi.BusinessRules.OwnerModels.OwnerService.GetOwnerAsync(int)'></a>

## OwnerService.GetOwnerAsync(int) Method

取得單一業主詳細資料

```csharp
public System.Threading.Tasks.Task<AlifeApi.BusinessRules.OwnerModels.OwnerGetOutput?> GetOwnerAsync(int ownerId);
```
#### Parameters

<a name='AlifeApi.BusinessRules.OwnerModels.OwnerService.GetOwnerAsync(int).ownerId'></a>

`ownerId` [System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')

業主ID

#### Returns
[System.Threading.Tasks.Task&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')[OwnerGetOutput](AlifeApi.BusinessRules.OwnerModels.OwnerGetOutput.md 'AlifeApi.BusinessRules.OwnerModels.OwnerGetOutput')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')  
業主詳細資料或 null

<a name='AlifeApi.BusinessRules.OwnerModels.OwnerService.GetOwnerListAsync(AlifeApi.BusinessRules.OwnerModels.OwnerListGetInput)'></a>

## OwnerService.GetOwnerListAsync(OwnerListGetInput) Method

取得業主列表 (分頁)

```csharp
public System.Threading.Tasks.Task<AlifeApi.BusinessRules.Infrastructure.PagedListOutput<AlifeApi.BusinessRules.OwnerModels.OwnerListItemGetOutput>> GetOwnerListAsync(AlifeApi.BusinessRules.OwnerModels.OwnerListGetInput input);
```
#### Parameters

<a name='AlifeApi.BusinessRules.OwnerModels.OwnerService.GetOwnerListAsync(AlifeApi.BusinessRules.OwnerModels.OwnerListGetInput).input'></a>

`input` [OwnerListGetInput](AlifeApi.BusinessRules.OwnerModels.OwnerListGetInput.md 'AlifeApi.BusinessRules.OwnerModels.OwnerListGetInput')

查詢條件

#### Returns
[System.Threading.Tasks.Task&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')[AlifeApi.BusinessRules.Infrastructure.PagedListOutput&lt;](AlifeApi.BusinessRules.Infrastructure.PagedListOutput_TDetail_.md 'AlifeApi.BusinessRules.Infrastructure.PagedListOutput<TDetail>')[OwnerListItemGetOutput](AlifeApi.BusinessRules.OwnerModels.OwnerListItemGetOutput.md 'AlifeApi.BusinessRules.OwnerModels.OwnerListItemGetOutput')[&gt;](AlifeApi.BusinessRules.Infrastructure.PagedListOutput_TDetail_.md 'AlifeApi.BusinessRules.Infrastructure.PagedListOutput<TDetail>')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')  
業主列表

<a name='AlifeApi.BusinessRules.OwnerModels.OwnerService.UpdateOwnerAsync(int,AlifeApi.BusinessRules.OwnerModels.OwnerUpdateInput)'></a>

## OwnerService.UpdateOwnerAsync(int, OwnerUpdateInput) Method

更新業主資料

```csharp
public System.Threading.Tasks.Task<AlifeApi.BusinessRules.OwnerModels.OwnerUpdateOutput> UpdateOwnerAsync(int ownerId, AlifeApi.BusinessRules.OwnerModels.OwnerUpdateInput input);
```
#### Parameters

<a name='AlifeApi.BusinessRules.OwnerModels.OwnerService.UpdateOwnerAsync(int,AlifeApi.BusinessRules.OwnerModels.OwnerUpdateInput).ownerId'></a>

`ownerId` [System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')

業主ID

<a name='AlifeApi.BusinessRules.OwnerModels.OwnerService.UpdateOwnerAsync(int,AlifeApi.BusinessRules.OwnerModels.OwnerUpdateInput).input'></a>

`input` [OwnerUpdateInput](AlifeApi.BusinessRules.OwnerModels.OwnerUpdateInput.md 'AlifeApi.BusinessRules.OwnerModels.OwnerUpdateInput')

更新業主輸入資料

#### Returns
[System.Threading.Tasks.Task&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')[OwnerUpdateOutput](AlifeApi.BusinessRules.OwnerModels.OwnerUpdateOutput.md 'AlifeApi.BusinessRules.OwnerModels.OwnerUpdateOutput')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')  
執行結果