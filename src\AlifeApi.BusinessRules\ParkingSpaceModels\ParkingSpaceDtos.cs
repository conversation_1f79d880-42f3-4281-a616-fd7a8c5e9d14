/// <summary>
/// 停車位銷售統計
/// </summary>
public class ParkingSpaceSalesStatistics
{
    /// <summary>
    /// 案場編號
    /// </summary>
    public string SiteCode { get; set; } = null!;

    /// <summary>
    /// 可售數量
    /// </summary>
    public int AvailableCount { get; set; }

    /// <summary>
    /// 保留數量
    /// </summary>
    public int ReservedCount { get; set; }

    /// <summary>
    /// 地主保留數量
    /// </summary>
    public int OwnerReservedCount { get; set; }

    /// <summary>
    /// 已預訂數量
    /// </summary>
    public int BookedCount { get; set; }

    /// <summary>
    /// 已售數量
    /// </summary>
    public int SoldCount { get; set; }

    /// <summary>
    /// 總數量
    /// </summary>
    public int TotalCount { get; set; }

    /// <summary>
    /// 銷售率 (%)
    /// </summary>
    public decimal SalesRate { get; set; }

    /// <summary>
    /// 可售總表價
    /// </summary>
    public decimal TotalAvailableListPrice { get; set; }

    /// <summary>
    /// 保留總表價
    /// </summary>
    public decimal TotalReservedListPrice { get; set; }
} 
