#### [AlifeApi.BusinessRules](index.md 'index')
### [AlifeApi.BusinessRules.ParkingSpaceModels](AlifeApi.BusinessRules.ParkingSpaceModels.md 'AlifeApi.BusinessRules.ParkingSpaceModels')

## ParkingSpaceService Class

停車位服務

```csharp
public class ParkingSpaceService : AlifeApi.BusinessRules.Infrastructure.ServiceBase<AlifeApi.DataAccess.ProjectContent.alifeContext>
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; [AlifeApi.Common.InfrastructureBase](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.Common.InfrastructureBase 'AlifeApi.Common.InfrastructureBase') &#129106; [AlifeApi.BusinessRules.Infrastructure.ServiceBase&lt;](AlifeApi.BusinessRules.Infrastructure.ServiceBase_TDbContext_.md 'AlifeApi.BusinessRules.Infrastructure.ServiceBase<TDbContext>')[AlifeApi.DataAccess.ProjectContent.alifeContext](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.DataAccess.ProjectContent.alifeContext 'AlifeApi.DataAccess.ProjectContent.alifeContext')[&gt;](AlifeApi.BusinessRules.Infrastructure.ServiceBase_TDbContext_.md 'AlifeApi.BusinessRules.Infrastructure.ServiceBase<TDbContext>') &#129106; ParkingSpaceService
### Methods

<a name='AlifeApi.BusinessRules.ParkingSpaceModels.ParkingSpaceService.CreateParkingSpaceAsync(AlifeApi.BusinessRules.ParkingSpaceModels.ParkingSpaceCreateInput)'></a>

## ParkingSpaceService.CreateParkingSpaceAsync(ParkingSpaceCreateInput) Method

建立停車位

```csharp
public System.Threading.Tasks.Task<int> CreateParkingSpaceAsync(AlifeApi.BusinessRules.ParkingSpaceModels.ParkingSpaceCreateInput input);
```
#### Parameters

<a name='AlifeApi.BusinessRules.ParkingSpaceModels.ParkingSpaceService.CreateParkingSpaceAsync(AlifeApi.BusinessRules.ParkingSpaceModels.ParkingSpaceCreateInput).input'></a>

`input` [ParkingSpaceCreateInput](AlifeApi.BusinessRules.ParkingSpaceModels.ParkingSpaceCreateInput.md 'AlifeApi.BusinessRules.ParkingSpaceModels.ParkingSpaceCreateInput')

停車位建立輸入資料

#### Returns
[System.Threading.Tasks.Task&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')[System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')  
新建停車位的ID

<a name='AlifeApi.BusinessRules.ParkingSpaceModels.ParkingSpaceService.DeleteParkingSpaceAsync(int)'></a>

## ParkingSpaceService.DeleteParkingSpaceAsync(int) Method

刪除停車位

```csharp
public System.Threading.Tasks.Task DeleteParkingSpaceAsync(int parkingSpaceId);
```
#### Parameters

<a name='AlifeApi.BusinessRules.ParkingSpaceModels.ParkingSpaceService.DeleteParkingSpaceAsync(int).parkingSpaceId'></a>

`parkingSpaceId` [System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')

停車位ID

#### Returns
[System.Threading.Tasks.Task](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task 'System.Threading.Tasks.Task')

<a name='AlifeApi.BusinessRules.ParkingSpaceModels.ParkingSpaceService.FormatParkingSpaceIds(System.Collections.Generic.List_int_)'></a>

## ParkingSpaceService.FormatParkingSpaceIds(List<int>) Method

將停車位ID列表轉換為字串

```csharp
public static string? FormatParkingSpaceIds(System.Collections.Generic.List<int>? parkingSpaceIds);
```
#### Parameters

<a name='AlifeApi.BusinessRules.ParkingSpaceModels.ParkingSpaceService.FormatParkingSpaceIds(System.Collections.Generic.List_int_).parkingSpaceIds'></a>

`parkingSpaceIds` [System.Collections.Generic.List&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Collections.Generic.List-1 'System.Collections.Generic.List`1')[System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Collections.Generic.List-1 'System.Collections.Generic.List`1')

停車位ID列表

#### Returns
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')  
逗號分隔的停車位ID字串

<a name='AlifeApi.BusinessRules.ParkingSpaceModels.ParkingSpaceService.GetAvailableParkingSpacesAsync(AlifeApi.BusinessRules.ParkingSpaceModels.ParkingSpaceQueryInput)'></a>

## ParkingSpaceService.GetAvailableParkingSpacesAsync(ParkingSpaceQueryInput) Method

取得可用停車位列表 (用於銷售選擇)  
註: 銷售狀態現在由Units表管理，此方法返回基本車位資訊

```csharp
public System.Threading.Tasks.Task<AlifeApi.BusinessRules.Infrastructure.PagedListOutput<AlifeApi.BusinessRules.ParkingSpaceModels.ParkingSpaceListOutput>> GetAvailableParkingSpacesAsync(AlifeApi.BusinessRules.ParkingSpaceModels.ParkingSpaceQueryInput input);
```
#### Parameters

<a name='AlifeApi.BusinessRules.ParkingSpaceModels.ParkingSpaceService.GetAvailableParkingSpacesAsync(AlifeApi.BusinessRules.ParkingSpaceModels.ParkingSpaceQueryInput).input'></a>

`input` [ParkingSpaceQueryInput](AlifeApi.BusinessRules.ParkingSpaceModels.ParkingSpaceQueryInput.md 'AlifeApi.BusinessRules.ParkingSpaceModels.ParkingSpaceQueryInput')

查詢條件

#### Returns
[System.Threading.Tasks.Task&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')[AlifeApi.BusinessRules.Infrastructure.PagedListOutput&lt;](AlifeApi.BusinessRules.Infrastructure.PagedListOutput_TDetail_.md 'AlifeApi.BusinessRules.Infrastructure.PagedListOutput<TDetail>')[ParkingSpaceListOutput](AlifeApi.BusinessRules.ParkingSpaceModels.ParkingSpaceListOutput.md 'AlifeApi.BusinessRules.ParkingSpaceModels.ParkingSpaceListOutput')[&gt;](AlifeApi.BusinessRules.Infrastructure.PagedListOutput_TDetail_.md 'AlifeApi.BusinessRules.Infrastructure.PagedListOutput<TDetail>')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')  
可用停車位列表

<a name='AlifeApi.BusinessRules.ParkingSpaceModels.ParkingSpaceService.GetParkingSpaceByIdAsync(int)'></a>

## ParkingSpaceService.GetParkingSpaceByIdAsync(int) Method

根據ID取得停車位詳細資料

```csharp
public System.Threading.Tasks.Task<AlifeApi.BusinessRules.ParkingSpaceModels.ParkingSpaceOutput?> GetParkingSpaceByIdAsync(int parkingSpaceId);
```
#### Parameters

<a name='AlifeApi.BusinessRules.ParkingSpaceModels.ParkingSpaceService.GetParkingSpaceByIdAsync(int).parkingSpaceId'></a>

`parkingSpaceId` [System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')

停車位ID

#### Returns
[System.Threading.Tasks.Task&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')[ParkingSpaceOutput](AlifeApi.BusinessRules.ParkingSpaceModels.ParkingSpaceOutput.md 'AlifeApi.BusinessRules.ParkingSpaceModels.ParkingSpaceOutput')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')  
停車位詳細資料

<a name='AlifeApi.BusinessRules.ParkingSpaceModels.ParkingSpaceService.GetParkingSpaceListAsync(AlifeApi.BusinessRules.ParkingSpaceModels.ParkingSpaceQueryInput)'></a>

## ParkingSpaceService.GetParkingSpaceListAsync(ParkingSpaceQueryInput) Method

取得指定樓層的停車位列表 (分頁)

```csharp
public System.Threading.Tasks.Task<AlifeApi.BusinessRules.Infrastructure.PagedListOutput<AlifeApi.BusinessRules.ParkingSpaceModels.ParkingSpaceListOutput>> GetParkingSpaceListAsync(AlifeApi.BusinessRules.ParkingSpaceModels.ParkingSpaceQueryInput input);
```
#### Parameters

<a name='AlifeApi.BusinessRules.ParkingSpaceModels.ParkingSpaceService.GetParkingSpaceListAsync(AlifeApi.BusinessRules.ParkingSpaceModels.ParkingSpaceQueryInput).input'></a>

`input` [ParkingSpaceQueryInput](AlifeApi.BusinessRules.ParkingSpaceModels.ParkingSpaceQueryInput.md 'AlifeApi.BusinessRules.ParkingSpaceModels.ParkingSpaceQueryInput')

查詢條件 (必須包含 FloorId)

#### Returns
[System.Threading.Tasks.Task&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')[AlifeApi.BusinessRules.Infrastructure.PagedListOutput&lt;](AlifeApi.BusinessRules.Infrastructure.PagedListOutput_TDetail_.md 'AlifeApi.BusinessRules.Infrastructure.PagedListOutput<TDetail>')[ParkingSpaceListOutput](AlifeApi.BusinessRules.ParkingSpaceModels.ParkingSpaceListOutput.md 'AlifeApi.BusinessRules.ParkingSpaceModels.ParkingSpaceListOutput')[&gt;](AlifeApi.BusinessRules.Infrastructure.PagedListOutput_TDetail_.md 'AlifeApi.BusinessRules.Infrastructure.PagedListOutput<TDetail>')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')  
分頁的停車位列表

<a name='AlifeApi.BusinessRules.ParkingSpaceModels.ParkingSpaceService.ParseParkingSpaceIds(string)'></a>

## ParkingSpaceService.ParseParkingSpaceIds(string) Method

解析停車位ID字串為ID列表

```csharp
public static System.Collections.Generic.List<int> ParseParkingSpaceIds(string? parkingSpaceIdsString);
```
#### Parameters

<a name='AlifeApi.BusinessRules.ParkingSpaceModels.ParkingSpaceService.ParseParkingSpaceIds(string).parkingSpaceIdsString'></a>

`parkingSpaceIdsString` [System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

逗號分隔的停車位ID字串

#### Returns
[System.Collections.Generic.List&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Collections.Generic.List-1 'System.Collections.Generic.List`1')[System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Collections.Generic.List-1 'System.Collections.Generic.List`1')  
停車位ID列表

<a name='AlifeApi.BusinessRules.ParkingSpaceModels.ParkingSpaceService.UpdateParkingSpaceAsync(int,AlifeApi.BusinessRules.ParkingSpaceModels.ParkingSpaceUpdateInput)'></a>

## ParkingSpaceService.UpdateParkingSpaceAsync(int, ParkingSpaceUpdateInput) Method

更新停車位資訊

```csharp
public System.Threading.Tasks.Task UpdateParkingSpaceAsync(int parkingSpaceId, AlifeApi.BusinessRules.ParkingSpaceModels.ParkingSpaceUpdateInput input);
```
#### Parameters

<a name='AlifeApi.BusinessRules.ParkingSpaceModels.ParkingSpaceService.UpdateParkingSpaceAsync(int,AlifeApi.BusinessRules.ParkingSpaceModels.ParkingSpaceUpdateInput).parkingSpaceId'></a>

`parkingSpaceId` [System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')

停車位ID

<a name='AlifeApi.BusinessRules.ParkingSpaceModels.ParkingSpaceService.UpdateParkingSpaceAsync(int,AlifeApi.BusinessRules.ParkingSpaceModels.ParkingSpaceUpdateInput).input'></a>

`input` [ParkingSpaceUpdateInput](AlifeApi.BusinessRules.ParkingSpaceModels.ParkingSpaceUpdateInput.md 'AlifeApi.BusinessRules.ParkingSpaceModels.ParkingSpaceUpdateInput')

停車位更新輸入資料

#### Returns
[System.Threading.Tasks.Task](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task 'System.Threading.Tasks.Task')