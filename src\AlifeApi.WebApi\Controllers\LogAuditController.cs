﻿using AlifeApi.BusinessRules.Infrastructure;
using AlifeApi.BusinessRules.LogAuditModels;
using Microsoft.AspNetCore.Mvc;

namespace AlifeApi.WebApi.Controllers
{
    /// <summary>
    /// 日誌稽核管理(LogAudit)
    /// </summary>
    public class LogAuditController : AuthenticatedController
    {
        //private readonly LogAuditService _logAuditService;

        ///// <summary>
        ///// Initializes a new instance of the <see cref="LogAuditController"/> class.
        ///// </summary>
        ///// <param name="logAuditService">The log audit service.</param>
        ///// <exception cref="ArgumentNullException">logAuditService</exception>
        //public LogAuditController(LogAuditService logAuditService)
        //{
        //    _logAuditService = logAuditService ?? throw new ArgumentNullException(nameof(logAuditService));
        //}

        ///// <summary>
        ///// 取得日誌查詢結果
        ///// </summary>
        ///// <param name="input">The input.</param>
        ///// <returns>統計結果</returns>
        //[HttpPost]
        //public async Task<PagedListOutput<LogAuditMonthStatisticsItemGetOutput>> GetLogAuditAsync(LogAuditMonthStatisticsGetInput input)
        //    => await _logAuditService.GetLogAuditMonthStatisticsAsync(input);


        ///// <summary>
        ///// 取得日誌查詢詳細資料
        ///// </summary>
        ///// <param name="input">The input.</param>
        ///// <returns>日誌詳細資料</returns>
        //[HttpPost]
        //public async Task<PagedListOutput<LogAuditDetailGetOutput>> GetLogAuditDetailsAsync(LogAuditDetailsGetInput input)
        //    => await _logAuditService.GetLogAuditDetailsAsync(input);
    }
}
