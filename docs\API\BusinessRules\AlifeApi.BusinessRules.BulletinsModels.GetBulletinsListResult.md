#### [AlifeApi.BusinessRules](index.md 'index')
### [AlifeApi.BusinessRules.BulletinsModels](AlifeApi.BusinessRules.BulletinsModels.md 'AlifeApi.BusinessRules.BulletinsModels')

## GetBulletinsListResult Class

```csharp
public class GetBulletinsListResult
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; GetBulletinsListResult
### Properties

<a name='AlifeApi.BusinessRules.BulletinsModels.GetBulletinsListResult.BL_Id'></a>

## GetBulletinsListResult.BL_Id Property

公告唯一識別值

```csharp
public int BL_Id { get; set; }
```

#### Property Value
[System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')

<a name='AlifeApi.BusinessRules.BulletinsModels.GetBulletinsListResult.Content'></a>

## GetBulletinsListResult.Content Property

公告內文

```csharp
public string Content { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.BulletinsModels.GetBulletinsListResult.CreateDate'></a>

## GetBulletinsListResult.CreateDate Property

建立時間

```csharp
public System.Nullable<System.DateTime> CreateDate { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.DateTime](https://docs.microsoft.com/en-us/dotnet/api/System.DateTime 'System.DateTime')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

<a name='AlifeApi.BusinessRules.BulletinsModels.GetBulletinsListResult.CreateUserId'></a>

## GetBulletinsListResult.CreateUserId Property

建立者帳號

```csharp
public string CreateUserId { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.BulletinsModels.GetBulletinsListResult.IsRead'></a>

## GetBulletinsListResult.IsRead Property

是否看過

```csharp
public bool IsRead { get; set; }
```

#### Property Value
[System.Boolean](https://docs.microsoft.com/en-us/dotnet/api/System.Boolean 'System.Boolean')

<a name='AlifeApi.BusinessRules.BulletinsModels.GetBulletinsListResult.IsTop'></a>

## GetBulletinsListResult.IsTop Property

是否至頂

```csharp
public bool IsTop { get; set; }
```

#### Property Value
[System.Boolean](https://docs.microsoft.com/en-us/dotnet/api/System.Boolean 'System.Boolean')

<a name='AlifeApi.BusinessRules.BulletinsModels.GetBulletinsListResult.PostFrom'></a>

## GetBulletinsListResult.PostFrom Property

公告起始日期

```csharp
public System.Nullable<System.DateTime> PostFrom { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.DateTime](https://docs.microsoft.com/en-us/dotnet/api/System.DateTime 'System.DateTime')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

<a name='AlifeApi.BusinessRules.BulletinsModels.GetBulletinsListResult.PostTo'></a>

## GetBulletinsListResult.PostTo Property

公告結束日期

```csharp
public System.Nullable<System.DateTime> PostTo { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.DateTime](https://docs.microsoft.com/en-us/dotnet/api/System.DateTime 'System.DateTime')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

<a name='AlifeApi.BusinessRules.BulletinsModels.GetBulletinsListResult.Title'></a>

## GetBulletinsListResult.Title Property

公告標題

```csharp
public string Title { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.BulletinsModels.GetBulletinsListResult.UpdateDate'></a>

## GetBulletinsListResult.UpdateDate Property

更新時間

```csharp
public System.Nullable<System.DateTime> UpdateDate { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.DateTime](https://docs.microsoft.com/en-us/dotnet/api/System.DateTime 'System.DateTime')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

<a name='AlifeApi.BusinessRules.BulletinsModels.GetBulletinsListResult.UpdateUserId'></a>

## GetBulletinsListResult.UpdateUserId Property

更新者帳號

```csharp
public string UpdateUserId { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.BulletinsModels.GetBulletinsListResult.ViewCount'></a>

## GetBulletinsListResult.ViewCount Property

瀏覽次數

```csharp
public int ViewCount { get; set; }
```

#### Property Value
[System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')