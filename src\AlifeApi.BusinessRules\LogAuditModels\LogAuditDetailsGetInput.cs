﻿using System.ComponentModel.DataAnnotations;
using AlifeApi.BusinessRules.Infrastructure;

namespace AlifeApi.BusinessRules.LogAuditModels
{
    /// <summary>
    /// 日誌稽核列明細查詢
    /// </summary>
    public class LogAuditDetailsGetInput : PagedListInput
    {
        /// <summary>
        /// 月份 (格式 yyyy-MM)
        /// </summary>
        [RegularExpression("^\\d{4}-((0[1-9])|(1[012]))$", ErrorMessage = "月份格式不對")]
        [Required]
        public string Month { get; set; }

        /// <summary>
        /// 部門代碼 (對照SysCode的Dept)
        /// </summary>
        [Required]
        public string Dept { get; set; }
    }
}
