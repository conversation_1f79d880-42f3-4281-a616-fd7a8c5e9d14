#### [AlifeApi.BusinessRules](index.md 'index')
### [AlifeApi.BusinessRules.DailyReportModels](AlifeApi.BusinessRules.DailyReportModels.md 'AlifeApi.BusinessRules.DailyReportModels')

## DailyReportOutput Class

日報統計結果輸出DTO

```csharp
public class DailyReportOutput
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; DailyReportOutput
### Properties

<a name='AlifeApi.BusinessRules.DailyReportModels.DailyReportOutput.CallConversionRate'></a>

## DailyReportOutput.CallConversionRate Property

來電轉換率 (留單數/來電數)

```csharp
public decimal CallConversionRate { get; set; }
```

#### Property Value
[System.Decimal](https://docs.microsoft.com/en-us/dotnet/api/System.Decimal 'System.Decimal')

<a name='AlifeApi.BusinessRules.DailyReportModels.DailyReportOutput.CallCount'></a>

## DailyReportOutput.CallCount Property

來電數

```csharp
public int CallCount { get; set; }
```

#### Property Value
[System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')

<a name='AlifeApi.BusinessRules.DailyReportModels.DailyReportOutput.CustomerDetails'></a>

## DailyReportOutput.CustomerDetails Property

當日客戶明細

```csharp
public System.Collections.Generic.List<AlifeApi.BusinessRules.DailyReportModels.DailyCustomerDetail> CustomerDetails { get; set; }
```

#### Property Value
[System.Collections.Generic.List&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Collections.Generic.List-1 'System.Collections.Generic.List`1')[DailyCustomerDetail](AlifeApi.BusinessRules.DailyReportModels.DailyCustomerDetail.md 'AlifeApi.BusinessRules.DailyReportModels.DailyCustomerDetail')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Collections.Generic.List-1 'System.Collections.Generic.List`1')

<a name='AlifeApi.BusinessRules.DailyReportModels.DailyReportOutput.DayOfWeek'></a>

## DailyReportOutput.DayOfWeek Property

星期幾

```csharp
public string DayOfWeek { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.DailyReportModels.DailyReportOutput.LeadCount'></a>

## DailyReportOutput.LeadCount Property

留單數

```csharp
public int LeadCount { get; set; }
```

#### Property Value
[System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')

<a name='AlifeApi.BusinessRules.DailyReportModels.DailyReportOutput.ReportDate'></a>

## DailyReportOutput.ReportDate Property

日報日期

```csharp
public System.DateTime ReportDate { get; set; }
```

#### Property Value
[System.DateTime](https://docs.microsoft.com/en-us/dotnet/api/System.DateTime 'System.DateTime')

<a name='AlifeApi.BusinessRules.DailyReportModels.DailyReportOutput.SiteCode'></a>

## DailyReportOutput.SiteCode Property

案場編號

```csharp
public string SiteCode { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.DailyReportModels.DailyReportOutput.SiteName'></a>

## DailyReportOutput.SiteName Property

案場名稱

```csharp
public string? SiteName { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.DailyReportModels.DailyReportOutput.TransactionConversionRate'></a>

## DailyReportOutput.TransactionConversionRate Property

成交轉換率 (成交數/留單數)

```csharp
public decimal TransactionConversionRate { get; set; }
```

#### Property Value
[System.Decimal](https://docs.microsoft.com/en-us/dotnet/api/System.Decimal 'System.Decimal')

<a name='AlifeApi.BusinessRules.DailyReportModels.DailyReportOutput.TransactionCount'></a>

## DailyReportOutput.TransactionCount Property

成交數

```csharp
public int TransactionCount { get; set; }
```

#### Property Value
[System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')

<a name='AlifeApi.BusinessRules.DailyReportModels.DailyReportOutput.VisitorConversionRate'></a>

## DailyReportOutput.VisitorConversionRate Property

來客轉換率 (留單數/來客數)

```csharp
public decimal VisitorConversionRate { get; set; }
```

#### Property Value
[System.Decimal](https://docs.microsoft.com/en-us/dotnet/api/System.Decimal 'System.Decimal')

<a name='AlifeApi.BusinessRules.DailyReportModels.DailyReportOutput.VisitorCount'></a>

## DailyReportOutput.VisitorCount Property

來客數

```csharp
public int VisitorCount { get; set; }
```

#### Property Value
[System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')