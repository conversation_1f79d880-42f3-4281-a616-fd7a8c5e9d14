#### [AlifeApi.BusinessRules](index.md 'index')
### [AlifeApi.BusinessRules.GradeModels](AlifeApi.BusinessRules.GradeModels.md 'AlifeApi.BusinessRules.GradeModels')

## GradeListResult Class

```csharp
public class GradeListResult
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; GradeListResult
### Properties

<a name='AlifeApi.BusinessRules.GradeModels.GradeListResult.CreatedTime'></a>

## GradeListResult.CreatedTime Property

建立時間

```csharp
public System.DateTime CreatedTime { get; set; }
```

#### Property Value
[System.DateTime](https://docs.microsoft.com/en-us/dotnet/api/System.DateTime 'System.DateTime')

<a name='AlifeApi.BusinessRules.GradeModels.GradeListResult.CreatedUserId'></a>

## GradeListResult.CreatedUserId Property

建立人帳號

```csharp
public string CreatedUserId { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.GradeModels.GradeListResult.FuncIds'></a>

## GradeListResult.FuncIds Property

職稱權限

```csharp
public System.Collections.Generic.IEnumerable<string> FuncIds { get; set; }
```

#### Property Value
[System.Collections.Generic.IEnumerable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Collections.Generic.IEnumerable-1 'System.Collections.Generic.IEnumerable`1')[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Collections.Generic.IEnumerable-1 'System.Collections.Generic.IEnumerable`1')

<a name='AlifeApi.BusinessRules.GradeModels.GradeListResult.GradeId'></a>

## GradeListResult.GradeId Property

使用者職稱

```csharp
public string GradeId { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.GradeModels.GradeListResult.GradeName'></a>

## GradeListResult.GradeName Property

使用者職稱

```csharp
public string GradeName { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.GradeModels.GradeListResult.IsDisabled'></a>

## GradeListResult.IsDisabled Property

是否刪除

```csharp
public bool IsDisabled { get; set; }
```

#### Property Value
[System.Boolean](https://docs.microsoft.com/en-us/dotnet/api/System.Boolean 'System.Boolean')

<a name='AlifeApi.BusinessRules.GradeModels.GradeListResult.MenuTrees'></a>

## GradeListResult.MenuTrees Property

權限樹

```csharp
public System.Collections.Generic.IEnumerable<AlifeApi.BusinessRules.RoleGroupModels.MenuTreeOutput> MenuTrees { get; set; }
```

#### Property Value
[System.Collections.Generic.IEnumerable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Collections.Generic.IEnumerable-1 'System.Collections.Generic.IEnumerable`1')[MenuTreeOutput](AlifeApi.BusinessRules.RoleGroupModels.MenuTreeOutput.md 'AlifeApi.BusinessRules.RoleGroupModels.MenuTreeOutput')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Collections.Generic.IEnumerable-1 'System.Collections.Generic.IEnumerable`1')

<a name='AlifeApi.BusinessRules.GradeModels.GradeListResult.ParentGradeId'></a>

## GradeListResult.ParentGradeId Property

上級使用者職稱

```csharp
public string ParentGradeId { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.GradeModels.GradeListResult.ParentGradeName'></a>

## GradeListResult.ParentGradeName Property

上級使用者職稱

```csharp
public string ParentGradeName { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.GradeModels.GradeListResult.Permission'></a>

## GradeListResult.Permission Property

職稱權限

```csharp
public string Permission { get; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.GradeModels.GradeListResult.UpdatedTime'></a>

## GradeListResult.UpdatedTime Property

更新時間

```csharp
public System.DateTime UpdatedTime { get; set; }
```

#### Property Value
[System.DateTime](https://docs.microsoft.com/en-us/dotnet/api/System.DateTime 'System.DateTime')

<a name='AlifeApi.BusinessRules.GradeModels.GradeListResult.UpdatedUserId'></a>

## GradeListResult.UpdatedUserId Property

更新者帳號

```csharp
public string UpdatedUserId { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.GradeModels.GradeListResult.UserIds'></a>

## GradeListResult.UserIds Property

職稱成員清單

```csharp
public System.Collections.Generic.IEnumerable<string> UserIds { get; set; }
```

#### Property Value
[System.Collections.Generic.IEnumerable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Collections.Generic.IEnumerable-1 'System.Collections.Generic.IEnumerable`1')[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Collections.Generic.IEnumerable-1 'System.Collections.Generic.IEnumerable`1')