#### [AlifeApi.Common](index.md 'index')
### [AlifeApi.Common.Util](AlifeApi.Common.Util.md 'AlifeApi.Common.Util')

## ValueMapper<TSource,TDesc> Class

提供來源與目標物件之間的值映射

```csharp
public class ValueMapper<TSource,TDesc>
```
#### Type parameters

<a name='AlifeApi.Common.Util.ValueMapper_TSource,TDesc_.TSource'></a>

`TSource`

來源物件類型

<a name='AlifeApi.Common.Util.ValueMapper_TSource,TDesc_.TDesc'></a>

`TDesc`

目標物件類型

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; ValueMapper<TSource,TDesc>
### Constructors

<a name='AlifeApi.Common.Util.ValueMapper_TSource,TDesc_.ValueMapper(TSource,TDesc)'></a>

## ValueMapper(TSource, TDesc) Constructor

初始化

```csharp
public ValueMapper(TSource sourceObject, TDesc destObject);
```
#### Parameters

<a name='AlifeApi.Common.Util.ValueMapper_TSource,TDesc_.ValueMapper(TSource,TDesc).sourceObject'></a>

`sourceObject` [TSource](AlifeApi.Common.Util.ValueMapper_TSource,TDesc_.md#AlifeApi.Common.Util.ValueMapper_TSource,TDesc_.TSource 'AlifeApi.Common.Util.ValueMapper<TSource,TDesc>.TSource')

來源物件

<a name='AlifeApi.Common.Util.ValueMapper_TSource,TDesc_.ValueMapper(TSource,TDesc).destObject'></a>

`destObject` [TDesc](AlifeApi.Common.Util.ValueMapper_TSource,TDesc_.md#AlifeApi.Common.Util.ValueMapper_TSource,TDesc_.TDesc 'AlifeApi.Common.Util.ValueMapper<TSource,TDesc>.TDesc')

目的物件
### Methods

<a name='AlifeApi.Common.Util.ValueMapper_TSource,TDesc_.MapIfHasValue_TProperty_(System.Func_TSource,System.Nullable_TProperty__,System.Linq.Expressions.Expression_System.Func_TDesc,System.Nullable_TProperty___)'></a>

## ValueMapper<TSource,TDesc>.MapIfHasValue<TProperty>(Func<TSource,Nullable<TProperty>>, Expression<Func<TDesc,Nullable<TProperty>>>) Method

如果來源選擇器返回非空值，將其映射到目的物件的屬性或欄位

```csharp
public void MapIfHasValue<TProperty>(System.Func<TSource,System.Nullable<TProperty>> sourceSelector, System.Linq.Expressions.Expression<System.Func<TDesc,System.Nullable<TProperty>>> destExpression)
    where TProperty : struct, System.ValueType, System.ValueType;
```
#### Type parameters

<a name='AlifeApi.Common.Util.ValueMapper_TSource,TDesc_.MapIfHasValue_TProperty_(System.Func_TSource,System.Nullable_TProperty__,System.Linq.Expressions.Expression_System.Func_TDesc,System.Nullable_TProperty___).TProperty'></a>

`TProperty`

屬性或欄位的類型
#### Parameters

<a name='AlifeApi.Common.Util.ValueMapper_TSource,TDesc_.MapIfHasValue_TProperty_(System.Func_TSource,System.Nullable_TProperty__,System.Linq.Expressions.Expression_System.Func_TDesc,System.Nullable_TProperty___).sourceSelector'></a>

`sourceSelector` [System.Func&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Func-2 'System.Func`2')[TSource](AlifeApi.Common.Util.ValueMapper_TSource,TDesc_.md#AlifeApi.Common.Util.ValueMapper_TSource,TDesc_.TSource 'AlifeApi.Common.Util.ValueMapper<TSource,TDesc>.TSource')[,](https://docs.microsoft.com/en-us/dotnet/api/System.Func-2 'System.Func`2')[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[TProperty](AlifeApi.Common.Util.ValueMapper_TSource,TDesc_.md#AlifeApi.Common.Util.ValueMapper_TSource,TDesc_.MapIfHasValue_TProperty_(System.Func_TSource,System.Nullable_TProperty__,System.Linq.Expressions.Expression_System.Func_TDesc,System.Nullable_TProperty___).TProperty 'AlifeApi.Common.Util.ValueMapper<TSource,TDesc>.MapIfHasValue<TProperty>(System.Func<TSource,System.Nullable<TProperty>>, System.Linq.Expressions.Expression<System.Func<TDesc,System.Nullable<TProperty>>>).TProperty')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Func-2 'System.Func`2')

來源選擇器函式

<a name='AlifeApi.Common.Util.ValueMapper_TSource,TDesc_.MapIfHasValue_TProperty_(System.Func_TSource,System.Nullable_TProperty__,System.Linq.Expressions.Expression_System.Func_TDesc,System.Nullable_TProperty___).destExpression'></a>

`destExpression` [System.Linq.Expressions.Expression&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Linq.Expressions.Expression-1 'System.Linq.Expressions.Expression`1')[System.Func&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Func-2 'System.Func`2')[TDesc](AlifeApi.Common.Util.ValueMapper_TSource,TDesc_.md#AlifeApi.Common.Util.ValueMapper_TSource,TDesc_.TDesc 'AlifeApi.Common.Util.ValueMapper<TSource,TDesc>.TDesc')[,](https://docs.microsoft.com/en-us/dotnet/api/System.Func-2 'System.Func`2')[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[TProperty](AlifeApi.Common.Util.ValueMapper_TSource,TDesc_.md#AlifeApi.Common.Util.ValueMapper_TSource,TDesc_.MapIfHasValue_TProperty_(System.Func_TSource,System.Nullable_TProperty__,System.Linq.Expressions.Expression_System.Func_TDesc,System.Nullable_TProperty___).TProperty 'AlifeApi.Common.Util.ValueMapper<TSource,TDesc>.MapIfHasValue<TProperty>(System.Func<TSource,System.Nullable<TProperty>>, System.Linq.Expressions.Expression<System.Func<TDesc,System.Nullable<TProperty>>>).TProperty')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Func-2 'System.Func`2')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Linq.Expressions.Expression-1 'System.Linq.Expressions.Expression`1')

目的表達式

<a name='AlifeApi.Common.Util.ValueMapper_TSource,TDesc_.MapIfHasValue_TProperty_(System.Func_TSource,System.Nullable_TProperty__,System.Linq.Expressions.Expression_System.Func_TDesc,TProperty__)'></a>

## ValueMapper<TSource,TDesc>.MapIfHasValue<TProperty>(Func<TSource,Nullable<TProperty>>, Expression<Func<TDesc,TProperty>>) Method

如果來源選擇器返回非空值，將其映射到目的物件的屬性或欄位

```csharp
public void MapIfHasValue<TProperty>(System.Func<TSource,System.Nullable<TProperty>> sourceSelector, System.Linq.Expressions.Expression<System.Func<TDesc,TProperty>> destExpression)
    where TProperty : struct, System.ValueType, System.ValueType;
```
#### Type parameters

<a name='AlifeApi.Common.Util.ValueMapper_TSource,TDesc_.MapIfHasValue_TProperty_(System.Func_TSource,System.Nullable_TProperty__,System.Linq.Expressions.Expression_System.Func_TDesc,TProperty__).TProperty'></a>

`TProperty`

屬性或欄位的類型
#### Parameters

<a name='AlifeApi.Common.Util.ValueMapper_TSource,TDesc_.MapIfHasValue_TProperty_(System.Func_TSource,System.Nullable_TProperty__,System.Linq.Expressions.Expression_System.Func_TDesc,TProperty__).sourceSelector'></a>

`sourceSelector` [System.Func&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Func-2 'System.Func`2')[TSource](AlifeApi.Common.Util.ValueMapper_TSource,TDesc_.md#AlifeApi.Common.Util.ValueMapper_TSource,TDesc_.TSource 'AlifeApi.Common.Util.ValueMapper<TSource,TDesc>.TSource')[,](https://docs.microsoft.com/en-us/dotnet/api/System.Func-2 'System.Func`2')[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[TProperty](AlifeApi.Common.Util.ValueMapper_TSource,TDesc_.md#AlifeApi.Common.Util.ValueMapper_TSource,TDesc_.MapIfHasValue_TProperty_(System.Func_TSource,System.Nullable_TProperty__,System.Linq.Expressions.Expression_System.Func_TDesc,TProperty__).TProperty 'AlifeApi.Common.Util.ValueMapper<TSource,TDesc>.MapIfHasValue<TProperty>(System.Func<TSource,System.Nullable<TProperty>>, System.Linq.Expressions.Expression<System.Func<TDesc,TProperty>>).TProperty')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Func-2 'System.Func`2')

來源選擇器函式

<a name='AlifeApi.Common.Util.ValueMapper_TSource,TDesc_.MapIfHasValue_TProperty_(System.Func_TSource,System.Nullable_TProperty__,System.Linq.Expressions.Expression_System.Func_TDesc,TProperty__).destExpression'></a>

`destExpression` [System.Linq.Expressions.Expression&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Linq.Expressions.Expression-1 'System.Linq.Expressions.Expression`1')[System.Func&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Func-2 'System.Func`2')[TDesc](AlifeApi.Common.Util.ValueMapper_TSource,TDesc_.md#AlifeApi.Common.Util.ValueMapper_TSource,TDesc_.TDesc 'AlifeApi.Common.Util.ValueMapper<TSource,TDesc>.TDesc')[,](https://docs.microsoft.com/en-us/dotnet/api/System.Func-2 'System.Func`2')[TProperty](AlifeApi.Common.Util.ValueMapper_TSource,TDesc_.md#AlifeApi.Common.Util.ValueMapper_TSource,TDesc_.MapIfHasValue_TProperty_(System.Func_TSource,System.Nullable_TProperty__,System.Linq.Expressions.Expression_System.Func_TDesc,TProperty__).TProperty 'AlifeApi.Common.Util.ValueMapper<TSource,TDesc>.MapIfHasValue<TProperty>(System.Func<TSource,System.Nullable<TProperty>>, System.Linq.Expressions.Expression<System.Func<TDesc,TProperty>>).TProperty')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Func-2 'System.Func`2')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Linq.Expressions.Expression-1 'System.Linq.Expressions.Expression`1')

目的表達式

<a name='AlifeApi.Common.Util.ValueMapper_TSource,TDesc_.MapIfHasValue_TProperty_(System.Func_TSource,TProperty_,System.Linq.Expressions.Expression_System.Func_TDesc,TProperty__)'></a>

## ValueMapper<TSource,TDesc>.MapIfHasValue<TProperty>(Func<TSource,TProperty>, Expression<Func<TDesc,TProperty>>) Method

如果來源選擇器返回非空值，將其映射到目的物件的屬性或欄位

```csharp
public void MapIfHasValue<TProperty>(System.Func<TSource,TProperty> sourceSelector, System.Linq.Expressions.Expression<System.Func<TDesc,TProperty>> destExpression)
    where TProperty : class;
```
#### Type parameters

<a name='AlifeApi.Common.Util.ValueMapper_TSource,TDesc_.MapIfHasValue_TProperty_(System.Func_TSource,TProperty_,System.Linq.Expressions.Expression_System.Func_TDesc,TProperty__).TProperty'></a>

`TProperty`

屬性或欄位的類型
#### Parameters

<a name='AlifeApi.Common.Util.ValueMapper_TSource,TDesc_.MapIfHasValue_TProperty_(System.Func_TSource,TProperty_,System.Linq.Expressions.Expression_System.Func_TDesc,TProperty__).sourceSelector'></a>

`sourceSelector` [System.Func&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Func-2 'System.Func`2')[TSource](AlifeApi.Common.Util.ValueMapper_TSource,TDesc_.md#AlifeApi.Common.Util.ValueMapper_TSource,TDesc_.TSource 'AlifeApi.Common.Util.ValueMapper<TSource,TDesc>.TSource')[,](https://docs.microsoft.com/en-us/dotnet/api/System.Func-2 'System.Func`2')[TProperty](AlifeApi.Common.Util.ValueMapper_TSource,TDesc_.md#AlifeApi.Common.Util.ValueMapper_TSource,TDesc_.MapIfHasValue_TProperty_(System.Func_TSource,TProperty_,System.Linq.Expressions.Expression_System.Func_TDesc,TProperty__).TProperty 'AlifeApi.Common.Util.ValueMapper<TSource,TDesc>.MapIfHasValue<TProperty>(System.Func<TSource,TProperty>, System.Linq.Expressions.Expression<System.Func<TDesc,TProperty>>).TProperty')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Func-2 'System.Func`2')

來源選擇器函式

<a name='AlifeApi.Common.Util.ValueMapper_TSource,TDesc_.MapIfHasValue_TProperty_(System.Func_TSource,TProperty_,System.Linq.Expressions.Expression_System.Func_TDesc,TProperty__).destExpression'></a>

`destExpression` [System.Linq.Expressions.Expression&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Linq.Expressions.Expression-1 'System.Linq.Expressions.Expression`1')[System.Func&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Func-2 'System.Func`2')[TDesc](AlifeApi.Common.Util.ValueMapper_TSource,TDesc_.md#AlifeApi.Common.Util.ValueMapper_TSource,TDesc_.TDesc 'AlifeApi.Common.Util.ValueMapper<TSource,TDesc>.TDesc')[,](https://docs.microsoft.com/en-us/dotnet/api/System.Func-2 'System.Func`2')[TProperty](AlifeApi.Common.Util.ValueMapper_TSource,TDesc_.md#AlifeApi.Common.Util.ValueMapper_TSource,TDesc_.MapIfHasValue_TProperty_(System.Func_TSource,TProperty_,System.Linq.Expressions.Expression_System.Func_TDesc,TProperty__).TProperty 'AlifeApi.Common.Util.ValueMapper<TSource,TDesc>.MapIfHasValue<TProperty>(System.Func<TSource,TProperty>, System.Linq.Expressions.Expression<System.Func<TDesc,TProperty>>).TProperty')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Func-2 'System.Func`2')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Linq.Expressions.Expression-1 'System.Linq.Expressions.Expression`1')

目的表達式

<a name='AlifeApi.Common.Util.ValueMapper_TSource,TDesc_.MapIfHasValue_TSourceProperty,TDescProperty_(System.Func_TSource,TSourceProperty_,System.Linq.Expressions.Expression_System.Func_TDesc,TDescProperty__,System.Func_TSourceProperty,TDescProperty_)'></a>

## ValueMapper<TSource,TDesc>.MapIfHasValue<TSourceProperty,TDescProperty>(Func<TSource,TSourceProperty>, Expression<Func<TDesc,TDescProperty>>, Func<TSourceProperty,TDescProperty>) Method

如果來源選擇器返回非空值，將其映射到目的物件的屬性或欄位

```csharp
public void MapIfHasValue<TSourceProperty,TDescProperty>(System.Func<TSource,TSourceProperty> sourceSelector, System.Linq.Expressions.Expression<System.Func<TDesc,TDescProperty>> destExpression, System.Func<TSourceProperty,TDescProperty> converter);
```
#### Type parameters

<a name='AlifeApi.Common.Util.ValueMapper_TSource,TDesc_.MapIfHasValue_TSourceProperty,TDescProperty_(System.Func_TSource,TSourceProperty_,System.Linq.Expressions.Expression_System.Func_TDesc,TDescProperty__,System.Func_TSourceProperty,TDescProperty_).TSourceProperty'></a>

`TSourceProperty`

來源屬性或欄位的類型

<a name='AlifeApi.Common.Util.ValueMapper_TSource,TDesc_.MapIfHasValue_TSourceProperty,TDescProperty_(System.Func_TSource,TSourceProperty_,System.Linq.Expressions.Expression_System.Func_TDesc,TDescProperty__,System.Func_TSourceProperty,TDescProperty_).TDescProperty'></a>

`TDescProperty`

目的屬性或欄位的類型
#### Parameters

<a name='AlifeApi.Common.Util.ValueMapper_TSource,TDesc_.MapIfHasValue_TSourceProperty,TDescProperty_(System.Func_TSource,TSourceProperty_,System.Linq.Expressions.Expression_System.Func_TDesc,TDescProperty__,System.Func_TSourceProperty,TDescProperty_).sourceSelector'></a>

`sourceSelector` [System.Func&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Func-2 'System.Func`2')[TSource](AlifeApi.Common.Util.ValueMapper_TSource,TDesc_.md#AlifeApi.Common.Util.ValueMapper_TSource,TDesc_.TSource 'AlifeApi.Common.Util.ValueMapper<TSource,TDesc>.TSource')[,](https://docs.microsoft.com/en-us/dotnet/api/System.Func-2 'System.Func`2')[TSourceProperty](AlifeApi.Common.Util.ValueMapper_TSource,TDesc_.md#AlifeApi.Common.Util.ValueMapper_TSource,TDesc_.MapIfHasValue_TSourceProperty,TDescProperty_(System.Func_TSource,TSourceProperty_,System.Linq.Expressions.Expression_System.Func_TDesc,TDescProperty__,System.Func_TSourceProperty,TDescProperty_).TSourceProperty 'AlifeApi.Common.Util.ValueMapper<TSource,TDesc>.MapIfHasValue<TSourceProperty,TDescProperty>(System.Func<TSource,TSourceProperty>, System.Linq.Expressions.Expression<System.Func<TDesc,TDescProperty>>, System.Func<TSourceProperty,TDescProperty>).TSourceProperty')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Func-2 'System.Func`2')

來源選擇器函式

<a name='AlifeApi.Common.Util.ValueMapper_TSource,TDesc_.MapIfHasValue_TSourceProperty,TDescProperty_(System.Func_TSource,TSourceProperty_,System.Linq.Expressions.Expression_System.Func_TDesc,TDescProperty__,System.Func_TSourceProperty,TDescProperty_).destExpression'></a>

`destExpression` [System.Linq.Expressions.Expression&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Linq.Expressions.Expression-1 'System.Linq.Expressions.Expression`1')[System.Func&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Func-2 'System.Func`2')[TDesc](AlifeApi.Common.Util.ValueMapper_TSource,TDesc_.md#AlifeApi.Common.Util.ValueMapper_TSource,TDesc_.TDesc 'AlifeApi.Common.Util.ValueMapper<TSource,TDesc>.TDesc')[,](https://docs.microsoft.com/en-us/dotnet/api/System.Func-2 'System.Func`2')[TDescProperty](AlifeApi.Common.Util.ValueMapper_TSource,TDesc_.md#AlifeApi.Common.Util.ValueMapper_TSource,TDesc_.MapIfHasValue_TSourceProperty,TDescProperty_(System.Func_TSource,TSourceProperty_,System.Linq.Expressions.Expression_System.Func_TDesc,TDescProperty__,System.Func_TSourceProperty,TDescProperty_).TDescProperty 'AlifeApi.Common.Util.ValueMapper<TSource,TDesc>.MapIfHasValue<TSourceProperty,TDescProperty>(System.Func<TSource,TSourceProperty>, System.Linq.Expressions.Expression<System.Func<TDesc,TDescProperty>>, System.Func<TSourceProperty,TDescProperty>).TDescProperty')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Func-2 'System.Func`2')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Linq.Expressions.Expression-1 'System.Linq.Expressions.Expression`1')

目的表達式

<a name='AlifeApi.Common.Util.ValueMapper_TSource,TDesc_.MapIfHasValue_TSourceProperty,TDescProperty_(System.Func_TSource,TSourceProperty_,System.Linq.Expressions.Expression_System.Func_TDesc,TDescProperty__,System.Func_TSourceProperty,TDescProperty_).converter'></a>

`converter` [System.Func&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Func-2 'System.Func`2')[TSourceProperty](AlifeApi.Common.Util.ValueMapper_TSource,TDesc_.md#AlifeApi.Common.Util.ValueMapper_TSource,TDesc_.MapIfHasValue_TSourceProperty,TDescProperty_(System.Func_TSource,TSourceProperty_,System.Linq.Expressions.Expression_System.Func_TDesc,TDescProperty__,System.Func_TSourceProperty,TDescProperty_).TSourceProperty 'AlifeApi.Common.Util.ValueMapper<TSource,TDesc>.MapIfHasValue<TSourceProperty,TDescProperty>(System.Func<TSource,TSourceProperty>, System.Linq.Expressions.Expression<System.Func<TDesc,TDescProperty>>, System.Func<TSourceProperty,TDescProperty>).TSourceProperty')[,](https://docs.microsoft.com/en-us/dotnet/api/System.Func-2 'System.Func`2')[TDescProperty](AlifeApi.Common.Util.ValueMapper_TSource,TDesc_.md#AlifeApi.Common.Util.ValueMapper_TSource,TDesc_.MapIfHasValue_TSourceProperty,TDescProperty_(System.Func_TSource,TSourceProperty_,System.Linq.Expressions.Expression_System.Func_TDesc,TDescProperty__,System.Func_TSourceProperty,TDescProperty_).TDescProperty 'AlifeApi.Common.Util.ValueMapper<TSource,TDesc>.MapIfHasValue<TSourceProperty,TDescProperty>(System.Func<TSource,TSourceProperty>, System.Linq.Expressions.Expression<System.Func<TDesc,TDescProperty>>, System.Func<TSourceProperty,TDescProperty>).TDescProperty')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Func-2 'System.Func`2')

可選的轉換器函式