﻿using System;
using System.Collections.Generic;

namespace AlifeApi.DataAccess.ProjectContext
{
    public partial class SysBulletinsAttach
    {
        public SysBulletinsAttach()
        {
            SysBulletinsAttachDownloadRecord = new HashSet<SysBulletinsAttachDownloadRecord>();
        }

        /// <summary>
        /// 公告附檔流水號
        /// </summary>
        public int BlaId { get; set; }

        /// <summary>
        /// 公告流水號
        /// </summary>
        public int BlId { get; set; }

        /// <summary>
        /// 檔案原名
        /// </summary>
        public string BlaOname { get; set; }

        /// <summary>
        /// 檔案
        /// </summary>
        public byte[] BlaFile { get; set; }

        /// <summary>
        /// 檔案類型
        /// </summary>
        public string BlaFileType { get; set; }

        public int? BlaFileSzie { get; set; }

        /// <summary>
        /// 建立者
        /// </summary>
        public string BlaCrUser { get; set; }

        /// <summary>
        /// 建立時間
        /// </summary>
        public DateTime? BlaCrDatetime { get; set; }

        public virtual SysBulletins Bl { get; set; }

        public virtual ICollection<SysBulletinsAttachDownloadRecord> SysBulletinsAttachDownloadRecord { get; set; }

    }
}
