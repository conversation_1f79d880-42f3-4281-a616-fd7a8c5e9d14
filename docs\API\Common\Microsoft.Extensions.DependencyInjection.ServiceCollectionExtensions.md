#### [AlifeApi.Common](index.md 'index')
### [Microsoft.Extensions.DependencyInjection](Microsoft.Extensions.DependencyInjection.md 'Microsoft.Extensions.DependencyInjection')

## ServiceCollectionExtensions Class

IServiceCollection 的擴充方法

```csharp
public static class ServiceCollectionExtensions
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; ServiceCollectionExtensions
### Methods

<a name='Microsoft.Extensions.DependencyInjection.ServiceCollectionExtensions.AddDependencies(thisMicrosoft.Extensions.DependencyInjection.IServiceCollection,string)'></a>

## ServiceCollectionExtensions.AddDependencies(this IServiceCollection, string) Method

在指定的 Assembly 集合中，尋找符合指定 Assembly 名稱模式的 Assembly，並將其中實作 `IScopedDependency`、`ITransientDependency` 和 `ISingletonDependency` 的類別進行相依性注入設定

```csharp
public static Microsoft.Extensions.DependencyInjection.IServiceCollection AddDependencies(this Microsoft.Extensions.DependencyInjection.IServiceCollection services, string dllFileNamePattern);
```
#### Parameters

<a name='Microsoft.Extensions.DependencyInjection.ServiceCollectionExtensions.AddDependencies(thisMicrosoft.Extensions.DependencyInjection.IServiceCollection,string).services'></a>

`services` [Microsoft.Extensions.DependencyInjection.IServiceCollection](https://docs.microsoft.com/en-us/dotnet/api/Microsoft.Extensions.DependencyInjection.IServiceCollection 'Microsoft.Extensions.DependencyInjection.IServiceCollection')

相依性注入的服務集合

<a name='Microsoft.Extensions.DependencyInjection.ServiceCollectionExtensions.AddDependencies(thisMicrosoft.Extensions.DependencyInjection.IServiceCollection,string).dllFileNamePattern'></a>

`dllFileNamePattern` [System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

The assembly name pattern.

#### Returns
[Microsoft.Extensions.DependencyInjection.IServiceCollection](https://docs.microsoft.com/en-us/dotnet/api/Microsoft.Extensions.DependencyInjection.IServiceCollection 'Microsoft.Extensions.DependencyInjection.IServiceCollection')  
已註冊服務的服務集合