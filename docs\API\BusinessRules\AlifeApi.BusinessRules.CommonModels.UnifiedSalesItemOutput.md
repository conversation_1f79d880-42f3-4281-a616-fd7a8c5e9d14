#### [AlifeApi.BusinessRules](index.md 'index')
### [AlifeApi.BusinessRules.CommonModels](AlifeApi.BusinessRules.CommonModels.md 'AlifeApi.BusinessRules.CommonModels')

## UnifiedSalesItemOutput Class

統一銷售物件輸出

```csharp
public class UnifiedSalesItemOutput
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; UnifiedSalesItemOutput
### Properties

<a name='AlifeApi.BusinessRules.CommonModels.UnifiedSalesItemOutput.Area'></a>

## UnifiedSalesItemOutput.Area Property

面積（僅房屋有，車位為null）

```csharp
public System.Nullable<decimal> Area { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.Decimal](https://docs.microsoft.com/en-us/dotnet/api/System.Decimal 'System.Decimal')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

<a name='AlifeApi.BusinessRules.CommonModels.UnifiedSalesItemOutput.BuildingId'></a>

## UnifiedSalesItemOutput.BuildingId Property

建築物ID

```csharp
public int BuildingId { get; set; }
```

#### Property Value
[System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')

<a name='AlifeApi.BusinessRules.CommonModels.UnifiedSalesItemOutput.BuildingName'></a>

## UnifiedSalesItemOutput.BuildingName Property

建築物名稱

```csharp
public string? BuildingName { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.CommonModels.UnifiedSalesItemOutput.CreatedTime'></a>

## UnifiedSalesItemOutput.CreatedTime Property

建立時間

```csharp
public System.DateTime CreatedTime { get; set; }
```

#### Property Value
[System.DateTime](https://docs.microsoft.com/en-us/dotnet/api/System.DateTime 'System.DateTime')

<a name='AlifeApi.BusinessRules.CommonModels.UnifiedSalesItemOutput.FloorId'></a>

## UnifiedSalesItemOutput.FloorId Property

樓層ID

```csharp
public int FloorId { get; set; }
```

#### Property Value
[System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')

<a name='AlifeApi.BusinessRules.CommonModels.UnifiedSalesItemOutput.FloorLabel'></a>

## UnifiedSalesItemOutput.FloorLabel Property

樓層標示

```csharp
public string? FloorLabel { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.CommonModels.UnifiedSalesItemOutput.Id'></a>

## UnifiedSalesItemOutput.Id Property

物件ID（房屋為UnitId，車位為ParkingSpaceId）

```csharp
public int Id { get; set; }
```

#### Property Value
[System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')

<a name='AlifeApi.BusinessRules.CommonModels.UnifiedSalesItemOutput.ItemType'></a>

## UnifiedSalesItemOutput.ItemType Property

物件類別（"房屋"、"車位"）

```csharp
public string ItemType { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.CommonModels.UnifiedSalesItemOutput.Layout'></a>

## UnifiedSalesItemOutput.Layout Property

格局/尺寸（房屋為格局，車位為尺寸）

```csharp
public string? Layout { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.CommonModels.UnifiedSalesItemOutput.ListPrice'></a>

## UnifiedSalesItemOutput.ListPrice Property

表價

```csharp
public System.Nullable<decimal> ListPrice { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.Decimal](https://docs.microsoft.com/en-us/dotnet/api/System.Decimal 'System.Decimal')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

<a name='AlifeApi.BusinessRules.CommonModels.UnifiedSalesItemOutput.MinimumPrice'></a>

## UnifiedSalesItemOutput.MinimumPrice Property

底價

```csharp
public System.Nullable<decimal> MinimumPrice { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.Decimal](https://docs.microsoft.com/en-us/dotnet/api/System.Decimal 'System.Decimal')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

<a name='AlifeApi.BusinessRules.CommonModels.UnifiedSalesItemOutput.Number'></a>

## UnifiedSalesItemOutput.Number Property

編號（房屋為戶號，車位為車位編號）

```csharp
public string Number { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.CommonModels.UnifiedSalesItemOutput.Remarks'></a>

## UnifiedSalesItemOutput.Remarks Property

備註

```csharp
public string? Remarks { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.CommonModels.UnifiedSalesItemOutput.SiteCode'></a>

## UnifiedSalesItemOutput.SiteCode Property

案場編號

```csharp
public string SiteCode { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.CommonModels.UnifiedSalesItemOutput.SiteName'></a>

## UnifiedSalesItemOutput.SiteName Property

案場名稱

```csharp
public string? SiteName { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.CommonModels.UnifiedSalesItemOutput.Status'></a>

## UnifiedSalesItemOutput.Status Property

狀態

```csharp
public string Status { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.CommonModels.UnifiedSalesItemOutput.TransactionPrice'></a>

## UnifiedSalesItemOutput.TransactionPrice Property

成交價

```csharp
public System.Nullable<decimal> TransactionPrice { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.Decimal](https://docs.microsoft.com/en-us/dotnet/api/System.Decimal 'System.Decimal')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

<a name='AlifeApi.BusinessRules.CommonModels.UnifiedSalesItemOutput.Type'></a>

## UnifiedSalesItemOutput.Type Property

類型（房屋為UnitType，車位為SpaceType）

```csharp
public string Type { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.CommonModels.UnifiedSalesItemOutput.UpdatedTime'></a>

## UnifiedSalesItemOutput.UpdatedTime Property

更新時間

```csharp
public System.DateTime UpdatedTime { get; set; }
```

#### Property Value
[System.DateTime](https://docs.microsoft.com/en-us/dotnet/api/System.DateTime 'System.DateTime')