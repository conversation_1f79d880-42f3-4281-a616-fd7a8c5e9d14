﻿using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using AlifeApi.BusinessRules.Infrastructure;
using AlifeApi.BusinessRules.SecuritySettingModels;
using AlifeApi.DataAccess.ProjectContent;
using AlifeApi.DataAccess.ProjectContext;
using AlifeApi.WebApi.Options;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using Microsoft.IdentityModel.Tokens;

namespace AlifeApi.WebApi.Util
{
    public class JwtHelper : ServiceBase<alifeContext>
    {

        private readonly TokenValidationParameters tokenValidationParameters;
        private readonly JwtOptions jwtOptions;


        public JwtHelper(IServiceProvider serviceProvider, alifeContext dbContext, TokenValidationParameters tokenValidationParameters, IOptionsSnapshot<JwtOptions> jwtSettingsAccessor) : base(serviceProvider, dbContext)
        {
            this.tokenValidationParameters = tokenValidationParameters;
            jwtOptions = jwtSettingsAccessor?.Value ?? throw new ArgumentNullException(nameof(jwtSettingsAccessor));
        }
        /// <summary>
        /// 產生 Token
        /// </summary>
        /// <param name="userId">The user identifier.</param>
        /// <param name="deptId">The dept identifier.</param>
        /// <param name="gradeCoe">The grade coe.</param>
        /// <param name="roleIds">The role ids.</param>
        /// <returns>JWT Token</returns>
        public string GenerateToken(string userId, string deptId, string gradeCoe, IEnumerable<string> roleIds)
        {
            var expirationTime = Convert.ToInt32(Db.SysSystemSettings.AsNoTracking().AsQueryable().Where(x => x.Type == "SecuritySetting" && x.Key == "AutoLogoutTime" && x.IsEnabled == true).FirstOrDefault()?.Value ?? Int32.MaxValue.ToString());
            // 設定要加入到 JWT Token 中的聲明資訊(Claims)
            List<Claim> claims = new()
            {
                new Claim(ClaimTypes.NameIdentifier, userId),
                new Claim(KunYouClaimTypes.DeptId, deptId),
                new Claim(KunYouClaimTypes.GradeCode, gradeCoe),
            };

            foreach (string roleId in roleIds)
            {
                claims.Add(new Claim(KunYouClaimTypes.Role, roleId));
            }

            ClaimsIdentity userClaimsIdentity = new(claims);

            // HmacSha256 有要求必須要大於 128 bits，所以 key 不能太短，至少要 16 字元以上
            // https://stackoverflow.com/questions/47279947/idx10603-the-algorithm-hs256-requires-the-securitykey-keysize-to-be-greater
            SigningCredentials signingCredentials = new(tokenValidationParameters.IssuerSigningKey, SecurityAlgorithms.HmacSha256Signature);

            // 建立 SecurityTokenDescriptor
            SecurityTokenDescriptor tokenDescriptor = new()
            {
                Issuer = jwtOptions.Issuer,
                Subject = userClaimsIdentity,
                Expires = DateTime.Now.AddMinutes(expirationTime),
                SigningCredentials = signingCredentials,
            };

            // 產出所需要的 JWT securityToken 物件，並取得序列化後的 Token 結果(字串格式)
            JwtSecurityTokenHandler tokenHandler = new();
            SecurityToken securityToken = tokenHandler.CreateToken(tokenDescriptor);

            return tokenHandler.WriteToken(securityToken);
        }

        /// <summary>
        /// 產生 Token
        /// </summary>
        /// <param name="userId">The user identifier.</param>
        /// <param name="deptId">The dept identifier.</param>
        /// <param name="gradeCoe">The grade coe.</param>
        /// <param name="roleIds">The role ids.</param>
        /// <returns>JWT Token</returns>
        public string GenerateTokenV2(string userId)
        {
            var expirationTime = Convert.ToInt32(Db.SysSystemSettings.AsNoTracking().AsQueryable().Where(x => x.Type == "SecuritySetting" && x.Key == "AutoLogoutTime" && x.IsEnabled == true).FirstOrDefault()?.Value ?? Int32.MaxValue.ToString());
            // 設定要加入到 JWT Token 中的聲明資訊(Claims)
            List<Claim> claims = new()
            {
                new Claim(ClaimTypes.NameIdentifier, userId),
            };


            ClaimsIdentity userClaimsIdentity = new(claims);

            // HmacSha256 有要求必須要大於 128 bits，所以 key 不能太短，至少要 16 字元以上
            // https://stackoverflow.com/questions/47279947/idx10603-the-algorithm-hs256-requires-the-securitykey-keysize-to-be-greater
            SigningCredentials signingCredentials = new(tokenValidationParameters.IssuerSigningKey, SecurityAlgorithms.HmacSha256Signature);

            // 建立 SecurityTokenDescriptor
            SecurityTokenDescriptor tokenDescriptor = new()
            {
                Issuer = jwtOptions.Issuer,
                Subject = userClaimsIdentity,
                Expires = DateTime.Now.AddMinutes(expirationTime),
                SigningCredentials = signingCredentials,
            };

            // 產出所需要的 JWT securityToken 物件，並取得序列化後的 Token 結果(字串格式)
            JwtSecurityTokenHandler tokenHandler = new();
            SecurityToken securityToken = tokenHandler.CreateToken(tokenDescriptor);

            return tokenHandler.WriteToken(securityToken);
        }

        /// <summary>
        /// 取回 Token 的有效日期時間
        /// </summary>
        /// <param name="token">The token.</param>
        public DateTime? GetTokenExpirationTime(string token)
        {
            (string _, DateTime? expirationTime) = GetTokenInfo(token);

            return expirationTime;
        }

        /// <summary>
        /// 取回 Token 的有效日期時間
        /// </summary>
        /// <param name="token">The token.</param>
        public (string, DateTime?) GetTokenInfo(string token)
        {
            ArgumentNullException.ThrowIfNull(token, nameof(token));

            token = token.Replace(JwtBearerDefaults.AuthenticationScheme, "").Trim();

            JwtSecurityTokenHandler jwtTokenHandler = new();
            ClaimsPrincipal tokenInVerification = jwtTokenHandler.ValidateToken(token, tokenValidationParameters, out SecurityToken validatedToken);

            if (validatedToken is JwtSecurityToken jwtSecurityToken)
            {
                if (!jwtSecurityToken.Header.Alg.Equals(SecurityAlgorithms.HmacSha256, StringComparison.InvariantCultureIgnoreCase))
                {
                    return (null, null);
                }
            }

            long expirationTimeStamp = long.Parse(tokenInVerification.Claims.FirstOrDefault(x => x.Type == JwtRegisteredClaimNames.Exp).Value);
            string userId = tokenInVerification.Claims.FirstOrDefault(c => c.Type.Equals(ClaimTypes.NameIdentifier))?.Value;

            return (userId, ConvertUnixTimeStampToDateTimeOffset(expirationTimeStamp));
        }

        /// <summary>
        /// 將 Unix 時間戳轉換為目標時區的 DateTimeOffset。
        /// </summary>
        /// <param name="unixTimeStamp">Unix 時間戳。</param>
        /// <returns>目標時區的 DateTimeOffset。</returns>
        private static DateTime ConvertUnixTimeStampToDateTimeOffset(long unixTimeStamp)
        {
            return new DateTime(1970, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc)
                .AddSeconds(unixTimeStamp)
                .ToUniversalTime();
        }
    }
}
