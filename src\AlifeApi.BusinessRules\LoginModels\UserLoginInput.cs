﻿using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;

namespace AlifeApi.BusinessRules.LoginModels
{
    /// <summary>
    /// 登入使用的 Request 資料模型
    /// </summary>
    public class UserLoginInput
    {
        /// <summary>
        /// 使用者帳號唯一識別值
        /// </summary>
        [Required]
        public string UserInfoId { get; set; }

        /// <summary>
        /// 使用者密碼
        /// </summary>
        [JsonPropertyName("UserPW")]
        [Required]
        public string UserPw { get; set; }

        /// <summary>
        /// 驗證碼
        /// </summary>
        [Required]
        public string Captcha { get; set; }
    }
}
