﻿using AlifeApi.BusinessRules.Infrastructure;
using AlifeApi.BusinessRules.LoginModels;
using AlifeApi.BusinessRules.SiteModels;
using AlifeApi.BusinessRules.UserInfoModels;
using AlifeApi.Common.Util;
using AlifeApi.WebApi.SignalR;
using AlifeApi.WebApi.Util;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.SignalR;
using Microsoft.Extensions.Primitives;

namespace AlifeApi.WebApi.Controllers
{
    /// <summary>
    /// 使用者操作相關(UserInfo)
    /// </summary>
    public class UserInfoController : AuthenticatedController
    {
        private readonly UserInfoServicePg _userInfoService;
        private readonly LoginServicePg _loginService;
        private readonly JwtHelper _jwtHelper;
        private readonly IHubContext<ConnectHub> _hubcontext;

        /// <summary>
        /// 建構子
        /// </summary>
        public UserInfoController(UserInfoServicePg userInfoService, LoginServicePg loginService, JwtHelper jwtHelper, IHubContext<ConnectHub> hubcontext)
        {
            _userInfoService = userInfoService ?? throw new ArgumentNullException(nameof(userInfoService));
            _loginService = loginService ?? throw new ArgumentNullException(nameof(loginService));
            _jwtHelper = jwtHelper ?? throw new ArgumentNullException(nameof(jwtHelper));
            _hubcontext = hubcontext ?? throw new ArgumentNullException(nameof(hubcontext));
        }

        /// <summary>
        /// 取得使用者選單
        /// </summary>
        /// <returns>使用者下拉選單列表</returns>
        [HttpPost]
        public async Task<ActionResult<List<UserInfoDropdownOutput>>> GetUserInfoDropdownListAsync(UserInfoDropdownInput input)
        {
            try
            {
                var result = await _userInfoService.GetUserInfoDropdownListAsync(input);
                return Ok(result);
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }

        /// <summary>
        /// 帳號權限管理-使用者清單
        /// </summary>
        /// <returns>使用者清單</returns>
        [HttpPost]
        public async Task<ActionResult<PagedListOutput<UserInfoListItemGetOutputPg>>> GetUserInfoListAsync(UserInfoListGetInput input)
        {
            try
            {
                var result = await _userInfoService.GetUserInfoListAsync(input);
                return Ok(result);
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }

        /// <summary>
        /// 使用者登入
        /// </summary>
        /// <param name="encryptionHelper">The encryption helper.</param>
        /// <param name="input">The input.</param>
        /// <returns>登入結果</returns>
        [AllowAnonymous]
        [HttpPost]
        public async Task<ActionResult<UserLoginPgOutput>> UserLoginAsync(
            [FromServices] IEncryptionHelper encryptionHelper, UserLoginInput input)
        {
            ArgumentNullException.ThrowIfNull(input, nameof(input));
            string session = HttpContext.Session.GetString("AlifeAuth");

            if (session is null)
            {
                return Ok(new UserLoginPgOutput
                {
                    Response = Message.LoginInfoWithoutCaptcha
                });
            }

            if (encryptionHelper.Encrypt(input.Captcha) != session)
            {
                return Ok(new UserLoginPgOutput
                {
                    Response = Message.CaptchaFail
                });
            }

            UserLoginPgOutput output = await _loginService.UserLoginAsync(input);
            if ((Message)output?.Response == Message.Success)
            {
                output.JwtToken = JwtBearerDefaults.AuthenticationScheme + " " + _jwtHelper.GenerateTokenV2(output.UserInfoId);
                output.JwtTokenExpireTime = _jwtHelper.GetTokenExpirationTime(output.JwtToken);
            }

            return Ok(output);
        }

        /// <summary>
        /// 帳號權限管理-帳號新增
        /// </summary>
        /// <param name="input">使用者新增輸入資料</param>
        /// <returns>新增結果</returns>
        [HttpPost]
        public async Task<ActionResult<UserInfoUpdateOutput>> CreateUserInfoAsync(UserInfoCreateInputPg input)
        {
            try
            {
                var result = await _userInfoService.CreateUserInfoAsync(input);
                return Ok(result);
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }

        /// <summary>
        /// 修改使用者資料
        /// </summary>
        /// <param name="input">使用者更新輸入資料</param>
        /// <returns>修改結果</returns>
        [HttpPost]
        public async Task<ActionResult<UserInfoUpdateOutput>> UpdateUserInfoAsync(UserInfoUpdateInputPg input)
        {
            try
            {
                var result = await _userInfoService.UpdateUserInfoAsync(input);
                return Ok(result);
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }

        /// <summary>
        /// 帳號權限管理-修改密碼
        /// </summary>
        /// <param name="input">密碼修改輸入資料</param>
        /// <returns>修改結果</returns>
        [HttpPost]
        public async Task<ActionResult<UserInfoUpdateOutput>> ChangePasswordAsync(PasswordChangeInput input)
        {
            try
            {
                var result = await _userInfoService.ChangePasswordAsync(input);
                return Ok(result);
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }

        /// <summary>
        /// 使用者-使用者登出
        /// </summary>
        /// <returns>ClientId</returns>
        [HttpPost]
        public async Task<ActionResult<string>> UserLogoutAsync()
        {
            try
            {
                if (Request.Headers.TryGetValue("Authorization", out StringValues token))
                {
                    (string userId, DateTime? _) = _jwtHelper.GetTokenInfo(token);
                    var client = ConnectHub.userConnections.FirstOrDefault(x => x.UserId == userId);
                    if (client != null)
                    {
                        await _hubcontext.Clients.Client(client.ConnectionId).SendAsync("ForceDisconnect");
                    }
                }

                Request.Headers.TryGetValue("ClientId", out StringValues clientId);
                return Ok(clientId.ToString());
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }

        /// <summary>
        /// 使用者-自動登出
        /// </summary>
        /// <returns>ClientId</returns>
        [HttpPost]
        public async Task<ActionResult<string>> AutoLogoutAsync()
        {
            return await UserLogoutAsync();
        }

        /// <summary>
        /// 更新使用者帳號狀態
        /// </summary>
        /// <param name="input">使用者狀態更新輸入資料</param>
        /// <returns>更新結果</returns>
        [HttpPost]
        public async Task<ActionResult<UserInfoUpdateOutput>> UpdateUserInfoStatusAsync([FromBody] UserInfoStatusUpdateInputPg input)
        {
            try
            {
                var result = await _userInfoService.UpdateUserInfoStatusAsync(input);
                return Ok(result);
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }
    }
}
