#### [AlifeApi.BusinessRules](index.md 'index')
### [AlifeApi.BusinessRules.ProjectVersionModels](AlifeApi.BusinessRules.ProjectVersionModels.md 'AlifeApi.BusinessRules.ProjectVersionModels')

## ProjectVersionListCondition Class

```csharp
public class ProjectVersionListCondition : AlifeApi.BusinessRules.Infrastructure.PagedListInput
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; [PagedListInput](AlifeApi.BusinessRules.Infrastructure.PagedListInput.md 'AlifeApi.BusinessRules.Infrastructure.PagedListInput') &#129106; ProjectVersionListCondition
### Properties

<a name='AlifeApi.BusinessRules.ProjectVersionModels.ProjectVersionListCondition.SearchContent'></a>

## ProjectVersionListCondition.SearchContent Property

搜尋內容

```csharp
public string SearchContent { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.ProjectVersionModels.ProjectVersionListCondition.SearchEndDate'></a>

## ProjectVersionListCondition.SearchEndDate Property

搜尋結束時間

```csharp
public System.Nullable<System.DateTime> SearchEndDate { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.DateTime](https://docs.microsoft.com/en-us/dotnet/api/System.DateTime 'System.DateTime')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

<a name='AlifeApi.BusinessRules.ProjectVersionModels.ProjectVersionListCondition.SearchStartDate'></a>

## ProjectVersionListCondition.SearchStartDate Property

搜尋起始時間

```csharp
public System.Nullable<System.DateTime> SearchStartDate { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.DateTime](https://docs.microsoft.com/en-us/dotnet/api/System.DateTime 'System.DateTime')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')