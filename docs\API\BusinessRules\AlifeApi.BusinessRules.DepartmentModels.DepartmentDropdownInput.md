#### [AlifeApi.BusinessRules](index.md 'index')
### [AlifeApi.BusinessRules.DepartmentModels](AlifeApi.BusinessRules.DepartmentModels.md 'AlifeApi.BusinessRules.DepartmentModels')

## DepartmentDropdownInput Class

部門下拉選單輸入資料

```csharp
public class DepartmentDropdownInput
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; DepartmentDropdownInput
### Properties

<a name='AlifeApi.BusinessRules.DepartmentModels.DepartmentDropdownInput.CompanyId'></a>

## DepartmentDropdownInput.CompanyId Property

公司ID

```csharp
public string CompanyId { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')