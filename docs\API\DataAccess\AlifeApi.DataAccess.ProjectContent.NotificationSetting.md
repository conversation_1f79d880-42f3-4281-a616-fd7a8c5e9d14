#### [AlifeApi.DataAccess](index.md 'index')
### [AlifeApi.DataAccess.ProjectContent](AlifeApi.DataAccess.ProjectContent.md 'AlifeApi.DataAccess.ProjectContent')

## NotificationSetting Class

審核通知設定表，用於儲存每個審核步驟的通知設定資訊。

```csharp
public class NotificationSetting
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; NotificationSetting
### Properties

<a name='AlifeApi.DataAccess.ProjectContent.NotificationSetting.Enabled'></a>

## NotificationSetting.Enabled Property

是否啟用，true 表示啟用，false 表示停用，預設為 true。

```csharp
public System.Nullable<bool> Enabled { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.Boolean](https://docs.microsoft.com/en-us/dotnet/api/System.Boolean 'System.Boolean')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

<a name='AlifeApi.DataAccess.ProjectContent.NotificationSetting.NotifyType'></a>

## NotificationSetting.NotifyType Property

通知類型，可選值為 system（系統通知）、email（電子郵件）、sms（簡訊）。

```csharp
public string NotifyType { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContent.NotificationSetting.SettingId'></a>

## NotificationSetting.SettingId Property

設定流水號，主鍵，自動遞增，用於唯一識別通知設定記錄。

```csharp
public int SettingId { get; set; }
```

#### Property Value
[System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')

<a name='AlifeApi.DataAccess.ProjectContent.NotificationSetting.StepId'></a>

## NotificationSetting.StepId Property

步驟流水號，對應 ReviewSteps 表的 StepId。

```csharp
public System.Nullable<int> StepId { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')