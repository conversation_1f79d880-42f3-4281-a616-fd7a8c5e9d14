#### [AlifeApi.BusinessRules](index.md 'index')
### [AlifeApi.BusinessRules.CategoryModels](AlifeApi.BusinessRules.CategoryModels.md 'AlifeApi.BusinessRules.CategoryModels')

## SmallCategoryService Class

小分類服務

```csharp
public class SmallCategoryService : AlifeApi.BusinessRules.Infrastructure.ServiceBase<AlifeApi.DataAccess.ProjectContent.alifeContext>
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; [AlifeApi.Common.InfrastructureBase](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.Common.InfrastructureBase 'AlifeApi.Common.InfrastructureBase') &#129106; [AlifeApi.BusinessRules.Infrastructure.ServiceBase&lt;](AlifeApi.BusinessRules.Infrastructure.ServiceBase_TDbContext_.md 'AlifeApi.BusinessRules.Infrastructure.ServiceBase<TDbContext>')[AlifeApi.DataAccess.ProjectContent.alifeContext](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.DataAccess.ProjectContent.alifeContext 'AlifeApi.DataAccess.ProjectContent.alifeContext')[&gt;](AlifeApi.BusinessRules.Infrastructure.ServiceBase_TDbContext_.md 'AlifeApi.BusinessRules.Infrastructure.ServiceBase<TDbContext>') &#129106; SmallCategoryService
### Methods

<a name='AlifeApi.BusinessRules.CategoryModels.SmallCategoryService.CreateSmallCategoryAsync(AlifeApi.BusinessRules.CategoryModels.SmallCategoryInput)'></a>

## SmallCategoryService.CreateSmallCategoryAsync(SmallCategoryInput) Method

建立小分類

```csharp
public System.Threading.Tasks.Task<long> CreateSmallCategoryAsync(AlifeApi.BusinessRules.CategoryModels.SmallCategoryInput input);
```
#### Parameters

<a name='AlifeApi.BusinessRules.CategoryModels.SmallCategoryService.CreateSmallCategoryAsync(AlifeApi.BusinessRules.CategoryModels.SmallCategoryInput).input'></a>

`input` [SmallCategoryInput](AlifeApi.BusinessRules.CategoryModels.SmallCategoryInput.md 'AlifeApi.BusinessRules.CategoryModels.SmallCategoryInput')

小分類建立輸入資料

#### Returns
[System.Threading.Tasks.Task&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')[System.Int64](https://docs.microsoft.com/en-us/dotnet/api/System.Int64 'System.Int64')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')  
新建小分類的ID

<a name='AlifeApi.BusinessRules.CategoryModels.SmallCategoryService.DeleteSmallCategoryAsync(long)'></a>

## SmallCategoryService.DeleteSmallCategoryAsync(long) Method

刪除小分類

```csharp
public System.Threading.Tasks.Task DeleteSmallCategoryAsync(long smallCategoryId);
```
#### Parameters

<a name='AlifeApi.BusinessRules.CategoryModels.SmallCategoryService.DeleteSmallCategoryAsync(long).smallCategoryId'></a>

`smallCategoryId` [System.Int64](https://docs.microsoft.com/en-us/dotnet/api/System.Int64 'System.Int64')

小分類ID

#### Returns
[System.Threading.Tasks.Task](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task 'System.Threading.Tasks.Task')

<a name='AlifeApi.BusinessRules.CategoryModels.SmallCategoryService.GetSmallCategoryByIdAsync(long)'></a>

## SmallCategoryService.GetSmallCategoryByIdAsync(long) Method

根據ID取得小分類詳細資料

```csharp
public System.Threading.Tasks.Task<AlifeApi.BusinessRules.CategoryModels.SmallCategoryOutput?> GetSmallCategoryByIdAsync(long smallCategoryId);
```
#### Parameters

<a name='AlifeApi.BusinessRules.CategoryModels.SmallCategoryService.GetSmallCategoryByIdAsync(long).smallCategoryId'></a>

`smallCategoryId` [System.Int64](https://docs.microsoft.com/en-us/dotnet/api/System.Int64 'System.Int64')

小分類ID

#### Returns
[System.Threading.Tasks.Task&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')[SmallCategoryOutput](AlifeApi.BusinessRules.CategoryModels.SmallCategoryOutput.md 'AlifeApi.BusinessRules.CategoryModels.SmallCategoryOutput')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')  
小分類詳細資料

<a name='AlifeApi.BusinessRules.CategoryModels.SmallCategoryService.GetSmallCategoryDropdownAsync(System.Nullable_long_,System.Nullable_long_)'></a>

## SmallCategoryService.GetSmallCategoryDropdownAsync(Nullable<long>, Nullable<long>) Method

取得小分類下拉選單列表

```csharp
public System.Threading.Tasks.Task<System.Collections.Generic.List<AlifeApi.BusinessRules.CategoryModels.SmallCategoryDropdownOutput>> GetSmallCategoryDropdownAsync(System.Nullable<long> largeCategoryId=null, System.Nullable<long> mediumCategoryId=null);
```
#### Parameters

<a name='AlifeApi.BusinessRules.CategoryModels.SmallCategoryService.GetSmallCategoryDropdownAsync(System.Nullable_long_,System.Nullable_long_).largeCategoryId'></a>

`largeCategoryId` [System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.Int64](https://docs.microsoft.com/en-us/dotnet/api/System.Int64 'System.Int64')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

可選的大分類ID，用於篩選特定大分類下的小分類

<a name='AlifeApi.BusinessRules.CategoryModels.SmallCategoryService.GetSmallCategoryDropdownAsync(System.Nullable_long_,System.Nullable_long_).mediumCategoryId'></a>

`mediumCategoryId` [System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.Int64](https://docs.microsoft.com/en-us/dotnet/api/System.Int64 'System.Int64')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

可選的中分類ID，用於篩選特定中分類下的小分類

#### Returns
[System.Threading.Tasks.Task&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')[System.Collections.Generic.List&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Collections.Generic.List-1 'System.Collections.Generic.List`1')[SmallCategoryDropdownOutput](AlifeApi.BusinessRules.CategoryModels.SmallCategoryDropdownOutput.md 'AlifeApi.BusinessRules.CategoryModels.SmallCategoryDropdownOutput')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Collections.Generic.List-1 'System.Collections.Generic.List`1')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')  
啟用的小分類下拉選單列表

<a name='AlifeApi.BusinessRules.CategoryModels.SmallCategoryService.GetSmallCategoryListAsync(AlifeApi.BusinessRules.CategoryModels.SmallCategoryQueryInput)'></a>

## SmallCategoryService.GetSmallCategoryListAsync(SmallCategoryQueryInput) Method

取得小分類列表 (分頁)

```csharp
public System.Threading.Tasks.Task<AlifeApi.BusinessRules.Infrastructure.PagedListOutput<AlifeApi.BusinessRules.CategoryModels.SmallCategoryListOutput>> GetSmallCategoryListAsync(AlifeApi.BusinessRules.CategoryModels.SmallCategoryQueryInput input);
```
#### Parameters

<a name='AlifeApi.BusinessRules.CategoryModels.SmallCategoryService.GetSmallCategoryListAsync(AlifeApi.BusinessRules.CategoryModels.SmallCategoryQueryInput).input'></a>

`input` [SmallCategoryQueryInput](AlifeApi.BusinessRules.CategoryModels.SmallCategoryQueryInput.md 'AlifeApi.BusinessRules.CategoryModels.SmallCategoryQueryInput')

查詢條件

#### Returns
[System.Threading.Tasks.Task&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')[AlifeApi.BusinessRules.Infrastructure.PagedListOutput&lt;](AlifeApi.BusinessRules.Infrastructure.PagedListOutput_TDetail_.md 'AlifeApi.BusinessRules.Infrastructure.PagedListOutput<TDetail>')[SmallCategoryListOutput](AlifeApi.BusinessRules.CategoryModels.SmallCategoryListOutput.md 'AlifeApi.BusinessRules.CategoryModels.SmallCategoryListOutput')[&gt;](AlifeApi.BusinessRules.Infrastructure.PagedListOutput_TDetail_.md 'AlifeApi.BusinessRules.Infrastructure.PagedListOutput<TDetail>')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')  
分頁的小分類列表

<a name='AlifeApi.BusinessRules.CategoryModels.SmallCategoryService.UpdateSmallCategoryAsync(long,AlifeApi.BusinessRules.CategoryModels.SmallCategoryInput)'></a>

## SmallCategoryService.UpdateSmallCategoryAsync(long, SmallCategoryInput) Method

更新小分類資訊

```csharp
public System.Threading.Tasks.Task UpdateSmallCategoryAsync(long smallCategoryId, AlifeApi.BusinessRules.CategoryModels.SmallCategoryInput input);
```
#### Parameters

<a name='AlifeApi.BusinessRules.CategoryModels.SmallCategoryService.UpdateSmallCategoryAsync(long,AlifeApi.BusinessRules.CategoryModels.SmallCategoryInput).smallCategoryId'></a>

`smallCategoryId` [System.Int64](https://docs.microsoft.com/en-us/dotnet/api/System.Int64 'System.Int64')

小分類ID

<a name='AlifeApi.BusinessRules.CategoryModels.SmallCategoryService.UpdateSmallCategoryAsync(long,AlifeApi.BusinessRules.CategoryModels.SmallCategoryInput).input'></a>

`input` [SmallCategoryInput](AlifeApi.BusinessRules.CategoryModels.SmallCategoryInput.md 'AlifeApi.BusinessRules.CategoryModels.SmallCategoryInput')

小分類更新輸入資料

#### Returns
[System.Threading.Tasks.Task](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task 'System.Threading.Tasks.Task')