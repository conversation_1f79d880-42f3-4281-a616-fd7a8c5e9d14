﻿using AlifeApi.BusinessRules.Infrastructure;
using AlifeApi.Common.DependencyInjection;
using AlifeApi.DataAccess.ProjectContent;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace AlifeApi.BusinessRules.ParkingSpaceModels
{
    /// <summary>
    /// 停車位服務
    /// </summary>
    public class ParkingSpaceService : ServiceBase<alifeContext>
    {
        public ParkingSpaceService(IServiceProvider serviceProvider, alifeContext dbContext) : base(serviceProvider, dbContext)
        {
        }

        /// <summary>
        /// 建立停車位
        /// </summary>
        /// <param name="input">停車位建立輸入資料</param>
        /// <returns>新建停車位的ID</returns>
        public async Task<int> CreateParkingSpaceAsync(ParkingSpaceCreateInput input)
        {
            ArgumentNullException.ThrowIfNull(input);

            // 驗證 FloorId 是否存在且 FloorType 為停車場
            var floor = await Db.Floors.FirstOrDefaultAsync(f => f.FloorId == input.FloorId);
            if (floor == null)
            {
                throw new Exception($"指定的樓層ID '{input.FloorId}' 不存在。");
            }
            
            // 驗證提供的 BuildingId 和 SiteCode 是否與 Floor 紀錄一致
            if (floor.BuildingId != input.BuildingId || floor.SiteCode != input.SiteCode)
            {
                throw new Exception("提供的 BuildingId 或 SiteCode 與指定 FloorId 的記錄不符。");
            }

            // 檢查同一樓層下是否已有相同車位編號
            if (await Db.ParkingSpaces.AnyAsync(p => p.FloorId == input.FloorId && p.SpaceNumber == input.SpaceNumber))
            {
                throw new Exception($"樓層 '{input.FloorId}' 下已存在相同車位編號 '{input.SpaceNumber}'。");
            }

            var parkingSpace = new ParkingSpace
            {
                SiteCode = input.SiteCode,
                BuildingId = input.BuildingId,
                FloorId = input.FloorId,
                SpaceNumber = input.SpaceNumber,
                SpaceType = input.SpaceType,
                Remarks = input.Remarks,
                ListPrice = input.ListPrice,
                MinimumPrice = input.MinimumPrice,
                Status = input.Status,
                CreatedTime = DateTime.Now,
                UpdatedTime = DateTime.Now,
                CreatedUserInfoId = CurrentUser.UserId,
                UpdatedUserInfoId = CurrentUser.UserId
            };

            Db.ParkingSpaces.Add(parkingSpace);
            await Db.SaveChangesAsync();

            return parkingSpace.ParkingSpaceId;
        }

        /// <summary>
        /// 取得指定樓層的停車位列表 (分頁)
        /// </summary>
        /// <param name="input">查詢條件 (必須包含 FloorId)</param>
        /// <returns>分頁的停車位列表</returns>
        public async Task<PagedListOutput<ParkingSpaceListOutput>> GetParkingSpaceListAsync(ParkingSpaceQueryInput input)
        {
            ArgumentNullException.ThrowIfNull(input);

            var query = Db.ParkingSpaces.AsQueryable();

            // 篩選 FloorId (如果提供)
            if (input.FloorId.HasValue)
            {
                query = query.Where(p => p.FloorId == input.FloorId.Value);
            }
            // 篩選 BuildingId (如果提供)
            if (input.BuildingId.HasValue)
            {
                 query = query.Where(p => p.BuildingId == input.BuildingId.Value);
            }
            // 篩選 SiteCode (如果提供)
            if (!string.IsNullOrEmpty(input.SiteCode))
            {
                 query = query.Where(p => p.SiteCode == input.SiteCode);
            }

            // 篩選 SpaceNumber
            if (!string.IsNullOrEmpty(input.SpaceNumber))
            {
                query = query.Where(p => p.SpaceNumber == input.SpaceNumber);
            }

            // 篩選 SpaceType
            if (!string.IsNullOrEmpty(input.SpaceType))
            {
                query = query.Where(p => p.SpaceType == input.SpaceType);
            }

            var projectedQuery = query
                .OrderBy(p => p.SiteCode).ThenBy(p => p.BuildingId).ThenBy(p => p.FloorId).ThenBy(p => p.SpaceNumber)
                .Select(p => new ParkingSpaceListOutput
                {
                    ParkingSpaceId = p.ParkingSpaceId,
                    SiteCode = p.SiteCode,
                    // SiteName, FloorLabel, BuildingName 稍後填充
                    BuildingId = p.BuildingId,
                    FloorId = p.FloorId,
                    SpaceNumber = p.SpaceNumber,
                    SpaceType = p.SpaceType,
                    Remarks = p.Remarks,
                    CreatedTime = p.CreatedTime,
                    // FloorLabel, BuildingName 稍後填充
                });

            var pagedResult = await projectedQuery.ToPagedListOutputAsync(input);

            if (pagedResult.Details.Any())
            {
                var siteCodes = pagedResult.Details.Select(p => p.SiteCode).Distinct().ToList();
                var floorIds = pagedResult.Details.Select(p => p.FloorId).Distinct().ToList();
                var buildingIds = pagedResult.Details.Select(p => p.BuildingId).Distinct().ToList();

                var floors = await Db.Floors
                                   .Where(f => floorIds.Contains(f.FloorId))
                                   .Select(f => new { f.FloorId, f.FloorLabel })
                                   .ToDictionaryAsync(f => f.FloorId, f => f.FloorLabel);

                var buildings = await Db.Buildings
                                    .Where(b => buildingIds.Contains(b.BuildingId))
                                    .Select(b => new { b.BuildingId, b.BuildingName })
                                    .ToDictionaryAsync(b => b.BuildingId, b => b.BuildingName);

                var siteNames = await Db.Sites
                                    .Where(s => siteCodes.Contains(s.SiteCode))
                                    .ToDictionaryAsync(s => s.SiteCode, s => s.SiteName);

                foreach (var psOutput in pagedResult.Details)
                {
                    if (siteNames.TryGetValue(psOutput.SiteCode, out var siteName))
                    {
                        psOutput.SiteName = siteName;
                    }
                    if (floors.TryGetValue(psOutput.FloorId, out var floorLabel))
                    {
                        psOutput.FloorLabel = floorLabel;
                    }
                    if (buildings.TryGetValue(psOutput.BuildingId, out var buildingName))
                    {
                        psOutput.BuildingName = buildingName;
                    }
                }
            }

            return pagedResult;
        }

        /// <summary>
        /// 根據ID取得停車位詳細資料
        /// </summary>
        /// <param name="parkingSpaceId">停車位ID</param>
        /// <returns>停車位詳細資料</returns>
        public async Task<ParkingSpaceOutput?> GetParkingSpaceByIdAsync(int parkingSpaceId)
        {
            var parkingSpace = await Db.ParkingSpaces
                .Where(p => p.ParkingSpaceId == parkingSpaceId)
                .Select(p => new ParkingSpaceOutput
                {
                    ParkingSpaceId = p.ParkingSpaceId,
                    SiteCode = p.SiteCode,
                    BuildingId = p.BuildingId,
                    FloorId = p.FloorId,
                    SpaceNumber = p.SpaceNumber,
                    SpaceType = p.SpaceType,
                    Remarks = p.Remarks,
                    ListPrice = p.ListPrice,
                    MinimumPrice = p.MinimumPrice,
                    TransactionPrice = p.TransactionPrice,
                    Status = p.Status,
                    CreatedTime = p.CreatedTime,
                    UpdatedTime = p.UpdatedTime,
                    CreatedUserInfoId = p.CreatedUserInfoId,
                    UpdatedUserInfoId = p.UpdatedUserInfoId
                    // FloorLabel, BuildingName, UserNames 稍後填充
                })
                .FirstOrDefaultAsync();

            if (parkingSpace == null)
            {
                return null;
            }

            var floor = await Db.Floors.Where(f => f.FloorId == parkingSpace.FloorId).Select(f => f.FloorLabel).FirstOrDefaultAsync();
            var building = await Db.Buildings.Where(b => b.BuildingId == parkingSpace.BuildingId).Select(b => b.BuildingName).FirstOrDefaultAsync();

            parkingSpace.FloorLabel = floor;
            parkingSpace.BuildingName = building;

            var userIds = new[] { parkingSpace.CreatedUserInfoId, parkingSpace.UpdatedUserInfoId }.Where(id => !string.IsNullOrEmpty(id)).Distinct().ToList();
             if (userIds.Any())
             {
                 var users = await Db.UserInfos
                     .Where(u => userIds.Contains(u.UserInfoId))
                     .Select(u => new { u.UserInfoId, u.Name })
                     .ToListAsync();
                 var userMap = users.ToDictionary(u => u.UserInfoId, u => u.Name);

                 if (userMap.TryGetValue(parkingSpace.CreatedUserInfoId, out var createdName))
                 {
                     parkingSpace.CreatedUserName = createdName;
                 }
                 if (!string.IsNullOrEmpty(parkingSpace.UpdatedUserInfoId) && userMap.TryGetValue(parkingSpace.UpdatedUserInfoId, out var updatedName))
                 {
                     parkingSpace.UpdatedUserName = updatedName;
                 }
             }

            return parkingSpace;
        }

        /// <summary>
        /// 更新停車位資訊
        /// </summary>
        /// <param name="parkingSpaceId">停車位ID</param>
        /// <param name="input">停車位更新輸入資料</param>
        public async Task UpdateParkingSpaceAsync(int parkingSpaceId, ParkingSpaceUpdateInput input)
        {
            ArgumentNullException.ThrowIfNull(input);

            var parkingSpace = await Db.ParkingSpaces.FindAsync(parkingSpaceId);

            if (parkingSpace == null)
            {
                throw new Exception($"找不到指定的停車位 (ID: {parkingSpaceId})");
            }

            parkingSpace.SpaceType = input.SpaceType;
            parkingSpace.Remarks = input.Remarks;
            parkingSpace.ListPrice = input.ListPrice;
            parkingSpace.MinimumPrice = input.MinimumPrice;
            parkingSpace.TransactionPrice = input.TransactionPrice;
            parkingSpace.Status = input.Status;
            parkingSpace.UpdatedTime = DateTime.Now;
            parkingSpace.UpdatedUserInfoId = CurrentUser.UserId;

            await Db.SaveChangesAsync();
        }

        /// <summary>
        /// 刪除停車位
        /// </summary>
        /// <param name="parkingSpaceId">停車位ID</param>
        public async Task DeleteParkingSpaceAsync(int parkingSpaceId)
        {
            var parkingSpace = await Db.ParkingSpaces.FindAsync(parkingSpaceId);

            if (parkingSpace == null)
            {
                return; // 允許刪除不存在的紀錄
            }

            // 檢查是否有訂單項目直接關聯到此車位
            bool isReferencedInOrder = await Db.PurchaseOrderItems
                .AnyAsync(item => item.ParkingSpaceId == parkingSpaceId);

            if (isReferencedInOrder)
            {
                // 如果您希望阻止刪除，可以拋出例外
                throw new Exception("此車位已關聯到訂單，無法刪除。");
            }
            
            Db.ParkingSpaces.Remove(parkingSpace);
            await Db.SaveChangesAsync();
        }

        /// <summary>
        /// 取得可用停車位列表 (用於銷售選擇)
        /// 註: 銷售狀態現在由Units表管理，此方法返回基本車位資訊
        /// </summary>
        /// <param name="input">查詢條件</param>
        /// <returns>可用停車位列表</returns>
        public async Task<PagedListOutput<ParkingSpaceListOutput>> GetAvailableParkingSpacesAsync(ParkingSpaceQueryInput input)
        {
            ArgumentNullException.ThrowIfNull(input);
            
            // 註: 此方法返回所有車位的基本資訊，銷售狀態需要從Units表查詢
            return await GetParkingSpaceListAsync(input);
        }

        /// <summary>
        /// 解析停車位ID字串為ID列表
        /// </summary>
        /// <param name="parkingSpaceIdsString">逗號分隔的停車位ID字串</param>
        /// <returns>停車位ID列表</returns>
        public static List<int> ParseParkingSpaceIds(string? parkingSpaceIdsString)
        {
            if (string.IsNullOrEmpty(parkingSpaceIdsString))
            {
                return new List<int>();
            }

            return parkingSpaceIdsString
                .Split(',', StringSplitOptions.RemoveEmptyEntries | StringSplitOptions.TrimEntries)
                .Where(id => int.TryParse(id, out _))
                .Select(int.Parse)
                .ToList();
        }

        /// <summary>
        /// 將停車位ID列表轉換為字串
        /// </summary>
        /// <param name="parkingSpaceIds">停車位ID列表</param>
        /// <returns>逗號分隔的停車位ID字串</returns>
        public static string? FormatParkingSpaceIds(List<int>? parkingSpaceIds)
        {
            if (parkingSpaceIds == null || !parkingSpaceIds.Any())
            {
                return null;
            }

            return string.Join(",", parkingSpaceIds);
        }
    }
} 
