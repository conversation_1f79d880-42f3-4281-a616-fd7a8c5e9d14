#### [AlifeApi.BusinessRules](index.md 'index')
### [AlifeApi.BusinessRules.ReviewModels](AlifeApi.BusinessRules.ReviewModels.md 'AlifeApi.BusinessRules.ReviewModels')

## ReviewTaskInput Class

審核流程輸入模型

```csharp
public class ReviewTaskInput
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; ReviewTaskInput
### Properties

<a name='AlifeApi.BusinessRules.ReviewModels.ReviewTaskInput.Description'></a>

## ReviewTaskInput.Description Property

流程描述

```csharp
public string Description { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.ReviewModels.ReviewTaskInput.Name'></a>

## ReviewTaskInput.Name Property

流程名稱

```csharp
public string Name { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.ReviewModels.ReviewTaskInput.Steps'></a>

## ReviewTaskInput.Steps Property

審核步驟清單

```csharp
public System.Collections.Generic.List<AlifeApi.BusinessRules.ReviewModels.ReviewStepInput> Steps { get; set; }
```

#### Property Value
[System.Collections.Generic.List&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Collections.Generic.List-1 'System.Collections.Generic.List`1')[ReviewStepInput](AlifeApi.BusinessRules.ReviewModels.ReviewStepInput.md 'AlifeApi.BusinessRules.ReviewModels.ReviewStepInput')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Collections.Generic.List-1 'System.Collections.Generic.List`1')