#### [AlifeApi.BusinessRules](index.md 'index')
### [AlifeApi.BusinessRules.PaymentRecordModels](AlifeApi.BusinessRules.PaymentRecordModels.md 'AlifeApi.BusinessRules.PaymentRecordModels')

## PaymentRecordQueryInput Class

收款記錄列表查詢輸入模型

```csharp
public class PaymentRecordQueryInput : AlifeApi.BusinessRules.Infrastructure.PagedListInput
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; [PagedListInput](AlifeApi.BusinessRules.Infrastructure.PagedListInput.md 'AlifeApi.BusinessRules.Infrastructure.PagedListInput') &#129106; PaymentRecordQueryInput
### Properties

<a name='AlifeApi.BusinessRules.PaymentRecordModels.PaymentRecordQueryInput.PaymentDateEnd'></a>

## PaymentRecordQueryInput.PaymentDateEnd Property

收款日期迄

```csharp
public System.Nullable<System.DateOnly> PaymentDateEnd { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.DateOnly](https://docs.microsoft.com/en-us/dotnet/api/System.DateOnly 'System.DateOnly')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

<a name='AlifeApi.BusinessRules.PaymentRecordModels.PaymentRecordQueryInput.PaymentDateStart'></a>

## PaymentRecordQueryInput.PaymentDateStart Property

收款日期起

```csharp
public System.Nullable<System.DateOnly> PaymentDateStart { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.DateOnly](https://docs.microsoft.com/en-us/dotnet/api/System.DateOnly 'System.DateOnly')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

<a name='AlifeApi.BusinessRules.PaymentRecordModels.PaymentRecordQueryInput.PaymentType'></a>

## PaymentRecordQueryInput.PaymentType Property

款別

```csharp
public string? PaymentType { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')