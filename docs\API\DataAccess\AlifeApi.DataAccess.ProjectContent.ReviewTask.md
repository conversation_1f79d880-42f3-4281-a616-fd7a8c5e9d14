#### [AlifeApi.DataAccess](index.md 'index')
### [AlifeApi.DataAccess.ProjectContent](AlifeApi.DataAccess.ProjectContent.md 'AlifeApi.DataAccess.ProjectContent')

## ReviewTask Class

審核任務表，用於儲存審核任務的基本資訊，與特定案場關聯。

```csharp
public class ReviewTask
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; ReviewTask
### Properties

<a name='AlifeApi.DataAccess.ProjectContent.ReviewTask.CreatedTime'></a>

## ReviewTask.CreatedTime Property

創建時間，記錄任務創建的時間，預設為當前時間。

```csharp
public System.Nullable<System.DateTime> CreatedTime { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.DateTime](https://docs.microsoft.com/en-us/dotnet/api/System.DateTime 'System.DateTime')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

<a name='AlifeApi.DataAccess.ProjectContent.ReviewTask.CreatedUserInfoId'></a>

## ReviewTask.CreatedUserInfoId Property

創建者編號，對應 UserInfo 表的 UserInfoId。

```csharp
public string CreatedUserInfoId { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContent.ReviewTask.Deadline'></a>

## ReviewTask.Deadline Property

截止日期，任務必須完成的時間。

```csharp
public System.Nullable<System.DateTime> Deadline { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.DateTime](https://docs.microsoft.com/en-us/dotnet/api/System.DateTime 'System.DateTime')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

<a name='AlifeApi.DataAccess.ProjectContent.ReviewTask.Description'></a>

## ReviewTask.Description Property

任務描述，提供任務的詳細資訊。

```csharp
public string Description { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContent.ReviewTask.SiteCode'></a>

## ReviewTask.SiteCode Property

案場號碼，與 Sites 表的 SiteCode 關聯，指定任務的案場。

```csharp
public string SiteCode { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContent.ReviewTask.Status'></a>

## ReviewTask.Status Property

任務狀態，enable（啟用）、disable（停用）、deleted(刪除)

```csharp
public string Status { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContent.ReviewTask.TaskId'></a>

## ReviewTask.TaskId Property

任務流水號，主鍵，自動遞增，用於唯一識別審核任務。

```csharp
public int TaskId { get; set; }
```

#### Property Value
[System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')

<a name='AlifeApi.DataAccess.ProjectContent.ReviewTask.Title'></a>

## ReviewTask.Title Property

任務標題，描述任務的主要內容。

```csharp
public string Title { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')