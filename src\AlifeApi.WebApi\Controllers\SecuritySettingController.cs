﻿using AlifeApi.BusinessRules.SecuritySettingModels;
using Microsoft.AspNetCore.Mvc;

namespace AlifeApi.WebApi.Controllers
{
    /// <summary>
    /// 安全性設定
    /// </summary>
    public class SecuritySettingController : AuthenticatedController
    {
        private readonly SecuritySettingService _securitySettingService;


        ///// <summary>
        ///// Initializes a new instance of the <see cref="SecuritySettingController"/> class.
        ///// </summary>
        ///// <param name="securitySettingService">The role group service.</param>
        ///// <exception cref="ArgumentNullException">roleGroupService</exception>
        //public SecuritySettingController(SecuritySettingService securitySettingService)
        //{
        //    _securitySettingService = securitySettingService ?? throw new ArgumentNullException(nameof(securitySettingService));
        //}

        ///// <summary>
        ///// 安全性設定更新
        ///// </summary>
        ///// <param name="input">The input.</param>
        ///// <returns>安全性設定</returns>
        //[HttpPost]
        //public async Task UpdateSecuritySettingAsync(List<SecuritySettingModel> input)
        //    => await _securitySettingService.UpdateSecuritySettingAsync(input);


        ///// <summary>
        ///// 取得所有安全設定
        ///// </summary>
        ///// <returns>系統版本號作業</returns>
        //[HttpGet]
        //public async Task<List<SecuritySettingModel>> GetSecuritySettingListAsync()
        //    => await _securitySettingService.GetSecuritySettingListAsync();

    }
}
