using AlifeApi.BusinessRules.Infrastructure;
using AlifeApi.DataAccess.ProjectContent;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace AlifeApi.BusinessRules.FloorModels
{
    /// <summary>
    /// 樓層服務
    /// </summary>
    public class FloorService : ServiceBase<alifeContext>
    {
        public FloorService(IServiceProvider serviceProvider, alifeContext dbContext)
            : base(serviceProvider, dbContext)
        {
        }

        /// <summary>
        /// 建立樓層
        /// </summary>
        /// <param name="input">樓層建立輸入資料</param>
        /// <returns>新建樓層的ID</returns>
        public async Task<int> CreateFloorAsync(FloorCreateInput input)
        {
            ArgumentNullException.ThrowIfNull(input);

            // 檢查 BuildingId 是否存在
            if (!await Db.Buildings.AnyAsync(b => b.BuildingId == input.BuildingId))
            {
                throw new Exception($"指定的建築物ID '{input.BuildingId}' 不存在。");
            }

            // 檢查同一棟樓、同一 SiteCode 下是否已有相同 FloorLabel 或 FloorLevel
            if (await Db.Floors.AnyAsync(f => f.BuildingId == input.BuildingId && f.SiteCode == input.SiteCode && (f.FloorLabel == input.FloorLabel || f.FloorLevel == input.FloorLevel)))
            {
                 throw new Exception($"建築物 '{input.BuildingId}' 下已存在相同樓層標示 '{input.FloorLabel}' 或樓層數值 '{input.FloorLevel}'。");
            }

            var floor = new Floor
            {
                BuildingId = input.BuildingId,
                SiteCode = input.SiteCode,
                FloorLabel = input.FloorLabel,
                FloorLevel = input.FloorLevel,
                FloorType = input.FloorType,
                FloorHeight = input.FloorHeight,
                Remarks = input.Remarks,
                CreatedTime = DateTime.Now,
                UpdatedTime = DateTime.Now,
                CreatedUserInfoId = CurrentUser.UserId,
                UpdatedUserInfoId = CurrentUser.UserId
            };

            Db.Floors.Add(floor);
            await Db.SaveChangesAsync();

            return floor.FloorId;
        }

        /// <summary>
        /// 取得指定建築物的樓層列表 (分頁)
        /// </summary>
        /// <param name="input">查詢條件 (必須包含 BuildingId)</param>
        /// <returns>分頁的樓層列表</returns>
        public async Task<PagedListOutput<FloorListOutput>> GetFloorListAsync(FloorQueryInput input)
        {
            ArgumentNullException.ThrowIfNull(input);

            var query = Db.Floors.AsQueryable(); // 先取得所有樓層

            // 如果提供了 BuildingId，則進行篩選
            if (input.BuildingId.HasValue)
            {
                query = query.Where(f => f.BuildingId == input.BuildingId.Value);
            }

            // 篩選 FloorType
            if (!string.IsNullOrEmpty(input.FloorType))
            {
                query = query.Where(f => f.FloorType == input.FloorType);
            }

            var pagedResult = await query
                .OrderBy(f => f.FloorLevel) // 通常樓層列表按樓層數值排序
                .Select(f => new FloorListOutput
                {
                    FloorId = f.FloorId,
                    BuildingId = f.BuildingId,
                    SiteCode = f.SiteCode,
                    // SiteName 會在稍後填充
                    FloorLabel = f.FloorLabel,
                    FloorLevel = f.FloorLevel,
                    FloorType = f.FloorType,
                    FloorHeight = f.FloorHeight,
                    CreatedTime = f.CreatedTime
                })
                .ToPagedListOutputAsync(input);

            if (pagedResult.Details.Any()) // 僅當有資料時才查詢 SiteName
            {
                var siteCodes = pagedResult.Details.Select(f => f.SiteCode).Distinct().ToList();
                var siteNames = await Db.Sites
                                         .Where(s => siteCodes.Contains(s.SiteCode))
                                         .ToDictionaryAsync(s => s.SiteCode, s => s.SiteName);

                foreach (var floorOutput in pagedResult.Details)
                {
                    if (siteNames.TryGetValue(floorOutput.SiteCode, out var name))
                    {
                        floorOutput.SiteName = name;
                    }
                }
            }

            return pagedResult;
        }

        /// <summary>
        /// 根據ID取得樓層詳細資料
        /// </summary>
        /// <param name="floorId">樓層ID</param>
        /// <returns>樓層詳細資料</returns>
        public async Task<FloorOutput?> GetFloorByIdAsync(int floorId)
        {
            var floor = await Db.Floors
                .Where(f => f.FloorId == floorId)
                .Select(f => new FloorOutput
                {
                    FloorId = f.FloorId,
                    BuildingId = f.BuildingId,
                    SiteCode = f.SiteCode,
                    FloorLabel = f.FloorLabel,
                    FloorLevel = f.FloorLevel,
                    FloorType = f.FloorType,
                    FloorHeight = f.FloorHeight,
                    Remarks = f.Remarks,
                    CreatedTime = f.CreatedTime,
                    UpdatedTime = f.UpdatedTime,
                    CreatedUserInfoId = f.CreatedUserInfoId,
                    UpdatedUserInfoId = f.UpdatedUserInfoId
                })
                .FirstOrDefaultAsync();

            if (floor == null)
            {
                return null;
            }

            // 查詢並填入建立者和更新者名稱 (如果需要)
            var userIds = new[] { floor.CreatedUserInfoId, floor.UpdatedUserInfoId }.Distinct().ToList();
            var users = await Db.UserInfos
                .Where(u => userIds.Contains(u.UserInfoId))
                .Select(u => new { u.UserInfoId, u.Name })
                .ToListAsync();
            var userMap = users.ToDictionary(u => u.UserInfoId, u => u.Name);

            if (userMap.TryGetValue(floor.CreatedUserInfoId, out var createdName))
            {
                floor.CreatedUserName = createdName;
            }
            if (userMap.TryGetValue(floor.UpdatedUserInfoId, out var updatedName))
            {
                floor.UpdatedUserName = updatedName;
            }

            return floor;
        }

        /// <summary>
        /// 更新樓層資訊
        /// </summary>
        /// <param name="floorId">樓層ID</param>
        /// <param name="input">樓層更新輸入資料</param>
        public async Task UpdateFloorAsync(int floorId, FloorUpdateInput input)
        {
            ArgumentNullException.ThrowIfNull(input);

            var floor = await Db.Floors.FindAsync(floorId);

            if (floor == null)
            {
                throw new Exception($"找不到指定的樓層 (ID: {floorId})");
            }

             // 檢查同一棟樓、同一 SiteCode 下是否已有其他樓層使用相同的 FloorLabel 或 FloorLevel
            if (await Db.Floors.AnyAsync(f => f.FloorId != floorId && f.BuildingId == floor.BuildingId && f.SiteCode == floor.SiteCode && (f.FloorLabel == input.FloorLabel || f.FloorLevel == input.FloorLevel)))
            {
                 throw new Exception($"建築物 '{floor.BuildingId}' 下已存在其他樓層使用相同樓層標示 '{input.FloorLabel}' 或樓層數值 '{input.FloorLevel}'。");
            }

            floor.FloorLabel = input.FloorLabel;
            floor.FloorLevel = input.FloorLevel;
            floor.FloorType = input.FloorType;
            floor.FloorHeight = input.FloorHeight;
            floor.Remarks = input.Remarks;
            floor.UpdatedTime = DateTime.Now;
            floor.UpdatedUserInfoId = CurrentUser.UserId;

            await Db.SaveChangesAsync();
        }

        /// <summary>
        /// 刪除樓層
        /// </summary>
        /// <param name="floorId">樓層ID</param>
        public async Task DeleteFloorAsync(int floorId)
        {
            var floor = await Db.Floors.FindAsync(floorId);

            if (floor == null)
            {
                return; // 允許刪除不存在的紀錄
            }

            // 檢查是否有相關連的房屋單位 (Units)
            bool hasRelatedUnits = await Db.Units.AnyAsync(u => u.FloorId == floorId);
            if (hasRelatedUnits)
            {
                throw new Exception($"無法刪除樓層 (ID: {floorId})，因為其下尚有關聯的房屋單位。請先移除相關單位。");
            }

            // 檢查是否有相關連的停車位 (ParkingSpaces)
            bool hasRelatedParkingSpaces = await Db.ParkingSpaces.AnyAsync(p => p.FloorId == floorId);
            if (hasRelatedParkingSpaces)
            {
                throw new Exception($"無法刪除樓層 (ID: {floorId})，因為其下尚有關聯的停車位。請先移除相關車位。");
            }

            Db.Floors.Remove(floor);
            await Db.SaveChangesAsync();
        }

        /// <summary>
        /// 根據建築物ID取得樓層下拉選單列表
        /// </summary>
        /// <param name="input">樓層下拉選單輸入資料 (包含 BuildingId)</param>
        /// <returns>樓層下拉選單列表</returns>
        public async Task<List<FloorDropdownOutput>> GetFloorDropdownListAsync(FloorDropdownInput input)
        {
            ArgumentNullException.ThrowIfNull(input);

            var floors = await Db.Floors
                .Where(f => f.BuildingId == input.BuildingId)
                .OrderBy(f => f.FloorLevel) // 通常按樓層數值排序
                .Select(f => new FloorDropdownOutput
                {
                    Name = f.FloorLabel, // 使用 FloorLabel 作為名稱
                    Value = f.FloorId      // 使用 FloorId 作為值
                })
                .ToListAsync();

            return floors;
        }
    }
} 
