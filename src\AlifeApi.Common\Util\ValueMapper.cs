﻿using System.Linq.Expressions;
using System.Reflection;

namespace AlifeApi.Common.Util
{
    /// <summary>
    /// 提供來源與目標物件之間的值映射
    /// </summary>
    /// <typeparam name="TSource">來源物件類型</typeparam>
    /// <typeparam name="TDesc">目標物件類型</typeparam>
    public class ValueMapper<TSource, TDesc>
    {
        private readonly TSource _sourceObject;
        private readonly TDesc _destObject;

        /// <summary>
        /// 初始化
        /// </summary>
        /// <param name="sourceObject">來源物件</param>
        /// <param name="destObject">目的物件</param>
        public ValueMapper(TSource sourceObject, TDesc destObject)
        {
            this._sourceObject = sourceObject ?? throw new ArgumentNullException(nameof(sourceObject));
            this._destObject = destObject ?? throw new ArgumentNullException(nameof(destObject));
        }

        /// <summary>
        /// 如果來源選擇器返回非空值，將其映射到目的物件的屬性或欄位
        /// </summary>
        /// <typeparam name="TProperty">屬性或欄位的類型</typeparam>
        /// <param name="sourceSelector">來源選擇器函式</param>
        /// <param name="destExpression">目的表達式</param>
        public void MapIfHasValue<TProperty>(
            Func<TSource, TProperty> sourceSelector,
            Expression<Func<TDesc, TProperty>> destExpression)
            where TProperty : class
        {
            TProperty propertyValue = sourceSelector(_sourceObject);
            if (propertyValue == null)
            {
                return;
            }
            MapIfHasValueInternal(destExpression, propertyValue);
        }

        /// <summary>
        /// 如果來源選擇器返回非空值，將其映射到目的物件的屬性或欄位
        /// </summary>
        /// <typeparam name="TProperty">屬性或欄位的類型</typeparam>
        /// <param name="sourceSelector">來源選擇器函式</param>
        /// <param name="destExpression">目的表達式</param>
        public void MapIfHasValue<TProperty>(
            Func<TSource, TProperty?> sourceSelector,
            Expression<Func<TDesc, TProperty>> destExpression)
            where TProperty : struct
        {
            TProperty? propertyValue = sourceSelector(_sourceObject);
            if (!propertyValue.HasValue)
            {
                return;
            }
            MapIfHasValueInternal(destExpression, propertyValue.Value);
        }

        /// <summary>
        /// 如果來源選擇器返回非空值，將其映射到目的物件的屬性或欄位
        /// </summary>
        /// <typeparam name="TProperty">屬性或欄位的類型</typeparam>
        /// <param name="sourceSelector">來源選擇器函式</param>
        /// <param name="destExpression">目的表達式</param>
        public void MapIfHasValue<TProperty>(
            Func<TSource, TProperty?> sourceSelector,
            Expression<Func<TDesc, TProperty?>> destExpression)
            where TProperty : struct
        {
            TProperty? propertyValue = sourceSelector(_sourceObject);
            if (!propertyValue.HasValue)
            {
                return;
            }
            MapIfHasValueInternal(destExpression, propertyValue);
        }

        /// <summary>
        /// 如果來源選擇器返回非空值，將其映射到目的物件的屬性或欄位
        /// </summary>
        /// <typeparam name="TSourceProperty">來源屬性或欄位的類型</typeparam>
        /// <typeparam name="TDescProperty">目的屬性或欄位的類型</typeparam>
        /// <param name="sourceSelector">來源選擇器函式</param>
        /// <param name="destExpression">目的表達式</param>
        /// <param name="converter">可選的轉換器函式</param>
        public void MapIfHasValue<TSourceProperty, TDescProperty>(
            Func<TSource, TSourceProperty> sourceSelector,
            Expression<Func<TDesc, TDescProperty>> destExpression,
            Func<TSourceProperty, TDescProperty> converter)
        {
            TSourceProperty sourcePropValue = sourceSelector(_sourceObject);
            if (sourcePropValue is null)
            {
                return;
            }

            MapIfHasValueInternal(destExpression, converter(sourcePropValue));
        }

        private void MapIfHasValueInternal<TDescProperty>(
            Expression<Func<TDesc, TDescProperty>> destExpression, TDescProperty descValue)
        {
            if (descValue == null)
            {
                return;
            }

            MemberInfo destMember = ((MemberExpression)destExpression.Body).Member;
            IEnumerable<string> paths = destExpression.Body.ToString().Split('.');

            object targetObject = _destObject;

            if (paths.Count() > 2)
            {
                paths = paths.Skip(1);

                foreach (string path in paths)
                {
                    BindingFlags flags = BindingFlags.Public | BindingFlags.Instance;
                    PropertyInfo propInfo = targetObject.GetType().GetProperty(path, flags);
                    if (propInfo != null)
                    {
                        targetObject = propInfo.GetValue(targetObject, null);
                        continue;
                    }

                    FieldInfo fieldInfo = targetObject.GetType().GetField(path, flags);
                    if (fieldInfo != null)
                    {
                        targetObject = fieldInfo.GetValue(targetObject);
                        continue;
                    }
                }
            }

            if (destMember is PropertyInfo destProperty)
            {
                destProperty.SetValue(targetObject, descValue);
            }
            else if (destMember is FieldInfo destField)
            {
                destField.SetValue(targetObject, descValue);
            }
        }
    }
}
