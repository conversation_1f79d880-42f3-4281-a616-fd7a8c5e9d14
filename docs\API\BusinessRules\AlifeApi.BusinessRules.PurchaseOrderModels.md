#### [AlifeApi.BusinessRules](index.md 'index')

## AlifeApi.BusinessRules.PurchaseOrderModels Namespace

| Classes | |
| :--- | :--- |
| [PurchaseOrderCreateInput](AlifeApi.BusinessRules.PurchaseOrderModels.PurchaseOrderCreateInput.md 'AlifeApi.BusinessRules.PurchaseOrderModels.PurchaseOrderCreateInput') | 買賣預定單建立輸入模型 |
| [PurchaseOrderListOutput](AlifeApi.BusinessRules.PurchaseOrderModels.PurchaseOrderListOutput.md 'AlifeApi.BusinessRules.PurchaseOrderModels.PurchaseOrderListOutput') | 買賣預定單列表輸出項目 |
| [PurchaseOrderOutput](AlifeApi.BusinessRules.PurchaseOrderModels.PurchaseOrderOutput.md 'AlifeApi.BusinessRules.PurchaseOrderModels.PurchaseOrderOutput') | 買賣預定單詳細輸出模型 |
| [PurchaseOrderQueryInput](AlifeApi.BusinessRules.PurchaseOrderModels.PurchaseOrderQueryInput.md 'AlifeApi.BusinessRules.PurchaseOrderModels.PurchaseOrderQueryInput') | 買賣預定單列表查詢輸入模型 |
| [PurchaseOrderService](AlifeApi.BusinessRules.PurchaseOrderModels.PurchaseOrderService.md 'AlifeApi.BusinessRules.PurchaseOrderModels.PurchaseOrderService') | 買賣預定單服務 |
| [PurchaseOrderUpdateInput](AlifeApi.BusinessRules.PurchaseOrderModels.PurchaseOrderUpdateInput.md 'AlifeApi.BusinessRules.PurchaseOrderModels.PurchaseOrderUpdateInput') | 買賣預定單更新輸入模型 |
