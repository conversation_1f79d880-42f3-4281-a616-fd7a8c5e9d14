﻿using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;

namespace AlifeApi.BusinessRules.ProblemReportModels
{
    public class ReportReplyInput
    {
        /// <summary>
        /// 問題回報處理唯一識別值
        /// </summary>
        [Required]
        [JsonPropertyName("PR_Id")]
        public long Id { get; set; }

        /// <summary>
        /// 類型
        /// </summary>
        [Required, MaxLength(10)]
        public string ProblemType { get; set; }

        /// <summary>
        /// 處理狀態
        /// </summary>
        [Required, MaxLength(10)]
        public string ProcessStatus { get; set; }

        /// <summary>
        /// 回覆問題內容
        /// </summary>
        [MaxLength(200)]
        public string ReplyContent { get; set; }

        /// <summary>
        /// 回覆人員
        /// </summary>
        [MaxLength(15)]
        public string ReplyUserId { get; set; }

        /// <summary>
        /// 處理時間
        /// </summary>
        [JsonPropertyName("ReplyDate")]
        public DateTime? ReplyTime { get; set; }
    }
}
