#### [AlifeApi.BusinessRules](index.md 'index')
### [AlifeApi.BusinessRules.LogModels](AlifeApi.BusinessRules.LogModels.md 'AlifeApi.BusinessRules.LogModels')

## ApiLogCreateInput Class

The log input.

```csharp
public class ApiLogCreateInput
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; ApiLogCreateInput
### Properties

<a name='AlifeApi.BusinessRules.LogModels.ApiLogCreateInput.ActionName'></a>

## ApiLogCreateInput.ActionName Property

Gets or sets the name of the action.

```csharp
public string ActionName { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.LogModels.ApiLogCreateInput.ControllerName'></a>

## ApiLogCreateInput.ControllerName Property

Gets or sets the name of the controller.

```csharp
public string ControllerName { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.LogModels.ApiLogCreateInput.EndTime'></a>

## ApiLogCreateInput.EndTime Property

```csharp
public System.DateTime EndTime { get; set; }
```

#### Property Value
[System.DateTime](https://docs.microsoft.com/en-us/dotnet/api/System.DateTime 'System.DateTime')

<a name='AlifeApi.BusinessRules.LogModels.ApiLogCreateInput.Exception'></a>

## ApiLogCreateInput.Exception Property

Gets or sets the exception.

```csharp
public string Exception { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.LogModels.ApiLogCreateInput.Headers'></a>

## ApiLogCreateInput.Headers Property

Gets or sets the header.

```csharp
public string Headers { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.LogModels.ApiLogCreateInput.Input'></a>

## ApiLogCreateInput.Input Property

Gets or sets the input.

```csharp
public string Input { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.LogModels.ApiLogCreateInput.Output'></a>

## ApiLogCreateInput.Output Property

Gets or sets the output.

```csharp
public string Output { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.LogModels.ApiLogCreateInput.Seconds'></a>

## ApiLogCreateInput.Seconds Property

Gets the second.

```csharp
public decimal Seconds { get; }
```

#### Property Value
[System.Decimal](https://docs.microsoft.com/en-us/dotnet/api/System.Decimal 'System.Decimal')

<a name='AlifeApi.BusinessRules.LogModels.ApiLogCreateInput.SessionId'></a>

## ApiLogCreateInput.SessionId Property

Gets or sets the session identifier.

```csharp
public string SessionId { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.LogModels.ApiLogCreateInput.Source'></a>

## ApiLogCreateInput.Source Property

Gets or sets the source.

```csharp
public string Source { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.LogModels.ApiLogCreateInput.StartTime'></a>

## ApiLogCreateInput.StartTime Property

Gets or sets the start time.

```csharp
public System.DateTime StartTime { get; set; }
```

#### Property Value
[System.DateTime](https://docs.microsoft.com/en-us/dotnet/api/System.DateTime 'System.DateTime')

<a name='AlifeApi.BusinessRules.LogModels.ApiLogCreateInput.SystemName'></a>

## ApiLogCreateInput.SystemName Property

Gets or sets the name of the system.

```csharp
public string SystemName { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')