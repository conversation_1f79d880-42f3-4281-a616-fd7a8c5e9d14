#### [AlifeApi.DataAccess](index.md 'index')
### [AlifeApi.DataAccess.ProjectContent](AlifeApi.DataAccess.ProjectContent.md 'AlifeApi.DataAccess.ProjectContent')

## SysRoleGroup Class

角色群組表，用於儲存系統中的角色群組資訊，支援與特定案場關聯。

```csharp
public class SysRoleGroup
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; SysRoleGroup
### Properties

<a name='AlifeApi.DataAccess.ProjectContent.SysRoleGroup.CreatedTime'></a>

## SysRoleGroup.CreatedTime Property

創建時間，記錄角色群組創建的時間。

```csharp
public System.DateTime CreatedTime { get; set; }
```

#### Property Value
[System.DateTime](https://docs.microsoft.com/en-us/dotnet/api/System.DateTime 'System.DateTime')

<a name='AlifeApi.DataAccess.ProjectContent.SysRoleGroup.CreatedUserInfoId'></a>

## SysRoleGroup.CreatedUserInfoId Property

建立者，記錄創建此角色群組的員工編號。

```csharp
public string CreatedUserInfoId { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContent.SysRoleGroup.IsAdmin'></a>

## SysRoleGroup.IsAdmin Property

是否為管理者角色，true 表示是，false 表示否。

```csharp
public bool IsAdmin { get; set; }
```

#### Property Value
[System.Boolean](https://docs.microsoft.com/en-us/dotnet/api/System.Boolean 'System.Boolean')

<a name='AlifeApi.DataAccess.ProjectContent.SysRoleGroup.Name'></a>

## SysRoleGroup.Name Property

角色群組名稱。

```csharp
public string Name { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContent.SysRoleGroup.RoleGroupId'></a>

## SysRoleGroup.RoleGroupId Property

角色群組識別編號，主鍵的一部分，用於唯一識別角色群組。

```csharp
public string RoleGroupId { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContent.SysRoleGroup.SiteCode'></a>

## SysRoleGroup.SiteCode Property

案場號碼，可為 NULL 表示全局角色，對應 Sites 表的 SiteCode。

```csharp
public string SiteCode { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContent.SysRoleGroup.System'></a>

## SysRoleGroup.System Property

系統名稱，主鍵的一部分，用於區分不同系統。

```csharp
public string System { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContent.SysRoleGroup.UpdatedTime'></a>

## SysRoleGroup.UpdatedTime Property

更新時間，記錄角色群組最後更新的時間。

```csharp
public System.DateTime UpdatedTime { get; set; }
```

#### Property Value
[System.DateTime](https://docs.microsoft.com/en-us/dotnet/api/System.DateTime 'System.DateTime')

<a name='AlifeApi.DataAccess.ProjectContent.SysRoleGroup.UpdatedUserInfoId'></a>

## SysRoleGroup.UpdatedUserInfoId Property

更新者，記錄最後更新此角色群組的員工編號。

```csharp
public string UpdatedUserInfoId { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')