﻿using AlifeApi.BusinessRules.Infrastructure;
using AlifeApi.BusinessRules.ProjectVersionModels;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AlifeApi.BusinessRules.BulletinsModels
{
    public class GetBulletinsResult
    {
        /// <summary>
        /// 公告唯一識別值
        /// </summary>
        public int BL_Id { get; set; }

        /// <summary>
        /// 公告標題
        /// </summary>
        public string Title { get; set; }
        /// <summary>
        /// 公告內文
        /// </summary>
        public string Content { get; set; }
        /// <summary>
        /// 公告起始日期
        /// </summary>
        public DateTime? PostFrom { get; set; }
        /// <summary>
        /// 公告結束日期
        /// </summary>
        public DateTime? PostTo { get; set; }
        /// <summary>
        /// 是否至頂
        /// </summary>
        public bool IsTop { get; set; }

        /// <summary>
        /// 建立者帳號
        /// </summary>
        public string CreateUserId { get; set; }

        /// <summary>
        /// 建立時間
        /// </summary>
        public DateTime? CreateDate { get; set; }
        /// <summary>
        /// 更新者帳號
        /// </summary>          
        public string UpdateUserId { get; set; }

        /// <summary>
        /// 更新時間
        /// </summary>
        public DateTime? UpdateDate { get; set; }
        /// <summary>
        /// 公告附檔清單
        /// </summary>
        public List<AttachListItem> AttachList { get; set; } = new List<AttachListItem>();

        /// <summary>
        /// 瀏覽次數
        /// </summary>
        public int ViewCount { get; set; }

        /// <summary>
        /// 是否看過
        /// </summary>
        public bool IsRead { get; set; }
    }
}
