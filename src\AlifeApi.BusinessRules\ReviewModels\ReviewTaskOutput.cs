﻿using System;

namespace AlifeApi.BusinessRules.ReviewModels
{
    /// <summary>
    /// 審核流程輸出資料
    /// </summary>
    public class ReviewTaskOutput
    {
        /// <summary>
        /// 審核流程ID
        /// </summary>
        public int TaskId { get; set; }

        /// <summary>
        /// 流程名稱
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// 描述
        /// </summary>
        public string Description { get; set; }

        /// <summary>
        /// 步驟總數
        /// </summary>
        public int TotalStep { get; set; }


        /// <summary>
        /// 創建時間
        /// </summary>
        public DateTime CreatedTime { get; set; }

        /// <summary>
        /// 創建者ID
        /// </summary>
        public string CreatedUserInfoId { get; set; }

        /// <summary>
        /// 更新時間
        /// </summary>
        public DateTime UpdatedTime { get; set; }

        /// <summary>
        /// 更新者ID
        /// </summary>
        public string UpdatedUserInfoId { get; set; }

        /// <summary>
        /// 狀態
        /// </summary>
        public string Status { get; set; }
    }

    /// <summary>
    /// 審核流程詳細資料輸出模型
    /// </summary>
    public class ReviewTaskDetailOutput : ReviewTaskOutput
    {
        /// <summary>
        /// 截止日期
        /// </summary>
        public DateTime? Deadline { get; set; }

        /// <summary>
        /// 審核步驟清單
        /// </summary>
        public List<ReviewStepInput> Steps { get; set; }
    }

    /// <summary>
    /// 審核步驟輸出模型
    /// </summary>
    public class ReviewStepOutput
    {
        /// <summary>
        /// 步驟ID
        /// </summary>
        public int StepId { get; set; }

        /// <summary>
        /// 步驟名稱
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// 時限（小時）
        /// </summary>
        public int TimeLimit { get; set; }

        /// <summary>
        /// 步驟順序
        /// </summary>
        public int StepOrder { get; set; }

        /// <summary>
        /// 審核人員清單
        /// </summary>
        public List<ReviewApproverOutput> Approvers { get; set; }

        /// <summary>
        /// 通知方式清單
        /// </summary>
        public List<string> Notifications { get; set; }
    }

    /// <summary>
    /// 審核人員輸出模型
    /// </summary>
    public class ReviewApproverOutput
    {
        /// <summary>
        /// 審核人員ID
        /// </summary>
        public int ApproverId { get; set; }

        /// <summary>
        /// 使用者ID
        /// </summary>
        public string UserInfoId { get; set; }
    }
} 
