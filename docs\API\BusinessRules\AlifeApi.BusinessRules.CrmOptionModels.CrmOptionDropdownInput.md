#### [AlifeApi.BusinessRules](index.md 'index')
### [AlifeApi.BusinessRules.CrmOptionModels](AlifeApi.BusinessRules.CrmOptionModels.md 'AlifeApi.BusinessRules.CrmOptionModels')

## CrmOptionDropdownInput Class

CRM選項下拉選單查詢輸入模型

```csharp
public class CrmOptionDropdownInput
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; CrmOptionDropdownInput
### Properties

<a name='AlifeApi.BusinessRules.CrmOptionModels.CrmOptionDropdownInput.CrmOptionTypeId'></a>

## CrmOptionDropdownInput.CrmOptionTypeId Property

CRM選項類型ID (必填)

```csharp
public long CrmOptionTypeId { get; set; }
```

#### Property Value
[System.Int64](https://docs.microsoft.com/en-us/dotnet/api/System.Int64 'System.Int64')

<a name='AlifeApi.BusinessRules.CrmOptionModels.CrmOptionDropdownInput.OnlyActive'></a>

## CrmOptionDropdownInput.OnlyActive Property

是否只取得啟用的選項 (預設為true)

```csharp
public bool OnlyActive { get; set; }
```

#### Property Value
[System.Boolean](https://docs.microsoft.com/en-us/dotnet/api/System.Boolean 'System.Boolean')

<a name='AlifeApi.BusinessRules.CrmOptionModels.CrmOptionDropdownInput.SiteCode'></a>

## CrmOptionDropdownInput.SiteCode Property

案場代碼 (必填)

```csharp
public string SiteCode { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')