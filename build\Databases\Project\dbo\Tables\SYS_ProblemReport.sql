﻿CREATE TABLE [dbo].[SYS_ProblemReport] (
    [Id]             BIGINT         IDENTITY (1, 1) NOT NULL,
    [DeptId]         VARCHAR (20)   NOT NULL,
    [System]         VARCHAR (30)   NOT NULL,
    [Menu]           NVARCHAR (50)  NOT NULL,
    [Subject]        NVARCHAR (200) NOT NULL,
    [ProblemContent] NVARCHAR (200) NOT NULL,
    [ProcessStatus]  VARCHAR (10)   NOT NULL,
    [ProblemType]    VARCHAR (10)   NOT NULL,
    [ReplyContent]   NVARCHAR (200) NOT NULL,
    [ReplyUserId]    VARCHAR (15)   NULL,
    [ReplyTime]      DATETIME       NULL,
    [CreatedUserId]  VARCHAR (15)   NOT NULL,
    [CreatedTime]    DATETIME       NOT NULL,
    [UpdatedUserId]  VARCHAR (15)   NOT NULL,
    [UpdatedTime]    DATETIME       NOT NULL,
    CONSTRAINT [PK_SYS_ProblemReport] PRIMARY KEY CLUSTERED ([Id] ASC),
    CONSTRAINT [FK_SYS_ProblemReport_UserDept] FOREIGN KEY ([DeptId]) REFERENCES [dbo].[UserDept] ([Id]),
    CONSTRAINT [FK_SYS_ProblemReport_UserInfo_Reply] FOREIGN KEY ([ReplyUserId]) REFERENCES [dbo].[UserInfo] ([Id])
);




GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'平台問題回報資料', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'SYS_ProblemReport';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'流水號', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'SYS_ProblemReport', @level2type = N'COLUMN', @level2name = N'Id';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'單位代碼', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'SYS_ProblemReport', @level2type = N'COLUMN', @level2name = N'DeptId';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'功能頁籤', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'SYS_ProblemReport', @level2type = N'COLUMN', @level2name = N'Menu';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'問題主旨', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'SYS_ProblemReport', @level2type = N'COLUMN', @level2name = N'Subject';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'問題內容', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'SYS_ProblemReport', @level2type = N'COLUMN', @level2name = N'ProblemContent';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'處理狀態', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'SYS_ProblemReport', @level2type = N'COLUMN', @level2name = N'ProcessStatus';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'問題類型', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'SYS_ProblemReport', @level2type = N'COLUMN', @level2name = N'ProblemType';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'處理回報', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'SYS_ProblemReport', @level2type = N'COLUMN', @level2name = N'ReplyContent';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'處理人員', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'SYS_ProblemReport', @level2type = N'COLUMN', @level2name = N'ReplyUserId';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'建立者', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'SYS_ProblemReport', @level2type = N'COLUMN', @level2name = N'CreatedUserId';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'建立時間', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'SYS_ProblemReport', @level2type = N'COLUMN', @level2name = N'CreatedTime';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'更新者', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'SYS_ProblemReport', @level2type = N'COLUMN', @level2name = N'UpdatedUserId';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'更新時間', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'SYS_ProblemReport', @level2type = N'COLUMN', @level2name = N'UpdatedTime';

