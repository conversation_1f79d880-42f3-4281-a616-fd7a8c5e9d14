using System.ComponentModel.DataAnnotations;

namespace AlifeApi.BusinessRules.CrmOptionModels
{
    /// <summary>
    /// CRM選項更新輸入模型
    /// </summary>
    public class CrmOptionUpdateInput
    {
        /// <summary>
        /// 選項值
        /// </summary>
        [Required(ErrorMessage = "缺少選項值")]
        [MaxLength(200, ErrorMessage = "選項值長度不能超過200字元")]
        public string OptionValue { get; set; } = null!;

        /// <summary>
        /// 排序順序
        /// </summary>
        [Range(0, int.MaxValue, ErrorMessage = "排序順序必須為非負整數")]
        public int SortOrder { get; set; }

        /// <summary>
        /// 是否啟用
        /// </summary>
        public bool IsActive { get; set; } = true;
    }
}
