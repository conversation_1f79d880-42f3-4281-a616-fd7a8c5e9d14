﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using AlifeApi.BusinessRules.Infrastructure;
using AlifeApi.BusinessRules.RoleGroupModels;
using AlifeApi.BusinessRules.SecuritySettingModels;
using AlifeApi.BusinessRules.UserInfoModels;
using AlifeApi.DataAccess.ProjectContent;
using Microsoft.EntityFrameworkCore;

namespace AlifeApi.BusinessRules.LoginModels
{
    /// <summary>
    /// 處理使用者登入邏輯的服務類別
    /// </summary>
    public class LoginServicePg : ServiceBase<alifeContext>
    {
        private readonly PasswordManager _passwordManager;
        private readonly RoleGroupServicePg _roleGroupService;
        public LoginServicePg(IServiceProvider serviceProvider, alifeContext dbContext, PasswordManager passwordManager, RoleGroupServicePg roleGroupService) : base(serviceProvider, dbContext)
        {
            _passwordManager = passwordManager ?? throw new ArgumentNullException(nameof(passwordManager));
            _roleGroupService = roleGroupService ?? throw new ArgumentNullException(nameof(roleGroupService));
        }

        /// <summary>
        /// 使用者登入處理
        /// </summary>
        /// <param name="input">登入輸入資料</param>
        /// <returns>登入結果</returns>
        public async Task<UserLoginPgOutput> UserLoginAsync(UserLoginInput input)
        {
            // 獲取系統安全設定
            var SecuritySetting = await Db.SysSystemSettings.AsQueryable()
                .Where(x => x.Type == "SecuritySetting" && x.IsEnabled == true)
                .Select(x => new SecuritySettingModel()
                {
                    Key = x.Key,
                    Value = x.Value,
                    Name = x.Name,
                    Enabled = (bool)x.IsEnabled
                }).ToListAsync();

            var output = new UserLoginPgOutput();

            // 獲取使用者基本資訊
            var user = await Db.UserInfos
                .SingleOrDefaultAsync(x => x.UserInfoId == input.UserInfoId);

            if (user is null)
            {
                output.Response = Message.AccountFail;
                return output;
            }
            else if (!user.Status)
            {
                output.Response = Message.CheckAccountIsValid;
                return output;
            }

            // 檢查登入失敗次數是否超過限制
            if (user.LoginFailedCount >= Convert.ToInt32(SecuritySetting.FirstOrDefault(x => x.Key == "LoginFailedCount")?.Value ?? int.MaxValue.ToString())
                && (DateTime.Now - user.LastLoginTime) < TimeSpan.FromMinutes(Convert.ToInt32(SecuritySetting.FirstOrDefault(x => x.Key == "LockAccountTime")?.Value ?? TimeSpan.MinValue.TotalMinutes.ToString())))
            {
                output.Response = Message.AccountLoginFailTooManyTimeNeedToWait;
                return output;
            }

            // 更新最後登入資訊
            user.LastLoginIp = CurrentUser.IPAddress;
            user.LastLoginTime = DateTime.Now;

            // 驗證密碼
            if (!_passwordManager.VerifyHashedPassword(user.Password, input.UserPw))
            {
                user.LoginFailedCount = (short)(user.LoginFailedCount.GetValueOrDefault() + 1);
                await Db.SaveChangesAsync();

                output.Response = Message.PasswordFail;
                return output;
            }

            // 檢查是否為預設密碼
            user.LoginFailedCount = 0;
            if (user.Password == await _passwordManager.GetDefaultPasswordAsync())
            {
                output.Response = Message.DefaultPassword;
                return output;
            }

            // 檢查密碼是否需要更改
            var userHistory = await Db.UserPasswordHistories
                .OrderByDescending(x => x.UserPasswordHistoryId)
                .FirstOrDefaultAsync(x => x.UserInfoId == input.UserInfoId);

            if (userHistory != null && (DateTime.Now - userHistory.CreatedTime) >= TimeSpan.FromDays(Convert.ToInt32(SecuritySetting.FirstOrDefault(x => x.Key == "ChangCycleDays")?.Value ?? TimeSpan.MaxValue.TotalDays.ToString())))
            {
                output.Response = Message.PasswordChange;
                return output;
            }

            // 重置登入失敗次數
            user.LoginFailedCount = 0;
            await Db.SaveChangesAsync();

            // 獲取使用者角色和權限
            var roleGroupUsers = await Db.SysRoleGroupUsers
                .Where(x => x.UserInfoId == user.UserInfoId)
                .ToListAsync();

            var roleGroupIds = roleGroupUsers.Select(x => x.RoleGroupId).ToList();

            var roleGroups = await Db.SysRoleGroups
                .Where(x => roleGroupIds.Contains(x.RoleGroupId))
                .ToListAsync();

            var rolePermissions = await Db.SysRoleGroupPermissions
                .Where(x => roleGroupIds.Contains(x.RoleGroupId))
                .ToListAsync();

            // 獲取使用者部門
            var userDepts = await Db.UserDepartments
                .Where(x => x.UserInfoId == user.UserInfoId)
                .ToListAsync();

            var deptIds = userDepts.Select(x => x.DepartmentId).ToList();

            var departments = await Db.Departments
                .Where(x => deptIds.Contains(x.DepartmentId))
                .ToListAsync();

            // 設定輸出資訊
            output.UserInfoId = user.UserInfoId;
            output.UserPassword = user.Password;
            output.UserName = user.Name;
            output.Email = user.Email;
            output.IP = CurrentUser.IPAddress;
            output.Response = Message.Success;

            // 設定新增的使用者資訊
            output.CompanyId = user.CompanyId;
            output.Gender = user.Gender;
            output.BirthDate = user.BirthDate.HasValue ? DateTime.Parse(user.BirthDate.Value.ToString("yyyy-MM-dd")) : null;
            output.TelephoneNumber = user.TelephoneNumber;
            output.MobileNumber = user.MobileNumber;
            output.RegisteredAddress = user.RegisteredAddress;
            output.MailingAddress = user.MailingAddress;
            output.EmergencyContactName = user.EmergencyContactName;
            output.EmergencyContactPhone = user.EmergencyContactPhone;
            output.EmergencyContactRelation = user.EmergencyContactRelation;
            output.ServiceUnit = user.ServiceUnit;
            output.HireDate = user.HireDate.HasValue ? DateTime.Parse(user.HireDate.Value.ToString("yyyy-MM-dd")) : null;
            output.Status = user.Status;
            output.LastLogoutTime = user.LastLogoutTime;
            output.LoginFailedCount = user.LoginFailedCount;
            output.IsInside = user.IsInside;
            output.IsM365 = user.IsM365;
            output.IsEmailNotificationEnabled = user.IsEmailNotificationEnabled;

            // 獲取選單樹狀結構
            var roleFuncIds = rolePermissions.Select(x => x.FuncId).Distinct();
            if (roleFuncIds.Any())
            {
                output.MenuTrees = await _roleGroupService.GetLoginMenuTreesAsync(roleFuncIds);
            }

            return output;
        }
    }
}
