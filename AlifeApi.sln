﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.9.34622.214
MinimumVisualStudioVersion = 10.0.40219.1
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "SolutionItems", "SolutionItems", "{349B5CDB-9ED0-4579-8F6E-F6771F91186B}"
	ProjectSection(SolutionItems) = preProject
		.editorconfig = .editorconfig
		README.md = README.md
	EndProjectSection
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "src", "src", "{707728B9-9E4A-4962-AE05-AE0E57759D3D}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "AlifeApi.DataAccess", "src\AlifeApi.DataAccess\AlifeApi.DataAccess.csproj", "{9B942761-2CAA-40B2-9824-483E58758C15}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "AlifeApi.BusinessRules", "src\AlifeApi.BusinessRules\AlifeApi.BusinessRules.csproj", "{CBA827F0-D102-4BBE-8811-E3B7DEAF8118}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "AlifeApi.Common", "src\AlifeApi.Common\AlifeApi.Common.csproj", "{48D3820C-AFC8-458B-BAF6-55C0A7508BAC}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "AlifeApi.WebApi", "src\AlifeApi.WebApi\AlifeApi.WebApi.csproj", "{9745F198-E630-4F03-BB24-71A58D76904B}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "build", "build", "{12C8BBEB-D95F-431D-A619-9EAD388E619C}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Databases", "Databases", "{C4878C94-FD6B-4D48-84DF-4F733233BAB3}"
EndProject
Project("{00D1A9C2-B5F0-4AF3-8072-F6C62B433612}") = "Project", "build\Databases\Project\Project.sqlproj", "{A8AF5A51-6ECE-4314-A99D-F29E22A5ACFA}"
EndProject
Project("{00D1A9C2-B5F0-4AF3-8072-F6C62B433612}") = "ApiLog", "build\Databases\ApiLog\ApiLog.sqlproj", "{B48B937B-0F32-401B-A6AD-162029A7D370}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{9B942761-2CAA-40B2-9824-483E58758C15}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{9B942761-2CAA-40B2-9824-483E58758C15}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{9B942761-2CAA-40B2-9824-483E58758C15}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{9B942761-2CAA-40B2-9824-483E58758C15}.Release|Any CPU.Build.0 = Release|Any CPU
		{CBA827F0-D102-4BBE-8811-E3B7DEAF8118}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{CBA827F0-D102-4BBE-8811-E3B7DEAF8118}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{CBA827F0-D102-4BBE-8811-E3B7DEAF8118}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{CBA827F0-D102-4BBE-8811-E3B7DEAF8118}.Release|Any CPU.Build.0 = Release|Any CPU
		{48D3820C-AFC8-458B-BAF6-55C0A7508BAC}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{48D3820C-AFC8-458B-BAF6-55C0A7508BAC}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{48D3820C-AFC8-458B-BAF6-55C0A7508BAC}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{48D3820C-AFC8-458B-BAF6-55C0A7508BAC}.Release|Any CPU.Build.0 = Release|Any CPU
		{9745F198-E630-4F03-BB24-71A58D76904B}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{9745F198-E630-4F03-BB24-71A58D76904B}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{9745F198-E630-4F03-BB24-71A58D76904B}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{9745F198-E630-4F03-BB24-71A58D76904B}.Release|Any CPU.Build.0 = Release|Any CPU
		{A8AF5A51-6ECE-4314-A99D-F29E22A5ACFA}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{A8AF5A51-6ECE-4314-A99D-F29E22A5ACFA}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{A8AF5A51-6ECE-4314-A99D-F29E22A5ACFA}.Debug|Any CPU.Deploy.0 = Debug|Any CPU
		{A8AF5A51-6ECE-4314-A99D-F29E22A5ACFA}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{A8AF5A51-6ECE-4314-A99D-F29E22A5ACFA}.Release|Any CPU.Build.0 = Release|Any CPU
		{A8AF5A51-6ECE-4314-A99D-F29E22A5ACFA}.Release|Any CPU.Deploy.0 = Release|Any CPU
		{B48B937B-0F32-401B-A6AD-162029A7D370}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{B48B937B-0F32-401B-A6AD-162029A7D370}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{B48B937B-0F32-401B-A6AD-162029A7D370}.Debug|Any CPU.Deploy.0 = Debug|Any CPU
		{B48B937B-0F32-401B-A6AD-162029A7D370}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{B48B937B-0F32-401B-A6AD-162029A7D370}.Release|Any CPU.Build.0 = Release|Any CPU
		{B48B937B-0F32-401B-A6AD-162029A7D370}.Release|Any CPU.Deploy.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{9B942761-2CAA-40B2-9824-483E58758C15} = {707728B9-9E4A-4962-AE05-AE0E57759D3D}
		{CBA827F0-D102-4BBE-8811-E3B7DEAF8118} = {707728B9-9E4A-4962-AE05-AE0E57759D3D}
		{48D3820C-AFC8-458B-BAF6-55C0A7508BAC} = {707728B9-9E4A-4962-AE05-AE0E57759D3D}
		{9745F198-E630-4F03-BB24-71A58D76904B} = {707728B9-9E4A-4962-AE05-AE0E57759D3D}
		{C4878C94-FD6B-4D48-84DF-4F733233BAB3} = {12C8BBEB-D95F-431D-A619-9EAD388E619C}
		{A8AF5A51-6ECE-4314-A99D-F29E22A5ACFA} = {C4878C94-FD6B-4D48-84DF-4F733233BAB3}
		{B48B937B-0F32-401B-A6AD-162029A7D370} = {C4878C94-FD6B-4D48-84DF-4F733233BAB3}
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {6EE5D1B1-8A6F-4156-B844-5DBC15D5CC5E}
	EndGlobalSection
EndGlobal
