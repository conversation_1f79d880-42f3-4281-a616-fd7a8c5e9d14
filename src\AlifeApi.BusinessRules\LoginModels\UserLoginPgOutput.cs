﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.Json.Serialization;
using System.Threading.Tasks;
using AlifeApi.BusinessRules.Infrastructure;
using AlifeApi.BusinessRules.RoleGroupModels;

namespace AlifeApi.BusinessRules.LoginModels
{
    public class UserLoginPgOutput : IApiMessage
    {
        private IEnumerable<RoleOutput> roles = Enumerable.Empty<RoleOutput>();

        /// <summary>
        /// 使用者帳號
        /// </summary>
        [JsonPropertyName("UserInfoId")]
        public string UserInfoId { get; set; }

        /// <summary>
        /// 使用者密碼
        /// </summary>
        [JsonPropertyName("UserPW")]
        public string UserPassword { get; set; }

        /// <summary>
        /// 使用者姓名
        /// </summary>
        [JsonPropertyName("Name")]
        public string UserName { get; set; }

        /// <summary>
        /// 使用者IP地址
        /// </summary>
        [JsonPropertyName("IP")]
        public string IP { get; set; }

        /// <summary>
        /// 使用者電子郵件
        /// </summary>
        [JsonPropertyName("Email")]
        public string Email { get; set; }

        /// <summary>
        /// 選單樹狀結構
        /// </summary>
        [JsonPropertyName("MenuTrees")]
        public IEnumerable<MenuTreeOutput> MenuTrees { get; set; } = Enumerable.Empty<MenuTreeOutput>();

        /// <summary>
        /// JWT Token
        /// </summary>
        /// <remarks>
        /// 這邊改名不是我嫌棄回傳前端大小寫有問題，只是單純 C# 遇到這種 3 碼以上的略縮字大小寫規則和別的語言不同
        /// </remarks>
        [JsonPropertyName("JWTToken")]
        public string JwtToken { get; set; } = string.Empty;

        /// <summary>
        /// JWT Token 到期日期時間
        /// </summary>
        /// <remarks>
        /// 這邊改名不是我嫌棄回傳前端大小寫有問題，只是單純 C# 遇到這種 3 碼以上的略縮字大小寫規則和別的語言不同
        /// </remarks>
        [JsonPropertyName("JWTTokenExpireTime")]
        public DateTime? JwtTokenExpireTime { get; set; }

        /// <summary>
        /// 公司ID
        /// </summary>
        [JsonPropertyName("CompanyId")]
        public string CompanyId { get; set; }

        /// <summary>
        /// 性別
        /// </summary>
        [JsonPropertyName("Gender")]
        public string Gender { get; set; }

        /// <summary>
        /// 出生日期
        /// </summary>
        [JsonPropertyName("BirthDate")]
        public DateTime? BirthDate { get; set; }

        /// <summary>
        /// 電話號碼
        /// </summary>
        [JsonPropertyName("TelephoneNumber")]
        public string TelephoneNumber { get; set; }

        /// <summary>
        /// 手機號碼
        /// </summary>
        [JsonPropertyName("MobileNumber")]
        public string MobileNumber { get; set; }

        /// <summary>
        /// 戶籍地址
        /// </summary>
        [JsonPropertyName("RegisteredAddress")]
        public string RegisteredAddress { get; set; }

        /// <summary>
        /// 通訊地址
        /// </summary>
        [JsonPropertyName("MailingAddress")]
        public string MailingAddress { get; set; }

        /// <summary>
        /// 緊急聯絡人姓名
        /// </summary>
        [JsonPropertyName("EmergencyContactName")]
        public string EmergencyContactName { get; set; }

        /// <summary>
        /// 緊急聯絡人電話
        /// </summary>
        [JsonPropertyName("EmergencyContactPhone")]
        public string EmergencyContactPhone { get; set; }

        /// <summary>
        /// 與緊急聯絡人的關係
        /// </summary>
        [JsonPropertyName("EmergencyContactRelation")]
        public string EmergencyContactRelation { get; set; }

        /// <summary>
        /// 服務單位
        /// </summary>
        [JsonPropertyName("ServiceUnit")]
        public string ServiceUnit { get; set; }

        /// <summary>
        /// 到職日期
        /// </summary>
        [JsonPropertyName("HireDate")]
        public DateTime? HireDate { get; set; }

        /// <summary>
        /// 帳號狀態
        /// </summary>
        [JsonPropertyName("Status")]
        public bool Status { get; set; }

        /// <summary>
        /// 最後一次登出的時間
        /// </summary>
        [JsonPropertyName("LastLogoutTime")]
        public DateTime? LastLogoutTime { get; set; }

        /// <summary>
        /// 登入失敗次數
        /// </summary>
        [JsonPropertyName("LoginFailedCount")]
        public short? LoginFailedCount { get; set; }

        /// <summary>
        /// 是否為內場/外場人員
        /// </summary>
        [JsonPropertyName("IsInside")]
        public bool? IsInside { get; set; }

        /// <summary>
        /// 是否為 M365 帳號
        /// </summary>
        [JsonPropertyName("IsM365")]
        public bool? IsM365 { get; set; }

        /// <summary>
        /// 是否需要發送電子郵件通知
        /// </summary>
        [JsonPropertyName("IsEmailNotificationEnabled")]
        public bool? IsEmailNotificationEnabled { get; set; }

        /// <inheritdoc/>
        public Enum Response { get; set; }
    }
}
