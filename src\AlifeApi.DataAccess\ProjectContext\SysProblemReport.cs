﻿using System;
using System.Collections.Generic;

namespace AlifeApi.DataAccess.ProjectContext
{
    /// <summary>
    /// 平台問題回報資料
    /// </summary>
    public partial class SysProblemReport
    {
        /// <summary>
        /// 流水號
        /// </summary>
        public long Id { get; set; }

        /// <summary>
        /// 單位代碼
        /// </summary>
        public string DeptId { get; set; }

        public string System { get; set; }

        /// <summary>
        /// 功能頁籤
        /// </summary>
        public string Menu { get; set; }

        /// <summary>
        /// 問題主旨
        /// </summary>
        public string Subject { get; set; }

        /// <summary>
        /// 問題內容
        /// </summary>
        public string ProblemContent { get; set; }

        /// <summary>
        /// 處理狀態
        /// </summary>
        public string ProcessStatus { get; set; }

        /// <summary>
        /// 問題類型
        /// </summary>
        public string ProblemType { get; set; }

        /// <summary>
        /// 處理回報
        /// </summary>
        public string ReplyContent { get; set; }

        /// <summary>
        /// 處理人員
        /// </summary>
        public string ReplyUserId { get; set; }

        public DateTime? ReplyTime { get; set; }

        /// <summary>
        /// 建立者
        /// </summary>
        public string CreatedUserId { get; set; }

        /// <summary>
        /// 建立時間
        /// </summary>
        public DateTime CreatedTime { get; set; }

        /// <summary>
        /// 更新者
        /// </summary>
        public string UpdatedUserId { get; set; }

        /// <summary>
        /// 更新時間
        /// </summary>
        public DateTime UpdatedTime { get; set; }

        public virtual UserDept Dept { get; set; }

        public virtual UserInfo ReplyUser { get; set; }

    }
}
