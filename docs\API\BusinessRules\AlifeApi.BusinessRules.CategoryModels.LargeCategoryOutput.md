#### [AlifeApi.BusinessRules](index.md 'index')
### [AlifeApi.BusinessRules.CategoryModels](AlifeApi.BusinessRules.CategoryModels.md 'AlifeApi.BusinessRules.CategoryModels')

## LargeCategoryOutput Class

大分類資料輸出 DTO

```csharp
public class LargeCategoryOutput
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; LargeCategoryOutput
### Properties

<a name='AlifeApi.BusinessRules.CategoryModels.LargeCategoryOutput.CreatedUserInfoId'></a>

## LargeCategoryOutput.CreatedUserInfoId Property

建立人員ID

```csharp
public string CreatedUserInfoId { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.CategoryModels.LargeCategoryOutput.CreatedUserName'></a>

## LargeCategoryOutput.CreatedUserName Property

建立人員名稱

```csharp
public string CreatedUserName { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.CategoryModels.LargeCategoryOutput.CreateTime'></a>

## LargeCategoryOutput.CreateTime Property

建立時間

```csharp
public System.DateTime CreateTime { get; set; }
```

#### Property Value
[System.DateTime](https://docs.microsoft.com/en-us/dotnet/api/System.DateTime 'System.DateTime')

<a name='AlifeApi.BusinessRules.CategoryModels.LargeCategoryOutput.Description'></a>

## LargeCategoryOutput.Description Property

大分類描述

```csharp
public string Description { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.CategoryModels.LargeCategoryOutput.IsActive'></a>

## LargeCategoryOutput.IsActive Property

是否啟用

```csharp
public System.Nullable<bool> IsActive { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.Boolean](https://docs.microsoft.com/en-us/dotnet/api/System.Boolean 'System.Boolean')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

<a name='AlifeApi.BusinessRules.CategoryModels.LargeCategoryOutput.LargeCategoryId'></a>

## LargeCategoryOutput.LargeCategoryId Property

大分類ID

```csharp
public long LargeCategoryId { get; set; }
```

#### Property Value
[System.Int64](https://docs.microsoft.com/en-us/dotnet/api/System.Int64 'System.Int64')

<a name='AlifeApi.BusinessRules.CategoryModels.LargeCategoryOutput.Name'></a>

## LargeCategoryOutput.Name Property

大分類名稱

```csharp
public string Name { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.CategoryModels.LargeCategoryOutput.SortOrder'></a>

## LargeCategoryOutput.SortOrder Property

排序順序

```csharp
public int SortOrder { get; set; }
```

#### Property Value
[System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')

<a name='AlifeApi.BusinessRules.CategoryModels.LargeCategoryOutput.UpdatedUserInfoId'></a>

## LargeCategoryOutput.UpdatedUserInfoId Property

更新人員ID

```csharp
public string UpdatedUserInfoId { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.CategoryModels.LargeCategoryOutput.UpdatedUserName'></a>

## LargeCategoryOutput.UpdatedUserName Property

更新人員名稱

```csharp
public string UpdatedUserName { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.CategoryModels.LargeCategoryOutput.UpdateTime'></a>

## LargeCategoryOutput.UpdateTime Property

更新時間

```csharp
public System.DateTime UpdateTime { get; set; }
```

#### Property Value
[System.DateTime](https://docs.microsoft.com/en-us/dotnet/api/System.DateTime 'System.DateTime')