#### [AlifeApi.DataAccess](index.md 'index')
### [AlifeApi.DataAccess.ProjectContent](AlifeApi.DataAccess.ProjectContent.md 'AlifeApi.DataAccess.ProjectContent')

## PurchaseOrdersHistory Class

買賣預定單歷史記錄表，主要追蹤業務狀態變更（售、足、簽、請、領）

```csharp
public class PurchaseOrdersHistory
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; PurchaseOrdersHistory
### Properties

<a name='AlifeApi.DataAccess.ProjectContent.PurchaseOrdersHistory.ActionType'></a>

## PurchaseOrdersHistory.ActionType Property

操作類型 (例如: STATUS_CHANGE, DATE_UPDATE, CREATE, MODIFY)

```csharp
public string ActionType { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContent.PurchaseOrdersHistory.ContentRecord'></a>

## PurchaseOrdersHistory.ContentRecord Property

詳細記錄內容或備註說明

```csharp
public string ContentRecord { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContent.PurchaseOrdersHistory.CreatedTime'></a>

## PurchaseOrdersHistory.CreatedTime Property

操作執行時間

```csharp
public System.DateTime CreatedTime { get; set; }
```

#### Property Value
[System.DateTime](https://docs.microsoft.com/en-us/dotnet/api/System.DateTime 'System.DateTime')

<a name='AlifeApi.DataAccess.ProjectContent.PurchaseOrdersHistory.CreatedUserInfoId'></a>

## PurchaseOrdersHistory.CreatedUserInfoId Property

執行此操作的使用者ID

```csharp
public string CreatedUserInfoId { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContent.PurchaseOrdersHistory.HistoryId'></a>

## PurchaseOrdersHistory.HistoryId Property

歷史記錄唯一識別碼 (主鍵, 自動遞增)

```csharp
public int HistoryId { get; set; }
```

#### Property Value
[System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')

<a name='AlifeApi.DataAccess.ProjectContent.PurchaseOrdersHistory.NewStatus'></a>

## PurchaseOrdersHistory.NewStatus Property

變更後的訂單狀態

```csharp
public string NewStatus { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContent.PurchaseOrdersHistory.OldStatus'></a>

## PurchaseOrdersHistory.OldStatus Property

變更前的訂單狀態

```csharp
public string OldStatus { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContent.PurchaseOrdersHistory.OrderId'></a>

## PurchaseOrdersHistory.OrderId Property

關聯的訂單ID

```csharp
public int OrderId { get; set; }
```

#### Property Value
[System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')