#### [AlifeApi.DataAccess](index.md 'index')
### [AlifeApi.DataAccess.ProjectContext](AlifeApi.DataAccess.ProjectContext.md 'AlifeApi.DataAccess.ProjectContext')

## SysRoleGroupUser Class

```csharp
public class SysRoleGroupUser
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; SysRoleGroupUser
### Properties

<a name='AlifeApi.DataAccess.ProjectContext.SysRoleGroupUser.RoleGroupId'></a>

## SysRoleGroupUser.RoleGroupId Property

角色群組

```csharp
public string RoleGroupId { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContext.SysRoleGroupUser.System'></a>

## SysRoleGroupUser.System Property

系統名稱

```csharp
public string System { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContext.SysRoleGroupUser.UserId'></a>

## SysRoleGroupUser.UserId Property

權限群組人員

```csharp
public string UserId { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')