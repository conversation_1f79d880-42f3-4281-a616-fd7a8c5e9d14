#### [AlifeApi.DataAccess](index.md 'index')
### [AlifeApi.DataAccess.ProjectContext](AlifeApi.DataAccess.ProjectContext.md 'AlifeApi.DataAccess.ProjectContext')

## SysUserRecord Class

使用者操作紀錄

```csharp
public class SysUserRecord
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; SysUserRecord
### Properties

<a name='AlifeApi.DataAccess.ProjectContext.SysUserRecord.DeptId'></a>

## SysUserRecord.DeptId Property

使用者部門

```csharp
public string DeptId { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContext.SysUserRecord.GradeCode'></a>

## SysUserRecord.GradeCode Property

職位

```csharp
public string GradeCode { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContext.SysUserRecord.Id'></a>

## SysUserRecord.Id Property

流水號

```csharp
public long Id { get; set; }
```

#### Property Value
[System.Int64](https://docs.microsoft.com/en-us/dotnet/api/System.Int64 'System.Int64')

<a name='AlifeApi.DataAccess.ProjectContext.SysUserRecord.InputData'></a>

## SysUserRecord.InputData Property

輸入資料

```csharp
public string InputData { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContext.SysUserRecord.Ip'></a>

## SysUserRecord.Ip Property

IP

```csharp
public string Ip { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContext.SysUserRecord.RecordEvent'></a>

## SysUserRecord.RecordEvent Property

紀錄API

```csharp
public string RecordEvent { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContext.SysUserRecord.RecordTime'></a>

## SysUserRecord.RecordTime Property

紀錄時間

```csharp
public System.DateTime RecordTime { get; set; }
```

#### Property Value
[System.DateTime](https://docs.microsoft.com/en-us/dotnet/api/System.DateTime 'System.DateTime')

<a name='AlifeApi.DataAccess.ProjectContext.SysUserRecord.UserId'></a>

## SysUserRecord.UserId Property

使用者

```csharp
public string UserId { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')