#### [AlifeApi.BusinessRules](index.md 'index')
### [AlifeApi.BusinessRules.ReviewModels](AlifeApi.BusinessRules.ReviewModels.md 'AlifeApi.BusinessRules.ReviewModels')

## ReviewTaskService Class

審核流程服務

```csharp
public class ReviewTaskService : AlifeApi.BusinessRules.Infrastructure.ServiceBase<AlifeApi.DataAccess.ProjectContent.alifeContext>
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; [AlifeApi.Common.InfrastructureBase](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.Common.InfrastructureBase 'AlifeApi.Common.InfrastructureBase') &#129106; [AlifeApi.BusinessRules.Infrastructure.ServiceBase&lt;](AlifeApi.BusinessRules.Infrastructure.ServiceBase_TDbContext_.md 'AlifeApi.BusinessRules.Infrastructure.ServiceBase<TDbContext>')[AlifeApi.DataAccess.ProjectContent.alifeContext](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.DataAccess.ProjectContent.alifeContext 'AlifeApi.DataAccess.ProjectContent.alifeContext')[&gt;](AlifeApi.BusinessRules.Infrastructure.ServiceBase_TDbContext_.md 'AlifeApi.BusinessRules.Infrastructure.ServiceBase<TDbContext>') &#129106; ReviewTaskService
### Methods

<a name='AlifeApi.BusinessRules.ReviewModels.ReviewTaskService.AddReviewHistoryAsync(AlifeApi.BusinessRules.ReviewModels.ReviewHistoryInput)'></a>

## ReviewTaskService.AddReviewHistoryAsync(ReviewHistoryInput) Method

添加審核歷史記錄

```csharp
public System.Threading.Tasks.Task AddReviewHistoryAsync(AlifeApi.BusinessRules.ReviewModels.ReviewHistoryInput input);
```
#### Parameters

<a name='AlifeApi.BusinessRules.ReviewModels.ReviewTaskService.AddReviewHistoryAsync(AlifeApi.BusinessRules.ReviewModels.ReviewHistoryInput).input'></a>

`input` [ReviewHistoryInput](AlifeApi.BusinessRules.ReviewModels.ReviewHistoryInput.md 'AlifeApi.BusinessRules.ReviewModels.ReviewHistoryInput')

審核歷史記錄輸入資料

#### Returns
[System.Threading.Tasks.Task](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task 'System.Threading.Tasks.Task')

<a name='AlifeApi.BusinessRules.ReviewModels.ReviewTaskService.CreateReviewTaskAsync(AlifeApi.BusinessRules.ReviewModels.ReviewTaskInput)'></a>

## ReviewTaskService.CreateReviewTaskAsync(ReviewTaskInput) Method

建立審核流程

```csharp
public System.Threading.Tasks.Task<int> CreateReviewTaskAsync(AlifeApi.BusinessRules.ReviewModels.ReviewTaskInput input);
```
#### Parameters

<a name='AlifeApi.BusinessRules.ReviewModels.ReviewTaskService.CreateReviewTaskAsync(AlifeApi.BusinessRules.ReviewModels.ReviewTaskInput).input'></a>

`input` [ReviewTaskInput](AlifeApi.BusinessRules.ReviewModels.ReviewTaskInput.md 'AlifeApi.BusinessRules.ReviewModels.ReviewTaskInput')

審核流程輸入資料

#### Returns
[System.Threading.Tasks.Task&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')[System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')  
審核流程ID

<a name='AlifeApi.BusinessRules.ReviewModels.ReviewTaskService.DeleteReviewTaskAsync(int)'></a>

## ReviewTaskService.DeleteReviewTaskAsync(int) Method

刪除審核流程

```csharp
public System.Threading.Tasks.Task DeleteReviewTaskAsync(int taskId);
```
#### Parameters

<a name='AlifeApi.BusinessRules.ReviewModels.ReviewTaskService.DeleteReviewTaskAsync(int).taskId'></a>

`taskId` [System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')

審核流程ID

#### Returns
[System.Threading.Tasks.Task](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task 'System.Threading.Tasks.Task')

<a name='AlifeApi.BusinessRules.ReviewModels.ReviewTaskService.GetNextStepAsync(int)'></a>

## ReviewTaskService.GetNextStepAsync(int) Method

獲取下一步驟資訊

```csharp
public System.Threading.Tasks.Task<AlifeApi.BusinessRules.ReviewModels.ReviewNextStepOutput> GetNextStepAsync(int taskId);
```
#### Parameters

<a name='AlifeApi.BusinessRules.ReviewModels.ReviewTaskService.GetNextStepAsync(int).taskId'></a>

`taskId` [System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')

審核流程ID

#### Returns
[System.Threading.Tasks.Task&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')[ReviewNextStepOutput](AlifeApi.BusinessRules.ReviewModels.ReviewNextStepOutput.md 'AlifeApi.BusinessRules.ReviewModels.ReviewNextStepOutput')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')  
下一步驟資訊

<a name='AlifeApi.BusinessRules.ReviewModels.ReviewTaskService.GetReviewHistoryAsync(int)'></a>

## ReviewTaskService.GetReviewHistoryAsync(int) Method

取得審核流程歷史記錄

```csharp
public System.Threading.Tasks.Task<System.Collections.Generic.List<AlifeApi.BusinessRules.ReviewModels.ReviewHistoryOutput>> GetReviewHistoryAsync(int taskId);
```
#### Parameters

<a name='AlifeApi.BusinessRules.ReviewModels.ReviewTaskService.GetReviewHistoryAsync(int).taskId'></a>

`taskId` [System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')

審核流程ID

#### Returns
[System.Threading.Tasks.Task&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')[System.Collections.Generic.List&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Collections.Generic.List-1 'System.Collections.Generic.List`1')[ReviewHistoryOutput](AlifeApi.BusinessRules.ReviewModels.ReviewHistoryOutput.md 'AlifeApi.BusinessRules.ReviewModels.ReviewHistoryOutput')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Collections.Generic.List-1 'System.Collections.Generic.List`1')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')  
歷史記錄清單

<a name='AlifeApi.BusinessRules.ReviewModels.ReviewTaskService.GetReviewTaskDetailAsync(int)'></a>

## ReviewTaskService.GetReviewTaskDetailAsync(int) Method

取得審核流程詳細資料

```csharp
public System.Threading.Tasks.Task<AlifeApi.BusinessRules.ReviewModels.ReviewTaskDetailOutput> GetReviewTaskDetailAsync(int taskId);
```
#### Parameters

<a name='AlifeApi.BusinessRules.ReviewModels.ReviewTaskService.GetReviewTaskDetailAsync(int).taskId'></a>

`taskId` [System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')

審核流程ID

#### Returns
[System.Threading.Tasks.Task&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')[ReviewTaskDetailOutput](AlifeApi.BusinessRules.ReviewModels.ReviewTaskDetailOutput.md 'AlifeApi.BusinessRules.ReviewModels.ReviewTaskDetailOutput')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')  
審核流程詳細資料

<a name='AlifeApi.BusinessRules.ReviewModels.ReviewTaskService.GetReviewTasksAsync(AlifeApi.BusinessRules.ReviewModels.ReviewTaskListInput)'></a>

## ReviewTaskService.GetReviewTasksAsync(ReviewTaskListInput) Method

取得審核流程清單

```csharp
public System.Threading.Tasks.Task<AlifeApi.BusinessRules.Infrastructure.PagedListOutput<AlifeApi.BusinessRules.ReviewModels.ReviewTaskOutput>> GetReviewTasksAsync(AlifeApi.BusinessRules.ReviewModels.ReviewTaskListInput input);
```
#### Parameters

<a name='AlifeApi.BusinessRules.ReviewModels.ReviewTaskService.GetReviewTasksAsync(AlifeApi.BusinessRules.ReviewModels.ReviewTaskListInput).input'></a>

`input` [ReviewTaskListInput](AlifeApi.BusinessRules.ReviewModels.ReviewTaskListInput.md 'AlifeApi.BusinessRules.ReviewModels.ReviewTaskListInput')

查詢條件

#### Returns
[System.Threading.Tasks.Task&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')[AlifeApi.BusinessRules.Infrastructure.PagedListOutput&lt;](AlifeApi.BusinessRules.Infrastructure.PagedListOutput_TDetail_.md 'AlifeApi.BusinessRules.Infrastructure.PagedListOutput<TDetail>')[ReviewTaskOutput](AlifeApi.BusinessRules.ReviewModels.ReviewTaskOutput.md 'AlifeApi.BusinessRules.ReviewModels.ReviewTaskOutput')[&gt;](AlifeApi.BusinessRules.Infrastructure.PagedListOutput_TDetail_.md 'AlifeApi.BusinessRules.Infrastructure.PagedListOutput<TDetail>')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')  
審核流程清單

<a name='AlifeApi.BusinessRules.ReviewModels.ReviewTaskService.GetReviewUsersAsync()'></a>

## ReviewTaskService.GetReviewUsersAsync() Method

獲取審核人員清單

```csharp
public System.Threading.Tasks.Task<System.Collections.Generic.List<AlifeApi.BusinessRules.ReviewModels.ReviewDeptGroupOutput>> GetReviewUsersAsync();
```

#### Returns
[System.Threading.Tasks.Task&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')[System.Collections.Generic.List&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Collections.Generic.List-1 'System.Collections.Generic.List`1')[ReviewDeptGroupOutput](AlifeApi.BusinessRules.ReviewModels.ReviewDeptGroupOutput.md 'AlifeApi.BusinessRules.ReviewModels.ReviewDeptGroupOutput')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Collections.Generic.List-1 'System.Collections.Generic.List`1')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')  
審核人員清單

<a name='AlifeApi.BusinessRules.ReviewModels.ReviewTaskService.UpdateReviewTaskAsync(int,AlifeApi.BusinessRules.ReviewModels.ReviewTaskUpdateInput)'></a>

## ReviewTaskService.UpdateReviewTaskAsync(int, ReviewTaskUpdateInput) Method

更新審核流程

```csharp
public System.Threading.Tasks.Task<int> UpdateReviewTaskAsync(int taskId, AlifeApi.BusinessRules.ReviewModels.ReviewTaskUpdateInput input);
```
#### Parameters

<a name='AlifeApi.BusinessRules.ReviewModels.ReviewTaskService.UpdateReviewTaskAsync(int,AlifeApi.BusinessRules.ReviewModels.ReviewTaskUpdateInput).taskId'></a>

`taskId` [System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')

審核流程ID

<a name='AlifeApi.BusinessRules.ReviewModels.ReviewTaskService.UpdateReviewTaskAsync(int,AlifeApi.BusinessRules.ReviewModels.ReviewTaskUpdateInput).input'></a>

`input` [ReviewTaskUpdateInput](AlifeApi.BusinessRules.ReviewModels.ReviewTaskUpdateInput.md 'AlifeApi.BusinessRules.ReviewModels.ReviewTaskUpdateInput')

審核流程更新輸入資料

#### Returns
[System.Threading.Tasks.Task&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')[System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')  
審核流程ID