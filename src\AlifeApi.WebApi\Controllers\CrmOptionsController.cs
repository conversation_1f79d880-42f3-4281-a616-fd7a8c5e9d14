using System;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using AlifeApi.BusinessRules.CrmOptionModels;
using AlifeApi.BusinessRules.Infrastructure;

namespace AlifeApi.WebApi.Controllers
{
    /// <summary>
    /// CRM選項管理
    /// </summary>
    public class CrmOptionsController : AuthenticatedController
    {
        private readonly CrmOptionService _crmOptionService;

        public CrmOptionsController(CrmOptionService crmOptionService)
        {
            _crmOptionService = crmOptionService ?? throw new ArgumentNullException(nameof(crmOptionService));
        }

        /// <summary>
        /// 取得CRM選項列表 (分頁)
        /// </summary>
        /// <param name="input">查詢條件</param>
        /// <returns>分頁的CRM選項列表</returns>
        [HttpPost]
        public async Task<ActionResult<PagedListOutput<CrmOptionListOutput>>> GetCrmOptionListAsync([FromBody] CrmOptionQueryInput input)
        {
            var result = await _crmOptionService.GetCrmOptionListAsync(input);
            return Ok(result);
        }

        /// <summary>
        /// 根據CRM選項ID取得詳細資訊
        /// </summary>
        /// <param name="siteCrmOptionId">CRM選項ID</param>
        /// <returns>CRM選項詳細資訊</returns>
        [HttpGet("{siteCrmOptionId}")]
        public async Task<ActionResult<CrmOptionOutput>> GetCrmOptionByIdAsync(long siteCrmOptionId)
        {
            var result = await _crmOptionService.GetCrmOptionByIdAsync(siteCrmOptionId);
            if (result == null)
                return NotFound();
            return Ok(result);
        }

        /// <summary>
        /// 新增CRM選項
        /// </summary>
        /// <param name="input">CRM選項建立輸入資料</param>
        /// <returns>新增的CRM選項ID</returns>
        [HttpPost]
        public async Task<ActionResult<long>> CreateCrmOptionAsync([FromBody] CrmOptionCreateInput input)
        {
            try
            {
                var id = await _crmOptionService.CreateCrmOptionAsync(input);
                return CreatedAtAction(nameof(GetCrmOptionByIdAsync), new { siteCrmOptionId = id }, new { SiteCrmOptionId = id });
            }
            catch (Exception ex)
            {
                return BadRequest(new { message = ex.Message });
            }
        }

        /// <summary>
        /// 更新CRM選項
        /// </summary>
        /// <param name="siteCrmOptionId">CRM選項ID</param>
        /// <param name="input">CRM選項更新輸入資料</param>
        /// <returns>NoContent</returns>
        [HttpPut("{siteCrmOptionId}")]
        public async Task<ActionResult> UpdateCrmOptionAsync(long siteCrmOptionId, [FromBody] CrmOptionUpdateInput input)
        {
            try
            {
                await _crmOptionService.UpdateCrmOptionAsync(siteCrmOptionId, input);
                return NoContent();
            }
            catch (Exception ex)
            {
                return BadRequest(new { message = ex.Message });
            }
        }

        /// <summary>
        /// 刪除CRM選項
        /// </summary>
        /// <param name="siteCrmOptionId">CRM選項ID</param>
        /// <returns>NoContent</returns>
        [HttpDelete("{siteCrmOptionId}")]
        public async Task<ActionResult> DeleteCrmOptionAsync(long siteCrmOptionId)
        {
            try
            {
                await _crmOptionService.DeleteCrmOptionAsync(siteCrmOptionId);
                return NoContent();
            }
            catch (Exception ex)
            {
                return BadRequest(new { message = ex.Message });
            }
        }

        /// <summary>
        /// 取得CRM選項下拉選單
        /// </summary>
        /// <param name="input">查詢條件</param>
        /// <returns>CRM選項下拉選單列表</returns>
        [HttpPost("dropdown")]
        public async Task<ActionResult> GetCrmOptionDropdownAsync([FromBody] CrmOptionDropdownInput input)
        {
            try
            {
                var result = await _crmOptionService.GetCrmOptionDropdownListAsync(input);
                return Ok(result);
            }
            catch (Exception ex)
            {
                return BadRequest(new { message = ex.Message });
            }
        }

        /// <summary>
        /// 取得CRM選項類型下拉選單
        /// </summary>
        /// <returns>CRM選項類型下拉選單列表</returns>
        [HttpGet("types")]
        public async Task<ActionResult> GetCrmOptionTypeDropdownAsync()
        {
            try
            {
                var result = await _crmOptionService.GetCrmOptionTypeDropdownListAsync();
                return Ok(result);
            }
            catch (Exception ex)
            {
                return BadRequest(new { message = ex.Message });
            }
        }
    }
}
