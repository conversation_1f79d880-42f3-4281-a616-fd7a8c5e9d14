﻿using AlifeApi.BusinessRules.Infrastructure;
using AlifeApi.DataAccess.LogContext;
using LogcontextPg;

namespace AlifeApi.BusinessRules.LogModels
{
    /// <summary>
    /// LOG 商業邏輯
    /// </summary>
    public class LogService : ServiceBase<LogContextPg>
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="LogService"/> class.
        /// </summary>
        /// <param name="serviceProvider">The service provider.</param>
        /// <param name="dbContext">The database context.</param>
        public LogService(IServiceProvider serviceProvider, LogContextPg dbContext) : base(serviceProvider, dbContext)
        {
        }

        /// <summary>
        /// Writes the API log asynchronous.
        /// </summary>
        /// <param name="input">The input.</param>
        public async Task CreateApiLogAsync(ApiLogCreateInput input)
        {
            ArgumentNullException.ThrowIfNull(input, nameof(input));

            Db.SysApiLogs.Add(new LogcontextPg.SysApiLog
            {
                System = input.SystemName ?? "", // CurrentUser 不一定有值，所以抓 Input
                ExecutedTime = DateTime.Now,
                ControllerName = input.ControllerName ?? "",
                ActionName = input.ActionName ?? "",
                Source = input.Source ?? "",
                Headers = input.Headers ?? "",
                Seconds = input.Seconds,
                InputData = input.Input ?? "",
                OutputData = input.Output ?? "",
                SessionId = input.SessionId ?? "",
                Exception = input.Exception ?? ""
            });

            await Db.SaveChangesAsync();
        }
    }
}
