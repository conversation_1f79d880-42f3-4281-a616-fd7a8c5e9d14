#### [AlifeApi.BusinessRules](index.md 'index')
### [AlifeApi.BusinessRules.SiteModels](AlifeApi.BusinessRules.SiteModels.md 'AlifeApi.BusinessRules.SiteModels')

## SiteOutput Class

案場輸出

```csharp
public class SiteOutput
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; SiteOutput
### Properties

<a name='AlifeApi.BusinessRules.SiteModels.SiteOutput.AboveGroundFloors'></a>

## SiteOutput.AboveGroundFloors Property

地上層數

```csharp
public string AboveGroundFloors { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.SiteModels.SiteOutput.Address'></a>

## SiteOutput.Address Property

完整地址

```csharp
public string Address { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.SiteModels.SiteOutput.AdvertisingBudget'></a>

## SiteOutput.AdvertisingBudget Property

應編廣告預算

```csharp
public System.Nullable<decimal> AdvertisingBudget { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.Decimal](https://docs.microsoft.com/en-us/dotnet/api/System.Decimal 'System.Decimal')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

<a name='AlifeApi.BusinessRules.SiteModels.SiteOutput.AdvertisingBudgetRate'></a>

## SiteOutput.AdvertisingBudgetRate Property

廣告預算率

```csharp
public System.Nullable<decimal> AdvertisingBudgetRate { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.Decimal](https://docs.microsoft.com/en-us/dotnet/api/System.Decimal 'System.Decimal')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

<a name='AlifeApi.BusinessRules.SiteModels.SiteOutput.BelowGroundFloors'></a>

## SiteOutput.BelowGroundFloors Property

地下層數

```csharp
public string BelowGroundFloors { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.SiteModels.SiteOutput.Broker'></a>

## SiteOutput.Broker Property

經紀人

```csharp
public string Broker { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.SiteModels.SiteOutput.BusinessIds'></a>

## SiteOutput.BusinessIds Property

業務

```csharp
public string BusinessIds { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.SiteModels.SiteOutput.Chairman'></a>

## SiteOutput.Chairman Property

主委

```csharp
public string Chairman { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.SiteModels.SiteOutput.City'></a>

## SiteOutput.City Property

所在縣市

```csharp
public string City { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.SiteModels.SiteOutput.CompanyId'></a>

## SiteOutput.CompanyId Property

執行公司別

```csharp
public string CompanyId { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.SiteModels.SiteOutput.ContractedAmount'></a>

## SiteOutput.ContractedAmount Property

已發包金額

```csharp
public System.Nullable<decimal> ContractedAmount { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.Decimal](https://docs.microsoft.com/en-us/dotnet/api/System.Decimal 'System.Decimal')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

<a name='AlifeApi.BusinessRules.SiteModels.SiteOutput.ContractPeriod'></a>

## SiteOutput.ContractPeriod Property

合約期間

```csharp
public System.Nullable<System.DateOnly> ContractPeriod { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.DateOnly](https://docs.microsoft.com/en-us/dotnet/api/System.DateOnly 'System.DateOnly')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

<a name='AlifeApi.BusinessRules.SiteModels.SiteOutput.ControlReserveRate'></a>

## SiteOutput.ControlReserveRate Property

控存率

```csharp
public System.Nullable<decimal> ControlReserveRate { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.Decimal](https://docs.microsoft.com/en-us/dotnet/api/System.Decimal 'System.Decimal')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

<a name='AlifeApi.BusinessRules.SiteModels.SiteOutput.CreatedTime'></a>

## SiteOutput.CreatedTime Property

創建時間

```csharp
public System.Nullable<System.DateTime> CreatedTime { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.DateTime](https://docs.microsoft.com/en-us/dotnet/api/System.DateTime 'System.DateTime')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

<a name='AlifeApi.BusinessRules.SiteModels.SiteOutput.CreatedUserId'></a>

## SiteOutput.CreatedUserId Property

創建者ID

```csharp
public string CreatedUserId { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.SiteModels.SiteOutput.CreatedUserName'></a>

## SiteOutput.CreatedUserName Property

創建者名稱

```csharp
public string CreatedUserName { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.SiteModels.SiteOutput.DeputyProjectManager'></a>

## SiteOutput.DeputyProjectManager Property

副專

```csharp
public string DeputyProjectManager { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.SiteModels.SiteOutput.Developer'></a>

## SiteOutput.Developer Property

投資興建

```csharp
public string Developer { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.SiteModels.SiteOutput.District'></a>

## SiteOutput.District Property

所在區域

```csharp
public string District { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.SiteModels.SiteOutput.ExcessPriceAllocation'></a>

## SiteOutput.ExcessPriceAllocation Property

超價款分配

```csharp
public string ExcessPriceAllocation { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.SiteModels.SiteOutput.ExtensionPeriod'></a>

## SiteOutput.ExtensionPeriod Property

展延期間

```csharp
public System.Nullable<System.DateOnly> ExtensionPeriod { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.DateOnly](https://docs.microsoft.com/en-us/dotnet/api/System.DateOnly 'System.DateOnly')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

<a name='AlifeApi.BusinessRules.SiteModels.SiteOutput.LandArea'></a>

## SiteOutput.LandArea Property

基地面積

```csharp
public System.Nullable<decimal> LandArea { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.Decimal](https://docs.microsoft.com/en-us/dotnet/api/System.Decimal 'System.Decimal')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

<a name='AlifeApi.BusinessRules.SiteModels.SiteOutput.PaidAmount'></a>

## SiteOutput.PaidAmount Property

已請款金額

```csharp
public System.Nullable<decimal> PaidAmount { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.Decimal](https://docs.microsoft.com/en-us/dotnet/api/System.Decimal 'System.Decimal')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

<a name='AlifeApi.BusinessRules.SiteModels.SiteOutput.ParkingType'></a>

## SiteOutput.ParkingType Property

車位類別

```csharp
public string ParkingType { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.SiteModels.SiteOutput.PlannedParkingSpaces'></a>

## SiteOutput.PlannedParkingSpaces Property

規劃戶數(車位)

```csharp
public System.Nullable<int> PlannedParkingSpaces { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

<a name='AlifeApi.BusinessRules.SiteModels.SiteOutput.PlannedResidentialUnits'></a>

## SiteOutput.PlannedResidentialUnits Property

規劃戶數(住家)

```csharp
public System.Nullable<int> PlannedResidentialUnits { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

<a name='AlifeApi.BusinessRules.SiteModels.SiteOutput.PlannedStoreUnits'></a>

## SiteOutput.PlannedStoreUnits Property

規劃戶數(店面)

```csharp
public System.Nullable<int> PlannedStoreUnits { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

<a name='AlifeApi.BusinessRules.SiteModels.SiteOutput.ProjectManager'></a>

## SiteOutput.ProjectManager Property

專案

```csharp
public string ProjectManager { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.SiteModels.SiteOutput.PromotionType'></a>

## SiteOutput.PromotionType Property

推案型態

```csharp
public string PromotionType { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.SiteModels.SiteOutput.PublicFacilityRatio'></a>

## SiteOutput.PublicFacilityRatio Property

公設比

```csharp
public System.Nullable<decimal> PublicFacilityRatio { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.Decimal](https://docs.microsoft.com/en-us/dotnet/api/System.Decimal 'System.Decimal')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

<a name='AlifeApi.BusinessRules.SiteModels.SiteOutput.ReceptionCenter'></a>

## SiteOutput.ReceptionCenter Property

接待中心

```csharp
public string ReceptionCenter { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.SiteModels.SiteOutput.ReserveAmount'></a>

## SiteOutput.ReserveAmount Property

保留款

```csharp
public System.Nullable<decimal> ReserveAmount { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.Decimal](https://docs.microsoft.com/en-us/dotnet/api/System.Decimal 'System.Decimal')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

<a name='AlifeApi.BusinessRules.SiteModels.SiteOutput.RunnerIds'></a>

## SiteOutput.RunnerIds Property

跑單

```csharp
public string RunnerIds { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.SiteModels.SiteOutput.SellableTotalPrice'></a>

## SiteOutput.SellableTotalPrice Property

可售總銷

```csharp
public System.Nullable<decimal> SellableTotalPrice { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.Decimal](https://docs.microsoft.com/en-us/dotnet/api/System.Decimal 'System.Decimal')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

<a name='AlifeApi.BusinessRules.SiteModels.SiteOutput.ServiceFeeCalculation'></a>

## SiteOutput.ServiceFeeCalculation Property

服務費計算

```csharp
public string ServiceFeeCalculation { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.SiteModels.SiteOutput.ServiceFeeRate'></a>

## SiteOutput.ServiceFeeRate Property

服務費率

```csharp
public System.Nullable<decimal> ServiceFeeRate { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.Decimal](https://docs.microsoft.com/en-us/dotnet/api/System.Decimal 'System.Decimal')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

<a name='AlifeApi.BusinessRules.SiteModels.SiteOutput.SiteCode'></a>

## SiteOutput.SiteCode Property

案場代碼

```csharp
public string SiteCode { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.SiteModels.SiteOutput.SiteLocation'></a>

## SiteOutput.SiteLocation Property

基地位置

```csharp
public string SiteLocation { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.SiteModels.SiteOutput.SiteName'></a>

## SiteOutput.SiteName Property

案場名稱

```csharp
public string SiteName { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.SiteModels.SiteOutput.SitePhone'></a>

## SiteOutput.SitePhone Property

案場電話

```csharp
public string SitePhone { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.SiteModels.SiteOutput.Structure'></a>

## SiteOutput.Structure Property

結構

```csharp
public string Structure { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.SiteModels.SiteOutput.TotalSalePrice'></a>

## SiteOutput.TotalSalePrice Property

全案總銷

```csharp
public System.Nullable<decimal> TotalSalePrice { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.Decimal](https://docs.microsoft.com/en-us/dotnet/api/System.Decimal 'System.Decimal')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

<a name='AlifeApi.BusinessRules.SiteModels.SiteOutput.UnitSize'></a>

## SiteOutput.UnitSize Property

坪數

```csharp
public string UnitSize { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.SiteModels.SiteOutput.UpdatedTime'></a>

## SiteOutput.UpdatedTime Property

更新時間

```csharp
public System.Nullable<System.DateTime> UpdatedTime { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.DateTime](https://docs.microsoft.com/en-us/dotnet/api/System.DateTime 'System.DateTime')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

<a name='AlifeApi.BusinessRules.SiteModels.SiteOutput.UpdatedUserId'></a>

## SiteOutput.UpdatedUserId Property

更新者ID

```csharp
public string UpdatedUserId { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.SiteModels.SiteOutput.UpdatedUserName'></a>

## SiteOutput.UpdatedUserName Property

更新者名稱

```csharp
public string UpdatedUserName { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.SiteModels.SiteOutput.ViceChairman'></a>

## SiteOutput.ViceChairman Property

副主委

```csharp
public string ViceChairman { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.SiteModels.SiteOutput.Zoning'></a>

## SiteOutput.Zoning Property

使用分區

```csharp
public string Zoning { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')