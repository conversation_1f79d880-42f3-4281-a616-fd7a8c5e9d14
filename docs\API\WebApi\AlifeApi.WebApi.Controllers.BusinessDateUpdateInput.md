#### [AlifeApi.WebApi](index.md 'index')
### [AlifeApi.WebApi.Controllers](AlifeApi.WebApi.Controllers.md 'AlifeApi.WebApi.Controllers')

## BusinessDateUpdateInput Class

業務日期更新輸入模型

```csharp
public class BusinessDateUpdateInput
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; BusinessDateUpdateInput
### Properties

<a name='AlifeApi.WebApi.Controllers.BusinessDateUpdateInput.CancellationDate'></a>

## BusinessDateUpdateInput.CancellationDate Property

退訂日期

```csharp
public System.Nullable<System.DateOnly> CancellationDate { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.DateOnly](https://docs.microsoft.com/en-us/dotnet/api/System.DateOnly 'System.DateOnly')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

<a name='AlifeApi.WebApi.Controllers.BusinessDateUpdateInput.ContractSignedDate'></a>

## BusinessDateUpdateInput.ContractSignedDate Property

實際簽約日期 (狀態：簽)

```csharp
public System.Nullable<System.DateOnly> ContractSignedDate { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.DateOnly](https://docs.microsoft.com/en-us/dotnet/api/System.DateOnly 'System.DateOnly')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

<a name='AlifeApi.WebApi.Controllers.BusinessDateUpdateInput.DepositFullPaidDate'></a>

## BusinessDateUpdateInput.DepositFullPaidDate Property

足訂日期 (狀態：足)

```csharp
public System.Nullable<System.DateOnly> DepositFullPaidDate { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.DateOnly](https://docs.microsoft.com/en-us/dotnet/api/System.DateOnly 'System.DateOnly')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

<a name='AlifeApi.WebApi.Controllers.BusinessDateUpdateInput.FinalPaymentDate'></a>

## BusinessDateUpdateInput.FinalPaymentDate Property

尾款繳清日期

```csharp
public System.Nullable<System.DateOnly> FinalPaymentDate { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.DateOnly](https://docs.microsoft.com/en-us/dotnet/api/System.DateOnly 'System.DateOnly')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

<a name='AlifeApi.WebApi.Controllers.BusinessDateUpdateInput.HandoverDate'></a>

## BusinessDateUpdateInput.HandoverDate Property

交屋日期

```csharp
public System.Nullable<System.DateOnly> HandoverDate { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.DateOnly](https://docs.microsoft.com/en-us/dotnet/api/System.DateOnly 'System.DateOnly')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

<a name='AlifeApi.WebApi.Controllers.BusinessDateUpdateInput.ReceiveDate'></a>

## BusinessDateUpdateInput.ReceiveDate Property

領款日期 (狀態：領)

```csharp
public System.Nullable<System.DateOnly> ReceiveDate { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.DateOnly](https://docs.microsoft.com/en-us/dotnet/api/System.DateOnly 'System.DateOnly')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

<a name='AlifeApi.WebApi.Controllers.BusinessDateUpdateInput.RequestDate'></a>

## BusinessDateUpdateInput.RequestDate Property

請款日期 (狀態：請)

```csharp
public System.Nullable<System.DateOnly> RequestDate { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.DateOnly](https://docs.microsoft.com/en-us/dotnet/api/System.DateOnly 'System.DateOnly')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

<a name='AlifeApi.WebApi.Controllers.BusinessDateUpdateInput.SaleDate'></a>

## BusinessDateUpdateInput.SaleDate Property

實際售出日期 (狀態：售)

```csharp
public System.Nullable<System.DateOnly> SaleDate { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.DateOnly](https://docs.microsoft.com/en-us/dotnet/api/System.DateOnly 'System.DateOnly')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')