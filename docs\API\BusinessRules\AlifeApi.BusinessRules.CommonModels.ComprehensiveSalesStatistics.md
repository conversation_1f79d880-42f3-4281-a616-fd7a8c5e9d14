#### [AlifeApi.BusinessRules](index.md 'index')
### [AlifeApi.BusinessRules.CommonModels](AlifeApi.BusinessRules.CommonModels.md 'AlifeApi.BusinessRules.CommonModels')

## ComprehensiveSalesStatistics Class

綜合銷售統計

```csharp
public class ComprehensiveSalesStatistics
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; ComprehensiveSalesStatistics
### Properties

<a name='AlifeApi.BusinessRules.CommonModels.ComprehensiveSalesStatistics.GeneratedTime'></a>

## ComprehensiveSalesStatistics.GeneratedTime Property

統計產生時間

```csharp
public System.DateTime GeneratedTime { get; set; }
```

#### Property Value
[System.DateTime](https://docs.microsoft.com/en-us/dotnet/api/System.DateTime 'System.DateTime')

<a name='AlifeApi.BusinessRules.CommonModels.ComprehensiveSalesStatistics.ParkingSpaceStatistics'></a>

## ComprehensiveSalesStatistics.ParkingSpaceStatistics Property

停車位銷售統計

```csharp
public global::ParkingSpaceSalesStatistics ParkingSpaceStatistics { get; set; }
```

#### Property Value
[ParkingSpaceSalesStatistics](https://docs.microsoft.com/en-us/dotnet/api/ParkingSpaceSalesStatistics 'ParkingSpaceSalesStatistics')

<a name='AlifeApi.BusinessRules.CommonModels.ComprehensiveSalesStatistics.SiteCode'></a>

## ComprehensiveSalesStatistics.SiteCode Property

案場編號

```csharp
public string SiteCode { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.CommonModels.ComprehensiveSalesStatistics.UnitStatistics'></a>

## ComprehensiveSalesStatistics.UnitStatistics Property

房屋銷售統計

```csharp
public global::UnitSalesStatistics UnitStatistics { get; set; }
```

#### Property Value
[UnitSalesStatistics](https://docs.microsoft.com/en-us/dotnet/api/UnitSalesStatistics 'UnitSalesStatistics')