﻿using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;

namespace AlifeApi.BusinessRules.RoleGroupModels
{
    /// <summary>
    /// 修改角色權限使用者
    /// </summary>
    public class RoleGroupUserUpdateInput
    {
        /// <summary>
        /// ID
        /// </summary>
        [Required]
        public string Id { get; set; }

        /// <summary>
        /// 使用者清單
        /// </summary>
        [JsonPropertyName("UserId")]
        public IEnumerable<string> UserIds { get; set; } = Enumerable.Empty<string>();

    }
}
