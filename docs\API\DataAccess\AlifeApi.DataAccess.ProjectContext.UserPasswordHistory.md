#### [AlifeApi.DataAccess](index.md 'index')
### [AlifeApi.DataAccess.ProjectContext](AlifeApi.DataAccess.ProjectContext.md 'AlifeApi.DataAccess.ProjectContext')

## UserPasswordHistory Class

密碼歷史資料

```csharp
public class UserPasswordHistory
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; UserPasswordHistory
### Properties

<a name='AlifeApi.DataAccess.ProjectContext.UserPasswordHistory.CreatedTime'></a>

## UserPasswordHistory.CreatedTime Property

建立時間

```csharp
public System.DateTime CreatedTime { get; set; }
```

#### Property Value
[System.DateTime](https://docs.microsoft.com/en-us/dotnet/api/System.DateTime 'System.DateTime')

<a name='AlifeApi.DataAccess.ProjectContext.UserPasswordHistory.CreatedUserId'></a>

## UserPasswordHistory.CreatedUserId Property

建立者

```csharp
public string CreatedUserId { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContext.UserPasswordHistory.Id'></a>

## UserPasswordHistory.Id Property

流水號

```csharp
public long Id { get; set; }
```

#### Property Value
[System.Int64](https://docs.microsoft.com/en-us/dotnet/api/System.Int64 'System.Int64')

<a name='AlifeApi.DataAccess.ProjectContext.UserPasswordHistory.Pw'></a>

## UserPasswordHistory.Pw Property

密碼

```csharp
public string Pw { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContext.UserPasswordHistory.UserId'></a>

## UserPasswordHistory.UserId Property

員工編號

```csharp
public string UserId { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')