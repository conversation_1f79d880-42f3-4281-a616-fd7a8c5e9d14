#### [AlifeApi.BusinessRules](index.md 'index')
### [AlifeApi.BusinessRules.WeeklyReportModels](AlifeApi.BusinessRules.WeeklyReportModels.md 'AlifeApi.BusinessRules.WeeklyReportModels')

## WeeklyCustomerAnalysisStatistics Class

客戶背景分析統計

```csharp
public class WeeklyCustomerAnalysisStatistics
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; WeeklyCustomerAnalysisStatistics
### Properties

<a name='AlifeApi.BusinessRules.WeeklyReportModels.WeeklyCustomerAnalysisStatistics.AgeAnalysis'></a>

## WeeklyCustomerAnalysisStatistics.AgeAnalysis Property

年齡分析

```csharp
public System.Collections.Generic.List<AlifeApi.BusinessRules.WeeklyReportModels.CustomerAgeAnalysis> AgeAnalysis { get; set; }
```

#### Property Value
[System.Collections.Generic.List&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Collections.Generic.List-1 'System.Collections.Generic.List`1')[CustomerAgeAnalysis](AlifeApi.BusinessRules.WeeklyReportModels.CustomerAgeAnalysis.md 'AlifeApi.BusinessRules.WeeklyReportModels.CustomerAgeAnalysis')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Collections.Generic.List-1 'System.Collections.Generic.List`1')

<a name='AlifeApi.BusinessRules.WeeklyReportModels.WeeklyCustomerAnalysisStatistics.IndustryAnalysis'></a>

## WeeklyCustomerAnalysisStatistics.IndustryAnalysis Property

行業分析

```csharp
public System.Collections.Generic.List<AlifeApi.BusinessRules.WeeklyReportModels.CustomerIndustryAnalysis> IndustryAnalysis { get; set; }
```

#### Property Value
[System.Collections.Generic.List&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Collections.Generic.List-1 'System.Collections.Generic.List`1')[CustomerIndustryAnalysis](AlifeApi.BusinessRules.WeeklyReportModels.CustomerIndustryAnalysis.md 'AlifeApi.BusinessRules.WeeklyReportModels.CustomerIndustryAnalysis')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Collections.Generic.List-1 'System.Collections.Generic.List`1')

<a name='AlifeApi.BusinessRules.WeeklyReportModels.WeeklyCustomerAnalysisStatistics.RegionAnalysis'></a>

## WeeklyCustomerAnalysisStatistics.RegionAnalysis Property

區域分析

```csharp
public System.Collections.Generic.List<AlifeApi.BusinessRules.WeeklyReportModels.CustomerRegionAnalysis> RegionAnalysis { get; set; }
```

#### Property Value
[System.Collections.Generic.List&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Collections.Generic.List-1 'System.Collections.Generic.List`1')[CustomerRegionAnalysis](AlifeApi.BusinessRules.WeeklyReportModels.CustomerRegionAnalysis.md 'AlifeApi.BusinessRules.WeeklyReportModels.CustomerRegionAnalysis')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Collections.Generic.List-1 'System.Collections.Generic.List`1')