#### [AlifeApi.BusinessRules](index.md 'index')
### [AlifeApi.BusinessRules.DailyReportModels](AlifeApi.BusinessRules.DailyReportModels.md 'AlifeApi.BusinessRules.DailyReportModels')

## DailyCustomerDetail Class

日報客戶明細

```csharp
public class DailyCustomerDetail
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; DailyCustomerDetail
### Properties

<a name='AlifeApi.BusinessRules.DailyReportModels.DailyCustomerDetail.CustomerId'></a>

## DailyCustomerDetail.CustomerId Property

客戶ID

```csharp
public int CustomerId { get; set; }
```

#### Property Value
[System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')

<a name='AlifeApi.BusinessRules.DailyReportModels.DailyCustomerDetail.CustomerName'></a>

## DailyCustomerDetail.CustomerName Property

客戶姓名

```csharp
public string? CustomerName { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.DailyReportModels.DailyCustomerDetail.HandledBy'></a>

## DailyCustomerDetail.HandledBy Property

負責人員

```csharp
public string? HandledBy { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.DailyReportModels.DailyCustomerDetail.LeadSource'></a>

## DailyCustomerDetail.LeadSource Property

客戶來源

```csharp
public string? LeadSource { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.DailyReportModels.DailyCustomerDetail.Notes'></a>

## DailyCustomerDetail.Notes Property

備註

```csharp
public string? Notes { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.DailyReportModels.DailyCustomerDetail.PhoneNumber'></a>

## DailyCustomerDetail.PhoneNumber Property

聯絡電話

```csharp
public string? PhoneNumber { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.DailyReportModels.DailyCustomerDetail.RecordTime'></a>

## DailyCustomerDetail.RecordTime Property

記錄時間

```csharp
public System.DateTime RecordTime { get; set; }
```

#### Property Value
[System.DateTime](https://docs.microsoft.com/en-us/dotnet/api/System.DateTime 'System.DateTime')

<a name='AlifeApi.BusinessRules.DailyReportModels.DailyCustomerDetail.RecordType'></a>

## DailyCustomerDetail.RecordType Property

記錄類型 (來客/來電/留單/成交)

```csharp
public string RecordType { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')