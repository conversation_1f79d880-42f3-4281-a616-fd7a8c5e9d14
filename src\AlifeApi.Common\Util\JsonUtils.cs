﻿using System.Collections.Concurrent;
using System.Reflection;
using System.Text.Json;
using System.Text.Json.Serialization;

namespace AlifeApi.Common.Util
{
    /// <summary>
    /// 用於序列化和反序列化 JSON 工具
    /// </summary>
    public static class JsonUtils
    {
        private static readonly JsonSerializerOptions DefaultOptions;
        private static readonly JsonSerializerOptions WriteIndentedOptions;
        private static readonly JsonSerializerOptions LoggingOptions;

        static JsonUtils()
        {
            DefaultOptions = new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true
            };
            DefaultOptions.Converters.Add(new DateOnlyJsonConverter());
            DefaultOptions.Converters.Add(new NullableDateOnlyJsonConverter());

            WriteIndentedOptions = new JsonSerializerOptions
            {
                WriteIndented = true,
                PropertyNameCaseInsensitive = true
            };
            WriteIndentedOptions.Converters.Add(new DateOnlyJsonConverter());
            WriteIndentedOptions.Converters.Add(new NullableDateOnlyJsonConverter());

            LoggingOptions = new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true
            };
            LoggingOptions.Converters.Add(new DateOnlyJsonConverter());
            LoggingOptions.Converters.Add(new NullableDateOnlyJsonConverter());
            LoggingOptions.Converters.Add(new LogIgnoreConverter());
        }

        /// <summary>
        /// 將指定物件序列化為 JSON 字串
        /// </summary>
        /// <typeparam name="T">要序列化的物件類型</typeparam>
        /// <param name="value">要序列化的物件</param>
        /// <param name="isWriteIndented">if set to <c>true</c> [is write indented].</param>
        /// <param name="isForLogging">若設定為 <c>true</c>，則針對日誌記錄進行序列化，並將帶有 [LogIgnore] 屬性的值清空。</param>
        /// <returns>序列化後的 JSON 字串</returns>
        /// <exception cref="JsonException">序列化過程中發生錯誤時擲出</exception>
        public static string Serialize<T>(T value, bool isWriteIndented = false, bool isForLogging = false)
        {
            var options = isForLogging ? LoggingOptions : 
                         isWriteIndented ? WriteIndentedOptions : 
                         DefaultOptions;

            return JsonSerializer.Serialize(value, options);
        }

        /// <summary>
        /// 將 JSON 字串反序列化為指定的物件型別
        /// </summary>
        /// <typeparam name="T">要反序列化的物件類型</typeparam>
        /// <param name="value">要反序列化的 JSON 字串</param>
        /// <returns>反序列化後的物件</returns>
        /// <exception cref="JsonException">反序列化過程中發生錯誤時擲出</exception>
        public static T Deserialize<T>(string value)
        {
            return JsonSerializer.Deserialize<T>(value, DefaultOptions);
        }

        private class LogIgnoreConverter : JsonConverter<object>
        {
            private static readonly ConcurrentDictionary<Type, bool> _typeLogIgnoreCache = new();

            public override bool CanConvert(Type typeToConvert)
            {
                if (typeToConvert.IsPrimitive || typeToConvert.IsEnum || typeToConvert == typeof(string) || typeToConvert == typeof(DateTime))
                {
                    return false;
                }

                if (!_typeLogIgnoreCache.TryGetValue(typeToConvert, out bool hasLogIgnore))
                {
                    hasLogIgnore = typeToConvert.GetProperties(BindingFlags.Public | BindingFlags.Instance)
                        .Any(prop => Attribute.IsDefined(prop, typeof(LogIgnoreAttribute)));

                    _typeLogIgnoreCache[typeToConvert] = hasLogIgnore;

                    return hasLogIgnore;
                }

                return false;
            }

            public override object Read(ref Utf8JsonReader reader, Type typeToConvert, JsonSerializerOptions options)
            {
                return JsonSerializer.Deserialize(ref reader, typeToConvert, options);
            }

            public override void Write(Utf8JsonWriter writer, object value, JsonSerializerOptions options)
            {
                Type type = value.GetType();
                writer.WriteStartObject();

                foreach (var prop in type.GetProperties(BindingFlags.Public | BindingFlags.Instance))
                {
                    object propValue = prop.GetValue(value);

                    writer.WritePropertyName(prop.Name);

                    if (propValue is null || Attribute.IsDefined(prop, typeof(LogIgnoreAttribute)))
                    {
                        writer.WriteNullValue();
                    }
                    else
                    {
                        JsonSerializer.Serialize(writer, propValue, propValue.GetType(), options);
                    }
                }

                writer.WriteEndObject();
            }
        }
    }
}
