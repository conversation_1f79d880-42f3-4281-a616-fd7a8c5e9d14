#### [AlifeApi.DataAccess](index.md 'index')
### [AlifeApi.DataAccess.ProjectContext](AlifeApi.DataAccess.ProjectContext.md 'AlifeApi.DataAccess.ProjectContext')

## SysMenuFunc Class

系統選單資料

```csharp
public class SysMenuFunc
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; SysMenuFunc
### Properties

<a name='AlifeApi.DataAccess.ProjectContext.SysMenuFunc.FuncId'></a>

## SysMenuFunc.FuncId Property

系統項目識別編號

```csharp
public string FuncId { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContext.SysMenuFunc.FuncName'></a>

## SysMenuFunc.FuncName Property

系統項目名稱

```csharp
public string FuncName { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContext.SysMenuFunc.FuncOrder'></a>

## SysMenuFunc.FuncOrder Property

項目排序

```csharp
public int FuncOrder { get; set; }
```

#### Property Value
[System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')

<a name='AlifeApi.DataAccess.ProjectContext.SysMenuFunc.ParentFuncId'></a>

## SysMenuFunc.ParentFuncId Property

項目路徑

```csharp
public string ParentFuncId { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContext.SysMenuFunc.System'></a>

## SysMenuFunc.System Property

系統名稱

```csharp
public string System { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')