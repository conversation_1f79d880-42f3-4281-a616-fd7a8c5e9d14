﻿CREATE TABLE [dbo].[SYS_ApiLog] (
    [Id]             BIGINT          IDENTITY (1, 1) NOT NULL,
    [System]         VARCHAR (30)    NOT NULL,
    [InputData]      NVARCHAR (MAX)  NOT NULL,
    [Headers]        NVARCHAR (4000) NOT NULL,
    [OutputData]     NVARCHAR (MAX)  NOT NULL,
    [ExecutedTime]   DATETIME        NOT NULL,
    [ControllerName] VARCHAR (100)   NOT NULL,
    [ActionName]     VARCHAR (100)   NOT NULL,
    [Seconds]        DECIMAL (5, 2)  NOT NULL,
    [Source]         VARCHAR (200)   NOT NULL,
    [Exception]      NVARCHAR (MAX)  NOT NULL,
    [SessionId]      VARCHAR (50)    NOT NULL,
    CONSTRAINT [PK_SYS_ApiLog] PRIMARY KEY CLUSTERED ([Id] ASC)
);


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'系統名稱', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'SYS_ApiLog', @level2type = N'COLUMN', @level2name = N'System';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'輸入資料', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'SYS_ApiLog', @level2type = N'COLUMN', @level2name = N'InputData';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'請求標頭', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'SYS_ApiLog', @level2type = N'COLUMN', @level2name = N'Headers';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'輸出資料', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'SYS_ApiLog', @level2type = N'COLUMN', @level2name = N'OutputData';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'操作時間', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'SYS_ApiLog', @level2type = N'COLUMN', @level2name = N'ExecutedTime';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'控制器名稱', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'SYS_ApiLog', @level2type = N'COLUMN', @level2name = N'ControllerName';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'執行方法名稱', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'SYS_ApiLog', @level2type = N'COLUMN', @level2name = N'ActionName';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'執行時間（秒）', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'SYS_ApiLog', @level2type = N'COLUMN', @level2name = N'Seconds';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'請求來源', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'SYS_ApiLog', @level2type = N'COLUMN', @level2name = N'Source';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'錯誤異常', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'SYS_ApiLog', @level2type = N'COLUMN', @level2name = N'Exception';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'Session ID', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'SYS_ApiLog', @level2type = N'COLUMN', @level2name = N'SessionId';

