#### [AlifeApi.BusinessRules](index.md 'index')
### [AlifeApi.BusinessRules.WeeklyReportModels](AlifeApi.BusinessRules.WeeklyReportModels.md 'AlifeApi.BusinessRules.WeeklyReportModels')

## WeeklyReportService Class

週報統計服務

```csharp
public class WeeklyReportService : AlifeApi.BusinessRules.Infrastructure.ServiceBase<AlifeApi.DataAccess.ProjectContent.alifeContext>
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; [AlifeApi.Common.InfrastructureBase](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.Common.InfrastructureBase 'AlifeApi.Common.InfrastructureBase') &#129106; [AlifeApi.BusinessRules.Infrastructure.ServiceBase&lt;](AlifeApi.BusinessRules.Infrastructure.ServiceBase_TDbContext_.md 'AlifeApi.BusinessRules.Infrastructure.ServiceBase<TDbContext>')[AlifeApi.DataAccess.ProjectContent.alifeContext](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.DataAccess.ProjectContent.alifeContext 'AlifeApi.DataAccess.ProjectContent.alifeContext')[&gt;](AlifeApi.BusinessRules.Infrastructure.ServiceBase_TDbContext_.md 'AlifeApi.BusinessRules.Infrastructure.ServiceBase<TDbContext>') &#129106; WeeklyReportService
### Constructors

<a name='AlifeApi.BusinessRules.WeeklyReportModels.WeeklyReportService.WeeklyReportService(System.IServiceProvider,AlifeApi.DataAccess.ProjectContent.alifeContext)'></a>

## WeeklyReportService(IServiceProvider, alifeContext) Constructor

建構函數

```csharp
public WeeklyReportService(System.IServiceProvider serviceProvider, AlifeApi.DataAccess.ProjectContent.alifeContext dbContext);
```
#### Parameters

<a name='AlifeApi.BusinessRules.WeeklyReportModels.WeeklyReportService.WeeklyReportService(System.IServiceProvider,AlifeApi.DataAccess.ProjectContent.alifeContext).serviceProvider'></a>

`serviceProvider` [System.IServiceProvider](https://docs.microsoft.com/en-us/dotnet/api/System.IServiceProvider 'System.IServiceProvider')

<a name='AlifeApi.BusinessRules.WeeklyReportModels.WeeklyReportService.WeeklyReportService(System.IServiceProvider,AlifeApi.DataAccess.ProjectContent.alifeContext).dbContext'></a>

`dbContext` [AlifeApi.DataAccess.ProjectContent.alifeContext](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.DataAccess.ProjectContent.alifeContext 'AlifeApi.DataAccess.ProjectContent.alifeContext')
### Methods

<a name='AlifeApi.BusinessRules.WeeklyReportModels.WeeklyReportService.AnalyzeCustomersByAge(System.Collections.Generic.List_dynamic_)'></a>

## WeeklyReportService.AnalyzeCustomersByAge(List<dynamic>) Method

按年齡分析客戶

```csharp
private System.Collections.Generic.List<AlifeApi.BusinessRules.WeeklyReportModels.CustomerAgeAnalysis> AnalyzeCustomersByAge(System.Collections.Generic.List<dynamic> customers);
```
#### Parameters

<a name='AlifeApi.BusinessRules.WeeklyReportModels.WeeklyReportService.AnalyzeCustomersByAge(System.Collections.Generic.List_dynamic_).customers'></a>

`customers` [System.Collections.Generic.List&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Collections.Generic.List-1 'System.Collections.Generic.List`1')[dynamic](https://docs.microsoft.com/en-us/dotnet/csharp/programming-guide/types/using-type-dynamic 'https://docs.microsoft.com/en-us/dotnet/csharp/programming-guide/types/using-type-dynamic')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Collections.Generic.List-1 'System.Collections.Generic.List`1')

#### Returns
[System.Collections.Generic.List&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Collections.Generic.List-1 'System.Collections.Generic.List`1')[CustomerAgeAnalysis](AlifeApi.BusinessRules.WeeklyReportModels.CustomerAgeAnalysis.md 'AlifeApi.BusinessRules.WeeklyReportModels.CustomerAgeAnalysis')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Collections.Generic.List-1 'System.Collections.Generic.List`1')

<a name='AlifeApi.BusinessRules.WeeklyReportModels.WeeklyReportService.AnalyzeCustomersByIndustry(System.Collections.Generic.List_dynamic_)'></a>

## WeeklyReportService.AnalyzeCustomersByIndustry(List<dynamic>) Method

按行業分析客戶

```csharp
private System.Collections.Generic.List<AlifeApi.BusinessRules.WeeklyReportModels.CustomerIndustryAnalysis> AnalyzeCustomersByIndustry(System.Collections.Generic.List<dynamic> customers);
```
#### Parameters

<a name='AlifeApi.BusinessRules.WeeklyReportModels.WeeklyReportService.AnalyzeCustomersByIndustry(System.Collections.Generic.List_dynamic_).customers'></a>

`customers` [System.Collections.Generic.List&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Collections.Generic.List-1 'System.Collections.Generic.List`1')[dynamic](https://docs.microsoft.com/en-us/dotnet/csharp/programming-guide/types/using-type-dynamic 'https://docs.microsoft.com/en-us/dotnet/csharp/programming-guide/types/using-type-dynamic')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Collections.Generic.List-1 'System.Collections.Generic.List`1')

#### Returns
[System.Collections.Generic.List&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Collections.Generic.List-1 'System.Collections.Generic.List`1')[CustomerIndustryAnalysis](AlifeApi.BusinessRules.WeeklyReportModels.CustomerIndustryAnalysis.md 'AlifeApi.BusinessRules.WeeklyReportModels.CustomerIndustryAnalysis')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Collections.Generic.List-1 'System.Collections.Generic.List`1')

<a name='AlifeApi.BusinessRules.WeeklyReportModels.WeeklyReportService.AnalyzeCustomersByRegion(System.Collections.Generic.List_dynamic_)'></a>

## WeeklyReportService.AnalyzeCustomersByRegion(List<dynamic>) Method

按區域分析客戶

```csharp
private System.Collections.Generic.List<AlifeApi.BusinessRules.WeeklyReportModels.CustomerRegionAnalysis> AnalyzeCustomersByRegion(System.Collections.Generic.List<dynamic> customers);
```
#### Parameters

<a name='AlifeApi.BusinessRules.WeeklyReportModels.WeeklyReportService.AnalyzeCustomersByRegion(System.Collections.Generic.List_dynamic_).customers'></a>

`customers` [System.Collections.Generic.List&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Collections.Generic.List-1 'System.Collections.Generic.List`1')[dynamic](https://docs.microsoft.com/en-us/dotnet/csharp/programming-guide/types/using-type-dynamic 'https://docs.microsoft.com/en-us/dotnet/csharp/programming-guide/types/using-type-dynamic')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Collections.Generic.List-1 'System.Collections.Generic.List`1')

#### Returns
[System.Collections.Generic.List&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Collections.Generic.List-1 'System.Collections.Generic.List`1')[CustomerRegionAnalysis](AlifeApi.BusinessRules.WeeklyReportModels.CustomerRegionAnalysis.md 'AlifeApi.BusinessRules.WeeklyReportModels.CustomerRegionAnalysis')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Collections.Generic.List-1 'System.Collections.Generic.List`1')

<a name='AlifeApi.BusinessRules.WeeklyReportModels.WeeklyReportService.CalculateAge(System.Nullable_System.DateTime_)'></a>

## WeeklyReportService.CalculateAge(Nullable<DateTime>) Method

計算年齡

```csharp
private static int CalculateAge(System.Nullable<System.DateTime> birthDate);
```
#### Parameters

<a name='AlifeApi.BusinessRules.WeeklyReportModels.WeeklyReportService.CalculateAge(System.Nullable_System.DateTime_).birthDate'></a>

`birthDate` [System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.DateTime](https://docs.microsoft.com/en-us/dotnet/api/System.DateTime 'System.DateTime')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

#### Returns
[System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')

<a name='AlifeApi.BusinessRules.WeeklyReportModels.WeeklyReportService.CreateParkingSalesStatistics(System.Collections.Generic.List_dynamic_,System.Collections.Generic.List_dynamic_)'></a>

## WeeklyReportService.CreateParkingSalesStatistics(List<dynamic>, List<dynamic>) Method

建立車位銷售統計

```csharp
private AlifeApi.BusinessRules.WeeklyReportModels.ParkingSalesStatistics CreateParkingSalesStatistics(System.Collections.Generic.List<dynamic> parkingStats, System.Collections.Generic.List<dynamic> signedParking);
```
#### Parameters

<a name='AlifeApi.BusinessRules.WeeklyReportModels.WeeklyReportService.CreateParkingSalesStatistics(System.Collections.Generic.List_dynamic_,System.Collections.Generic.List_dynamic_).parkingStats'></a>

`parkingStats` [System.Collections.Generic.List&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Collections.Generic.List-1 'System.Collections.Generic.List`1')[dynamic](https://docs.microsoft.com/en-us/dotnet/csharp/programming-guide/types/using-type-dynamic 'https://docs.microsoft.com/en-us/dotnet/csharp/programming-guide/types/using-type-dynamic')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Collections.Generic.List-1 'System.Collections.Generic.List`1')

<a name='AlifeApi.BusinessRules.WeeklyReportModels.WeeklyReportService.CreateParkingSalesStatistics(System.Collections.Generic.List_dynamic_,System.Collections.Generic.List_dynamic_).signedParking'></a>

`signedParking` [System.Collections.Generic.List&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Collections.Generic.List-1 'System.Collections.Generic.List`1')[dynamic](https://docs.microsoft.com/en-us/dotnet/csharp/programming-guide/types/using-type-dynamic 'https://docs.microsoft.com/en-us/dotnet/csharp/programming-guide/types/using-type-dynamic')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Collections.Generic.List-1 'System.Collections.Generic.List`1')

#### Returns
[ParkingSalesStatistics](AlifeApi.BusinessRules.WeeklyReportModels.ParkingSalesStatistics.md 'AlifeApi.BusinessRules.WeeklyReportModels.ParkingSalesStatistics')

<a name='AlifeApi.BusinessRules.WeeklyReportModels.WeeklyReportService.CreateUnitSalesStatistics(System.Collections.Generic.List_dynamic_,System.Collections.Generic.List_dynamic_)'></a>

## WeeklyReportService.CreateUnitSalesStatistics(List<dynamic>, List<dynamic>) Method

建立房屋銷售統計

```csharp
private AlifeApi.BusinessRules.WeeklyReportModels.UnitSalesStatistics CreateUnitSalesStatistics(System.Collections.Generic.List<dynamic> unitStats, System.Collections.Generic.List<dynamic> signedUnits);
```
#### Parameters

<a name='AlifeApi.BusinessRules.WeeklyReportModels.WeeklyReportService.CreateUnitSalesStatistics(System.Collections.Generic.List_dynamic_,System.Collections.Generic.List_dynamic_).unitStats'></a>

`unitStats` [System.Collections.Generic.List&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Collections.Generic.List-1 'System.Collections.Generic.List`1')[dynamic](https://docs.microsoft.com/en-us/dotnet/csharp/programming-guide/types/using-type-dynamic 'https://docs.microsoft.com/en-us/dotnet/csharp/programming-guide/types/using-type-dynamic')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Collections.Generic.List-1 'System.Collections.Generic.List`1')

<a name='AlifeApi.BusinessRules.WeeklyReportModels.WeeklyReportService.CreateUnitSalesStatistics(System.Collections.Generic.List_dynamic_,System.Collections.Generic.List_dynamic_).signedUnits'></a>

`signedUnits` [System.Collections.Generic.List&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Collections.Generic.List-1 'System.Collections.Generic.List`1')[dynamic](https://docs.microsoft.com/en-us/dotnet/csharp/programming-guide/types/using-type-dynamic 'https://docs.microsoft.com/en-us/dotnet/csharp/programming-guide/types/using-type-dynamic')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Collections.Generic.List-1 'System.Collections.Generic.List`1')

#### Returns
[UnitSalesStatistics](AlifeApi.BusinessRules.WeeklyReportModels.UnitSalesStatistics.md 'AlifeApi.BusinessRules.WeeklyReportModels.UnitSalesStatistics')

<a name='AlifeApi.BusinessRules.WeeklyReportModels.WeeklyReportService.CreateWeeklyStatisticItem(string,System.Collections.Generic.List_AlifeApi.BusinessRules.WeeklyReportModels.DailyStatistic_,System.Collections.Generic.Dictionary_string,int_)'></a>

## WeeklyReportService.CreateWeeklyStatisticItem(string, List<DailyStatistic>, Dictionary<string,int>) Method

建立週統計項目

```csharp
private AlifeApi.BusinessRules.WeeklyReportModels.WeeklyStatisticItem CreateWeeklyStatisticItem(string recordType, System.Collections.Generic.List<AlifeApi.BusinessRules.WeeklyReportModels.DailyStatistic> dailyStats, System.Collections.Generic.Dictionary<string,int> previousStats);
```
#### Parameters

<a name='AlifeApi.BusinessRules.WeeklyReportModels.WeeklyReportService.CreateWeeklyStatisticItem(string,System.Collections.Generic.List_AlifeApi.BusinessRules.WeeklyReportModels.DailyStatistic_,System.Collections.Generic.Dictionary_string,int_).recordType'></a>

`recordType` [System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.WeeklyReportModels.WeeklyReportService.CreateWeeklyStatisticItem(string,System.Collections.Generic.List_AlifeApi.BusinessRules.WeeklyReportModels.DailyStatistic_,System.Collections.Generic.Dictionary_string,int_).dailyStats'></a>

`dailyStats` [System.Collections.Generic.List&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Collections.Generic.List-1 'System.Collections.Generic.List`1')[DailyStatistic](AlifeApi.BusinessRules.WeeklyReportModels.DailyStatistic.md 'AlifeApi.BusinessRules.WeeklyReportModels.DailyStatistic')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Collections.Generic.List-1 'System.Collections.Generic.List`1')

<a name='AlifeApi.BusinessRules.WeeklyReportModels.WeeklyReportService.CreateWeeklyStatisticItem(string,System.Collections.Generic.List_AlifeApi.BusinessRules.WeeklyReportModels.DailyStatistic_,System.Collections.Generic.Dictionary_string,int_).previousStats'></a>

`previousStats` [System.Collections.Generic.Dictionary&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Collections.Generic.Dictionary-2 'System.Collections.Generic.Dictionary`2')[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')[,](https://docs.microsoft.com/en-us/dotnet/api/System.Collections.Generic.Dictionary-2 'System.Collections.Generic.Dictionary`2')[System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Collections.Generic.Dictionary-2 'System.Collections.Generic.Dictionary`2')

#### Returns
[WeeklyStatisticItem](AlifeApi.BusinessRules.WeeklyReportModels.WeeklyStatisticItem.md 'AlifeApi.BusinessRules.WeeklyReportModels.WeeklyStatisticItem')

<a name='AlifeApi.BusinessRules.WeeklyReportModels.WeeklyReportService.GetAgeRange(int)'></a>

## WeeklyReportService.GetAgeRange(int) Method

取得年齡區間

```csharp
private static string GetAgeRange(int age);
```
#### Parameters

<a name='AlifeApi.BusinessRules.WeeklyReportModels.WeeklyReportService.GetAgeRange(int).age'></a>

`age` [System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')

#### Returns
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.WeeklyReportModels.WeeklyReportService.GetBusinessResultStatisticsAsync(string,System.DateTime,System.DateTime)'></a>

## WeeklyReportService.GetBusinessResultStatisticsAsync(string, DateTime, DateTime) Method

取得經營成果統計

```csharp
private System.Threading.Tasks.Task<AlifeApi.BusinessRules.WeeklyReportModels.WeeklyBusinessResultStatistics> GetBusinessResultStatisticsAsync(string siteCode, System.DateTime startDate, System.DateTime endDate);
```
#### Parameters

<a name='AlifeApi.BusinessRules.WeeklyReportModels.WeeklyReportService.GetBusinessResultStatisticsAsync(string,System.DateTime,System.DateTime).siteCode'></a>

`siteCode` [System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.WeeklyReportModels.WeeklyReportService.GetBusinessResultStatisticsAsync(string,System.DateTime,System.DateTime).startDate'></a>

`startDate` [System.DateTime](https://docs.microsoft.com/en-us/dotnet/api/System.DateTime 'System.DateTime')

<a name='AlifeApi.BusinessRules.WeeklyReportModels.WeeklyReportService.GetBusinessResultStatisticsAsync(string,System.DateTime,System.DateTime).endDate'></a>

`endDate` [System.DateTime](https://docs.microsoft.com/en-us/dotnet/api/System.DateTime 'System.DateTime')

#### Returns
[System.Threading.Tasks.Task&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')[WeeklyBusinessResultStatistics](AlifeApi.BusinessRules.WeeklyReportModels.WeeklyBusinessResultStatistics.md 'AlifeApi.BusinessRules.WeeklyReportModels.WeeklyBusinessResultStatistics')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')

<a name='AlifeApi.BusinessRules.WeeklyReportModels.WeeklyReportService.GetChineseDayOfWeek(System.DateTime)'></a>

## WeeklyReportService.GetChineseDayOfWeek(DateTime) Method

取得中文星期顯示

```csharp
private static string GetChineseDayOfWeek(System.DateTime date);
```
#### Parameters

<a name='AlifeApi.BusinessRules.WeeklyReportModels.WeeklyReportService.GetChineseDayOfWeek(System.DateTime).date'></a>

`date` [System.DateTime](https://docs.microsoft.com/en-us/dotnet/api/System.DateTime 'System.DateTime')

#### Returns
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.WeeklyReportModels.WeeklyReportService.GetCustomerAnalysisStatisticsAsync(string,System.DateTime,System.DateTime)'></a>

## WeeklyReportService.GetCustomerAnalysisStatisticsAsync(string, DateTime, DateTime) Method

取得客戶分析統計

```csharp
private System.Threading.Tasks.Task<AlifeApi.BusinessRules.WeeklyReportModels.WeeklyCustomerAnalysisStatistics> GetCustomerAnalysisStatisticsAsync(string siteCode, System.DateTime startDate, System.DateTime endDate);
```
#### Parameters

<a name='AlifeApi.BusinessRules.WeeklyReportModels.WeeklyReportService.GetCustomerAnalysisStatisticsAsync(string,System.DateTime,System.DateTime).siteCode'></a>

`siteCode` [System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.WeeklyReportModels.WeeklyReportService.GetCustomerAnalysisStatisticsAsync(string,System.DateTime,System.DateTime).startDate'></a>

`startDate` [System.DateTime](https://docs.microsoft.com/en-us/dotnet/api/System.DateTime 'System.DateTime')

<a name='AlifeApi.BusinessRules.WeeklyReportModels.WeeklyReportService.GetCustomerAnalysisStatisticsAsync(string,System.DateTime,System.DateTime).endDate'></a>

`endDate` [System.DateTime](https://docs.microsoft.com/en-us/dotnet/api/System.DateTime 'System.DateTime')

#### Returns
[System.Threading.Tasks.Task&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')[WeeklyCustomerAnalysisStatistics](AlifeApi.BusinessRules.WeeklyReportModels.WeeklyCustomerAnalysisStatistics.md 'AlifeApi.BusinessRules.WeeklyReportModels.WeeklyCustomerAnalysisStatistics')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')

<a name='AlifeApi.BusinessRules.WeeklyReportModels.WeeklyReportService.GetDailyStatsInPeriodAsync(string,System.DateTime,System.DateTime)'></a>

## WeeklyReportService.GetDailyStatsInPeriodAsync(string, DateTime, DateTime) Method

取得期間內每日統計

```csharp
private System.Threading.Tasks.Task<System.Collections.Generic.List<AlifeApi.BusinessRules.WeeklyReportModels.DailyStatistic>> GetDailyStatsInPeriodAsync(string siteCode, System.DateTime startDate, System.DateTime endDate);
```
#### Parameters

<a name='AlifeApi.BusinessRules.WeeklyReportModels.WeeklyReportService.GetDailyStatsInPeriodAsync(string,System.DateTime,System.DateTime).siteCode'></a>

`siteCode` [System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.WeeklyReportModels.WeeklyReportService.GetDailyStatsInPeriodAsync(string,System.DateTime,System.DateTime).startDate'></a>

`startDate` [System.DateTime](https://docs.microsoft.com/en-us/dotnet/api/System.DateTime 'System.DateTime')

<a name='AlifeApi.BusinessRules.WeeklyReportModels.WeeklyReportService.GetDailyStatsInPeriodAsync(string,System.DateTime,System.DateTime).endDate'></a>

`endDate` [System.DateTime](https://docs.microsoft.com/en-us/dotnet/api/System.DateTime 'System.DateTime')

#### Returns
[System.Threading.Tasks.Task&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')[System.Collections.Generic.List&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Collections.Generic.List-1 'System.Collections.Generic.List`1')[DailyStatistic](AlifeApi.BusinessRules.WeeklyReportModels.DailyStatistic.md 'AlifeApi.BusinessRules.WeeklyReportModels.DailyStatistic')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Collections.Generic.List-1 'System.Collections.Generic.List`1')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')

<a name='AlifeApi.BusinessRules.WeeklyReportModels.WeeklyReportService.GetPeriodStatsAsync(string,System.DateTime,System.DateTime)'></a>

## WeeklyReportService.GetPeriodStatsAsync(string, DateTime, DateTime) Method

取得期間統計

```csharp
private System.Threading.Tasks.Task<System.Collections.Generic.Dictionary<string,int>> GetPeriodStatsAsync(string siteCode, System.DateTime startDate, System.DateTime endDate);
```
#### Parameters

<a name='AlifeApi.BusinessRules.WeeklyReportModels.WeeklyReportService.GetPeriodStatsAsync(string,System.DateTime,System.DateTime).siteCode'></a>

`siteCode` [System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.WeeklyReportModels.WeeklyReportService.GetPeriodStatsAsync(string,System.DateTime,System.DateTime).startDate'></a>

`startDate` [System.DateTime](https://docs.microsoft.com/en-us/dotnet/api/System.DateTime 'System.DateTime')

<a name='AlifeApi.BusinessRules.WeeklyReportModels.WeeklyReportService.GetPeriodStatsAsync(string,System.DateTime,System.DateTime).endDate'></a>

`endDate` [System.DateTime](https://docs.microsoft.com/en-us/dotnet/api/System.DateTime 'System.DateTime')

#### Returns
[System.Threading.Tasks.Task&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')[System.Collections.Generic.Dictionary&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Collections.Generic.Dictionary-2 'System.Collections.Generic.Dictionary`2')[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')[,](https://docs.microsoft.com/en-us/dotnet/api/System.Collections.Generic.Dictionary-2 'System.Collections.Generic.Dictionary`2')[System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Collections.Generic.Dictionary-2 'System.Collections.Generic.Dictionary`2')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')

<a name='AlifeApi.BusinessRules.WeeklyReportModels.WeeklyReportService.GetSalesStatisticsAsync(string)'></a>

## WeeklyReportService.GetSalesStatisticsAsync(string) Method

取得銷售統計

```csharp
private System.Threading.Tasks.Task<AlifeApi.BusinessRules.WeeklyReportModels.WeeklySalesStatistics> GetSalesStatisticsAsync(string siteCode);
```
#### Parameters

<a name='AlifeApi.BusinessRules.WeeklyReportModels.WeeklyReportService.GetSalesStatisticsAsync(string).siteCode'></a>

`siteCode` [System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

#### Returns
[System.Threading.Tasks.Task&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')[WeeklySalesStatistics](AlifeApi.BusinessRules.WeeklyReportModels.WeeklySalesStatistics.md 'AlifeApi.BusinessRules.WeeklyReportModels.WeeklySalesStatistics')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')

<a name='AlifeApi.BusinessRules.WeeklyReportModels.WeeklyReportService.GetWeekDateRange(int,int)'></a>

## WeeklyReportService.GetWeekDateRange(int, int) Method

根據年份和週次計算日期範圍

```csharp
private static (System.DateTime startDate,System.DateTime endDate) GetWeekDateRange(int year, int weekNumber);
```
#### Parameters

<a name='AlifeApi.BusinessRules.WeeklyReportModels.WeeklyReportService.GetWeekDateRange(int,int).year'></a>

`year` [System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')

<a name='AlifeApi.BusinessRules.WeeklyReportModels.WeeklyReportService.GetWeekDateRange(int,int).weekNumber'></a>

`weekNumber` [System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')

#### Returns
[&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.ValueTuple 'System.ValueTuple')[System.DateTime](https://docs.microsoft.com/en-us/dotnet/api/System.DateTime 'System.DateTime')[,](https://docs.microsoft.com/en-us/dotnet/api/System.ValueTuple 'System.ValueTuple')[System.DateTime](https://docs.microsoft.com/en-us/dotnet/api/System.DateTime 'System.DateTime')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.ValueTuple 'System.ValueTuple')

<a name='AlifeApi.BusinessRules.WeeklyReportModels.WeeklyReportService.GetWeeklyReportAsync(AlifeApi.BusinessRules.WeeklyReportModels.WeeklyReportQueryInput)'></a>

## WeeklyReportService.GetWeeklyReportAsync(WeeklyReportQueryInput) Method

取得週報統計

```csharp
public System.Threading.Tasks.Task<AlifeApi.BusinessRules.WeeklyReportModels.WeeklyReportOutput> GetWeeklyReportAsync(AlifeApi.BusinessRules.WeeklyReportModels.WeeklyReportQueryInput input);
```
#### Parameters

<a name='AlifeApi.BusinessRules.WeeklyReportModels.WeeklyReportService.GetWeeklyReportAsync(AlifeApi.BusinessRules.WeeklyReportModels.WeeklyReportQueryInput).input'></a>

`input` [WeeklyReportQueryInput](AlifeApi.BusinessRules.WeeklyReportModels.WeeklyReportQueryInput.md 'AlifeApi.BusinessRules.WeeklyReportModels.WeeklyReportQueryInput')

查詢條件

#### Returns
[System.Threading.Tasks.Task&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')[WeeklyReportOutput](AlifeApi.BusinessRules.WeeklyReportModels.WeeklyReportOutput.md 'AlifeApi.BusinessRules.WeeklyReportModels.WeeklyReportOutput')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')  
週報統計結果