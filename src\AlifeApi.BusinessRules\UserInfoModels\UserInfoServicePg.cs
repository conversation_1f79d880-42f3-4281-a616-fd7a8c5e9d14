﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using AlifeApi.BusinessRules.Infrastructure;
using AlifeApi.BusinessRules.SiteModels;
using AlifeApi.Common.DataAnnotations;
using AlifeApi.DataAccess.ProjectContent;
using Microsoft.EntityFrameworkCore;

namespace AlifeApi.BusinessRules.UserInfoModels
{
    public class UserInfoServicePg : ServiceBase<alifeContext>
    {
        private readonly PasswordManager _passwordManager;
        public UserInfoServicePg(IServiceProvider serviceProvider, alifeContext dbContext, PasswordManager passwordManager) : base(serviceProvider, dbContext)
        {
            _passwordManager = passwordManager ?? throw new ArgumentNullException(nameof(passwordManager));
        }

        /// <summary>
        /// 帳號權限管理-帳號新增
        /// </summary>
        /// <param name="input">使用者新增輸入資料</param>
        /// <returns>執行結果</returns>
        /// <exception cref="ArgumentNullException">input</exception>
        public async Task<UserInfoUpdateOutput> CreateUserInfoAsync(UserInfoCreateInputPg input)
        {
            ArgumentNullException.ThrowIfNull(input, nameof(input));

            UserInfoUpdateOutput output = new();
            bool isDefaultPasswordUsed = false;

            // 如果沒有指定密碼，使用加密後的身分證字號作為預設密碼
            if (string.IsNullOrEmpty(input.Password))
            {
                input.Password = input.Identity;
                isDefaultPasswordUsed = true;
            }

            if (await Db.UserInfos.AnyAsync(x => x.UserInfoId == input.UserInfoId))
            {
                output.Response = Message.AccountAlreadyExist;
                return output;
            }

            // 檢查部門是否存在
            if (!await Db.Departments.AnyAsync(x => x.CompanyId == input.CompanyId && x.DepartmentId == input.DepartmentId))
            {
                throw new Exception("該部門不存在");
            }

            // 檢查職稱是否存在
            if (!await Db.JobTitles.AnyAsync(x => x.DepartmentId == input.DepartmentId && x.JobTitleId == input.JobTitleId))
            {
                throw new Exception("該職稱不存在");
            }

            var user = new UserInfo
            {
                UserInfoId = input.UserInfoId,
                CompanyId = input.CompanyId,
                Password = _passwordManager.HashPassword(input.Password),
                Name = input.Name,
                Gender = input.Gender,
                Identity = input.Identity,
                BirthDate = input.BirthDate,
                TelephoneNumber = input.TelephoneNumber,
                MobileNumber = input.MobileNumber,
                Email = input.Email,
                RegisteredAddress = input.RegisteredAddress,
                MailingAddress = input.MailingAddress,
                EmergencyContactName = input.EmergencyContactName,
                EmergencyContactPhone = input.EmergencyContactPhone,
                EmergencyContactRelation = input.EmergencyContactRelation,
                ServiceUnit = input.ServiceUnit,
                HireDate = input.HireDate,
                Status = input.Status ?? false,
                IsInside = input.IsInside ?? false,
                IsM365 = input.IsM365 ?? false,
                IsEmailNotificationEnabled = input.IsEmailNotificationEnabled ?? false,
                CreatedTime = DateTime.Now,
                CreatedUserInfoId = CurrentUser.UserId,
                UpdatedTime = DateTime.Now,
                UpdatedUserInfoId = CurrentUser.UserId
            };

            Db.UserInfos.Add(user);

            // 新增部門關聯
            Db.UserDepartments.Add(new UserDepartment
            {
                UserInfoId = user.UserInfoId,
                DepartmentId = input.DepartmentId,
                IsPrimary = true
            });

            // 新增職稱關聯
            Db.UserJobTitles.Add(new UserJobTitle
            {
                UserInfoId = user.UserInfoId,
                JobTitleId = input.JobTitleId,
                IsPrimary = true
            });

            // 新增角色關聯
            foreach (string roleGroupId in input.RoleGroupIds.Distinct())
            {
                if (await Db.SysRoleGroups.AnyAsync(x => x.System == "Alife" && x.RoleGroupId == roleGroupId))
                {
                    Db.SysRoleGroupUsers.Add(new SysRoleGroupUser
                    {
                        System = "Alife",
                        RoleGroupId = roleGroupId,
                        UserInfoId = user.UserInfoId
                    });
                }
            }

            if (!isDefaultPasswordUsed)
            {
                await _passwordManager.CreatePasswordHistoryAsync(input.UserInfoId, input.Password);
            }

            // **一次性儲存所有變更**
            try
            {
                await Db.SaveChangesAsync();
            }
            catch (DbUpdateException ex)
            {
                // 捕捉並記錄詳細錯誤
                Console.WriteLine("Error saving changes: " + ex.ToString());
                throw;
            }

            output.Response = Message.Success;
            return output;
        }

        /// <summary>
        /// 修改使用者資料
        /// </summary>
        /// <param name="input">使用者更新輸入資料</param>
        /// <returns>執行結果</returns>
        /// <exception cref="ArgumentNullException">input</exception>
        public async Task<UserInfoUpdateOutput> UpdateUserInfoAsync(UserInfoUpdateInputPg input)
        {
            ArgumentNullException.ThrowIfNull(input, nameof(input));

            UserInfoUpdateOutput output = new();

            var user = await Db.UserInfos
                .Include(x => x.UserDepartments)
                .Include(x => x.UserJobTitles)
                .Include(x => x.SysRoleGroupUsers)
                .FirstOrDefaultAsync(x => x.UserInfoId == input.UserInfoId);

            if (user == null)
            {
                output.Response = Message.AccountFail;
                return output;
            }

            // 檢查部門是否存在
            if (!await Db.Departments.AnyAsync(x => x.CompanyId == input.CompanyId && x.DepartmentId == input.DepartmentId))
            {
                throw new Exception("該部門不存在");
            }

            // 檢查職稱是否存在
            if (!await Db.JobTitles.AnyAsync(x => x.DepartmentId == input.DepartmentId && x.JobTitleId == input.JobTitleId))
            {
                throw new Exception("該職稱不存在");
            }

            // 更新基本資料
            user.CompanyId = input.CompanyId;
            user.Name = input.Name;
            user.Gender = input.Gender;
            user.BirthDate = input.BirthDate;
            user.TelephoneNumber = input.TelephoneNumber;
            user.MobileNumber = input.MobileNumber;
            user.Email = input.Email;
            user.RegisteredAddress = input.RegisteredAddress;
            user.MailingAddress = input.MailingAddress;
            user.EmergencyContactName = input.EmergencyContactName;
            user.EmergencyContactPhone = input.EmergencyContactPhone;
            user.EmergencyContactRelation = input.EmergencyContactRelation;
            user.ServiceUnit = input.ServiceUnit;
            user.HireDate = input.HireDate;
            user.IsInside = input.IsInside ?? user.IsInside;
            user.IsM365 = input.IsM365 ?? user.IsM365;
            user.IsEmailNotificationEnabled = input.IsEmailNotificationEnabled ?? user.IsEmailNotificationEnabled;
            user.UpdatedTime = DateTime.Now;
            user.UpdatedUserInfoId = CurrentUser.UserId;

            if (input.Status.HasValue)
            {
                user.Status = input.Status.Value;
            }

            // 更新部門關聯
            var primaryDepartment = user.UserDepartments.FirstOrDefault(x => x.IsPrimary == true);
            if (primaryDepartment != null)
            {
                primaryDepartment.DepartmentId = input.DepartmentId;
            }
            else
            {
                Db.UserDepartments.Add(new UserDepartment
                {
                    UserInfoId = user.UserInfoId,
                    DepartmentId = input.DepartmentId,
                    IsPrimary = true
                });
            }

            // 更新職稱關聯
            var primaryJobTitle = user.UserJobTitles.FirstOrDefault(x => x.IsPrimary == true);
            if (primaryJobTitle != null)
            {
                primaryJobTitle.JobTitleId = input.JobTitleId;
            }
            else
            {
                Db.UserJobTitles.Add(new UserJobTitle
                {
                    UserInfoId = user.UserInfoId,
                    JobTitleId = input.JobTitleId,
                    IsPrimary = true
                });
            }

            // 更新角色關聯
            if (input.RoleGroupIds.Any())
            {
                var existingRoles = user.SysRoleGroupUsers
                    .Where(x => x.System == "Alife")
                    .ToList();

                // 移除不再需要的角色
                var rolesToRemove = existingRoles
                    .Where(x => !input.RoleGroupIds.Contains(x.RoleGroupId))
                    .ToList();
                Db.SysRoleGroupUsers.RemoveRange(rolesToRemove);

                // 新增新的角色
                foreach (string roleGroupId in input.RoleGroupIds.Distinct())
                {
                    if (!existingRoles.Any(x => x.RoleGroupId == roleGroupId) &&
                        await Db.SysRoleGroups.AnyAsync(x => x.System == "Alife" && x.RoleGroupId == roleGroupId))
                    {
                        Db.SysRoleGroupUsers.Add(new SysRoleGroupUser
                        {
                            System = "Alife",
                            RoleGroupId = roleGroupId,
                            UserInfoId = user.UserInfoId
                        });
                    }
                }
            }

            await Db.SaveChangesAsync();

            output.Response = Message.Success;
            return output;
        }

        /// <summary>
        /// 帳號權限管理-修改密碼
        /// </summary>
        /// <param name="input">密碼修改輸入資料</param>
        /// <returns>執行結果</returns>
        /// <exception cref="ArgumentNullException">input</exception>
        public async Task<UserInfoUpdateOutput> ChangePasswordAsync(PasswordChangeInput input)
        {
            ArgumentNullException.ThrowIfNull(input, nameof(input));

            UserInfoUpdateOutput output = new();

            var user = await Db.UserInfos.FirstOrDefaultAsync(x => x.UserInfoId == input.UserId);
            if (user == null)
            {
                output.Response = Message.AccountFail;
                return output;
            }

            if (await _passwordManager.IsPasswordUsedBeforeAsync(input.UserId, input.ChangePw))
            {
                output.Response = Message.PasswordUsedRecently;
                return output;
            }

            user.Password = _passwordManager.HashPassword(input.ChangePw);
            user.UpdatedTime = DateTime.Now;
            user.UpdatedUserInfoId = CurrentUser.UserId;

            await Db.SaveChangesAsync();
            await _passwordManager.CreatePasswordHistoryAsync(input.UserId, input.ChangePw);

            output.Response = Message.Success;
            return output;
        }

        /// <summary>
        /// 帳號權限管理-人員列表
        /// </summary>
        /// <param name="input">查詢條件</param>
        /// <returns>人員列表</returns>
        /// <exception cref="ArgumentNullException">input</exception>
        public async Task<PagedListOutput<UserInfoListItemGetOutputPg>> GetUserInfoListAsync(UserInfoListGetInput input)
        {
            ArgumentNullException.ThrowIfNull(input, nameof(input));

            // 1. 取得登入失敗次數上限
            var loginFailedThresholdSetting = await Db.SysSystemSettings
                .Where(x => x.Type == "SecuritySetting" && x.Key == "LoginFailedCount")
                .Select(x => x.Value)
                .FirstOrDefaultAsync();
            var loginFailedThreshold = Convert.ToInt32(loginFailedThresholdSetting ?? int.MaxValue.ToString());

            // 2. 查詢基礎使用者資料 (不過濾 Status)
            var baseQuery = Db.UserInfos;

            // 3. 關聯部門 (Left Join)
            var queryWithDept = from u in baseQuery
                                join ud in Db.UserDepartments on u.UserInfoId equals ud.UserInfoId into deptGroup
                                from ud in deptGroup.DefaultIfEmpty() // Left Join
                                join d in Db.Departments on new { u.CompanyId, ud.DepartmentId } equals new { d.CompanyId, d.DepartmentId } into dGroup
                                from d in dGroup.DefaultIfEmpty() // Left Join
                                select new { User = u, Department = d };

            // 4. 關聯職稱 (Left Join)
            var queryWithJob = from ud_data in queryWithDept
                               join uj in Db.UserJobTitles on ud_data.User.UserInfoId equals uj.UserInfoId into jobGroup
                               from uj in jobGroup.DefaultIfEmpty() // Left Join
                               join j in Db.JobTitles on uj.JobTitleId equals j.JobTitleId into jGroup
                               from j in jGroup.DefaultIfEmpty() // Left Join
                               select new { ud_data.User, ud_data.Department, JobTitle = j };

            // 5. 處理角色 (較複雜，保持子查詢，但確保只取一個 RoleGroupId)
            var finalQuery = from data in queryWithJob
                             let firstRoleGroupId = data.User.SysRoleGroupUsers
                                                    .Where(r => r.System == "Alife")
                                                    .Select(r => r.RoleGroupId)
                                                    .FirstOrDefault() // 確保只取一個 RoleGroupId
                             join rg in Db.SysRoleGroups on firstRoleGroupId equals rg.RoleGroupId into rgGroup
                             from rg in rgGroup.DefaultIfEmpty() // Left Join
                             select new UserInfoListItemGetOutputPg
                             {
                                 UserInfoId = data.User.UserInfoId,
                                 Name = data.User.Name,
                                 Email = data.User.Email,
                                 MobileNumber = data.User.MobileNumber,
                                 DepartmentId = data.Department == null ? null : data.Department.DepartmentId,
                                 DepartmentName = data.Department == null ? null : data.Department.Name,
                                 JobTitleId = data.JobTitle == null ? null : data.JobTitle.JobTitleId,
                                 JobTitleName = data.JobTitle == null ? null : data.JobTitle.Name,
                                 Status = data.User.Status,
                                 IsAdmin = rg != null && rg.IsAdmin,
                                 LastLoginTime = data.User.LastLoginTime,
                                 LastLoginIp = data.User.LastLoginIp,
                                 LoginFailedCount = data.User.LoginFailedCount.GetValueOrDefault(),
                                 IsLock = data.User.LoginFailedCount.GetValueOrDefault() > loginFailedThreshold,
                                 CompanyId = data.User.CompanyId,
                                 Gender = data.User.Gender,
                                 BirthDate = data.User.BirthDate.HasValue ? data.User.BirthDate.Value.ToDateTime(TimeOnly.MinValue) : null,
                                 TelephoneNumber = data.User.TelephoneNumber,
                                 RegisteredAddress = data.User.RegisteredAddress,
                                 MailingAddress = data.User.MailingAddress,
                                 EmergencyContactName = data.User.EmergencyContactName,
                                 EmergencyContactPhone = data.User.EmergencyContactPhone,
                                 EmergencyContactRelation = data.User.EmergencyContactRelation,
                                 ServiceUnit = data.User.ServiceUnit,
                                 HireDate = data.User.HireDate.HasValue ? data.User.HireDate.Value.ToDateTime(TimeOnly.MinValue) : null,
                                 LastLogoutTime = data.User.LastLogoutTime,
                                 IsInside = data.User.IsInside,
                                 IsM365 = data.User.IsM365,
                                 IsEmailNotificationEnabled = data.User.IsEmailNotificationEnabled,
                                 Identity = data.User.Identity,
                                 RoleGroups = data.User.SysRoleGroupUsers
                                     .Where(r => r.System == "Alife")
                                     .Join(Db.SysRoleGroups,
                                           r => r.RoleGroupId,
                                           g => g.RoleGroupId,
                                           (r, g) => new RoleGroupOutput
                                           {
                                               RoleGroupId = r.RoleGroupId,
                                               Name = g.Name
                                           })
                                     .ToList() // 注意: ToList() 可能導致 N+1 查詢，若有效能問題可考慮優化
                             };

            // 6. 套用分頁和排序 (由 ToPagedListOutputAsync 處理)
            return await finalQuery.ToPagedListOutputAsync(input);
        }

        /// <summary>
        /// 取得使用者下拉選單
        /// </summary>
        /// <param name="input">查詢條件</param>
        /// <returns>使用者下拉選單列表</returns>
        public async Task<List<UserInfoDropdownOutput>> GetUserInfoDropdownListAsync(UserInfoDropdownInput input)
        {
            var query = from u in Db.UserInfos
                       join ud in Db.UserDepartments on u.UserInfoId equals ud.UserInfoId
                       join d in Db.Departments on new { CompanyId = u.CompanyId, DepartmentId = ud.DepartmentId } equals new { d.CompanyId, d.DepartmentId }
                       join uj in Db.UserJobTitles on u.UserInfoId equals uj.UserInfoId
                       join j in Db.JobTitles on uj.JobTitleId equals j.JobTitleId
                       where u.Status && ud.IsPrimary == true && uj.IsPrimary == true
                       select new { u, d, j };

            // 套用過濾條件
            if (!string.IsNullOrEmpty(input.DepartmentId))
            {
                query = query.Where(x => x.d.DepartmentId == input.DepartmentId);
            }

            if (!string.IsNullOrEmpty(input.JobTitleId))
            {
                query = query.Where(x => x.j.JobTitleId == input.JobTitleId);
            }

            if (!string.IsNullOrEmpty(input.Name))
            {
                query = query.Where(x => x.u.Name.Contains(input.Name));
            }

            if (!string.IsNullOrEmpty(input.RoleGroupId))
            {
                query = query.Where(x => Db.SysRoleGroupUsers
                    .Any(r => r.System == "Alife" && 
                             r.RoleGroupId == input.RoleGroupId && 
                             r.UserInfoId == x.u.UserInfoId));
            }

            return await query
                .OrderBy(x => x.u.Name)
                .Select(x => new UserInfoDropdownOutput
                {
                    Name = $"{x.u.Name}({x.d.Name}-{x.j.Name})",
                    Value = x.u.UserInfoId
                })
                .ToListAsync();
        }

        /// <summary>
        /// 更新使用者帳號狀態
        /// </summary>
        /// <param name="input">使用者狀態更新輸入資料</param>
        /// <returns>執行結果</returns>
        /// <exception cref="ArgumentNullException">input</exception>
        public async Task<UserInfoUpdateOutput> UpdateUserInfoStatusAsync(UserInfoStatusUpdateInputPg input)
        {
            ArgumentNullException.ThrowIfNull(input, nameof(input));

            UserInfoUpdateOutput output = new();

            var user = await Db.UserInfos.FirstOrDefaultAsync(x => x.UserInfoId == input.UserInfoId);

            if (user == null)
            {
                output.Response = Message.AccountFail;
                return output;
            }

            user.Status = input.Status;
            user.UpdatedTime = DateTime.Now;
            user.UpdatedUserInfoId = CurrentUser.UserId;

            await Db.SaveChangesAsync();

            output.Response = Message.Success;
            return output;
        }
    }
}
