﻿using System.Xml.Linq;
using System.Xml.XPath;
using AlifeApi.WebApi.Filters;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.OpenApi.Models;
using Swashbuckle.AspNetCore.SwaggerGen;

namespace AlifeApi.WebApi.DependencyInjection
{
    public static class SwaggerGenOptionsExtensions
    {
        /// <summary>
        /// Includes the XML comments from files matching the specified pattern and adds support for enum descriptions.
        /// </summary>
        /// <param name="options">The <see cref="SwaggerGenOptions"/> instance.</param>
        /// <param name="xmlPattern">The pattern used to match XML files containing comments.</param>
        public static void IncludeXmlCommentsFromPattern(this SwaggerGenOptions options, string xmlPattern)
        {
            foreach (string xmlFile in Directory.GetFiles(AppContext.BaseDirectory, xmlPattern))
            {
                XDocument xmlDoc = XDocument.Load(xmlFile);
                options.IncludeXmlComments(() => new XPathDocument(xmlDoc.CreateReader()), true);
                options.SchemaFilter<EnumSchemaFilter>(xmlDoc);
            }
        }

        /// <summary>
        /// Adds the JWT security.
        /// </summary>
        /// <param name="options">The options.</param>
        public static void AddJwtSecurity(this SwaggerGenOptions options)
        {
            options.AddSecurityDefinition(JwtBearerDefaults.AuthenticationScheme, new OpenApiSecurityScheme
            {
                Name = "Authorization",
                Type = SecuritySchemeType.ApiKey,
                Scheme = JwtBearerDefaults.AuthenticationScheme,
                BearerFormat = "JWT",
                In = ParameterLocation.Header,
                Description = "JWT Authorization"
            });

            options.AddSecurityRequirement(
                new OpenApiSecurityRequirement
                {
                    {
                        new OpenApiSecurityScheme
                        {
                            Reference = new OpenApiReference
                            {
                                Type = ReferenceType.SecurityScheme,
                                Id = JwtBearerDefaults.AuthenticationScheme
                            }
                        },
                        Array.Empty<string>()
                    }
                });
        }
    }
}
