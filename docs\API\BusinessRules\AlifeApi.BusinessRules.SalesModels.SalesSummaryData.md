#### [AlifeApi.BusinessRules](index.md 'index')
### [AlifeApi.BusinessRules.SalesModels](AlifeApi.BusinessRules.SalesModels.md 'AlifeApi.BusinessRules.SalesModels')

## SalesSummaryData Class

銷售統計摘要資料

```csharp
public class SalesSummaryData
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; SalesSummaryData
### Properties

<a name='AlifeApi.BusinessRules.SalesModels.SalesSummaryData.AvailableUnits'></a>

## SalesSummaryData.AvailableUnits Property

可售戶數

```csharp
public int AvailableUnits { get; set; }
```

#### Property Value
[System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')

<a name='AlifeApi.BusinessRules.SalesModels.SalesSummaryData.ReservedUnits'></a>

## SalesSummaryData.ReservedUnits Property

保留戶數

```csharp
public int ReservedUnits { get; set; }
```

#### Property Value
[System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')

<a name='AlifeApi.BusinessRules.SalesModels.SalesSummaryData.SalesRate'></a>

## SalesSummaryData.SalesRate Property

去化率 (百分比)

```csharp
public decimal SalesRate { get; set; }
```

#### Property Value
[System.Decimal](https://docs.microsoft.com/en-us/dotnet/api/System.Decimal 'System.Decimal')

<a name='AlifeApi.BusinessRules.SalesModels.SalesSummaryData.SiteCode'></a>

## SalesSummaryData.SiteCode Property

案場編號

```csharp
public string SiteCode { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.SalesModels.SalesSummaryData.SoldUnits'></a>

## SalesSummaryData.SoldUnits Property

已售戶數

```csharp
public int SoldUnits { get; set; }
```

#### Property Value
[System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')

<a name='AlifeApi.BusinessRules.SalesModels.SalesSummaryData.TotalUnits'></a>

## SalesSummaryData.TotalUnits Property

總戶數

```csharp
public int TotalUnits { get; set; }
```

#### Property Value
[System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')