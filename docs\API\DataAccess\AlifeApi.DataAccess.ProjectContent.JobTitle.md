#### [AlifeApi.DataAccess](index.md 'index')
### [AlifeApi.DataAccess.ProjectContent](AlifeApi.DataAccess.ProjectContent.md 'AlifeApi.DataAccess.ProjectContent')

## JobTitle Class

職位資料表，用於儲存每個公司的職位資訊。

```csharp
public class JobTitle
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; JobTitle
### Properties

<a name='AlifeApi.DataAccess.ProjectContent.JobTitle.CreatedTime'></a>

## JobTitle.CreatedTime Property

創建時間，記錄資料創建的時間。

```csharp
public System.DateTime CreatedTime { get; set; }
```

#### Property Value
[System.DateTime](https://docs.microsoft.com/en-us/dotnet/api/System.DateTime 'System.DateTime')

<a name='AlifeApi.DataAccess.ProjectContent.JobTitle.CreatedUserInfoId'></a>

## JobTitle.CreatedUserInfoId Property

建立者，記錄創建此資料的員工編號。

```csharp
public string CreatedUserInfoId { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContent.JobTitle.DepartmentId'></a>

## JobTitle.DepartmentId Property

部門編號，主鍵的一部分，對應 Departments 表的 DepartmentId。

```csharp
public string DepartmentId { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContent.JobTitle.JobTitleId'></a>

## JobTitle.JobTitleId Property

職位編號，主鍵的一部分，用於唯一識別職位。

```csharp
public string JobTitleId { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContent.JobTitle.Name'></a>

## JobTitle.Name Property

職位名稱。

```csharp
public string Name { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContent.JobTitle.UpdatedTime'></a>

## JobTitle.UpdatedTime Property

更新時間，記錄資料最後更新的時間。

```csharp
public System.DateTime UpdatedTime { get; set; }
```

#### Property Value
[System.DateTime](https://docs.microsoft.com/en-us/dotnet/api/System.DateTime 'System.DateTime')

<a name='AlifeApi.DataAccess.ProjectContent.JobTitle.UpdatedUserInfoId'></a>

## JobTitle.UpdatedUserInfoId Property

更新者，記錄最後更新此資料的員工編號。

```csharp
public string UpdatedUserInfoId { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')