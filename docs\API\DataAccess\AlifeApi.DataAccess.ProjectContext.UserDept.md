#### [AlifeApi.DataAccess](index.md 'index')
### [AlifeApi.DataAccess.ProjectContext](AlifeApi.DataAccess.ProjectContext.md 'AlifeApi.DataAccess.ProjectContext')

## UserDept Class

組織部門資料

```csharp
public class UserDept
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; UserDept
### Properties

<a name='AlifeApi.DataAccess.ProjectContext.UserDept.CreatedTime'></a>

## UserDept.CreatedTime Property

建立時間

```csharp
public System.DateTime CreatedTime { get; set; }
```

#### Property Value
[System.DateTime](https://docs.microsoft.com/en-us/dotnet/api/System.DateTime 'System.DateTime')

<a name='AlifeApi.DataAccess.ProjectContext.UserDept.CreatedUserId'></a>

## UserDept.CreatedUserId Property

建立者

```csharp
public string CreatedUserId { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContext.UserDept.DeptName'></a>

## UserDept.DeptName Property

部門名稱

```csharp
public string DeptName { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContext.UserDept.Id'></a>

## UserDept.Id Property

部門代碼

```csharp
public string Id { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContext.UserDept.IsDisabled'></a>

## UserDept.IsDisabled Property

是否刪除

```csharp
public bool IsDisabled { get; set; }
```

#### Property Value
[System.Boolean](https://docs.microsoft.com/en-us/dotnet/api/System.Boolean 'System.Boolean')

<a name='AlifeApi.DataAccess.ProjectContext.UserDept.LeaderUserId'></a>

## UserDept.LeaderUserId Property

部門主管員工編號

```csharp
public string LeaderUserId { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContext.UserDept.ParentDeptId'></a>

## UserDept.ParentDeptId Property

上層部門代碼

```csharp
public string ParentDeptId { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContext.UserDept.UpdatedTime'></a>

## UserDept.UpdatedTime Property

更新時間

```csharp
public System.DateTime UpdatedTime { get; set; }
```

#### Property Value
[System.DateTime](https://docs.microsoft.com/en-us/dotnet/api/System.DateTime 'System.DateTime')

<a name='AlifeApi.DataAccess.ProjectContext.UserDept.UpdatedUserId'></a>

## UserDept.UpdatedUserId Property

更新者

```csharp
public string UpdatedUserId { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')