#### [AlifeApi.BusinessRules](index.md 'index')
### [AlifeApi.BusinessRules.ProjectVersionModels](AlifeApi.BusinessRules.ProjectVersionModels.md 'AlifeApi.BusinessRules.ProjectVersionModels')

## ProjectVersionBase Class

```csharp
public class ProjectVersionBase
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; ProjectVersionBase

Derived  
&#8627; [ProjectVersionListResult](AlifeApi.BusinessRules.ProjectVersionModels.ProjectVersionListResult.md 'AlifeApi.BusinessRules.ProjectVersionModels.ProjectVersionListResult')  
&#8627; [ProjectVersionOutput](AlifeApi.BusinessRules.ProjectVersionModels.ProjectVersionOutput.md 'AlifeApi.BusinessRules.ProjectVersionModels.ProjectVersionOutput')  
&#8627; [SyncProjectVersion](AlifeApi.BusinessRules.ProjectVersionModels.SyncProjectVersion.md 'AlifeApi.BusinessRules.ProjectVersionModels.SyncProjectVersion')
### Properties

<a name='AlifeApi.BusinessRules.ProjectVersionModels.ProjectVersionBase.ProjectName'></a>

## ProjectVersionBase.ProjectName Property

系統專案名稱

```csharp
public string ProjectName { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')