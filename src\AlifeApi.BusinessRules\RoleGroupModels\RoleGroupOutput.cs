﻿using System.Text.Json.Serialization;

namespace AlifeApi.BusinessRules.RoleGroupModels
{
    public class RoleGroupOutput
    {
        /// <summary>
        /// RoleGroupId
        /// </summary>
        public string RoleGroupId { get; set; }

        /// <summary>
        /// 角色群組名稱
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// 建立人帳號
        /// </summary>
        public string CreatedUserId { get; set; }

        /// <summary>
        /// 建立人姓名
        /// </summary>
        public string CreatedUserName { get; set; }

        /// <summary>
        /// 建立時間
        /// </summary>
        public DateTime CreatedTime { get; set; }

        /// <summary>
        /// 更新者帳號
        /// </summary>
        public string UpdatedUserId { get; set; }

        /// <summary>
        /// 更新者姓名
        /// </summary>
        public string UpdatedUserName { get; set; }

        /// <summary>
        /// 更新時間
        /// </summary>
        public DateTime UpdatedTime { get; set; }

        /// <summary>
        /// 權限擁有者清單
        /// </summary>
        public IEnumerable<string> UserIds { get; set; } = Enumerable.Empty<string>();

        /// <summary>
        /// 角色權限
        /// </summary>
        [JsonIgnore]
        public IEnumerable<string> FuncIds { get; set; } = Enumerable.Empty<string>();

        /// <summary>
        /// 角色權限
        /// </summary>
        public string Permission => string.Join(',', FuncIds);

        /// <summary>
        /// 權限樹
        /// </summary>
        [JsonPropertyName("PermissionTree")]
        public IEnumerable<MenuTreeOutput> MenuTrees { get; set; } = Enumerable.Empty<MenuTreeOutput>();
    }
}
