#### [AlifeApi.BusinessRules](index.md 'index')
### [AlifeApi.BusinessRules.SysCodeModels](AlifeApi.BusinessRules.SysCodeModels.md 'AlifeApi.BusinessRules.SysCodeModels')

## SysTypeOutput Class

SysType 類型

```csharp
public class SysTypeOutput
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; SysTypeOutput
### Properties

<a name='AlifeApi.BusinessRules.SysCodeModels.SysTypeOutput.IsDisabled'></a>

## SysTypeOutput.IsDisabled Property

是否作廢

```csharp
public bool IsDisabled { get; set; }
```

#### Property Value
[System.Boolean](https://docs.microsoft.com/en-us/dotnet/api/System.Boolean 'System.Boolean')

### Remarks
沒作用的欄位，前人不知道從哪抄的，拿掉又怕前端程式會死

<a name='AlifeApi.BusinessRules.SysCodeModels.SysTypeOutput.Label'></a>

## SysTypeOutput.Label Property

選項

```csharp
public string Label { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.SysCodeModels.SysTypeOutput.Value'></a>

## SysTypeOutput.Value Property

值

```csharp
public string Value { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')