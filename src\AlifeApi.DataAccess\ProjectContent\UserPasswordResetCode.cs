﻿using System;
using System.Collections.Generic;

namespace AlifeApi.DataAccess.ProjectContent
{
    /// <summary>
    /// 密碼重置驗證碼表，用於儲存員工密碼重置時的驗證碼資訊。
    /// </summary>
    public partial class UserPasswordResetCode
    {
        /// <summary>
        /// 流水號，主鍵，使用 UUID 格式唯一識別驗證碼記錄。
        /// </summary>
        public Guid UserPasswordResetCodesId { get; set; }
        /// <summary>
        /// 員工編號，對應 UserInfo 表的 UserInfoId。
        /// </summary>
        public string UserInfoId { get; set; }
        /// <summary>
        /// 驗證碼，通常為 6 位數。
        /// </summary>
        public string VerificationCode { get; set; }
        /// <summary>
        /// 驗證碼到期時間，過期後無法使用。
        /// </summary>
        public DateTime? ExpirationTime { get; set; }
        /// <summary>
        /// 是否已使用，true 表示已使用，false 表示未使用，預設為 false。
        /// </summary>
        public bool? IsUsed { get; set; }
    }
}
