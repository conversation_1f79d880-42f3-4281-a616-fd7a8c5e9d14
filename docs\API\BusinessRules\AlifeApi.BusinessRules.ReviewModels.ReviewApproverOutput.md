#### [AlifeApi.BusinessRules](index.md 'index')
### [AlifeApi.BusinessRules.ReviewModels](AlifeApi.BusinessRules.ReviewModels.md 'AlifeApi.BusinessRules.ReviewModels')

## ReviewApproverOutput Class

審核人員輸出模型

```csharp
public class ReviewApproverOutput
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; ReviewApproverOutput
### Properties

<a name='AlifeApi.BusinessRules.ReviewModels.ReviewApproverOutput.ApproverId'></a>

## ReviewApproverOutput.ApproverId Property

審核人員ID

```csharp
public int ApproverId { get; set; }
```

#### Property Value
[System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')

<a name='AlifeApi.BusinessRules.ReviewModels.ReviewApproverOutput.UserInfoId'></a>

## ReviewApproverOutput.UserInfoId Property

使用者ID

```csharp
public string UserInfoId { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')