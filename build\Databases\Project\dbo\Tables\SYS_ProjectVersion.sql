﻿CREATE TABLE [dbo].[SYS_ProjectVersion] (
    [P_ID]          INT            IDENTITY (1, 1) NOT NULL,
    [P_projectName] VARCHAR (256)  NOT NULL,
    [P_versionHash] VARCHAR (256)  NULL,
    [P_versionName] VARCHAR (256)  NULL,
    [P_CR_DateTime] DATETIME       NULL,
    [P_Cr_User]     NVARCHAR (50)  NULL,
    [P_Content]     NVARCHAR (MAX) NULL,
    CONSTRAINT [PK_Project_Version] PRIMARY KEY CLUSTERED ([P_ID] ASC)
);


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'更新內容', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'SYS_ProjectVersion', @level2type = N'COLUMN', @level2name = N'P_Content';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'上版人員', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'SYS_ProjectVersion', @level2type = N'COLUMN', @level2name = N'P_Cr_User';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'版更時間', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'SYS_ProjectVersion', @level2type = N'COLUMN', @level2name = N'P_CR_DateTime';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'版號', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'SYS_ProjectVersion', @level2type = N'COLUMN', @level2name = N'P_versionName';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'git唯一值', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'SYS_ProjectVersion', @level2type = N'COLUMN', @level2name = N'P_versionHash';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'專案代號', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'SYS_ProjectVersion', @level2type = N'COLUMN', @level2name = N'P_projectName';

