﻿CREATE TABLE [dbo].[SYS_Code] (
    [Type]          VARCHAR (50)   NOT NULL,
    [Code]          VARCHAR (30)   NOT NULL,
    [ParentCode]    VARCHAR (30)   NULL,
    [CodeDesc]      NVARCHAR (300) NOT NULL,
    [IsDisabled]    BIT            NOT NULL,
    [<PERSON><PERSON>rde<PERSON>]     SMALLINT       NOT NULL,
    [CreatedUserId] VARCHAR (15)   NOT NULL,
    [CreatedTime]   DATETIME       NOT NULL,
    [UpdatedUserId] VARCHAR (15)   NOT NULL,
    [UpdatedTime]   DATETIME       NOT NULL,
    CONSTRAINT [PK_SYS_CODE] PRIMARY KEY CLUSTERED ([Type] ASC, [Code] ASC),
    CONSTRAINT [FK_SYS_Code_SYS_Code] FOREIGN KEY ([Type], [ParentCode]) REFERENCES [dbo].[SYS_Code] ([Type], [Code])
);


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'系統代碼資料', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'SYS_Code';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'類型', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'SYS_Code', @level2type = N'COLUMN', @level2name = N'Type';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'代碼', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'SYS_Code', @level2type = N'COLUMN', @level2name = N'Code';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'上層代碼', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'SYS_Code', @level2type = N'COLUMN', @level2name = N'ParentCode';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'內容', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'SYS_Code', @level2type = N'COLUMN', @level2name = N'CodeDesc';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'是否作廢', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'SYS_Code', @level2type = N'COLUMN', @level2name = N'IsDisabled';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'排序', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'SYS_Code', @level2type = N'COLUMN', @level2name = N'CodeOrder';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'建立者', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'SYS_Code', @level2type = N'COLUMN', @level2name = N'CreatedUserId';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'建立時間', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'SYS_Code', @level2type = N'COLUMN', @level2name = N'CreatedTime';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'新增者', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'SYS_Code', @level2type = N'COLUMN', @level2name = N'UpdatedUserId';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'更新時間', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'SYS_Code', @level2type = N'COLUMN', @level2name = N'UpdatedTime';

