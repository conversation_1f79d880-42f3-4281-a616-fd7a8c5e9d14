using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using AlifeApi.BusinessRules.Infrastructure;
using AlifeApi.BusinessRules.UserInfoModels;
using AlifeApi.DataAccess.ProjectContent;
using Microsoft.EntityFrameworkCore;

namespace AlifeApi.BusinessRules.DepartmentModels
{
    /// <summary>
    /// 部門服務
    /// </summary>
    public class DepartmentService : ServiceBase<alifeContext>
    {
        /// <summary>
        /// 建構函數
        /// </summary>
        public DepartmentService(IServiceProvider serviceProvider, alifeContext dbContext)
            : base(serviceProvider, dbContext)
        {
        }

        /// <summary>
        /// 取得部門列表 (分頁)
        /// </summary>
        /// <param name="input">查詢條件</param>
        /// <returns>分頁的部門列表</returns>
        public async Task<PagedListOutput<DepartmentOutput>> GetDepartmentListAsync(DepartmentListGetInput input)
        {
            ArgumentNullException.ThrowIfNull(input, nameof(input));

            // 1. 基礎查詢
            IQueryable<Department> query = Db.Departments.AsQueryable();

            // 2. 套用篩選條件
            if (!string.IsNullOrEmpty(input.CompanyId))
            {
                query = query.Where(d => d.CompanyId == input.CompanyId);
            }
            if (!string.IsNullOrEmpty(input.Name))
            {
                query = query.Where(d => d.Name.Contains(input.Name));
            }

            // 3. 包含關聯資料並執行投影
            var projectedQuery = query
                .Include(d => d.Company) // 在 Select 之前 Include
                .Select(d => new DepartmentOutput
                {
                    CompanyId = d.CompanyId,
                    CompanyName = d.Company.Name,
                    DepartmentId = d.DepartmentId,
                    Name = d.Name,
                    CreatedTime = d.CreatedTime,
                    CreatedUserId = d.CreatedUserInfoId,
                    UpdatedTime = d.UpdatedTime,
                    UpdatedUserId = d.UpdatedUserInfoId
                });

            // 4. 執行分頁查詢
            var pagedResult = await projectedQuery.ToPagedListOutputAsync(input);

            // 5. 查詢並填入建立者和更新者名稱 (這部分不變)
            if (pagedResult.Details.Any())
            {
                var userIds = pagedResult.Details
                                        .SelectMany(x => new[] { x.CreatedUserId, x.UpdatedUserId })
                                        .Where(id => !string.IsNullOrEmpty(id))
                                        .Distinct()
                                        .ToList();
                if (userIds.Any())
                {
                    var users = await Db.UserInfos
                        .Where(u => userIds.Contains(u.UserInfoId))
                        .Select(u => new { u.UserInfoId, u.Name })
                        .ToListAsync();
                    var userMap = users.ToDictionary(u => u.UserInfoId, u => u.Name);

                    foreach (var dept in pagedResult.Details)
                    {
                        if (!string.IsNullOrEmpty(dept.CreatedUserId) && userMap.TryGetValue(dept.CreatedUserId, out var createdName))
                        {
                            dept.CreatedUserName = createdName;
                        }
                        if (!string.IsNullOrEmpty(dept.UpdatedUserId) && userMap.TryGetValue(dept.UpdatedUserId, out var updatedName))
                        {
                            dept.UpdatedUserName = updatedName;
                        }
                    }
                }
            }

            return pagedResult;
        }

        /// <summary>
        /// 根據公司ID和部門ID取得單一部門資訊
        /// </summary>
        /// <param name="companyId">公司ID</param>
        /// <param name="departmentId">部門ID</param>
        /// <returns>部門詳細資訊</returns>
        public async Task<DepartmentOutput> GetDepartmentByIdAsync(string companyId, string departmentId)
        {
            var department = await Db.Departments
                .Include(d => d.Company) // 包含公司資料
                .Where(d => d.CompanyId == companyId && d.DepartmentId == departmentId)
                .Select(d => new DepartmentOutput
                {
                    CompanyId = d.CompanyId,
                    CompanyName = d.Company.Name, // 從關聯的 Company 取得名稱
                    DepartmentId = d.DepartmentId,
                    Name = d.Name,
                    CreatedTime = d.CreatedTime,
                    CreatedUserId = d.CreatedUserInfoId,
                    UpdatedTime = d.UpdatedTime,
                    UpdatedUserId = d.UpdatedUserInfoId
                })
                .FirstOrDefaultAsync();

            if (department == null)
            {
                return null;
            }

            // 查詢並填入建立者和更新者名稱
            var userIds = new[] { department.CreatedUserId, department.UpdatedUserId }
                            .Where(id => !string.IsNullOrEmpty(id))
                            .Distinct()
                            .ToList();
            if (userIds.Any())
            {
                var users = await Db.UserInfos
                    .Where(u => userIds.Contains(u.UserInfoId))
                    .Select(u => new { u.UserInfoId, u.Name })
                    .ToListAsync();
                var userMap = users.ToDictionary(u => u.UserInfoId, u => u.Name);

                if (!string.IsNullOrEmpty(department.CreatedUserId) && userMap.TryGetValue(department.CreatedUserId, out var createdName))
                {
                    department.CreatedUserName = createdName;
                }
                if (!string.IsNullOrEmpty(department.UpdatedUserId) && userMap.TryGetValue(department.UpdatedUserId, out var updatedName))
                {
                    department.UpdatedUserName = updatedName;
                }
            }

            return department;
        }

        /// <summary>
        /// 新增部門
        /// </summary>
        /// <param name="input">新增部門輸入資料</param>
        /// <exception cref="ArgumentNullException">input</exception>
        /// <exception cref="Exception">部門已存在或其他資料庫錯誤</exception>
        public async Task CreateDepartmentAsync(DepartmentCreateInput input)
        {
            ArgumentNullException.ThrowIfNull(input, nameof(input));

            // 檢查公司是否存在 (可選，取決於您的業務邏輯)
            if (!await Db.Companies.AnyAsync(c => c.CompanyId == input.CompanyId))
            {
                throw new Exception($"找不到公司ID: {input.CompanyId}");
            }

            // 檢查部門是否已存在
            if (await Db.Departments.AnyAsync(d => d.CompanyId == input.CompanyId && d.DepartmentId == input.DepartmentId))
            {
                throw new Exception($"部門 {input.DepartmentId} 在公司 {input.CompanyId} 中已存在");
            }

            var department = new Department
            {
                CompanyId = input.CompanyId,
                DepartmentId = input.DepartmentId,
                Name = input.Name,
                CreatedTime = DateTime.Now,
                CreatedUserInfoId = CurrentUser.UserId, // 假設您有 CurrentUser 服務
                UpdatedTime = DateTime.Now,
                UpdatedUserInfoId = CurrentUser.UserId
            };

            Db.Departments.Add(department);
            await Db.SaveChangesAsync();
        }

        /// <summary>
        /// 更新部門資訊
        /// </summary>
        /// <param name="input">更新部門輸入資料</param>
        /// <returns>更新後的部門資訊</returns>
        /// <exception cref="ArgumentNullException">input</exception>
        /// <exception cref="Exception">找不到部門或其他資料庫錯誤</exception>
        public async Task<DepartmentOutput> UpdateDepartmentAsync(DepartmentUpdateInput input)
        {
            ArgumentNullException.ThrowIfNull(input, nameof(input));

            var department = await Db.Departments.FirstOrDefaultAsync(d => d.CompanyId == input.CompanyId && d.DepartmentId == input.DepartmentId);

            if (department == null)
            {
                throw new Exception($"找不到部門 {input.DepartmentId} (公司: {input.CompanyId})");
            }

            // 只更新允許修改的欄位
            department.Name = input.Name;
            department.UpdatedTime = DateTime.Now;
            department.UpdatedUserInfoId = CurrentUser.UserId;

            await Db.SaveChangesAsync();

            // 更新成功後，查詢並返回更新後的資料
            // 使用與 GetDepartmentByIdAsync 類似的邏輯來包含 Company 和 UserInfo
            var updatedDepartmentOutput = await GetDepartmentByIdAsync(department.CompanyId, department.DepartmentId);

            // GetDepartmentByIdAsync 已經處理了 null 的情況，但做個保險檢查
            if (updatedDepartmentOutput == null)
            {
                 // 這理論上不應該發生，因為我們剛剛更新了它
                 throw new Exception("更新後無法立即查詢到部門資料，請檢查資料庫狀態。");
            }

            return updatedDepartmentOutput; // 返回更新後的 DTO
        }

        /// <summary>
        /// 刪除部門
        /// </summary>
        /// <param name="companyId">公司ID</param>
        /// <param name="departmentId">部門ID</param>
        /// <returns>執行結果訊息</returns>
        /// <exception cref="Exception">找不到部門或部門已被使用</exception>
        public async Task<UserInfoUpdateOutput> DeleteDepartmentAsync(string companyId, string departmentId)
        {
            var department = await Db.Departments.FirstOrDefaultAsync(d => d.CompanyId == companyId && d.DepartmentId == departmentId);

            if (department == null)
            {
                throw new Exception($"找不到部門 {departmentId} (公司: {companyId})");
            }

            // 檢查是否有使用者關聯到此部門 (根據您的資料庫結構調整)
            bool isUsedByUser = await Db.UserDepartments.AnyAsync(ud => ud.DepartmentId == departmentId);
            if (isUsedByUser)
            {
                throw new Exception($"部門 {departmentId} 已被使用者使用，無法刪除");
            }

            // 檢查是否有職位關聯到此部門 (如果您的 JobTitle 表有關聯 Department)
            bool isUsedByJobTitle = await Db.JobTitles.AnyAsync(jt => jt.DepartmentId == departmentId);
            if (isUsedByJobTitle)
            {
                throw new Exception($"部門 {departmentId} 下尚有職位，無法刪除");
            }

            Db.Departments.Remove(department);
            await Db.SaveChangesAsync();

            // 返回成功訊息
            return new UserInfoUpdateOutput { Response = Message.Success };
        }

        /// <summary>
        /// 取得部門下拉選單
        /// </summary>
        /// <param name="input">查詢條件</param>
        /// <returns>部門下拉選單列表</returns>
        public async Task<List<DepartmentDropdownOutput>> GetDepartmentDropdownListAsync(DepartmentDropdownInput input)
        {
            var query = Db.Departments.AsQueryable();
            
            if (!string.IsNullOrEmpty(input.CompanyId))
            {
                query = query.Where(x => x.CompanyId == input.CompanyId);
            }

            return await query
                .OrderBy(x => x.Name)
                .Select(x => new DepartmentDropdownOutput
                {
                    Name = x.Name,
                    Value = x.DepartmentId
                })
                .ToListAsync();
        }
    }
} 
