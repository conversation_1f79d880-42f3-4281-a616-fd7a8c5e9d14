using System;

namespace AlifeApi.BusinessRules.PurchaseOrderModels
{
    /// <summary>
    /// 買賣預定單列表輸出項目
    /// </summary>
    public class PurchaseOrderListOutput
    {
        public int OrderId { get; set; }
        public string? OrderNumber { get; set; }
        public DateOnly OrderDate { get; set; }
        public string SiteCode { get; set; } = null!;
        public int CustomerId { get; set; }
        public string? CustomerName { get; set; }
        public int? UnitId { get; set; }
        public string? UnitNumber { get; set; }
        public string? BuildingName { get; set; }
        public string? FloorLabel { get; set; }
        public decimal TotalPrice { get; set; }
        public string SalespersonUserInfoId { get; set; } = null!;
        public string? SalespersonName { get; set; }
        public string Status { get; set; } = null!;
        public DateTime CreatedTime { get; set; }
        public string PurchasedParkingSpaceNumbers { get; set; } = string.Empty;
    }
} 
