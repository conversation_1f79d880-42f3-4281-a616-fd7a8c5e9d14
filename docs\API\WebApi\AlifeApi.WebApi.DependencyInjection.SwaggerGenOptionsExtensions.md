#### [AlifeApi.WebApi](index.md 'index')
### [AlifeApi.WebApi.DependencyInjection](AlifeApi.WebApi.DependencyInjection.md 'AlifeApi.WebApi.DependencyInjection')

## SwaggerGenOptionsExtensions Class

```csharp
public static class SwaggerGenOptionsExtensions
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; SwaggerGenOptionsExtensions
### Methods

<a name='AlifeApi.WebApi.DependencyInjection.SwaggerGenOptionsExtensions.AddJwtSecurity(thisSwashbuckle.AspNetCore.SwaggerGen.SwaggerGenOptions)'></a>

## SwaggerGenOptionsExtensions.AddJwtSecurity(this SwaggerGenOptions) Method

Adds the JWT security.

```csharp
public static void AddJwtSecurity(this Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenOptions options);
```
#### Parameters

<a name='AlifeApi.WebApi.DependencyInjection.SwaggerGenOptionsExtensions.AddJwtSecurity(thisSwashbuckle.AspNetCore.SwaggerGen.SwaggerGenOptions).options'></a>

`options` [Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenOptions](https://docs.microsoft.com/en-us/dotnet/api/Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenOptions 'Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenOptions')

The options.

<a name='AlifeApi.WebApi.DependencyInjection.SwaggerGenOptionsExtensions.IncludeXmlCommentsFromPattern(thisSwashbuckle.AspNetCore.SwaggerGen.SwaggerGenOptions,string)'></a>

## SwaggerGenOptionsExtensions.IncludeXmlCommentsFromPattern(this SwaggerGenOptions, string) Method

Includes the XML comments from files matching the specified pattern and adds support for enum descriptions.

```csharp
public static void IncludeXmlCommentsFromPattern(this Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenOptions options, string xmlPattern);
```
#### Parameters

<a name='AlifeApi.WebApi.DependencyInjection.SwaggerGenOptionsExtensions.IncludeXmlCommentsFromPattern(thisSwashbuckle.AspNetCore.SwaggerGen.SwaggerGenOptions,string).options'></a>

`options` [Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenOptions](https://docs.microsoft.com/en-us/dotnet/api/Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenOptions 'Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenOptions')

The [Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenOptions](https://docs.microsoft.com/en-us/dotnet/api/Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenOptions 'Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenOptions') instance.

<a name='AlifeApi.WebApi.DependencyInjection.SwaggerGenOptionsExtensions.IncludeXmlCommentsFromPattern(thisSwashbuckle.AspNetCore.SwaggerGen.SwaggerGenOptions,string).xmlPattern'></a>

`xmlPattern` [System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

The pattern used to match XML files containing comments.