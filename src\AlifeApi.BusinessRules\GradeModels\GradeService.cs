﻿//using AlifeApi.BusinessRules.Infrastructure;
//using AlifeApi.Common.DataAnnotations;
//using AlifeApi.DataAccess.ProjectContext;
//using Microsoft.EntityFrameworkCore;

//namespace AlifeApi.BusinessRules.GradeModels
//{
//    public class GradeService : ServiceBase<ProjectContext>
//    {
//        public GradeService(IServiceProvider serviceProvider, ProjectContext dbContext) : base(serviceProvider, dbContext)
//        {
//        }

//        public async Task CreateGrade(CreateGradeCondition input)
//        {
//            ArgumentNullException.ThrowIfNull(input, nameof(input));



//            if (await Db.UserGrade.AnyAsync(x => x.Id == input.GradeId))
//            {
//                throw new KyException("職稱代碼已存在");
//            }
//            var parentGrade = await Db.UserGrade.Include(x => x.UserGradePermission).FirstOrDefaultAsync(x => x.Id == input.ParentGradeId);

//            if (parentGrade != null)
//            {
//                var errorPermission = input.FuncIds.Except(parentGrade.UserGradePermission.Select(x => x.FuncId));
//                if (errorPermission.Any())
//                {
//                    throw new KyException($"{string.Join("、", errorPermission)},不存在於上層職稱權限中");
//                }
//            }

//            Db.UserGrade.Add(new UserGrade()
//            {
//                Id = input.GradeId,
//                GradeName = input.GradeName,
//                ParentGradeId = parentGrade?.Id,
//                CreatedUserId = CurrentUser.UserId,
//                CreatedTime = DateTime.Now,
//                UpdatedUserId = CurrentUser.UserId,
//                UpdatedTime = DateTime.Now,
//                UserGradePermission = input.FuncIds.Select(x => new UserGradePermission()
//                {
//                    System = CurrentUser.System,
//                    FuncId = x
//                }).ToList()
//            });

//            await Db.SaveChangesAsync();
//        }

//        public async Task DeleteGrade(DeleteGradeCondition input)
//        {
//            ArgumentNullException.ThrowIfNull(input, nameof(input));

//            var GradeList = Db.UserGrade.Include(x => x.UserInfo).Where(d => d.Id == input.GradeId).ToList();

//            if (!GradeList.Any())
//                throw new KyException($"找不到職稱");

//            // 递归查询子部门
//            void GetSubDepartments(string parentId)
//            {
//                var subDepartments = Db.UserGrade.Include(x => x.UserInfo).Where(d => d.ParentGradeId == parentId).ToList();
//                foreach (var department in subDepartments)
//                {
//                    GradeList.Add(department);
//                    GetSubDepartments(department.Id); // 遞迴查詢子職稱
//                }
//            }

//            // 执行递归查询
//            GetSubDepartments(input.GradeId);

//            if (GradeList.Where(x => x.UserInfo.Any()).Any())
//                throw new KyException($"尚有使用者存在於此職稱或其子職稱，請先將移轉使用者職稱");

//            if (GradeList.FirstOrDefault().IsDisabled)
//                GradeList.FirstOrDefault().IsDisabled = false;
//            else
//                GradeList.ForEach((Grade) =>
//                {
//                    Grade.IsDisabled = true;
//                });

//            await Db.SaveChangesAsync();
//        }

//        public async Task<PagedListOutput<GradeListResult>> GetGradeList(GradeListCondition input)
//        {
//            ArgumentNullException.ThrowIfNull(input, nameof(input));
//            PagedListOutput<GradeListResult> output = await Db.UserGrade.AsNoTracking()
//                .Select(s => new GradeListResult
//                {
//                    GradeId = s.Id,
//                    GradeName = s.GradeName,
//                    ParentGradeId = s.ParentGradeId,
//                    ParentGradeName = s.ParentGrade.GradeName,
//                    CreatedUserId = s.CreatedUserId,
//                    CreatedTime = s.CreatedTime,
//                    UpdatedUserId = s.UpdatedUserId,
//                    UpdatedTime = s.UpdatedTime,
//                    IsDisabled = s.IsDisabled,
//                    FuncIds = s.UserGradePermission.Select(x => x.FuncId),
//                    UserIds = s.UserInfo.Select(x => x.Id)
//                }).ToPagedListOutputAsync(input);

//            return output;
//        }

//        public async Task UpdateGrade(UpdateGradeCondition input)
//        {
//            ArgumentNullException.ThrowIfNull(input, nameof(input));

//            UserGrade Grade = await Db.UserGrade
//                .Include(x => x.UserGradePermission)
//                .SingleAsync(x => x.Id == input.GradeId);

//            var parentGrade = await Db.UserGrade.Include(x => x.UserGradePermission).FirstOrDefaultAsync(x => x.Id == input.ParentGradeId);

//            if (parentGrade != null)
//            {
//                var errorPermission = input.FuncIds.Except(parentGrade.UserGradePermission.Select(x => x.FuncId));
//                if (errorPermission.Any())
//                {
//                    throw new KyException($"{string.Join("、", errorPermission)},不存在於上層職稱權限中");
//                }
//            }

//            Grade.GradeName = input.GradeName;
//            Grade.ParentGradeId = input.ParentGradeId;
//            Grade.UpdatedUserId = CurrentUser.UserId;
//            Grade.UpdatedTime = DateTime.Now;
//            Grade.UserGradePermission = input.FuncIds.Select(x => new UserGradePermission()
//            {
//                System = CurrentUser.System,
//                FuncId = x
//            }).ToList();

//            await Db.SaveChangesAsync();
//        }

//        public async Task UpdateGradePermission(UpdateGradePermissionCondition input)
//        {
//            ArgumentNullException.ThrowIfNull(input, nameof(input));

//            UserGrade Grade = await Db.UserGrade
//                .Include(x => x.UserGradePermission)
//                .SingleAsync(x => x.Id == input.GradeId);

//            var parentGrade = await Db.UserGrade.Include(x => x.UserGradePermission).FirstOrDefaultAsync(x => x.Id == Grade.ParentGradeId);

//            if (parentGrade != null)
//            {
//                var errorPermission = input.FuncIds.Except(parentGrade.UserGradePermission.Select(x => x.FuncId));
//                if (errorPermission.Any())
//                {
//                    throw new KyException($"{string.Join("、", errorPermission)},不存在於上層職稱權限中");
//                }
//            }
//            Grade.UpdatedUserId = CurrentUser.UserId;
//            Grade.UpdatedTime = DateTime.Now;
//            Grade.UserGradePermission = input.FuncIds.Select(x => new UserGradePermission()
//            {
//                System = CurrentUser.System,
//                FuncId = x
//            }).ToList();

//            await Db.SaveChangesAsync();
//        }
//    }
//}
