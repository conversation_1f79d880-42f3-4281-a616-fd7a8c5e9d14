#### [AlifeApi.DataAccess](index.md 'index')
### [AlifeApi.DataAccess.ProjectContext](AlifeApi.DataAccess.ProjectContext.md 'AlifeApi.DataAccess.ProjectContext')

## SysScheduleHistory Class

```csharp
public class SysScheduleHistory
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; SysScheduleHistory
### Properties

<a name='AlifeApi.DataAccess.ProjectContext.SysScheduleHistory.SsEndTime'></a>

## SysScheduleHistory.SsEndTime Property

排程結束時間

```csharp
public System.DateTime SsEndTime { get; set; }
```

#### Property Value
[System.DateTime](https://docs.microsoft.com/en-us/dotnet/api/System.DateTime 'System.DateTime')

<a name='<PERSON>feApi.DataAccess.ProjectContext.SysScheduleHistory.SsError'></a>

## SysScheduleHistory.SsError Property

錯誤訊息

```csharp
public string SsError { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContext.SysScheduleHistory.SsName'></a>

## SysScheduleHistory.SsName Property

排程名稱

```csharp
public string SsName { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContext.SysScheduleHistory.SsStartTime'></a>

## SysScheduleHistory.SsStartTime Property

排程開始時間

```csharp
public System.DateTime SsStartTime { get; set; }
```

#### Property Value
[System.DateTime](https://docs.microsoft.com/en-us/dotnet/api/System.DateTime 'System.DateTime')