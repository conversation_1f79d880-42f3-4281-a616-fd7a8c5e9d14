using AlifeApi.BusinessRules.Infrastructure;
using System.Text.Json.Serialization;

namespace AlifeApi.BusinessRules.CategoryModels
{
    /// <summary>
    /// 小分類查詢輸入 DTO
    /// </summary>
    public class SmallCategoryQueryInput : PagedListInput
    {
        /// <summary>
        /// 所屬大分類ID (篩選條件)
        /// </summary>
        [JsonPropertyName("LargeCategoryId")]
        public long? LargeCategoryId { get; set; }

        /// <summary>
        /// 所屬中分類ID (篩選條件)
        /// </summary>
        [JsonPropertyName("MediumCategoryId")]
        public long? MediumCategoryId { get; set; }

        /// <summary>
        /// 小分類名稱 (模糊查詢)
        /// </summary>
        [JsonPropertyName("Name")]
        public string Name { get; set; }

        /// <summary>
        /// 是否啟用 (null表示不篩選)
        /// </summary>
        [JsonPropertyName("IsActive")]
        public bool? IsActive { get; set; }
    }
} 
