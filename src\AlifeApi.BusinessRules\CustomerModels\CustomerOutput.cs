﻿using System;
using System.Text.Json.Serialization;

namespace AlifeApi.BusinessRules.CustomerModels
{
    /// <summary>
    /// 客戶資料輸出 DTO (詳細資訊)
    /// </summary>
    public class CustomerOutput
    {
        /// <summary>
        /// 客戶ID
        /// </summary>
        [JsonPropertyName("CustomerId")]
        public string CustomerId { get; set; }

        /// <summary>
        /// 客戶姓名
        /// </summary>
        [JsonPropertyName("Name")]
        public string Name { get; set; }

        /// <summary>
        /// 性別
        /// </summary>
        [JsonPropertyName("Gender")]
        public string Gender { get; set; }

        /// <summary>
        /// 生日
        /// </summary>
        [JsonPropertyName("Birthday")]
        public DateOnly? Birthday { get; set; }

        /// <summary>
        /// 縣市
        /// </summary>
        [JsonPropertyName("City")]
        public string City { get; set; }

        /// <summary>
        /// 區域
        /// </summary>
        [JsonPropertyName("District")]
        public string District { get; set; }

        /// <summary>
        /// 詳細地址
        /// </summary>
        [JsonPropertyName("Address")]
        public string Address { get; set; }

        /// <summary>
        /// 電話
        /// </summary>
        [JsonPropertyName("PhoneNumber")]
        public string PhoneNumber { get; set; }

        /// <summary>
        /// 電子信箱
        /// </summary>
        [JsonPropertyName("Email")]
        public string Email { get; set; }

        /// <summary>
        /// 職業
        /// </summary>
        [JsonPropertyName("Occupation")]
        public string Occupation { get; set; }

        /// <summary>
        /// 得知管道 (客戶來源)
        /// </summary>
        [JsonPropertyName("LeadSource")]
        public string LeadSource { get; set; }

        /// <summary>
        /// 需求坪數
        /// </summary>
        [JsonPropertyName("RequiredPingArea")]
        public string RequiredPingArea { get; set; }

        /// <summary>
        /// 需求格局
        /// </summary>
        [JsonPropertyName("RequiredLayout")]
        public string RequiredLayout { get; set; }

        /// <summary>
        /// 預算範圍
        /// </summary>
        [JsonPropertyName("Budget")]
        public string Budget { get; set; }

        /// <summary>
        /// 其他購屋條件/備註 (對應 Customer.PurchaseConditions)
        /// </summary>
        [JsonPropertyName("PurchaseConditions")]
        public string PurchaseConditions { get; set; }

        /// <summary>
        /// 案場代碼
        /// </summary>
        [JsonPropertyName("SiteCode")]
        public string SiteCode { get; set; }

        /// <summary>
        /// 圖片路徑 (對應 Customer.WebPath)
        /// </summary>
        [JsonPropertyName("ImagePath")]
        public string ImagePath { get; set; }

        /// <summary>
        /// 建立時間
        /// </summary>
        [JsonPropertyName("CreatedTime")]
        public DateTime CreatedTime { get; set; }

        /// <summary>
        /// 建立人員ID
        /// </summary>
        [JsonPropertyName("CreatedUserId")]
        public string CreatedUserId { get; set; }

        /// <summary>
        /// 建立人員名稱
        /// </summary>
        [JsonPropertyName("CreatedUserName")]
        public string CreatedUserName { get; set; }

        /// <summary>
        /// 更新時間
        /// </summary>
        [JsonPropertyName("UpdatedTime")]
        public DateTime UpdatedTime { get; set; }

        /// <summary>
        /// 更新人員ID
        /// </summary>
        [JsonPropertyName("UpdatedUserId")]
        public string UpdatedUserId { get; set; }

        /// <summary>
        /// 更新人員名稱
        /// </summary>
        [JsonPropertyName("UpdatedUserName")]
        public string UpdatedUserName { get; set; }

        /// <summary>
        /// 客戶訪談紀錄列表
        /// </summary>
        [JsonPropertyName("CustomerRecords")]
        public List<CustomerRecordOutput> CustomerRecords { get; set; }
    }
}
