using System;
using System.Text.Json.Serialization;

namespace AlifeApi.BusinessRules.CategoryModels
{
    /// <summary>
    /// 小分類資料輸出 DTO
    /// </summary>
    public class SmallCategoryOutput
    {
        /// <summary>
        /// 小分類ID
        /// </summary>
        [JsonPropertyName("SmallCategoryId")]
        public long SmallCategoryId { get; set; }

        /// <summary>
        /// 所屬中分類ID
        /// </summary>
        [JsonPropertyName("MediumCategoryId")]
        public long MediumCategoryId { get; set; }

        /// <summary>
        /// 所屬中分類名稱
        /// </summary>
        [JsonPropertyName("MediumCategoryName")]
        public string MediumCategoryName { get; set; }

        /// <summary>
        /// 所屬大分類ID
        /// </summary>
        [JsonPropertyName("LargeCategoryId")]
        public long LargeCategoryId { get; set; }

        /// <summary>
        /// 所屬大分類名稱
        /// </summary>
        [JsonPropertyName("LargeCategoryName")]
        public string LargeCategoryName { get; set; }

        /// <summary>
        /// 小分類名稱
        /// </summary>
        [JsonPropertyName("Name")]
        public string Name { get; set; }

        /// <summary>
        /// 小分類描述
        /// </summary>
        [JsonPropertyName("Description")]
        public string Description { get; set; }

        /// <summary>
        /// 排序順序
        /// </summary>
        [JsonPropertyName("SortOrder")]
        public int SortOrder { get; set; }

        /// <summary>
        /// 是否啟用
        /// </summary>
        [JsonPropertyName("IsActive")]
        public bool? IsActive { get; set; }

        /// <summary>
        /// 建立時間
        /// </summary>
        [JsonPropertyName("CreateTime")]
        public DateTime CreateTime { get; set; }

        /// <summary>
        /// 建立人員ID
        /// </summary>
        [JsonPropertyName("CreatedUserInfoId")]
        public string CreatedUserInfoId { get; set; }

        /// <summary>
        /// 建立人員名稱
        /// </summary>
        [JsonPropertyName("CreatedUserName")]
        public string CreatedUserName { get; set; }

        /// <summary>
        /// 更新時間
        /// </summary>
        [JsonPropertyName("UpdateTime")]
        public DateTime UpdateTime { get; set; }

        /// <summary>
        /// 更新人員ID
        /// </summary>
        [JsonPropertyName("UpdatedUserInfoId")]
        public string UpdatedUserInfoId { get; set; }

        /// <summary>
        /// 更新人員名稱
        /// </summary>
        [JsonPropertyName("UpdatedUserName")]
        public string UpdatedUserName { get; set; }
    }
} 
