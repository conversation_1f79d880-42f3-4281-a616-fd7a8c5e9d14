﻿using AlifeApi.BusinessRules.Infrastructure;
using AlifeApi.BusinessRules.RoleGroupModels;
using Microsoft.AspNetCore.Mvc;

namespace AlifeApi.WebApi.Controllers
{
    /// <summary>
    /// 角色權限(RoleGroup)
    /// </summary>
    public class RoleGroupController : AuthenticatedController
    {
        ///// <summary>
        ///// 商業邏輯類別
        ///// </summary>
        //private readonly RoleGroupService _roleGroupService;

        ///// <summary>
        ///// Initializes a new instance of the <see cref="RoleGroupController"/> class.
        ///// </summary>
        ///// <param name="roleGroupService">The role group service.</param>
        ///// <exception cref="ArgumentNullException">roleGroupService</exception>
        //public RoleGroupController(RoleGroupService roleGroupService)
        //{
        //    _roleGroupService = roleGroupService ?? throw new ArgumentNullException(nameof(roleGroupService));
        //}

        ///// <summary>
        ///// 取得角色權限
        ///// </summary>
        ///// <param name="input">The input.</param>
        ///// <returns>角色權限</returns>
        //[HttpPost]
        //public async Task<PagedListOutput<RoleGroupOutput>> GetRoleGroupListAsync(RoleGroupListGetInput input)
        //    => await _roleGroupService.GetRoleGroupListAsync(input);

        ///// <summary>
        ///// 新增角色權限
        ///// </summary>
        ///// <param name="input">The input.</param>
        //[HttpPost]
        //public async Task CreateRoleGroupAsync(RoleGroupCreateInput input) => await _roleGroupService.CreateRoleGroupAsync(input);

        ///// <summary>
        ///// 修改角色權限功能
        ///// </summary>
        ///// <param name="input">The input.</param>
        //[HttpPost]
        //public async Task UpdateRoleGroupPermissionAsync(RoleGroupPermissionUpdateInput input)
        //    => await _roleGroupService.UpdateRoleGroupPermissionAsync(input);

        ///// <summary>
        ///// 修改角色權限使用者
        ///// </summary>
        ///// <param name="input">The input.</param>
        //[HttpPost]
        //public async Task UpdateRoleGroupUserAsync(RoleGroupUserUpdateInput input)
        //    => await _roleGroupService.UpdateRoleGroupUserAsync(input);

        ///// <summary>
        ///// 刪除角色
        ///// </summary>
        ///// <param name="input">The input.</param>
        //[HttpPost]
        //public async Task DeleteRoleGroupAsync(RoleGroupDeleteInput input) => await _roleGroupService.DeleteRoleGroupAsync(input);

        ///// <summary>
        ///// 取得權限樹
        ///// </summary>
        ///// <returns></returns>
        //[HttpPost]
        //public async Task<IEnumerable<MenuTreeOutput>> GetMenuTreeAsync() => await _roleGroupService.GetMenuTreesAsync();

        ///// <summary>
        ///// 取得角色群組下拉選單
        ///// </summary>
        ///// <returns>角色群組下拉選單列表</returns>
        //[HttpGet]
        //public async Task<ActionResult<List<RoleGroupDropdownOutput>>> GetRoleGroupDropdownListAsync()
        //{
        //    try
        //    {
        //        return await _roleGroupService.GetRoleGroupDropdownListAsync();
        //    }
        //    catch (Exception ex)
        //    {
        //        return BadRequest(ex.Message);
        //    }
        //}
    }
}
