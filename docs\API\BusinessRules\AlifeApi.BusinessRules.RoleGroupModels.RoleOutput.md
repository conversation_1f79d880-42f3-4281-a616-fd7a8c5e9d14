#### [AlifeApi.BusinessRules](index.md 'index')
### [AlifeApi.BusinessRules.RoleGroupModels](AlifeApi.BusinessRules.RoleGroupModels.md 'AlifeApi.BusinessRules.RoleGroupModels')

## RoleOutput Class

角色資料模型

```csharp
public class RoleOutput
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; RoleOutput
### Properties

<a name='AlifeApi.BusinessRules.RoleGroupModels.RoleOutput.Order'></a>

## RoleOutput.Order Property

排序編號  
在多角色的情況下  
方便前台能依序找到第1位角色

```csharp
public int Order { get; set; }
```

#### Property Value
[System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')

<a name='AlifeApi.BusinessRules.RoleGroupModels.RoleOutput.RoleId'></a>

## RoleOutput.RoleId Property

角色代碼

```csharp
public string RoleId { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.RoleGroupModels.RoleOutput.RoleName'></a>

## RoleOutput.RoleName Property

角色名稱

```csharp
public string RoleName { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')