#### [AlifeApi.BusinessRules](index.md 'index')
### [AlifeApi.BusinessRules.FloorModels](AlifeApi.BusinessRules.FloorModels.md 'AlifeApi.BusinessRules.FloorModels')

## FloorListOutput Class

樓層列表輸出項目

```csharp
public class FloorListOutput
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; FloorListOutput
### Properties

<a name='AlifeApi.BusinessRules.FloorModels.FloorListOutput.BuildingId'></a>

## FloorListOutput.BuildingId Property

建築物ID

```csharp
public int BuildingId { get; set; }
```

#### Property Value
[System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')

<a name='AlifeApi.BusinessRules.FloorModels.FloorListOutput.CreatedTime'></a>

## FloorListOutput.CreatedTime Property

建立時間

```csharp
public System.DateTime CreatedTime { get; set; }
```

#### Property Value
[System.DateTime](https://docs.microsoft.com/en-us/dotnet/api/System.DateTime 'System.DateTime')

<a name='AlifeApi.BusinessRules.FloorModels.FloorListOutput.FloorHeight'></a>

## FloorListOutput.FloorHeight Property

樓層高度

```csharp
public System.Nullable<decimal> FloorHeight { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.Decimal](https://docs.microsoft.com/en-us/dotnet/api/System.Decimal 'System.Decimal')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

<a name='AlifeApi.BusinessRules.FloorModels.FloorListOutput.FloorId'></a>

## FloorListOutput.FloorId Property

樓層唯一識別碼

```csharp
public int FloorId { get; set; }
```

#### Property Value
[System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')

<a name='AlifeApi.BusinessRules.FloorModels.FloorListOutput.FloorLabel'></a>

## FloorListOutput.FloorLabel Property

樓層標示

```csharp
public string FloorLabel { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.FloorModels.FloorListOutput.FloorLevel'></a>

## FloorListOutput.FloorLevel Property

樓層數值

```csharp
public int FloorLevel { get; set; }
```

#### Property Value
[System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')

<a name='AlifeApi.BusinessRules.FloorModels.FloorListOutput.FloorType'></a>

## FloorListOutput.FloorType Property

樓層類型

```csharp
public string FloorType { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.FloorModels.FloorListOutput.SiteCode'></a>

## FloorListOutput.SiteCode Property

案場代碼

```csharp
public string SiteCode { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.FloorModels.FloorListOutput.SiteName'></a>

## FloorListOutput.SiteName Property

案場名稱

```csharp
public string? SiteName { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')