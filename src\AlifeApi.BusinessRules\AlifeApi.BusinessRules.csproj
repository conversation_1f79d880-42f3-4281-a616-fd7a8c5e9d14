﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net6.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <AssemblyName>$(MSBuildProjectName)</AssemblyName>
    <RootNamespace>$(MSBuildProjectName.Replace(" ", "_"))</RootNamespace>
    <Company></Company>
    <Authors></Authors>
    <GenerateDocumentationFile>True</GenerateDocumentationFile>
    <DefaultDocumentationGeneratedPages>Namespaces,Types</DefaultDocumentationGeneratedPages>
    <DefaultDocumentationFolder>../../docs/API/BusinessRules</DefaultDocumentationFolder>
    <NoWarn>$(NoWarn);1591</NoWarn>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="DefaultDocumentation" Version="0.8.2">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.AspNetCore.Http.Features" Version="2.2.0" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="6.0.36" />
    <PackageReference Include="EPPlus" Version="6.0.8" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\AlifeApi.Common\AlifeApi.Common.csproj" />
    <ProjectReference Include="..\AlifeApi.DataAccess\AlifeApi.DataAccess.csproj" />
  </ItemGroup>

</Project>
