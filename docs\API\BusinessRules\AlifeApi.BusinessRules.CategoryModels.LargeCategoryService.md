#### [AlifeApi.BusinessRules](index.md 'index')
### [AlifeApi.BusinessRules.CategoryModels](AlifeApi.BusinessRules.CategoryModels.md 'AlifeApi.BusinessRules.CategoryModels')

## LargeCategoryService Class

大分類服務

```csharp
public class LargeCategoryService : AlifeApi.BusinessRules.Infrastructure.ServiceBase<AlifeApi.DataAccess.ProjectContent.alifeContext>
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; [AlifeApi.Common.InfrastructureBase](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.Common.InfrastructureBase 'AlifeApi.Common.InfrastructureBase') &#129106; [AlifeApi.BusinessRules.Infrastructure.ServiceBase&lt;](AlifeApi.BusinessRules.Infrastructure.ServiceBase_TDbContext_.md 'AlifeApi.BusinessRules.Infrastructure.ServiceBase<TDbContext>')[AlifeApi.DataAccess.ProjectContent.alifeContext](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.DataAccess.ProjectContent.alifeContext 'AlifeApi.DataAccess.ProjectContent.alifeContext')[&gt;](AlifeApi.BusinessRules.Infrastructure.ServiceBase_TDbContext_.md 'AlifeApi.BusinessRules.Infrastructure.ServiceBase<TDbContext>') &#129106; LargeCategoryService
### Methods

<a name='AlifeApi.BusinessRules.CategoryModels.LargeCategoryService.CreateLargeCategoryAsync(AlifeApi.BusinessRules.CategoryModels.LargeCategoryInput)'></a>

## LargeCategoryService.CreateLargeCategoryAsync(LargeCategoryInput) Method

建立大分類

```csharp
public System.Threading.Tasks.Task<long> CreateLargeCategoryAsync(AlifeApi.BusinessRules.CategoryModels.LargeCategoryInput input);
```
#### Parameters

<a name='AlifeApi.BusinessRules.CategoryModels.LargeCategoryService.CreateLargeCategoryAsync(AlifeApi.BusinessRules.CategoryModels.LargeCategoryInput).input'></a>

`input` [LargeCategoryInput](AlifeApi.BusinessRules.CategoryModels.LargeCategoryInput.md 'AlifeApi.BusinessRules.CategoryModels.LargeCategoryInput')

大分類建立輸入資料

#### Returns
[System.Threading.Tasks.Task&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')[System.Int64](https://docs.microsoft.com/en-us/dotnet/api/System.Int64 'System.Int64')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')  
新建大分類的ID

<a name='AlifeApi.BusinessRules.CategoryModels.LargeCategoryService.DeleteLargeCategoryAsync(long)'></a>

## LargeCategoryService.DeleteLargeCategoryAsync(long) Method

刪除大分類

```csharp
public System.Threading.Tasks.Task DeleteLargeCategoryAsync(long largeCategoryId);
```
#### Parameters

<a name='AlifeApi.BusinessRules.CategoryModels.LargeCategoryService.DeleteLargeCategoryAsync(long).largeCategoryId'></a>

`largeCategoryId` [System.Int64](https://docs.microsoft.com/en-us/dotnet/api/System.Int64 'System.Int64')

大分類ID

#### Returns
[System.Threading.Tasks.Task](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task 'System.Threading.Tasks.Task')

<a name='AlifeApi.BusinessRules.CategoryModels.LargeCategoryService.GetLargeCategoryByIdAsync(long)'></a>

## LargeCategoryService.GetLargeCategoryByIdAsync(long) Method

根據ID取得大分類詳細資料

```csharp
public System.Threading.Tasks.Task<AlifeApi.BusinessRules.CategoryModels.LargeCategoryOutput?> GetLargeCategoryByIdAsync(long largeCategoryId);
```
#### Parameters

<a name='AlifeApi.BusinessRules.CategoryModels.LargeCategoryService.GetLargeCategoryByIdAsync(long).largeCategoryId'></a>

`largeCategoryId` [System.Int64](https://docs.microsoft.com/en-us/dotnet/api/System.Int64 'System.Int64')

大分類ID

#### Returns
[System.Threading.Tasks.Task&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')[LargeCategoryOutput](AlifeApi.BusinessRules.CategoryModels.LargeCategoryOutput.md 'AlifeApi.BusinessRules.CategoryModels.LargeCategoryOutput')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')  
大分類詳細資料

<a name='AlifeApi.BusinessRules.CategoryModels.LargeCategoryService.GetLargeCategoryDropdownAsync()'></a>

## LargeCategoryService.GetLargeCategoryDropdownAsync() Method

取得大分類下拉選單列表

```csharp
public System.Threading.Tasks.Task<System.Collections.Generic.List<AlifeApi.BusinessRules.CategoryModels.LargeCategoryDropdownOutput>> GetLargeCategoryDropdownAsync();
```

#### Returns
[System.Threading.Tasks.Task&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')[System.Collections.Generic.List&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Collections.Generic.List-1 'System.Collections.Generic.List`1')[LargeCategoryDropdownOutput](AlifeApi.BusinessRules.CategoryModels.LargeCategoryDropdownOutput.md 'AlifeApi.BusinessRules.CategoryModels.LargeCategoryDropdownOutput')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Collections.Generic.List-1 'System.Collections.Generic.List`1')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')  
啟用的大分類下拉選單列表

<a name='AlifeApi.BusinessRules.CategoryModels.LargeCategoryService.GetLargeCategoryListAsync(AlifeApi.BusinessRules.CategoryModels.LargeCategoryQueryInput)'></a>

## LargeCategoryService.GetLargeCategoryListAsync(LargeCategoryQueryInput) Method

取得大分類列表 (分頁)

```csharp
public System.Threading.Tasks.Task<AlifeApi.BusinessRules.Infrastructure.PagedListOutput<AlifeApi.BusinessRules.CategoryModels.LargeCategoryListOutput>> GetLargeCategoryListAsync(AlifeApi.BusinessRules.CategoryModels.LargeCategoryQueryInput input);
```
#### Parameters

<a name='AlifeApi.BusinessRules.CategoryModels.LargeCategoryService.GetLargeCategoryListAsync(AlifeApi.BusinessRules.CategoryModels.LargeCategoryQueryInput).input'></a>

`input` [LargeCategoryQueryInput](AlifeApi.BusinessRules.CategoryModels.LargeCategoryQueryInput.md 'AlifeApi.BusinessRules.CategoryModels.LargeCategoryQueryInput')

查詢條件

#### Returns
[System.Threading.Tasks.Task&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')[AlifeApi.BusinessRules.Infrastructure.PagedListOutput&lt;](AlifeApi.BusinessRules.Infrastructure.PagedListOutput_TDetail_.md 'AlifeApi.BusinessRules.Infrastructure.PagedListOutput<TDetail>')[LargeCategoryListOutput](AlifeApi.BusinessRules.CategoryModels.LargeCategoryListOutput.md 'AlifeApi.BusinessRules.CategoryModels.LargeCategoryListOutput')[&gt;](AlifeApi.BusinessRules.Infrastructure.PagedListOutput_TDetail_.md 'AlifeApi.BusinessRules.Infrastructure.PagedListOutput<TDetail>')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')  
分頁的大分類列表

<a name='AlifeApi.BusinessRules.CategoryModels.LargeCategoryService.UpdateLargeCategoryAsync(long,AlifeApi.BusinessRules.CategoryModels.LargeCategoryInput)'></a>

## LargeCategoryService.UpdateLargeCategoryAsync(long, LargeCategoryInput) Method

更新大分類資訊

```csharp
public System.Threading.Tasks.Task UpdateLargeCategoryAsync(long largeCategoryId, AlifeApi.BusinessRules.CategoryModels.LargeCategoryInput input);
```
#### Parameters

<a name='AlifeApi.BusinessRules.CategoryModels.LargeCategoryService.UpdateLargeCategoryAsync(long,AlifeApi.BusinessRules.CategoryModels.LargeCategoryInput).largeCategoryId'></a>

`largeCategoryId` [System.Int64](https://docs.microsoft.com/en-us/dotnet/api/System.Int64 'System.Int64')

大分類ID

<a name='AlifeApi.BusinessRules.CategoryModels.LargeCategoryService.UpdateLargeCategoryAsync(long,AlifeApi.BusinessRules.CategoryModels.LargeCategoryInput).input'></a>

`input` [LargeCategoryInput](AlifeApi.BusinessRules.CategoryModels.LargeCategoryInput.md 'AlifeApi.BusinessRules.CategoryModels.LargeCategoryInput')

大分類更新輸入資料

#### Returns
[System.Threading.Tasks.Task](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task 'System.Threading.Tasks.Task')