#### [AlifeApi.BusinessRules](index.md 'index')
### [AlifeApi.BusinessRules.BulletinsModels](AlifeApi.BusinessRules.BulletinsModels.md 'AlifeApi.BusinessRules.BulletinsModels')

## AttachListItem Class

```csharp
public class AttachListItem
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; AttachListItem
### Properties

<a name='AlifeApi.BusinessRules.BulletinsModels.AttachListItem.File'></a>

## AttachListItem.File Property

附件

```csharp
public byte[] File { get; set; }
```

#### Property Value
[System.Byte](https://docs.microsoft.com/en-us/dotnet/api/System.Byte 'System.Byte')[[]](https://docs.microsoft.com/en-us/dotnet/api/System.Array 'System.Array')

<a name='AlifeApi.BusinessRules.BulletinsModels.AttachListItem.FileName'></a>

## AttachListItem.FileName Property

公告附件檔案名稱(須附副檔名)

```csharp
public string FileName { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')