﻿using System.Text.Json.Serialization;

namespace AlifeApi.BusinessRules.ProblemReportModels
{
    public class ProblemReportListItemGetOutput
    {
        /// <summary>
        /// 問題回報處理唯一識別值
        /// </summary>
        [JsonPropertyName("PR_Id")]
        public long Id { get; set; }

        /// <summary>
        /// 申報日期
        /// </summary>
        [JsonPropertyName("CreateDate")]
        public DateTime? CreatedTime { get; set; }

        /// <summary>
        /// 申報人
        /// </summary>
        [JsonPropertyName("CreateUserId")]
        public string CreatedUserId { get; set; }

        /// <summary>
        /// 申報人名稱
        /// </summary>
        [JsonPropertyName("CreateUserName")]
        public string CreatedUserName { get; set; }

        /// <summary>
        /// 單位 ID
        /// </summary>
        [JsonPropertyName("Dept")]
        public string DeptId { get; set; }

        /// <summary>
        /// 單位名稱
        /// </summary>
        public string DeptName { get; set; }

        /// <summary>
        /// 功能頁面
        /// </summary>
        public string Menu { get; set; }

        /// <summary>
        /// 問題主旨
        /// </summary>
        public string Subject { get; set; }

        /// <summary>
        /// 問題內容
        /// </summary>
        public string ReportContent { get; set; }

        /// <summary>
        /// 處理狀態
        /// </summary>
        public string ProcessStatus { get; set; }

        /// <summary>
        /// 處理狀態名稱
        /// </summary>
        public string ProcessStatusName { get; set; }

        /// <summary>
        /// 類型
        /// </summary>
        public string ProblemType { get; set; }

        /// <summary>
        /// 類型名稱
        /// </summary>
        public string ProblemTypeName { get; set; }

        /// <summary>
        /// 回覆問題內容
        /// </summary>
        public string ReplyContent { get; set; }

        /// <summary>
        /// 回覆人員
        /// </summary>
        public string ReplyUserId { get; set; }

        /// <summary>
        /// 回覆人員名稱
        /// </summary>
        public string ReplyUserName { get; set; }

        /// <summary>
        /// 處理時間
        /// </summary>
        [JsonPropertyName("ReplyDate")]
        public DateTime? ReplyTime { get; set; }
    }
}
