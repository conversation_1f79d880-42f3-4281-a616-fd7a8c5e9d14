﻿namespace AlifeApi.BusinessRules.SiteModels
{
    /// <summary>
    /// 案場輸出
    /// </summary>
    public class SiteOutput
    {
        /// <summary>
        /// 案場代碼
        /// </summary>
        public string SiteCode { get; set; }

        /// <summary>
        /// 執行公司別
        /// </summary>
        public string CompanyId { get; set; }

        /// <summary>
        /// 案場名稱
        /// </summary>
        public string SiteName { get; set; }

        /// <summary>
        /// 推案型態
        /// </summary>
        public string PromotionType { get; set; }

        /// <summary>
        /// 主委
        /// </summary>
        public string Chairman { get; set; }

        /// <summary>
        /// 副主委
        /// </summary>
        public string ViceChairman { get; set; }

        /// <summary>
        /// 專案
        /// </summary>
        public string ProjectManager { get; set; }

        /// <summary>
        /// 副專
        /// </summary>
        public string DeputyProjectManager { get; set; }

        /// <summary>
        /// 業務
        /// </summary>
        public string BusinessIds { get; set; }

        /// <summary>
        /// 跑單
        /// </summary>
        public string RunnerIds { get; set; }

        /// <summary>
        /// 接待中心
        /// </summary>
        public string ReceptionCenter { get; set; }

        /// <summary>
        /// 案場電話
        /// </summary>
        public string SitePhone { get; set; }

        /// <summary>
        /// 經紀人
        /// </summary>
        public string Broker { get; set; }

        /// <summary>
        /// 所在縣市
        /// </summary>
        public string City { get; set; }

        /// <summary>
        /// 所在區域
        /// </summary>
        public string District { get; set; }

        /// <summary>
        /// 投資興建
        /// </summary>
        public string Developer { get; set; }

        /// <summary>
        /// 基地位置
        /// </summary>
        public string SiteLocation { get; set; }

        /// <summary>
        /// 完整地址
        /// </summary>
        public string Address { get; set; }

        /// <summary>
        /// 基地面積
        /// </summary>
        public decimal? LandArea { get; set; }

        /// <summary>
        /// 地上層數
        /// </summary>
        public string AboveGroundFloors { get; set; }

        /// <summary>
        /// 地下層數
        /// </summary>
        public string BelowGroundFloors { get; set; }

        /// <summary>
        /// 使用分區
        /// </summary>
        public string Zoning { get; set; }

        /// <summary>
        /// 公設比
        /// </summary>
        public decimal? PublicFacilityRatio { get; set; }

        /// <summary>
        /// 結構
        /// </summary>
        public string Structure { get; set; }

        /// <summary>
        /// 規劃戶數(住家)
        /// </summary>
        public int? PlannedResidentialUnits { get; set; }

        /// <summary>
        /// 規劃戶數(店面)
        /// </summary>
        public int? PlannedStoreUnits { get; set; }

        /// <summary>
        /// 規劃戶數(車位)
        /// </summary>
        public int? PlannedParkingSpaces { get; set; }

        /// <summary>
        /// 坪數
        /// </summary>
        public string UnitSize { get; set; }

        /// <summary>
        /// 全案總銷
        /// </summary>
        public decimal? TotalSalePrice { get; set; }

        /// <summary>
        /// 車位類別
        /// </summary>
        public string ParkingType { get; set; }

        /// <summary>
        /// 合約期間
        /// </summary>
        public DateOnly? ContractPeriod { get; set; }

        /// <summary>
        /// 展延期間
        /// </summary>
        public DateOnly? ExtensionPeriod { get; set; }

        /// <summary>
        /// 可售總銷
        /// </summary>
        public decimal? SellableTotalPrice { get; set; }

        /// <summary>
        /// 服務費計算
        /// </summary>
        public string ServiceFeeCalculation { get; set; }

        /// <summary>
        /// 服務費率
        /// </summary>
        public decimal? ServiceFeeRate { get; set; }

        /// <summary>
        /// 保留款
        /// </summary>
        public decimal? ReserveAmount { get; set; }

        /// <summary>
        /// 應編廣告預算
        /// </summary>
        public decimal? AdvertisingBudget { get; set; }

        /// <summary>
        /// 廣告預算率
        /// </summary>
        public decimal? AdvertisingBudgetRate { get; set; }

        /// <summary>
        /// 超價款分配
        /// </summary>
        public string ExcessPriceAllocation { get; set; }

        /// <summary>
        /// 已發包金額
        /// </summary>
        public decimal? ContractedAmount { get; set; }

        /// <summary>
        /// 控存率
        /// </summary>
        public decimal? ControlReserveRate { get; set; }

        /// <summary>
        /// 已請款金額
        /// </summary>
        public decimal? PaidAmount { get; set; }

        /// <summary>
        /// 創建者ID
        /// </summary>
        public string CreatedUserId { get; set; }

        /// <summary>
        /// 創建者名稱
        /// </summary>
        public string CreatedUserName { get; set; }

        /// <summary>
        /// 創建時間
        /// </summary>
        public DateTime? CreatedTime { get; set; }

        /// <summary>
        /// 更新者ID
        /// </summary>
        public string UpdatedUserId { get; set; }

        /// <summary>
        /// 更新者名稱
        /// </summary>
        public string UpdatedUserName { get; set; }

        /// <summary>
        /// 更新時間
        /// </summary>
        public DateTime? UpdatedTime { get; set; }
    }
} 
