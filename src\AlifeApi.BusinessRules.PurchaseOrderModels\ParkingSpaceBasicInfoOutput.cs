using System;

namespace AlifeApi.BusinessRules.PurchaseOrderModels
{
    /// <summary>
    /// 停車位基本資訊輸出模型 (用於 PurchaseOrderOutput)
    /// </summary>
    public class ParkingSpaceBasicInfoOutput
    {
        /// <summary>
        /// 車位唯一識別碼
        /// </summary>
        public int ParkingSpaceId { get; set; }

        /// <summary>
        /// 車位編號
        /// </summary>
        public string SpaceNumber { get; set; } = null!;

        /// <summary>
        /// 車位類型
        /// </summary>
        public string SpaceType { get; set; } = null!;

        /// <summary>
        /// 樓層標示 (選填)
        /// </summary>
        public string? FloorLabel { get; set; }

        /// <summary>
        /// 建築物名稱 (選填)
        /// </summary>
        public string? BuildingName { get; set; }
    }
} 
