#### [AlifeApi.BusinessRules](index.md 'index')
### [AlifeApi.BusinessRules.ParkingSpaceModels](AlifeApi.BusinessRules.ParkingSpaceModels.md 'AlifeApi.BusinessRules.ParkingSpaceModels')

## ParkingSpaceListOutput Class

停車位列表輸出項目

```csharp
public class ParkingSpaceListOutput
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; ParkingSpaceListOutput
### Properties

<a name='AlifeApi.BusinessRules.ParkingSpaceModels.ParkingSpaceListOutput.Dimensions'></a>

## ParkingSpaceListOutput.Dimensions Property

車位尺寸 (例如 "250x550cm")

```csharp
public string? Dimensions { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.ParkingSpaceModels.ParkingSpaceListOutput.Location'></a>

## ParkingSpaceListOutput.Location Property

車位詳細位置描述 (例如 "靠近電梯", "角落位置")

```csharp
public string? Location { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.ParkingSpaceModels.ParkingSpaceListOutput.Remarks'></a>

## ParkingSpaceListOutput.Remarks Property

備註 (例如 "柱子較多", "出入較便利")

```csharp
public string? Remarks { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')