﻿using AlifeApi.BusinessRules.Infrastructure;
using AlifeApi.BusinessRules.RoleGroupModels;
using AlifeApi.BusinessRules.SecuritySettingModels;
using AlifeApi.BusinessRules.UserInfoModels;
using AlifeApi.BusinessRules.UserRecordModels;
using AlifeApi.DataAccess.ProjectContext;
using Microsoft.EntityFrameworkCore;

//namespace AlifeApi.BusinessRules.LoginModels
//{
//    /// <summary>
//    /// 處理使用者登入邏輯
//    /// </summary>
//    public class LoginService : ServiceBase<ProjectContext>
//    {
//        private readonly PasswordManager _passwordManager;
//        private readonly RoleGroupService _roleGroupService;
//        private readonly UserRecordService _userRecordService;

//        public LoginService(IServiceProvider serviceProvider, ProjectContext dbContext, PasswordManager passwordManager, RoleGroupService roleGroupService, UserRecordService userRecordService) : base(serviceProvider, dbContext)
//        {
//            _passwordManager = passwordManager ?? throw new ArgumentNullException(nameof(passwordManager));
//            _roleGroupService = roleGroupService ?? throw new ArgumentNullException(nameof(roleGroupService));
//            _userRecordService = userRecordService ?? throw new ArgumentNullException(nameof(userRecordService));
//        }

        /// <summary>
        /// 使用者-使用者登入
        /// </summary>
        /// <param name="input">The input.</param>
        /// <returns>登入結果</returns>
        //public async Task<UserLoginOutput> UserLoginAsync(UserLoginInput input)
        //{
        //    var SecuritySetting = await Db.SysSystemSetting.AsNoTracking().AsQueryable().Where(x => x.Type == "SecuritySetting" && x.IsEnabled == true).Select(x => new SecuritySettingModel()
        //    {
        //        Key = x.Key,
        //        Value = x.Value,
        //        Name = x.Name,
        //        Enabled = (bool)x.IsEnabled
        //    }).ToListAsync();

        //    UserLoginOutput output = new();

        //    UserInfo user = await Db.UserInfo.SingleOrDefaultAsync(x => x.Id == input.UserId);

        //    if (user is null)
        //    {
        //        output.Response = Message.AccountFail;
        //        return output;
        //    }
        //    else if (!user.IsEnabled)
        //    {
        //        output.Response = Message.CheckAccountIsValid;
        //        return output;
        //    }

        //    if (user.LoginFailedCount >= Convert.ToInt32(SecuritySetting.FirstOrDefault(x => x.Key == "LoginFailedCount")?.Value ?? int.MaxValue.ToString())
        //        && (DateTime.Now - user.LastLoginTime) < TimeSpan.FromMinutes(Convert.ToInt32(SecuritySetting.FirstOrDefault(x => x.Key == "LockAccountTime")?.Value ?? TimeSpan.MinValue.TotalMinutes.ToString())))
        //    {
        //        output.Response = Message.AccountLoginFailTooManyTimeNeedToWait;
        //        return output;
        //    }

        //    user.LastLoginIp = CurrentUser.IPAddress;
        //    user.LastLoginTime = DateTime.Now;

        //    if (!_passwordManager.VerifyHashedPassword(user.Pw, input.UserPw))
        //    {
        //        user.LoginFailedCount++;
        //        await Db.SaveChangesAsync();

        //        output.Response = Message.PasswordFail;
        //        return output;
        //    }

        //    user.LoginFailedCount = 0;
        //    if (user.Pw == await _passwordManager.GetDefaultPasswordAsync())
        //    {
        //        output.Response = Message.DefaultPassword;
        //        return output;
        //    }

        //    UserPasswordHistory userHistory = Db.UserPasswordHistory.OrderByDescending(x => x.Id).FirstOrDefault(x => x.UserId == input.UserId);

        //    if ((DateTime.Now - userHistory.CreatedTime) >= TimeSpan.FromDays(Convert.ToInt32(SecuritySetting.FirstOrDefault(x => x.Key == "ChangCycleDays")?.Value ?? TimeSpan.MaxValue.TotalDays.ToString())))
        //    {
        //        output.Response = Message.PasswordChange;
        //        return output;
        //    }

        //    user.LoginFailedCount = 0;
        //    await Db.SaveChangesAsync();

        //    output = await Db.UserInfo.AsNoTracking()
        //        .Where(x => x.Id == input.UserId && x.IsEnabled)
        //        .Select(x => new UserLoginOutput
        //        {
        //            UserId = x.Id,
        //            UserPw = x.Pw,
        //            UserName = x.Name ?? "",
        //            GradeCode = x.GradeCode ?? "",
        //            GradeName = x.GradeCodeNavigation.GradeName ?? "",
        //            DeptId = x.DeptId ?? "",
        //            DeptName = x.Dept.DeptName ?? "",
        //            Roles = x.SysRoleGroupUser.Select(y => new RoleOutput
        //            {
        //                RoleId = y.RoleGroupId,
        //                RoleName = y.SysRoleGroup.Name
        //            }),
        //            RoleFuncIds = x.SysRoleGroupUser.SelectMany(y => y.SysRoleGroup.SysRoleGroupPermission.Select(z => z.FuncId)),
        //            DeptFuncIds = x.Dept.UserDeptPermission.Select(z => z.FuncId),
        //            GradeFuncIds = x.GradeCodeNavigation.UserGradePermission.Select(z => z.FuncId),
        //            IdNo = x.IdNo,
        //            Email = x.Email,
        //            IsAdmin = x.SysRoleGroupUser.Any(y => y.SysRoleGroup.IsAdmin)
        //        })
        //        .SingleAsync();

        //    output.MenuTrees = await _roleGroupService.GetMenuTreesAsync(output.FuncIds);
        //    output.IP = CurrentUser.IPAddress;
        //    output.Response = Message.Success;

        //    return output;
        //}


        ///// <summary>
        ///// 帳號登出
        ///// </summary>
        ///// <param name="userId"></param>
        ///// <returns></returns>
        //public async Task UserAutoLogOut(string userId, string ip)
        //{
        //    var user = Db.UserInfo.AsNoTracking().FirstOrDefault(x => x.Id == userId);


        //    if (user != null)
        //    {
        //        if (user != null)
        //        {
        //            user.LastLogoutTime = DateTime.Now;
        //            Db.Entry(user).State = EntityState.Modified;
        //        }
        //        var record = new SysUserRecord()
        //        {
        //            UserId = user.Id,
        //            DeptId = user.DeptId,
        //            GradeCode = user.GradeCode,
        //            RecordTime = DateTime.Now,
        //            RecordEvent = "UserAutoLogOut",
        //            InputData = "input:{\"userId\":\"" + userId + "\"}]}.",
        //            Ip = ip
        //        };
        //        Db.SysUserRecord.Add(record);
        //    }
        //    Db.SaveChanges();
        //}
//    }
//}
