﻿using System.Text.Json.Serialization;
using AlifeApi.BusinessRules.RoleGroupModels;

namespace AlifeApi.BusinessRules.UserInfoModels
{
    public class UserInfoListItemGetOutput
    {
        private IEnumerable<RoleOutput> roles = Enumerable.Empty<RoleOutput>();

        /// <summary>
        /// 員工編號
        /// </summary>
        [JsonPropertyName("U_ID")]
        public string Id { get; set; }

        /// <summary>
        /// 使用者名稱
        /// </summary>
        public string UserName { get; set; }

        /// <summary>
        /// 使用者密碼
        /// </summary>
        [JsonPropertyName("UserPW")]
        public string UserPw { get; set; }

        /// <summary>
        /// 使用者員編
        /// </summary>
        [JsonPropertyName("UserIDNO")]
        public string UserIdNo { get; set; }

        /// <summary>
        /// 使用者職稱
        /// </summary>
        [JsonPropertyName("Grade")]
        public string GradeCode { get; set; }

        /// <summary>
        /// 使用者職稱
        /// </summary>
        public string GradeName { get; set; }

        /// <summary>
        /// 使用者單位
        /// </summary>
        [JsonPropertyName("Dept")]
        public string DeptId { get; set; }

        /// <summary>
        /// 使用者單位
        /// </summary>
        public string DeptName { get; set; }

        /// <summary>
        /// Email
        /// </summary>
        public string Email { get; set; }

        /// <summary>
        /// 是否啟用
        /// </summary>
        [JsonPropertyName("Check")]
        public bool IsEnabled { get; set; }

        /// <summary>
        /// 最新登入 IP
        /// </summary>
        public string LastLoginIP { get; set; }

        /// <summary>
        /// 最新登入時間
        /// </summary>
        public DateTime? LastLoginTime { get; set; }

        /// <summary>
        /// 登入失敗次數加總
        /// </summary>
        public short LoginFailedCount { get; set; }

        /// <summary>
        /// 是否鎖定
        /// </summary>
        public bool IsLock { get; set; }

        /// <summary>
        /// 是否為管理者
        /// </summary>
        public bool IsAdmin { get; set; }

        /// <summary>
        /// 角色群組
        /// </summary>
        public IEnumerable<RoleOutput> Roles
        {
            get
            {
                return roles.Select((x, i) =>
                {
                    x.Order = i + 1;
                    return x;
                });
            }
            set => roles = value;
        }
    }
}
