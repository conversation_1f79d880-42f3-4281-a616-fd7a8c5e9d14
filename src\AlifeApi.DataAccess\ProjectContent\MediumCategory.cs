﻿using System;
using System.Collections.Generic;

namespace AlifeApi.DataAccess.ProjectContent
{
    /// <summary>
    /// 商品中分類資料表
    /// </summary>
    public partial class MediumCategory
    {
        /// <summary>
        /// 主鍵，中分類唯一識別碼
        /// </summary>
        public long MediumCategoryId { get; set; }
        /// <summary>
        /// 所屬大分類的ID，需由應用程式確保其有效性
        /// </summary>
        public long LargeCategoryId { get; set; }
        /// <summary>
        /// 中分類的顯示名稱
        /// </summary>
        public string Name { get; set; }
        /// <summary>
        /// 中分類的詳細文字描述
        /// </summary>
        public string Description { get; set; }
        /// <summary>
        /// 在同一個大分類下的排序順序，數字越小越前面
        /// </summary>
        public int SortOrder { get; set; }
        /// <summary>
        /// 是否啟用此分類 (TRUE: 啟用, FALSE: 停用)
        /// </summary>
        public bool? IsActive { get; set; }
        /// <summary>
        /// 建立者，記錄創建此資料的員工編號。
        /// </summary>
        public string CreatedUserInfoId { get; set; }
        /// <summary>
        /// 創建時間，記錄資料創建的時間。
        /// </summary>
        public DateTime CreateTime { get; set; }
        /// <summary>
        /// 更新者，記錄最後更新此資料的員工編號。
        /// </summary>
        public string UpdatedUserInfoId { get; set; }
        /// <summary>
        /// 更新時間，記錄資料最後更新的時間。
        /// </summary>
        public DateTime UpdateTime { get; set; }
    }
}
