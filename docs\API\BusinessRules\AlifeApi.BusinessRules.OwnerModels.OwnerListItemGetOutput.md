#### [AlifeApi.BusinessRules](index.md 'index')
### [AlifeApi.BusinessRules.OwnerModels](AlifeApi.BusinessRules.OwnerModels.md 'AlifeApi.BusinessRules.OwnerModels')

## OwnerListItemGetOutput Class

業主列表項目輸出模型

```csharp
public class OwnerListItemGetOutput
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; OwnerListItemGetOutput
### Properties

<a name='AlifeApi.BusinessRules.OwnerModels.OwnerListItemGetOutput.CompanyName'></a>

## OwnerListItemGetOutput.CompanyName Property

公司名稱

```csharp
public string CompanyName { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='<PERSON>feApi.BusinessRules.OwnerModels.OwnerListItemGetOutput.ContactPerson'></a>

## OwnerListItemGetOutput.ContactPerson Property

主要聯絡窗口人員姓名

```csharp
public string? ContactPerson { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.OwnerModels.OwnerListItemGetOutput.ContactPhone1'></a>

## OwnerListItemGetOutput.ContactPhone1 Property

主要聯絡電話

```csharp
public string? ContactPhone1 { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.OwnerModels.OwnerListItemGetOutput.IdentificationNumber'></a>

## OwnerListItemGetOutput.IdentificationNumber Property

證號 (依人別決定是 統一編號 或 身分證ID)

```csharp
public string IdentificationNumber { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.OwnerModels.OwnerListItemGetOutput.OwnerId'></a>

## OwnerListItemGetOutput.OwnerId Property

業主唯一識別碼

```csharp
public int OwnerId { get; set; }
```

#### Property Value
[System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')

<a name='AlifeApi.BusinessRules.OwnerModels.OwnerListItemGetOutput.PersonType'></a>

## OwnerListItemGetOutput.PersonType Property

人別 (值為 法人 或 自然人)

```csharp
public string PersonType { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.OwnerModels.OwnerListItemGetOutput.ResponsiblePerson'></a>

## OwnerListItemGetOutput.ResponsiblePerson Property

負責人姓名

```csharp
public string? ResponsiblePerson { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')