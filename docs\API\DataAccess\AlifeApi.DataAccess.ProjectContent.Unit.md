#### [AlifeApi.DataAccess](index.md 'index')
### [AlifeApi.DataAccess.ProjectContent](AlifeApi.DataAccess.ProjectContent.md 'AlifeApi.DataAccess.ProjectContent')

## Unit Class

儲存住宅/商業樓層上具體可銷售的獨立房屋單位(戶)的詳細資訊與狀態。**此表為必要**，對應銷控表主體格子。

```csharp
public class Unit
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; Unit
### Properties

<a name='AlifeApi.DataAccess.ProjectContent.Unit.AuxiliaryArea'></a>

## Unit.AuxiliaryArea Property

附屬建物坪數。

```csharp
public System.Nullable<decimal> AuxiliaryArea { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.Decimal](https://docs.microsoft.com/en-us/dotnet/api/System.Decimal 'System.Decimal')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

<a name='AlifeApi.DataAccess.ProjectContent.Unit.AwningArea'></a>

## Unit.AwningArea Property

雨遮面積 (坪)

```csharp
public System.Nullable<decimal> AwningArea { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.Decimal](https://docs.microsoft.com/en-us/dotnet/api/System.Decimal 'System.Decimal')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

<a name='AlifeApi.DataAccess.ProjectContent.Unit.BalconyArea'></a>

## Unit.BalconyArea Property

陽台面積 (坪)

```csharp
public System.Nullable<decimal> BalconyArea { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.Decimal](https://docs.microsoft.com/en-us/dotnet/api/System.Decimal 'System.Decimal')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

<a name='AlifeApi.DataAccess.ProjectContent.Unit.BuildingId'></a>

## Unit.BuildingId Property

所屬建築物識別碼 (參考 Buildings 表) - 方便查詢用。

```csharp
public int BuildingId { get; set; }
```

#### Property Value
[System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')

<a name='AlifeApi.DataAccess.ProjectContent.Unit.CreatedTime'></a>

## Unit.CreatedTime Property

資料創建時間 (TIMESTAMP WITHOUT TIME ZONE)。

```csharp
public System.DateTime CreatedTime { get; set; }
```

#### Property Value
[System.DateTime](https://docs.microsoft.com/en-us/dotnet/api/System.DateTime 'System.DateTime')

<a name='AlifeApi.DataAccess.ProjectContent.Unit.CreatedUserInfoId'></a>

## Unit.CreatedUserInfoId Property

建立者 (參考 UserInfo 表)。

```csharp
public string CreatedUserInfoId { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContent.Unit.FloorId'></a>

## Unit.FloorId Property

所屬樓層識別碼 (參考 Floors 表中 FloorType 為 '住宅'/'商業' 的樓層ID)。

```csharp
public int FloorId { get; set; }
```

#### Property Value
[System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')

<a name='AlifeApi.DataAccess.ProjectContent.Unit.IsPublicAreaIncluded'></a>

## Unit.IsPublicAreaIncluded Property

權狀是否含公設 (True/False)。

```csharp
public System.Nullable<bool> IsPublicAreaIncluded { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.Boolean](https://docs.microsoft.com/en-us/dotnet/api/System.Boolean 'System.Boolean')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

<a name='AlifeApi.DataAccess.ProjectContent.Unit.LargePublicArea'></a>

## Unit.LargePublicArea Property

大公面積 (坪)

```csharp
public System.Nullable<decimal> LargePublicArea { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.Decimal](https://docs.microsoft.com/en-us/dotnet/api/System.Decimal 'System.Decimal')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

<a name='AlifeApi.DataAccess.ProjectContent.Unit.Layout'></a>

## Unit.Layout Property

格局 (例如: "3房2廳2衛")。

```csharp
public string Layout { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContent.Unit.ListPrice'></a>

## Unit.ListPrice Property

表價 (牌價)。

```csharp
public System.Nullable<decimal> ListPrice { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.Decimal](https://docs.microsoft.com/en-us/dotnet/api/System.Decimal 'System.Decimal')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

<a name='AlifeApi.DataAccess.ProjectContent.Unit.MainArea'></a>

## Unit.MainArea Property

主建物坪數。

```csharp
public System.Nullable<decimal> MainArea { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.Decimal](https://docs.microsoft.com/en-us/dotnet/api/System.Decimal 'System.Decimal')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

<a name='AlifeApi.DataAccess.ProjectContent.Unit.MinimumPrice'></a>

## Unit.MinimumPrice Property

底價。

```csharp
public System.Nullable<decimal> MinimumPrice { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.Decimal](https://docs.microsoft.com/en-us/dotnet/api/System.Decimal 'System.Decimal')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

<a name='AlifeApi.DataAccess.ProjectContent.Unit.Orientation'></a>

## Unit.Orientation Property

座向。

```csharp
public string Orientation { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContent.Unit.PublicAreaShare'></a>

## Unit.PublicAreaShare Property

公設分攤坪數。

```csharp
public System.Nullable<decimal> PublicAreaShare { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.Decimal](https://docs.microsoft.com/en-us/dotnet/api/System.Decimal 'System.Decimal')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

<a name='AlifeApi.DataAccess.ProjectContent.Unit.Remarks'></a>

## Unit.Remarks Property

備註。

```csharp
public string Remarks { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContent.Unit.SiteCode'></a>

## Unit.SiteCode Property

所屬案場編號 (參考 Sites 表) - 方便查詢用。

```csharp
public string SiteCode { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContent.Unit.SmallPublicArea'></a>

## Unit.SmallPublicArea Property

小公面積 (坪)

```csharp
public System.Nullable<decimal> SmallPublicArea { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.Decimal](https://docs.microsoft.com/en-us/dotnet/api/System.Decimal 'System.Decimal')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

<a name='AlifeApi.DataAccess.ProjectContent.Unit.Status'></a>

## Unit.Status Property

單位目前的**獨立銷售狀態** (例如: '保留','業主保留','地主保留','可售','售','足','簽')。

```csharp
public string Status { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContent.Unit.TotalArea'></a>

## Unit.TotalArea Property

權狀總坪數。

```csharp
public decimal TotalArea { get; set; }
```

#### Property Value
[System.Decimal](https://docs.microsoft.com/en-us/dotnet/api/System.Decimal 'System.Decimal')

<a name='AlifeApi.DataAccess.ProjectContent.Unit.TransactionPrice'></a>

## Unit.TransactionPrice Property

實際成交價 (成交後填入)。

```csharp
public System.Nullable<decimal> TransactionPrice { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.Decimal](https://docs.microsoft.com/en-us/dotnet/api/System.Decimal 'System.Decimal')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

<a name='AlifeApi.DataAccess.ProjectContent.Unit.UnitId'></a>

## Unit.UnitId Property

房屋單位唯一識別碼 (主鍵, 自動遞增)。

```csharp
public int UnitId { get; set; }
```

#### Property Value
[System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')

<a name='AlifeApi.DataAccess.ProjectContent.Unit.UnitNumber'></a>

## Unit.UnitNumber Property

戶號 (銷控表橫軸，例如: "A", "B", "C")。

```csharp
public string UnitNumber { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContent.Unit.UnitType'></a>

## Unit.UnitType Property

單位類型 (例如: "住宅", "店面", "車位" - 建議關聯 SYS_Code)

```csharp
public string UnitType { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContent.Unit.UpdatedTime'></a>

## Unit.UpdatedTime Property

資料最後更新時間 (TIMESTAMP WITHOUT TIME ZONE)。

```csharp
public System.DateTime UpdatedTime { get; set; }
```

#### Property Value
[System.DateTime](https://docs.microsoft.com/en-us/dotnet/api/System.DateTime 'System.DateTime')

<a name='AlifeApi.DataAccess.ProjectContent.Unit.UpdatedUserInfoId'></a>

## Unit.UpdatedUserInfoId Property

更新者 (參考 UserInfo 表)。

```csharp
public string UpdatedUserInfoId { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')