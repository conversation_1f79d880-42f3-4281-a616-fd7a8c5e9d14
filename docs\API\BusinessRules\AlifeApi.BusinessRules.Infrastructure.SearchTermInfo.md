#### [AlifeApi.BusinessRules](index.md 'index')
### [AlifeApi.BusinessRules.Infrastructure](AlifeApi.BusinessRules.Infrastructure.md 'AlifeApi.BusinessRules.Infrastructure')

## SearchTermInfo Class

過濾資訊

```csharp
public class SearchTermInfo
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; SearchTermInfo
### Properties

<a name='AlifeApi.BusinessRules.Infrastructure.SearchTermInfo.SearchField'></a>

## SearchTermInfo.SearchField Property

過濾欄位

```csharp
public string SearchField { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.Infrastructure.SearchTermInfo.SearchValue'></a>

## SearchTermInfo.SearchValue Property

過濾值

```csharp
public object SearchValue { get; set; }
```

#### Property Value
[System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object')