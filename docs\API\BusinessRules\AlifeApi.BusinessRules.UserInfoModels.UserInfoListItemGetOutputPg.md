#### [AlifeApi.BusinessRules](index.md 'index')
### [AlifeApi.BusinessRules.UserInfoModels](AlifeApi.BusinessRules.UserInfoModels.md 'AlifeApi.BusinessRules.UserInfoModels')

## UserInfoListItemGetOutputPg Class

使用者列表項目輸出資料 (PostgreSQL版本)

```csharp
public class UserInfoListItemGetOutputPg
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; UserInfoListItemGetOutputPg
### Properties

<a name='AlifeApi.BusinessRules.UserInfoModels.UserInfoListItemGetOutputPg.BirthDate'></a>

## UserInfoListItemGetOutputPg.BirthDate Property

出生日期

```csharp
public System.Nullable<System.DateTime> BirthDate { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.DateTime](https://docs.microsoft.com/en-us/dotnet/api/System.DateTime 'System.DateTime')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

<a name='AlifeApi.BusinessRules.UserInfoModels.UserInfoListItemGetOutputPg.CompanyId'></a>

## UserInfoListItemGetOutputPg.CompanyId Property

公司ID

```csharp
public string CompanyId { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.UserInfoModels.UserInfoListItemGetOutputPg.DepartmentId'></a>

## UserInfoListItemGetOutputPg.DepartmentId Property

部門ID

```csharp
public string DepartmentId { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.UserInfoModels.UserInfoListItemGetOutputPg.DepartmentName'></a>

## UserInfoListItemGetOutputPg.DepartmentName Property

部門名稱

```csharp
public string DepartmentName { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.UserInfoModels.UserInfoListItemGetOutputPg.Email'></a>

## UserInfoListItemGetOutputPg.Email Property

電子郵件

```csharp
public string Email { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.UserInfoModels.UserInfoListItemGetOutputPg.EmergencyContactName'></a>

## UserInfoListItemGetOutputPg.EmergencyContactName Property

緊急聯絡人姓名

```csharp
public string EmergencyContactName { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.UserInfoModels.UserInfoListItemGetOutputPg.EmergencyContactPhone'></a>

## UserInfoListItemGetOutputPg.EmergencyContactPhone Property

緊急聯絡人電話

```csharp
public string EmergencyContactPhone { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.UserInfoModels.UserInfoListItemGetOutputPg.EmergencyContactRelation'></a>

## UserInfoListItemGetOutputPg.EmergencyContactRelation Property

與緊急聯絡人的關係

```csharp
public string EmergencyContactRelation { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.UserInfoModels.UserInfoListItemGetOutputPg.Gender'></a>

## UserInfoListItemGetOutputPg.Gender Property

性別

```csharp
public string Gender { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.UserInfoModels.UserInfoListItemGetOutputPg.HireDate'></a>

## UserInfoListItemGetOutputPg.HireDate Property

到職日期

```csharp
public System.Nullable<System.DateTime> HireDate { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.DateTime](https://docs.microsoft.com/en-us/dotnet/api/System.DateTime 'System.DateTime')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

<a name='AlifeApi.BusinessRules.UserInfoModels.UserInfoListItemGetOutputPg.Identity'></a>

## UserInfoListItemGetOutputPg.Identity Property

身分證字號

```csharp
public string Identity { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.UserInfoModels.UserInfoListItemGetOutputPg.IsAdmin'></a>

## UserInfoListItemGetOutputPg.IsAdmin Property

是否為管理員

```csharp
public bool IsAdmin { get; set; }
```

#### Property Value
[System.Boolean](https://docs.microsoft.com/en-us/dotnet/api/System.Boolean 'System.Boolean')

<a name='AlifeApi.BusinessRules.UserInfoModels.UserInfoListItemGetOutputPg.IsEmailNotificationEnabled'></a>

## UserInfoListItemGetOutputPg.IsEmailNotificationEnabled Property

是否需要發送電子郵件通知

```csharp
public System.Nullable<bool> IsEmailNotificationEnabled { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.Boolean](https://docs.microsoft.com/en-us/dotnet/api/System.Boolean 'System.Boolean')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

<a name='AlifeApi.BusinessRules.UserInfoModels.UserInfoListItemGetOutputPg.IsInside'></a>

## UserInfoListItemGetOutputPg.IsInside Property

是否為內場/外場人員

```csharp
public System.Nullable<bool> IsInside { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.Boolean](https://docs.microsoft.com/en-us/dotnet/api/System.Boolean 'System.Boolean')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

<a name='AlifeApi.BusinessRules.UserInfoModels.UserInfoListItemGetOutputPg.IsLock'></a>

## UserInfoListItemGetOutputPg.IsLock Property

是否鎖定

```csharp
public bool IsLock { get; set; }
```

#### Property Value
[System.Boolean](https://docs.microsoft.com/en-us/dotnet/api/System.Boolean 'System.Boolean')

<a name='AlifeApi.BusinessRules.UserInfoModels.UserInfoListItemGetOutputPg.IsM365'></a>

## UserInfoListItemGetOutputPg.IsM365 Property

是否為 M365 帳號

```csharp
public System.Nullable<bool> IsM365 { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.Boolean](https://docs.microsoft.com/en-us/dotnet/api/System.Boolean 'System.Boolean')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

<a name='AlifeApi.BusinessRules.UserInfoModels.UserInfoListItemGetOutputPg.JobTitleId'></a>

## UserInfoListItemGetOutputPg.JobTitleId Property

職稱ID

```csharp
public string JobTitleId { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.UserInfoModels.UserInfoListItemGetOutputPg.JobTitleName'></a>

## UserInfoListItemGetOutputPg.JobTitleName Property

職稱名稱

```csharp
public string JobTitleName { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.UserInfoModels.UserInfoListItemGetOutputPg.LastLoginIp'></a>

## UserInfoListItemGetOutputPg.LastLoginIp Property

最後登入IP

```csharp
public string LastLoginIp { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.UserInfoModels.UserInfoListItemGetOutputPg.LastLoginTime'></a>

## UserInfoListItemGetOutputPg.LastLoginTime Property

最後登入時間

```csharp
public System.Nullable<System.DateTime> LastLoginTime { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.DateTime](https://docs.microsoft.com/en-us/dotnet/api/System.DateTime 'System.DateTime')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

<a name='AlifeApi.BusinessRules.UserInfoModels.UserInfoListItemGetOutputPg.LastLogoutTime'></a>

## UserInfoListItemGetOutputPg.LastLogoutTime Property

最後登出時間

```csharp
public System.Nullable<System.DateTime> LastLogoutTime { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.DateTime](https://docs.microsoft.com/en-us/dotnet/api/System.DateTime 'System.DateTime')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

<a name='AlifeApi.BusinessRules.UserInfoModels.UserInfoListItemGetOutputPg.LoginFailedCount'></a>

## UserInfoListItemGetOutputPg.LoginFailedCount Property

登入失敗次數

```csharp
public int LoginFailedCount { get; set; }
```

#### Property Value
[System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')

<a name='AlifeApi.BusinessRules.UserInfoModels.UserInfoListItemGetOutputPg.MailingAddress'></a>

## UserInfoListItemGetOutputPg.MailingAddress Property

通訊地址

```csharp
public string MailingAddress { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.UserInfoModels.UserInfoListItemGetOutputPg.MobileNumber'></a>

## UserInfoListItemGetOutputPg.MobileNumber Property

手機

```csharp
public string MobileNumber { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.UserInfoModels.UserInfoListItemGetOutputPg.Name'></a>

## UserInfoListItemGetOutputPg.Name Property

姓名

```csharp
public string Name { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.UserInfoModels.UserInfoListItemGetOutputPg.RegisteredAddress'></a>

## UserInfoListItemGetOutputPg.RegisteredAddress Property

戶籍地址

```csharp
public string RegisteredAddress { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.UserInfoModels.UserInfoListItemGetOutputPg.RoleGroups'></a>

## UserInfoListItemGetOutputPg.RoleGroups Property

角色群組列表

```csharp
public System.Collections.Generic.List<AlifeApi.BusinessRules.UserInfoModels.RoleGroupOutput> RoleGroups { get; set; }
```

#### Property Value
[System.Collections.Generic.List&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Collections.Generic.List-1 'System.Collections.Generic.List`1')[RoleGroupOutput](AlifeApi.BusinessRules.UserInfoModels.RoleGroupOutput.md 'AlifeApi.BusinessRules.UserInfoModels.RoleGroupOutput')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Collections.Generic.List-1 'System.Collections.Generic.List`1')

<a name='AlifeApi.BusinessRules.UserInfoModels.UserInfoListItemGetOutputPg.ServiceUnit'></a>

## UserInfoListItemGetOutputPg.ServiceUnit Property

服務單位

```csharp
public string ServiceUnit { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.UserInfoModels.UserInfoListItemGetOutputPg.Status'></a>

## UserInfoListItemGetOutputPg.Status Property

帳號狀態

```csharp
public bool Status { get; set; }
```

#### Property Value
[System.Boolean](https://docs.microsoft.com/en-us/dotnet/api/System.Boolean 'System.Boolean')

<a name='AlifeApi.BusinessRules.UserInfoModels.UserInfoListItemGetOutputPg.TelephoneNumber'></a>

## UserInfoListItemGetOutputPg.TelephoneNumber Property

電話號碼

```csharp
public string TelephoneNumber { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.UserInfoModels.UserInfoListItemGetOutputPg.UserInfoId'></a>

## UserInfoListItemGetOutputPg.UserInfoId Property

使用者ID

```csharp
public string UserInfoId { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')