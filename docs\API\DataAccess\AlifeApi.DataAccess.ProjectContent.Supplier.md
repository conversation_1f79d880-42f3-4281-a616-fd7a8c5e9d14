#### [AlifeApi.DataAccess](index.md 'index')
### [AlifeApi.DataAccess.ProjectContent](AlifeApi.DataAccess.ProjectContent.md 'AlifeApi.DataAccess.ProjectContent')

## Supplier Class

供應商資料表，用於儲存供應商的基本資訊

```csharp
public class Supplier
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; Supplier
### Properties

<a name='AlifeApi.DataAccess.ProjectContent.Supplier.AccountName'></a>

## Supplier.AccountName Property

收款戶名

```csharp
public string AccountName { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContent.Supplier.Address'></a>

## Supplier.Address Property

通訊地址

```csharp
public string Address { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContent.Supplier.BankBranch'></a>

## Supplier.BankBranch Property

銀行分行名稱

```csharp
public string BankBranch { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContent.Supplier.BankName'></a>

## Supplier.BankName Property

銀行名稱

```csharp
public string BankName { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContent.Supplier.CompanyPhone'></a>

## Supplier.CompanyPhone Property

公司電話

```csharp
public string CompanyPhone { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContent.Supplier.ContactName'></a>

## Supplier.ContactName Property

聯絡窗口姓名

```csharp
public string ContactName { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContent.Supplier.ContactPerson'></a>

## Supplier.ContactPerson Property

負責人姓名

```csharp
public string ContactPerson { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContent.Supplier.ContactPhone1'></a>

## Supplier.ContactPhone1 Property

聯絡電話1

```csharp
public string ContactPhone1 { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContent.Supplier.ContactPhone2'></a>

## Supplier.ContactPhone2 Property

聯絡電話2

```csharp
public string ContactPhone2 { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContent.Supplier.CreatedTime'></a>

## Supplier.CreatedTime Property

資料創建時間

```csharp
public System.DateTime CreatedTime { get; set; }
```

#### Property Value
[System.DateTime](https://docs.microsoft.com/en-us/dotnet/api/System.DateTime 'System.DateTime')

<a name='AlifeApi.DataAccess.ProjectContent.Supplier.CreatedUserInfoId'></a>

## Supplier.CreatedUserInfoId Property

資料創建人識別碼（參考 UserInfo 表的 UserInfoId）

```csharp
public string CreatedUserInfoId { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContent.Supplier.Email'></a>

## Supplier.Email Property

電子郵件

```csharp
public string Email { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContent.Supplier.IdNumber'></a>

## Supplier.IdNumber Property

證號（身分證號或統一編號）

```csharp
public string IdNumber { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContent.Supplier.IdType'></a>

## Supplier.IdType Property

證號類型（例如：身分證、統一編號）

```csharp
public string IdType { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContent.Supplier.Name'></a>

## Supplier.Name Property

供應商名稱（個人或公司名稱）

```csharp
public string Name { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContent.Supplier.PersonType'></a>

## Supplier.PersonType Property

人別（個人或法人，例如：個人、公司）

```csharp
public string PersonType { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContent.Supplier.SupplierId'></a>

## Supplier.SupplierId Property

供應商唯一識別碼，自動遞增

```csharp
public int SupplierId { get; set; }
```

#### Property Value
[System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')

<a name='AlifeApi.DataAccess.ProjectContent.Supplier.UpdatedTime'></a>

## Supplier.UpdatedTime Property

資料更新時間

```csharp
public System.DateTime UpdatedTime { get; set; }
```

#### Property Value
[System.DateTime](https://docs.microsoft.com/en-us/dotnet/api/System.DateTime 'System.DateTime')

<a name='AlifeApi.DataAccess.ProjectContent.Supplier.UpdatedUserInfoId'></a>

## Supplier.UpdatedUserInfoId Property

資料更新人識別碼（參考 UserInfo 表的 UserInfoId）

```csharp
public string UpdatedUserInfoId { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')