# CRM選項管理 API 測試指南

本文檔提供CRM選項管理API的測試步驟和範例。

## 前置條件

1. 確保資料庫中有以下測試資料：
   - 至少一個案場 (Sites 表)
   - 至少一個CRM選項類型 (CrmOptionTypes 表)

## 測試步驟

### 1. 建立測試用的CRM選項類型

首先需要在資料庫中建立一個CRM選項類型：

```sql
INSERT INTO "CrmOptionTypes" ("TypeName", "Description", "CreatedUserInfoId", "UpdatedUserInfoId")
VALUES ('需求坪數', '客戶需求的房屋坪數範圍', 'test_user', 'test_user');
```

### 2. 測試新增CRM選項

**請求**:
```bash
POST /api/CrmOptions/CreateCrmOption
Content-Type: application/json

{
  "SiteCode": "A001",
  "CrmOptionTypeId": 1,
  "OptionValue": "20-30坪",
  "SortOrder": 1,
  "IsActive": true
}
```

**預期回應**:
```json
{
  "SiteCrmOptionId": 1
}
```

### 3. 測試查詢CRM選項列表

**請求**:
```bash
POST /api/CrmOptions/GetCrmOptions
Content-Type: application/json

{
  "SiteCode": "A001",
  "PageIndex": 1,
  "PageSize": 10
}
```

**預期回應**:
```json
{
  "TotalCount": 1,
  "PageIndex": 1,
  "PageSize": 10,
  "TotalPages": 1,
  "Details": [
    {
      "SiteCrmOptionId": 1,
      "SiteCode": "A001",
      "SiteName": "測試案場",
      "CrmOptionTypeId": 1,
      "CrmOptionTypeName": "需求坪數",
      "OptionValue": "20-30坪",
      "SortOrder": 1,
      "IsActive": true,
      "CreateTime": "2024-01-01T10:00:00"
    }
  ]
}
```

### 4. 測試取得CRM選項詳細資料

**請求**:
```bash
GET /api/CrmOptions/GetCrmOption/1
```

**預期回應**:
```json
{
  "SiteCrmOptionId": 1,
  "SiteCode": "A001",
  "SiteName": "測試案場",
  "CrmOptionTypeId": 1,
  "CrmOptionTypeName": "需求坪數",
  "OptionValue": "20-30坪",
  "SortOrder": 1,
  "IsActive": true,
  "CreateTime": "2024-01-01T10:00:00",
  "UpdateTime": "2024-01-01T10:00:00",
  "CreatedUserInfoId": "test_user",
  "CreatedUserName": "測試用戶",
  "UpdatedUserInfoId": "test_user",
  "UpdatedUserName": "測試用戶"
}
```

### 5. 測試更新CRM選項

**請求**:
```bash
PUT /api/CrmOptions/UpdateCrmOption/1
Content-Type: application/json

{
  "OptionValue": "25-35坪",
  "SortOrder": 2,
  "IsActive": true
}
```

**預期回應**:
```
HTTP 204 No Content
```

### 6. 測試取得CRM選項下拉選單

**請求**:
```bash
POST /api/CrmOptions/GetCrmOptionDropdown
Content-Type: application/json

{
  "SiteCode": "A001",
  "CrmOptionTypeId": 1,
  "OnlyActive": true
}
```

**預期回應**:
```json
[
  {
    "Name": "25-35坪",
    "Value": 1,
    "SortOrder": 2
  }
]
```

### 7. 測試刪除CRM選項

**請求**:
```bash
DELETE /api/CrmOptions/DeleteCrmOption/1
```

**預期回應**:
```
HTTP 204 No Content
```

## 錯誤測試案例

### 1. 測試重複選項值

建立兩個相同的選項值，應該會收到錯誤：

**請求**:
```bash
POST /api/CrmOptions/CreateCrmOption
Content-Type: application/json

{
  "SiteCode": "A001",
  "CrmOptionTypeId": 1,
  "OptionValue": "20-30坪",
  "SortOrder": 1,
  "IsActive": true
}
```

**預期錯誤回應**:
```json
{
  "message": "案場 'A001' 在此選項類型下已存在選項值 '20-30坪'。"
}
```

### 2. 測試不存在的案場代碼

**請求**:
```bash
POST /api/CrmOptions/CreateCrmOption
Content-Type: application/json

{
  "SiteCode": "INVALID",
  "CrmOptionTypeId": 1,
  "OptionValue": "20-30坪",
  "SortOrder": 1,
  "IsActive": true
}
```

**預期錯誤回應**:
```json
{
  "message": "指定的案場代碼 'INVALID' 不存在。"
}
```

### 3. 測試不存在的CRM選項類型ID

**請求**:
```bash
POST /api/CrmOptions/CreateCrmOption
Content-Type: application/json

{
  "SiteCode": "A001",
  "CrmOptionTypeId": 999,
  "OptionValue": "20-30坪",
  "SortOrder": 1,
  "IsActive": true
}
```

**預期錯誤回應**:
```json
{
  "message": "指定的CRM選項類型ID '999' 不存在。"
}
```

## 使用 Postman 測試

1. 匯入API端點到Postman
2. 設定環境變數：
   - `baseUrl`: API的基礎URL (例如: `https://localhost:5001`)
   - `token`: 認證Token (如果需要)

3. 按照上述測試步驟依序執行

## 使用 curl 測試

```bash
# 新增CRM選項
curl -X POST "https://localhost:5001/api/CrmOptions/CreateCrmOption" \
  -H "Content-Type: application/json" \
  -d '{
    "SiteCode": "A001",
    "CrmOptionTypeId": 1,
    "OptionValue": "20-30坪",
    "SortOrder": 1,
    "IsActive": true
  }'

# 查詢CRM選項列表
curl -X POST "https://localhost:5001/api/CrmOptions/GetCrmOptions" \
  -H "Content-Type: application/json" \
  -d '{
    "SiteCode": "A001",
    "PageIndex": 1,
    "PageSize": 10
  }'

# 取得下拉選單
curl -X POST "https://localhost:5001/api/CrmOptions/GetCrmOptionDropdown" \
  -H "Content-Type: application/json" \
  -d '{
    "SiteCode": "A001",
    "CrmOptionTypeId": 1,
    "OnlyActive": true
  }'
```

## 注意事項

1. 確保在測試前資料庫中有必要的基礎資料
2. 測試時請注意資料的一致性和完整性
3. 建議在測試環境中進行，避免影響生產資料
4. 測試完成後記得清理測試資料
