#### [AlifeApi.BusinessRules](index.md 'index')
### [AlifeApi.BusinessRules.UserRecordModels](AlifeApi.BusinessRules.UserRecordModels.md 'AlifeApi.BusinessRules.UserRecordModels')

## UserRecordService Class

使用者紀錄，稽核用

```csharp
public class UserRecordService : AlifeApi.BusinessRules.Infrastructure.ServiceBase<AlifeApi.DataAccess.ProjectContent.alifeContext>
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; [AlifeApi.Common.InfrastructureBase](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.Common.InfrastructureBase 'AlifeApi.Common.InfrastructureBase') &#129106; [AlifeApi.BusinessRules.Infrastructure.ServiceBase&lt;](AlifeApi.BusinessRules.Infrastructure.ServiceBase_TDbContext_.md 'AlifeApi.BusinessRules.Infrastructure.ServiceBase<TDbContext>')[AlifeApi.DataAccess.ProjectContent.alifeContext](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.DataAccess.ProjectContent.alifeContext 'AlifeApi.DataAccess.ProjectContent.alifeContext')[&gt;](AlifeApi.BusinessRules.Infrastructure.ServiceBase_TDbContext_.md 'AlifeApi.BusinessRules.Infrastructure.ServiceBase<TDbContext>') &#129106; UserRecordService
### Constructors

<a name='AlifeApi.BusinessRules.UserRecordModels.UserRecordService.UserRecordService(System.IServiceProvider,AlifeApi.DataAccess.ProjectContent.alifeContext)'></a>

## UserRecordService(IServiceProvider, alifeContext) Constructor

Initializes a new instance of the [UserRecordService](AlifeApi.BusinessRules.UserRecordModels.UserRecordService.md 'AlifeApi.BusinessRules.UserRecordModels.UserRecordService') class.

```csharp
public UserRecordService(System.IServiceProvider serviceProvider, AlifeApi.DataAccess.ProjectContent.alifeContext dbContext);
```
#### Parameters

<a name='AlifeApi.BusinessRules.UserRecordModels.UserRecordService.UserRecordService(System.IServiceProvider,AlifeApi.DataAccess.ProjectContent.alifeContext).serviceProvider'></a>

`serviceProvider` [System.IServiceProvider](https://docs.microsoft.com/en-us/dotnet/api/System.IServiceProvider 'System.IServiceProvider')

The service provider.

<a name='AlifeApi.BusinessRules.UserRecordModels.UserRecordService.UserRecordService(System.IServiceProvider,AlifeApi.DataAccess.ProjectContent.alifeContext).dbContext'></a>

`dbContext` [AlifeApi.DataAccess.ProjectContent.alifeContext](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.DataAccess.ProjectContent.alifeContext 'AlifeApi.DataAccess.ProjectContent.alifeContext')

The database context.