using AlifeApi.BusinessRules.Infrastructure;

namespace AlifeApi.BusinessRules.ParkingSpaceModels
{
    /// <summary>
    /// 停車位列表查詢輸入模型
    /// </summary>
    public class ParkingSpaceQueryInput : PagedListInput
    {
        public int? FloorId { get; set; }

        /// <summary>
        /// 建築物ID (篩選)
        /// </summary>
        public int? BuildingId { get; set; }

        /// <summary>
        /// 案場代碼 (篩選)
        /// </summary>
        public string? SiteCode { get; set; }

        /// <summary>
        /// 車位編號 (篩選)
        /// </summary>
        public string? SpaceNumber { get; set; }

        /// <summary>
        /// 車位類型 (篩選)
        /// </summary>
        public string? SpaceType { get; set; }

        /// <summary>
        /// 車位位置 (篩選)
        /// </summary>
        public string? Location { get; set; }
    }
} 
