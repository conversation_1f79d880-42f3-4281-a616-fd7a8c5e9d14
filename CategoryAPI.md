# 分類管理 API 文檔

本文檔詳細說明大分類、中分類、小分類的 API 使用方式，包括請求格式、回應格式和使用範例。

## API 路由說明

本系統使用 ASP.NET Core 的路由模式：`/api/[controller]/[action]`

- **基礎路徑**: `/api/`
- **控制器名稱**: `LargeCategories`、`MediumCategories`、`SmallCategories`
- **動作名稱**: 對應 Controller 中的方法名稱（如 `GetLargeCategories`、`CreateLargeCategory` 等）

因此完整的 API 端點格式為：`/api/{ControllerName}/{ActionName}`

## 目錄

- [大分類 API](#大分類-api)
- [中分類 API](#中分類-api)
- [小分類 API](#小分類-api)
- [錯誤處理](#錯誤處理)
- [使用範例](#使用範例)

---

## 大分類 API

### 1. 查詢大分類列表 (分頁)

**端點**: `POST /api/LargeCategories/GetLargeCategories`

**請求格式**:
```json
{
  "Name": "string (可選)",           // 大分類名稱 (模糊查詢)
  "IsActive": true/false/null,      // 是否啟用 (null表示不篩選)
  "PageIndex": 1,                   // 頁碼 (從1開始)
  "PageSize": 10                    // 每頁筆數
}
```

**回應格式**:
```json
{
  "TotalCount": 25,                 // 總筆數
  "PageIndex": 1,                   // 當前頁碼
  "PageSize": 10,                   // 每頁筆數
  "TotalPages": 3,                  // 總頁數
  "Details": [
    {
      "LargeCategoryId": 1,         // 大分類ID
      "Name": "電子產品",            // 大分類名稱
      "Description": "各種電子產品", // 描述
      "SortOrder": 1,               // 排序順序
      "IsActive": true,             // 是否啟用
      "CreateTime": "2024-01-01T10:00:00" // 建立時間
    }
  ]
}
```

### 2. 取得大分類詳細資料

**端點**: `GET /api/LargeCategories/GetLargeCategory/{largeCategoryId}`

**路徑參數**:
- `largeCategoryId`: 大分類ID (long)

**回應格式**:
```json
{
  "LargeCategoryId": 1,
  "Name": "電子產品",
  "Description": "各種電子產品",
  "SortOrder": 1,
  "IsActive": true,
  "CreateTime": "2024-01-01T10:00:00",
  "CreatedUserInfoId": "user001",
  "CreatedUserName": "張三",
  "UpdateTime": "2024-01-01T10:00:00",
  "UpdatedUserInfoId": "user001",
  "UpdatedUserName": "張三"
}
```

### 3. 新增大分類

**端點**: `POST /api/LargeCategories/CreateLargeCategory`

**請求格式**:
```json
{
  "Name": "電子產品",               // 必填，最大100字元
  "Description": "各種電子產品",     // 可選，最大500字元
  "SortOrder": 1,                  // 必填，非負整數
  "IsActive": true                 // 可選，預設true
}
```

**回應格式**:
```json
{
  "LargeCategoryId": 1             // 新建的大分類ID
}
```

### 4. 更新大分類

**端點**: `POST /api/LargeCategories/UpdateLargeCategory/{largeCategoryId}`

**路徑參數**:
- `largeCategoryId`: 大分類ID (long)

**請求格式**: 同新增大分類

**回應**: HTTP 200 OK (成功) 或錯誤訊息

### 5. 刪除大分類

**端點**: `DELETE /api/LargeCategories/DeleteLargeCategory/{largeCategoryId}`

**路徑參數**:
- `largeCategoryId`: 大分類ID (long)

**回應**: HTTP 200 OK (成功) 或錯誤訊息

**注意**: 如果大分類下有中分類，將無法刪除

### 6. 取得大分類下拉選單

**端點**: `GET /api/LargeCategories/GetLargeCategoryDropdown`

**說明**: 取得所有啟用的大分類，用於下拉選單顯示

**回應格式**:
```json
[
  {
    "LargeCategoryId": 1,         // 大分類ID
    "Name": "電子產品",            // 大分類名稱
    "SortOrder": 1                // 排序順序
  },
  {
    "LargeCategoryId": 2,
    "Name": "服飾配件",
    "SortOrder": 2
  }
]
```

---

## 中分類 API

### 1. 查詢中分類列表 (分頁)

**端點**: `POST /api/MediumCategories/GetMediumCategories`

**請求格式**:
```json
{
  "LargeCategoryId": 1,             // 可選，所屬大分類ID
  "Name": "string (可選)",           // 中分類名稱 (模糊查詢)
  "IsActive": true/false/null,      // 是否啟用 (null表示不篩選)
  "PageIndex": 1,                   // 頁碼 (從1開始)
  "PageSize": 10                    // 每頁筆數
}
```

**回應格式**:
```json
{
  "TotalCount": 15,
  "PageIndex": 1,
  "PageSize": 10,
  "TotalPages": 2,
  "Details": [
    {
      "MediumCategoryId": 1,        // 中分類ID
      "LargeCategoryId": 1,         // 所屬大分類ID
      "LargeCategoryName": "電子產品", // 所屬大分類名稱
      "Name": "手機",               // 中分類名稱
      "Description": "各種手機產品", // 描述
      "SortOrder": 1,               // 排序順序
      "IsActive": true,             // 是否啟用
      "CreateTime": "2024-01-01T10:00:00" // 建立時間
    }
  ]
}
```

### 2. 取得中分類詳細資料

**端點**: `GET /api/MediumCategories/GetMediumCategory/{mediumCategoryId}`

**路徑參數**:
- `mediumCategoryId`: 中分類ID (long)

**回應格式**:
```json
{
  "MediumCategoryId": 1,
  "LargeCategoryId": 1,
  "LargeCategoryName": "電子產品",
  "Name": "手機",
  "Description": "各種手機產品",
  "SortOrder": 1,
  "IsActive": true,
  "CreateTime": "2024-01-01T10:00:00",
  "CreatedUserInfoId": "user001",
  "CreatedUserName": "張三",
  "UpdateTime": "2024-01-01T10:00:00",
  "UpdatedUserInfoId": "user001",
  "UpdatedUserName": "張三"
}
```

### 3. 新增中分類

**端點**: `POST /api/MediumCategories/CreateMediumCategory`

**請求格式**:
```json
{
  "LargeCategoryId": 1,            // 必填，所屬大分類ID
  "Name": "手機",                  // 必填，最大100字元
  "Description": "各種手機產品",    // 可選，最大500字元
  "SortOrder": 1,                  // 必填，非負整數
  "IsActive": true                 // 可選，預設true
}
```

**回應格式**:
```json
{
  "MediumCategoryId": 1            // 新建的中分類ID
}
```

### 4. 更新中分類

**端點**: `POST /api/MediumCategories/UpdateMediumCategory/{mediumCategoryId}`

**路徑參數**:
- `mediumCategoryId`: 中分類ID (long)

**請求格式**: 同新增中分類

**回應**: HTTP 200 OK (成功) 或錯誤訊息

### 5. 刪除中分類

**端點**: `DELETE /api/MediumCategories/DeleteMediumCategory/{mediumCategoryId}`

**路徑參數**:
- `mediumCategoryId`: 中分類ID (long)

**回應**: HTTP 200 OK (成功) 或錯誤訊息

**注意**: 如果中分類下有小分類，將無法刪除

### 6. 取得中分類下拉選單

**端點**: `GET /api/MediumCategories/GetMediumCategoryDropdown`

**說明**: 取得所有啟用的中分類，用於下拉選單顯示。可選擇性篩選特定大分類下的中分類

**查詢參數**:
- `largeCategoryId` (可選): 大分類ID，用於篩選特定大分類下的中分類

**使用範例**:
- 取得所有中分類: `GET /api/MediumCategories/GetMediumCategoryDropdown`
- 取得特定大分類下的中分類: `GET /api/MediumCategories/GetMediumCategoryDropdown?largeCategoryId=1`

**回應格式**:
```json
[
  {
    "MediumCategoryId": 1,        // 中分類ID
    "LargeCategoryId": 1,         // 所屬大分類ID
    "LargeCategoryName": "電子產品", // 所屬大分類名稱
    "Name": "手機",               // 中分類名稱
    "SortOrder": 1                // 排序順序
  },
  {
    "MediumCategoryId": 2,
    "LargeCategoryId": 1,
    "LargeCategoryName": "電子產品",
    "Name": "電腦",
    "SortOrder": 2
  }
]
```

---

## 小分類 API

### 1. 查詢小分類列表 (分頁)

**端點**: `POST /api/SmallCategories/GetSmallCategories`

**請求格式**:
```json
{
  "LargeCategoryId": 1,             // 可選，所屬大分類ID
  "MediumCategoryId": 1,            // 可選，所屬中分類ID
  "Name": "string (可選)",           // 小分類名稱 (模糊查詢)
  "IsActive": true/false/null,      // 是否啟用 (null表示不篩選)
  "PageIndex": 1,                   // 頁碼 (從1開始)
  "PageSize": 10                    // 每頁筆數
}
```

**回應格式**:
```json
{
  "TotalCount": 8,
  "PageIndex": 1,
  "PageSize": 10,
  "TotalPages": 1,
  "Details": [
    {
      "SmallCategoryId": 1,         // 小分類ID
      "MediumCategoryId": 1,        // 所屬中分類ID
      "MediumCategoryName": "手機",  // 所屬中分類名稱
      "LargeCategoryId": 1,         // 所屬大分類ID
      "LargeCategoryName": "電子產品", // 所屬大分類名稱
      "Name": "智慧型手機",          // 小分類名稱
      "Description": "各種智慧型手機", // 描述
      "SortOrder": 1,               // 排序順序
      "IsActive": true,             // 是否啟用
      "CreateTime": "2024-01-01T10:00:00" // 建立時間
    }
  ]
}
```

### 2. 取得小分類詳細資料

**端點**: `GET /api/SmallCategories/GetSmallCategory/{smallCategoryId}`

**路徑參數**:
- `smallCategoryId`: 小分類ID (long)

**回應格式**:
```json
{
  "SmallCategoryId": 1,
  "MediumCategoryId": 1,
  "MediumCategoryName": "手機",
  "LargeCategoryId": 1,
  "LargeCategoryName": "電子產品",
  "Name": "智慧型手機",
  "Description": "各種智慧型手機",
  "SortOrder": 1,
  "IsActive": true,
  "CreateTime": "2024-01-01T10:00:00",
  "CreatedUserInfoId": "user001",
  "CreatedUserName": "張三",
  "UpdateTime": "2024-01-01T10:00:00",
  "UpdatedUserInfoId": "user001",
  "UpdatedUserName": "張三"
}
```

### 3. 新增小分類

**端點**: `POST /api/SmallCategories/CreateSmallCategory`

**請求格式**:
```json
{
  "MediumCategoryId": 1,           // 必填，所屬中分類ID
  "Name": "智慧型手機",             // 必填，最大100字元
  "Description": "各種智慧型手機",   // 可選，最大500字元
  "SortOrder": 1,                  // 必填，非負整數
  "IsActive": true                 // 可選，預設true
}
```

**回應格式**:
```json
{
  "SmallCategoryId": 1             // 新建的小分類ID
}
```

### 4. 更新小分類

**端點**: `POST /api/SmallCategories/UpdateSmallCategory/{smallCategoryId}`

**路徑參數**:
- `smallCategoryId`: 小分類ID (long)

**請求格式**: 同新增小分類

**回應**: HTTP 200 OK (成功) 或錯誤訊息

### 5. 刪除小分類

**端點**: `DELETE /api/SmallCategories/DeleteSmallCategory/{smallCategoryId}`

**路徑參數**:
- `smallCategoryId`: 小分類ID (long)

**回應**: HTTP 200 OK (成功) 或錯誤訊息

### 6. 取得小分類下拉選單

**端點**: `GET /api/SmallCategories/GetSmallCategoryDropdown`

**說明**: 取得所有啟用的小分類，用於下拉選單顯示。可選擇性篩選特定大分類或中分類下的小分類

**查詢參數**:
- `largeCategoryId` (可選): 大分類ID，用於篩選特定大分類下的小分類
- `mediumCategoryId` (可選): 中分類ID，用於篩選特定中分類下的小分類

**使用範例**:
- 取得所有小分類: `GET /api/SmallCategories/GetSmallCategoryDropdown`
- 取得特定大分類下的小分類: `GET /api/SmallCategories/GetSmallCategoryDropdown?largeCategoryId=1`
- 取得特定中分類下的小分類: `GET /api/SmallCategories/GetSmallCategoryDropdown?mediumCategoryId=1`
- 組合篩選: `GET /api/SmallCategories/GetSmallCategoryDropdown?largeCategoryId=1&mediumCategoryId=1`

**回應格式**:
```json
[
  {
    "SmallCategoryId": 1,         // 小分類ID
    "MediumCategoryId": 1,        // 所屬中分類ID
    "MediumCategoryName": "手機",  // 所屬中分類名稱
    "LargeCategoryId": 1,         // 所屬大分類ID
    "LargeCategoryName": "電子產品", // 所屬大分類名稱
    "Name": "智慧型手機",          // 小分類名稱
    "SortOrder": 1                // 排序順序
  },
  {
    "SmallCategoryId": 2,
    "MediumCategoryId": 1,
    "MediumCategoryName": "手機",
    "LargeCategoryId": 1,
    "LargeCategoryName": "電子產品",
    "Name": "功能型手機",
    "SortOrder": 2
  }
]
```

---

## 錯誤處理

### 常見錯誤回應格式

**400 Bad Request** - 請求參數錯誤或業務邏輯錯誤:
```json
{
  "message": "大分類名稱 '電子產品' 已存在。"
}
```

**404 Not Found** - 資源不存在:
```json
{
  "message": "找不到指定的大分類 (ID: 999)"
}
```

**500 Internal Server Error** - 伺服器內部錯誤:
```json
{
  "message": "更新分類時發生內部錯誤"
}
```

### 驗證錯誤

當輸入資料不符合驗證規則時，會返回 400 錯誤，常見的驗證錯誤包括：

- 必填欄位為空
- 字串長度超過限制
- 數值超出範圍
- 名稱重複
- 父分類不存在

---

## 使用範例

### 範例 1: 建立完整的三級分類

```javascript
// 1. 建立大分類
const largeCategory = await fetch('/api/LargeCategories/CreateLargeCategory', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    Name: '電子產品',
    Description: '各種電子產品',
    SortOrder: 1,
    IsActive: true
  })
});
const largeCategoryResult = await largeCategory.json();
// 回應: { "LargeCategoryId": 1 }

// 2. 建立中分類
const mediumCategory = await fetch('/api/MediumCategories/CreateMediumCategory', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    LargeCategoryId: largeCategoryResult.LargeCategoryId,
    Name: '手機',
    Description: '各種手機產品',
    SortOrder: 1,
    IsActive: true
  })
});
const mediumCategoryResult = await mediumCategory.json();
// 回應: { "MediumCategoryId": 1 }

// 3. 建立小分類
const smallCategory = await fetch('/api/SmallCategories/CreateSmallCategory', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    MediumCategoryId: mediumCategoryResult.MediumCategoryId,
    Name: '智慧型手機',
    Description: '各種智慧型手機',
    SortOrder: 1,
    IsActive: true
  })
});
const smallCategoryResult = await smallCategory.json();
// 回應: { "SmallCategoryId": 1 }
```

### 範例 2: 查詢特定大分類下的所有中分類

```javascript
const response = await fetch('/api/MediumCategories/GetMediumCategories', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    LargeCategoryId: 1,    // 篩選特定大分類
    IsActive: true,        // 只查詢啟用的分類
    PageIndex: 1,
    PageSize: 20
  })
});
const result = await response.json();
```

### 範例 3: 跨層級查詢 - 查詢特定大分類下的所有小分類

```javascript
const response = await fetch('/api/SmallCategories/GetSmallCategories', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    LargeCategoryId: 1,    // 直接指定大分類ID
    PageIndex: 1,
    PageSize: 50
  })
});
const result = await response.json();
// 會返回該大分類下所有中分類的所有小分類
```

### 範例 4: 模糊搜尋分類

```javascript
const response = await fetch('/api/LargeCategories/GetLargeCategories', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    Name: '電子',          // 模糊搜尋包含"電子"的分類
    PageIndex: 1,
    PageSize: 10
  })
});
const result = await response.json();
```

### 範例 5: 更新分類資訊

```javascript
const response = await fetch('/api/LargeCategories/UpdateLargeCategory/1', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    Name: '電子產品 (更新)',
    Description: '更新後的描述',
    SortOrder: 2,
    IsActive: false
  })
});
// 回應: HTTP 200 OK (成功)
```

### 範例 6: 刪除分類

```javascript
const response = await fetch('/api/SmallCategories/DeleteSmallCategory/1', {
  method: 'DELETE'
});
// 回應: HTTP 200 OK (成功) 或錯誤訊息
```

### 範例 7: 使用下拉選單 API - 前端表單應用

```javascript
// 前端新增中分類表單的應用場景

// 1. 頁面載入時，取得大分類下拉選單
const loadLargeCategories = async () => {
  const response = await fetch('/api/LargeCategories/GetLargeCategoryDropdown');
  const largeCategories = await response.json();
  
  // 填充大分類下拉選單
  const largeCategorySelect = document.getElementById('largeCategorySelect');
  largeCategories.forEach(category => {
    const option = document.createElement('option');
    option.value = category.LargeCategoryId;
    option.textContent = category.Name;
    largeCategorySelect.appendChild(option);
  });
};

// 2. 當用戶選擇大分類時，動態載入該大分類下的中分類
const onLargeCategoryChange = async (largeCategoryId) => {
  const response = await fetch(`/api/MediumCategories/GetMediumCategoryDropdown?largeCategoryId=${largeCategoryId}`);
  const mediumCategories = await response.json();
  
  // 清空並重新填充中分類下拉選單
  const mediumCategorySelect = document.getElementById('mediumCategorySelect');
  mediumCategorySelect.innerHTML = '<option value="">請選擇中分類</option>';
  
  mediumCategories.forEach(category => {
    const option = document.createElement('option');
    option.value = category.MediumCategoryId;
    option.textContent = category.Name;
    mediumCategorySelect.appendChild(option);
  });
};

// 3. 當用戶選擇中分類時，動態載入該中分類下的小分類
const onMediumCategoryChange = async (mediumCategoryId) => {
  const response = await fetch(`/api/SmallCategories/GetSmallCategoryDropdown?mediumCategoryId=${mediumCategoryId}`);
  const smallCategories = await response.json();
  
  // 清空並重新填充小分類下拉選單
  const smallCategorySelect = document.getElementById('smallCategorySelect');
  smallCategorySelect.innerHTML = '<option value="">請選擇小分類</option>';
  
  smallCategories.forEach(category => {
    const option = document.createElement('option');
    option.value = category.SmallCategoryId;
    option.textContent = category.Name;
    smallCategorySelect.appendChild(option);
  });
};
```

### 範例 8: 階層式分類選擇器 - React 組件範例

```javascript
// React 組件中的應用
import React, { useState, useEffect } from 'react';

const CategorySelector = ({ onCategoryChange }) => {
  const [largeCategories, setLargeCategories] = useState([]);
  const [mediumCategories, setMediumCategories] = useState([]);
  const [smallCategories, setSmallCategories] = useState([]);
  const [selectedLarge, setSelectedLarge] = useState('');
  const [selectedMedium, setSelectedMedium] = useState('');
  const [selectedSmall, setSelectedSmall] = useState('');

  // 載入大分類
  useEffect(() => {
    const fetchLargeCategories = async () => {
      const response = await fetch('/api/LargeCategories/GetLargeCategoryDropdown');
      const data = await response.json();
      setLargeCategories(data);
    };
    fetchLargeCategories();
  }, []);

  // 當大分類改變時載入中分類
  useEffect(() => {
    if (selectedLarge) {
      const fetchMediumCategories = async () => {
        const response = await fetch(`/api/MediumCategories/GetMediumCategoryDropdown?largeCategoryId=${selectedLarge}`);
        const data = await response.json();
        setMediumCategories(data);
        setSmallCategories([]); // 清空小分類
        setSelectedMedium('');
        setSelectedSmall('');
      };
      fetchMediumCategories();
    } else {
      setMediumCategories([]);
      setSmallCategories([]);
    }
  }, [selectedLarge]);

  // 當中分類改變時載入小分類
  useEffect(() => {
    if (selectedMedium) {
      const fetchSmallCategories = async () => {
        const response = await fetch(`/api/SmallCategories/GetSmallCategoryDropdown?mediumCategoryId=${selectedMedium}`);
        const data = await response.json();
        setSmallCategories(data);
        setSelectedSmall('');
      };
      fetchSmallCategories();
    } else {
      setSmallCategories([]);
    }
  }, [selectedMedium]);

  // 通知父組件選擇的分類
  useEffect(() => {
    onCategoryChange({
      largeCategoryId: selectedLarge,
      mediumCategoryId: selectedMedium,
      smallCategoryId: selectedSmall
    });
  }, [selectedLarge, selectedMedium, selectedSmall]);

  return (
    <div className="category-selector">
      <div className="form-group">
        <label>大分類:</label>
        <select value={selectedLarge} onChange={(e) => setSelectedLarge(e.target.value)}>
          <option value="">請選擇大分類</option>
          {largeCategories.map(category => (
            <option key={category.LargeCategoryId} value={category.LargeCategoryId}>
              {category.Name}
            </option>
          ))}
        </select>
      </div>

      <div className="form-group">
        <label>中分類:</label>
        <select value={selectedMedium} onChange={(e) => setSelectedMedium(e.target.value)} disabled={!selectedLarge}>
          <option value="">請選擇中分類</option>
          {mediumCategories.map(category => (
            <option key={category.MediumCategoryId} value={category.MediumCategoryId}>
              {category.Name}
            </option>
          ))}
        </select>
      </div>

      <div className="form-group">
        <label>小分類:</label>
        <select value={selectedSmall} onChange={(e) => setSelectedSmall(e.target.value)} disabled={!selectedMedium}>
          <option value="">請選擇小分類</option>
          {smallCategories.map(category => (
            <option key={category.SmallCategoryId} value={category.SmallCategoryId}>
              {category.Name}
            </option>
          ))}
        </select>
      </div>
    </div>
  );
};

export default CategorySelector;
```

---

## 注意事項

1. **階層關聯**: 刪除分類時會檢查是否有子分類，有子分類的情況下無法刪除
2. **名稱唯一性**: 在同一父分類下，分類名稱必須唯一
3. **排序**: 查詢結果會按照 大分類排序 → 中分類排序 → 小分類排序 → 建立時間 的順序排列
4. **分頁**: 所有列表查詢都支援分頁，建議設定適當的 PageSize 以獲得最佳效能
5. **權限**: 所有 API 都需要通過身份驗證，操作會記錄操作者資訊
6. **下拉選單**: 下拉選單 API 只返回啟用 (IsActive=true) 的分類，確保前端顯示的都是有效選項
7. **階層式選擇**: 建議前端實作階層式選擇器，當上層分類改變時自動更新下層選項
8. **效能考量**: 下拉選單 API 已針對效能優化，只返回必要欄位，適合頻繁調用 