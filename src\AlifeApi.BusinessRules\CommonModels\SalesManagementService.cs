using AlifeApi.BusinessRules.Infrastructure;
using AlifeApi.BusinessRules.ParkingSpaceModels;
using AlifeApi.BusinessRules.UnitModels;
using AlifeApi.DataAccess.ProjectContent;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace AlifeApi.BusinessRules.CommonModels
{
    /// <summary>
    /// 銷售管理統一服務
    /// </summary>
    public class SalesManagementService : ServiceBase<alifeContext>
    {
        private readonly UnitService _unitService;
        private readonly ParkingSpaceService _parkingSpaceService;

        public SalesManagementService(
            IServiceProvider serviceProvider,
            alifeContext dbContext,
            UnitService unitService,
            ParkingSpaceService parkingSpaceService)
            : base(serviceProvider, dbContext)
        {
            _unitService = unitService ?? throw new ArgumentNullException(nameof(unitService));
            _parkingSpaceService = parkingSpaceService ?? throw new ArgumentNullException(nameof(parkingSpaceService));
        }

        /// <summary>
        /// 獲取綜合銷售統計
        /// </summary>
        /// <param name="siteCode">案場編號</param>
        /// <returns>綜合銷售統計</returns>
        public async Task<ComprehensiveSalesStatistics> GetSalesStatisticsAsync(string siteCode)
        {
            ArgumentNullException.ThrowIfNull(siteCode);

            // 房屋統計
            var unitStats = await Db.Units
                .Where(u => u.SiteCode == siteCode)
                .GroupBy(u => u.Status)
                .Select(g => new { Status = g.Key, Count = g.Count() })
                .ToListAsync();

            var unitStatistics = new UnitSalesStatistics
            {
                SiteCode = siteCode,
                TotalCount = unitStats.Sum(s => s.Count),
                AvailableCount = unitStats.FirstOrDefault(s => s.Status == "可售")?.Count ?? 0,
                ReservedCount = unitStats.Where(s => s.Status == "保留" || s.Status == "業主保留" || s.Status == "地主保留").Sum(s => s.Count),
                SoldCount = unitStats.FirstOrDefault(s => s.Status == "已售")?.Count ?? 0,
            };

            // 車位統計
            var parkingSpaceStats = await Db.ParkingSpaces
                .Where(ps => ps.SiteCode == siteCode)
                .GroupBy(ps => ps.Status)
                .Select(g => new { Status = g.Key, Count = g.Count() })
                .ToListAsync();

            var parkingSpaceStatistics = new ParkingSpaceSalesStatistics
            {
                SiteCode = siteCode,
                TotalCount = parkingSpaceStats.Sum(s => s.Count),
                AvailableCount = parkingSpaceStats.FirstOrDefault(s => s.Status == "可售")?.Count ?? 0,
                ReservedCount = parkingSpaceStats.Where(s => s.Status == "保留" || s.Status == "業主保留" || s.Status == "地主保留").Sum(s => s.Count),
                SoldCount = parkingSpaceStats.FirstOrDefault(s => s.Status == "已售")?.Count ?? 0,
            };

            return new ComprehensiveSalesStatistics
            {
                SiteCode = siteCode,
                UnitStatistics = unitStatistics,
                ParkingSpaceStatistics = parkingSpaceStatistics,
                GeneratedTime = DateTime.Now
            };
        }

        /// <summary>
        /// 處理訂單建立時的狀態變更
        /// </summary>
        /// <param name="orderId">訂單ID</param>
        /// <param name="unitId">房屋單位ID (可選)</param>
        /// <param name="parkingSpaceIdsString">停車位ID字串 (可選)</param>
        public async Task HandleOrderCreatedAsync(int orderId, int? unitId, string? parkingSpaceIdsString)
        {
            using var transaction = await Db.Database.BeginTransactionAsync();
            try
            {
                // 變更房屋單位狀態為保留
                if (unitId.HasValue)
                {
                    await ChangeUnitStatusAsync(unitId.Value, "保留", orderId);
                }

                // 變更停車位狀態為保留 - 現在透過Units表管理
                if (!string.IsNullOrEmpty(parkingSpaceIdsString))
                {
                    var parkingSpaceIds = ParkingSpaceService.ParseParkingSpaceIds(parkingSpaceIdsString);
                    if (parkingSpaceIds.Any())
                    {
                        await BatchChangeParkingSpaceStatusAsync(parkingSpaceIds, "保留", orderId);
                    }
                }

                await transaction.CommitAsync();
            }
            catch
            {
                await transaction.RollbackAsync();
                throw;
            }
        }

        /// <summary>
        /// 處理訂單取消時的狀態回滾
        /// </summary>
        /// <param name="orderId">訂單ID</param>
        /// <param name="unitId">房屋單位ID (可選)</param>
        /// <param name="parkingSpaceIdsString">停車位ID字串 (可選)</param>
        public async Task HandleOrderCancelledAsync(int orderId, int? unitId, string? parkingSpaceIdsString)
        {
            using var transaction = await Db.Database.BeginTransactionAsync();
            try
            {
                // 變更房屋單位狀態回可售
                if (unitId.HasValue)
                {
                    await ChangeUnitStatusAsync(unitId.Value, "可售", orderId);
                }

                // 變更停車位狀態回可售 - 現在透過Units表管理
                if (!string.IsNullOrEmpty(parkingSpaceIdsString))
                {
                    var parkingSpaceIds = ParkingSpaceService.ParseParkingSpaceIds(parkingSpaceIdsString);
                    if (parkingSpaceIds.Any())
                    {
                        await BatchChangeParkingSpaceStatusAsync(parkingSpaceIds, "可售", orderId);
                    }
                }

                await transaction.CommitAsync();
            }
            catch
            {
                await transaction.RollbackAsync();
                throw;
            }
        }

        /// <summary>
        /// 處理訂單完成簽約時的狀態變更
        /// </summary>
        /// <param name="orderId">訂單ID</param>
        /// <param name="unitId">房屋單位ID (可選)</param>
        /// <param name="parkingSpaceIdsString">停車位ID字串 (可選)</param>
        public async Task HandleOrderCompletedAsync(int orderId, int? unitId, string? parkingSpaceIdsString)
        {
            using var transaction = await Db.Database.BeginTransactionAsync();
            try
            {
                // 變更房屋單位狀態為已售
                if (unitId.HasValue)
                {
                    await ChangeUnitStatusAsync(unitId.Value, "已售", orderId);
                }

                // 變更停車位狀態為已售 - 現在透過Units表管理
                if (!string.IsNullOrEmpty(parkingSpaceIdsString))
                {
                    var parkingSpaceIds = ParkingSpaceService.ParseParkingSpaceIds(parkingSpaceIdsString);
                    if (parkingSpaceIds.Any())
                    {
                        await BatchChangeParkingSpaceStatusAsync(parkingSpaceIds, "已售", orderId);
                    }
                }

                await transaction.CommitAsync();
            }
            catch
            {
                await transaction.RollbackAsync();
                throw;
            }
        }

        /// <summary>
        /// 批次設定房屋為保留狀態
        /// </summary>
        /// <param name="unitIds">房屋單位ID列表</param>
        /// <param name="reason">保留原因</param>
        public async Task BatchReserveUnitsAsync(List<int> unitIds, string? reason = null)
        {
            ArgumentNullException.ThrowIfNull(unitIds);

            await BatchChangeUnitStatusAsync(unitIds, "保留");
        }

        /// <summary>
        /// 批次設定停車位為保留狀態
        /// </summary>
        /// <param name="parkingSpaceIds">停車位ID列表</param>
        /// <param name="reason">保留原因</param>
        public async Task BatchReserveParkingSpacesAsync(List<int> parkingSpaceIds, string? reason = null)
        {
            ArgumentNullException.ThrowIfNull(parkingSpaceIds);

            await BatchChangeParkingSpaceStatusAsync(parkingSpaceIds, "保留");
        }

        /// <summary>
        /// 批次設定停車位為地主保留狀態
        /// </summary>
        /// <param name="parkingSpaceIds">停車位ID列表</param>
        /// <param name="reason">保留原因</param>
        public async Task BatchOwnerReserveParkingSpacesAsync(List<int> parkingSpaceIds, string? reason = null)
        {
            ArgumentNullException.ThrowIfNull(parkingSpaceIds);

            await BatchChangeParkingSpaceStatusAsync(parkingSpaceIds, "地主保留");
        }

        /// <summary>
        /// 檢查房屋和車位的可售狀態
        /// </summary>
        /// <param name="unitId">房屋單位ID (可選)</param>
        /// <param name="parkingSpaceIds">停車位ID列表 (可選)</param>
        /// <returns>驗證結果</returns>
        public async Task<SalesValidationResult> ValidateAvailabilityAsync(int? unitId, List<int>? parkingSpaceIds)
        {
            var result = new SalesValidationResult { IsValid = true, Messages = new List<string>() };

            // 檢查房屋狀態
            if (unitId.HasValue)
            {
                var unit = await Db.Units.FindAsync(unitId.Value);
                if (unit == null)
                {
                    result.IsValid = false;
                    result.Messages.Add($"房屋單位 ID {unitId.Value} 不存在");
                }
                else if (unit.Status != "可售")
                {
                    result.IsValid = false;
                    result.Messages.Add($"房屋單位 {unit.UnitNumber} 目前狀態為 '{unit.Status}'，無法銷售");
                }
            }

            // 檢查停車位狀態
            if (parkingSpaceIds != null && parkingSpaceIds.Any())
            {
                var parkingSpaceStatuses = await Db.ParkingSpaces
                                                .Where(ps => parkingSpaceIds.Contains(ps.ParkingSpaceId))
                                                .Select(ps => new { ps.ParkingSpaceId, ps.SpaceNumber, ps.Status })
                                                .ToListAsync();

                // 檢查每個車位的狀態
                foreach (var parkingSpaceId in parkingSpaceIds)
                {
                    var parkingSpaceInfo = parkingSpaceStatuses.FirstOrDefault(p => p.ParkingSpaceId == parkingSpaceId);
                    if (parkingSpaceInfo == null)
                    {
                        result.IsValid = false;
                        result.Messages.Add($"停車位 ID {parkingSpaceId} 不存在");
                        continue;
                    }

                    if (parkingSpaceInfo.Status != "可售")
                    {
                        result.IsValid = false;
                        result.Messages.Add($"停車位 {parkingSpaceInfo.SpaceNumber} 目前狀態為 '{parkingSpaceInfo.Status}'，無法銷售");
                    }
                }
            }

            return result;
        }

        /// <summary>
        /// 直接變更車位銷售狀態
        /// </summary>
        /// <param name="parkingSpaceIds">停車位ID列表</param>
        /// <param name="status">新狀態</param>
        /// <param name="orderId">訂單ID (可選)</param>
        private async Task BatchChangeParkingSpaceStatusAsync(List<int> parkingSpaceIds, string status, int? orderId = null)
        {
            var parkingSpaces = await Db.ParkingSpaces
                .Where(ps => parkingSpaceIds.Contains(ps.ParkingSpaceId))
                .ToListAsync();

            // 更新狀態
            foreach (var ps in parkingSpaces)
            {
                ps.Status = status;
                ps.UpdatedTime = DateTime.Now;
                ps.UpdatedUserInfoId = CurrentUser?.UserId;
            }

            await Db.SaveChangesAsync();
        }

        private async Task ChangeUnitStatusAsync(int unitId, string newStatus, int? orderId = null)
        {
            var unit = await Db.Units.FindAsync(unitId);
            if (unit == null)
            {
                throw new Exception($"找不到ID為 {unitId} 的房屋單位。");
            }
            unit.Status = newStatus;
            unit.UpdatedTime = DateTime.Now;
            unit.UpdatedUserInfoId = CurrentUser.UserId;

            await Db.SaveChangesAsync();
        }

        protected async Task BatchChangeUnitStatusAsync(List<int> unitIds, string newStatus)
        {
            var units = await Db.Units.Where(u => unitIds.Contains(u.UnitId)).ToListAsync();
            
            foreach (var unit in units)
            {
                unit.Status = newStatus;
                unit.UpdatedTime = DateTime.Now;
                unit.UpdatedUserInfoId = CurrentUser.UserId;
            }

            await Db.SaveChangesAsync();
        }
    }

    /// <summary>
    /// 綜合銷售統計
    /// </summary>
    public class ComprehensiveSalesStatistics
    {
        /// <summary>
        /// 案場編號
        /// </summary>
        public string SiteCode { get; set; } = null!;

        /// <summary>
        /// 房屋銷售統計
        /// </summary>
        public UnitSalesStatistics UnitStatistics { get; set; } = null!;

        /// <summary>
        /// 停車位銷售統計
        /// </summary>
        public ParkingSpaceSalesStatistics ParkingSpaceStatistics { get; set; } = null!;

        /// <summary>
        /// 統計產生時間
        /// </summary>
        public DateTime GeneratedTime { get; set; }
    }

    /// <summary>
    /// 銷售驗證結果
    /// </summary>
    public class SalesValidationResult
    {
        /// <summary>
        /// 是否通過驗證
        /// </summary>
        public bool IsValid { get; set; }

        /// <summary>
        /// 驗證訊息
        /// </summary>
        public List<string> Messages { get; set; } = new();
    }
} 
