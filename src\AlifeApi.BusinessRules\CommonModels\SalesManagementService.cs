using AlifeApi.BusinessRules.Infrastructure;
using AlifeApi.BusinessRules.ParkingSpaceModels;
using AlifeApi.BusinessRules.UnitModels;
using AlifeApi.DataAccess.ProjectContent;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace AlifeApi.BusinessRules.CommonModels
{
    /// <summary>
    /// 銷售管理統一服務
    /// </summary>
    public class SalesManagementService : ServiceBase<alifeContext>
    {
        private readonly UnitService _unitService;
        private readonly ParkingSpaceService _parkingSpaceService;

        public SalesManagementService(
            IServiceProvider serviceProvider,
            alifeContext dbContext,
            UnitService unitService,
            ParkingSpaceService parkingSpaceService)
            : base(serviceProvider, dbContext)
        {
            _unitService = unitService ?? throw new ArgumentNullException(nameof(unitService));
            _parkingSpaceService = parkingSpaceService ?? throw new ArgumentNullException(nameof(parkingSpaceService));
        }

        /// <summary>
        /// 獲取綜合銷售統計
        /// </summary>
        /// <param name="siteCode">案場編號</param>
        /// <returns>綜合銷售統計</returns>
        public async Task<ComprehensiveSalesStatistics> GetSalesStatisticsAsync(string siteCode)
        {
            ArgumentNullException.ThrowIfNull(siteCode);

            var unitStats = await _unitService.GetUnitSalesStatisticsAsync(siteCode);
            
            // 車位統計現在需要從Units表中查詢UnitType="車位"的記錄
            var parkingStats = await GetParkingSpaceStatisticsFromUnitsAsync(siteCode);

            return new ComprehensiveSalesStatistics
            {
                SiteCode = siteCode,
                UnitStatistics = unitStats,
                ParkingSpaceStatistics = parkingStats,
                GeneratedTime = DateTime.Now
            };
        }

        /// <summary>
        /// 處理訂單建立時的狀態變更
        /// </summary>
        /// <param name="orderId">訂單ID</param>
        /// <param name="unitId">房屋單位ID (可選)</param>
        /// <param name="parkingSpaceIdsString">停車位ID字串 (可選)</param>
        public async Task HandleOrderCreatedAsync(int orderId, int? unitId, string? parkingSpaceIdsString)
        {
            using var transaction = await Db.Database.BeginTransactionAsync();
            try
            {
                // 變更房屋單位狀態為已預訂
                if (unitId.HasValue)
                {
                    await _unitService.ChangeUnitStatusAsync(unitId.Value, "已預訂", orderId);
                }

                // 變更停車位狀態為已預訂 - 現在透過Units表管理
                if (!string.IsNullOrEmpty(parkingSpaceIdsString))
                {
                    var parkingSpaceIds = ParkingSpaceService.ParseParkingSpaceIds(parkingSpaceIdsString);
                    if (parkingSpaceIds.Any())
                    {
                        await BatchChangeParkingSpaceStatusViaUnitsAsync(parkingSpaceIds, "已預訂", orderId);
                    }
                }

                await transaction.CommitAsync();
            }
            catch
            {
                await transaction.RollbackAsync();
                throw;
            }
        }

        /// <summary>
        /// 處理訂單取消時的狀態回滾
        /// </summary>
        /// <param name="orderId">訂單ID</param>
        /// <param name="unitId">房屋單位ID (可選)</param>
        /// <param name="parkingSpaceIdsString">停車位ID字串 (可選)</param>
        public async Task HandleOrderCancelledAsync(int orderId, int? unitId, string? parkingSpaceIdsString)
        {
            using var transaction = await Db.Database.BeginTransactionAsync();
            try
            {
                // 變更房屋單位狀態回可售
                if (unitId.HasValue)
                {
                    await _unitService.ChangeUnitStatusAsync(unitId.Value, "可售", orderId);
                }

                // 變更停車位狀態回可售 - 現在透過Units表管理
                if (!string.IsNullOrEmpty(parkingSpaceIdsString))
                {
                    var parkingSpaceIds = ParkingSpaceService.ParseParkingSpaceIds(parkingSpaceIdsString);
                    if (parkingSpaceIds.Any())
                    {
                        await BatchChangeParkingSpaceStatusViaUnitsAsync(parkingSpaceIds, "可售", orderId);
                    }
                }

                await transaction.CommitAsync();
            }
            catch
            {
                await transaction.RollbackAsync();
                throw;
            }
        }

        /// <summary>
        /// 處理訂單完成簽約時的狀態變更
        /// </summary>
        /// <param name="orderId">訂單ID</param>
        /// <param name="unitId">房屋單位ID (可選)</param>
        /// <param name="parkingSpaceIdsString">停車位ID字串 (可選)</param>
        public async Task HandleOrderCompletedAsync(int orderId, int? unitId, string? parkingSpaceIdsString)
        {
            using var transaction = await Db.Database.BeginTransactionAsync();
            try
            {
                // 變更房屋單位狀態為已售
                if (unitId.HasValue)
                {
                    await _unitService.ChangeUnitStatusAsync(unitId.Value, "已售", orderId);
                }

                // 變更停車位狀態為已售 - 現在透過Units表管理
                if (!string.IsNullOrEmpty(parkingSpaceIdsString))
                {
                    var parkingSpaceIds = ParkingSpaceService.ParseParkingSpaceIds(parkingSpaceIdsString);
                    if (parkingSpaceIds.Any())
                    {
                        await BatchChangeParkingSpaceStatusViaUnitsAsync(parkingSpaceIds, "已售", orderId);
                    }
                }

                await transaction.CommitAsync();
            }
            catch
            {
                await transaction.RollbackAsync();
                throw;
            }
        }

        /// <summary>
        /// 批次設定房屋為保留狀態
        /// </summary>
        /// <param name="unitIds">房屋單位ID列表</param>
        /// <param name="reason">保留原因</param>
        public async Task BatchReserveUnitsAsync(List<int> unitIds, string? reason = null)
        {
            ArgumentNullException.ThrowIfNull(unitIds);

            await _unitService.BatchChangeUnitStatusAsync(unitIds, "保留");
        }

        /// <summary>
        /// 批次設定停車位為保留狀態 - 現在透過Units表管理
        /// </summary>
        /// <param name="parkingSpaceIds">停車位ID列表</param>
        /// <param name="reason">保留原因</param>
        public async Task BatchReserveParkingSpacesAsync(List<int> parkingSpaceIds, string? reason = null)
        {
            ArgumentNullException.ThrowIfNull(parkingSpaceIds);

            await BatchChangeParkingSpaceStatusViaUnitsAsync(parkingSpaceIds, "保留");
        }

        /// <summary>
        /// 批次設定停車位為地主保留狀態 - 現在透過Units表管理
        /// </summary>
        /// <param name="parkingSpaceIds">停車位ID列表</param>
        /// <param name="reason">保留原因</param>
        public async Task BatchOwnerReserveParkingSpacesAsync(List<int> parkingSpaceIds, string? reason = null)
        {
            ArgumentNullException.ThrowIfNull(parkingSpaceIds);

            await BatchChangeParkingSpaceStatusViaUnitsAsync(parkingSpaceIds, "地主保留");
        }

        /// <summary>
        /// 檢查房屋和車位的可售狀態
        /// </summary>
        /// <param name="unitId">房屋單位ID (可選)</param>
        /// <param name="parkingSpaceIds">停車位ID列表 (可選)</param>
        /// <returns>驗證結果</returns>
        public async Task<SalesValidationResult> ValidateAvailabilityAsync(int? unitId, List<int>? parkingSpaceIds)
        {
            var result = new SalesValidationResult { IsValid = true, Messages = new List<string>() };

            // 檢查房屋狀態
            if (unitId.HasValue)
            {
                var unit = await Db.Units.FindAsync(unitId.Value);
                if (unit == null)
                {
                    result.IsValid = false;
                    result.Messages.Add($"房屋單位 ID {unitId.Value} 不存在");
                }
                else if (unit.Status != "可售")
                {
                    result.IsValid = false;
                    result.Messages.Add($"房屋單位 {unit.UnitNumber} 目前狀態為 '{unit.Status}'，無法銷售");
                }
            }

            // 檢查停車位狀態 - 現在透過Units表查詢
            if (parkingSpaceIds != null && parkingSpaceIds.Any())
            {
                // 查詢關聯的車位單位
                var parkingUnits = await Db.Units
                    .Where(u => u.UnitType == "車位" && 
                               parkingSpaceIds.Any(psId => u.AssociatedParkingSpaceIds != null && 
                                                          u.AssociatedParkingSpaceIds.Contains(psId.ToString())))
                    .ToListAsync();

                // 檢查每個車位的狀態
                foreach (var parkingSpaceId in parkingSpaceIds)
                {
                    var parkingSpace = await Db.ParkingSpaces.FindAsync(parkingSpaceId);
                    if (parkingSpace == null)
                    {
                        result.IsValid = false;
                        result.Messages.Add($"停車位 ID {parkingSpaceId} 不存在");
                        continue;
                    }

                    var parkingUnit = parkingUnits.FirstOrDefault(u => 
                        u.AssociatedParkingSpaceIds != null && 
                        u.AssociatedParkingSpaceIds.Contains(parkingSpaceId.ToString()));

                    if (parkingUnit != null && parkingUnit.Status != "可售")
                    {
                        result.IsValid = false;
                        result.Messages.Add($"停車位 {parkingSpace.SpaceNumber} 目前狀態為 '{parkingUnit.Status}'，無法銷售");
                    }
                }
            }

            return result;
        }

        /// <summary>
        /// 透過Units表批次變更車位狀態
        /// </summary>
        /// <param name="parkingSpaceIds">停車位ID列表</param>
        /// <param name="status">新狀態</param>
        /// <param name="orderId">訂單ID (可選)</param>
        private async Task BatchChangeParkingSpaceStatusViaUnitsAsync(List<int> parkingSpaceIds, string status, int? orderId = null)
        {
            // 查詢關聯的車位單位
            var parkingUnits = await Db.Units
                .Where(u => u.UnitType == "車位" && 
                           parkingSpaceIds.Any(psId => u.AssociatedParkingSpaceIds != null && 
                                                      u.AssociatedParkingSpaceIds.Contains(psId.ToString())))
                .ToListAsync();

            // 更新狀態
            foreach (var unit in parkingUnits)
            {
                unit.Status = status;
                unit.UpdatedTime = DateTime.Now;
                unit.UpdatedUserInfoId = CurrentUser?.UserId;
            }

            await Db.SaveChangesAsync();
        }

        /// <summary>
        /// 從Units表獲取車位統計資料
        /// </summary>
        /// <param name="siteCode">案場編號</param>
        /// <returns>車位銷售統計</returns>
        private async Task<ParkingSpaceSalesStatistics> GetParkingSpaceStatisticsFromUnitsAsync(string siteCode)
        {
            var parkingUnits = await Db.Units
                .Where(u => u.SiteCode == siteCode && u.UnitType == "車位")
                .ToListAsync();

            var totalCount = parkingUnits.Count();
            var soldCount = parkingUnits.Count(u => u.Status == "已售");

            return new ParkingSpaceSalesStatistics
            {
                SiteCode = siteCode,
                TotalCount = totalCount,
                AvailableCount = parkingUnits.Count(u => u.Status == "可售"),
                ReservedCount = parkingUnits.Count(u => u.Status == "保留"),
                BookedCount = parkingUnits.Count(u => u.Status == "已預訂"),
                SoldCount = soldCount,
                OwnerReservedCount = parkingUnits.Count(u => u.Status == "地主保留"),
                SalesRate = totalCount > 0 ? (decimal)soldCount / totalCount * 100 : 0,
                TotalAvailableListPrice = parkingUnits
                    .Where(u => u.Status == "可售" && u.ListPrice.HasValue)
                    .Sum(u => u.ListPrice.Value),
                TotalReservedListPrice = parkingUnits
                    .Where(u => u.Status == "保留" && u.ListPrice.HasValue)
                    .Sum(u => u.ListPrice.Value)
            };
        }
    }

    /// <summary>
    /// 綜合銷售統計
    /// </summary>
    public class ComprehensiveSalesStatistics
    {
        /// <summary>
        /// 案場編號
        /// </summary>
        public string SiteCode { get; set; } = null!;

        /// <summary>
        /// 房屋銷售統計
        /// </summary>
        public UnitSalesStatistics UnitStatistics { get; set; } = null!;

        /// <summary>
        /// 停車位銷售統計
        /// </summary>
        public ParkingSpaceSalesStatistics ParkingSpaceStatistics { get; set; } = null!;

        /// <summary>
        /// 統計產生時間
        /// </summary>
        public DateTime GeneratedTime { get; set; }
    }

    /// <summary>
    /// 銷售驗證結果
    /// </summary>
    public class SalesValidationResult
    {
        /// <summary>
        /// 是否通過驗證
        /// </summary>
        public bool IsValid { get; set; }

        /// <summary>
        /// 驗證訊息
        /// </summary>
        public List<string> Messages { get; set; } = new();
    }
} 
