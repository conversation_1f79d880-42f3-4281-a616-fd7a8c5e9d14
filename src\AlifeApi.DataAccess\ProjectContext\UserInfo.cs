﻿using System;
using System.Collections.Generic;

namespace AlifeApi.DataAccess.ProjectContext
{
    /// <summary>
    /// 人員基本資料
    /// </summary>
    public partial class UserInfo
    {
        public UserInfo()
        {
            SysProblemReport = new HashSet<SysProblemReport>();
            SysRoleGroupUser = new HashSet<SysRoleGroupUser>();
            SysUserRecord = new HashSet<SysUserRecord>();
            UserDept = new HashSet<UserDept>();
            UserPasswordHistory = new HashSet<UserPasswordHistory>();
        }

        /// <summary>
        /// 員工編號
        /// </summary>
        public string Id { get; set; }

        /// <summary>
        /// 人員姓名
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// 密碼
        /// </summary>
        public string Pw { get; set; }

        /// <summary>
        /// 身分證字號
        /// </summary>
        public string IdNo { get; set; }

        /// <summary>
        /// 職位
        /// </summary>
        public string GradeCode { get; set; }

        /// <summary>
        /// 部門
        /// </summary>
        public string DeptId { get; set; }

        /// <summary>
        /// 電子郵件
        /// </summary>
        public string Email { get; set; }

        /// <summary>
        /// 是否啟用
        /// </summary>
        public bool IsEnabled { get; set; }

        /// <summary>
        /// 是否刪除
        /// </summary>
        public bool IsDisabled { get; set; }

        /// <summary>
        /// 啟用時間
        /// </summary>
        public DateTime? EnabledTtime { get; set; }

        /// <summary>
        /// 最後一次登入IP
        /// </summary>
        public string LastLoginIp { get; set; }

        /// <summary>
        /// 最後登入時間
        /// </summary>
        public DateTime? LastLoginTime { get; set; }

        /// <summary>
        /// 最後登出時間
        /// </summary>
        public DateTime? LastLogoutTime { get; set; }

        /// <summary>
        /// 登入失敗次數加總
        /// </summary>
        public short LoginFailedCount { get; set; }

        /// <summary>
        /// 建立者
        /// </summary>
        public string CreatedUserId { get; set; }

        /// <summary>
        /// 建立時間
        /// </summary>
        public DateTime CreatedTime { get; set; }

        /// <summary>
        /// 更新者
        /// </summary>
        public string UpdatedUserId { get; set; }

        /// <summary>
        /// 更新時間
        /// </summary>
        public DateTime UpdatedTime { get; set; }

        public virtual UserDept Dept { get; set; }

        public virtual UserGrade GradeCodeNavigation { get; set; }

        public virtual ICollection<SysProblemReport> SysProblemReport { get; set; }

        public virtual ICollection<SysRoleGroupUser> SysRoleGroupUser { get; set; }

        public virtual ICollection<SysUserRecord> SysUserRecord { get; set; }

        public virtual ICollection<UserDept> UserDept { get; set; }

        public virtual ICollection<UserPasswordHistory> UserPasswordHistory { get; set; }

    }
}
