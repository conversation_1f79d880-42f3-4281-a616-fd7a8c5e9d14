﻿using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;
using AlifeApi.Common.DataAnnotations;

namespace AlifeApi.BusinessRules.UserInfoModels
{
    /// <summary>
    /// 新增人員request資料模型
    /// </summary>
    public class UserInfoCreateInput
    {
        /// <summary>
        /// 員工編號
        /// </summary>
        [Required, MaxLength(15)]
        public string UserId { get; set; }

        /// <summary>
        /// 人員姓名
        /// </summary>
        [MaxLength(15)]
        public string UserName { get; set; }

        /// <summary>
        /// 密碼
        /// </summary>
        [MaxLength(255)]
        public string UserPw { get; set; }

        /// <summary>
        /// 身分證字號
        /// </summary>
        [MaxLength(15)]
        public string UserIdNo { get; set; }

        /// <summary>
        /// 職別
        /// </summary>
        [MaxLength(72)]
        [JsonPropertyName("Grade")]
        public string GradeCode { get; set; }

        /// <summary>
        /// 部門
        /// </summary>
        [MaxLength(20)]
        [RequiredForType(typeof(UserInfoCreateInput))]
        [JsonPropertyName("Dept")]
        public string DeptId { get; set; }

        /// <summary>
        /// 電子郵件
        /// </summary>
        [MaxLength(254)]
        public string UserEmail { get; set; }

        /// <summary>
        /// 角色
        /// </summary>
        [JsonPropertyName("Role")]
        public IEnumerable<string> Roles { get; set; }
    }
}
