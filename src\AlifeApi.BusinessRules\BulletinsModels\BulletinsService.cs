﻿//using AlifeApi.BusinessRules.Infrastructure;
//using AlifeApi.DataAccess.ProjectContext;
//using Microsoft.EntityFrameworkCore;

//namespace AlifeApi.BusinessRules.BulletinsModels
//{
//    public class BulletinsService : ServiceBase<ProjectContext>
//    {
//        public BulletinsService(IServiceProvider serviceProvider, ProjectContext dbContext) : base(serviceProvider, dbContext)
//        {
//        }

//        public async Task<List<CreateBulletinsDataResult>> CreateBulletins(List<CreateBulletinsDataCondition> inputs)
//        {
//            ArgumentNullException.ThrowIfNull(inputs, nameof(inputs));

//            //儲存公告主檔
//            var billboards = inputs.Select(x => new SysBulletins
//            {
//                BlTitle = x.Title,
//                BlContent = x.Content,
//                BlPostDateFrom = x.PostDateFrom,
//                BlPostDateToxx = x.PostDateTo,
//                BlIsTop = x.IsTop,
//                BlIsEnable = true,
//                BlCrUser = CurrentUser.UserId,
//                BlCrDatetime = DateTime.Now,
//                BlUpUser = CurrentUser.UserId,
//                BlUpDatetime = DateTime.Now,
//                SysBulletinsAttach = x.AttachList.Select(y => new SysBulletinsAttach()
//                {
//                    BlaOname = y.FileName,
//                    BlaFile = y.File,
//                    BlaFileType = y.FileName,
//                    BlaFileSzie = y.File.Length / 1024,
//                    BlaCrUser = CurrentUser.UserId,
//                    BlaCrDatetime = DateTime.Now
//                }).ToList()
//            }).ToList();

//            Db.SysBulletins.AddRange(billboards);
//            await Db.SaveChangesAsync();


//            return billboards.Select(x => new CreateBulletinsDataResult()
//            {
//                BL_Id = x.BlId,
//                UserId = x.BlCrUser
//            }).ToList();
//        }

//        public async Task DeleteBulletins(BulletinsCondition input)
//        {
//            ArgumentNullException.ThrowIfNull(input, nameof(input));

//            var bulletin = await Db.SysBulletins.AsQueryable().FirstOrDefaultAsync(w => w.BlId == input.BL_Id);
//            if (bulletin != null)
//            {
//                Db.SysBulletins.Remove(bulletin);

//                await Db.SaveChangesAsync();
//            }
//        }

//        public async Task<List<BulletinAttachmenFileModel>> GetBulletinAttachmentAllFile(int bL_Id)
//        {
//            ArgumentNullException.ThrowIfNull(bL_Id, nameof(bL_Id));

//            return Db.SysBulletinsAttach.AsQueryable()
//              .Where(x => x.BlId == bL_Id)
//              .Select(attach => new { attach.BlaId, attach.BlaFile, attach.BlaOname })
//              .AsEnumerable()
//              .Select(attach => new BulletinAttachmenFileModel
//              {
//                  FileName = attach.BlaOname,
//                  File = attach.BlaFile,
//                  BLA_Id = attach.BlaId,
//              }).ToList();
//        }

//        public async Task<PagedListOutput<GetBulletinsListResult>> GetBulletinsList(GetBulletinsListCondition input)
//        {
//            ArgumentNullException.ThrowIfNull(input, nameof(input));

//            PagedListOutput<GetBulletinsListResult> output = await Db.SysBulletins.AsNoTracking()
//                .Select(r => new GetBulletinsListResult
//                {
//                    BL_Id = r.BlId,
//                    Title = r.BlTitle,
//                    Content = r.BlContent,
//                    PostFrom = r.BlPostDateFrom,
//                    PostTo = r.BlPostDateToxx,
//                    IsTop = r.BlIsTop,
//                    CreateUserId = r.BlCrUser,
//                    CreateDate = r.BlCrDatetime,
//                    UpdateUserId = r.BlUpUser,
//                    UpdateDate = r.BlUpDatetime,
//                    ViewCount = r.SysBulletinsClickRecord.Sum(x => x.ViewCount),
//                    IsRead = r.SysBulletinsClickRecord.Any(x => x.UserId == CurrentUser.UserId)
//                })
//                .ToPagedListOutputAsync(input);

//            return output;
//        }

//        public async Task RecordDownloadMultipleBulletinAttachment(List<int> bulletinAttachIds)
//        {
//            foreach (var attachmentId in bulletinAttachIds)
//            {
//                await RecordDownloadBulletinAttachment(attachmentId);
//            }
//        }

//        public async Task RecordDownloadBulletinAttachment(int bulletinAttachId)
//        {
//            var downloadRecord = Db.SysBulletinsAttachDownloadRecord.FirstOrDefault(x => x.BlaId == bulletinAttachId && x.UserId == CurrentUser.UserId);

//            if (downloadRecord == null)
//            {
//                downloadRecord = new SysBulletinsAttachDownloadRecord()
//                {
//                    DownloadCount = 1,
//                    LastDownloadDate = DateTime.Now,
//                    BlaId = bulletinAttachId,
//                    UserId = CurrentUser.UserId,
//                };

//                Db.SysBulletinsAttachDownloadRecord.Add(downloadRecord);
//                await Db.SaveChangesAsync();
//            }
//            else
//            {
//                downloadRecord.DownloadCount++;
//                downloadRecord.LastDownloadDate = DateTime.Now;
//                Db.Entry(downloadRecord).State = EntityState.Modified;
//                await Db.SaveChangesAsync();
//            }
//        }

//        public async Task UpdateBulletins(UpdateBulletinsCondition input)
//        {
//            ArgumentNullException.ThrowIfNull(input, nameof(input));

//            var data = await Db.SysBulletins.AsQueryable().Where(c => c.BlId == input.BL_Id).Include(x => x.SysBulletinsAttach).FirstOrDefaultAsync();

//            if (data != null)
//            {
//                if (!string.IsNullOrEmpty(input.Title)) data.BlTitle = input.Title;
//                if (!string.IsNullOrEmpty(input.Content)) data.BlContent = input.Content;

//                if (input.PostDateFrom != null) data.BlPostDateFrom = input.PostDateFrom;
//                if (input.PostDateTo != null) data.BlPostDateToxx = input.PostDateTo;

//                if (input.IsTop != null) data.BlIsTop = (bool)input.IsTop;
//                data.BlCrUser = CurrentUser.UserId;
//                data.BlUpDatetime = DateTime.Now;


//                if (input.AttachList.Any())
//                {
//                    data.SysBulletinsAttach = input.AttachList.Select(y => new SysBulletinsAttach()
//                    {
//                        BlaOname = y.FileName,
//                        BlaFile = y.File,
//                        BlaFileType = y.FileName,
//                        BlaFileSzie = y.File.Length / 1024,
//                        BlaCrUser = CurrentUser.UserId,
//                        BlaCrDatetime = DateTime.Now
//                    }).ToList();

//                }
//                Db.Entry(data).State = EntityState.Modified;

//                await Db.SaveChangesAsync();

//            }
//        }

//        public async Task<GetBulletinsResult> GetBulletins(BulletinsCondition input)
//        {
//            ArgumentNullException.ThrowIfNull(input, nameof(input));

//            var data = await Db.SysBulletins.AsQueryable()
//                .Include(x => x.SysBulletinsAttach)
//                .Include(x => x.SysBulletinsClickRecord)
//                .FirstOrDefaultAsync(x => x.BlId == input.BL_Id);


//            if (data.SysBulletinsClickRecord.Any(x => x.UserId == CurrentUser.UserId))
//            {
//                data.SysBulletinsClickRecord.FirstOrDefault(x => x.UserId == CurrentUser.UserId).LastViewDate = DateTime.Now;
//                data.SysBulletinsClickRecord.FirstOrDefault(x => x.UserId == CurrentUser.UserId).ViewCount++;
//                Db.Entry(data).State = EntityState.Modified;
//            }
//            else
//            {
//                data.SysBulletinsClickRecord.Add(new SysBulletinsClickRecord()
//                {
//                    UserId = CurrentUser.UserId,
//                    ViewCount = 1,
//                    LastViewDate = DateTime.Now
//                });
//            }
//            Db.SaveChanges();

//            return new GetBulletinsResult
//            {
//                BL_Id = data.BlId,
//                Title = data.BlTitle,
//                Content = data.BlContent,
//                PostFrom = data.BlPostDateFrom,
//                PostTo = data.BlPostDateToxx,
//                IsTop = data.BlIsTop,
//                CreateUserId = data.BlCrUser,
//                CreateDate = data.BlCrDatetime,
//                UpdateUserId = data.BlUpUser,
//                UpdateDate = data.BlUpDatetime,
//                AttachList = data.SysBulletinsAttach.Select(x => new AttachListItem()
//                {
//                    File = x.BlaFile,
//                    FileName = x.BlaOname
//                }).ToList(),
//                ViewCount = data.SysBulletinsClickRecord.Sum(x => x.ViewCount),
//                IsRead = data.SysBulletinsClickRecord.Any(x => x.UserId == CurrentUser.UserId)
//            };
//        }
//    }
//}
