﻿using System;
using System.Collections.Generic;

namespace AlifeApi.DataAccess.ProjectContent
{
    /// <summary>
    /// 系統代碼表，用於儲存系統中的標準化代碼資訊，例如狀態碼、類型碼等。
    /// </summary>
    public partial class SysCode
    {
        /// <summary>
        /// 代碼類型，主鍵的一部分，用於區分不同的代碼類別。
        /// </summary>
        public string Type { get; set; }
        /// <summary>
        /// 代碼值，主鍵的一部分，用於唯一識別具體代碼。
        /// </summary>
        public string Code { get; set; }
        /// <summary>
        /// 上層代碼，可為 NULL，表示代碼的層級關係。
        /// </summary>
        public string ParentCode { get; set; }
        /// <summary>
        /// 代碼描述，提供代碼的詳細解釋。
        /// </summary>
        public string CodeDesc { get; set; }
        /// <summary>
        /// 是否啟用，true 表示啟用，false 表示停用，預設為 true。
        /// </summary>
        public bool? IsActive { get; set; }
        /// <summary>
        /// 排序序號，用於控制代碼的顯示順序。
        /// </summary>
        public short? CodeOrder { get; set; }
        /// <summary>
        /// 建立者，記錄創建此資料的員工編號。
        /// </summary>
        public string CreatedUserInfoId { get; set; }
        /// <summary>
        /// 創建時間，記錄資料創建的時間。
        /// </summary>
        public DateTime CreatedTime { get; set; }
        /// <summary>
        /// 更新者，記錄最後更新此資料的員工編號。
        /// </summary>
        public string UpdatedUserInfoId { get; set; }
        /// <summary>
        /// 更新時間，記錄資料最後更新的時間。
        /// </summary>
        public DateTime UpdatedTime { get; set; }
    }
}
