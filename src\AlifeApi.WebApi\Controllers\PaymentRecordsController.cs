using System;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using AlifeApi.BusinessRules.PaymentRecordModels;
using AlifeApi.BusinessRules.Infrastructure;

namespace AlifeApi.WebApi.Controllers
{
    /// <summary>
    /// 收款記錄管理
    /// </summary>
    public class PaymentRecordsController : AuthenticatedController
    {
        private readonly PaymentRecordService _paymentRecordService;

        public PaymentRecordsController(PaymentRecordService paymentRecordService)
        {
            _paymentRecordService = paymentRecordService ?? throw new ArgumentNullException(nameof(paymentRecordService));
        }

        /// <summary>
        /// 取得收款記錄列表 (分頁)
        /// </summary>
        /// <param name="input">查詢條件 (必須包含訂單ID)</param>
        /// <returns>分頁的收款記錄列表</returns>
        [HttpPost]
        public async Task<ActionResult<PagedListOutput<PaymentRecordListOutput>>> GetPaymentRecords([FromBody] PaymentRecordQueryInput input)
        {
            var result = await _paymentRecordService.GetPaymentRecordListAsync(input);
            return Ok(result);
        }

        /// <summary>
        /// 根據收款記錄ID取得詳細資訊
        /// </summary>
        /// <param name="paymentRecordId">收款記錄ID</param>
        /// <returns>收款記錄詳細資訊</returns>
        [HttpGet("{paymentRecordId}")]
        public async Task<ActionResult<PaymentRecordOutput>> GetPaymentRecord(int paymentRecordId)
        {
            var result = await _paymentRecordService.GetPaymentRecordByIdAsync(paymentRecordId);
            if (result == null)
                return NotFound();
            return Ok(result);
        }

        /// <summary>
        /// 新增收款記錄
        /// </summary>
        /// <param name="input">收款記錄建立輸入資料</param>
        /// <returns>新增的收款記錄ID</returns>
        [HttpPost]
        public async Task<ActionResult<int>> CreatePaymentRecord([FromBody] PaymentRecordCreateInput input)
        {
            try
            {
                var id = await _paymentRecordService.CreatePaymentRecordAsync(input);
                return CreatedAtAction(nameof(GetPaymentRecord), new { paymentRecordId = id }, new { PaymentRecordId = id });
            }
            catch (Exception ex)
            {
                return BadRequest(new { message = ex.Message });
            }
        }

        /// <summary>
        /// 更新收款記錄
        /// </summary>
        /// <param name="paymentRecordId">收款記錄ID</param>
        /// <param name="input">收款記錄更新輸入資料</param>
        /// <returns>NoContent</returns>
        [HttpPut("{paymentRecordId}")]
        public async Task<ActionResult> UpdatePaymentRecord(int paymentRecordId, [FromBody] PaymentRecordUpdateInput input)
        {
            try
            {
                await _paymentRecordService.UpdatePaymentRecordAsync(paymentRecordId, input);
                return NoContent();
            }
            catch (Exception ex)
            {
                return BadRequest(new { message = ex.Message });
            }
        }

        /// <summary>
        /// 刪除收款記錄
        /// </summary>
        /// <param name="paymentRecordId">收款記錄ID</param>
        /// <returns>NoContent</returns>
        [HttpDelete("{paymentRecordId}")]
        public async Task<ActionResult> DeletePaymentRecord(int paymentRecordId)
        {
            try
            {
                await _paymentRecordService.DeletePaymentRecordAsync(paymentRecordId);
                return NoContent();
            }
            catch (Exception ex)
            {
                return BadRequest(new { message = ex.Message });
            }
        }
    }
} 
