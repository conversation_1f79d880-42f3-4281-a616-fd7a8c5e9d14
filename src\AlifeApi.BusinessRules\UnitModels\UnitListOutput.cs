using System;

namespace AlifeApi.BusinessRules.UnitModels
{
    /// <summary>
    /// 房屋單位列表輸出項目
    /// </summary>
    public class UnitListOutput
    {
        /// <summary>
        /// 房屋單位唯一識別碼
        /// </summary>
        public int UnitId { get; set; }

        /// <summary>
        /// 樓層ID
        /// </summary>
        public int FloorId { get; set; }

        /// <summary>
        /// 建築物ID
        /// </summary>
        public int BuildingId { get; set; }

        /// <summary>
        /// 案場代碼
        /// </summary>
        public string SiteCode { get; set; } = null!;

        /// <summary>
        /// 戶號
        /// </summary>
        public string UnitNumber { get; set; } = null!;

        /// <summary>
        /// 單位類型
        /// </summary>
        public string UnitType { get; set; } = null!;

        /// <summary>
        /// 格局
        /// </summary>
        public string? Layout { get; set; }

        /// <summary>
        /// 權狀總坪數
        /// </summary>
        public decimal TotalArea { get; set; }

        /// <summary>
        /// 列表售價
        /// </summary>
        public decimal? ListPrice { get; set; }

        /// <summary>
        /// 銷售狀態
        /// </summary>
        public string Status { get; set; } = null!;

        /// <summary>
        /// 建立時間
        /// </summary>
        public DateTime CreatedTime { get; set; }

        /// <summary>
        /// 樓層標示 (選填)
        /// </summary>
        public string? FloorLabel { get; set; }

        /// <summary>
        /// 建築物名稱 (選填)
        /// </summary>
        public string? BuildingName { get; set; }
    }
} 
