﻿using System.ComponentModel.DataAnnotations;

namespace AlifeApi.BusinessRules.ProblemReportModels
{
    /// <summary>
    /// 新增問題回報 Request 資料模型
    /// </summary>
    public class UserReportCreateInput
    {
        /// <summary>
        /// 功能頁面
        /// </summary>
        [Required, <PERSON><PERSON>ength(50)]
        public string Menu { get; set; }

        /// <summary>
        /// 類型
        /// </summary>
        [Required, MaxLength(10)]
        public string ProblemType { get; set; }

        /// <summary>
        /// 問題主旨
        /// </summary>
        [Required, MaxLength(200)]
        public string Subject { get; set; }

        /// <summary>
        /// 問題內容
        /// </summary>
        [Required, <PERSON><PERSON>ength(200)]
        public string ReportContent { get; set; }
    }
}
