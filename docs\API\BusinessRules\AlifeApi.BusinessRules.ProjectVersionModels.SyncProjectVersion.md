#### [AlifeApi.BusinessRules](index.md 'index')
### [AlifeApi.BusinessRules.ProjectVersionModels](AlifeApi.BusinessRules.ProjectVersionModels.md 'AlifeApi.BusinessRules.ProjectVersionModels')

## SyncProjectVersion Class

```csharp
public class SyncProjectVersion : AlifeApi.BusinessRules.ProjectVersionModels.ProjectVersionBase
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; [ProjectVersionBase](AlifeApi.BusinessRules.ProjectVersionModels.ProjectVersionBase.md 'AlifeApi.BusinessRules.ProjectVersionModels.ProjectVersionBase') &#129106; SyncProjectVersion
### Properties

<a name='AlifeApi.BusinessRules.ProjectVersionModels.SyncProjectVersion.Content'></a>

## SyncProjectVersion.Content Property

更新內容

```csharp
public string Content { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.ProjectVersionModels.SyncProjectVersion.LastUpdateUser'></a>

## SyncProjectVersion.LastUpdateUser Property

版本發佈人

```csharp
public string LastUpdateUser { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.ProjectVersionModels.SyncProjectVersion.VersionHash'></a>

## SyncProjectVersion.VersionHash Property

系統專案版號

```csharp
public string VersionHash { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')