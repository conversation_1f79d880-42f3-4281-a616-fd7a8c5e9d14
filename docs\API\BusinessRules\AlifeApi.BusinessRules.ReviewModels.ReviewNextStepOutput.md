#### [AlifeApi.BusinessRules](index.md 'index')
### [AlifeApi.BusinessRules.ReviewModels](AlifeApi.BusinessRules.ReviewModels.md 'AlifeApi.BusinessRules.ReviewModels')

## ReviewNextStepOutput Class

下一步驟輸出資料

```csharp
public class ReviewNextStepOutput
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; ReviewNextStepOutput
### Properties

<a name='AlifeApi.BusinessRules.ReviewModels.ReviewNextStepOutput.Approvers'></a>

## ReviewNextStepOutput.Approvers Property

審核人員清單

```csharp
public System.Collections.Generic.List<AlifeApi.BusinessRules.ReviewModels.ReviewApproverOutput> Approvers { get; set; }
```

#### Property Value
[System.Collections.Generic.List&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Collections.Generic.List-1 'System.Collections.Generic.List`1')[ReviewApproverOutput](AlifeApi.BusinessRules.ReviewModels.ReviewApproverOutput.md 'AlifeApi.BusinessRules.ReviewModels.ReviewApproverOutput')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Collections.Generic.List-1 'System.Collections.Generic.List`1')

<a name='AlifeApi.BusinessRules.ReviewModels.ReviewNextStepOutput.Sequence'></a>

## ReviewNextStepOutput.Sequence Property

步驟順序

```csharp
public int Sequence { get; set; }
```

#### Property Value
[System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')

<a name='AlifeApi.BusinessRules.ReviewModels.ReviewNextStepOutput.StepId'></a>

## ReviewNextStepOutput.StepId Property

步驟ID

```csharp
public int StepId { get; set; }
```

#### Property Value
[System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')

<a name='AlifeApi.BusinessRules.ReviewModels.ReviewNextStepOutput.StepName'></a>

## ReviewNextStepOutput.StepName Property

步驟名稱

```csharp
public string StepName { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')