#### [AlifeApi.BusinessRules](index.md 'index')
### [AlifeApi.BusinessRules.SiteModels](AlifeApi.BusinessRules.SiteModels.md 'AlifeApi.BusinessRules.SiteModels')

## SiteCreateInput Class

案場創建輸入

```csharp
public class SiteCreateInput
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; SiteCreateInput
### Properties

<a name='AlifeApi.BusinessRules.SiteModels.SiteCreateInput.AboveGroundFloors'></a>

## SiteCreateInput.AboveGroundFloors Property

地上層數

```csharp
public string AboveGroundFloors { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.SiteModels.SiteCreateInput.AdvertisingBudget'></a>

## SiteCreateInput.AdvertisingBudget Property

應編廣告預算

```csharp
public System.Nullable<decimal> AdvertisingBudget { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.Decimal](https://docs.microsoft.com/en-us/dotnet/api/System.Decimal 'System.Decimal')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

<a name='AlifeApi.BusinessRules.SiteModels.SiteCreateInput.AdvertisingBudgetRate'></a>

## SiteCreateInput.AdvertisingBudgetRate Property

廣告預算率

```csharp
public System.Nullable<decimal> AdvertisingBudgetRate { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.Decimal](https://docs.microsoft.com/en-us/dotnet/api/System.Decimal 'System.Decimal')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

<a name='AlifeApi.BusinessRules.SiteModels.SiteCreateInput.BelowGroundFloors'></a>

## SiteCreateInput.BelowGroundFloors Property

地下層數

```csharp
public string BelowGroundFloors { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.SiteModels.SiteCreateInput.Broker'></a>

## SiteCreateInput.Broker Property

經紀人

```csharp
public string Broker { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.SiteModels.SiteCreateInput.BusinessIds'></a>

## SiteCreateInput.BusinessIds Property

業務

```csharp
public string BusinessIds { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.SiteModels.SiteCreateInput.Chairman'></a>

## SiteCreateInput.Chairman Property

主委

```csharp
public string Chairman { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.SiteModels.SiteCreateInput.City'></a>

## SiteCreateInput.City Property

所在縣市

```csharp
public string City { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.SiteModels.SiteCreateInput.CompanyId'></a>

## SiteCreateInput.CompanyId Property

執行公司別

```csharp
public string CompanyId { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.SiteModels.SiteCreateInput.ContractedAmount'></a>

## SiteCreateInput.ContractedAmount Property

已發包金額

```csharp
public System.Nullable<decimal> ContractedAmount { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.Decimal](https://docs.microsoft.com/en-us/dotnet/api/System.Decimal 'System.Decimal')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

<a name='AlifeApi.BusinessRules.SiteModels.SiteCreateInput.ContractPeriod'></a>

## SiteCreateInput.ContractPeriod Property

合約期間

```csharp
public System.Nullable<System.DateOnly> ContractPeriod { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.DateOnly](https://docs.microsoft.com/en-us/dotnet/api/System.DateOnly 'System.DateOnly')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

<a name='AlifeApi.BusinessRules.SiteModels.SiteCreateInput.ControlReserveRate'></a>

## SiteCreateInput.ControlReserveRate Property

控存率

```csharp
public System.Nullable<decimal> ControlReserveRate { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.Decimal](https://docs.microsoft.com/en-us/dotnet/api/System.Decimal 'System.Decimal')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

<a name='AlifeApi.BusinessRules.SiteModels.SiteCreateInput.DeputyProjectManager'></a>

## SiteCreateInput.DeputyProjectManager Property

副專

```csharp
public string DeputyProjectManager { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.SiteModels.SiteCreateInput.Developer'></a>

## SiteCreateInput.Developer Property

投資興建

```csharp
public string Developer { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.SiteModels.SiteCreateInput.District'></a>

## SiteCreateInput.District Property

所在區域

```csharp
public string District { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.SiteModels.SiteCreateInput.ExcessPriceAllocation'></a>

## SiteCreateInput.ExcessPriceAllocation Property

超價款分配

```csharp
public string ExcessPriceAllocation { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.SiteModels.SiteCreateInput.ExtensionPeriod'></a>

## SiteCreateInput.ExtensionPeriod Property

展延期間

```csharp
public System.Nullable<System.DateOnly> ExtensionPeriod { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.DateOnly](https://docs.microsoft.com/en-us/dotnet/api/System.DateOnly 'System.DateOnly')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

<a name='AlifeApi.BusinessRules.SiteModels.SiteCreateInput.LandArea'></a>

## SiteCreateInput.LandArea Property

基地面積

```csharp
public System.Nullable<decimal> LandArea { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.Decimal](https://docs.microsoft.com/en-us/dotnet/api/System.Decimal 'System.Decimal')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

<a name='AlifeApi.BusinessRules.SiteModels.SiteCreateInput.PaidAmount'></a>

## SiteCreateInput.PaidAmount Property

已請款金額

```csharp
public System.Nullable<decimal> PaidAmount { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.Decimal](https://docs.microsoft.com/en-us/dotnet/api/System.Decimal 'System.Decimal')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

<a name='AlifeApi.BusinessRules.SiteModels.SiteCreateInput.ParkingType'></a>

## SiteCreateInput.ParkingType Property

車位類別

```csharp
public string ParkingType { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.SiteModels.SiteCreateInput.PlannedFloors'></a>

## SiteCreateInput.PlannedFloors Property

規劃樓層

```csharp
public string PlannedFloors { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.SiteModels.SiteCreateInput.PlannedParkingSpaces'></a>

## SiteCreateInput.PlannedParkingSpaces Property

規劃戶數(車位)

```csharp
public System.Nullable<int> PlannedParkingSpaces { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

<a name='AlifeApi.BusinessRules.SiteModels.SiteCreateInput.PlannedResidentialUnits'></a>

## SiteCreateInput.PlannedResidentialUnits Property

規劃戶數(住家)

```csharp
public System.Nullable<int> PlannedResidentialUnits { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

<a name='AlifeApi.BusinessRules.SiteModels.SiteCreateInput.PlannedStoreUnits'></a>

## SiteCreateInput.PlannedStoreUnits Property

規劃戶數(店面)

```csharp
public System.Nullable<int> PlannedStoreUnits { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

<a name='AlifeApi.BusinessRules.SiteModels.SiteCreateInput.PlannedUnitsAndParking'></a>

## SiteCreateInput.PlannedUnitsAndParking Property

規劃戶車

```csharp
public string PlannedUnitsAndParking { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.SiteModels.SiteCreateInput.ProjectManager'></a>

## SiteCreateInput.ProjectManager Property

專案

```csharp
public string ProjectManager { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.SiteModels.SiteCreateInput.PromotionType'></a>

## SiteCreateInput.PromotionType Property

推案型態

```csharp
public string PromotionType { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.SiteModels.SiteCreateInput.PublicFacilityRatio'></a>

## SiteCreateInput.PublicFacilityRatio Property

公設比

```csharp
public System.Nullable<decimal> PublicFacilityRatio { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.Decimal](https://docs.microsoft.com/en-us/dotnet/api/System.Decimal 'System.Decimal')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

<a name='AlifeApi.BusinessRules.SiteModels.SiteCreateInput.ReceptionCenter'></a>

## SiteCreateInput.ReceptionCenter Property

接待中心

```csharp
public string ReceptionCenter { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.SiteModels.SiteCreateInput.ReserveAmount'></a>

## SiteCreateInput.ReserveAmount Property

保留款

```csharp
public System.Nullable<decimal> ReserveAmount { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.Decimal](https://docs.microsoft.com/en-us/dotnet/api/System.Decimal 'System.Decimal')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

<a name='AlifeApi.BusinessRules.SiteModels.SiteCreateInput.RunnerIds'></a>

## SiteCreateInput.RunnerIds Property

跑單

```csharp
public string RunnerIds { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.SiteModels.SiteCreateInput.SellableTotalPrice'></a>

## SiteCreateInput.SellableTotalPrice Property

可售總銷

```csharp
public System.Nullable<decimal> SellableTotalPrice { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.Decimal](https://docs.microsoft.com/en-us/dotnet/api/System.Decimal 'System.Decimal')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

<a name='AlifeApi.BusinessRules.SiteModels.SiteCreateInput.ServiceFeeCalculation'></a>

## SiteCreateInput.ServiceFeeCalculation Property

服務費計算

```csharp
public string ServiceFeeCalculation { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.SiteModels.SiteCreateInput.ServiceFeeRate'></a>

## SiteCreateInput.ServiceFeeRate Property

服務費率

```csharp
public System.Nullable<decimal> ServiceFeeRate { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.Decimal](https://docs.microsoft.com/en-us/dotnet/api/System.Decimal 'System.Decimal')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

<a name='AlifeApi.BusinessRules.SiteModels.SiteCreateInput.SiteCode'></a>

## SiteCreateInput.SiteCode Property

案場代碼

```csharp
public string SiteCode { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.SiteModels.SiteCreateInput.SiteLocation'></a>

## SiteCreateInput.SiteLocation Property

基地位置

```csharp
public string SiteLocation { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.SiteModels.SiteCreateInput.SiteName'></a>

## SiteCreateInput.SiteName Property

案場名稱

```csharp
public string SiteName { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.SiteModels.SiteCreateInput.SitePhone'></a>

## SiteCreateInput.SitePhone Property

案場電話

```csharp
public string SitePhone { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.SiteModels.SiteCreateInput.Structure'></a>

## SiteCreateInput.Structure Property

結構

```csharp
public string Structure { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.SiteModels.SiteCreateInput.TotalPlannedFloors'></a>

## SiteCreateInput.TotalPlannedFloors Property

規劃總層

```csharp
public System.Nullable<int> TotalPlannedFloors { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

<a name='AlifeApi.BusinessRules.SiteModels.SiteCreateInput.TotalSalePrice'></a>

## SiteCreateInput.TotalSalePrice Property

全案總銷

```csharp
public System.Nullable<decimal> TotalSalePrice { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.Decimal](https://docs.microsoft.com/en-us/dotnet/api/System.Decimal 'System.Decimal')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

<a name='AlifeApi.BusinessRules.SiteModels.SiteCreateInput.UnitSize'></a>

## SiteCreateInput.UnitSize Property

坪數

```csharp
public string UnitSize { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.SiteModels.SiteCreateInput.ViceChairman'></a>

## SiteCreateInput.ViceChairman Property

副主委

```csharp
public string ViceChairman { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.SiteModels.SiteCreateInput.Zoning'></a>

## SiteCreateInput.Zoning Property

使用分區

```csharp
public string Zoning { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')