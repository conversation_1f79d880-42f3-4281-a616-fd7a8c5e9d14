﻿using System;
using System.Collections.Generic;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata;

namespace LogcontextPg
{
    public partial class LogContextPg : DbContext
    {
        public LogContextPg()
        {
        }

        public LogContextPg(DbContextOptions<LogContextPg> options)
            : base(options)
        {
        }

        public virtual DbSet<SysApiLog> SysApiLogs { get; set; }

        protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
        {
            
        }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            modelBuilder.Entity<SysApiLog>(entity =>
            {
                entity.ToTable("SYS_ApiLog");

                entity.HasComment("API 日誌記錄表");

                entity.Property(e => e.Id).HasComment("0");

                entity.Property(e => e.ActionName)
                    .IsRequired()
                    .HasMaxLength(100)
                    .HasComment("執行方法名稱");

                entity.Property(e => e.ControllerName)
                    .IsRequired()
                    .HasMaxLength(100)
                    .HasComment("控制器名稱");

                entity.Property(e => e.Exception)
                    .IsRequired()
                    .HasComment("錯誤異常");

                entity.Property(e => e.ExecutedTime)
                    .HasColumnType("timestamp without time zone")
                    .HasComment("操作時間");

                entity.Property(e => e.Headers)
                    .IsRequired()
                    .HasMaxLength(4000)
                    .HasComment("請求標頭");

                entity.Property(e => e.InputData)
                    .IsRequired()
                    .HasComment("輸入資料");

                entity.Property(e => e.OutputData)
                    .IsRequired()
                    .HasComment("輸出資料");

                entity.Property(e => e.Seconds).HasComment("執行時間（秒）");

                entity.Property(e => e.SessionId)
                    .IsRequired()
                    .HasMaxLength(50)
                    .HasComment("Session ID");

                entity.Property(e => e.Source)
                    .IsRequired()
                    .HasMaxLength(200)
                    .HasComment("請求來源");

                entity.Property(e => e.System)
                    .IsRequired()
                    .HasMaxLength(30)
                    .HasComment("系統名稱");
            });

            OnModelCreatingPartial(modelBuilder);
        }

        partial void OnModelCreatingPartial(ModelBuilder modelBuilder);
    }
}
