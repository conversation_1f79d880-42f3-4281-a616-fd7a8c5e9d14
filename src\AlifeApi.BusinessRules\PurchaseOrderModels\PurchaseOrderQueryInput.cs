using AlifeApi.BusinessRules.Infrastructure;
using System;

namespace AlifeApi.BusinessRules.PurchaseOrderModels
{
    /// <summary>
    /// 買賣預定單列表查詢輸入模型
    /// </summary>
    public class PurchaseOrderQueryInput : PagedListInput
    {
        public string? SiteCode { get; set; }
        public int? CustomerId { get; set; }
        public string? CustomerName { get; set; }
        public int? UnitId { get; set; }
        public string? UnitNumber { get; set; }
        public string? OrderNumber { get; set; }
        public DateOnly? OrderDateStart { get; set; }
        public DateOnly? OrderDateEnd { get; set; }
        public string? SalespersonUserInfoId { get; set; }
        public string? SalespersonName { get; set; }
        public string? Status { get; set; }
        
        public DateOnly? SaleDateStart { get; set; }
        public DateOnly? SaleDateEnd { get; set; }
        public DateOnly? DepositFullPaidDateStart { get; set; }
        public DateOnly? DepositFullPaidDateEnd { get; set; }
        public DateOnly? ContractSignedDateStart { get; set; }
        public DateOnly? ContractSignedDateEnd { get; set; }
        public DateOnly? CancellationDateStart { get; set; }
        public DateOnly? CancellationDateEnd { get; set; }
    }
} 
