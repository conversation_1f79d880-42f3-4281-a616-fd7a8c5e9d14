#### [AlifeApi.BusinessRules](index.md 'index')
### [AlifeApi.BusinessRules.SysCodeModels](AlifeApi.BusinessRules.SysCodeModels.md 'AlifeApi.BusinessRules.SysCodeModels')

## SysCodeService Class

SYSCode

```csharp
public class SysCodeService : AlifeApi.BusinessRules.Infrastructure.ServiceBase<AlifeApi.DataAccess.ProjectContent.alifeContext>
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; [AlifeApi.Common.InfrastructureBase](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.Common.InfrastructureBase 'AlifeApi.Common.InfrastructureBase') &#129106; [AlifeApi.BusinessRules.Infrastructure.ServiceBase&lt;](AlifeApi.BusinessRules.Infrastructure.ServiceBase_TDbContext_.md 'AlifeApi.BusinessRules.Infrastructure.ServiceBase<TDbContext>')[AlifeApi.DataAccess.ProjectContent.alifeContext](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.DataAccess.ProjectContent.alifeContext 'AlifeApi.DataAccess.ProjectContent.alifeContext')[&gt;](AlifeApi.BusinessRules.Infrastructure.ServiceBase_TDbContext_.md 'AlifeApi.BusinessRules.Infrastructure.ServiceBase<TDbContext>') &#129106; SysCodeService
### Methods

<a name='AlifeApi.BusinessRules.SysCodeModels.SysCodeService.CreateSysCodeAsync(AlifeApi.BusinessRules.SysCodeModels.SysCodeCreateInput)'></a>

## SysCodeService.CreateSysCodeAsync(SysCodeCreateInput) Method

新增代碼

```csharp
public System.Threading.Tasks.Task CreateSysCodeAsync(AlifeApi.BusinessRules.SysCodeModels.SysCodeCreateInput input);
```
#### Parameters

<a name='AlifeApi.BusinessRules.SysCodeModels.SysCodeService.CreateSysCodeAsync(AlifeApi.BusinessRules.SysCodeModels.SysCodeCreateInput).input'></a>

`input` [SysCodeCreateInput](AlifeApi.BusinessRules.SysCodeModels.SysCodeCreateInput.md 'AlifeApi.BusinessRules.SysCodeModels.SysCodeCreateInput')

The input.

#### Returns
[System.Threading.Tasks.Task](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task 'System.Threading.Tasks.Task')

#### Exceptions

[System.ArgumentNullException](https://docs.microsoft.com/en-us/dotnet/api/System.ArgumentNullException 'System.ArgumentNullException')  
input

<a name='AlifeApi.BusinessRules.SysCodeModels.SysCodeService.GetAllSysCodeTypesAsync()'></a>

## SysCodeService.GetAllSysCodeTypesAsync() Method

取得所有 SysCode 類型 (不分頁)

```csharp
public System.Threading.Tasks.Task<System.Collections.Generic.List<string>> GetAllSysCodeTypesAsync();
```

#### Returns
[System.Threading.Tasks.Task&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')[System.Collections.Generic.List&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Collections.Generic.List-1 'System.Collections.Generic.List`1')[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Collections.Generic.List-1 'System.Collections.Generic.List`1')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')

<a name='AlifeApi.BusinessRules.SysCodeModels.SysCodeService.GetSysCodeListByTypeAsync(AlifeApi.BusinessRules.SysCodeModels.SysCodeByTypeGetInput)'></a>

## SysCodeService.GetSysCodeListByTypeAsync(SysCodeByTypeGetInput) Method

依 Type 取得系統代碼

```csharp
public System.Threading.Tasks.Task<AlifeApi.BusinessRules.Infrastructure.PagedListOutput<AlifeApi.BusinessRules.SysCodeModels.SysCodeOutput>> GetSysCodeListByTypeAsync(AlifeApi.BusinessRules.SysCodeModels.SysCodeByTypeGetInput input);
```
#### Parameters

<a name='AlifeApi.BusinessRules.SysCodeModels.SysCodeService.GetSysCodeListByTypeAsync(AlifeApi.BusinessRules.SysCodeModels.SysCodeByTypeGetInput).input'></a>

`input` [SysCodeByTypeGetInput](AlifeApi.BusinessRules.SysCodeModels.SysCodeByTypeGetInput.md 'AlifeApi.BusinessRules.SysCodeModels.SysCodeByTypeGetInput')

The input.

#### Returns
[System.Threading.Tasks.Task&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')[AlifeApi.BusinessRules.Infrastructure.PagedListOutput&lt;](AlifeApi.BusinessRules.Infrastructure.PagedListOutput_TDetail_.md 'AlifeApi.BusinessRules.Infrastructure.PagedListOutput<TDetail>')[SysCodeOutput](AlifeApi.BusinessRules.SysCodeModels.SysCodeOutput.md 'AlifeApi.BusinessRules.SysCodeModels.SysCodeOutput')[&gt;](AlifeApi.BusinessRules.Infrastructure.PagedListOutput_TDetail_.md 'AlifeApi.BusinessRules.Infrastructure.PagedListOutput<TDetail>')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')  
系統代碼

#### Exceptions

[System.ArgumentNullException](https://docs.microsoft.com/en-us/dotnet/api/System.ArgumentNullException 'System.ArgumentNullException')  
input

<a name='AlifeApi.BusinessRules.SysCodeModels.SysCodeService.GetSysCodesByTypeAsync(string)'></a>

## SysCodeService.GetSysCodesByTypeAsync(string) Method

根據類型取得所有 SysCode 條目 (不分頁)

```csharp
public System.Threading.Tasks.Task<System.Collections.Generic.List<AlifeApi.BusinessRules.SysCodeModels.SysCodeOutput>> GetSysCodesByTypeAsync(string type);
```
#### Parameters

<a name='AlifeApi.BusinessRules.SysCodeModels.SysCodeService.GetSysCodesByTypeAsync(string).type'></a>

`type` [System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

#### Returns
[System.Threading.Tasks.Task&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')[System.Collections.Generic.List&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Collections.Generic.List-1 'System.Collections.Generic.List`1')[SysCodeOutput](AlifeApi.BusinessRules.SysCodeModels.SysCodeOutput.md 'AlifeApi.BusinessRules.SysCodeModels.SysCodeOutput')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Collections.Generic.List-1 'System.Collections.Generic.List`1')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')

<a name='AlifeApi.BusinessRules.SysCodeModels.SysCodeService.UpdateSysCodeAsync(AlifeApi.BusinessRules.SysCodeModels.SysCodeUpdateInput)'></a>

## SysCodeService.UpdateSysCodeAsync(SysCodeUpdateInput) Method

編輯、作廢代碼

```csharp
public System.Threading.Tasks.Task UpdateSysCodeAsync(AlifeApi.BusinessRules.SysCodeModels.SysCodeUpdateInput input);
```
#### Parameters

<a name='AlifeApi.BusinessRules.SysCodeModels.SysCodeService.UpdateSysCodeAsync(AlifeApi.BusinessRules.SysCodeModels.SysCodeUpdateInput).input'></a>

`input` [SysCodeUpdateInput](AlifeApi.BusinessRules.SysCodeModels.SysCodeUpdateInput.md 'AlifeApi.BusinessRules.SysCodeModels.SysCodeUpdateInput')

The input.

#### Returns
[System.Threading.Tasks.Task](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task 'System.Threading.Tasks.Task')

#### Exceptions

[System.ArgumentNullException](https://docs.microsoft.com/en-us/dotnet/api/System.ArgumentNullException 'System.ArgumentNullException')  
input