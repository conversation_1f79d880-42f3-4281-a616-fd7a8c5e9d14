using System;
using System.ComponentModel.DataAnnotations;

namespace AlifeApi.BusinessRules.FloorModels
{
    /// <summary>
    /// 樓層建立輸入模型
    /// </summary>
    public class FloorCreateInput
    {
        [Required(ErrorMessage = "缺少建築物ID")]
        public int BuildingId { get; set; }

        [Required(ErrorMessage = "缺少案場代碼")]
        public string SiteCode { get; set; } = null!;

        [Required(ErrorMessage = "缺少樓層標示")]
        public string FloorLabel { get; set; } = null!;

        [Required(ErrorMessage = "缺少樓層數值")]
        public int FloorLevel { get; set; }

        [Required(ErrorMessage = "缺少樓層類型")]
        public string FloorType { get; set; } = null!;

        public decimal? FloorHeight { get; set; }
        public string? Remarks { get; set; }
    }
} 
