#### [AlifeApi.Common](index.md 'index')
### [AlifeApi.Common](AlifeApi.Common.md 'AlifeApi.Common')

## InfrastructureBase Class

Provides a basic class for infrastructure components.

```csharp
public class InfrastructureBase
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; InfrastructureBase
### Constructors

<a name='AlifeApi.Common.InfrastructureBase.InfrastructureBase(System.IServiceProvider)'></a>

## InfrastructureBase(IServiceProvider) Constructor

Initializes a new instance of the [InfrastructureBase](AlifeApi.Common.InfrastructureBase.md 'AlifeApi.Common.InfrastructureBase') class.

```csharp
public InfrastructureBase(System.IServiceProvider serviceProvider);
```
#### Parameters

<a name='AlifeApi.Common.InfrastructureBase.InfrastructureBase(System.IServiceProvider).serviceProvider'></a>

`serviceProvider` [System.IServiceProvider](https://docs.microsoft.com/en-us/dotnet/api/System.IServiceProvider 'System.IServiceProvider')

The optional service provider used for dependency resolution.

#### Exceptions

[System.ArgumentNullException](https://docs.microsoft.com/en-us/dotnet/api/System.ArgumentNullException 'System.ArgumentNullException')  
Thrown when [serviceProvider](AlifeApi.Common.InfrastructureBase.md#AlifeApi.Common.InfrastructureBase.InfrastructureBase(System.IServiceProvider).serviceProvider 'AlifeApi.Common.InfrastructureBase.InfrastructureBase(System.IServiceProvider).serviceProvider') is `null`.
### Properties

<a name='AlifeApi.Common.InfrastructureBase.LazyServiceProvider'></a>

## InfrastructureBase.LazyServiceProvider Property

Gets the lazy service provider used for resolving services.

```csharp
protected AlifeApi.Common.DependencyInjection.LazyServiceProvider LazyServiceProvider { get; }
```

#### Property Value
[LazyServiceProvider](AlifeApi.Common.DependencyInjection.LazyServiceProvider.md 'AlifeApi.Common.DependencyInjection.LazyServiceProvider')