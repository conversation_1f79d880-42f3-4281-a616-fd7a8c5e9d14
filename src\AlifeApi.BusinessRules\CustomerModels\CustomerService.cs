﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using AlifeApi.BusinessRules.Infrastructure;
using AlifeApi.DataAccess.ProjectContent;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;

namespace AlifeApi.BusinessRules.CustomerModels
{
    /// <summary>
    /// 客戶服務
    /// </summary>
    public class CustomerService : ServiceBase<alifeContext>
    {
        private readonly IImageStorageService _imageStorageService;
        private readonly string _imageBaseUrl;

        /// <summary>
        /// 建構函數
        /// </summary>
        public CustomerService(
            IServiceProvider serviceProvider,
            alifeContext dbContext,
            IImageStorageService imageStorageService,
            IConfiguration configuration)
            : base(serviceProvider, dbContext)
        {
            _imageStorageService = imageStorageService;
            _imageBaseUrl = configuration["ImageStorageSettings:ImageBaseUrl"];
            if (!string.IsNullOrEmpty(_imageBaseUrl) && !_imageBaseUrl.EndsWith("/"))
            {
                _imageBaseUrl += "/";
            }
        }

        /// <summary>
        /// 取得客戶列表 (分頁，摘要資訊)
        /// </summary>
        /// <param name="input">查詢條件</param>
        /// <returns>分頁的客戶列表 (摘要)</returns>
        public async Task<PagedListOutput<CustomerListOutput>> GetCustomerListAsync(CustomerQueryInput input)
        {
            ArgumentNullException.ThrowIfNull(input, nameof(input));

            var currentUserId = CurrentUser.UserId;

            // 第一步：先將所有的客戶資料撈出來
            var allCustomers = await Db.Customers.ToListAsync();

            // 第二步：將所有的客戶記錄資料撈出來
            var allCustomerRecords = await Db.CustomerRecords.ToListAsync();

            // 第三步：計算每個客戶的記錄統計資訊
            var customerRecordStats = allCustomerRecords
                .GroupBy(cr => cr.CustomerId)
                .Select(g => new { 
                    CustomerId = g.Key, 
                    EarliestTime = g.Min(cr => cr.RecordedAt),
                    RecordCount = g.Count(),
                    Records = g.OrderBy(cr => cr.RecordedAt).ToList()
                })
                .ToList();

            // 第四步：找出符合權限條件的客戶ID
            var accessibleCustomerIds = new List<int>();
            
            foreach (var stat in customerRecordStats)
            {
                var firstRecord = stat.Records.FirstOrDefault();
                
                if (firstRecord != null)
                {
                    bool isAccessible = false;

                    if (firstRecord.RecordType == "來電")
                    {
                        // 第一筆記錄是"來電"
                        if (stat.RecordCount == 1)
                        {
                            // 只有一筆記錄且是"來電" → 所有人可見
                            isAccessible = true;
                        }
                        else
                        {
                            // 有多筆記錄且第一筆是"來電" → 依照後續記錄的HandledBy判斷
                            isAccessible = stat.Records.Any(cr => cr.HandledBy == currentUserId);
                        }
                    }
                    else
                    {
                        // 第一筆記錄不是"來電" → 只有該Customer資料的創建人能看見
                        var customer = allCustomers.FirstOrDefault(c => c.CustomerId == stat.CustomerId);
                        isAccessible = customer?.CreatedUserInfoId == currentUserId;
                    }

                    if (isAccessible)
                    {
                        accessibleCustomerIds.Add(stat.CustomerId);
                    }
                }
            }

            // 第五步：處理沒有記錄的客戶（只有客戶資料，沒有客戶記錄）
            var customersWithoutRecords = allCustomers
                .Where(c => !customerRecordStats.Any(s => s.CustomerId == c.CustomerId))
                .Where(c => c.CreatedUserInfoId == currentUserId) // 只有創建人能看見
                .Select(c => c.CustomerId)
                .ToList();

            accessibleCustomerIds.AddRange(customersWithoutRecords);

            // 第六步：基礎查詢 - 只包含當前用戶可以存取的客戶
            IQueryable<Customer> query;
            if (accessibleCustomerIds.Any())
            {
                query = Db.Customers.Where(c => accessibleCustomerIds.Contains(c.CustomerId));
            }
            else
            {
                // 如果沒有可存取的客戶，返回空查詢
                query = Db.Customers.Where(c => false);
            }

            // 第七步：執行投影到摘要 DTO
            var projectedQuery = query.Select(c => new CustomerListOutput
            {
                CustomerId = c.CustomerId.ToString(),
                Name = c.Name,
                PhoneNumber = c.PhoneNumber,
                Email = c.Email,
                FullAddress = $"{c.City}{c.District}{c.Address}",
                SiteCode = c.SiteCode,
                CreatedTime = c.CreatedTime,
            });

            // 第八步：執行分頁查詢
            var pagedResult = await projectedQuery
                .OrderByDescending(c => c.CreatedTime)
                .ToPagedListOutputAsync(input);

            return pagedResult;
        }

        /// <summary>
        /// 根據客戶ID取得單一客戶資訊
        /// </summary>
        /// <param name="customerId">客戶ID</param>
        /// <returns>客戶詳細資訊</returns>
        public async Task<CustomerOutput> GetCustomerByIdAsync(string customerId)
        {
            var customer = await Db.Customers
                .Include(c => c.CustomerRecords) // 加入 Include 載入關聯紀錄
                .Where(c => c.CustomerId == int.Parse(customerId))
                .Select(c => new CustomerOutput
                {
                    CustomerId = c.CustomerId.ToString(),
                    Name = c.Name,
                    Gender = c.Gender,
                    Birthday = c.Birthday,
                    City = c.City,
                    District = c.District,
                    Address = c.Address,
                    PhoneNumber = c.PhoneNumber,
                    Email = c.Email,
                    Occupation = c.Occupation,
                    LeadSource = c.LeadSource,
                    RequiredPingArea = c.RequiredPingArea,
                    RequiredLayout = c.RequiredLayout,
                    Budget = c.Budget,
                    PurchaseConditions = c.PurchaseConditions,
                    SiteCode = c.SiteCode,
                    ImagePath = !string.IsNullOrEmpty(c.WebPath) && !string.IsNullOrEmpty(_imageBaseUrl)
                                  ? (_imageBaseUrl + Path.GetFileName(c.WebPath)).Replace("\\", "/")
                                  : null,
                    CreatedTime = c.CreatedTime,
                    CreatedUserId = c.CreatedUserInfoId,
                    UpdatedTime = c.UpdatedTime,
                    UpdatedUserId = c.UpdatedUserInfoId,
                    // 映射訪談紀錄
                    CustomerRecords = c.CustomerRecords.Select(r => new CustomerRecordOutput
                    {
                        CustomerRecordId = r.CustomerRecordId,
                        RecordType = r.RecordType,
                        Notes = r.Notes,
                        RecordedAt = r.RecordedAt,
                        CustomerLevel = r.CustomerLevel,
                        HandledBy = r.HandledBy,
                        CreateUserInfoId = r.CreatedUserInfoId,
                        CreatedTime = r.CreatedTime
                    }).ToList()
                })
                .FirstOrDefaultAsync();

            if (customer == null)
            {
                return null;
            }

            // 查詢並填入建立者和更新者名稱
            var userIds = new[] { customer.CreatedUserId, customer.UpdatedUserId }
                .Where(id => !string.IsNullOrEmpty(id))
                .Distinct()
                .ToList();

            if (userIds.Any())
            {
                var users = await Db.UserInfos
                    .Where(u => userIds.Contains(u.UserInfoId))
                    .Select(u => new { u.UserInfoId, u.Name })
                    .ToListAsync();
                var userMap = users.ToDictionary(u => u.UserInfoId, u => u.Name);

                if (!string.IsNullOrEmpty(customer.CreatedUserId) && userMap.TryGetValue(customer.CreatedUserId, out var createdName))
                {
                    customer.CreatedUserName = createdName;
                }
                if (!string.IsNullOrEmpty(customer.UpdatedUserId) && userMap.TryGetValue(customer.UpdatedUserId, out var updatedName))
                {
                    customer.UpdatedUserName = updatedName;
                }
            }

            return customer;
        }

        /// <summary>
        /// 新增客戶
        /// </summary>
        /// <param name="input">新增客戶輸入資料</param>
        public async Task<string> CreateCustomerAsync(CustomerInput input)
        {
            ArgumentNullException.ThrowIfNull(input, nameof(input));

            var customer = new Customer
            {
                Name = input.Name,
                Gender = input.Gender,
                Birthday = input.Birthday,
                City = input.City,
                District = input.District,
                Address = input.Address,
                PhoneNumber = input.PhoneNumber,
                Email = input.Email,
                Occupation = input.Occupation,
                LeadSource = input.LeadSource,
                RequiredPingArea = input.RequiredPingArea,
                RequiredLayout = input.RequiredLayout,
                Budget = input.Budget,
                PurchaseConditions = input.PurchaseConditions,
                SiteCode = input.SiteCode,
                CreatedTime = DateTime.Now,
                CreatedUserInfoId = CurrentUser.UserId,
                UpdatedTime = DateTime.Now,
                UpdatedUserInfoId = CurrentUser.UserId
            };

            // 先儲存客戶資料以取得 CustomerId
            Db.Customers.Add(customer);
            await Db.SaveChangesAsync(); // 先儲存客戶以獲取 ID

            // 新增訪談紀錄
            if (input.CustomerRecords != null && input.CustomerRecords.Any())
            {
                foreach (var recordInput in input.CustomerRecords)
                {
                    var newRecord = new CustomerRecord
                    {
                        CustomerId = customer.CustomerId, // 關聯新客戶 ID
                        RecordType = recordInput.RecordType,
                        Notes = recordInput.Notes,
                        RecordedAt = recordInput.RecordedAt ?? DateTime.Now,
                        CustomerLevel = recordInput.CustomerLevel,
                        HandledBy = recordInput.HandledBy ?? CurrentUser.UserId, // 如果未提供，使用當前登入者 ID
                        CreatedTime = DateTime.Now,
                        CreatedUserInfoId = CurrentUser.UserId,
                        UpdatedTime = DateTime.Now,
                        UpdatedUserInfoId = CurrentUser.UserId
                    };
                    Db.CustomerRecords.Add(newRecord);
                }
                await Db.SaveChangesAsync(); // 儲存訪談紀錄
            }

            // 儲存圖片 (需要在儲存客戶資料之後，才能取得 customer.CustomerId)
            string imagePath = null;
            if (!string.IsNullOrEmpty(input.ImageBase64))
            {
                try
                {
                    // 使用 customer.CustomerId 來命名檔案
                    imagePath = await _imageStorageService.SaveImageAsync(input.ImageBase64, customer.CustomerId.ToString());
                    customer.WebPath = imagePath; // 更新圖片路徑
                    await Db.SaveChangesAsync(); // 再次儲存以更新 WebPath
                }
                catch (Exception ex)
                {
                    // 如果圖片儲存失敗，考慮是否要刪除剛剛建立的客戶資料和訪談紀錄，或記錄錯誤
                    Console.WriteLine($"為客戶 {customer.CustomerId} 儲存圖片時失敗: {ex.Message}");
                    // 可以選擇拋出異常，讓 Controller 層處理
                    // throw;
                }
            }

            return customer.CustomerId.ToString();
        }

        /// <summary>
        /// 更新客戶資訊
        /// </summary>
        /// <param name="customerId">客戶ID</param>
        /// <param name="input">更新客戶輸入資料</param>
        public async Task UpdateCustomerAsync(string customerId, CustomerInput input)
        {
            ArgumentNullException.ThrowIfNull(input, nameof(input));

            var customer = await Db.Customers.FirstOrDefaultAsync(c => c.CustomerId == int.Parse(customerId));
            if (customer == null)
            {
                throw new Exception($"找不到客戶 {customerId}");
            }

            // 更新基本資料
            customer.Name = input.Name;
            customer.Gender = input.Gender;
            customer.Birthday = input.Birthday;
            customer.City = input.City;
            customer.District = input.District;
            customer.Address = input.Address;
            customer.PhoneNumber = input.PhoneNumber;
            customer.Email = input.Email;
            customer.Occupation = input.Occupation;
            customer.LeadSource = input.LeadSource;
            customer.RequiredPingArea = input.RequiredPingArea;
            customer.RequiredLayout = input.RequiredLayout;
            customer.Budget = input.Budget;
            customer.PurchaseConditions = input.PurchaseConditions;
            customer.SiteCode = input.SiteCode;
            customer.UpdatedTime = DateTime.Now;
            customer.UpdatedUserInfoId = CurrentUser.UserId;

            // 處理訪談紀錄的新增與更新
            if (input.CustomerRecords != null)
            {
                foreach (var recordInput in input.CustomerRecords)
                {
                    if (recordInput.CustomerRecordId.HasValue && recordInput.CustomerRecordId > 0)
                    {
                        // 更新現有紀錄
                        var existingRecord = await Db.CustomerRecords
                            .FirstOrDefaultAsync(r => r.CustomerRecordId == recordInput.CustomerRecordId.Value && r.CustomerId == customer.CustomerId);

                        if (existingRecord != null)
                        {
                            existingRecord.RecordType = recordInput.RecordType;
                            existingRecord.Notes = recordInput.Notes;
                            existingRecord.RecordedAt = (recordInput.RecordedAt.HasValue ? recordInput.RecordedAt.Value.ToLocalTime() : existingRecord.RecordedAt);
                            existingRecord.CustomerLevel = recordInput.CustomerLevel;
                            existingRecord.HandledBy = recordInput.HandledBy ?? existingRecord.HandledBy;
                            existingRecord.UpdatedTime = DateTime.Now;
                            existingRecord.UpdatedUserInfoId = CurrentUser.UserId;
                            Db.CustomerRecords.Update(existingRecord);
                        }
                        else
                        {
                            // 處理找不到紀錄的錯誤，可能是 ID 錯誤或不屬於此客戶
                            Console.WriteLine($"警告：找不到要更新的訪談紀錄 ID {recordInput.CustomerRecordId} (客戶 ID: {customerId})");
                        }
                    }
                    else
                    {
                        // 新增紀錄
                        var newRecord = new CustomerRecord
                        {
                            CustomerId = customer.CustomerId, // 關聯客戶 ID
                            RecordType = recordInput.RecordType,
                            Notes = recordInput.Notes,
                            RecordedAt = (recordInput.RecordedAt.HasValue ? recordInput.RecordedAt.Value.ToLocalTime() : DateTime.Now),
                            CustomerLevel = recordInput.CustomerLevel,
                            HandledBy = recordInput.HandledBy ?? CurrentUser.UserId,
                            CreatedTime = DateTime.Now,
                            CreatedUserInfoId = CurrentUser.UserId,
                            UpdatedTime = DateTime.Now,
                            UpdatedUserInfoId = CurrentUser.UserId
                        };
                        Db.CustomerRecords.Add(newRecord);
                    }
                }

                // 處理刪除客戶訪談紀錄：刪除那些已存在但不在 input 列表中的紀錄
                var inputRecordIds = input.CustomerRecords
                    .Where(r => r.CustomerRecordId.HasValue)
                    .Select(r => r.CustomerRecordId.Value)
                    .ToList();
                var toDeleteRecords = await Db.CustomerRecords
                    .Where(r => r.CustomerId == customer.CustomerId && !inputRecordIds.Contains(r.CustomerRecordId))
                    .ToListAsync();
                if (toDeleteRecords.Any())
                {
                    Db.CustomerRecords.RemoveRange(toDeleteRecords);
                }
            }

            // 處理圖片更新
            if (!string.IsNullOrEmpty(input.ImageBase64))
            {
                string oldImagePath = customer.WebPath;
                try
                {
                    customer.WebPath = await _imageStorageService.SaveImageAsync(input.ImageBase64, customerId);
                    // 如果成功儲存新圖片，且有舊圖片，則刪除舊圖片
                    if (!string.IsNullOrEmpty(oldImagePath) && oldImagePath != customer.WebPath)
                    {
                        await _imageStorageService.DeleteImageAsync(oldImagePath);
                    }
                }
                catch (Exception ex)
                {
                    // 圖片儲存失敗，可以選擇恢復 WebPath 或記錄錯誤
                    customer.WebPath = oldImagePath; // 恢復舊路徑
                    Console.WriteLine($"為客戶 {customerId} 更新圖片時失敗: {ex.Message}");
                    // throw;
                }
            }
            // 如果 ImageBase64 為空，表示可能要刪除圖片 (根據您的業務邏輯決定)
            // else if (string.IsNullOrEmpty(input.ImageBase64) && !string.IsNullOrEmpty(customer.WebPath))
            // {
            //     await _imageStorageService.DeleteImageAsync(customer.WebPath);
            //     customer.WebPath = null;
            // }

            // 最後儲存所有變更 (包含基本資料、訪談紀錄的新增/更新/刪除)
            await Db.SaveChangesAsync();
        }

        /// <summary>
        /// 刪除客戶
        /// </summary>
        /// <param name="customerId">客戶ID</param>
        public async Task DeleteCustomerAsync(string customerId)
        {
            var customer = await Db.Customers.FirstOrDefaultAsync(c => c.CustomerId == int.Parse(customerId));
            if (customer == null)
            {
                throw new Exception($"找不到客戶 {customerId}");
            }

            // 檢查是否有相關記錄
            if (await Db.CustomerRecords.AnyAsync(cr => cr.CustomerId == customer.CustomerId))
            {
                throw new Exception($"客戶 {customerId} 已有相關記錄，無法刪除");
            }

            // 刪除客戶圖片
            if (!string.IsNullOrEmpty(customer.WebPath))
            {
                await _imageStorageService.DeleteImageAsync(customer.WebPath);
            }

            Db.Customers.Remove(customer);
            await Db.SaveChangesAsync();
        }

        /// <summary>
        /// 刪除特定客戶的單筆訪談紀錄
        /// </summary>
        /// <param name="customerId">客戶ID</param>
        /// <param name="recordId">訪談紀錄ID</param>
        public async Task DeleteCustomerRecordAsync(string customerId, int recordId)
        {
            if (!int.TryParse(customerId, out var custId))
                throw new Exception("客戶ID格式不正確");
            var record = await Db.CustomerRecords
                .FirstOrDefaultAsync(r => r.CustomerRecordId == recordId && r.CustomerId == custId);
            if (record == null)
                throw new Exception($"找不到訪談紀錄 {recordId} (客戶: {customerId})");
            Db.CustomerRecords.Remove(record);
            await Db.SaveChangesAsync();
        }
    }
} 
