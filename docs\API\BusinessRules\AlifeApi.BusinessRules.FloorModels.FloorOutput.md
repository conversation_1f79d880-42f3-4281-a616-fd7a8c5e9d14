#### [AlifeApi.BusinessRules](index.md 'index')
### [AlifeApi.BusinessRules.FloorModels](AlifeApi.BusinessRules.FloorModels.md 'AlifeApi.BusinessRules.FloorModels')

## FloorOutput Class

樓層詳細輸出模型

```csharp
public class FloorOutput
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; FloorOutput
### Properties

<a name='AlifeApi.BusinessRules.FloorModels.FloorOutput.BuildingId'></a>

## FloorOutput.BuildingId Property

建築物ID

```csharp
public int BuildingId { get; set; }
```

#### Property Value
[System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')

<a name='AlifeApi.BusinessRules.FloorModels.FloorOutput.CreatedTime'></a>

## FloorOutput.CreatedTime Property

建立時間

```csharp
public System.DateTime CreatedTime { get; set; }
```

#### Property Value
[System.DateTime](https://docs.microsoft.com/en-us/dotnet/api/System.DateTime 'System.DateTime')

<a name='AlifeApi.BusinessRules.FloorModels.FloorOutput.CreatedUserInfoId'></a>

## FloorOutput.CreatedUserInfoId Property

建立者 UserInfoId

```csharp
public string CreatedUserInfoId { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.FloorModels.FloorOutput.CreatedUserName'></a>

## FloorOutput.CreatedUserName Property

建立者名稱 (選填)

```csharp
public string? CreatedUserName { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.FloorModels.FloorOutput.FloorHeight'></a>

## FloorOutput.FloorHeight Property

樓層高度

```csharp
public System.Nullable<decimal> FloorHeight { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.Decimal](https://docs.microsoft.com/en-us/dotnet/api/System.Decimal 'System.Decimal')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

<a name='AlifeApi.BusinessRules.FloorModels.FloorOutput.FloorId'></a>

## FloorOutput.FloorId Property

樓層唯一識別碼

```csharp
public int FloorId { get; set; }
```

#### Property Value
[System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')

<a name='AlifeApi.BusinessRules.FloorModels.FloorOutput.FloorLabel'></a>

## FloorOutput.FloorLabel Property

樓層標示

```csharp
public string FloorLabel { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.FloorModels.FloorOutput.FloorLevel'></a>

## FloorOutput.FloorLevel Property

樓層數值

```csharp
public int FloorLevel { get; set; }
```

#### Property Value
[System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')

<a name='AlifeApi.BusinessRules.FloorModels.FloorOutput.FloorType'></a>

## FloorOutput.FloorType Property

樓層類型

```csharp
public string FloorType { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.FloorModels.FloorOutput.Remarks'></a>

## FloorOutput.Remarks Property

備註

```csharp
public string? Remarks { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.FloorModels.FloorOutput.SiteCode'></a>

## FloorOutput.SiteCode Property

案場代碼

```csharp
public string SiteCode { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.FloorModels.FloorOutput.UpdatedTime'></a>

## FloorOutput.UpdatedTime Property

更新時間

```csharp
public System.DateTime UpdatedTime { get; set; }
```

#### Property Value
[System.DateTime](https://docs.microsoft.com/en-us/dotnet/api/System.DateTime 'System.DateTime')

<a name='AlifeApi.BusinessRules.FloorModels.FloorOutput.UpdatedUserInfoId'></a>

## FloorOutput.UpdatedUserInfoId Property

更新者 UserInfoId

```csharp
public string UpdatedUserInfoId { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.FloorModels.FloorOutput.UpdatedUserName'></a>

## FloorOutput.UpdatedUserName Property

更新者名稱 (選填)

```csharp
public string? UpdatedUserName { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')