#### [AlifeApi.DataAccess](index.md 'index')
### [AlifeApi.DataAccess.ProjectContext](AlifeApi.DataAccess.ProjectContext.md 'AlifeApi.DataAccess.ProjectContext')

## SysBulletinsAttach Class

```csharp
public class SysBulletinsAttach
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; SysBulletinsAttach
### Properties

<a name='AlifeApi.DataAccess.ProjectContext.SysBulletinsAttach.BlaCrDatetime'></a>

## SysBulletinsAttach.BlaCrDatetime Property

建立時間

```csharp
public System.Nullable<System.DateTime> BlaCrDatetime { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.DateTime](https://docs.microsoft.com/en-us/dotnet/api/System.DateTime 'System.DateTime')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

<a name='AlifeApi.DataAccess.ProjectContext.SysBulletinsAttach.BlaCrUser'></a>

## SysBulletinsAttach.BlaCrUser Property

建立者

```csharp
public string BlaCrUser { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContext.SysBulletinsAttach.BlaFile'></a>

## SysBulletinsAttach.BlaFile Property

檔案

```csharp
public byte[] BlaFile { get; set; }
```

#### Property Value
[System.Byte](https://docs.microsoft.com/en-us/dotnet/api/System.Byte 'System.Byte')[[]](https://docs.microsoft.com/en-us/dotnet/api/System.Array 'System.Array')

<a name='AlifeApi.DataAccess.ProjectContext.SysBulletinsAttach.BlaFileType'></a>

## SysBulletinsAttach.BlaFileType Property

檔案類型

```csharp
public string BlaFileType { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContext.SysBulletinsAttach.BlaId'></a>

## SysBulletinsAttach.BlaId Property

公告附檔流水號

```csharp
public int BlaId { get; set; }
```

#### Property Value
[System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')

<a name='AlifeApi.DataAccess.ProjectContext.SysBulletinsAttach.BlaOname'></a>

## SysBulletinsAttach.BlaOname Property

檔案原名

```csharp
public string BlaOname { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContext.SysBulletinsAttach.BlId'></a>

## SysBulletinsAttach.BlId Property

公告流水號

```csharp
public int BlId { get; set; }
```

#### Property Value
[System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')