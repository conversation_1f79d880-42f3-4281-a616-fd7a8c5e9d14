﻿//using AlifeApi.BusinessRules.Infrastructure;
//using AlifeApi.DataAccess.ProjectContext;
//using Microsoft.EntityFrameworkCore;
//using Microsoft.EntityFrameworkCore.Internal;

//namespace AlifeApi.BusinessRules.ProblemReportModels
//{
//    /// <summary>
//    /// 問題回報
//    /// </summary>
//    public class ProblemReportService : ServiceBase<ProjectContext>
//    {
//        /// <summary>
//        /// Initializes a new instance of the <see cref="ProblemReportService"/> class.
//        /// </summary>
//        /// <param name="serviceProvider">The service provider.</param>
//        /// <param name="dbContext">The database context.</param>
//        public ProblemReportService(IServiceProvider serviceProvider, ProjectContext dbContext) : base(serviceProvider, dbContext)
//        {
//        }

//        /// <summary>
//        /// 新增問題回報
//        /// </summary>
//        /// <param name="input">The input.</param>
//        /// <exception cref="ArgumentNullException">input</exception>
//        public async Task CreateUserReportAsync(UserReportCreateInput input)
//        {
//            ArgumentNullException.ThrowIfNull(input, nameof(input));

//            Db.SysProblemReport.Add(new SysProblemReport
//            {
//                DeptId = CurrentUser.DeptId,
//                System = CurrentUser.System,
//                Menu = input.Menu,
//                Subject = input.Subject,
//                ProblemContent = input.ReportContent,
//                ProcessStatus = "1",
//                ProblemType = input.ProblemType,
//                ReplyContent = "",
//                CreatedUserId = CurrentUser.UserId,
//                CreatedTime = DateTime.Now,
//                UpdatedUserId = CurrentUser.UserId,
//                UpdatedTime = DateTime.Now,
//            });

//            await Db.SaveChangesAsync();
//        }

//        /// <summary>
//        /// 回復問題回報
//        /// </summary>
//        /// <param name="input">The input.</param>
//        /// <exception cref="ArgumentNullException">input</exception>
//        public async Task ReplyReportAsync(ReportReplyInput input)
//        {
//            ArgumentNullException.ThrowIfNull(input, nameof(input));

//            SysProblemReport entity = await Db.SysProblemReport.SingleAsync(x => x.Id == input.Id);
//            entity.ProblemType = input.ProblemType;
//            entity.ProcessStatus = input.ProcessStatus;
//            entity.ReplyContent = input.ReplyContent;
//            entity.ReplyUserId = input.ReplyUserId;
//            entity.ReplyTime = input.ReplyTime;
//            entity.UpdatedUserId = CurrentUser.UserId;
//            entity.UpdatedTime = DateTime.Now;

//            await Db.SaveChangesAsync();
//        }

//        /// <summary>
//        /// 取得問題回報管理清單
//        /// </summary>
//        /// <param name="input">The input.</param>
//        /// <returns>問題回報管理清單</returns>
//        public async Task<PagedListOutput<ProblemReportListItemGetOutput>> GetProblemReportListAsync(ProblemReportListGetInput input)
//        {
//            ArgumentNullException.ThrowIfNull(input, nameof(input));

//            return await Db.SysProblemReport.AsNoTracking()
//                .WhereIf(input.QueryStartDate.HasValue, x => x.CreatedTime >= input.QueryStartDate)
//                .WhereIf(input.QueryEndDate.HasValue, () =>
//                {
//                    DateTime nextForEndDate = input.QueryEndDate.Value.AddDays(1);
//                    return x => x.CreatedTime < nextForEndDate;
//                })
//                .LeftJoin(Db.UserInfo, x => x.CreatedUserId, x => x.Id, (p, cu) => new { ProblemReport = p, CreatedUserName = cu.Name })
//                .Join(
//                    Db.SysCode, x => x.ProblemReport.ProcessStatus, x => x.Code,
//                    (p, ss) => new { p.ProblemReport, p.CreatedUserName, ProcessStatusName = ss.CodeDesc }
//                )
//                .Join(
//                    Db.SysCode, x => x.ProblemReport.ProblemType, x => x.Code,
//                    (p, ts) => new { p.ProblemReport, p.CreatedUserName, p.ProcessStatusName, ProblemTypeName = ts.CodeDesc })
//                .Select(x => new ProblemReportListItemGetOutput
//                {
//                    Id = x.ProblemReport.Id,
//                    CreatedTime = x.ProblemReport.CreatedTime,
//                    CreatedUserId = x.ProblemReport.CreatedUserId,
//                    CreatedUserName = x.CreatedUserName ?? "",
//                    DeptId = x.ProblemReport.DeptId,
//                    DeptName = x.ProblemReport.Dept.DeptName,
//                    Menu = x.ProblemReport.Menu,
//                    Subject = x.ProblemReport.Subject,
//                    ReportContent = x.ProblemReport.ProblemContent ?? "",
//                    ProcessStatus = x.ProblemReport.ProcessStatus,
//                    ProcessStatusName = x.ProcessStatusName,
//                    ProblemType = x.ProblemReport.ProblemType,
//                    ProblemTypeName = x.ProblemTypeName,
//                    ReplyContent = x.ProblemReport.ReplyContent ?? "",
//                    ReplyUserId = x.ProblemReport.ReplyUserId ?? "",
//                    ReplyUserName = x.ProblemReport.ReplyUser.Name ?? "'",
//                    ReplyTime = x.ProblemReport.ReplyTime
//                })
//                .ToPagedListOutputAsync(input);
//        }
//    }
//}
