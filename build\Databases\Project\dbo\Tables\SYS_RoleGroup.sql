﻿CREATE TABLE [dbo].[SYS_RoleGroup] (
    [System]        VARCHAR (30)  NOT NULL,
    [RoleGroupId]   VARCHAR (50)  NOT NULL,
    [Name]          NVARCHAR (50) NOT NULL,
    [IsAdmin]       BIT           NOT NULL,
    [CreatedUserId] VARCHAR (15)  NOT NULL,
    [CreatedTime]   DATETIME      NOT NULL,
    [UpdatedUserId] VARCHAR (15)  NOT NULL,
    [UpdatedTime]   DATETIME      NOT NULL,
    CONSTRAINT [PK_SYS_RoleGroup_1] PRIMARY KEY CLUSTERED ([System] ASC, [RoleGroupId] ASC)
);


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'角色群組資料', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'SYS_RoleGroup';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'系統名稱', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'SYS_RoleGroup', @level2type = N'COLUMN', @level2name = N'System';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'角色群組識別編號', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'SYS_RoleGroup', @level2type = N'COLUMN', @level2name = N'RoleGroupId';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'角色群組名稱', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'SYS_RoleGroup', @level2type = N'COLUMN', @level2name = N'Name';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'是否為管理者', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'SYS_RoleGroup', @level2type = N'COLUMN', @level2name = N'IsAdmin';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'建立時間', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'SYS_RoleGroup', @level2type = N'COLUMN', @level2name = N'CreatedUserId';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'建立者', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'SYS_RoleGroup', @level2type = N'COLUMN', @level2name = N'CreatedTime';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'更新時間', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'SYS_RoleGroup', @level2type = N'COLUMN', @level2name = N'UpdatedUserId';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'更新者', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'SYS_RoleGroup', @level2type = N'COLUMN', @level2name = N'UpdatedTime';

