﻿using System.ComponentModel.DataAnnotations;

namespace AlifeApi.BusinessRules.SysCodeModels
{
    /// <summary>
    /// 新增 SysCode 代碼
    /// </summary>
    public class SysCodeCreateInput
    {
        /// <summary>
        /// 單位代碼類型
        /// </summary>
        [Required]
        [MaxLength(30)]
        public string Type { get; set; }

        /// <summary>
        /// 單位代碼名稱
        /// </summary>
        [Required]
        [MaxLength(300)]
        public string Desc { get; set; }

        /// <summary>
        /// 單位代碼類型
        /// </summary>
        [MaxLength(30)]
        public string ParentCode { get; set; }
    }
}
