using System;

namespace AlifeApi.BusinessRules.PaymentRecordModels
{
    /// <summary>
    /// 收款記錄列表輸出項目
    /// </summary>
    public class PaymentRecordListOutput
    {
        /// <summary>
        /// 收款紀錄唯一識別碼
        /// </summary>
        public int PaymentRecordId { get; set; }

        /// <summary>
        /// 訂單ID
        /// </summary>
        public int OrderId { get; set; }

        /// <summary>
        /// 收款日期
        /// </summary>
        public DateOnly PaymentDate { get; set; }

        /// <summary>
        /// 收款金額
        /// </summary>
        public decimal Amount { get; set; }

        /// <summary>
        /// 款別
        /// </summary>
        public string PaymentType { get; set; } = null!;

        /// <summary>
        /// 收款方式
        /// </summary>
        public string PaymentMethod { get; set; } = null!;

        /// <summary>
        /// 備註
        /// </summary>
        public string? Remarks { get; set; }

        /// <summary>
        /// 建立時間
        /// </summary>
        public DateTime CreatedTime { get; set; }

        /// <summary>
        /// 建立者 UserInfoId
        /// </summary>
        public string CreatedUserInfoId { get; set; } = null!;

        /// <summary>
        /// 建立者名稱 (選填)
        /// </summary>
        public string? CreatedUserName { get; set; }
    }
} 
