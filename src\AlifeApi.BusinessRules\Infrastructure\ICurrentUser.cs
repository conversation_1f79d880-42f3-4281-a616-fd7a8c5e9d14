﻿namespace AlifeApi.BusinessRules.Infrastructure
{
    /// <summary>
    /// 表示當前使用者的介面
    /// </summary>
    public interface ICurrentUser
    {
        /// <summary>
        /// 使用者 ID
        /// </summary>
        public string UserId { get; }

        /// <summary>
        /// 部門 ID
        /// </summary>
        public string? DeptId { get; }

        /// <summary>
        /// 職別
        /// </summary>
        public string? GradeCode { get; }

        /// <summary>
        /// 系統
        /// </summary>
        public string System { get; }

        /// <summary>
        /// Gets or sets the ip address.
        /// </summary>
        public string IPAddress { get; }

        /// <summary>
        /// 角色 ID 的集合
        /// </summary>
        public IEnumerable<string> RoleIds { get; }
    }
}
