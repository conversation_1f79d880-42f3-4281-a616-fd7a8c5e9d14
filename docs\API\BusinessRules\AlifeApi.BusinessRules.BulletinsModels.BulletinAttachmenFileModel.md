#### [AlifeApi.BusinessRules](index.md 'index')
### [AlifeApi.BusinessRules.BulletinsModels](AlifeApi.BusinessRules.BulletinsModels.md 'AlifeApi.BusinessRules.BulletinsModels')

## BulletinAttachmenFileModel Class

```csharp
public class BulletinAttachmenFileModel
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; BulletinAttachmenFileModel
### Properties

<a name='AlifeApi.BusinessRules.BulletinsModels.BulletinAttachmenFileModel.BLA_Id'></a>

## BulletinAttachmenFileModel.BLA_Id Property

公告附件唯一識別值

```csharp
public int BLA_Id { get; set; }
```

#### Property Value
[System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')

<a name='AlifeApi.BusinessRules.BulletinsModels.BulletinAttachmenFileModel.File'></a>

## BulletinAttachmenFileModel.File Property

附件

```csharp
public byte[] File { get; set; }
```

#### Property Value
[System.Byte](https://docs.microsoft.com/en-us/dotnet/api/System.Byte 'System.Byte')[[]](https://docs.microsoft.com/en-us/dotnet/api/System.Array 'System.Array')

<a name='AlifeApi.BusinessRules.BulletinsModels.BulletinAttachmenFileModel.FileName'></a>

## BulletinAttachmenFileModel.FileName Property

公告附件檔案名稱(須附副檔名)

```csharp
public string FileName { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')