using AlifeApi.BusinessRules.Infrastructure;
using Microsoft.AspNetCore.Http;
using System;
using System.Collections.Generic;

namespace AlifeApi.BusinessRules.SupplierModels
{
    public class SupplierFileInput
    {
        public int SupplierFileId { get; set; } // 新增此ID，0代表新檔案
        public string FormType { get; set; }
        public string FileName { get; set; } // Original client file name
        public string FilePath { get; set; } // Client-side path / identifier or original filename, usage may vary. Service will determine final server path.
        public string Remark { get; set; }
        public string Agent { get; set; }
    }

    public class SupplierCreateInput
    {
        public string Name { get; set; }
        public string PersonType { get; set; }
        public string ContactPerson { get; set; }
        public string IdType { get; set; }
        public string IdNumber { get; set; }
        public string CompanyPhone { get; set; }
        public string Address { get; set; }
        public string ContactName { get; set; }
        public string ContactPhone1 { get; set; }
        public string ContactPhone2 { get; set; }
        public string Email { get; set; }
        public string BankName { get; set; }
        public string BankBranch { get; set; }
        public string AccountName { get; set; }
        /// <summary>
        /// List of metadata for files. Should correspond to ActualFiles.
        /// </summary>
        public List<SupplierFileInput> Files { get; set; } 
        /// <summary>
        /// The actual file uploads.
        /// </summary>
        public List<IFormFile> ActualFiles { get; set; }
    }

    public class SupplierUpdateInput
    {
        public string Name { get; set; }
        public string PersonType { get; set; }
        public string ContactPerson { get; set; }
        public string IdType { get; set; }
        public string IdNumber { get; set; }
        public string CompanyPhone { get; set; }
        public string Address { get; set; }
        public string ContactName { get; set; }
        public string ContactPhone1 { get; set; }
        public string ContactPhone2 { get; set; }
        public string Email { get; set; }
        public string BankName { get; set; }
        public string BankBranch { get; set; }
        public string AccountName { get; set; }
        /// <summary>
        /// List of metadata for new/updated files. Should correspond to ActualFiles.
        /// </summary>
        public List<SupplierFileInput> Files { get; set; }
        /// <summary>
        /// The actual new/updated file uploads.
        /// </summary>
        public List<IFormFile> ActualFiles { get; set; }
    }

    public class SupplierQueryInput : PagedListInput
    {
        public string Name { get; set; }
        public string PersonType { get; set; }
        public string IdNumber { get; set; }
        public string ContactName { get; set; }
        public string ContactPhone { get; set; }
    }

    public class SupplierListOutput
    {
        public int SupplierId { get; set; }
        public string Name { get; set; }
        public string PersonType { get; set; }
        public string ContactPerson { get; set; }
        public string IdNumber { get; set; }
        public string CompanyPhone { get; set; }
        public string ContactName { get; set; }
        public string ContactPhone1 { get; set; }
        public int FileCount { get; set; }
        public DateTime UpdatedTime { get; set; }
        public string UpdatedUserName { get; set; }
    }

    public class SupplierFileOutput
    {
        public int SupplierFileId { get; set; }
        public int SupplierId { get; set; }
        public string FormType { get; set; }
        public string FileName { get; set; } // Server-side file name (usually original file name)
        public string FilePath { get; set; } // Server-side full path
        public string Remark { get; set; }
        public string Agent { get; set; }
        public string CreatedUserInfoId { get; set; }
        public string CreatedUserName { get; set; }
        public string UpdatedUserInfoId { get; set; }
        public string UpdatedUserName { get; set; }
        public DateTime CreatedTime { get; set; }
        public DateTime UpdatedTime { get; set; }
    }

    public class SupplierOutput
    {
        public int SupplierId { get; set; }
        public string Name { get; set; }
        public string PersonType { get; set; }
        public string ContactPerson { get; set; }
        public string IdType { get; set; }
        public string IdNumber { get; set; }
        public string CompanyPhone { get; set; }
        public string Address { get; set; }
        public string ContactName { get; set; }
        public string ContactPhone1 { get; set; }
        public string ContactPhone2 { get; set; }
        public string Email { get; set; }
        public string BankName { get; set; }
        public string BankBranch { get; set; }
        public string AccountName { get; set; }
        public string CreatedUserInfoId { get; set; }
        public string CreatedUserName { get; set; }
        public string UpdatedUserInfoId { get; set; }
        public string UpdatedUserName { get; set; }
        public DateTime CreatedTime { get; set; }
        public DateTime UpdatedTime { get; set; }
        public List<SupplierFileOutput> Files { get; set; }
    }
} 
