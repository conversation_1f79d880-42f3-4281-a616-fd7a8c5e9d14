#### [AlifeApi.BusinessRules](index.md 'index')
### [AlifeApi.BusinessRules.Infrastructure](AlifeApi.BusinessRules.Infrastructure.md 'AlifeApi.BusinessRules.Infrastructure')

## PagedListInput Class

分頁列表輸入

```csharp
public abstract class PagedListInput
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; PagedListInput

Derived  
&#8627; [BuildingQueryInput](AlifeApi.BusinessRules.BuildingModels.BuildingQueryInput.md 'AlifeApi.BusinessRules.BuildingModels.BuildingQueryInput')  
&#8627; [LargeCategoryQueryInput](AlifeApi.BusinessRules.CategoryModels.LargeCategoryQueryInput.md 'AlifeApi.BusinessRules.CategoryModels.LargeCategoryQueryInput')  
&#8627; [MediumCategoryQueryInput](AlifeApi.BusinessRules.CategoryModels.MediumCategoryQueryInput.md 'AlifeApi.BusinessRules.CategoryModels.MediumCategoryQueryInput')  
&#8627; [SmallCategoryQueryInput](AlifeApi.BusinessRules.CategoryModels.SmallCategoryQueryInput.md 'AlifeApi.BusinessRules.CategoryModels.SmallCategoryQueryInput')  
&#8627; [UnifiedSalesQueryInput](AlifeApi.BusinessRules.CommonModels.UnifiedSalesQueryInput.md 'AlifeApi.BusinessRules.CommonModels.UnifiedSalesQueryInput')  
&#8627; [CrmOptionQueryInput](AlifeApi.BusinessRules.CrmOptionModels.CrmOptionQueryInput.md 'AlifeApi.BusinessRules.CrmOptionModels.CrmOptionQueryInput')  
&#8627; [CustomerQueryInput](AlifeApi.BusinessRules.CustomerModels.CustomerQueryInput.md 'AlifeApi.BusinessRules.CustomerModels.CustomerQueryInput')  
&#8627; [DepartmentListGetInput](AlifeApi.BusinessRules.DepartmentModels.DepartmentListGetInput.md 'AlifeApi.BusinessRules.DepartmentModels.DepartmentListGetInput')  
&#8627; [FloorQueryInput](AlifeApi.BusinessRules.FloorModels.FloorQueryInput.md 'AlifeApi.BusinessRules.FloorModels.FloorQueryInput')  
&#8627; [PagedListOutput&lt;TDetail&gt;](AlifeApi.BusinessRules.Infrastructure.PagedListOutput_TDetail_.md 'AlifeApi.BusinessRules.Infrastructure.PagedListOutput<TDetail>')  
&#8627; [LogAuditDetailsGetInput](AlifeApi.BusinessRules.LogAuditModels.LogAuditDetailsGetInput.md 'AlifeApi.BusinessRules.LogAuditModels.LogAuditDetailsGetInput')  
&#8627; [LogAuditMonthStatisticsGetInput](AlifeApi.BusinessRules.LogAuditModels.LogAuditMonthStatisticsGetInput.md 'AlifeApi.BusinessRules.LogAuditModels.LogAuditMonthStatisticsGetInput')  
&#8627; [OwnerListGetInput](AlifeApi.BusinessRules.OwnerModels.OwnerListGetInput.md 'AlifeApi.BusinessRules.OwnerModels.OwnerListGetInput')  
&#8627; [ParkingSpaceQueryInput](AlifeApi.BusinessRules.ParkingSpaceModels.ParkingSpaceQueryInput.md 'AlifeApi.BusinessRules.ParkingSpaceModels.ParkingSpaceQueryInput')  
&#8627; [PaymentRecordQueryInput](AlifeApi.BusinessRules.PaymentRecordModels.PaymentRecordQueryInput.md 'AlifeApi.BusinessRules.PaymentRecordModels.PaymentRecordQueryInput')  
&#8627; [ProblemReportListGetInput](AlifeApi.BusinessRules.ProblemReportModels.ProblemReportListGetInput.md 'AlifeApi.BusinessRules.ProblemReportModels.ProblemReportListGetInput')  
&#8627; [ProjectVersionListCondition](AlifeApi.BusinessRules.ProjectVersionModels.ProjectVersionListCondition.md 'AlifeApi.BusinessRules.ProjectVersionModels.ProjectVersionListCondition')  
&#8627; [PurchaseOrderQueryInput](AlifeApi.BusinessRules.PurchaseOrderModels.PurchaseOrderQueryInput.md 'AlifeApi.BusinessRules.PurchaseOrderModels.PurchaseOrderQueryInput')  
&#8627; [ReviewTaskListInput](AlifeApi.BusinessRules.ReviewModels.ReviewTaskListInput.md 'AlifeApi.BusinessRules.ReviewModels.ReviewTaskListInput')  
&#8627; [RoleGroupListGetInput](AlifeApi.BusinessRules.RoleGroupModels.RoleGroupListGetInput.md 'AlifeApi.BusinessRules.RoleGroupModels.RoleGroupListGetInput')  
&#8627; [RoleGroupListGetInputPg](AlifeApi.BusinessRules.RoleGroupModels.RoleGroupListGetInputPg.md 'AlifeApi.BusinessRules.RoleGroupModels.RoleGroupListGetInputPg')  
&#8627; [SiteListGetInput](AlifeApi.BusinessRules.SiteModels.SiteListGetInput.md 'AlifeApi.BusinessRules.SiteModels.SiteListGetInput')  
&#8627; [SysCodeByTypeGetInput](AlifeApi.BusinessRules.SysCodeModels.SysCodeByTypeGetInput.md 'AlifeApi.BusinessRules.SysCodeModels.SysCodeByTypeGetInput')  
&#8627; [SysTypeGetInput](AlifeApi.BusinessRules.SysCodeModels.SysTypeGetInput.md 'AlifeApi.BusinessRules.SysCodeModels.SysTypeGetInput')  
&#8627; [UnitQueryInput](AlifeApi.BusinessRules.UnitModels.UnitQueryInput.md 'AlifeApi.BusinessRules.UnitModels.UnitQueryInput')
### Properties

<a name='AlifeApi.BusinessRules.Infrastructure.PagedListInput.NumberOfPerPage'></a>

## PagedListInput.NumberOfPerPage Property

每頁筆數

```csharp
public int NumberOfPerPage { get; set; }
```

#### Property Value
[System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')

<a name='AlifeApi.BusinessRules.Infrastructure.PagedListInput.PageIndex'></a>

## PagedListInput.PageIndex Property

頁次索引

```csharp
public int PageIndex { get; set; }
```

#### Property Value
[System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')

<a name='AlifeApi.BusinessRules.Infrastructure.PagedListInput.SearchTermInfos'></a>

## PagedListInput.SearchTermInfos Property

過濾資訊

```csharp
public System.Collections.Generic.IEnumerable<AlifeApi.BusinessRules.Infrastructure.SearchTermInfo> SearchTermInfos { get; set; }
```

#### Property Value
[System.Collections.Generic.IEnumerable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Collections.Generic.IEnumerable-1 'System.Collections.Generic.IEnumerable`1')[SearchTermInfo](AlifeApi.BusinessRules.Infrastructure.SearchTermInfo.md 'AlifeApi.BusinessRules.Infrastructure.SearchTermInfo')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Collections.Generic.IEnumerable-1 'System.Collections.Generic.IEnumerable`1')

<a name='AlifeApi.BusinessRules.Infrastructure.PagedListInput.SortOrderInfos'></a>

## PagedListInput.SortOrderInfos Property

排序資訊

```csharp
public System.Collections.Generic.IEnumerable<AlifeApi.BusinessRules.Infrastructure.SortOrderInfo> SortOrderInfos { get; set; }
```

#### Property Value
[System.Collections.Generic.IEnumerable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Collections.Generic.IEnumerable-1 'System.Collections.Generic.IEnumerable`1')[SortOrderInfo](AlifeApi.BusinessRules.Infrastructure.SortOrderInfo.md 'AlifeApi.BusinessRules.Infrastructure.SortOrderInfo')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Collections.Generic.IEnumerable-1 'System.Collections.Generic.IEnumerable`1')

<a name='AlifeApi.BusinessRules.Infrastructure.PagedListInput.UsingPaging'></a>

## PagedListInput.UsingPaging Property

使用分頁模式

```csharp
public bool UsingPaging { get; set; }
```

#### Property Value
[System.Boolean](https://docs.microsoft.com/en-us/dotnet/api/System.Boolean 'System.Boolean')