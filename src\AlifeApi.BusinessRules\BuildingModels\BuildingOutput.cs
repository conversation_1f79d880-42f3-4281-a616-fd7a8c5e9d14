using System;

namespace AlifeApi.BusinessRules.BuildingModels
{
    /// <summary>
    /// 建築物詳細輸出模型
    /// </summary>
    public class BuildingOutput
    {
        /// <summary>
        /// 建築物唯一識別碼
        /// </summary>
        public int BuildingId { get; set; }

        /// <summary>
        /// 案場代碼
        /// </summary>
        public string SiteCode { get; set; } = null!;

        /// <summary>
        /// 建築物名稱
        /// </summary>
        public string BuildingName { get; set; } = null!;

        /// <summary>
        /// 地上樓層總數
        /// </summary>
        public int? TotalAboveGroundFloors { get; set; }

        /// <summary>
        /// 地下樓層總數
        /// </summary>
        public int? TotalBelowGroundFloors { get; set; }

        /// <summary>
        /// 建築物類型
        /// </summary>
        public string? BuildingType { get; set; }

        /// <summary>
        /// 完工日期
        /// </summary>
        public DateOnly? CompletionDate { get; set; }

        /// <summary>
        /// 備註
        /// </summary>
        public string? Remarks { get; set; }

        /// <summary>
        /// 建立時間
        /// </summary>
        public DateTime CreatedTime { get; set; }

        /// <summary>
        /// 更新時間
        /// </summary>
        public DateTime UpdatedTime { get; set; }

        /// <summary>
        /// 建立者 UserInfoId
        /// </summary>
        public string CreatedUserInfoId { get; set; } = null!;

        /// <summary>
        /// 更新者 UserInfoId
        /// </summary>
        public string UpdatedUserInfoId { get; set; } = null!;

        /// <summary>
        /// 建立者名稱 (選填)
        /// </summary>
        public string? CreatedUserName { get; set; }

        /// <summary>
        /// 更新者名稱 (選填)
        /// </summary>
        public string? UpdatedUserName { get; set; }
    }
} 
