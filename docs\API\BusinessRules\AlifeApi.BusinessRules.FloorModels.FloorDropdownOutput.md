#### [AlifeApi.BusinessRules](index.md 'index')
### [AlifeApi.BusinessRules.FloorModels](AlifeApi.BusinessRules.FloorModels.md 'AlifeApi.BusinessRules.FloorModels')

## FloorDropdownOutput Class

樓層下拉選單輸出資料

```csharp
public class FloorDropdownOutput
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; FloorDropdownOutput
### Properties

<a name='AlifeApi.BusinessRules.FloorModels.FloorDropdownOutput.Name'></a>

## FloorDropdownOutput.Name Property

名稱 (樓層標籤)

```csharp
public string Name { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.FloorModels.FloorDropdownOutput.Value'></a>

## FloorDropdownOutput.Value Property

值 (樓層ID)

```csharp
public int Value { get; set; }
```

#### Property Value
[System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')