﻿using AlifeApi.BusinessRules.Infrastructure;
using AlifeApi.BusinessRules.ProjectVersionModels;
using Microsoft.AspNetCore.Mvc;

namespace AlifeApi.WebApi.Controllers
{
    /// <summary>
    /// 系統版本號作業
    /// </summary>
    public class ProjectVersionController : AuthenticatedController
    {
        //private readonly ProjectVersionService _projectService;


        ///// <summary>
        ///// Initializes a new instance of the <see cref="RoleGroupController"/> class.
        ///// </summary>
        ///// <param name="projectService">The role group service.</param>
        ///// <exception cref="ArgumentNullException">roleGroupService</exception>
        //public ProjectVersionController(ProjectVersionService projectService)
        //{
        //    _projectService = projectService ?? throw new ArgumentNullException(nameof(projectService));
        //}

        ///// <summary>
        ///// 根據專案名稱取得版本號
        ///// </summary>
        ///// <param name="input">The input.</param>
        ///// <returns>系統版本號作業</returns>
        //[HttpPost]
        //public async Task<ProjectVersionOutput> GetProjectVersionAsync(ProjectVersionBase input)
        //    => await _projectService.GetProjectVersion(input);

        ///// <summary>
        ///// 同步專案版本號
        ///// </summary>
        ///// <param name="input">The input.</param>
        ///// <returns>系統版本號作業</returns>
        //[HttpPost]
        //public async Task SyncProjectVersionAsync(SyncProjectVersion input)
        //    => await _projectService.SyncProjectVersion(input);

        ///// <summary>
        ///// 取得所有版號
        ///// </summary>
        ///// <param name="input">The input.</param>
        ///// <returns>系統版本號作業</returns>
        //[HttpPost]
        //public async Task<PagedListOutput<ProjectVersionListResult>> GetProjectVersionListAsync(ProjectVersionListCondition input)
        //    => await _projectService.GetProjectVersionList(input);
    }
}
