#### [AlifeApi.WebApi](index.md 'index')
### [AlifeApi.WebApi.Controllers](AlifeApi.WebApi.Controllers.md 'AlifeApi.WebApi.Controllers')

## PaymentRecordsController Class

收款記錄管理

```csharp
public class PaymentRecordsController : AlifeApi.WebApi.Controllers.AuthenticatedController
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; [Microsoft.AspNetCore.Mvc.ControllerBase](https://docs.microsoft.com/en-us/dotnet/api/Microsoft.AspNetCore.Mvc.ControllerBase 'Microsoft.AspNetCore.Mvc.ControllerBase') &#129106; [Microsoft.AspNetCore.Mvc.Controller](https://docs.microsoft.com/en-us/dotnet/api/Microsoft.AspNetCore.Mvc.Controller 'Microsoft.AspNetCore.Mvc.Controller') &#129106; [AlifeApi.WebApi.Controllers.BasicController](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.WebApi.Controllers.BasicController 'AlifeApi.WebApi.Controllers.BasicController') &#129106; [AlifeApi.WebApi.Controllers.AuthenticatedController](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.WebApi.Controllers.AuthenticatedController 'AlifeApi.WebApi.Controllers.AuthenticatedController') &#129106; PaymentRecordsController
### Methods

<a name='AlifeApi.WebApi.Controllers.PaymentRecordsController.CreatePaymentRecord(AlifeApi.BusinessRules.PaymentRecordModels.PaymentRecordCreateInput)'></a>

## PaymentRecordsController.CreatePaymentRecord(PaymentRecordCreateInput) Method

新增收款記錄

```csharp
public System.Threading.Tasks.Task<Microsoft.AspNetCore.Mvc.ActionResult<int>> CreatePaymentRecord(AlifeApi.BusinessRules.PaymentRecordModels.PaymentRecordCreateInput input);
```
#### Parameters

<a name='AlifeApi.WebApi.Controllers.PaymentRecordsController.CreatePaymentRecord(AlifeApi.BusinessRules.PaymentRecordModels.PaymentRecordCreateInput).input'></a>

`input` [AlifeApi.BusinessRules.PaymentRecordModels.PaymentRecordCreateInput](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.BusinessRules.PaymentRecordModels.PaymentRecordCreateInput 'AlifeApi.BusinessRules.PaymentRecordModels.PaymentRecordCreateInput')

收款記錄建立輸入資料

#### Returns
[System.Threading.Tasks.Task&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')[Microsoft.AspNetCore.Mvc.ActionResult&lt;](https://docs.microsoft.com/en-us/dotnet/api/Microsoft.AspNetCore.Mvc.ActionResult-1 'Microsoft.AspNetCore.Mvc.ActionResult`1')[System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/Microsoft.AspNetCore.Mvc.ActionResult-1 'Microsoft.AspNetCore.Mvc.ActionResult`1')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')  
新增的收款記錄ID

<a name='AlifeApi.WebApi.Controllers.PaymentRecordsController.DeletePaymentRecord(int)'></a>

## PaymentRecordsController.DeletePaymentRecord(int) Method

刪除收款記錄

```csharp
public System.Threading.Tasks.Task<Microsoft.AspNetCore.Mvc.ActionResult> DeletePaymentRecord(int paymentRecordId);
```
#### Parameters

<a name='AlifeApi.WebApi.Controllers.PaymentRecordsController.DeletePaymentRecord(int).paymentRecordId'></a>

`paymentRecordId` [System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')

收款記錄ID

#### Returns
[System.Threading.Tasks.Task&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')[Microsoft.AspNetCore.Mvc.ActionResult](https://docs.microsoft.com/en-us/dotnet/api/Microsoft.AspNetCore.Mvc.ActionResult 'Microsoft.AspNetCore.Mvc.ActionResult')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')  
NoContent

<a name='AlifeApi.WebApi.Controllers.PaymentRecordsController.GetPaymentRecord(int)'></a>

## PaymentRecordsController.GetPaymentRecord(int) Method

根據收款記錄ID取得詳細資訊

```csharp
public System.Threading.Tasks.Task<Microsoft.AspNetCore.Mvc.ActionResult<AlifeApi.BusinessRules.PaymentRecordModels.PaymentRecordOutput>> GetPaymentRecord(int paymentRecordId);
```
#### Parameters

<a name='AlifeApi.WebApi.Controllers.PaymentRecordsController.GetPaymentRecord(int).paymentRecordId'></a>

`paymentRecordId` [System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')

收款記錄ID

#### Returns
[System.Threading.Tasks.Task&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')[Microsoft.AspNetCore.Mvc.ActionResult&lt;](https://docs.microsoft.com/en-us/dotnet/api/Microsoft.AspNetCore.Mvc.ActionResult-1 'Microsoft.AspNetCore.Mvc.ActionResult`1')[AlifeApi.BusinessRules.PaymentRecordModels.PaymentRecordOutput](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.BusinessRules.PaymentRecordModels.PaymentRecordOutput 'AlifeApi.BusinessRules.PaymentRecordModels.PaymentRecordOutput')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/Microsoft.AspNetCore.Mvc.ActionResult-1 'Microsoft.AspNetCore.Mvc.ActionResult`1')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')  
收款記錄詳細資訊

<a name='AlifeApi.WebApi.Controllers.PaymentRecordsController.GetPaymentRecords(AlifeApi.BusinessRules.PaymentRecordModels.PaymentRecordQueryInput)'></a>

## PaymentRecordsController.GetPaymentRecords(PaymentRecordQueryInput) Method

取得收款記錄列表 (分頁)

```csharp
public System.Threading.Tasks.Task<Microsoft.AspNetCore.Mvc.ActionResult<AlifeApi.BusinessRules.Infrastructure.PagedListOutput<AlifeApi.BusinessRules.PaymentRecordModels.PaymentRecordListOutput>>> GetPaymentRecords(AlifeApi.BusinessRules.PaymentRecordModels.PaymentRecordQueryInput input);
```
#### Parameters

<a name='AlifeApi.WebApi.Controllers.PaymentRecordsController.GetPaymentRecords(AlifeApi.BusinessRules.PaymentRecordModels.PaymentRecordQueryInput).input'></a>

`input` [AlifeApi.BusinessRules.PaymentRecordModels.PaymentRecordQueryInput](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.BusinessRules.PaymentRecordModels.PaymentRecordQueryInput 'AlifeApi.BusinessRules.PaymentRecordModels.PaymentRecordQueryInput')

查詢條件 (必須包含訂單ID)

#### Returns
[System.Threading.Tasks.Task&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')[Microsoft.AspNetCore.Mvc.ActionResult&lt;](https://docs.microsoft.com/en-us/dotnet/api/Microsoft.AspNetCore.Mvc.ActionResult-1 'Microsoft.AspNetCore.Mvc.ActionResult`1')[AlifeApi.BusinessRules.Infrastructure.PagedListOutput&lt;](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.BusinessRules.Infrastructure.PagedListOutput-1 'AlifeApi.BusinessRules.Infrastructure.PagedListOutput`1')[AlifeApi.BusinessRules.PaymentRecordModels.PaymentRecordListOutput](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.BusinessRules.PaymentRecordModels.PaymentRecordListOutput 'AlifeApi.BusinessRules.PaymentRecordModels.PaymentRecordListOutput')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.BusinessRules.Infrastructure.PagedListOutput-1 'AlifeApi.BusinessRules.Infrastructure.PagedListOutput`1')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/Microsoft.AspNetCore.Mvc.ActionResult-1 'Microsoft.AspNetCore.Mvc.ActionResult`1')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')  
分頁的收款記錄列表

<a name='AlifeApi.WebApi.Controllers.PaymentRecordsController.UpdatePaymentRecord(int,AlifeApi.BusinessRules.PaymentRecordModels.PaymentRecordUpdateInput)'></a>

## PaymentRecordsController.UpdatePaymentRecord(int, PaymentRecordUpdateInput) Method

更新收款記錄

```csharp
public System.Threading.Tasks.Task<Microsoft.AspNetCore.Mvc.ActionResult> UpdatePaymentRecord(int paymentRecordId, AlifeApi.BusinessRules.PaymentRecordModels.PaymentRecordUpdateInput input);
```
#### Parameters

<a name='AlifeApi.WebApi.Controllers.PaymentRecordsController.UpdatePaymentRecord(int,AlifeApi.BusinessRules.PaymentRecordModels.PaymentRecordUpdateInput).paymentRecordId'></a>

`paymentRecordId` [System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')

收款記錄ID

<a name='AlifeApi.WebApi.Controllers.PaymentRecordsController.UpdatePaymentRecord(int,AlifeApi.BusinessRules.PaymentRecordModels.PaymentRecordUpdateInput).input'></a>

`input` [AlifeApi.BusinessRules.PaymentRecordModels.PaymentRecordUpdateInput](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.BusinessRules.PaymentRecordModels.PaymentRecordUpdateInput 'AlifeApi.BusinessRules.PaymentRecordModels.PaymentRecordUpdateInput')

收款記錄更新輸入資料

#### Returns
[System.Threading.Tasks.Task&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')[Microsoft.AspNetCore.Mvc.ActionResult](https://docs.microsoft.com/en-us/dotnet/api/Microsoft.AspNetCore.Mvc.ActionResult 'Microsoft.AspNetCore.Mvc.ActionResult')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')  
NoContent