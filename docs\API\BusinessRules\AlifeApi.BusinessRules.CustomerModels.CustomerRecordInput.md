#### [AlifeApi.BusinessRules](index.md 'index')
### [AlifeApi.BusinessRules.CustomerModels](AlifeApi.BusinessRules.CustomerModels.md 'AlifeApi.BusinessRules.CustomerModels')

## CustomerRecordInput Class

客戶訪談紀錄輸入 DTO

```csharp
public class CustomerRecordInput
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; CustomerRecordInput
### Properties

<a name='AlifeApi.BusinessRules.CustomerModels.CustomerRecordInput.CustomerLevel'></a>

## CustomerRecordInput.CustomerLevel Property

客戶分級或標籤

```csharp
public string CustomerLevel { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.CustomerModels.CustomerRecordInput.CustomerRecordId'></a>

## CustomerRecordInput.CustomerRecordId Property

記錄唯一識別碼 (用於更新，新增時為 0 或 null)

```csharp
public System.Nullable<int> CustomerRecordId { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

<a name='AlifeApi.BusinessRules.CustomerModels.CustomerRecordInput.HandledBy'></a>

## CustomerRecordInput.HandledBy Property

負責此次互動的人員資訊 (通常是登入者，但允許前端指定)

```csharp
public string HandledBy { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.CustomerModels.CustomerRecordInput.Notes'></a>

## CustomerRecordInput.Notes Property

詳細的記錄內容

```csharp
public string Notes { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.CustomerModels.CustomerRecordInput.RecordedAt'></a>

## CustomerRecordInput.RecordedAt Property

互動發生的實際時間或記錄時間 (預設為當前時間)

```csharp
public System.Nullable<System.DateTime> RecordedAt { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.DateTime](https://docs.microsoft.com/en-us/dotnet/api/System.DateTime 'System.DateTime')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

<a name='AlifeApi.BusinessRules.CustomerModels.CustomerRecordInput.RecordType'></a>

## CustomerRecordInput.RecordType Property

互動或記錄的類型

```csharp
public string RecordType { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')