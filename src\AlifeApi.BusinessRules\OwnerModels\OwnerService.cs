using AlifeApi.BusinessRules.Infrastructure;
using AlifeApi.BusinessRules.OwnerModels;
using AlifeApi.Common.Util;
// using AlifeApi.DataAccess.ProjectContext; // 改用 alifeContext
using AlifeApi.DataAccess.ProjectContent; // 包含 alifeContext 和 Owner
using Microsoft.EntityFrameworkCore;
using System;
using System.Linq;
using System.Threading.Tasks;

namespace AlifeApi.BusinessRules.OwnerModels
{
    /// <summary>
    /// 業主資料管理服務
    /// </summary>
    public class OwnerService : ServiceBase<alifeContext> // 改用 alifeContext
    {
        public OwnerService(IServiceProvider serviceProvider, alifeContext dbContext) // 改用 alifeContext
            : base(serviceProvider, dbContext)
        {
        }

        /// <summary>
        /// 建立新業主
        /// </summary>
        /// <param name="input">建立業主輸入資料</param>
        /// <returns>執行結果</returns>
        public async Task<OwnerUpdateOutput> CreateOwnerAsync(OwnerCreateInput input)
        {
            ArgumentNullException.ThrowIfNull(input, nameof(input));

            OwnerUpdateOutput output = new();

            // 檢查證號是否已存在
            if (await Db.Owners.AnyAsync(x => x.IdentificationNumber == input.IdentificationNumber)) // DbSet 通常是複數 Owners
            {
                // 可以定義一個更具體的 Message enum 值，例如 OwnerAlreadyExistsByIdentificationNumber
                // output.Response = Message.OwnerAlreadyExistsByIdentificationNumber;
                output.Response = Message.AccountAlreadyExist; // 暫時使用現有的，表示資料重複
                return output;
            }

            Owner entity = new()
            {
                CompanyName = input.CompanyName,
                ResponsiblePerson = input.ResponsiblePerson ?? "",
                CompanyPhone = input.CompanyPhone ?? "",
                CompanyAddress = input.CompanyAddress ?? "",
                MailingAddress = input.MailingAddress ?? "",
                ContactPerson = input.ContactPerson ?? "",
                Email = input.Email ?? "",
                PersonType = input.PersonType,
                IdentificationNumber = input.IdentificationNumber,
                ContactPhone1 = input.ContactPhone1 ?? "",
                ContactPhone2 = input.ContactPhone2 ?? ""
                // 可以在這裡添加 CreatedTime, CreatedUserId 等審計欄位 (如果 Owner 表有)
            };

            Db.Owners.Add(entity); // DbSet 通常是複數 Owners
            await Db.SaveChangesAsync();

            output.Response = Message.Success;
            return output;
        }

        /// <summary>
        /// 更新業主資料
        /// </summary>
        /// <param name="ownerId">業主ID</param>
        /// <param name="input">更新業主輸入資料</param>
        /// <returns>執行結果</returns>
        public async Task<OwnerUpdateOutput> UpdateOwnerAsync(int ownerId, OwnerUpdateInput input)
        {
            ArgumentNullException.ThrowIfNull(input, nameof(input));

            OwnerUpdateOutput output = new();

            Owner? entity = await Db.Owners.FindAsync(ownerId); // DbSet 通常是複數 Owners

            if (entity is null)
            {
                output.Response = Message.Http_404_NotFound; // 或定義 OwnerNotFound
                return output;
            }

            // 檢查更新後的證號是否與其他業主重複
            if (input.IdentificationNumber is not null &&
                input.IdentificationNumber != entity.IdentificationNumber &&
                await Db.Owners.AnyAsync(x => x.OwnerId != ownerId && x.IdentificationNumber == input.IdentificationNumber)) // DbSet 通常是複數 Owners
            {
                // output.Response = Message.OwnerAlreadyExistsByIdentificationNumber;
                output.Response = Message.AccountAlreadyExist; // 暫時使用現有的
                return output;
            }

            // 使用 ValueMapper 或手動更新欄位
            ValueMapper<OwnerUpdateInput, Owner> mapper = new(input, entity);
            mapper.MapIfHasValue(x => x.CompanyName, x => x.CompanyName);
            mapper.MapIfHasValue(x => x.ResponsiblePerson, x => x.ResponsiblePerson);
            mapper.MapIfHasValue(x => x.CompanyPhone, x => x.CompanyPhone);
            mapper.MapIfHasValue(x => x.CompanyAddress, x => x.CompanyAddress);
            mapper.MapIfHasValue(x => x.MailingAddress, x => x.MailingAddress);
            mapper.MapIfHasValue(x => x.ContactPerson, x => x.ContactPerson);
            mapper.MapIfHasValue(x => x.Email, x => x.Email);
            mapper.MapIfHasValue(x => x.PersonType, x => x.PersonType);
            mapper.MapIfHasValue(x => x.IdentificationNumber, x => x.IdentificationNumber);
            mapper.MapIfHasValue(x => x.ContactPhone1, x => x.ContactPhone1);
            mapper.MapIfHasValue(x => x.ContactPhone2, x => x.ContactPhone2);
            // 可以在這裡添加 UpdatedTime, UpdatedUserId 等審計欄位 (如果 Owner 表有)

            Db.Owners.Update(entity); // DbSet 通常是複數 Owners
            await Db.SaveChangesAsync();

            output.Response = Message.Success;
            return output;
        }

        /// <summary>
        /// 刪除業主資料 (硬刪除)
        /// </summary>
        /// <param name="ownerId">業主ID</param>
        /// <returns>執行結果</returns>
        public async Task<OwnerUpdateOutput> DeleteOwnerAsync(int ownerId)
        {
            OwnerUpdateOutput output = new();
            Owner? entity = await Db.Owners.FindAsync(ownerId); // DbSet 通常是複數 Owners

            if (entity is null)
            {
                // 即使找不到也回傳成功，符合 RESTful 風格
                output.Response = Message.Success;
                return output;
            }

            // 硬刪除
            Db.Owners.Remove(entity); // DbSet 通常是複數 Owners
            // TODO: 如果需要軟刪除，需要 Owner 表有 IsDeleted 或 Status 欄位，並更新該欄位

            await Db.SaveChangesAsync();

            output.Response = Message.Success;
            return output;
        }

        /// <summary>
        /// 取得業主列表 (分頁)
        /// </summary>
        /// <param name="input">查詢條件</param>
        /// <returns>業主列表</returns>
        public async Task<PagedListOutput<OwnerListItemGetOutput>> GetOwnerListAsync(OwnerListGetInput input)
        {
            ArgumentNullException.ThrowIfNull(input, nameof(input));

            var query = Db.Owners.AsNoTracking(); // DbSet 通常是複數 Owners

            // 篩選條件
            if (!string.IsNullOrWhiteSpace(input.CompanyName))
            {
                query = query.Where(x => x.CompanyName.Contains(input.CompanyName));
            }
            if (!string.IsNullOrWhiteSpace(input.ResponsiblePerson))
            {
                query = query.Where(x => x.ResponsiblePerson.Contains(input.ResponsiblePerson));
            }
            if (!string.IsNullOrWhiteSpace(input.IdentificationNumber))
            {
                query = query.Where(x => x.IdentificationNumber == input.IdentificationNumber);
            }
            if (!string.IsNullOrWhiteSpace(input.PersonType))
            {
                query = query.Where(x => x.PersonType == input.PersonType);
            }

            // TODO: 加上軟刪除的過濾 (如果適用)

            var pagedResult = await query
                .Select(x => new OwnerListItemGetOutput
                {
                    OwnerId = x.OwnerId,
                    CompanyName = x.CompanyName,
                    ResponsiblePerson = x.ResponsiblePerson,
                    ContactPerson = x.ContactPerson,
                    ContactPhone1 = x.ContactPhone1,
                    PersonType = x.PersonType,
                    IdentificationNumber = x.IdentificationNumber
                })
                .ToPagedListOutputAsync(input); // 使用擴充方法處理分頁和排序

            return pagedResult;
        }

        /// <summary>
        /// 取得單一業主詳細資料
        /// </summary>
        /// <param name="ownerId">業主ID</param>
        /// <returns>業主詳細資料或 null</returns>
        public async Task<OwnerGetOutput?> GetOwnerAsync(int ownerId)
        {
            var owner = await Db.Owners.AsNoTracking() // DbSet 通常是複數 Owners
                .Where(x => x.OwnerId == ownerId)
                // TODO: 加上軟刪除的過濾 (如果適用)
                .Select(x => new OwnerGetOutput
                {
                    OwnerId = x.OwnerId,
                    CompanyName = x.CompanyName,
                    ResponsiblePerson = x.ResponsiblePerson,
                    CompanyPhone = x.CompanyPhone,
                    CompanyAddress = x.CompanyAddress,
                    MailingAddress = x.MailingAddress,
                    ContactPerson = x.ContactPerson,
                    Email = x.Email,
                    PersonType = x.PersonType,
                    IdentificationNumber = x.IdentificationNumber,
                    ContactPhone1 = x.ContactPhone1,
                    ContactPhone2 = x.ContactPhone2
                })
                .FirstOrDefaultAsync();

            return owner;
        }
    }
} 
