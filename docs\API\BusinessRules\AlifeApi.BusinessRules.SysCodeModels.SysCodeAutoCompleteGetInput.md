#### [AlifeApi.BusinessRules](index.md 'index')
### [AlifeApi.BusinessRules.SysCodeModels](AlifeApi.BusinessRules.SysCodeModels.md 'AlifeApi.BusinessRules.SysCodeModels')

## SysCodeAutoCompleteGetInput Class

配合 SysCode 的輸入自動提示

```csharp
public class SysCodeAutoCompleteGetInput
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; SysCodeAutoCompleteGetInput
### Properties

<a name='AlifeApi.BusinessRules.SysCodeModels.SysCodeAutoCompleteGetInput.Keyword'></a>

## SysCodeAutoCompleteGetInput.Keyword Property

查詢字以 StartWith 查詢

```csharp
public string Keyword { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.SysCodeModels.SysCodeAutoCompleteGetInput.Type'></a>

## SysCodeAutoCompleteGetInput.Type Property

分類

```csharp
public string Type { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')