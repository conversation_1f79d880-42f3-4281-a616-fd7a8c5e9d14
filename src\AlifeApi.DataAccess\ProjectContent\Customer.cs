﻿using System;
using System.Collections.Generic;

namespace AlifeApi.DataAccess.ProjectContent
{
    /// <summary>
    /// 客戶基本資料表
    /// </summary>
    public partial class Customer
    {
        public Customer()
        {
            CustomerRecords = new HashSet<CustomerRecord>();
        }

        /// <summary>
        /// 客戶唯一識別碼 (主鍵)
        /// </summary>
        public int CustomerId { get; set; }
        /// <summary>
        /// 客戶姓名
        /// </summary>
        public string Name { get; set; }
        /// <summary>
        /// 性別
        /// </summary>
        public string Gender { get; set; }
        /// <summary>
        /// 生日
        /// </summary>
        public DateOnly? Birthday { get; set; }
        /// <summary>
        /// 職業
        /// </summary>
        public string Occupation { get; set; }
        /// <summary>
        /// 居住或感興趣的縣市
        /// </summary>
        public string City { get; set; }
        /// <summary>
        /// 居住或感興趣的區域
        /// </summary>
        public string District { get; set; }
        /// <summary>
        /// 詳細地址
        /// </summary>
        public string Address { get; set; }
        /// <summary>
        /// 聯絡電話
        /// </summary>
        public string PhoneNumber { get; set; }
        /// <summary>
        /// 電子郵件地址 (唯一)
        /// </summary>
        public string Email { get; set; }
        /// <summary>
        /// 客戶來源或得知管道
        /// </summary>
        public string LeadSource { get; set; }
        /// <summary>
        /// 需求坪數 (文字描述或數值)
        /// </summary>
        public string RequiredPingArea { get; set; }
        /// <summary>
        /// 需求格局 (例如: 3房2廳)
        /// </summary>
        public string RequiredLayout { get; set; }
        /// <summary>
        /// 購房預算範圍或數值 (文字描述或數值)
        /// </summary>
        public string Budget { get; set; }
        /// <summary>
        /// 其他購屋條件
        /// </summary>
        public string PurchaseConditions { get; set; }
        /// <summary>
        /// 購屋主要用途 (例如: 自住, 投資)
        /// </summary>
        public string PurchasePurpose { get; set; }
        /// <summary>
        /// 需求的房屋類型 (例如: 預售屋, 新成屋)
        /// </summary>
        public string RequiredPropertyType { get; set; }
        /// <summary>
        /// 樓層偏好
        /// </summary>
        public string FloorPreference { get; set; }
        /// <summary>
        /// 預計購屋時間範圍
        /// </summary>
        public string PurchaseTimeline { get; set; }
        /// <summary>
        /// 是否同意個資使用或行銷 (True/False)
        /// </summary>
        public bool? HasConsent { get; set; }
        /// <summary>
        /// 資料創建人員的 UserInfo ID (VARCHAR(15) 外鍵)
        /// </summary>
        public string CreatedUserInfoId { get; set; }
        /// <summary>
        /// 資料創建時間
        /// </summary>
        public DateTime CreatedTime { get; set; }
        /// <summary>
        /// 資料最後更新人員的 UserInfo ID (VARCHAR(15) 外鍵)
        /// </summary>
        public string UpdatedUserInfoId { get; set; }
        /// <summary>
        /// 資料最後更新時間
        /// </summary>
        public DateTime UpdatedTime { get; set; }
        /// <summary>
        /// 簽名檔的IIS訪問路徑 (指向 C:\alifeImg\...)
        /// </summary>
        public string WebPath { get; set; }
        /// <summary>
        /// 簽名檔在NAS上的存檔路徑
        /// </summary>
        public string ArchivePath { get; set; }
        /// <summary>
        /// 客戶主要關聯的案場號碼
        /// </summary>
        public string SiteCode { get; set; }

        public virtual ICollection<CustomerRecord> CustomerRecords { get; set; }
    }
}
