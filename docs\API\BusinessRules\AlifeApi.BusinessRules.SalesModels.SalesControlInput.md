#### [AlifeApi.BusinessRules](index.md 'index')
### [AlifeApi.BusinessRules.SalesModels](AlifeApi.BusinessRules.SalesModels.md 'AlifeApi.BusinessRules.SalesModels')

## SalesControlInput Class

銷控表查詢輸入模型

```csharp
public class SalesControlInput
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; SalesControlInput
### Properties

<a name='AlifeApi.BusinessRules.SalesModels.SalesControlInput.BuildingId'></a>

## SalesControlInput.BuildingId Property

建築物ID (可選，不指定則顯示所有建築物)

```csharp
public System.Nullable<int> BuildingId { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

<a name='AlifeApi.BusinessRules.SalesModels.SalesControlInput.SiteCode'></a>

## SalesControlInput.SiteCode Property

案場編號

```csharp
public string SiteCode { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')