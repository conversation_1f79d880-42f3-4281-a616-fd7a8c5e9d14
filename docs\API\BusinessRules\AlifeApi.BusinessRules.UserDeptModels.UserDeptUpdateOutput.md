#### [AlifeApi.BusinessRules](index.md 'index')
### [AlifeApi.BusinessRules.UserDeptModels](AlifeApi.BusinessRules.UserDeptModels.md 'AlifeApi.BusinessRules.UserDeptModels')

## UserDeptUpdateOutput Class

```csharp
public class UserDeptUpdateOutput :
AlifeApi.BusinessRules.Infrastructure.IApiMessage
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; UserDeptUpdateOutput

Implements [IApiMessage](AlifeApi.BusinessRules.Infrastructure.IApiMessage.md 'AlifeApi.BusinessRules.Infrastructure.IApiMessage')
### Properties

<a name='AlifeApi.BusinessRules.UserDeptModels.UserDeptUpdateOutput.Response'></a>

## UserDeptUpdateOutput.Response Property

Gets the response.

```csharp
public System.Enum Response { get; set; }
```

Implements [Response](AlifeApi.BusinessRules.Infrastructure.IApiMessage.md#AlifeApi.BusinessRules.Infrastructure.IApiMessage.Response 'AlifeApi.BusinessRules.Infrastructure.IApiMessage.Response')

#### Property Value
[System.Enum](https://docs.microsoft.com/en-us/dotnet/api/System.Enum 'System.Enum')