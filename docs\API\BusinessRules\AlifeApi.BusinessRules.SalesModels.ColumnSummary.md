#### [AlifeApi.BusinessRules](index.md 'index')
### [AlifeApi.BusinessRules.SalesModels](AlifeApi.BusinessRules.SalesModels.md 'AlifeApi.BusinessRules.SalesModels')

## ColumnSummary Class

戶別統計摘要 (A戶、B戶等的統計)

```csharp
public class ColumnSummary
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; ColumnSummary
### Properties

<a name='AlifeApi.BusinessRules.SalesModels.ColumnSummary.Available'></a>

## ColumnSummary.Available Property

該戶別可售數量

```csharp
public int Available { get; set; }
```

#### Property Value
[System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')

<a name='AlifeApi.BusinessRules.SalesModels.ColumnSummary.Column'></a>

## ColumnSummary.Column Property

戶別 (例如: "A", "B", "C")

```csharp
public string Column { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.SalesModels.ColumnSummary.Reserved'></a>

## ColumnSummary.Reserved Property

該戶別保留數量

```csharp
public int Reserved { get; set; }
```

#### Property Value
[System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')

<a name='AlifeApi.BusinessRules.SalesModels.ColumnSummary.SalesRate'></a>

## ColumnSummary.SalesRate Property

該戶別去化百分比

```csharp
public string SalesRate { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.SalesModels.ColumnSummary.Sold'></a>

## ColumnSummary.Sold Property

該戶別已售數量

```csharp
public int Sold { get; set; }
```

#### Property Value
[System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')