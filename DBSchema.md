# 資料庫綱要 (Database Schema)

本文件詳細說明了資料庫中所有資料表的結構與欄位資訊。

## Buildings (建築物資料表)

| 欄位名稱 (Column Name)     | 資料型態 (Data Type)         | 描述 (Description)                                  |
| :------------------------- | :--------------------------- | :-------------------------------------------------- |
| `BuildingId`               | `integer`                    | 建築物唯一識別碼 (主鍵, 自動遞增)。                 |
| `SiteCode`                 | `character varying`          | 所屬案場編號 (參考 Sites 表)。                        |
| `BuildingName`             | `character varying`          | 建築物名稱或編號 (例如: "A棟", "帝寶一期", "Building 1")。 |
| `TotalAboveGroundFloors`   | `integer`                    | 該棟地上總樓層數。                                  |
| `TotalBelowGroundFloors`   | `integer`                    | 該棟地下總樓層數。                                  |
| `BuildingType`             | `character varying`          | 建築類型 (例如: "住宅", "商辦", "住商混合" - 建議關聯 SYS_Code)。 |
| `CompletionDate`           | `date`                       | 預計或實際完工日期。                                |
| `Remarks`                  | `text`                       | 備註。                                              |
| `CreatedTime`              | `timestamp without time zone`| 資料創建時間 (TIMESTAMP WITHOUT TIME ZONE)。          |
| `UpdatedTime`              | `timestamp without time zone`| 資料最後更新時間 (TIMESTAMP WITHOUT TIME ZONE)。      |
| `CreatedUserInfoId`        | `character varying`          | 建立者 (參考 UserInfo 表)。                         |
| `UpdatedUserInfoId`        | `character varying`          | 更新者 (參考 UserInfo 表)。                         |

## Company (公司資料表)

| 欄位名稱 (Column Name)     | 資料型態 (Data Type)         | 描述 (Description)                             |
| :------------------------- | :--------------------------- | :--------------------------------------------- |
| `CompanyId`                | `character varying`          | 公司編號，主鍵，用於唯一識別公司。                   |
| `Name`                     | `character varying`          | 公司名稱。                                     |
| `CreatedTime`              | `timestamp without time zone`| 創建時間，記錄資料創建的時間。                       |
| `UpdatedTime`              | `timestamp without time zone`| 更新時間，記錄資料最後更新的時間。                     |
| `CreatedUserInfoId`        | `character varying`          | 建立者，記錄創建此資料的員工編號。                   |
| `UpdatedUserInfoId`        | `character varying`          | 更新者，記錄最後更新此資料的員工編號。                 |

## CrmOptionTypes (CRM選項類型資料表)

| 欄位名稱 (Column Name)     | 資料型態 (Data Type)         | 描述 (Description)                             |
| :------------------------- | :--------------------------- | :--------------------------------------------- |
| `CrmOptionTypeId`          | `bigint`                     | 主鍵，選項類型唯一識別碼                           |
| `TypeName`                 | `character varying`          | 選項類型名稱 (例如: 需求坪數, 需求格局, 預算範圍)      |
| `Description`              | `text`                       | 此選項類型的描述，方便後台管理人員理解                 |
| `CreatedUserInfoId`        | `character varying`          | 建立者，記錄創建此資料的員工編號。                   |
| `CreateTime`               | `timestamp without time zone`| 創建時間，記錄資料創建的時間。                       |
| `UpdatedUserInfoId`        | `character varying`          | 更新者，記錄最後更新此資料的員工編號。                 |
| `UpdateTime`               | `timestamp without time zone`| 更新時間，記錄資料最後更新的時間。                     |

## Customer (客戶資料表)

| 欄位名稱 (Column Name)       | 資料型態 (Data Type)         | 描述 (Description)                                          |
| :--------------------------- | :--------------------------- | :---------------------------------------------------------- |
| `CustomerId`                 | `integer`                    | 客戶唯一識別碼 (主鍵)                                         |
| `Name`                       | `text`                       | 客戶姓名                                                    |
| `Gender`                     | `text`                       | 性別                                                        |
| `Birthday`                   | `date`                       | 生日                                                        |
| `Occupation`                 | `text`                       | 職業                                                        |
| `City`                       | `text`                       | 居住或感興趣的縣市                                          |
| `District`                   | `text`                       | 居住或感興趣的區域                                          |
| `Address`                    | `text`                       | 詳細地址                                                    |
| `PhoneNumber`                | `character varying`          | 聯絡電話                                                    |
| `Email`                      | `character varying`          | 電子郵件地址 (唯一)                                         |
| `LeadSource`                 | `text`                       | 客戶來源或得知管道                                          |
| `RequiredPingArea`           | `text`                       | 需求坪數 (文字描述或數值)                                   |
| `RequiredLayout`             | `text`                       | 需求格局 (例如: 3房2廳)                                     |
| `Budget`                     | `text`                       | 購房預算範圍或數值 (文字描述或數值)                           |
| `PurchaseConditions`         | `text`                       | 其他購屋條件                                                |
| `PurchasePurpose`            | `text`                       | 購屋主要用途 (例如: 自住, 投資)                               |
| `RequiredPropertyType`       | `text`                       | 需求的房屋類型 (例如: 預售屋, 新成屋)                         |
| `FloorPreference`            | `text`                       | 樓層偏好                                                    |
| `PurchaseTimeline`           | `text`                       | 預計購屋時間範圍                                            |
| `HasConsent`                 | `boolean`                    | 是否同意個資使用或行銷 (True/False)                           |
| `CreatedUserInfoId`          | `character varying`          | 資料創建人員的 UserInfo ID (VARCHAR(15) 外鍵)                 |
| `CreatedTime`                | `timestamp without time zone`| 資料創建時間                                                |
| `UpdatedUserInfoId`          | `character varying`          | 資料最後更新人員的 UserInfo ID (VARCHAR(15) 外鍵)             |
| `UpdatedTime`                | `timestamp without time zone`| 資料最後更新時間                                            |
| `WebPath`                    | `text`                       | 簽名檔的IIS訪問路徑 (指向 C:\\alifeImg\\...)                  |
| `ArchivePath`                | `text`                       | 簽名檔在NAS上的存檔路徑                                     |
| `SiteCode`                   | `character varying`          | 客戶主要關聯的案場號碼                                      |

## Customer_Record (客戶互動記錄資料表)

| 欄位名稱 (Column Name)     | 資料型態 (Data Type)         | 描述 (Description)                                          |
| :------------------------- | :--------------------------- | :---------------------------------------------------------- |
| `CustomerRecordId`         | `integer`                    | 記錄唯一識別碼 (主鍵)                                         |
| `CustomerId`               | `integer`                    | 關聯的客戶ID (外鍵)                                         |
| `RecordType`               | `text`                       | 互動或記錄的類型                                            |
| `Notes`                    | `text`                       | 詳細的記錄內容                                              |
| `RecordedAt`               | `timestamp without time zone`| 互動發生的實際時間或記錄時間                                  |
| `CustomerLevel`            | `text`                       | 客戶分級或標籤                                              |
| `HandledBy`                | `text`                       | 負責此次互動的人員資訊                                      |
| `CreatedUserInfoId`        | `character varying`          | 此記錄創建人員的 UserInfo ID (VARCHAR(15) 外鍵)                 |
| `CreatedTime`              | `timestamp without time zone`| 此記錄的創建時間                                            |
| `UpdatedUserInfoId`        | `character varying`          | 此記錄最後更新人員的 UserInfo ID (VARCHAR(15) 外鍵)             |
| `UpdatedTime`              | `timestamp without time zone`| 此記錄的最後更新時間                                        |

## Departments (部門資料表)

| 欄位名稱 (Column Name)     | 資料型態 (Data Type)         | 描述 (Description)                                     |
| :------------------------- | :--------------------------- | :----------------------------------------------------- |
| `CompanyId`                | `character varying`          | 公司編號，主鍵的一部分，對應 Company 表的 CompanyId。        |
| `DepartmentId`             | `character varying`          | 部門編號，主鍵的一部分，用於唯一識別部門。                   |
| `Name`                     | `character varying`          | 部門名稱。                                         |
| `CreatedTime`              | `timestamp without time zone`| 創建時間，記錄資料創建的時間。                           |
| `UpdatedTime`              | `timestamp without time zone`| 更新時間，記錄資料最後更新的時間。                         |
| `CreatedUserInfoId`        | `character varying`          | 建立者，記錄創建此資料的員工編號。                       |
| `UpdatedUserInfoId`        | `character varying`          | 更新者，記錄最後更新此資料的員工編號。                     |

## EmailSendLog (郵件發送記錄資料表)

| 欄位名稱 (Column Name)     | 資料型態 (Data Type)         | 描述 (Description)                                     |
| :------------------------- | :--------------------------- | :----------------------------------------------------- |
| `EmailSendLogId`           | `integer`                    | 流水號，主鍵，用於唯一識別郵件發送記錄。                     |
| `RecipientEmail`           | `character varying`          | 收件者電子郵件地址。                                   |
| `RecipientName`            | `character varying`          | 收件者姓名。                                         |
| `Subject`                  | `character varying`          | 郵件主題。                                         |
| `Body`                     | `text`                       | 郵件內容。                                         |
| `SentDate`                 | `timestamp without time zone`| 郵件發送時間。                                       |
| `IsSuccess`                | `boolean`                    | 是否發送成功，true 表示成功，false 表示失敗。                |
| `ErrorMessage`             | `text`                       | 若發送失敗，記錄錯誤訊息。                               |
| `ReportStartDate`          | `timestamp without time zone`| 報表統計的開始日期。                                   |
| `ReportEndDate`            | `timestamp without time zone`| 報表統計的結束日期。                                   |
| `AttachmentFileName`       | `character varying`          | 附件的檔案名稱。                                     |
| `AttachmentFileSize`       | `integer`                    | 附件檔案大小，單位為位元組。                               |
| `CreatedUserInfoId`        | `character varying`          | 建立者，記錄創建此記錄的員工編號或系統帳號。                   |
| `CreatedTime`              | `timestamp without time zone`| 創建時間，記錄郵件記錄創建的時間。                         |

## Floors (樓層資料表)

| 欄位名稱 (Column Name)     | 資料型態 (Data Type)         | 描述 (Description)                                                               |
| :------------------------- | :--------------------------- | :------------------------------------------------------------------------------- |
| `FloorId`                  | `integer`                    | 樓層唯一識別碼 (主鍵, 自動遞增)。                                                  |
| `BuildingId`               | `integer`                    | 所屬建築物識別碼 (參考 Buildings 表)。                                             |
| `SiteCode`                 | `character varying`          | 所屬案場編號 (參考 Sites 表) - 方便查詢用。                                        |
| `FloorLabel`               | `character varying`          | 樓層的顯示標示 (例如: "15F", "1F", "店面", "P1F", "B1")。                           |
| `FloorLevel`               | `integer`                    | 樓層的數值，用於排序 (例如: 15, 1, 0, -1, -2)。                                     |
| `FloorType`                | `character varying`          | 樓層主要用途，**用於區分住宅/商業樓層與停車場樓層** (例如: "住宅", "商業", "停車場" - 建議關聯 SYS_Code)。 |
| `FloorHeight`              | `numeric`                    | 樓層高度 (米)。                                                                  |
| `Remarks`                  | `text`                       | 備註。                                                                           |
| `CreatedTime`              | `timestamp without time zone`| 資料創建時間 (TIMESTAMP WITHOUT TIME ZONE)。                                       |
| `UpdatedTime`              | `timestamp without time zone`| 資料最後更新時間 (TIMESTAMP WITHOUT TIME ZONE)。                                     |
| `CreatedUserInfoId`        | `character varying`          | 建立者 (參考 UserInfo 表)。                                                       |
| `UpdatedUserInfoId`        | `character varying`          | 更新者 (參考 UserInfo 表)。                                                       |

## JobTitles (職位資料表)

| 欄位名稱 (Column Name)     | 資料型態 (Data Type)         | 描述 (Description)                                        |
| :------------------------- | :--------------------------- | :-------------------------------------------------------- |
| `DepartmentId`             | `character varying`          | 部門編號，主鍵的一部分，對應 Departments 表的 DepartmentId。    |
| `JobTitleId`               | `character varying`          | 職位編號，主鍵的一部分，用於唯一識別職位。                      |
| `Name`                     | `character varying`          | 職位名稱。                                            |
| `CreatedTime`              | `timestamp without time zone`| 創建時間，記錄資料創建的時間。                              |
| `UpdatedTime`              | `timestamp without time zone`| 更新時間，記錄資料最後更新的時間。                            |
| `CreatedUserInfoId`        | `character varying`          | 建立者，記錄創建此資料的員工編號。                          |
| `UpdatedUserInfoId`        | `character varying`          | 更新者，記錄最後更新此資料的員工編號。                        |

## LargeCategory (大分類資料表)

| 欄位名稱 (Column Name)     | 資料型態 (Data Type)         | 描述 (Description)                                        |
| :------------------------- | :--------------------------- | :-------------------------------------------------------- |
| `LargeCategoryId`          | `bigint`                     | 主鍵，大分類唯一識別碼                                      |
| `Name`                     | `character varying`          | 大分類的顯示名稱                                          |
| `Description`              | `character varying`          | 大分類的詳細文字描述                                        |
| `SortOrder`                | `integer`                    | 排序欄位，數字越小越前面                                    |
| `IsActive`                 | `boolean`                    | 是否啟用此分類 (TRUE: 啟用, FALSE: 停用)                   |
| `CreatedUserInfoId`        | `character varying`          | 建立者，記錄創建此資料的員工編號                            |
| `CreateTime`               | `timestamp without time zone`| 創建時間，記錄資料創建的時間                                |
| `UpdatedUserInfoId`        | `character varying`          | 更新者，記錄最後更新此資料的員工編號                        |
| `UpdateTime`               | `timestamp without time zone`| 更新時間，記錄資料最後更新的時間                            |

## MediumCategory (中分類資料表)

| 欄位名稱 (Column Name)     | 資料型態 (Data Type)         | 描述 (Description)                                        |
| :------------------------- | :--------------------------- | :-------------------------------------------------------- |
| `MediumCategoryId`         | `bigint`                     | 主鍵，中分類唯一識別碼                                      |
| `LargeCategoryId`          | `bigint`                     | 所屬大分類的ID，需由應用程式確保其有效性                    |
| `Name`                     | `character varying`          | 中分類的顯示名稱                                          |
| `Description`              | `character varying`          | 中分類的詳細文字描述                                        |
| `SortOrder`                | `integer`                    | 在同一個大分類下的排序順序，數字越小越前面                  |
| `IsActive`                 | `boolean`                    | 是否啟用此分類 (TRUE: 啟用, FALSE: 停用)                   |
| `CreatedUserInfoId`        | `character varying`          | 建立者，記錄創建此資料的員工編號                            |
| `CreateTime`               | `timestamp without time zone`| 創建時間，記錄資料創建的時間                                |
| `UpdatedUserInfoId`        | `character varying`          | 更新者，記錄最後更新此資料的員工編號                        |
| `UpdateTime`               | `timestamp without time zone`| 更新時間，記錄資料最後更新的時間                            |

## SmallCategory (小分類資料表)

| 欄位名稱 (Column Name)     | 資料型態 (Data Type)         | 描述 (Description)                                        |
| :------------------------- | :--------------------------- | :-------------------------------------------------------- |
| `SmallCategoryId`          | `bigint`                     | 主鍵，小分類唯一識別碼                                      |
| `MediumCategoryId`         | `bigint`                     | 所屬中分類的ID，需由應用程式確保其有效性                    |
| `Name`                     | `character varying`          | 小分類的顯示名稱                                          |
| `Description`              | `character varying`          | 小分類的詳細文字描述                                        |
| `SortOrder`                | `integer`                    | 在同一個中分類下的排序順序，數字越小越前面                  |
| `IsActive`                 | `boolean`                    | 是否啟用此分類 (TRUE: 啟用, FALSE: 停用)                   |
| `CreatedUserInfoId`        | `character varying`          | 建立者，記錄創建此資料的員工編號                            |
| `CreateTime`               | `timestamp without time zone`| 創建時間，記錄資料創建的時間                                |
| `UpdatedUserInfoId`        | `character varying`          | 更新者，記錄最後更新此資料的員工編號                        |
| `UpdateTime`               | `timestamp without time zone`| 更新時間，記錄資料最後更新的時間                            |

## NotificationSettings (通知設定資料表)

| 欄位名稱 (Column Name) | 資料型態 (Data Type) | 描述 (Description)                                                               |
| :--------------------- | :------------------- | :------------------------------------------------------------------------------- |
| `SettingId`            | `integer`            | 設定流水號，主鍵，自動遞增，用於唯一識別通知設定記錄。                                   |
| `StepId`               | `integer`            | 步驟流水號，對應 ReviewSteps 表的 StepId。                                         |
| `NotifyType`           | `character varying`  | 通知類型，可選值為 system（系統通知）、email（電子郵件）、sms（簡訊）。                      |
| `Enabled`              | `boolean`            | 是否啟用，true 表示啟用，false 表示停用，預設為 true。                                  |

## Owner (業主資料表)

| 欄位名稱 (Column Name)       | 資料型態 (Data Type) | 描述 (Description)                               |
| :--------------------------- | :------------------- | :----------------------------------------------- |
| `OwnerId`                  | `integer`            | 業主唯一識別碼 (自動增長)                            |
| `CompanyName`              | `text`               | 公司名稱                                         |
| `ResponsiblePerson`        | `text`               | 負責人姓名                                       |
| `CompanyPhone`             | `character varying`  | 公司登記電話                                     |
| `CompanyAddress`           | `text`               | 公司登記地址                                     |
| `MailingAddress`           | `text`               | 郵件通訊地址 (若與公司地址不同)                        |
| `ContactPerson`            | `text`               | 主要聯絡窗口人員姓名                               |
| `Email`                    | `text`               | 聯絡電子郵件地址                                 |
| `PersonType`               | `character varying`  | 人別 (值為 法人 或 自然人)                           |
| `IdentificationNumber`     | `character varying`  | 證號 (依人別決定是 統一編號 或 身分證ID)                 |
| `ContactPhone1`            | `character varying`  | 主要聯絡電話                                     |
| `ContactPhone2`            | `character varying`  | 次要聯絡電話 (備用)                                |

## ParkingSpaces (停車位資料表)

| 欄位名稱 (Column Name)     | 資料型態 (Data Type)         | 描述 (Description)                                                                                                |
| :------------------------- | :--------------------------- | :---------------------------------------------------------------------------------------------------------------- |
| `ParkingSpaceId`           | `integer`                    | 停車位唯一識別碼 (主鍵, 自動遞增)。                                                                                 |
| `SiteCode`                 | `character varying`          | 所屬案場編號 (參考 Sites 表)。                                                                                      |
| `BuildingId`               | `integer`                    | 所屬建築物識別碼 (參考 Buildings 表)。                                                                              |
| `FloorId`                  | `integer`                    | 所在樓層識別碼 (**參考 Floors 表中 FloorType 為 '停車場' 的樓層ID**, e.g., P1F, B1)。                                  |
| `SpaceNumber`              | `character varying`          | 車位編號 (例如 "A01", "B15", "50")。                                                                                |
| `SpaceType`                | `character varying`          | 車位類型 (例如 "坡道平面", "機械", "大型", "小型", "殘障" - 建議關聯 SYS_Code)。                                             |
| `Dimensions`               | `character varying`          | 車位尺寸 (例如 "250x550cm")。                                                                                     |
| `Location`                 | `character varying`          | 車位詳細位置描述 (例如 "靠近電梯", "角落位置")。                                                                       |
| `Remarks`                  | `text`                       | 備註 (例如 "柱子較多", "出入較便利")。                                                                                |
| `CreatedTime`              | `timestamp without time zone`| 資料創建時間 (TIMESTAMP WITHOUT TIME ZONE)。                                                                        |
| `UpdatedTime`              | `timestamp without time zone`| 資料最後更新時間 (TIMESTAMP WITHOUT TIME ZONE)。                                                                      |
| `CreatedUserInfoId`        | `character varying`          | 建立者 (參考 UserInfo 表)。                                                                                       |
| `UpdatedUserInfoId`        | `character varying`          | 更新者 (參考 UserInfo 表)。                                                                                       |

**設計說明：**
- 本表只儲存車位的基本資訊和物理特性
- 如需銷售車位，應在 `Units` 表中建立對應記錄，`UnitType` 設為 "車位"
- `Units` 表中的車位記錄可透過 `AssociatedParkingSpaceIds` 欄位關聯到本表
- 價格、銷售狀態等商業資訊統一由 `Units` 表管理

## ProjectExpenses (專案支出記錄資料表)

| 欄位名稱 (Column Name)     | 資料型態 (Data Type)         | 描述 (Description)                             |
| :------------------------- | :--------------------------- | :--------------------------------------------- |
| `ProjectExpenseId`         | `bigint`                     | 主鍵，支出紀錄唯一識別碼                           |
| `SiteCode`                 | `character varying`          | 案場代碼，指明此筆支出屬於哪個案場                   |
| `SmallCategoryId`          | `bigint`                     | 關聯的小分類ID (需由應用程式確保其有效性)            |
| `Amount`                   | `numeric`                    | 支出金額                                       |
| `ExpenseDate`              | `date`                       | 支出發生的日期                                   |
| `Description`              | `text`                       | 關於此筆支出的額外文字描述或備註                     |
| `CreatedUserInfoId`        | `character varying`          | 建立者，記錄創建此資料的員工編號。                   |
| `CreateTime`               | `timestamp without time zone`| 創建時間，記錄資料創建的時間。                       |
| `UpdatedUserInfoId`        | `character varying`          | 更新者，記錄最後更新此資料的員工編號。                 |
| `UpdateTime`               | `timestamp without time zone`| 更新時間，記錄資料最後更新的時間。                     |

## PaymentRecords (收款記錄資料表)

| 欄位名稱 (Column Name)         | 資料型態 (Data Type)         | 描述 (Description)                                                              |
| :----------------------------- | :--------------------------- | :------------------------------------------------------------------------------ |
| `PaymentRecordId`            | `integer`                    | 收款記錄唯一識別碼 (主鍵, 自動遞增)。                                                 |
| `OrderId`                    | `integer`                    | 關聯的訂單 ID (參考 PurchaseOrders 表)。                                           |
| `PaymentDate`                | `date`                       | 實際收款的日期 (或預計收款日)。                                                     |
| `Amount`                     | `numeric`                    | 本次收款的金額 (新台幣)。                                                         |
| `PaymentType`                | `character varying`          | 本次收款的款項類別 (例如: 定金, 簽約金, 工程款 - 建議關聯 SYS_Code)。                  |
| `PaymentMethod`              | `character varying`          | 本次收款的方式 (例如: 現金, 匯款, 支票 - 建議關lungen SYS_Code)。                     |
| `CheckNumber`                | `character varying`          | 支票號碼 (如果付款方式是支票)。                                                     |
| `CheckDueDate`               | `date`                       | 支票到期日 (如果付款方式是支票)。                                                   |
| `BankName`                   | `character varying`          | 匯款銀行或支票開立銀行。                                                          |
| `BankAccountNumber`          | `character varying`          | 匯款帳號末五碼或支票帳號。                                                        |
| `AttachmentPath`             | `character varying`          | 相關檔案 (如收據掃描檔、匯款單) 的儲存路徑。                                        |
| `Remarks`                    | `text`                       | 收款備註 (例如：特定期數說明)。                                                     |
| `IsReceived`                 | `boolean`                    | 標記此筆款項是否已確認入帳/兌現 (True/False)。                                      |
| `HandlingFee`                | `numeric`                    | 本次收款可能產生的手續費 (例如刷卡手續費，可選)。                                    |
| `TransferredToDeveloper`     | `boolean`                    | 該筆款項是否已轉交給建設公司 (True/False，可選)。                                  |
| `CreatedTime`                | `timestamp without time zone`| 記錄創建時間 (TIMESTAMP WITHOUT TIME ZONE)。                                        |
| `UpdatedTime`                | `timestamp without time zone`| 記錄最後更新時間 (TIMESTAMP WITHOUT TIME ZONE)。                                      |
| `CreatedUserInfoId`          | `character varying`          | 建立此收款記錄的人員ID (參考 UserInfo 表)。                                        |
| `UpdatedUserInfoId`          | `character varying`          | 最後更新此收款記錄的人員ID (參考 UserInfo 表)。                                      |

## PurchaseOrders (訂單資料表)

| 欄位名稱 (Column Name)           | 資料型態 (Data Type)         | 描述 (Description)                                                              |
| :------------------------------- | :--------------------------- | :------------------------------------------------------------------------------ |
| `OrderId`                        | `integer`                    | 訂單唯一識別碼 (主鍵, 自動遞增)。                                                 |
| `OrderNumber`                    | `character varying`          | 預定單編號 (可選, 應具備唯一性)。                                                  |
| `OrderDate`                      | `date`                       | 訂單建立日期。                                                                  |
| `SiteCode`                       | `character varying`          | 建案編號 (參考 Sites 表)。                                                       |
| `CustomerId`                     | `integer`                    | 買受人ID (參考 Customer 表)。                                                    |
| `SalesAgencyName`                | `character varying`          | 代銷公司名稱。                                                                  |
| `SalesAgencyMobile`              | `character varying`          | 代銷公司行動電話。                                                                |
| `SalesAgencyLandline`            | `character varying`          | 代銷公司市話。                                                                  |
| `SalesAgencyEmail`               | `character varying`          | 代銷公司 E-mail。                                                               |
| `UnitId`                         | `integer`                    | 購買的主要房屋單位 ID (參考 Units 表)，若僅購買車位則此欄位為 Null。                   |
| `LandShareArea`                  | `numeric`                    | 土地持分面積 (坪)。                                                               |
| `PurchasedParkingSpaceIds`       | `text`                       | 本次訂單購買的停車位 ID 列表，以逗號分隔 (參考 ParkingSpaces 表)。                  |
| `PropertyPrice`                  | `numeric`                    | 本次交易的房地售價 (新台幣，不含車位價)。                                          |
| `ParkingSpacePrice`              | `numeric`                    | 本次交易的車位售價 (新台幣，可能是多個車位的總和)。                                  |
| `TotalPrice`                     | `numeric`                    | 本次交易的總價 (新台幣 = PropertyPrice + ParkingSpacePrice)。                      |
| `DepositAmount`                  | `numeric`                    | 定金總額 (新台幣)。                                                               |
| `DepositPaidAmount`              | `numeric`                    | 已付定金 (新台幣)。                                                               |
| `DepositPaymentMethod`           | `character varying`          | 定金付款方式 (建議使用 SYS_Code)。                                                 |
| `DepositPayee`                   | `character varying`          | 定金收款人。                                                                    |
| `DepositDueDate`                 | `timestamp without time zone`| 應補足定金的截止日期與時間。                                                        |
| `DepositBalanceAmount`           | `numeric`                    | 應補足的定金金額 (新台幣)。                                                        |
| `ContractSigningAppointment`     | `timestamp without time zone`| 約定的簽約日期與時間。                                                            |
| `ContractSigningAmount`          | `numeric`                    | 簽約金 (新台幣)。                                                                 |
| `ConsentToDataUsage`             | `boolean`                    | 是否同意個人資料收集與利用 (True/False)。                                         |
| `OrderRemarks`                   | `text`                       | 訂單備註 (例如：特殊協議)。                                                         |
| `SalespersonUserInfoId`          | `character varying`          | 執行此訂單的銷售人員ID (參考 UserInfo 表)。                                        |
| `SaleType`                       | `character varying`          | 銷售類型 (例如: '委售', '自售' - 建議使用 SYS_Code)。                               |
| `Status`                         | `character varying`          | 訂單本身的狀態 (例如: '預訂中', '已轉訂', '已簽約', '已取消', '已作廢' - 建議使用 SYS_Code)。 |
| `SaleDate`                       | `date`                       | **實際售出日期** (客戶確認購買意願的日期)。                                           |
| `DepositFullPaidDate`            | `date`                       | **足訂日期** (定金足額到位的實際日期)。                                             |
| `ContractSignedDate`             | `date`                       | **實際簽約日期** (正式簽約完成的日期)。                                             |
| `CancellationDate`               | `date`                       | **退訂日期** (客戶退訂或訂單取消的日期)。                                           |
| `HandoverDate`                   | `date`                       | **交屋日期** (房屋正式交付給客戶的日期)。                                           |
| `FinalPaymentDate`               | `date`                       | **尾款繳清日期** (最後一筆款項收款完成的日期)。                                       |
| `RequestDate`                    | `date`                       | **請款日期** (向業主請費用的日期) - 狀態：請。                                       |
| `ReceiveDate`                    | `date`                       | **領款日期** (已向業主領到費用的日期) - 狀態：領。                                   |
| `CreatedTime`                    | `timestamp without time zone`| 訂單記錄創建時間 (TIMESTAMP WITHOUT TIME ZONE)。                                     |
| `UpdatedTime`                    | `timestamp without time zone`| 訂單記錄最後更新時間 (TIMESTAMP WITHOUT TIME ZONE)。                                  |
| `CreatedUserInfoId`              | `character varying`          | 建立者 (參考 UserInfo 表)。                                                       |
| `UpdatedUserInfoId`              | `character varying`          | 更新者 (參考 UserInfo 表)。                                                       |

## PurchaseOrdersHistory (訂單歷史記錄資料表)

| 欄位名稱 (Column Name)     | 資料型態 (Data Type)         | 描述 (Description)                               |
| :------------------------- | :--------------------------- | :----------------------------------------------- |
| `HistoryId`                | `integer`                    | 歷史記錄唯一識別碼 (主鍵, 自動遞增)。                |
| `OrderId`                  | `integer`                    | 關聯的訂單ID (參考 PurchaseOrders 表)。           |
| `ActionType`               | `character varying`          | 操作類型 (例如: STATUS_CHANGE, DATE_UPDATE, CREATE, MODIFY)。 |
| `OldStatus`                | `character varying`          | 變更前的訂單狀態。                                |
| `NewStatus`                | `character varying`          | 變更後的訂單狀態。                                |
| `ContentRecord`            | `text`                       | 詳細記錄內容或備註說明。                          |
| `CreatedUserInfoId`        | `character varying`          | 執行此操作的使用者ID (參考 UserInfo 表)。          |
| `CreatedTime`              | `timestamp without time zone`| 操作執行時間。                                   |

## ReviewApprovers (審核人員資料表)

| 欄位名稱 (Column Name) | 資料型態 (Data Type) | 描述 (Description)                                                               |
| :--------------------- | :------------------- | :------------------------------------------------------------------------------- |
| `ApproverId`           | `integer`            | 審核人員流水號，主鍵，自動遞增，用於唯一識別審核人員記錄。                                   |
| `StepId`               | `integer`            | 步驟流水號，對應 ReviewSteps 表的 StepId。                                         |
| `UserInfoId`           | `character varying`  | 使用者編號，對應 UserInfo 表的 UserInfoId。                                        |
| `IsRequired`           | `boolean`            | 是否為必要審核人員，true 表示必須審核，false 表示可選，預設為 true。                         |

## ReviewHistory (審核歷史記錄資料表)

| 欄位名稱 (Column Name) | 資料型態 (Data Type)         | 描述 (Description)                                                               |
| :--------------------- | :--------------------------- | :------------------------------------------------------------------------------- |
| `HistoryId`            | `integer`                    | 歷史記錄流水號，主鍵，自動遞增，用於唯一識別歷史記錄。                                   |
| `TaskId`               | `integer`                    | 任務流水號，對應 ReviewTasks 表的 TaskId。                                         |
| `StepId`               | `integer`                    | 步驟流水號，對應 ReviewSteps 表的 StepId。                                         |
| `UserInfoId`           | `character varying`          | 使用者編號，對應 UserInfo 表的 UserInfoId。                                        |
| `Action`               | `character varying`          | 操作類型，可選值為 approve（批准）、reject（拒絕）、return（退回）、comment（評論）。        |
| `Comment`              | `text`                       | 操作評論，提供操作的詳細說明或意見。                                                 |
| `Timestamp`            | `timestamp without time zone`| 操作時間，記錄操作發生的時間，預設為當前時間。                                         |

## ReviewSteps (審核步驟資料表)

| 欄位名稱 (Column Name) | 資料型態 (Data Type) | 描述 (Description)                                                               |
| :--------------------- | :------------------- | :------------------------------------------------------------------------------- |
| `StepId`               | `integer`            | 步驟流水號，主鍵，自動遞增，用於唯一識別審核步驟。                                     |
| `TaskId`               | `integer`            | 任務流水號，對應 ReviewTasks 表的 TaskId。                                         |
| `Name`                 | `character varying`  | 步驟名稱，描述步驟的具體內容。                                                       |
| `Sequence`             | `integer`            | 步驟順序，用於定義步驟的執行順序。                                                   |
| `TimeLimit`            | `integer`            | 時間限制，以小時為單位，指定步驟必須完成的時間。                                         |
| `Status`               | `character varying`  | 步驟狀態，可選值為 waiting（等待中）、active（進行中）、completed（已完成）、skipped（已跳過）、rejected（已拒絕）。 |

## ReviewTasks (審核任務資料表)

| 欄位名稱 (Column Name)     | 資料型態 (Data Type)         | 描述 (Description)                                                               |
| :------------------------- | :--------------------------- | :------------------------------------------------------------------------------- |
| `TaskId`                   | `integer`                    | 任務流水號，主鍵，自動遞增，用於唯一識別審核任務。                                     |
| `SiteCode`                 | `character varying`          | 案場號碼，與 Sites 表的 SiteCode 關聯，指定任務的案場。                                |
| `Title`                    | `character varying`          | 任務標題，描述任務的主要內容。                                                       |
| `Description`              | `text`                       | 任務描述，提供任務的詳細資訊。                                                       |
| `CreatedTime`              | `timestamp without time zone`| 創建時間，記錄任務創建的時間，預設為當前時間。                                         |
| `Deadline`                 | `timestamp without time zone`| 截止日期，任務必須完成的時間。                                                       |
| `Status`                   | `character varying`          | 任務狀態，enable（啟用）、disable（停用）、deleted(刪除)                               |
| `CreatedUserInfoId`        | `character varying`          | 創建者編號，對應 UserInfo 表的 UserInfoId。                                        |

## SYS_Code (系統代碼資料表)

| 欄位名稱 (Column Name)     | 資料型態 (Data Type)         | 描述 (Description)                               |
| :------------------------- | :--------------------------- | :----------------------------------------------- |
| `Type`                     | `character varying`          | 代碼類型，主鍵的一部分，用於區分不同的代碼類別。             |
| `Code`                     | `character varying`          | 代碼值，主鍵的一部分，用於唯一識別具體代碼。                 |
| `ParentCode`               | `character varying`          | 上層代碼，可為 NULL，表示代碼的層級關係。                |
| `CodeDesc`                 | `character varying`          | 代碼描述，提供代碼的詳細解釋。                           |
| `IsActive`                 | `boolean`                    | 是否啟用，true 表示啟用，false 表示停用，預設為 true。         |
| `CodeOrder`                | `smallint`                   | 排序序號，用於控制代碼的顯示順序。                         |
| `CreatedUserInfoId`        | `character varying`          | 建立者，記錄創建此資料的員工編號。                         |
| `CreatedTime`              | `timestamp without time zone`| 創建時間，記錄資料創建的時間。                             |
| `UpdatedUserInfoId`        | `character varying`          | 更新者，記錄最後更新此資料的員工編號。                       |
| `UpdatedTime`              | `timestamp without time zone`| 更新時間，記錄資料最後更新的時間。                           |

## SYS_MenuFunc (系統選單功能資料表)

| 欄位名稱 (Column Name) | 資料型態 (Data Type) | 描述 (Description)                                   |
| :--------------------- | :------------------- | :--------------------------------------------------- |
| `System`               | `character varying`  | 系統名稱，主鍵的一部分，用於區分不同系統。                     |
| `FuncId`               | `character varying`  | 功能項目識別編號，主鍵的一部分，用於唯一識別功能項目。             |
| `FuncName`             | `character varying`  | 功能項目名稱。                                       |
| `FuncOrder`            | `integer`            | 功能項目排序，用於控制選單顯示順序。                           |
| `ParentFuncId`         | `character varying`  | 上層功能項目識別編號，用於表示功能項目的層級結構。                 |

## SYS_RoleGroup (系統角色群組資料表)

| 欄位名稱 (Column Name)     | 資料型態 (Data Type)         | 描述 (Description)                                       |
| :------------------------- | :--------------------------- | :------------------------------------------------------- |
| `System`                   | `character varying`          | 系統名稱，主鍵的一部分，用於區分不同系統。                       |
| `SiteCode`                 | `character varying`          | 案場號碼，可為 NULL 表示全局角色，對應 Sites 表的 SiteCode。       |
| `RoleGroupId`              | `character varying`          | 角色群組識別編號，主鍵的一部分，用於唯一識別角色群組。               |
| `Name`                     | `character varying`          | 角色群組名稱。                                         |
| `IsAdmin`                  | `boolean`                    | 是否為管理者角色，true 表示是，false 表示否。                    |
| `CreatedUserInfoId`        | `character varying`          | 建立者，記錄創建此角色群組的員工編號。                         |
| `CreatedTime`              | `timestamp without time zone`| 創建時間，記錄角色群組創建的時間。                             |
| `UpdatedUserInfoId`        | `character varying`          | 更新者，記錄最後更新此角色群組的員工編號。                       |
| `UpdatedTime`              | `timestamp without time zone`| 更新時間，記錄角色群組最後更新的時間。                         |

## SYS_RoleGroupPermission (系統角色群組權限資料表)

| 欄位名稱 (Column Name)  | 資料型態 (Data Type) | 描述 (Description)                                                          |
| :---------------------- | :------------------- | :-------------------------------------------------------------------------- |
| `System`                | `character varying`  | 系統名稱，主鍵的一部分，用於區分不同系統。                                        |
| `RoleGroupId`           | `character varying`  | 角色群組識別編號，主鍵的一部分，對應 SYS_RoleGroup 表的 RoleGroupId。               |
| `FuncId`                | `character varying`  | 功能項目識別編號，主鍵的一部分，對應 SYS_MenuFunc 表的 FuncId。                   |
| `Crud`                  | `text`               | CRUD 權限，儲存對功能的創建(C)、讀取(R)、更新(U)、刪除(D)權限設定。                |
| `IsActive`              | `boolean`            | 是否啟用，true 表示啟用，false 表示停用，預設為 false。                           |

## SYS_RoleGroupUser (系統角色群組使用者關聯資料表)

| 欄位名稱 (Column Name) | 資料型態 (Data Type) | 描述 (Description)                                                          |
| :--------------------- | :------------------- | :-------------------------------------------------------------------------- |
| `System`               | `character varying`  | 系統名稱，主鍵的一部分，用於區分不同系統。                                        |
| `RoleGroupId`          | `character varying`  | 角色群組識別編號，主鍵的一部分，對應 SYS_RoleGroup 表的 RoleGroupId。               |
| `UserInfoId`           | `character varying`  | 使用者編號，主鍵的一部分，對應 UserInfo 表的 UserInfoId。                         |

## SYS_SystemSetting (系統設定資料表)

| 欄位名稱 (Column Name)     | 資料型態 (Data Type)         | 描述 (Description)                                                          |
| :------------------------- | :--------------------------- | :-------------------------------------------------------------------------- |
| `Type`                     | `character varying`          | 類別，用於區分不同的設定類型。                                                  |
| `Key`                      | `character varying`          | 鍵，主鍵的一部分，用於唯一識別系統設定項目。                                      |
| `Value`                    | `character varying`          | 值，儲存設定的具體值，可為空。                                                  |
| `Name`                     | `character varying`          | 詳細名稱，描述設定的具體用途或含義。                                              |
| `IsEnabled`                | `boolean`                    | 是否啟用，true 表示啟用，false 表示停用，預設為 true。                            |
| `CreatedUserInfoId`        | `character varying`          | 建立者，記錄創建此設定的員工編號，對應 UserInfo 表的 UserInfoId。                   |
| `CreatedTime`              | `timestamp without time zone`| 創建時間，記錄設定創建的時間。                                                  |
| `UpdatedUserInfoId`        | `character varying`          | 更新者，記錄最後更新此設定的員工編號，對應 UserInfo 表的 UserInfoId。                 |
| `UpdatedTime`              | `timestamp without time zone`| 更新時間，記錄設定最後更新的時間。                                                |

## SiteCrmOptions (案場CRM選項資料表)

| 欄位名稱 (Column Name)     | 資料型態 (Data Type)         | 描述 (Description)                             |
| :------------------------- | :--------------------------- | :--------------------------------------------- |
| `SiteCrmOptionId`          | `bigint`                     | 主鍵，選項值唯一識別碼                             |
| `SiteCode`                 | `character varying`          | 關聯的案場代碼 (參考 Sites 表的 SiteCode)          |
| `CrmOptionTypeId`          | `bigint`                     | 關聯的選項類型ID (參考 CrmOptionTypes 表)         |
| `OptionValue`              | `character varying`          | 實際顯示在下拉選單中的文字 (例如: 20-30坪, 3房2廳)    |
| `SortOrder`                | `integer`                    | 在同一個下拉選單中的顯示順序，數字越小越前面            |
| `IsActive`                 | `boolean`                    | 此選項是否啟用                                   |
| `CreatedUserInfoId`        | `character varying`          | 建立者，記錄創建此資料的員工編號。                   |
| `CreateTime`               | `timestamp without time zone`| 創建時間，記錄資料創建的時間。                       |
| `UpdatedUserInfoId`        | `character varying`          | 更新者，記錄最後更新此資料的員工編號。                 |
| `UpdateTime`               | `timestamp without time zone`| 更新時間，記錄資料最後更新的時間。                     |

## Sites (案場資料表)

| 欄位名稱 (Column Name)        | 資料型態 (Data Type)         | 描述 (Description)                                                               |
| :---------------------------- | :--------------------------- | :------------------------------------------------------------------------------- |
| `SiteCode`                    | `character varying`          | 案場號碼，主鍵，用於唯一識別案場。                                                   |
| `CompanyId`                   | `character varying`          | 執行公司別，對應 Company 表的 CompanyId，記錄案場所屬公司。                            |
| `SiteName`                    | `character varying`          | 案名，案場名稱。                                                                 |
| `PromotionType`               | `character varying`          | 推案型態，描述案場的推案方式或類型。                                                 |
| `Chairman`                    | `character varying`          | 主委，儲存單一人員 ID，對應 UserInfo 表的 UserInfoId。                               |
| `ViceChairman`                | `character varying`          | 副主委，儲存單一人員 ID，對應 UserInfo 表的 UserInfoId。                               |
| `ProjectManager`              | `character varying`          | 專案，儲存單一人員 ID，對應 UserInfo 表的 UserInfoId。                               |
| `DeputyProjectManager`        | `character varying`          | 副專，儲存單一人員 ID，對應 UserInfo 表的 UserInfoId。                               |
| `BusinessIds`                 | `text`                       | 業務，儲存以逗號分隔的字串（如 "pm8327,bnv783,oia198027,..."），對應多個 UserInfo 表的 UserInfoId。 |
| `RunnerIds`                   | `text`                       | 跑單，儲存以逗號分隔的字串（如 "pm8327,bnv783,oia198027,..."），對應多個 UserInfo 表的 UserInfoId。 |
| `ReceptionCenter`             | `character varying`          | 接待中心，案場的接待中心名稱或地址。                                                 |
| `SitePhone`                   | `character varying`          | 案場電話，案場的聯絡電話。                                                         |
| `Broker`                      | `character varying`          | 經紀人，負責案場的經紀人名稱或編號。                                                 |
| `City`                        | `character varying`          | 所在縣市，案場所在的縣市。                                                         |
| `District`                    | `character varying`          | 所在區域，案場所在的區域或鄉鎮區。                                                   |
| `Developer`                   | `character varying`          | 投資興建，開發商或投資方的名稱。                                                     |
| `SiteLocation`                | `text`                       | 基地位置，案場的具體地址。                                                         |
| `LandArea`                    | `numeric`                    | 基地面積，案場用地的面積，單位為平方公尺。                                             |
| `Zoning`                      | `character varying`          | 使用分區，案場所在的使用分區類型。                                                   |
| `PublicFacilityRatio`         | `numeric`                    | 公設比，公共設施比例（百分比）。                                                     |
| `Structure`                   | `character varying`          | 結構，案場的建築結構類型。                                                         |
| `AboveGroundFloors`           | `character varying`          | 規劃樓層(上層)，地上樓層數。                                                       |
| `BelowGroundFloors`           | `character varying`          | 規劃樓層(下層)，地下樓層數。                                                       |
| `PlannedStoreUnits`           | `integer`                    | 規劃戶車(店面)，規劃的店面戶數。                                                     |
| `PlannedResidentialUnits`     | `integer`                    | 規劃戶車(住家)，規劃的住宅戶數。                                                     |
| `PlannedParkingSpaces`        | `integer`                    | 規劃戶車(車位)，規劃的車位數。                                                       |
| `UnitSize`                    | `character varying`          | 坪數，案場的單位面積（坪數）。                                                       |
| `TotalSalePrice`              | `numeric`                    | 全案總銷，案場的總銷售金額。                                                         |
| `ParkingType`                 | `character varying`          | 車位類別，案場的車位類型（如機械車位、平面車位）。                                       |
| `ContractPeriod`              | `date`                       | 合約期間，案場合約的期間（天數或月數）。                                               |
| `ExtensionPeriod`             | `date`                       | 展延期間，合約展延的期間（天數或月數）。                                               |
| `SellableTotalPrice`          | `numeric`                    | 可售總銷，可供銷售的總金額。                                                         |
| `ServiceFeeCalculation`       | `character varying`          | 服務費計算，服務費的計算方式。                                                       |
| `ServiceFeeRate`              | `numeric`                    | 服務費率，服務費的比率（百分比）。                                                     |
| `ReserveAmount`               | `numeric`                    | 保留款，案場的保留款金額。                                                           |
| `AdvertisingBudget`           | `numeric`                    | 應編廣告預算，案場應編列的廣告預算金額。                                               |
| `AdvertisingBudgetRate`       | `numeric`                    | 廣告預算率，廣告預算的比率（百分比）。                                                 |
| `ExcessPriceAllocation`       | `character varying`          | 超價款分配，超價款的分配方式。                                                       |
| `ContractedAmount`            | `numeric`                    | 已發包金額，已發包的金額。                                                           |
| `ControlReserveRate`          | `numeric`                    | 控存率，控制儲備的比率（百分比）。                                                     |
| `PaidAmount`                  | `numeric`                    | 已請款金額，已請款的金額，預設為 0。                                                   |
| `CreatedUserInfoId`           | `character varying`          | 建立者，記錄創建此資料的員工編號，對應 UserInfo 表的 UserInfoId。                        |
| `CreatedTime`                 | `timestamp without time zone`| 創建時間，記錄資料創建的時間。                                                       |
| `UpdatedUserInfoId`           | `character varying`          | 更新者，記錄最後更新此資料的員工編號，對應 UserInfo 表的 UserInfoId。                      |
| `UpdatedTime`                 | `timestamp without time zone`| 更新時間，記錄資料最後更新的時間。                                                     |

## Supplier (供應商資料表)

| 欄位名稱 (Column Name)    | 資料型態 (Data Type)         | 描述 (Description)                                                |
| :------------------------ | :--------------------------- | :---------------------------------------------------------------- |
| `SupplierId`              | `integer`                    | 供應商唯一識別碼，自動遞增                                            |
| `Name`                    | `character varying`          | 供應商名稱（個人或公司名稱）                                          |
| `PersonType`              | `character varying`          | 人別（個人或法人，例如：個人、公司）                                    |
| `ContactPerson`           | `character varying`          | 負責人姓名                                                          |
| `IdType`                  | `character varying`          | 證號類型（例如：身分證、統一編號）                                      |
| `IdNumber`                | `character varying`          | 證號（身分證號或統一編號）                                            |
| `CompanyPhone`            | `character varying`          | 公司電話                                                            |
| `Address`                 | `text`                       | 通訊地址                                                            |
| `ContactName`             | `character varying`          | 聯絡窗口姓名                                                        |
| `ContactPhone1`           | `character varying`          | 聯絡電話1                                                           |
| `ContactPhone2`           | `character varying`          | 聯絡電話2                                                           |
| `Email`                   | `character varying`          | 電子郵件                                                            |
| `BankName`                | `character varying`          | 銀行名稱                                                            |
| `BankBranch`              | `character varying`          | 銀行分行名稱                                                        |
| `AccountName`             | `character varying`          | 收款戶名                                                            |
| `CreatedUserInfoId`       | `character varying`          | 資料創建人識別碼（參考 UserInfo 表的 UserInfoId）                       |
| `UpdatedUserInfoId`       | `character varying`          | 資料更新人識別碼（參考 UserInfo 表的 UserInfoId）                       |
| `UpdatedTime`             | `timestamp without time zone`| 資料更新時間                                                          |
| `CreatedTime`             | `timestamp without time zone`| 資料創建時間                                                          |

## SupplierFile (供應商檔案資料表)

| 欄位名稱 (Column Name)    | 資料型態 (Data Type)         | 描述 (Description)                                                |
| :------------------------ | :--------------------------- | :---------------------------------------------------------------- |
| `SupplierFileId`          | `integer`                    | 檔案唯一識別碼，自動遞增                                              |
| `SupplierId`              | `integer`                    | 關聯的供應商識別碼，外鍵                                              |
| `FormType`                | `character varying`          | 表單類別（例如：合約、證明文件）                                      |
| `FileName`                | `character varying`          | 檔案名稱                                                            |
| `FilePath`                | `text`                       | 檔案儲存路徑                                                        |
| `Remark`                  | `text`                       | 備註                                                                |
| `Agent`                   | `character varying`          | 承辦人姓名                                                          |
| `CreatedUserInfoId`       | `character varying`          | 資料創建人識別碼（參考 UserInfo 表的 UserInfoId）                       |
| `UpdatedUserInfoId`       | `character varying`          | 資料更新人識別碼（參考 UserInfo 表的 UserInfoId）                       |
| `UpdatedTime`             | `timestamp without time zone`| 資料更新時間                                                          |
| `CreatedTime`             | `timestamp without time zone`| 資料創建時間                                                          |

## Units (房屋單位資料表)

| 欄位名稱 (Column Name)          | 資料型態 (Data Type)         | 描述 (Description)                                                               |
| :------------------------------ | :--------------------------- | :------------------------------------------------------------------------------- |
| `UnitId`                        | `integer`                    | 房屋單位唯一識別碼 (主鍵, 自動遞增)。                                                |
| `FloorId`                       | `integer`                    | 所屬樓層識別碼 (參考 Floors 表)。**注意：車位單位應參考 FloorType 為 '停車場' 的樓層ID**   |
| `BuildingId`                    | `integer`                    | 所屬建築物識別碼 (參考 Buildings 表) - 方便查詢用。                                  |
| `SiteCode`                      | `character varying`          | 所屬案場編號 (參考 Sites 表) - 方便查詢用。                                        |
| `UnitNumber`                    | `character varying`          | 戶號 (例如: "A", "B", "C" 或車位編號 "P01", "A01")。                                |
| `UnitType`                      | `character varying`          | 單位類型 (例如: "住宅", "店面", "車位" - 建議關聯 SYS_Code)。                          |
| `Layout`                        | `character varying`          | 格局 (例如: "3房2廳2衛"，車位則可填入車位類型如 "坡道平面")。                           |
| `Orientation`                   | `character varying`          | 座向 (住宅用，車位可為空)。                                                         |
| `MainArea`                      | `numeric`                    | 主建物坪數 (住宅用，車位可為空)。                                                     |
| `AuxiliaryArea`                 | `numeric`                    | 附屬建物坪數 (住宅用，車位可為空)。                                                   |
| `PublicAreaShare`               | `numeric`                    | 公設分攤坪數 (住宅用，車位可為空)。                                                   |
| `TotalArea`                     | `numeric`                    | 權狀總坪數 (住宅用，車位可填入車位面積)。                                             |
| `ListPrice`                     | `numeric`                    | 表價 (牌價)。                                                                      |
| `MinimumPrice`                  | `numeric`                    | 底價。                                                                           |
| `TransactionPrice`              | `numeric`                    | 實際成交價 (成交後填入)。                                                            |
| `Status`                        | `character varying`          | 單位目前的銷售狀態 (例如: '可售', '已預訂', '已售', '保留' - 建議關聯 SYS_Code)。         |
| `IsPublicAreaIncluded`          | `boolean`                    | 權狀是否含公設 (True/False，住宅用，車位通常為 False)。                               |
| `AssociatedParkingSpaceIds`     | `text`                       | **關聯的停車位ID列表**，以逗號分隔 (參考 ParkingSpaces 表)。**住宅單位用於記錄建議搭配的車位；車位單位用於記錄對應的實體車位** |
| `Remarks`                       | `text`                       | 備註。                                                                           |
| `CreatedTime`                   | `timestamp without time zone`| 資料創建時間 (TIMESTAMP WITHOUT TIME ZONE)。                                       |
| `UpdatedTime`                   | `timestamp without time zone`| 資料最後更新時間 (TIMESTAMP WITHOUT TIME ZONE)。                                     |
| `CreatedUserInfoId`             | `character varying`          | 建立者 (參考 UserInfo 表)。                                                       |
| `UpdatedUserInfoId`             | `character varying`          | 更新者 (參考 UserInfo 表)。                                                       |

**設計說明：**
- 本表為統一的銷售單位管理表，可儲存住宅、店面、車位等所有可銷售單位
- 當 `UnitType` = "車位" 時，透過 `AssociatedParkingSpaceIds` 關聯到 `ParkingSpaces` 表的實體車位
- 所有銷售相關資訊 (價格、狀態) 統一在此表管理，避免與 `ParkingSpaces` 表重複

## UserDepartments (使用者部門關聯資料表)

| 欄位名稱 (Column Name) | 資料型態 (Data Type) | 描述 (Description)                                                |
| :--------------------- | :------------------- | :---------------------------------------------------------------- |
| `UserInfoId`           | `character varying`  | 員工編號，主鍵的一部分，對應 UserInfo 表的 UserInfoId。                 |
| `DepartmentId`         | `character varying`  | 部門編號，主鍵的一部分，對應 Departments 表的 DepartmentId。          |
| `IsPrimary`            | `boolean`            | 是否為主要部門，true 表示是，false 表示否，預設為 false。             |

## UserInfo (使用者資訊資料表)

| 欄位名稱 (Column Name)           | 資料型態 (Data Type)         | 描述 (Description)                                        |
| :------------------------------- | :--------------------------- | :-------------------------------------------------------- |
| `UserInfoId`                     | `character varying`          | 員工編號，主鍵，用於唯一識別員工。                              |
| `CompanyId`                      | `character varying`          | 公司編號，對應 Company 表的 CompanyId，記錄員工所屬的主要公司。     |
| `Password`                       | `character varying`          | 密碼，儲存加密後的密碼內容。                                  |
| `Name`                           | `character varying`          | 員工姓名。                                              |
| `Gender`                         | `character varying`          | 性別，例如 M（男）、F（女）。                                |
| `BirthDate`                      | `date`                       | 出生日期。                                              |
| `TelephoneNumber`                | `character varying`          | 電話號碼。                                              |
| `MobileNumber`                   | `character varying`          | 手機號碼。                                              |
| `Email`                          | `character varying`          | 電子郵件地址。                                            |
| `RegisteredAddress`              | `text`                       | 戶籍地址。                                              |
| `MailingAddress`                 | `text`                       | 通訊地址。                                              |
| `EmergencyContactName`           | `character varying`          | 緊急聯絡人姓名。                                          |
| `EmergencyContactPhone`          | `character varying`          | 緊急聯絡人電話。                                          |
| `EmergencyContactRelation`       | `character varying`          | 與緊急聯絡人的關係，例如父母、配偶等。                          |
| `ServiceUnit`                    | `character varying`          | 所屬案場。                                              |
| `HireDate`                       | `date`                       | 到職日期。                                              |
| `Status`                         | `boolean`                    | 狀態，true 表示啟用，false 表示停用。                         |
| `CreatedTime`                    | `timestamp without time zone`| 創建時間，記錄資料創建的時間。                              |
| `UpdatedTime`                    | `timestamp without time zone`| 更新時間，記錄資料最後更新的時間。                            |
| `CreatedUserInfoId`              | `character varying`          | 建立者，記錄創建此資料的員工編號。                          |
| `UpdatedUserInfoId`              | `character varying`          | 更新者，記錄最後更新此資料的員工編號。                        |
| `LastLoginIP`                    | `character varying`          | 最後一次登入的 IP 地址。                                   |
| `LastLoginTime`                  | `timestamp without time zone`| 最後一次登入的時間。                                        |
| `LastLogoutTime`                 | `timestamp without time zone`| 最後一次登出的時間。                                        |
| `LoginFailedCount`               | `smallint`                   | 登入失敗次數加總，用於追蹤密碼錯誤次數。                        |
| `IsInside`                       | `boolean`                    | 使否為內場/外場人員                                       |
| `IsM365`                         | `boolean`                    | 是否為 M365 帳號，true 表示是，false 表示否。                 |
| `IsEmailNotificationEnabled`     | `boolean`                    | 是否需要發送電子郵件通知，true 表示是，false 表示否。             |
| `Identity`                       | `character varying`          | 身分證字號                                              |

## UserJobTitles (使用者職位關聯資料表)

| 欄位名稱 (Column Name) | 資料型態 (Data Type) | 描述 (Description)                                                |
| :--------------------- | :------------------- | :---------------------------------------------------------------- |
| `UserInfoId`           | `character varying`  | 員工編號，主鍵的一部分，對應 UserInfo 表的 UserInfoId。                 |
| `JobTitleId`           | `character varying`  | 職位編號，主鍵的一部分，對應 JobTitles 表的 JobTitleId。              |
| `IsPrimary`            | `boolean`            | 是否為主要職位，true 表示是，false 表示否，預設為 false。             |

## UserPasswordHistory (使用者密碼歷史記錄資料表)

| 欄位名稱 (Column Name)        | 資料型態 (Data Type)         | 描述 (Description)                                     |
| :---------------------------- | :--------------------------- | :----------------------------------------------------- |
| `UserPasswordHistoryId`       | `bigint`                     | 流水號，主鍵，用於唯一識別密碼記錄。                           |
| `UserInfoId`                  | `character varying`          | 員工編號，對應 UserInfo 表的 UserInfoId。                    |
| `Password`                    | `character varying`          | 歷史密碼，儲存加密後的密碼內容。                             |
| `CreatedUserInfoId`           | `character varying`          | 建立者，記錄創建此記錄的員工編號。                           |
| `CreatedTime`                 | `timestamp without time zone`| 創建時間，記錄密碼記錄創建的時間。                           |

## UserPasswordResetCodes (使用者密碼重設驗證碼資料表)

| 欄位名稱 (Column Name)         | 資料型態 (Data Type)         | 描述 (Description)                                        |
| :----------------------------- | :--------------------------- | :-------------------------------------------------------- |
| `UserPasswordResetCodesId`     | `uuid`                       | 流水號，主鍵，使用 UUID 格式唯一識別驗證碼記錄。                  |
| `UserInfoId`                   | `character varying`          | 員工編號，對應 UserInfo 表的 UserInfoId。                     |
| `VerificationCode`             | `character varying`          | 驗證碼，通常為 6 位數。                                     |
| `ExpirationTime`               | `timestamp without time zone`| 驗證碼到期時間，過期後無法使用。                                |
| `IsUsed`                       | `boolean`                    | 是否已使用，true 表示已使用，false 表示未使用，預設為 false。         |

## \_\_EFMigrationsHistory (Entity Framework 遷移歷史記錄資料表)

| 欄位名稱 (Column Name) | 資料型態 (Data Type) | 描述 (Description) |
| :--------------------- | :------------------- | :----------------- |
| `MigrationId`          | `character varying`  |                    |
| `ProductVersion`       | `character varying`  |                    |

## WeeklyReports (週報資料表)

| 欄位名稱 (Column Name)     | 資料型態 (Data Type)         | 描述 (Description)                                     |
| :------------------------- | :--------------------------- | :----------------------------------------------------- |
| `WeeklyReportId`           | `integer`                    | 週報唯一識別碼 (主鍵, 自動遞增)。                          |
| `SiteCode`                 | `character varying`          | 案場編號 (參考 Sites 表)。                              |
| `WeekNumber`               | `integer`                    | 週次 (例如: 第17週)。                                   |
| `StartDate`                | `date`                       | 週報統計開始日期 (週一)。                                 |
| `EndDate`                  | `date`                       | 週報統計結束日期 (週日)。                                 |
| `Year`                     | `integer`                    | 年份 (例如: 2024)。                                    |
| `Title`                    | `character varying`          | 週報標題 (例如: "第17週 週報表")。                        |
| `StockIndex`               | `numeric`                    | 股市走勢指數。                                          |
| `Status`                   | `character varying`          | 週報狀態 (例如: '草稿', '已發布', '已作廢' - 建議關聯 SYS_Code)。 |
| `CreatedTime`              | `timestamp without time zone`| 週報創建時間。                                          |
| `UpdatedTime`              | `timestamp without time zone`| 週報最後更新時間。                                       |
| `CreatedUserInfoId`        | `character varying`          | 建立者 (參考 UserInfo 表)。                            |
| `UpdatedUserInfoId`        | `character varying`          | 更新者 (參考 UserInfo 表)。                            |

## Units (房屋單位資料表)

| 欄位名稱 (Column Name)           | 資料型態 (Data Type)         | 描述 (Description)                                                               |
| :------------------------------- | :--------------------------- | :------------------------------------------------------------------------------- |
| `UnitId`                         | `integer`                    | 房屋單位唯一識別碼 (主鍵, 自動遞增)。                                                 |
| `FloorId`                        | `integer`                    | 所屬樓層識別碼 (參考 Floors 表中 FloorType 為 '住宅'/'商業' 的樓層ID)。                  |
| `BuildingId`                     | `integer`                    | 所屬建築物識別碼 (參考 Buildings 表) - 方便查詢用。                                    |
| `SiteCode`                       | `character varying`          | 所屬案場編號 (參考 Sites 表) - 方便查詢用。                                          |
| `UnitNumber`                     | `character varying`          | 戶號 (銷控表橫軸，例如: "A", "B", "C")。                                            |
| `UnitType`                       | `character varying`          | 單位類型 (例如: "住宅", "店面", "車位" - 建議關聯 SYS_Code)                            |
| `Layout`                         | `character varying`          | 格局 (例如: "3房2廳2衛")。                                                         |
| `Orientation`                    | `character varying`          | 座向。                                                                           |
| `MainArea`                       | `numeric`                    | 主建物坪數。                                                                     |
| `AuxiliaryArea`                  | `numeric`                    | 附屬建物坪數。                                                                   |
| `PublicAreaShare`                | `numeric`                    | 公設分攤坪數。                                                                   |
| `TotalArea`                      | `numeric`                    | 權狀總坪數。                                                                     |
| `ListPrice`                      | `numeric`                    | 表價 (牌價)。                                                                    |
| `MinimumPrice`                   | `numeric`                    | 底價。                                                                           |
| `TransactionPrice`               | `numeric`                    | 實際成交價 (成交後填入)。                                                          |
| `Status`                         | `character varying`          | 單位目前的**獨立銷售狀態** (例如: '可售', '已預訂', '已售', '保留' - 建議關聯 SYS_Code)。     |
| `IsPublicAreaIncluded`           | `boolean`                    | 權狀是否含公設 (True/False)。                                                      |
| `AssociatedParkingSpaceIds`      | `text`                       | 關聯的停車位ID列表，以逗號分隔 (參考 ParkingSpaces 表)。住宅單位用於記錄建議搭配的車位；車位單位用於記錄對應的實體車位 |
| `Remarks`                        | `text`                       | 備註。                                                                           |
| `CreatedTime`                    | `timestamp without time zone`| 資料創建時間 (TIMESTAMP WITHOUT TIME ZONE)。                                        |
| `UpdatedTime`                    | `timestamp without time zone`| 資料最後更新時間 (TIMESTAMP WITHOUT TIME ZONE)。                                      |
| `CreatedUserInfoId`              | `character varying`          | 建立者 (參考 UserInfo 表)。                                                       |
| `UpdatedUserInfoId`              | `character varying`          | 更新者 (參考 UserInfo 表)。                                                       |
| `SmallPublicArea`                | `numeric`                    | 小公面積 (坪)                                                                    |
| `LargePublicArea`                | `numeric`                    | 大公面積 (坪)                                                                    |
| `AwningArea`                     | `numeric`                    | 雨遮面積 (坪)                                                                    |
| `BalconyArea`                    | `numeric`                    | 陽台面積 (坪)                                                                    |

## DailyReports (日報資料表)

| 欄位名稱 (Column Name)     | 資料型態 (Data Type)         | 描述 (Description)                                     |
| :------------------------- | :--------------------------- | :----------------------------------------------------- |
| `DailyReportId`            | `integer`                    | 日報唯一識別碼 (主鍵, 自動遞增)。                          |
| `SiteCode`                 | `character varying`          | 案場編號 (參考 Sites 表)。                              |
| `ReportDate`               | `date`                       | 日報日期。                                             |
| `Title`                    | `character varying`          | 日報標題 (例如: "2024/07/22 日報表")。                   |
| `Weather`                  | `character varying`          | 天氣狀況。                                             |
| `VisitorCount`             | `integer`                    | 當日來客數。                                           |
| `CallCount`                | `integer`                    | 當日來電數。                                           |
| `LeadCount`                | `integer`                    | 當日留單數。                                           |
| `TransactionCount`         | `integer`                    | 當日成交數。                                           |
| `Notes`                    | `text`                       | 備註。                                                |
| `Status`                   | `character varying`          | 日報狀態 (例如: '草稿', '已發布', '已作廢' - 建議關聯 SYS_Code)。 |
| `CreatedTime`              | `timestamp without time zone`| 日報創建時間。                                          |
| `UpdatedTime`              | `timestamp without time zone`| 日報最後更新時間。                                       |
| `CreatedUserInfoId`        | `character varying`          | 建立者 (參考 UserInfo 表)。                            |
| `UpdatedUserInfoId`        | `character varying`          | 更新者 (參考 UserInfo 表)。                            |

</rewritten_file> 