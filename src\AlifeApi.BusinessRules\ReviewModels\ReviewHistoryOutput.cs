using System;

namespace AlifeApi.BusinessRules.ReviewModels
{
    /// <summary>
    /// 審核歷史記錄輸出模型
    /// </summary>
    public class ReviewHistoryOutput
    {
        /// <summary>
        /// 歷史記錄ID
        /// </summary>
        public int HistoryId { get; set; }

        /// <summary>
        /// 審核任務ID
        /// </summary>
        public int TaskId { get; set; }

        /// <summary>
        /// 審核步驟ID
        /// </summary>
        public int? StepId { get; set; }

        /// <summary>
        /// 審核步驟名稱
        /// </summary>
        public string StepName { get; set; }

        /// <summary>
        /// 操作者ID
        /// </summary>
        public string UserInfoId { get; set; }

        /// <summary>
        /// 操作者姓名
        /// </summary>
        public string UserName { get; set; }

        /// <summary>
        /// 操作類型
        /// </summary>
        public string Action { get; set; }

        /// <summary>
        /// 評論內容
        /// </summary>
        public string Comment { get; set; }

        /// <summary>
        /// 操作時間
        /// </summary>
        public DateTime? Timestamp { get; set; }
    }
} 
