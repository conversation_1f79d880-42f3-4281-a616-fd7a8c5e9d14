using System.Text.Json.Serialization;

namespace AlifeApi.BusinessRules.UserInfoModels
{
    /// <summary>
    /// 使用者新增輸入資料 (PostgreSQL版本)
    /// </summary>
    public class UserInfoCreateInputPg
    {
        /// <summary>
        /// 使用者ID
        /// </summary>
        [JsonPropertyName("UserInfoId")]
        public string UserInfoId { get; set; }

        /// <summary>
        /// 公司ID
        /// </summary>
        [JsonPropertyName("CompanyId")]
        public string CompanyId { get; set; }

        /// <summary>
        /// 密碼
        /// </summary>
        [JsonPropertyName("Password")]
        public string Password { get; set; }

        /// <summary>
        /// 姓名
        /// </summary>
        [JsonPropertyName("Name")]
        public string Name { get; set; }

        /// <summary>
        /// 性別
        /// </summary>
        [JsonPropertyName("Gender")]
        public string Gender { get; set; }

        /// <summary>
        /// 身分證字號
        /// </summary>
        [JsonPropertyName("Identity")]
        public string Identity { get; set; }

        /// <summary>
        /// 出生日期
        /// </summary>
        [JsonPropertyName("BirthDate")]
        public DateOnly? BirthDate { get; set; }

        /// <summary>
        /// 市話
        /// </summary>
        [JsonPropertyName("TelephoneNumber")]
        public string TelephoneNumber { get; set; }

        /// <summary>
        /// 手機
        /// </summary>
        [JsonPropertyName("MobileNumber")]
        public string MobileNumber { get; set; }

        /// <summary>
        /// 電子郵件
        /// </summary>
        [JsonPropertyName("Email")]
        public string Email { get; set; }

        /// <summary>
        /// 戶籍地址
        /// </summary>
        [JsonPropertyName("RegisteredAddress")]
        public string RegisteredAddress { get; set; }

        /// <summary>
        /// 通訊地址
        /// </summary>
        [JsonPropertyName("MailingAddress")]
        public string MailingAddress { get; set; }

        /// <summary>
        /// 緊急聯絡人姓名
        /// </summary>
        [JsonPropertyName("EmergencyContactName")]
        public string EmergencyContactName { get; set; }

        /// <summary>
        /// 緊急聯絡人電話
        /// </summary>
        [JsonPropertyName("EmergencyContactPhone")]
        public string EmergencyContactPhone { get; set; }

        /// <summary>
        /// 緊急聯絡人關係
        /// </summary>
        [JsonPropertyName("EmergencyContactRelation")]
        public string EmergencyContactRelation { get; set; }

        /// <summary>
        /// 服務單位
        /// </summary>
        [JsonPropertyName("ServiceUnit")]
        public string ServiceUnit { get; set; }

        /// <summary>
        /// 到職日期
        /// </summary>
        [JsonPropertyName("HireDate")]
        public DateOnly? HireDate { get; set; }

        /// <summary>
        /// 是否啟用
        /// </summary>
        [JsonPropertyName("Status")]
        public bool? Status { get; set; }

        /// <summary>
        /// 是否為內部人員
        /// </summary>
        [JsonPropertyName("IsInside")]
        public bool? IsInside { get; set; }

        /// <summary>
        /// 是否啟用M365
        /// </summary>
        [JsonPropertyName("IsM365")]
        public bool? IsM365 { get; set; }

        /// <summary>
        /// 是否啟用郵件通知
        /// </summary>
        [JsonPropertyName("IsEmailNotificationEnabled")]
        public bool? IsEmailNotificationEnabled { get; set; }

        /// <summary>
        /// 部門ID
        /// </summary>
        [JsonPropertyName("DepartmentId")]
        public string DepartmentId { get; set; }

        /// <summary>
        /// 職稱ID
        /// </summary>
        [JsonPropertyName("JobTitleId")]
        public string JobTitleId { get; set; }

        /// <summary>
        /// 角色群組ID列表
        /// </summary>
        [JsonPropertyName("RoleGroupIds")]
        public List<string> RoleGroupIds { get; set; } = new();
    }
} 
