﻿using System;
using System.Collections.Generic;

namespace AlifeApi.DataAccess.ProjectContext
{
    /// <summary>
    /// 系統設定資料
    /// </summary>
    public partial class SysSystemSetting
    {
        /// <summary>
        /// 類別
        /// </summary>
        public string Type { get; set; }

        /// <summary>
        /// 鍵
        /// </summary>
        public string Key { get; set; }

        /// <summary>
        /// 值
        /// </summary>
        public string Value { get; set; }

        /// <summary>
        /// 詳細名稱
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// 是否啟用
        /// </summary>
        public bool? IsEnabled { get; set; }

        /// <summary>
        /// 建立者
        /// </summary>
        public string CreatedUserId { get; set; }

        /// <summary>
        /// 建立時間
        /// </summary>
        public DateTime CreatedTime { get; set; }

        /// <summary>
        /// 更新者
        /// </summary>
        public string UpdatedUserId { get; set; }

        /// <summary>
        /// 更新時間
        /// </summary>
        public DateTime UpdatedTime { get; set; }

    }
}
