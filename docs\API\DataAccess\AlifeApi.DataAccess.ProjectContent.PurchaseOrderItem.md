#### [AlifeApi.DataAccess](index.md 'index')
### [AlifeApi.DataAccess.ProjectContent](AlifeApi.DataAccess.ProjectContent.md 'AlifeApi.DataAccess.ProjectContent')

## PurchaseOrderItem Class

訂單項目表，記錄一張訂單包含的所有購買項目（房屋或車位）

```csharp
public class PurchaseOrderItem
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; PurchaseOrderItem
### Properties

<a name='AlifeApi.DataAccess.ProjectContent.PurchaseOrderItem.CreatedUserInfoId'></a>

## PurchaseOrderItem.CreatedUserInfoId Property

建立者 (參考 UserInfo 表)。

```csharp
public string CreatedUserInfoId { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContent.PurchaseOrderItem.CreateTime'></a>

## PurchaseOrderItem.CreateTime Property

資料創建時間 (TIMESTAMP WITHOUT TIME ZONE)。

```csharp
public System.DateTime CreateTime { get; set; }
```

#### Property Value
[System.DateTime](https://docs.microsoft.com/en-us/dotnet/api/System.DateTime 'System.DateTime')

<a name='AlifeApi.DataAccess.ProjectContent.PurchaseOrderItem.ItemPrice'></a>

## PurchaseOrderItem.ItemPrice Property

成交價格

```csharp
public decimal ItemPrice { get; set; }
```

#### Property Value
[System.Decimal](https://docs.microsoft.com/en-us/dotnet/api/System.Decimal 'System.Decimal')

<a name='AlifeApi.DataAccess.ProjectContent.PurchaseOrderItem.ParkingSpaceId'></a>

## PurchaseOrderItem.ParkingSpaceId Property

關聯的車位銷售資訊ID (參考 ParkingSpaces 表)，如果此項目是房屋，則為 NULL

```csharp
public System.Nullable<int> ParkingSpaceId { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

<a name='AlifeApi.DataAccess.ProjectContent.PurchaseOrderItem.UnitId'></a>

## PurchaseOrderItem.UnitId Property

關聯的房屋單位ID (參考 Units 表)，如果此項目是車位，則為 NULL

```csharp
public System.Nullable<int> UnitId { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')