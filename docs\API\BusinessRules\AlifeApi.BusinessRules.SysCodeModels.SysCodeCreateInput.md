#### [AlifeApi.BusinessRules](index.md 'index')
### [AlifeApi.BusinessRules.SysCodeModels](AlifeApi.BusinessRules.SysCodeModels.md 'AlifeApi.BusinessRules.SysCodeModels')

## SysCodeCreateInput Class

新增 SysCode 代碼

```csharp
public class SysCodeCreateInput
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; SysCodeCreateInput
### Properties

<a name='AlifeApi.BusinessRules.SysCodeModels.SysCodeCreateInput.Desc'></a>

## SysCodeCreateInput.Desc Property

單位代碼名稱

```csharp
public string Desc { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.SysCodeModels.SysCodeCreateInput.ParentCode'></a>

## SysCodeCreateInput.ParentCode Property

單位代碼類型

```csharp
public string ParentCode { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.SysCodeModels.SysCodeCreateInput.Type'></a>

## SysCodeCreateInput.Type Property

單位代碼類型

```csharp
public string Type { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')