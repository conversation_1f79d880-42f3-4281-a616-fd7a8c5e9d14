#### [AlifeApi.BusinessRules](index.md 'index')
### [AlifeApi.BusinessRules.RoleGroupModels](AlifeApi.BusinessRules.RoleGroupModels.md 'AlifeApi.BusinessRules.RoleGroupModels')

## RoleGroupCreateInput Class

角色權限

```csharp
public class RoleGroupCreateInput
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; RoleGroupCreateInput

Derived  
&#8627; [RoleGroupPermissionUpdateInput](AlifeApi.BusinessRules.RoleGroupModels.RoleGroupPermissionUpdateInput.md 'AlifeApi.BusinessRules.RoleGroupModels.RoleGroupPermissionUpdateInput')
### Properties

<a name='AlifeApi.BusinessRules.RoleGroupModels.RoleGroupCreateInput.FuncIds'></a>

## RoleGroupCreateInput.FuncIds Property

```csharp
public System.Collections.Generic.IEnumerable<string> FuncIds { get; }
```

#### Property Value
[System.Collections.Generic.IEnumerable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Collections.Generic.IEnumerable-1 'System.Collections.Generic.IEnumerable`1')[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Collections.Generic.IEnumerable-1 'System.Collections.Generic.IEnumerable`1')

### Remarks
前端會傳字串，只好這樣處理

<a name='AlifeApi.BusinessRules.RoleGroupModels.RoleGroupCreateInput.Name'></a>

## RoleGroupCreateInput.Name Property

角色群組名稱

```csharp
public string Name { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.RoleGroupModels.RoleGroupCreateInput.Permission'></a>

## RoleGroupCreateInput.Permission Property

權限

```csharp
public string Permission { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')