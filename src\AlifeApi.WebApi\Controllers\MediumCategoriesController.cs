using AlifeApi.BusinessRules.CategoryModels;
using AlifeApi.BusinessRules.Infrastructure;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Threading.Tasks;

namespace AlifeApi.WebApi.Controllers
{
    /// <summary>
    /// 中分類管理
    /// </summary>
    public class MediumCategoriesController : AuthenticatedController
    {
        private readonly MediumCategoryService _mediumCategoryService;

        public MediumCategoriesController(MediumCategoryService mediumCategoryService)
        {
            _mediumCategoryService = mediumCategoryService;
        }

        /// <summary>
        /// 取得中分類列表 (分頁)
        /// </summary>
        /// <param name="input">查詢條件</param>
        /// <returns>分頁的中分類列表</returns>
        [HttpPost]
        public async Task<IActionResult> GetMediumCategories([FromBody] MediumCategoryQueryInput input)
        {
            var result = await _mediumCategoryService.GetMediumCategoryListAsync(input);
            return Ok(result);
        }

        /// <summary>
        /// 根據中分類ID取得單一中分類資訊
        /// </summary>
        /// <param name="mediumCategoryId">中分類ID</param>
        /// <returns>中分類詳細資訊</returns>
        [HttpGet("{mediumCategoryId}")]
        public async Task<IActionResult> GetMediumCategory(long mediumCategoryId)
        {
            var result = await _mediumCategoryService.GetMediumCategoryByIdAsync(mediumCategoryId);
            if (result == null)
            {
                return NotFound();
            }
            return Ok(result);
        }

        /// <summary>
        /// 新增中分類
        /// </summary>
        /// <param name="input">新增中分類輸入資料</param>
        /// <returns>新增的中分類ID</returns>
        [HttpPost]
        public async Task<IActionResult> CreateMediumCategory([FromBody] MediumCategoryInput input)
        {
            try
            {
                var mediumCategoryId = await _mediumCategoryService.CreateMediumCategoryAsync(input);
                return CreatedAtAction(nameof(GetMediumCategory), new { mediumCategoryId = mediumCategoryId }, new { MediumCategoryId = mediumCategoryId });
            }
            catch (Exception ex)
            {
                return BadRequest(new { message = ex.Message });
            }
        }

        /// <summary>
        /// 更新中分類資料
        /// </summary>
        /// <param name="mediumCategoryId">要更新的中分類ID</param>
        /// <param name="input">更新的中分類資料</param>
        /// <returns>成功時返回 Ok，失敗時返回 NotFound 或 BadRequest</returns>
        [HttpPost("{mediumCategoryId}")]
        public async Task<IActionResult> UpdateMediumCategory(long mediumCategoryId, [FromBody] MediumCategoryInput input)
        {
            try
            {
                await _mediumCategoryService.UpdateMediumCategoryAsync(mediumCategoryId, input);
                return Ok();
            }
            catch (Exception ex) when (ex.Message.Contains("找不到"))
            {
                return NotFound(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                return BadRequest(new { message = ex.Message });
            }
        }

        /// <summary>
        /// 刪除中分類
        /// </summary>
        /// <param name="mediumCategoryId">要刪除的中分類ID</param>
        /// <returns>成功時返回 Ok，失敗時返回 NotFound 或 BadRequest</returns>
        [HttpDelete("{mediumCategoryId}")]
        public async Task<IActionResult> DeleteMediumCategory(long mediumCategoryId)
        {
            try
            {
                await _mediumCategoryService.DeleteMediumCategoryAsync(mediumCategoryId);
                return Ok();
            }
            catch (Exception ex) when (ex.Message.Contains("找不到"))
            {
                return NotFound(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                return BadRequest(new { message = ex.Message });
            }
        }

        /// <summary>
        /// 取得中分類下拉選單列表
        /// </summary>
        /// <param name="largeCategoryId">可選的大分類ID，用於篩選特定大分類下的中分類</param>
        /// <returns>啟用的中分類下拉選單列表</returns>
        [HttpGet]
        public async Task<IActionResult> GetMediumCategoryDropdown([FromQuery] long? largeCategoryId = null)
        {
            var result = await _mediumCategoryService.GetMediumCategoryDropdownAsync(largeCategoryId);
            return Ok(result);
        }
    }
} 
