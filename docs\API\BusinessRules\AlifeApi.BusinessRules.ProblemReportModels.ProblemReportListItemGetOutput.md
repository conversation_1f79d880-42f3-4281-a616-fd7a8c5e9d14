#### [AlifeApi.BusinessRules](index.md 'index')
### [AlifeApi.BusinessRules.ProblemReportModels](AlifeApi.BusinessRules.ProblemReportModels.md 'AlifeApi.BusinessRules.ProblemReportModels')

## ProblemReportListItemGetOutput Class

```csharp
public class ProblemReportListItemGetOutput
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; ProblemReportListItemGetOutput
### Properties

<a name='AlifeApi.BusinessRules.ProblemReportModels.ProblemReportListItemGetOutput.CreatedTime'></a>

## ProblemReportListItemGetOutput.CreatedTime Property

申報日期

```csharp
public System.Nullable<System.DateTime> CreatedTime { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.DateTime](https://docs.microsoft.com/en-us/dotnet/api/System.DateTime 'System.DateTime')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

<a name='AlifeApi.BusinessRules.ProblemReportModels.ProblemReportListItemGetOutput.CreatedUserId'></a>

## ProblemReportListItemGetOutput.CreatedUserId Property

申報人

```csharp
public string CreatedUserId { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.ProblemReportModels.ProblemReportListItemGetOutput.CreatedUserName'></a>

## ProblemReportListItemGetOutput.CreatedUserName Property

申報人名稱

```csharp
public string CreatedUserName { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.ProblemReportModels.ProblemReportListItemGetOutput.DeptId'></a>

## ProblemReportListItemGetOutput.DeptId Property

單位 ID

```csharp
public string DeptId { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.ProblemReportModels.ProblemReportListItemGetOutput.DeptName'></a>

## ProblemReportListItemGetOutput.DeptName Property

單位名稱

```csharp
public string DeptName { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.ProblemReportModels.ProblemReportListItemGetOutput.Id'></a>

## ProblemReportListItemGetOutput.Id Property

問題回報處理唯一識別值

```csharp
public long Id { get; set; }
```

#### Property Value
[System.Int64](https://docs.microsoft.com/en-us/dotnet/api/System.Int64 'System.Int64')

<a name='AlifeApi.BusinessRules.ProblemReportModels.ProblemReportListItemGetOutput.Menu'></a>

## ProblemReportListItemGetOutput.Menu Property

功能頁面

```csharp
public string Menu { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.ProblemReportModels.ProblemReportListItemGetOutput.ProblemType'></a>

## ProblemReportListItemGetOutput.ProblemType Property

類型

```csharp
public string ProblemType { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.ProblemReportModels.ProblemReportListItemGetOutput.ProblemTypeName'></a>

## ProblemReportListItemGetOutput.ProblemTypeName Property

類型名稱

```csharp
public string ProblemTypeName { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.ProblemReportModels.ProblemReportListItemGetOutput.ProcessStatus'></a>

## ProblemReportListItemGetOutput.ProcessStatus Property

處理狀態

```csharp
public string ProcessStatus { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.ProblemReportModels.ProblemReportListItemGetOutput.ProcessStatusName'></a>

## ProblemReportListItemGetOutput.ProcessStatusName Property

處理狀態名稱

```csharp
public string ProcessStatusName { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.ProblemReportModels.ProblemReportListItemGetOutput.ReplyContent'></a>

## ProblemReportListItemGetOutput.ReplyContent Property

回覆問題內容

```csharp
public string ReplyContent { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.ProblemReportModels.ProblemReportListItemGetOutput.ReplyTime'></a>

## ProblemReportListItemGetOutput.ReplyTime Property

處理時間

```csharp
public System.Nullable<System.DateTime> ReplyTime { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.DateTime](https://docs.microsoft.com/en-us/dotnet/api/System.DateTime 'System.DateTime')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

<a name='AlifeApi.BusinessRules.ProblemReportModels.ProblemReportListItemGetOutput.ReplyUserId'></a>

## ProblemReportListItemGetOutput.ReplyUserId Property

回覆人員

```csharp
public string ReplyUserId { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.ProblemReportModels.ProblemReportListItemGetOutput.ReplyUserName'></a>

## ProblemReportListItemGetOutput.ReplyUserName Property

回覆人員名稱

```csharp
public string ReplyUserName { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.ProblemReportModels.ProblemReportListItemGetOutput.ReportContent'></a>

## ProblemReportListItemGetOutput.ReportContent Property

問題內容

```csharp
public string ReportContent { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.ProblemReportModels.ProblemReportListItemGetOutput.Subject'></a>

## ProblemReportListItemGetOutput.Subject Property

問題主旨

```csharp
public string Subject { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')