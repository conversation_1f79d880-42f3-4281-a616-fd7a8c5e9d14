#### [AlifeApi.BusinessRules](index.md 'index')

## AlifeApi.BusinessRules.UnitModels Namespace

| Classes | |
| :--- | :--- |
| [UnitCreateInput](AlifeApi.BusinessRules.UnitModels.UnitCreateInput.md 'AlifeApi.BusinessRules.UnitModels.UnitCreateInput') | 房屋單位建立輸入模型 |
| [UnitListOutput](AlifeApi.BusinessRules.UnitModels.UnitListOutput.md 'AlifeApi.BusinessRules.UnitModels.UnitListOutput') | 房屋單位列表輸出項目 |
| [UnitOutput](AlifeApi.BusinessRules.UnitModels.UnitOutput.md 'AlifeApi.BusinessRules.UnitModels.UnitOutput') | 房屋單位詳細輸出模型 |
| [UnitQueryInput](AlifeApi.BusinessRules.UnitModels.UnitQueryInput.md 'AlifeApi.BusinessRules.UnitModels.UnitQueryInput') | 房屋單位列表查詢輸入模型 |
| [UnitService](AlifeApi.BusinessRules.UnitModels.UnitService.md 'AlifeApi.BusinessRules.UnitModels.UnitService') | 房屋單位服務 |
| [UnitUpdateInput](AlifeApi.BusinessRules.UnitModels.UnitUpdateInput.md 'AlifeApi.BusinessRules.UnitModels.UnitUpdateInput') | 房屋單位更新輸入模型 |
