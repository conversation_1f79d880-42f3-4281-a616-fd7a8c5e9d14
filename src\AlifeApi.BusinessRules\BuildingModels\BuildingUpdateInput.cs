using System;
using System.ComponentModel.DataAnnotations;

namespace AlifeApi.BusinessRules.BuildingModels
{
    /// <summary>
    /// 建築物更新輸入模型
    /// </summary>
    public class BuildingUpdateInput
    {
        [Required(ErrorMessage = "缺少建築物名稱")]
        public string BuildingName { get; set; } = null!;

        public int? TotalAboveGroundFloors { get; set; }
        public int? TotalBelowGroundFloors { get; set; }
        public string? BuildingType { get; set; }
        public DateOnly? CompletionDate { get; set; }
        public string? Remarks { get; set; }
    }
} 
