#### [AlifeApi.BusinessRules](index.md 'index')
### [AlifeApi.BusinessRules.RoleGroupModels](AlifeApi.BusinessRules.RoleGroupModels.md 'AlifeApi.BusinessRules.RoleGroupModels')

## RoleGroupPermissionUpdateInput Class

更新角色權限

```csharp
public class RoleGroupPermissionUpdateInput : AlifeApi.BusinessRules.RoleGroupModels.RoleGroupCreateInput
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; [RoleGroupCreateInput](AlifeApi.BusinessRules.RoleGroupModels.RoleGroupCreateInput.md 'AlifeApi.BusinessRules.RoleGroupModels.RoleGroupCreateInput') &#129106; RoleGroupPermissionUpdateInput
### Properties

<a name='AlifeApi.BusinessRules.RoleGroupModels.RoleGroupPermissionUpdateInput.Id'></a>

## RoleGroupPermissionUpdateInput.Id Property

ID

```csharp
public string Id { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')