#### [AlifeApi.BusinessRules](index.md 'index')
### [AlifeApi.BusinessRules.UserInfoModels](AlifeApi.BusinessRules.UserInfoModels.md 'AlifeApi.BusinessRules.UserInfoModels')

## UserInfoCreateInputPg Class

使用者新增輸入資料 (PostgreSQL版本)

```csharp
public class UserInfoCreateInputPg
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; UserInfoCreateInputPg
### Properties

<a name='AlifeApi.BusinessRules.UserInfoModels.UserInfoCreateInputPg.BirthDate'></a>

## UserInfoCreateInputPg.BirthDate Property

出生日期

```csharp
public System.Nullable<System.DateOnly> BirthDate { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.DateOnly](https://docs.microsoft.com/en-us/dotnet/api/System.DateOnly 'System.DateOnly')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

<a name='AlifeApi.BusinessRules.UserInfoModels.UserInfoCreateInputPg.CompanyId'></a>

## UserInfoCreateInputPg.CompanyId Property

公司ID

```csharp
public string CompanyId { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.UserInfoModels.UserInfoCreateInputPg.DepartmentId'></a>

## UserInfoCreateInputPg.DepartmentId Property

部門ID

```csharp
public string DepartmentId { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.UserInfoModels.UserInfoCreateInputPg.Email'></a>

## UserInfoCreateInputPg.Email Property

電子郵件

```csharp
public string Email { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.UserInfoModels.UserInfoCreateInputPg.EmergencyContactName'></a>

## UserInfoCreateInputPg.EmergencyContactName Property

緊急聯絡人姓名

```csharp
public string EmergencyContactName { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.UserInfoModels.UserInfoCreateInputPg.EmergencyContactPhone'></a>

## UserInfoCreateInputPg.EmergencyContactPhone Property

緊急聯絡人電話

```csharp
public string EmergencyContactPhone { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.UserInfoModels.UserInfoCreateInputPg.EmergencyContactRelation'></a>

## UserInfoCreateInputPg.EmergencyContactRelation Property

緊急聯絡人關係

```csharp
public string EmergencyContactRelation { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.UserInfoModels.UserInfoCreateInputPg.Gender'></a>

## UserInfoCreateInputPg.Gender Property

性別

```csharp
public string Gender { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.UserInfoModels.UserInfoCreateInputPg.HireDate'></a>

## UserInfoCreateInputPg.HireDate Property

到職日期

```csharp
public System.Nullable<System.DateOnly> HireDate { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.DateOnly](https://docs.microsoft.com/en-us/dotnet/api/System.DateOnly 'System.DateOnly')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

<a name='AlifeApi.BusinessRules.UserInfoModels.UserInfoCreateInputPg.Identity'></a>

## UserInfoCreateInputPg.Identity Property

身分證字號

```csharp
public string Identity { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.UserInfoModels.UserInfoCreateInputPg.IsEmailNotificationEnabled'></a>

## UserInfoCreateInputPg.IsEmailNotificationEnabled Property

是否啟用郵件通知

```csharp
public System.Nullable<bool> IsEmailNotificationEnabled { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.Boolean](https://docs.microsoft.com/en-us/dotnet/api/System.Boolean 'System.Boolean')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

<a name='AlifeApi.BusinessRules.UserInfoModels.UserInfoCreateInputPg.IsInside'></a>

## UserInfoCreateInputPg.IsInside Property

是否為內部人員

```csharp
public System.Nullable<bool> IsInside { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.Boolean](https://docs.microsoft.com/en-us/dotnet/api/System.Boolean 'System.Boolean')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

<a name='AlifeApi.BusinessRules.UserInfoModels.UserInfoCreateInputPg.IsM365'></a>

## UserInfoCreateInputPg.IsM365 Property

是否啟用M365

```csharp
public System.Nullable<bool> IsM365 { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.Boolean](https://docs.microsoft.com/en-us/dotnet/api/System.Boolean 'System.Boolean')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

<a name='AlifeApi.BusinessRules.UserInfoModels.UserInfoCreateInputPg.JobTitleId'></a>

## UserInfoCreateInputPg.JobTitleId Property

職稱ID

```csharp
public string JobTitleId { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.UserInfoModels.UserInfoCreateInputPg.MailingAddress'></a>

## UserInfoCreateInputPg.MailingAddress Property

通訊地址

```csharp
public string MailingAddress { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.UserInfoModels.UserInfoCreateInputPg.MobileNumber'></a>

## UserInfoCreateInputPg.MobileNumber Property

手機

```csharp
public string MobileNumber { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.UserInfoModels.UserInfoCreateInputPg.Name'></a>

## UserInfoCreateInputPg.Name Property

姓名

```csharp
public string Name { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.UserInfoModels.UserInfoCreateInputPg.Password'></a>

## UserInfoCreateInputPg.Password Property

密碼

```csharp
public string Password { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.UserInfoModels.UserInfoCreateInputPg.RegisteredAddress'></a>

## UserInfoCreateInputPg.RegisteredAddress Property

戶籍地址

```csharp
public string RegisteredAddress { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.UserInfoModels.UserInfoCreateInputPg.RoleGroupIds'></a>

## UserInfoCreateInputPg.RoleGroupIds Property

角色群組ID列表

```csharp
public System.Collections.Generic.List<string> RoleGroupIds { get; set; }
```

#### Property Value
[System.Collections.Generic.List&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Collections.Generic.List-1 'System.Collections.Generic.List`1')[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Collections.Generic.List-1 'System.Collections.Generic.List`1')

<a name='AlifeApi.BusinessRules.UserInfoModels.UserInfoCreateInputPg.ServiceUnit'></a>

## UserInfoCreateInputPg.ServiceUnit Property

服務單位

```csharp
public string ServiceUnit { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.UserInfoModels.UserInfoCreateInputPg.Status'></a>

## UserInfoCreateInputPg.Status Property

是否啟用

```csharp
public System.Nullable<bool> Status { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.Boolean](https://docs.microsoft.com/en-us/dotnet/api/System.Boolean 'System.Boolean')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

<a name='AlifeApi.BusinessRules.UserInfoModels.UserInfoCreateInputPg.TelephoneNumber'></a>

## UserInfoCreateInputPg.TelephoneNumber Property

市話

```csharp
public string TelephoneNumber { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.UserInfoModels.UserInfoCreateInputPg.UserInfoId'></a>

## UserInfoCreateInputPg.UserInfoId Property

使用者ID

```csharp
public string UserInfoId { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')