﻿using System.Data;
using System.Text.Json.Serialization;
using AlifeApi.BusinessRules.Infrastructure;
using AlifeApi.BusinessRules.RoleGroupModels;

namespace AlifeApi.BusinessRules.LoginModels
{
    /// <summary>
    /// 使用者登入
    /// </summary>
    public class UserLoginOutput : IApiMessage
    {
        private IEnumerable<RoleOutput> roles = Enumerable.Empty<RoleOutput>();

        /// <summary>
        /// 使用者帳號
        /// </summary>
        public string UserInfoId { get; set; }

        /// <summary>
        /// 使用者密碼
        /// </summary>
        [JsonPropertyName("UserPW")]
        public string UserPassword { get; set; }

        /// <summary>
        /// 使用者姓名
        /// </summary>
        public string UserName { get; set; }

        /// <summary>
        /// 職稱
        /// </summary>
        [JsonPropertyName("Grade")]
        public string GradeCode { get; set; }

        /// <summary>
        /// 職稱
        /// </summary>
        public string GradeName { get; set; }

        /// <summary>
        /// 是否為管理者
        /// </summary>
        public bool IsAdmin { get; set; }

        /// <summary>
        /// 單位
        /// </summary>
        [JsonPropertyName("Dept")]
        public string DeptId { get; set; }

        /// <summary>
        /// 單位
        /// </summary>
        public string DeptName { get; set; }

        /// <summary>
        /// 信箱
        /// </summary>
        public string Email { get; set; }

        /// <summary>
        /// 身份證字號
        /// </summary>
        /// <remarks>
        /// 別的地方叫 USERIDNO，這邊只叫 IDNO？
        /// </remarks>
        [JsonPropertyName("IDNO")]
        public string IdNo { get; set; }

        /// <summary>
        /// 角色群組
        /// </summary>
        public IEnumerable<RoleOutput> Roles
        {
            get
            {
                return roles.Select((x, i) =>
                {
                    x.Order = i + 1;
                    return x;
                });
            }
            set => roles = value;
        }

        /// <summary>
        /// 權限
        /// </summary>
        [JsonPropertyName("Permission")]
        public IEnumerable<string> FuncIds
        {
            get
            {
                return RoleFuncIds.Union(DeptFuncIds).Union(GradeFuncIds).Distinct();
            }
        }

        /// <remarks>
        /// 角色權限
        /// </remarks>
        public IEnumerable<string> RoleFuncIds;

        /// <remarks>
        /// 部門權限
        /// </remarks>
        public IEnumerable<string> DeptFuncIds;

        /// <remarks>
        /// 職稱權限
        /// </remarks>
        public IEnumerable<string> GradeFuncIds;

        /// <summary>
        /// 權限樹
        /// </summary>
        [JsonPropertyName("PermissionTree")]
        public IEnumerable<MenuTreeOutput> MenuTrees { get; set; } = Enumerable.Empty<MenuTreeOutput>();

        /// <summary>
        /// IP
        /// </summary>
        public string IP { get; set; }

        /// <summary>
        /// JWT Token
        /// </summary>
        /// <remarks>
        /// 這邊改名不是我嫌棄回傳前端大小寫有問題，只是單純 C# 遇到這種 3 碼以上的略縮字大小寫規則和別的語言不同
        /// </remarks>
        [JsonPropertyName("JWTToken")]
        public string JwtToken { get; set; } = string.Empty;

        /// <summary>
        /// JWT Token 到期日期時間
        /// </summary>
        /// <remarks>
        /// 這邊改名不是我嫌棄回傳前端大小寫有問題，只是單純 C# 遇到這種 3 碼以上的略縮字大小寫規則和別的語言不同
        /// </remarks>
        [JsonPropertyName("JWTTokenExpireTime")]
        public DateTime? JwtTokenExpireTime { get; set; }

        /// <inheritdoc/>
        public Enum Response { get; set; }
    }
}
