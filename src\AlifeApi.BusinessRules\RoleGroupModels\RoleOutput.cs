﻿using System.Text.Json.Serialization;

namespace AlifeApi.BusinessRules.RoleGroupModels
{
    /// <summary>
    /// 角色資料模型
    /// </summary>
    public class RoleOutput
    {
        /// <summary>
        /// 角色代碼
        /// </summary>
        [JsonPropertyName("Role")]
        public string RoleId { get; set; } = string.Empty;

        /// <summary>
        /// 角色名稱
        /// </summary>
        public string RoleName { get; set; } = string.Empty;

        /// <summary>
        /// 排序編號
        /// 在多角色的情況下
        /// 方便前台能依序找到第1位角色
        /// </summary>
        [JsonPropertyName("Sort")]
        public int Order { get; set; }
    }
}
