using System.ComponentModel.DataAnnotations;

namespace AlifeApi.BusinessRules.SiteModels
{
    /// <summary>
    /// 案場創建輸入
    /// </summary>
    public class SiteCreateInput
    {
        /// <summary>
        /// 案場代碼
        /// </summary>
        [Required(ErrorMessage = "案場代碼為必填")]
        [MaxLength(10, ErrorMessage = "案場代碼最多10個字元")]
        public string SiteCode { get; set; }

        /// <summary>
        /// 執行公司別
        /// </summary>
        [MaxLength(10, ErrorMessage = "執行公司別最多10個字元")]
        public string CompanyId { get; set; }

        /// <summary>
        /// 案場名稱
        /// </summary>
        [Required(ErrorMessage = "案場名稱為必填")]
        [MaxLength(50, ErrorMessage = "案場名稱最多50個字元")]
        public string SiteName { get; set; }

        /// <summary>
        /// 推案型態
        /// </summary>
        [MaxLength(50, ErrorMessage = "推案型態最多50個字元")]
        public string PromotionType { get; set; }

        /// <summary>
        /// 主委
        /// </summary>
        [MaxLength(15, ErrorMessage = "主委最多15個字元")]
        public string Chairman { get; set; }

        /// <summary>
        /// 副主委
        /// </summary>
        [MaxLength(15, ErrorMessage = "副主委最多15個字元")]
        public string ViceChairman { get; set; }

        /// <summary>
        /// 專案
        /// </summary>
        [MaxLength(15, ErrorMessage = "專案最多15個字元")]
        public string ProjectManager { get; set; }

        /// <summary>
        /// 副專
        /// </summary>
        [MaxLength(15, ErrorMessage = "副專最多15個字元")]
        public string DeputyProjectManager { get; set; }

        /// <summary>
        /// 業務
        /// </summary>
        public string BusinessIds { get; set; }

        /// <summary>
        /// 跑單
        /// </summary>
        public string RunnerIds { get; set; }

        /// <summary>
        /// 接待中心
        /// </summary>
        [MaxLength(100, ErrorMessage = "接待中心最多100個字元")]
        public string ReceptionCenter { get; set; }

        /// <summary>
        /// 案場電話
        /// </summary>
        [MaxLength(20, ErrorMessage = "案場電話最多20個字元")]
        public string SitePhone { get; set; }

        /// <summary>
        /// 經紀人
        /// </summary>
        [MaxLength(50, ErrorMessage = "經紀人最多50個字元")]
        public string Broker { get; set; }

        /// <summary>
        /// 所在縣市
        /// </summary>
        [Required(ErrorMessage = "所在縣市為必填")]
        [MaxLength(10, ErrorMessage = "所在縣市最多10個字元")]
        public string City { get; set; }

        /// <summary>
        /// 所在區域
        /// </summary>
        [Required(ErrorMessage = "所在區域為必填")]
        [MaxLength(10, ErrorMessage = "所在區域最多10個字元")]
        public string District { get; set; }

        /// <summary>
        /// 投資興建
        /// </summary>
        [MaxLength(50, ErrorMessage = "投資興建最多50個字元")]
        public string Developer { get; set; }

        /// <summary>
        /// 基地位置
        /// </summary>
        [Required(ErrorMessage = "基地位置為必填")]
        [MaxLength(100, ErrorMessage = "基地位置最多100個字元")]
        public string SiteLocation { get; set; }

        /// <summary>
        /// 基地面積
        /// </summary>
        public decimal? LandArea { get; set; }

        /// <summary>
        /// 規劃總層
        /// </summary>
        public int? TotalPlannedFloors { get; set; }

        /// <summary>
        /// 使用分區
        /// </summary>
        [MaxLength(50, ErrorMessage = "使用分區最多50個字元")]
        public string Zoning { get; set; }

        /// <summary>
        /// 公設比
        /// </summary>
        public decimal? PublicFacilityRatio { get; set; }

        /// <summary>
        /// 結構
        /// </summary>
        [MaxLength(50, ErrorMessage = "結構最多50個字元")]
        public string Structure { get; set; }

        /// <summary>
        /// 規劃樓層
        /// </summary>
        [MaxLength(100, ErrorMessage = "規劃樓層最多100個字元")]
        public string PlannedFloors { get; set; }

        /// <summary>
        /// 規劃戶車
        /// </summary>
        [MaxLength(100, ErrorMessage = "規劃戶車最多100個字元")]
        public string PlannedUnitsAndParking { get; set; }

        /// <summary>
        /// 坪數
        /// </summary>
        [MaxLength(100, ErrorMessage = "坪數最多100個字元")]
        public string UnitSize { get; set; }

        /// <summary>
        /// 全案總銷
        /// </summary>
        public decimal? TotalSalePrice { get; set; }

        /// <summary>
        /// 車位類別
        /// </summary>
        [MaxLength(50, ErrorMessage = "車位類別最多50個字元")]
        public string ParkingType { get; set; }

        /// <summary>
        /// 合約期間
        /// </summary>
        public DateOnly? ContractPeriod { get; set; }

        /// <summary>
        /// 展延期間
        /// </summary>
        public DateOnly? ExtensionPeriod { get; set; }

        /// <summary>
        /// 可售總銷
        /// </summary>
        public decimal? SellableTotalPrice { get; set; }

        /// <summary>
        /// 服務費計算
        /// </summary>
        [MaxLength(100, ErrorMessage = "服務費計算最多100個字元")]
        public string ServiceFeeCalculation { get; set; }

        /// <summary>
        /// 服務費率
        /// </summary>
        public decimal? ServiceFeeRate { get; set; }

        /// <summary>
        /// 保留款
        /// </summary>
        public decimal? ReserveAmount { get; set; }

        /// <summary>
        /// 應編廣告預算
        /// </summary>
        public decimal? AdvertisingBudget { get; set; }

        /// <summary>
        /// 廣告預算率
        /// </summary>
        public decimal? AdvertisingBudgetRate { get; set; }

        /// <summary>
        /// 超價款分配
        /// </summary>
        [MaxLength(100, ErrorMessage = "超價款分配最多100個字元")]
        public string ExcessPriceAllocation { get; set; }

        /// <summary>
        /// 已發包金額
        /// </summary>
        public decimal? ContractedAmount { get; set; }

        /// <summary>
        /// 控存率
        /// </summary>
        public decimal? ControlReserveRate { get; set; }

        /// <summary>
        /// 已請款金額
        /// </summary>
        public decimal? PaidAmount { get; set; }

        /// <summary>
        /// 地上層數
        /// </summary>
        [MaxLength(50, ErrorMessage = "地上層數最多50個字元")]
        public string AboveGroundFloors { get; set; }

        /// <summary>
        /// 地下層數
        /// </summary>
        [MaxLength(50, ErrorMessage = "地下層數最多50個字元")]
        public string BelowGroundFloors { get; set; }

        /// <summary>
        /// 規劃戶數(住家)
        /// </summary>
        public int? PlannedResidentialUnits { get; set; }

        /// <summary>
        /// 規劃戶數(店面)
        /// </summary>
        public int? PlannedStoreUnits { get; set; }

        /// <summary>
        /// 規劃戶數(車位)
        /// </summary>
        public int? PlannedParkingSpaces { get; set; }
    }
} 
