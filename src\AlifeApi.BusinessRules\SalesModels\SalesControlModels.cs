using System;
using System.Collections.Generic;

/// <summary>
/// 銷控表列表格式輸入條件
/// </summary>
public class SalesControlListInput
{
    /// <summary>
    /// 案場編號（必填）
    /// </summary>
    public string SiteCode { get; set; } = string.Empty;

    /// <summary>
    /// 建築物ID（可選，用於篩選特定建築物）
    /// </summary>
    public int? BuildingId { get; set; }

    /// <summary>
    /// 起始日期（可選，用於篩選售出日期）
    /// </summary>
    public DateOnly? StartDate { get; set; }

    /// <summary>
    /// 結束日期（可選，用於篩選售出日期）
    /// </summary>
    public DateOnly? EndDate { get; set; }
}

/// <summary>
/// 銷控表列表格式輸出
/// </summary>
public class SalesControlListOutput
{
    /// <summary>
    /// 案場編號
    /// </summary>
    public string SiteCode { get; set; } = string.Empty;

    /// <summary>
    /// 建築物ID
    /// </summary>
    public int? BuildingId { get; set; }

    /// <summary>
    /// 建築物名稱
    /// </summary>
    public string? BuildingName { get; set; }

    /// <summary>
    /// 銷售記錄清單
    /// </summary>
    public List<SalesRecordItem> Records { get; set; } = new List<SalesRecordItem>();

    /// <summary>
    /// 統計摘要
    /// </summary>
    public SalesListSummary Summary { get; set; } = new SalesListSummary();
}

/// <summary>
/// 銷售記錄項目
/// </summary>
public class SalesRecordItem
{
    /// <summary>
    /// 售出日期
    /// </summary>
    public DateOnly? SaleDate { get; set; }

    /// <summary>
    /// 足訂日期（定金足額到位日期）
    /// </summary>
    public DateOnly? DepositFullPaidDate { get; set; }

    /// <summary>
    /// 簽約日期
    /// </summary>
    public DateOnly? ContractSignedDate { get; set; }

    /// <summary>
    /// 客戶姓名
    /// </summary>
    public string CustomerName { get; set; } = string.Empty;

    /// <summary>
    /// 戶別（建築物+樓層格式，例如：A-15F, A1-11F）
    /// </summary>
    public string UnitDesignation { get; set; } = string.Empty;

    /// <summary>
    /// 坪數
    /// </summary>
    public decimal? Area { get; set; }

    /// <summary>
    /// 房屋成交價
    /// </summary>
    public decimal? PropertyTransactionPrice { get; set; }

    /// <summary>
    /// 車位資訊
    /// </summary>
    public string ParkingSpaces { get; set; } = string.Empty;

    /// <summary>
    /// 車位成交價
    /// </summary>
    public decimal? ParkingTransactionPrice { get; set; }

    /// <summary>
    /// 實價登錄
    /// </summary>
    public string RealPriceRegistration { get; set; } = string.Empty;

    /// <summary>
    /// 業務員姓名
    /// </summary>
    public string SalespersonName { get; set; } = string.Empty;

    /// <summary>
    /// 訂單ID（用於追蹤）
    /// </summary>
    public int OrderId { get; set; }

    /// <summary>
    /// 房屋單位ID（用於追蹤）
    /// </summary>
    public int UnitId { get; set; }
}

/// <summary>
/// 銷售列表統計摘要
/// </summary>
public class SalesListSummary
{
    /// <summary>
    /// 總銷售筆數
    /// </summary>
    public int TotalSalesCount { get; set; }

    /// <summary>
    /// 總房屋成交金額
    /// </summary>
    public decimal TotalPropertyAmount { get; set; }

    /// <summary>
    /// 總車位成交金額
    /// </summary>
    public decimal TotalParkingAmount { get; set; }

    /// <summary>
    /// 總成交金額
    /// </summary>
    public decimal TotalAmount => TotalPropertyAmount + TotalParkingAmount;

    /// <summary>
    /// 平均房屋成交價
    /// </summary>
    public decimal AveragePropertyPrice => TotalSalesCount > 0 ? TotalPropertyAmount / TotalSalesCount : 0;

    /// <summary>
    /// 已售筆數
    /// </summary>
    public int SoldCount { get; set; }

    /// <summary>
    /// 已足訂筆數
    /// </summary>
    public int DepositFullCount { get; set; }

    /// <summary>
    /// 已簽約筆數
    /// </summary>
    public int ContractedCount { get; set; }
}

/// <summary>
/// 銷控表單位完整更新輸入
/// </summary>
public class SalesControlUnitUpdateInput
{
    /// <summary>
    /// 銷售狀態 (售、足、簽、請、領、可售、保留)
    /// </summary>
    public string Status { get; set; } = null!;

    /// <summary>
    /// 購買者資訊 (客戶姓名或聯絡資訊)
    /// </summary>
    public string? PurchaseInfo { get; set; }

    /// <summary>
    /// 售價
    /// </summary>
    public decimal? SalePrice { get; set; }

    /// <summary>
    /// 坪數 (如果有變更)
    /// </summary>
    public decimal? Area { get; set; }

    /// <summary>
    /// 房型 (如果有變更)
    /// </summary>
    public string? UnitType { get; set; }

    /// <summary>
    /// 如果狀態變更涉及訂單，可能需要的訂單ID
    /// </summary>
    public int? OrderId { get; set; }

    /// <summary>
    /// 備註
    /// </summary>
    public string? Remarks { get; set; }
}

/// <summary>
/// 停車位銷控資料
/// </summary>
public class ParkingSpaceSalesData
{
    public int ParkingSpaceId { get; set; }
    public string? SpaceNumber { get; set; }
    public string? Status { get; set; }
    public decimal? ListPrice { get; set; }
    public decimal? TransactionPrice { get; set; }
}

/// <summary>
/// 房屋單位銷控資料
/// </summary>
public class UnitSalesData
{
    public int UnitId { get; set; }
    public string? UnitNumber { get; set; }
    public string? Status { get; set; }
    public decimal? Area { get; set; }
    public decimal? ListPrice { get; set; }
    public decimal? TransactionPrice { get; set; }
    
    // 來自訂單的額外資訊
    public int? CustomerId { get; set; }
    public string? CustomerName { get; set; }
    public DateOnly? SaleDate { get; set; }
    public DateOnly? RequestDate { get; set; } // 訂購/要約日期
    public DateOnly? ReceiveDate { get; set; } // 收訂日期
    public string? PurchaseInfo { get; set; } // 購買資訊(備註)
    public DateOnly? PriceRegistrationSubmissionDate { get; set; } // 實價登錄提報日期
}

/// <summary>
/// 樓層銷控資料
/// </summary>
public class FloorSalesData
{
    public int FloorId { get; set; }
    public string? FloorLabel { get; set; } // 前端顯示用的 floorNumber
    public List<UnitSalesData> Units { get; set; } = new();
    public FloorSummary Summary { get; set; } = new();
}

/// <summary>
/// 樓層銷售統計
/// </summary>
public class FloorSummary
{
    public int Available { get; set; }
    public int Sold { get; set; }
    public int Reserved { get; set; }
    public string SalesRate { get; set; } = "0%";
}

/// <summary>
/// 棟別(戶號)垂直統計
/// </summary>
public class ColumnSummary
{
    public string? Column { get; set; } // e.g., "A", "B"
    public int Available { get; set; }
    public int Sold { get; set; }
    public int Reserved { get; set; }
    public string SalesRate { get; set; } = "0%";
}

/// <summary>
/// 建案整體銷售總計
/// </summary>
public class TotalSummary
{
    public int TotalAvailable { get; set; }
    public int TotalSold { get; set; }
    public int TotalReserved { get; set; }
    public string OverallSalesRate { get; set; } = "0%";
}

/// <summary>
/// 建築銷控總資料 (回傳給前端的根物件)
/// </summary>
public class BuildingSalesData
{
    public int BuildingId { get; set; }
    public string? BuildingName { get; set; }
    public List<FloorSalesData> Floors { get; set; } = new();
    public List<ColumnSummary> ColumnSummary { get; set; } = new();
    public TotalSummary TotalSummary { get; set; } = new();
} 
