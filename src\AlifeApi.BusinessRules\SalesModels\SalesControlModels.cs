/// <summary>
/// 銷控表列表格式輸入條件
/// </summary>
public class SalesControlListInput
{
    /// <summary>
    /// 案場編號（必填）
    /// </summary>
    public string SiteCode { get; set; } = string.Empty;

    /// <summary>
    /// 建築物ID（可選，用於篩選特定建築物）
    /// </summary>
    public int? BuildingId { get; set; }

    /// <summary>
    /// 起始日期（可選，用於篩選售出日期）
    /// </summary>
    public DateOnly? StartDate { get; set; }

    /// <summary>
    /// 結束日期（可選，用於篩選售出日期）
    /// </summary>
    public DateOnly? EndDate { get; set; }
}

/// <summary>
/// 銷控表列表格式輸出
/// </summary>
public class SalesControlListOutput
{
    /// <summary>
    /// 案場編號
    /// </summary>
    public string SiteCode { get; set; } = string.Empty;

    /// <summary>
    /// 建築物ID
    /// </summary>
    public int? BuildingId { get; set; }

    /// <summary>
    /// 建築物名稱
    /// </summary>
    public string? BuildingName { get; set; }

    /// <summary>
    /// 銷售記錄清單
    /// </summary>
    public List<SalesRecordItem> Records { get; set; } = new List<SalesRecordItem>();

    /// <summary>
    /// 統計摘要
    /// </summary>
    public SalesListSummary Summary { get; set; } = new SalesListSummary();
}

/// <summary>
/// 銷售記錄項目
/// </summary>
public class SalesRecordItem
{
    /// <summary>
    /// 售出日期
    /// </summary>
    public DateOnly? SaleDate { get; set; }

    /// <summary>
    /// 足訂日期（定金足額到位日期）
    /// </summary>
    public DateOnly? DepositFullPaidDate { get; set; }

    /// <summary>
    /// 簽約日期
    /// </summary>
    public DateOnly? ContractSignedDate { get; set; }

    /// <summary>
    /// 客戶姓名
    /// </summary>
    public string CustomerName { get; set; } = string.Empty;

    /// <summary>
    /// 戶別（建築物+樓層格式，例如：A-15F, A1-11F）
    /// </summary>
    public string UnitDesignation { get; set; } = string.Empty;

    /// <summary>
    /// 坪數
    /// </summary>
    public decimal? Area { get; set; }

    /// <summary>
    /// 房屋成交價
    /// </summary>
    public decimal? PropertyTransactionPrice { get; set; }

    /// <summary>
    /// 車位資訊
    /// </summary>
    public string ParkingSpaces { get; set; } = string.Empty;

    /// <summary>
    /// 車位成交價
    /// </summary>
    public decimal? ParkingTransactionPrice { get; set; }

    /// <summary>
    /// 實價登錄
    /// </summary>
    public string RealPriceRegistration { get; set; } = string.Empty;

    /// <summary>
    /// 業務員姓名
    /// </summary>
    public string SalespersonName { get; set; } = string.Empty;

    /// <summary>
    /// 訂單ID（用於追蹤）
    /// </summary>
    public int OrderId { get; set; }

    /// <summary>
    /// 房屋單位ID（用於追蹤）
    /// </summary>
    public int UnitId { get; set; }
}

/// <summary>
/// 銷售列表統計摘要
/// </summary>
public class SalesListSummary
{
    /// <summary>
    /// 總銷售筆數
    /// </summary>
    public int TotalSalesCount { get; set; }

    /// <summary>
    /// 總房屋成交金額
    /// </summary>
    public decimal TotalPropertyAmount { get; set; }

    /// <summary>
    /// 總車位成交金額
    /// </summary>
    public decimal TotalParkingAmount { get; set; }

    /// <summary>
    /// 總成交金額
    /// </summary>
    public decimal TotalAmount => TotalPropertyAmount + TotalParkingAmount;

    /// <summary>
    /// 平均房屋成交價
    /// </summary>
    public decimal AveragePropertyPrice => TotalSalesCount > 0 ? TotalPropertyAmount / TotalSalesCount : 0;

    /// <summary>
    /// 已售筆數
    /// </summary>
    public int SoldCount { get; set; }

    /// <summary>
    /// 已足訂筆數
    /// </summary>
    public int DepositFullCount { get; set; }

    /// <summary>
    /// 已簽約筆數
    /// </summary>
    public int ContractedCount { get; set; }
} 
