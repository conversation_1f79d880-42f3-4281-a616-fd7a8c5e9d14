#### [AlifeApi.BusinessRules](index.md 'index')
### [AlifeApi.BusinessRules.UserDeptModels](AlifeApi.BusinessRules.UserDeptModels.md 'AlifeApi.BusinessRules.UserDeptModels')

## UserDeptListItemGetOutput Class

```csharp
public class UserDeptListItemGetOutput
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; UserDeptListItemGetOutput
### Properties

<a name='AlifeApi.BusinessRules.UserDeptModels.UserDeptListItemGetOutput.DeptId'></a>

## UserDeptListItemGetOutput.DeptId Property

部門代號

```csharp
public string DeptId { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.UserDeptModels.UserDeptListItemGetOutput.DeptName'></a>

## UserDeptListItemGetOutput.DeptName Property

部門代號名稱

```csharp
public string DeptName { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.UserDeptModels.UserDeptListItemGetOutput.IsDisabled'></a>

## UserDeptListItemGetOutput.IsDisabled Property

是否啟用

```csharp
public bool IsDisabled { get; set; }
```

#### Property Value
[System.Boolean](https://docs.microsoft.com/en-us/dotnet/api/System.Boolean 'System.Boolean')

<a name='AlifeApi.BusinessRules.UserDeptModels.UserDeptListItemGetOutput.LeaderUserId'></a>

## UserDeptListItemGetOutput.LeaderUserId Property

部門主管員工編號

```csharp
public string LeaderUserId { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.UserDeptModels.UserDeptListItemGetOutput.LeaderUserName'></a>

## UserDeptListItemGetOutput.LeaderUserName Property

部門主管員工姓名

```csharp
public string LeaderUserName { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.UserDeptModels.UserDeptListItemGetOutput.ParentDeptId'></a>

## UserDeptListItemGetOutput.ParentDeptId Property

上層部門代號

```csharp
public string ParentDeptId { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.UserDeptModels.UserDeptListItemGetOutput.ParentDeptName'></a>

## UserDeptListItemGetOutput.ParentDeptName Property

上層部門代號名稱

```csharp
public string ParentDeptName { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')