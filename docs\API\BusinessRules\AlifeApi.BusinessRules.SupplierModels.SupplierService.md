#### [AlifeApi.BusinessRules](index.md 'index')
### [AlifeApi.BusinessRules.SupplierModels](AlifeApi.BusinessRules.SupplierModels.md 'AlifeApi.BusinessRules.SupplierModels')

## SupplierService Class

```csharp
public class SupplierService : AlifeApi.BusinessRules.Infrastructure.ServiceBase<AlifeApi.DataAccess.ProjectContent.alifeContext>
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; [AlifeApi.Common.InfrastructureBase](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.Common.InfrastructureBase 'AlifeApi.Common.InfrastructureBase') &#129106; [AlifeApi.BusinessRules.Infrastructure.ServiceBase&lt;](AlifeApi.BusinessRules.Infrastructure.ServiceBase_TDbContext_.md 'AlifeApi.BusinessRules.Infrastructure.ServiceBase<TDbContext>')[AlifeApi.DataAccess.ProjectContent.alifeContext](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.DataAccess.ProjectContent.alifeContext 'AlifeApi.DataAccess.ProjectContent.alifeContext')[&gt;](AlifeApi.BusinessRules.Infrastructure.ServiceBase_TDbContext_.md 'AlifeApi.BusinessRules.Infrastructure.ServiceBase<TDbContext>') &#129106; SupplierService
### Methods

<a name='AlifeApi.BusinessRules.SupplierModels.SupplierService.CreateSupplierAsync(AlifeApi.BusinessRules.SupplierModels.SupplierCreateInput)'></a>

## SupplierService.CreateSupplierAsync(SupplierCreateInput) Method

建立供應商

```csharp
public System.Threading.Tasks.Task<int> CreateSupplierAsync(AlifeApi.BusinessRules.SupplierModels.SupplierCreateInput input);
```
#### Parameters

<a name='AlifeApi.BusinessRules.SupplierModels.SupplierService.CreateSupplierAsync(AlifeApi.BusinessRules.SupplierModels.SupplierCreateInput).input'></a>

`input` [SupplierCreateInput](AlifeApi.BusinessRules.SupplierModels.SupplierCreateInput.md 'AlifeApi.BusinessRules.SupplierModels.SupplierCreateInput')

#### Returns
[System.Threading.Tasks.Task&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')[System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')

<a name='AlifeApi.BusinessRules.SupplierModels.SupplierService.DeleteSupplierAsync(int)'></a>

## SupplierService.DeleteSupplierAsync(int) Method

刪除供應商

```csharp
public System.Threading.Tasks.Task DeleteSupplierAsync(int supplierId);
```
#### Parameters

<a name='AlifeApi.BusinessRules.SupplierModels.SupplierService.DeleteSupplierAsync(int).supplierId'></a>

`supplierId` [System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')

#### Returns
[System.Threading.Tasks.Task](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task 'System.Threading.Tasks.Task')

<a name='AlifeApi.BusinessRules.SupplierModels.SupplierService.GetSupplierByIdAsync(int)'></a>

## SupplierService.GetSupplierByIdAsync(int) Method

根據ID取得供應商詳細資訊

```csharp
public System.Threading.Tasks.Task<AlifeApi.BusinessRules.SupplierModels.SupplierOutput> GetSupplierByIdAsync(int supplierId);
```
#### Parameters

<a name='AlifeApi.BusinessRules.SupplierModels.SupplierService.GetSupplierByIdAsync(int).supplierId'></a>

`supplierId` [System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')

#### Returns
[System.Threading.Tasks.Task&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')[AlifeApi.BusinessRules.SupplierModels.SupplierOutput](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.BusinessRules.SupplierModels.SupplierOutput 'AlifeApi.BusinessRules.SupplierModels.SupplierOutput')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')

<a name='AlifeApi.BusinessRules.SupplierModels.SupplierService.GetSupplierListAsync(AlifeApi.BusinessRules.SupplierModels.SupplierQueryInput)'></a>

## SupplierService.GetSupplierListAsync(SupplierQueryInput) Method

取得供應商列表 (分頁)

```csharp
public System.Threading.Tasks.Task<AlifeApi.BusinessRules.Infrastructure.PagedListOutput<AlifeApi.BusinessRules.SupplierModels.SupplierListOutput>> GetSupplierListAsync(AlifeApi.BusinessRules.SupplierModels.SupplierQueryInput input);
```
#### Parameters

<a name='AlifeApi.BusinessRules.SupplierModels.SupplierService.GetSupplierListAsync(AlifeApi.BusinessRules.SupplierModels.SupplierQueryInput).input'></a>

`input` [AlifeApi.BusinessRules.SupplierModels.SupplierQueryInput](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.BusinessRules.SupplierModels.SupplierQueryInput 'AlifeApi.BusinessRules.SupplierModels.SupplierQueryInput')

#### Returns
[System.Threading.Tasks.Task&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')[AlifeApi.BusinessRules.Infrastructure.PagedListOutput&lt;](AlifeApi.BusinessRules.Infrastructure.PagedListOutput_TDetail_.md 'AlifeApi.BusinessRules.Infrastructure.PagedListOutput<TDetail>')[AlifeApi.BusinessRules.SupplierModels.SupplierListOutput](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.BusinessRules.SupplierModels.SupplierListOutput 'AlifeApi.BusinessRules.SupplierModels.SupplierListOutput')[&gt;](AlifeApi.BusinessRules.Infrastructure.PagedListOutput_TDetail_.md 'AlifeApi.BusinessRules.Infrastructure.PagedListOutput<TDetail>')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')

<a name='AlifeApi.BusinessRules.SupplierModels.SupplierService.UpdateSupplierAsync(int,AlifeApi.BusinessRules.SupplierModels.SupplierUpdateInput)'></a>

## SupplierService.UpdateSupplierAsync(int, SupplierUpdateInput) Method

更新供應商

```csharp
public System.Threading.Tasks.Task UpdateSupplierAsync(int supplierId, AlifeApi.BusinessRules.SupplierModels.SupplierUpdateInput input);
```
#### Parameters

<a name='AlifeApi.BusinessRules.SupplierModels.SupplierService.UpdateSupplierAsync(int,AlifeApi.BusinessRules.SupplierModels.SupplierUpdateInput).supplierId'></a>

`supplierId` [System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')

<a name='AlifeApi.BusinessRules.SupplierModels.SupplierService.UpdateSupplierAsync(int,AlifeApi.BusinessRules.SupplierModels.SupplierUpdateInput).input'></a>

`input` [SupplierUpdateInput](AlifeApi.BusinessRules.SupplierModels.SupplierUpdateInput.md 'AlifeApi.BusinessRules.SupplierModels.SupplierUpdateInput')

#### Returns
[System.Threading.Tasks.Task](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task 'System.Threading.Tasks.Task')