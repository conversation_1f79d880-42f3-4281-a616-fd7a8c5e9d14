using AlifeApi.BusinessRules.Infrastructure;
using AlifeApi.BusinessRules.OwnerModels;
using AlifeApi.WebApi.Controllers; // 引用基礎 Controller
using Microsoft.AspNetCore.Mvc;
using System.Threading.Tasks;

namespace AlifeApi.WebApi.Controllers
{
    /// <summary>
    /// 業主資料管理 API
    /// </summary>
    public class OwnerController : AuthenticatedController // 繼承 AuthenticatedController 以要求驗證
    {
        private readonly OwnerService _ownerService;

        /// <summary>
        /// 建構子
        /// </summary>
        public OwnerController(OwnerService ownerService)
        {
            _ownerService = ownerService ?? throw new ArgumentNullException(nameof(ownerService));
        }

        /// <summary>
        /// 建立新業主
        /// </summary>
        /// <param name="input">建立業主輸入資料</param>
        /// <returns>執行結果</returns>
        [HttpPost]
        public async Task<ActionResult<OwnerUpdateOutput>> CreateOwnerAsync([FromBody] OwnerCreateInput input)
        {
            try
            {
                var result = await _ownerService.CreateOwnerAsync(input);
                if (result.Response == Message.Success)
                {
                    return Ok(result);
                }
                // 可以根據不同的失敗訊息回傳不同的狀態碼，例如 Conflict (409)
                return BadRequest(result); // 暫時回傳 BadRequest
            }
            catch (Exception ex)
            {
                // 記錄錯誤
                return BadRequest(ex.Message); // 或者回傳 500 Internal Server Error
            }
        }

        /// <summary>
        /// 更新業主資料
        /// </summary>
        /// <param name="ownerId">業主ID</param>
        /// <param name="input">更新業主輸入資料</param>
        /// <returns>執行結果</returns>
        [HttpPut("{ownerId}")]
        public async Task<ActionResult<OwnerUpdateOutput>> UpdateOwnerAsync(int ownerId, [FromBody] OwnerUpdateInput input)
        {
            try
            {
                var result = await _ownerService.UpdateOwnerAsync(ownerId, input);
                if (result.Response == Message.Success)
                {
                    return Ok(result);
                }
                else if (result.Response == Message.Http_404_NotFound)
                {
                    return NotFound();
                }
                // 可以根據不同的失敗訊息回傳不同的狀態碼
                return BadRequest(result);
            }
            catch (Exception ex)
            {
                // 記錄錯誤
                return BadRequest(ex.Message);
            }
        }

        /// <summary>
        /// 刪除業主資料
        /// </summary>
        /// <param name="ownerId">業主ID</param>
        /// <returns>執行結果</returns>
        [HttpDelete("{ownerId}")]
        public async Task<ActionResult<OwnerUpdateOutput>> DeleteOwnerAsync(int ownerId)
        {
            try
            {
                var result = await _ownerService.DeleteOwnerAsync(ownerId);
                 // 即使找不到也回傳 OK (200)，符合 RESTful 風格的 Delete
                return Ok(result);
            }
            catch (Exception ex)
            {
                // 記錄錯誤
                return BadRequest(ex.Message);
            }
        }

        /// <summary>
        /// 取得業主列表 (分頁)
        /// </summary>
        /// <param name="input">查詢條件</param>
        /// <returns>業主列表</returns>
        [HttpPost]
        public async Task<ActionResult<PagedListOutput<OwnerListItemGetOutput>>> GetOwnerListAsync([FromBody] OwnerListGetInput input)
        {
             try
            {
                var result = await _ownerService.GetOwnerListAsync(input);
                return Ok(result);
            }
            catch (Exception ex)
            {
                // 記錄錯誤
                return BadRequest(ex.Message);
            }
        }

        /// <summary>
        /// 取得單一業主詳細資料
        /// </summary>
        /// <param name="ownerId">業主ID</param>
        /// <returns>業主詳細資料</returns>
        [HttpGet("{ownerId}")]
        [ProducesResponseType(typeof(OwnerGetOutput), 200)]
        [ProducesResponseType(404)]
        public async Task<ActionResult<OwnerGetOutput>> GetOwnerAsync(int ownerId)
        {
            var owner = await _ownerService.GetOwnerAsync(ownerId);
            if (owner == null)
            {
                return NotFound();
            }
            return Ok(owner);
        }
    }
} 
