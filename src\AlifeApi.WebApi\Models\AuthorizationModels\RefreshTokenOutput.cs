﻿namespace AlifeApi.WebApi.Models.AuthorizationModels
{
    /// <summary>
    /// 刷新Token
    /// </summary>
    public class RefreshTokenOutput
    {
        /// <summary>
        /// 到期日
        /// </summary>
        public DateTime? ExpireDate { get; set; }

        /// <summary>
        /// JWT Token
        /// </summary>
        public string Token { get; set; } = string.Empty;

        /// <summary>
        /// 訊息內容
        /// </summary>
        public string Message { get; set; } = string.Empty;
    }
}
