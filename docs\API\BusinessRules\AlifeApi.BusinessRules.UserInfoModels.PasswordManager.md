#### [AlifeApi.BusinessRules](index.md 'index')
### [AlifeApi.BusinessRules.UserInfoModels](AlifeApi.BusinessRules.UserInfoModels.md 'AlifeApi.BusinessRules.UserInfoModels')

## PasswordManager Class

密碼邏輯

```csharp
public class PasswordManager : AlifeApi.BusinessRules.Infrastructure.ServiceBase<AlifeApi.DataAccess.ProjectContent.alifeContext>
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; [AlifeApi.Common.InfrastructureBase](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.Common.InfrastructureBase 'AlifeApi.Common.InfrastructureBase') &#129106; [AlifeApi.BusinessRules.Infrastructure.ServiceBase&lt;](AlifeApi.BusinessRules.Infrastructure.ServiceBase_TDbContext_.md 'AlifeApi.BusinessRules.Infrastructure.ServiceBase<TDbContext>')[AlifeApi.DataAccess.ProjectContent.alifeContext](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.DataAccess.ProjectContent.alifeContext 'AlifeApi.DataAccess.ProjectContent.alifeContext')[&gt;](AlifeApi.BusinessRules.Infrastructure.ServiceBase_TDbContext_.md 'AlifeApi.BusinessRules.Infrastructure.ServiceBase<TDbContext>') &#129106; PasswordManager
### Methods

<a name='AlifeApi.BusinessRules.UserInfoModels.PasswordManager.CreatePasswordHistoryAsync(string,string)'></a>

## PasswordManager.CreatePasswordHistoryAsync(string, string) Method

增加使用者密碼使用紀錄

```csharp
public System.Threading.Tasks.Task CreatePasswordHistoryAsync(string userId, string password);
```
#### Parameters

<a name='AlifeApi.BusinessRules.UserInfoModels.PasswordManager.CreatePasswordHistoryAsync(string,string).userId'></a>

`userId` [System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

The user identifier.

<a name='AlifeApi.BusinessRules.UserInfoModels.PasswordManager.CreatePasswordHistoryAsync(string,string).password'></a>

`password` [System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

The password.

#### Returns
[System.Threading.Tasks.Task](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task 'System.Threading.Tasks.Task')

<a name='AlifeApi.BusinessRules.UserInfoModels.PasswordManager.GetDefaultPasswordAsync()'></a>

## PasswordManager.GetDefaultPasswordAsync() Method

取得預設密碼

```csharp
public System.Threading.Tasks.Task<string> GetDefaultPasswordAsync();
```

#### Returns
[System.Threading.Tasks.Task&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')  
預設密碼

<a name='AlifeApi.BusinessRules.UserInfoModels.PasswordManager.HashPassword(string)'></a>

## PasswordManager.HashPassword(string) Method

使用 HMACSHA1 進行雜湊

```csharp
public string HashPassword(string password);
```
#### Parameters

<a name='AlifeApi.BusinessRules.UserInfoModels.PasswordManager.HashPassword(string).password'></a>

`password` [System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

未雜湊密碼

#### Returns
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')  
雜湊後密碼

<a name='AlifeApi.BusinessRules.UserInfoModels.PasswordManager.IsPasswordUsedBeforeAsync(string,string)'></a>

## PasswordManager.IsPasswordUsedBeforeAsync(string, string) Method

檢查密碼是否之前用過(抓最新的3筆)

```csharp
public System.Threading.Tasks.Task<bool> IsPasswordUsedBeforeAsync(string userId, string newPassword);
```
#### Parameters

<a name='AlifeApi.BusinessRules.UserInfoModels.PasswordManager.IsPasswordUsedBeforeAsync(string,string).userId'></a>

`userId` [System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

The user identifier.

<a name='AlifeApi.BusinessRules.UserInfoModels.PasswordManager.IsPasswordUsedBeforeAsync(string,string).newPassword'></a>

`newPassword` [System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

The new password.

#### Returns
[System.Threading.Tasks.Task&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')[System.Boolean](https://docs.microsoft.com/en-us/dotnet/api/System.Boolean 'System.Boolean')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')  
`true` if [is password used before asynchronous] [the specified user identifier]; otherwise, `false`.

<a name='AlifeApi.BusinessRules.UserInfoModels.PasswordManager.ValidatePasswordFormat(string)'></a>

## PasswordManager.ValidatePasswordFormat(string) Method

Validates the format of the password according to certain criteria.

```csharp
public bool ValidatePasswordFormat(string password);
```
#### Parameters

<a name='AlifeApi.BusinessRules.UserInfoModels.PasswordManager.ValidatePasswordFormat(string).password'></a>

`password` [System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

The password to be validated.

#### Returns
[System.Boolean](https://docs.microsoft.com/en-us/dotnet/api/System.Boolean 'System.Boolean')  
`true` if the password format is valid; otherwise, `false`.

<a name='AlifeApi.BusinessRules.UserInfoModels.PasswordManager.VerifyHashedPassword(string,string)'></a>

## PasswordManager.VerifyHashedPassword(string, string) Method

將 `password` 雜湊後，與`hashedPassword`比對是否一致

```csharp
public bool VerifyHashedPassword(string hashedPassword, string password);
```
#### Parameters

<a name='AlifeApi.BusinessRules.UserInfoModels.PasswordManager.VerifyHashedPassword(string,string).hashedPassword'></a>

`hashedPassword` [System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

The hashed password.

<a name='AlifeApi.BusinessRules.UserInfoModels.PasswordManager.VerifyHashedPassword(string,string).password'></a>

`password` [System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

The password.

#### Returns
[System.Boolean](https://docs.microsoft.com/en-us/dotnet/api/System.Boolean 'System.Boolean')  
如果相符，則為 true;否則為 false

### Remarks
額外拆分原因為當雜奏規則變更，但要相容舊雜湊規則，只需變更此方法