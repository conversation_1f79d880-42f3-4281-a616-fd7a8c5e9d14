#### [AlifeApi.BusinessRules](index.md 'index')
### [AlifeApi.BusinessRules.UserInfoModels](AlifeApi.BusinessRules.UserInfoModels.md 'AlifeApi.BusinessRules.UserInfoModels')

## UserInfoServicePg Class

```csharp
public class UserInfoServicePg : AlifeApi.BusinessRules.Infrastructure.ServiceBase<AlifeApi.DataAccess.ProjectContent.alifeContext>
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; [AlifeApi.Common.InfrastructureBase](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.Common.InfrastructureBase 'AlifeApi.Common.InfrastructureBase') &#129106; [AlifeApi.BusinessRules.Infrastructure.ServiceBase&lt;](AlifeApi.BusinessRules.Infrastructure.ServiceBase_TDbContext_.md 'AlifeApi.BusinessRules.Infrastructure.ServiceBase<TDbContext>')[AlifeApi.DataAccess.ProjectContent.alifeContext](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.DataAccess.ProjectContent.alifeContext 'AlifeApi.DataAccess.ProjectContent.alifeContext')[&gt;](AlifeApi.BusinessRules.Infrastructure.ServiceBase_TDbContext_.md 'AlifeApi.BusinessRules.Infrastructure.ServiceBase<TDbContext>') &#129106; UserInfoServicePg
### Methods

<a name='AlifeApi.BusinessRules.UserInfoModels.UserInfoServicePg.ChangePasswordAsync(AlifeApi.BusinessRules.UserInfoModels.PasswordChangeInput)'></a>

## UserInfoServicePg.ChangePasswordAsync(PasswordChangeInput) Method

帳號權限管理-修改密碼

```csharp
public System.Threading.Tasks.Task<AlifeApi.BusinessRules.UserInfoModels.UserInfoUpdateOutput> ChangePasswordAsync(AlifeApi.BusinessRules.UserInfoModels.PasswordChangeInput input);
```
#### Parameters

<a name='AlifeApi.BusinessRules.UserInfoModels.UserInfoServicePg.ChangePasswordAsync(AlifeApi.BusinessRules.UserInfoModels.PasswordChangeInput).input'></a>

`input` [PasswordChangeInput](AlifeApi.BusinessRules.UserInfoModels.PasswordChangeInput.md 'AlifeApi.BusinessRules.UserInfoModels.PasswordChangeInput')

密碼修改輸入資料

#### Returns
[System.Threading.Tasks.Task&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')[UserInfoUpdateOutput](AlifeApi.BusinessRules.UserInfoModels.UserInfoUpdateOutput.md 'AlifeApi.BusinessRules.UserInfoModels.UserInfoUpdateOutput')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')  
執行結果

#### Exceptions

[System.ArgumentNullException](https://docs.microsoft.com/en-us/dotnet/api/System.ArgumentNullException 'System.ArgumentNullException')  
input

<a name='AlifeApi.BusinessRules.UserInfoModels.UserInfoServicePg.CreateUserInfoAsync(AlifeApi.BusinessRules.UserInfoModels.UserInfoCreateInputPg)'></a>

## UserInfoServicePg.CreateUserInfoAsync(UserInfoCreateInputPg) Method

帳號權限管理-帳號新增

```csharp
public System.Threading.Tasks.Task<AlifeApi.BusinessRules.UserInfoModels.UserInfoUpdateOutput> CreateUserInfoAsync(AlifeApi.BusinessRules.UserInfoModels.UserInfoCreateInputPg input);
```
#### Parameters

<a name='AlifeApi.BusinessRules.UserInfoModels.UserInfoServicePg.CreateUserInfoAsync(AlifeApi.BusinessRules.UserInfoModels.UserInfoCreateInputPg).input'></a>

`input` [UserInfoCreateInputPg](AlifeApi.BusinessRules.UserInfoModels.UserInfoCreateInputPg.md 'AlifeApi.BusinessRules.UserInfoModels.UserInfoCreateInputPg')

使用者新增輸入資料

#### Returns
[System.Threading.Tasks.Task&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')[UserInfoUpdateOutput](AlifeApi.BusinessRules.UserInfoModels.UserInfoUpdateOutput.md 'AlifeApi.BusinessRules.UserInfoModels.UserInfoUpdateOutput')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')  
執行結果

#### Exceptions

[System.ArgumentNullException](https://docs.microsoft.com/en-us/dotnet/api/System.ArgumentNullException 'System.ArgumentNullException')  
input

<a name='AlifeApi.BusinessRules.UserInfoModels.UserInfoServicePg.GetUserInfoDropdownListAsync(AlifeApi.BusinessRules.UserInfoModels.UserInfoDropdownInput)'></a>

## UserInfoServicePg.GetUserInfoDropdownListAsync(UserInfoDropdownInput) Method

取得使用者下拉選單

```csharp
public System.Threading.Tasks.Task<System.Collections.Generic.List<AlifeApi.BusinessRules.UserInfoModels.UserInfoDropdownOutput>> GetUserInfoDropdownListAsync(AlifeApi.BusinessRules.UserInfoModels.UserInfoDropdownInput input);
```
#### Parameters

<a name='AlifeApi.BusinessRules.UserInfoModels.UserInfoServicePg.GetUserInfoDropdownListAsync(AlifeApi.BusinessRules.UserInfoModels.UserInfoDropdownInput).input'></a>

`input` [UserInfoDropdownInput](AlifeApi.BusinessRules.UserInfoModels.UserInfoDropdownInput.md 'AlifeApi.BusinessRules.UserInfoModels.UserInfoDropdownInput')

查詢條件

#### Returns
[System.Threading.Tasks.Task&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')[System.Collections.Generic.List&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Collections.Generic.List-1 'System.Collections.Generic.List`1')[UserInfoDropdownOutput](AlifeApi.BusinessRules.UserInfoModels.UserInfoDropdownOutput.md 'AlifeApi.BusinessRules.UserInfoModels.UserInfoDropdownOutput')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Collections.Generic.List-1 'System.Collections.Generic.List`1')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')  
使用者下拉選單列表

<a name='AlifeApi.BusinessRules.UserInfoModels.UserInfoServicePg.GetUserInfoListAsync(AlifeApi.BusinessRules.UserInfoModels.UserInfoListGetInput)'></a>

## UserInfoServicePg.GetUserInfoListAsync(UserInfoListGetInput) Method

帳號權限管理-人員列表

```csharp
public System.Threading.Tasks.Task<AlifeApi.BusinessRules.Infrastructure.PagedListOutput<AlifeApi.BusinessRules.UserInfoModels.UserInfoListItemGetOutputPg>> GetUserInfoListAsync(AlifeApi.BusinessRules.UserInfoModels.UserInfoListGetInput input);
```
#### Parameters

<a name='AlifeApi.BusinessRules.UserInfoModels.UserInfoServicePg.GetUserInfoListAsync(AlifeApi.BusinessRules.UserInfoModels.UserInfoListGetInput).input'></a>

`input` [AlifeApi.BusinessRules.UserInfoModels.UserInfoListGetInput](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.BusinessRules.UserInfoModels.UserInfoListGetInput 'AlifeApi.BusinessRules.UserInfoModels.UserInfoListGetInput')

查詢條件

#### Returns
[System.Threading.Tasks.Task&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')[AlifeApi.BusinessRules.Infrastructure.PagedListOutput&lt;](AlifeApi.BusinessRules.Infrastructure.PagedListOutput_TDetail_.md 'AlifeApi.BusinessRules.Infrastructure.PagedListOutput<TDetail>')[UserInfoListItemGetOutputPg](AlifeApi.BusinessRules.UserInfoModels.UserInfoListItemGetOutputPg.md 'AlifeApi.BusinessRules.UserInfoModels.UserInfoListItemGetOutputPg')[&gt;](AlifeApi.BusinessRules.Infrastructure.PagedListOutput_TDetail_.md 'AlifeApi.BusinessRules.Infrastructure.PagedListOutput<TDetail>')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')  
人員列表

#### Exceptions

[System.ArgumentNullException](https://docs.microsoft.com/en-us/dotnet/api/System.ArgumentNullException 'System.ArgumentNullException')  
input

<a name='AlifeApi.BusinessRules.UserInfoModels.UserInfoServicePg.UpdateUserInfoAsync(AlifeApi.BusinessRules.UserInfoModels.UserInfoUpdateInputPg)'></a>

## UserInfoServicePg.UpdateUserInfoAsync(UserInfoUpdateInputPg) Method

修改使用者資料

```csharp
public System.Threading.Tasks.Task<AlifeApi.BusinessRules.UserInfoModels.UserInfoUpdateOutput> UpdateUserInfoAsync(AlifeApi.BusinessRules.UserInfoModels.UserInfoUpdateInputPg input);
```
#### Parameters

<a name='AlifeApi.BusinessRules.UserInfoModels.UserInfoServicePg.UpdateUserInfoAsync(AlifeApi.BusinessRules.UserInfoModels.UserInfoUpdateInputPg).input'></a>

`input` [UserInfoUpdateInputPg](AlifeApi.BusinessRules.UserInfoModels.UserInfoUpdateInputPg.md 'AlifeApi.BusinessRules.UserInfoModels.UserInfoUpdateInputPg')

使用者更新輸入資料

#### Returns
[System.Threading.Tasks.Task&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')[UserInfoUpdateOutput](AlifeApi.BusinessRules.UserInfoModels.UserInfoUpdateOutput.md 'AlifeApi.BusinessRules.UserInfoModels.UserInfoUpdateOutput')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')  
執行結果

#### Exceptions

[System.ArgumentNullException](https://docs.microsoft.com/en-us/dotnet/api/System.ArgumentNullException 'System.ArgumentNullException')  
input

<a name='AlifeApi.BusinessRules.UserInfoModels.UserInfoServicePg.UpdateUserInfoStatusAsync(AlifeApi.BusinessRules.UserInfoModels.UserInfoStatusUpdateInputPg)'></a>

## UserInfoServicePg.UpdateUserInfoStatusAsync(UserInfoStatusUpdateInputPg) Method

更新使用者帳號狀態

```csharp
public System.Threading.Tasks.Task<AlifeApi.BusinessRules.UserInfoModels.UserInfoUpdateOutput> UpdateUserInfoStatusAsync(AlifeApi.BusinessRules.UserInfoModels.UserInfoStatusUpdateInputPg input);
```
#### Parameters

<a name='AlifeApi.BusinessRules.UserInfoModels.UserInfoServicePg.UpdateUserInfoStatusAsync(AlifeApi.BusinessRules.UserInfoModels.UserInfoStatusUpdateInputPg).input'></a>

`input` [UserInfoStatusUpdateInputPg](AlifeApi.BusinessRules.UserInfoModels.UserInfoStatusUpdateInputPg.md 'AlifeApi.BusinessRules.UserInfoModels.UserInfoStatusUpdateInputPg')

使用者狀態更新輸入資料

#### Returns
[System.Threading.Tasks.Task&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')[UserInfoUpdateOutput](AlifeApi.BusinessRules.UserInfoModels.UserInfoUpdateOutput.md 'AlifeApi.BusinessRules.UserInfoModels.UserInfoUpdateOutput')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')  
執行結果

#### Exceptions

[System.ArgumentNullException](https://docs.microsoft.com/en-us/dotnet/api/System.ArgumentNullException 'System.ArgumentNullException')  
input