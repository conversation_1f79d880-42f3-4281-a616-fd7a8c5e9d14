#### [AlifeApi.BusinessRules](index.md 'index')
### [AlifeApi.BusinessRules.LogAuditModels](AlifeApi.BusinessRules.LogAuditModels.md 'AlifeApi.BusinessRules.LogAuditModels')

## LogAuditDetailGetOutput Class

日誌稽核明細

```csharp
public class LogAuditDetailGetOutput
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; LogAuditDetailGetOutput
### Properties

<a name='AlifeApi.BusinessRules.LogAuditModels.LogAuditDetailGetOutput.DeptId'></a>

## LogAuditDetailGetOutput.DeptId Property

部門代碼

```csharp
public string DeptId { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.LogAuditModels.LogAuditDetailGetOutput.DeptName'></a>

## LogAuditDetailGetOutput.DeptName Property

部門名稱

```csharp
public string DeptName { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.LogAuditModels.LogAuditDetailGetOutput.FunctionName'></a>

## LogAuditDetailGetOutput.FunctionName Property

事件

```csharp
public string FunctionName { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.LogAuditModels.LogAuditDetailGetOutput.GradeCode'></a>

## LogAuditDetailGetOutput.GradeCode Property

職稱代碼

```csharp
public string GradeCode { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.LogAuditModels.LogAuditDetailGetOutput.GradeName'></a>

## LogAuditDetailGetOutput.GradeName Property

職稱名稱

```csharp
public string GradeName { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.LogAuditModels.LogAuditDetailGetOutput.InputData'></a>

## LogAuditDetailGetOutput.InputData Property

查詢條件

```csharp
public string InputData { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.LogAuditModels.LogAuditDetailGetOutput.IP'></a>

## LogAuditDetailGetOutput.IP Property

IP位址

```csharp
public string IP { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.LogAuditModels.LogAuditDetailGetOutput.RecordTime'></a>

## LogAuditDetailGetOutput.RecordTime Property

查詢時間

```csharp
public System.DateTime RecordTime { get; set; }
```

#### Property Value
[System.DateTime](https://docs.microsoft.com/en-us/dotnet/api/System.DateTime 'System.DateTime')

<a name='AlifeApi.BusinessRules.LogAuditModels.LogAuditDetailGetOutput.UserName'></a>

## LogAuditDetailGetOutput.UserName Property

使用者

```csharp
public string UserName { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')