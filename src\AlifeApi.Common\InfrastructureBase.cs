﻿using AlifeApi.Common.DependencyInjection;

namespace AlifeApi.Common
{
    /// <summary>
    /// Provides a basic class for infrastructure components.
    /// </summary>
    public class InfrastructureBase
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="InfrastructureBase"/> class.
        /// </summary>
        /// <param name="serviceProvider">The optional service provider used for dependency resolution.</param>
        /// <exception cref="ArgumentNullException">Thrown when <paramref name="serviceProvider"/> is <c>null</c>.</exception>
        public InfrastructureBase(IServiceProvider serviceProvider)
        {
            ArgumentNullException.ThrowIfNull(serviceProvider, nameof(serviceProvider));

            LazyServiceProvider = LazyServiceProvider = new LazyServiceProvider(serviceProvider);
        }

        /// <summary>
        /// Gets the lazy service provider used for resolving services.
        /// </summary>
        protected LazyServiceProvider LazyServiceProvider { get; }
    }
}
