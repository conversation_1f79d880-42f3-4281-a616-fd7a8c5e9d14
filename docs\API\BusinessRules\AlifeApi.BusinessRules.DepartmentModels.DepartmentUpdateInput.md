#### [AlifeApi.BusinessRules](index.md 'index')
### [AlifeApi.BusinessRules.DepartmentModels](AlifeApi.BusinessRules.DepartmentModels.md 'AlifeApi.BusinessRules.DepartmentModels')

## DepartmentUpdateInput Class

更新部門輸入資料

```csharp
public class DepartmentUpdateInput
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; DepartmentUpdateInput
### Properties

<a name='AlifeApi.BusinessRules.DepartmentModels.DepartmentUpdateInput.CompanyId'></a>

## DepartmentUpdateInput.CompanyId Property

公司ID (主鍵，不可變)

```csharp
public string CompanyId { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.DepartmentModels.DepartmentUpdateInput.DepartmentId'></a>

## DepartmentUpdateInput.DepartmentId Property

部門ID (主鍵，不可變)

```csharp
public string DepartmentId { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.DepartmentModels.DepartmentUpdateInput.Name'></a>

## DepartmentUpdateInput.Name Property

部門名稱

```csharp
public string Name { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')