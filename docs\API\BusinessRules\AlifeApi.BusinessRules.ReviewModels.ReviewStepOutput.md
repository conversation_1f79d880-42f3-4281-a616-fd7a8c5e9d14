#### [AlifeApi.BusinessRules](index.md 'index')
### [AlifeApi.BusinessRules.ReviewModels](AlifeApi.BusinessRules.ReviewModels.md 'AlifeApi.BusinessRules.ReviewModels')

## ReviewStepOutput Class

審核步驟輸出模型

```csharp
public class ReviewStepOutput
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; ReviewStepOutput
### Properties

<a name='AlifeApi.BusinessRules.ReviewModels.ReviewStepOutput.Approvers'></a>

## ReviewStepOutput.Approvers Property

審核人員清單

```csharp
public System.Collections.Generic.List<AlifeApi.BusinessRules.ReviewModels.ReviewApproverOutput> Approvers { get; set; }
```

#### Property Value
[System.Collections.Generic.List&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Collections.Generic.List-1 'System.Collections.Generic.List`1')[ReviewApproverOutput](AlifeApi.BusinessRules.ReviewModels.ReviewApproverOutput.md 'AlifeApi.BusinessRules.ReviewModels.ReviewApproverOutput')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Collections.Generic.List-1 'System.Collections.Generic.List`1')

<a name='AlifeApi.BusinessRules.ReviewModels.ReviewStepOutput.Name'></a>

## ReviewStepOutput.Name Property

步驟名稱

```csharp
public string Name { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.ReviewModels.ReviewStepOutput.Notifications'></a>

## ReviewStepOutput.Notifications Property

通知方式清單

```csharp
public System.Collections.Generic.List<string> Notifications { get; set; }
```

#### Property Value
[System.Collections.Generic.List&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Collections.Generic.List-1 'System.Collections.Generic.List`1')[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Collections.Generic.List-1 'System.Collections.Generic.List`1')

<a name='AlifeApi.BusinessRules.ReviewModels.ReviewStepOutput.StepId'></a>

## ReviewStepOutput.StepId Property

步驟ID

```csharp
public int StepId { get; set; }
```

#### Property Value
[System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')

<a name='AlifeApi.BusinessRules.ReviewModels.ReviewStepOutput.StepOrder'></a>

## ReviewStepOutput.StepOrder Property

步驟順序

```csharp
public int StepOrder { get; set; }
```

#### Property Value
[System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')

<a name='AlifeApi.BusinessRules.ReviewModels.ReviewStepOutput.TimeLimit'></a>

## ReviewStepOutput.TimeLimit Property

時限（小時）

```csharp
public int TimeLimit { get; set; }
```

#### Property Value
[System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')