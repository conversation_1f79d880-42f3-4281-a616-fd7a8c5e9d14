using System;

namespace AlifeApi.BusinessRules.ParkingSpaceModels
{
    /// <summary>
    /// 停車位詳細輸出模型
    /// </summary>
    public class ParkingSpaceOutput
    {
        public int ParkingSpaceId { get; set; }
        public string SiteCode { get; set; } = null!;
        public int BuildingId { get; set; }
        public int FloorId { get; set; }
        public string SpaceNumber { get; set; } = null!;
        public string SpaceType { get; set; } = null!;
        public string? Dimensions { get; set; }
        
        /// <summary>
        /// 車位詳細位置描述 (例如 "靠近電梯", "角落位置")
        /// </summary>
        public string? Location { get; set; }
        
        public string? Remarks { get; set; }
        public DateTime CreatedTime { get; set; }
        public DateTime UpdatedTime { get; set; }
        public string CreatedUserInfoId { get; set; } = null!;
        public string UpdatedUserInfoId { get; set; } = null!;

        /// <summary>
        /// 建立者名稱 (選填)
        /// </summary>
        public string? CreatedUserName { get; set; }

        /// <summary>
        /// 更新者名稱 (選填)
        /// </summary>
        public string? UpdatedUserName { get; set; }

        /// <summary>
        /// 樓層標示 (選填)
        /// </summary>
        public string? FloorLabel { get; set; }

        /// <summary>
        /// 建築物名稱 (選填)
        /// </summary>
        public string? BuildingName { get; set; }
    }
} 
