﻿using System.Security.Cryptography;

namespace AlifeApi.Common.Util
{
    /// <summary>
    /// 產生亂數數值工具
    /// </summary>
    public static class RandomUtils
    {
        /// <summary>
        /// 產生一個非負數的亂數
        /// </summary>
        public static int Next()
        {
            using RandomNumberGenerator generator = RandomNumberGenerator.Create();
            byte[] randomByte = new byte[4];
            generator.GetBytes(randomByte);

            int value = BitConverter.ToInt32(randomByte, 0);

            return value < 0 ? -value : value;
        }

        /// <summary>
        /// 產生一個非負數且最大值 max 以下的亂數
        /// </summary>
        /// <param name="max">最大值</param>
        public static int Next(int max)
        {
            return Next() % (max + 1);
        }

        /// <summary>
        /// 產生一個非負數且最小值在 min 以上最大值在 max 以下的亂數
        /// </summary>
        /// <param name="min">最小值</param>
        /// <param name="max">最大值</param>
        public static int Next(int min, int max)
        {
            return Next(max - min) + min;
        }

        /// <summary>
        /// 產生一個非負數的double
        /// </summary>
        public static double NextDouble()
        {
            using RandomNumberGenerator generator = RandomNumberGenerator.Create();
            byte[] result = new byte[8];
            generator.GetBytes(result);

            return (double)BitConverter.ToUInt64(result, 0) / ulong.MaxValue;
        }
    }
}
