﻿using System;
using System.Collections.Generic;

namespace AlifeApi.DataAccess.ProjectContent
{
    /// <summary>
    /// 儲存住宅/商業樓層上具體可銷售的獨立房屋單位(戶)的詳細資訊與狀態。**此表為必要**，對應銷控表主體格子。
    /// </summary>
    public partial class Unit
    {
        /// <summary>
        /// 房屋單位唯一識別碼 (主鍵, 自動遞增)。
        /// </summary>
        public int UnitId { get; set; }
        /// <summary>
        /// 所屬樓層識別碼 (參考 Floors 表中 FloorType 為 &apos;住宅&apos;/&apos;商業&apos; 的樓層ID)。
        /// </summary>
        public int FloorId { get; set; }
        /// <summary>
        /// 所屬建築物識別碼 (參考 Buildings 表) - 方便查詢用。
        /// </summary>
        public int BuildingId { get; set; }
        /// <summary>
        /// 所屬案場編號 (參考 Sites 表) - 方便查詢用。
        /// </summary>
        public string SiteCode { get; set; }
        /// <summary>
        /// 戶號 (銷控表橫軸，例如: &quot;A&quot;, &quot;B&quot;, &quot;C&quot;)。
        /// </summary>
        public string UnitNumber { get; set; }
        /// <summary>
        /// 單位類型 (例如: &quot;住宅&quot;, &quot;店面&quot;, &quot;車位&quot; - 建議關聯 SYS_Code)
        /// </summary>
        public string UnitType { get; set; }
        /// <summary>
        /// 格局 (例如: &quot;3房2廳2衛&quot;)。
        /// </summary>
        public string Layout { get; set; }
        /// <summary>
        /// 座向。
        /// </summary>
        public string Orientation { get; set; }
        /// <summary>
        /// 主建物坪數。
        /// </summary>
        public decimal? MainArea { get; set; }
        /// <summary>
        /// 附屬建物坪數。
        /// </summary>
        public decimal? AuxiliaryArea { get; set; }
        /// <summary>
        /// 公設分攤坪數。
        /// </summary>
        public decimal? PublicAreaShare { get; set; }
        /// <summary>
        /// 權狀總坪數。
        /// </summary>
        public decimal TotalArea { get; set; }
        /// <summary>
        /// 表價 (牌價)。
        /// </summary>
        public decimal? ListPrice { get; set; }
        /// <summary>
        /// 底價。
        /// </summary>
        public decimal? MinimumPrice { get; set; }
        /// <summary>
        /// 實際成交價 (成交後填入)。
        /// </summary>
        public decimal? TransactionPrice { get; set; }
        /// <summary>
        /// 單位目前的**獨立銷售狀態** (例如: &apos;可售&apos;, &apos;已預訂&apos;, &apos;已售&apos;, &apos;保留&apos; - 建議關聯 SYS_Code)。
        /// </summary>
        public string Status { get; set; }
        /// <summary>
        /// 權狀是否含公設 (True/False)。
        /// </summary>
        public bool? IsPublicAreaIncluded { get; set; }
        /// <summary>
        /// 關聯的停車位ID列表，以逗號分隔 (參考 ParkingSpaces 表)。住宅單位用於記錄建議搭配的車位；車位單位用於記錄對應的實體車位
        /// </summary>
        public string AssociatedParkingSpaceIds { get; set; }
        /// <summary>
        /// 備註。
        /// </summary>
        public string Remarks { get; set; }
        /// <summary>
        /// 資料創建時間 (TIMESTAMP WITHOUT TIME ZONE)。
        /// </summary>
        public DateTime CreatedTime { get; set; }
        /// <summary>
        /// 資料最後更新時間 (TIMESTAMP WITHOUT TIME ZONE)。
        /// </summary>
        public DateTime UpdatedTime { get; set; }
        /// <summary>
        /// 建立者 (參考 UserInfo 表)。
        /// </summary>
        public string CreatedUserInfoId { get; set; }
        /// <summary>
        /// 更新者 (參考 UserInfo 表)。
        /// </summary>
        public string UpdatedUserInfoId { get; set; }
        /// <summary>
        /// 小公面積 (坪)
        /// </summary>
        public decimal? SmallPublicArea { get; set; }
        /// <summary>
        /// 大公面積 (坪)
        /// </summary>
        public decimal? LargePublicArea { get; set; }
        /// <summary>
        /// 雨遮面積 (坪)
        /// </summary>
        public decimal? AwningArea { get; set; }
        /// <summary>
        /// 陽台面積 (坪)
        /// </summary>
        public decimal? BalconyArea { get; set; }
    }
}
