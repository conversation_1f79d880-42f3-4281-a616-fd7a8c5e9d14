#### [AlifeApi.BusinessRules](index.md 'index')
### [AlifeApi.BusinessRules.RoleGroupModels](AlifeApi.BusinessRules.RoleGroupModels.md 'AlifeApi.BusinessRules.RoleGroupModels')

## RoleGroupPermissionUpdateInputPg Class

更新角色權限（PostgreSQL 版本）

```csharp
public class RoleGroupPermissionUpdateInputPg : AlifeApi.BusinessRules.RoleGroupModels.RoleGroupCreateInputPg
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; [RoleGroupCreateInputPg](AlifeApi.BusinessRules.RoleGroupModels.RoleGroupCreateInputPg.md 'AlifeApi.BusinessRules.RoleGroupModels.RoleGroupCreateInputPg') &#129106; RoleGroupPermissionUpdateInputPg
### Properties

<a name='AlifeApi.BusinessRules.RoleGroupModels.RoleGroupPermissionUpdateInputPg.Id'></a>

## RoleGroupPermissionUpdateInputPg.Id Property

角色ID

```csharp
public string Id { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')