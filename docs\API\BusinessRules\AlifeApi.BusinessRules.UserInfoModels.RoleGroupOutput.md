#### [AlifeApi.BusinessRules](index.md 'index')
### [AlifeApi.BusinessRules.UserInfoModels](AlifeApi.BusinessRules.UserInfoModels.md 'AlifeApi.BusinessRules.UserInfoModels')

## RoleGroupOutput Class

角色群組輸出資料

```csharp
public class RoleGroupOutput
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; RoleGroupOutput
### Properties

<a name='AlifeApi.BusinessRules.UserInfoModels.RoleGroupOutput.Name'></a>

## RoleGroupOutput.Name Property

名稱

```csharp
public string Name { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.UserInfoModels.RoleGroupOutput.RoleGroupId'></a>

## RoleGroupOutput.RoleGroupId Property

角色群組ID

```csharp
public string RoleGroupId { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')