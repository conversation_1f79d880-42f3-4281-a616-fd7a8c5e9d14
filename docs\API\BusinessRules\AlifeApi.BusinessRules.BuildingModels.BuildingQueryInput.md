#### [AlifeApi.BusinessRules](index.md 'index')
### [AlifeApi.BusinessRules.BuildingModels](AlifeApi.BusinessRules.BuildingModels.md 'AlifeApi.BusinessRules.BuildingModels')

## BuildingQueryInput Class

建築物列表查詢輸入模型

```csharp
public class BuildingQueryInput : AlifeApi.BusinessRules.Infrastructure.PagedListInput
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; [PagedListInput](AlifeApi.BusinessRules.Infrastructure.PagedListInput.md 'AlifeApi.BusinessRules.Infrastructure.PagedListInput') &#129106; BuildingQueryInput
### Properties

<a name='AlifeApi.BusinessRules.BuildingModels.BuildingQueryInput.BuildingName'></a>

## BuildingQueryInput.BuildingName Property

建築物名稱 (模糊查詢)

```csharp
public string? BuildingName { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.BuildingModels.BuildingQueryInput.BuildingType'></a>

## BuildingQueryInput.BuildingType Property

建築類型 (篩選)

```csharp
public string? BuildingType { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.BuildingModels.BuildingQueryInput.SiteCode'></a>

## BuildingQueryInput.SiteCode Property

案場代碼 (篩選)

```csharp
public string? SiteCode { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')