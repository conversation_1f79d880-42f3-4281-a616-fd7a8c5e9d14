using System.ComponentModel.DataAnnotations;

namespace AlifeApi.BusinessRules.ReviewModels
{
    /// <summary>
    /// 審核流程輸入模型
    /// </summary>
    public class ReviewTaskInput
    {
        /// <summary>
        /// 流程名稱
        /// </summary>
        [Required]
        public string Name { get; set; }

        /// <summary>
        /// 流程描述
        /// </summary>
        public string Description { get; set; }

        /// <summary>
        /// 審核步驟清單
        /// </summary>
        [Required]
        public List<ReviewStepInput> Steps { get; set; }
    }

    /// <summary>
    /// 審核步驟輸入模型
    /// </summary>
    public class ReviewStepInput
    {
        /// <summary>
        /// 步驟名稱
        /// </summary>
        [Required]
        public string Name { get; set; }

        /// <summary>
        /// 審核人員清單
        /// </summary>
        [Required]
        public List<string> ApproverIds { get; set; }

        /// <summary>
        /// 時限（小時）
        /// </summary>
        [Required]
        [Range(1, int.MaxValue)]
        public int TimeLimit { get; set; }

        /// <summary>
        /// 系統通知
        /// </summary>
        public bool SystemNotification { get; set; } = true;

        /// <summary>
        /// 電子郵件通知
        /// </summary>
        public bool EmailNotification { get; set; }

        /// <summary>
        /// 簡訊通知
        /// </summary>
        public bool SmsNotification { get; set; }
    }
} 
