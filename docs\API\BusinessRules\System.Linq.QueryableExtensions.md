#### [AlifeApi.BusinessRules](index.md 'index')
### [System.Linq](System.Linq.md 'System.Linq')

## QueryableExtensions Class

IQueryable 的擴充方法

```csharp
internal static class QueryableExtensions
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; QueryableExtensions
### Methods

<a name='System.Linq.QueryableExtensions.ToPagedListOutput_TDetail_(thisSystem.Collections.Generic.IEnumerable_TDetail_,AlifeApi.BusinessRules.Infrastructure.PagedListInput)'></a>

## QueryableExtensions.ToPagedListOutput<TDetail>(this IEnumerable<TDetail>, PagedListInput) Method

將 IQueryable 來源根據提供的輸入參數轉換為分頁列表

```csharp
public static AlifeApi.BusinessRules.Infrastructure.PagedListOutput<TDetail> ToPagedListOutput<TDetail>(this System.Collections.Generic.IEnumerable<TDetail> source, AlifeApi.BusinessRules.Infrastructure.PagedListInput input);
```
#### Type parameters

<a name='System.Linq.QueryableExtensions.ToPagedListOutput_TDetail_(thisSystem.Collections.Generic.IEnumerable_TDetail_,AlifeApi.BusinessRules.Infrastructure.PagedListInput).TDetail'></a>

`TDetail`

IQueryable 來源中的元素類型
#### Parameters

<a name='System.Linq.QueryableExtensions.ToPagedListOutput_TDetail_(thisSystem.Collections.Generic.IEnumerable_TDetail_,AlifeApi.BusinessRules.Infrastructure.PagedListInput).source'></a>

`source` [System.Collections.Generic.IEnumerable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Collections.Generic.IEnumerable-1 'System.Collections.Generic.IEnumerable`1')[TDetail](System.Linq.QueryableExtensions.md#System.Linq.QueryableExtensions.ToPagedListOutput_TDetail_(thisSystem.Collections.Generic.IEnumerable_TDetail_,AlifeApi.BusinessRules.Infrastructure.PagedListInput).TDetail 'System.Linq.QueryableExtensions.ToPagedListOutput<TDetail>(this System.Collections.Generic.IEnumerable<TDetail>, AlifeApi.BusinessRules.Infrastructure.PagedListInput).TDetail')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Collections.Generic.IEnumerable-1 'System.Collections.Generic.IEnumerable`1')

要進行分頁和篩選的 IQueryable 來源

<a name='System.Linq.QueryableExtensions.ToPagedListOutput_TDetail_(thisSystem.Collections.Generic.IEnumerable_TDetail_,AlifeApi.BusinessRules.Infrastructure.PagedListInput).input'></a>

`input` [PagedListInput](AlifeApi.BusinessRules.Infrastructure.PagedListInput.md 'AlifeApi.BusinessRules.Infrastructure.PagedListInput')

分頁和篩選的輸入參數

#### Returns
[AlifeApi.BusinessRules.Infrastructure.PagedListOutput&lt;](AlifeApi.BusinessRules.Infrastructure.PagedListOutput_TDetail_.md 'AlifeApi.BusinessRules.Infrastructure.PagedListOutput<TDetail>')[TDetail](System.Linq.QueryableExtensions.md#System.Linq.QueryableExtensions.ToPagedListOutput_TDetail_(thisSystem.Collections.Generic.IEnumerable_TDetail_,AlifeApi.BusinessRules.Infrastructure.PagedListInput).TDetail 'System.Linq.QueryableExtensions.ToPagedListOutput<TDetail>(this System.Collections.Generic.IEnumerable<TDetail>, AlifeApi.BusinessRules.Infrastructure.PagedListInput).TDetail')[&gt;](AlifeApi.BusinessRules.Infrastructure.PagedListOutput_TDetail_.md 'AlifeApi.BusinessRules.Infrastructure.PagedListOutput<TDetail>')  
包含分頁列表輸出結果

<a name='System.Linq.QueryableExtensions.ToPagedListOutputAsync_TDetail_(thisSystem.Linq.IQueryable_TDetail_,AlifeApi.BusinessRules.Infrastructure.PagedListInput)'></a>

## QueryableExtensions.ToPagedListOutputAsync<TDetail>(this IQueryable<TDetail>, PagedListInput) Method

將 IQueryable 來源根據提供的輸入參數轉換為分頁列表

```csharp
public static System.Threading.Tasks.Task<AlifeApi.BusinessRules.Infrastructure.PagedListOutput<TDetail>> ToPagedListOutputAsync<TDetail>(this System.Linq.IQueryable<TDetail> source, AlifeApi.BusinessRules.Infrastructure.PagedListInput input);
```
#### Type parameters

<a name='System.Linq.QueryableExtensions.ToPagedListOutputAsync_TDetail_(thisSystem.Linq.IQueryable_TDetail_,AlifeApi.BusinessRules.Infrastructure.PagedListInput).TDetail'></a>

`TDetail`

IQueryable 來源中的元素類型
#### Parameters

<a name='System.Linq.QueryableExtensions.ToPagedListOutputAsync_TDetail_(thisSystem.Linq.IQueryable_TDetail_,AlifeApi.BusinessRules.Infrastructure.PagedListInput).source'></a>

`source` [System.Linq.IQueryable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Linq.IQueryable-1 'System.Linq.IQueryable`1')[TDetail](System.Linq.QueryableExtensions.md#System.Linq.QueryableExtensions.ToPagedListOutputAsync_TDetail_(thisSystem.Linq.IQueryable_TDetail_,AlifeApi.BusinessRules.Infrastructure.PagedListInput).TDetail 'System.Linq.QueryableExtensions.ToPagedListOutputAsync<TDetail>(this System.Linq.IQueryable<TDetail>, AlifeApi.BusinessRules.Infrastructure.PagedListInput).TDetail')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Linq.IQueryable-1 'System.Linq.IQueryable`1')

要進行分頁和篩選的 IQueryable 來源

<a name='System.Linq.QueryableExtensions.ToPagedListOutputAsync_TDetail_(thisSystem.Linq.IQueryable_TDetail_,AlifeApi.BusinessRules.Infrastructure.PagedListInput).input'></a>

`input` [PagedListInput](AlifeApi.BusinessRules.Infrastructure.PagedListInput.md 'AlifeApi.BusinessRules.Infrastructure.PagedListInput')

分頁和篩選的輸入參數

#### Returns
[System.Threading.Tasks.Task&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')[AlifeApi.BusinessRules.Infrastructure.PagedListOutput&lt;](AlifeApi.BusinessRules.Infrastructure.PagedListOutput_TDetail_.md 'AlifeApi.BusinessRules.Infrastructure.PagedListOutput<TDetail>')[TDetail](System.Linq.QueryableExtensions.md#System.Linq.QueryableExtensions.ToPagedListOutputAsync_TDetail_(thisSystem.Linq.IQueryable_TDetail_,AlifeApi.BusinessRules.Infrastructure.PagedListInput).TDetail 'System.Linq.QueryableExtensions.ToPagedListOutputAsync<TDetail>(this System.Linq.IQueryable<TDetail>, AlifeApi.BusinessRules.Infrastructure.PagedListInput).TDetail')[&gt;](AlifeApi.BusinessRules.Infrastructure.PagedListOutput_TDetail_.md 'AlifeApi.BusinessRules.Infrastructure.PagedListOutput<TDetail>')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')  
包含分頁列表輸出結果

<a name='System.Linq.QueryableExtensions.WhereIf_TEntity_(thisSystem.Linq.IQueryable_TEntity_,bool,System.Func_System.Linq.Expressions.Expression_System.Func_TEntity,bool___)'></a>

## QueryableExtensions.WhereIf<TEntity>(this IQueryable<TEntity>, bool, Func<Expression<Func<TEntity,bool>>>) Method

Wheres if.

```csharp
public static System.Linq.IQueryable<TEntity> WhereIf<TEntity>(this System.Linq.IQueryable<TEntity> source, bool condition, System.Func<System.Linq.Expressions.Expression<System.Func<TEntity,bool>>> predicateGenerator);
```
#### Type parameters

<a name='System.Linq.QueryableExtensions.WhereIf_TEntity_(thisSystem.Linq.IQueryable_TEntity_,bool,System.Func_System.Linq.Expressions.Expression_System.Func_TEntity,bool___).TEntity'></a>

`TEntity`

The type of the entity.
#### Parameters

<a name='System.Linq.QueryableExtensions.WhereIf_TEntity_(thisSystem.Linq.IQueryable_TEntity_,bool,System.Func_System.Linq.Expressions.Expression_System.Func_TEntity,bool___).source'></a>

`source` [System.Linq.IQueryable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Linq.IQueryable-1 'System.Linq.IQueryable`1')[TEntity](System.Linq.QueryableExtensions.md#System.Linq.QueryableExtensions.WhereIf_TEntity_(thisSystem.Linq.IQueryable_TEntity_,bool,System.Func_System.Linq.Expressions.Expression_System.Func_TEntity,bool___).TEntity 'System.Linq.QueryableExtensions.WhereIf<TEntity>(this System.Linq.IQueryable<TEntity>, bool, System.Func<System.Linq.Expressions.Expression<System.Func<TEntity,bool>>>).TEntity')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Linq.IQueryable-1 'System.Linq.IQueryable`1')

The source.

<a name='System.Linq.QueryableExtensions.WhereIf_TEntity_(thisSystem.Linq.IQueryable_TEntity_,bool,System.Func_System.Linq.Expressions.Expression_System.Func_TEntity,bool___).condition'></a>

`condition` [System.Boolean](https://docs.microsoft.com/en-us/dotnet/api/System.Boolean 'System.Boolean')

if set to `true` [condition].

<a name='System.Linq.QueryableExtensions.WhereIf_TEntity_(thisSystem.Linq.IQueryable_TEntity_,bool,System.Func_System.Linq.Expressions.Expression_System.Func_TEntity,bool___).predicateGenerator'></a>

`predicateGenerator` [System.Func&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Func-1 'System.Func`1')[System.Linq.Expressions.Expression&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Linq.Expressions.Expression-1 'System.Linq.Expressions.Expression`1')[System.Func&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Func-2 'System.Func`2')[TEntity](System.Linq.QueryableExtensions.md#System.Linq.QueryableExtensions.WhereIf_TEntity_(thisSystem.Linq.IQueryable_TEntity_,bool,System.Func_System.Linq.Expressions.Expression_System.Func_TEntity,bool___).TEntity 'System.Linq.QueryableExtensions.WhereIf<TEntity>(this System.Linq.IQueryable<TEntity>, bool, System.Func<System.Linq.Expressions.Expression<System.Func<TEntity,bool>>>).TEntity')[,](https://docs.microsoft.com/en-us/dotnet/api/System.Func-2 'System.Func`2')[System.Boolean](https://docs.microsoft.com/en-us/dotnet/api/System.Boolean 'System.Boolean')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Func-2 'System.Func`2')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Linq.Expressions.Expression-1 'System.Linq.Expressions.Expression`1')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Func-1 'System.Func`1')

The predicate generator.

#### Returns
[System.Linq.IQueryable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Linq.IQueryable-1 'System.Linq.IQueryable`1')[TEntity](System.Linq.QueryableExtensions.md#System.Linq.QueryableExtensions.WhereIf_TEntity_(thisSystem.Linq.IQueryable_TEntity_,bool,System.Func_System.Linq.Expressions.Expression_System.Func_TEntity,bool___).TEntity 'System.Linq.QueryableExtensions.WhereIf<TEntity>(this System.Linq.IQueryable<TEntity>, bool, System.Func<System.Linq.Expressions.Expression<System.Func<TEntity,bool>>>).TEntity')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Linq.IQueryable-1 'System.Linq.IQueryable`1')

<a name='System.Linq.QueryableExtensions.WhereIf_TEntity_(thisSystem.Linq.IQueryable_TEntity_,bool,System.Linq.Expressions.Expression_System.Func_TEntity,bool__)'></a>

## QueryableExtensions.WhereIf<TEntity>(this IQueryable<TEntity>, bool, Expression<Func<TEntity,bool>>) Method

Wheres if.

```csharp
public static System.Linq.IQueryable<TEntity> WhereIf<TEntity>(this System.Linq.IQueryable<TEntity> source, bool condition, System.Linq.Expressions.Expression<System.Func<TEntity,bool>> predicate);
```
#### Type parameters

<a name='System.Linq.QueryableExtensions.WhereIf_TEntity_(thisSystem.Linq.IQueryable_TEntity_,bool,System.Linq.Expressions.Expression_System.Func_TEntity,bool__).TEntity'></a>

`TEntity`

The type of the entity.
#### Parameters

<a name='System.Linq.QueryableExtensions.WhereIf_TEntity_(thisSystem.Linq.IQueryable_TEntity_,bool,System.Linq.Expressions.Expression_System.Func_TEntity,bool__).source'></a>

`source` [System.Linq.IQueryable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Linq.IQueryable-1 'System.Linq.IQueryable`1')[TEntity](System.Linq.QueryableExtensions.md#System.Linq.QueryableExtensions.WhereIf_TEntity_(thisSystem.Linq.IQueryable_TEntity_,bool,System.Linq.Expressions.Expression_System.Func_TEntity,bool__).TEntity 'System.Linq.QueryableExtensions.WhereIf<TEntity>(this System.Linq.IQueryable<TEntity>, bool, System.Linq.Expressions.Expression<System.Func<TEntity,bool>>).TEntity')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Linq.IQueryable-1 'System.Linq.IQueryable`1')

The source.

<a name='System.Linq.QueryableExtensions.WhereIf_TEntity_(thisSystem.Linq.IQueryable_TEntity_,bool,System.Linq.Expressions.Expression_System.Func_TEntity,bool__).condition'></a>

`condition` [System.Boolean](https://docs.microsoft.com/en-us/dotnet/api/System.Boolean 'System.Boolean')

if set to `true` [condition].

<a name='System.Linq.QueryableExtensions.WhereIf_TEntity_(thisSystem.Linq.IQueryable_TEntity_,bool,System.Linq.Expressions.Expression_System.Func_TEntity,bool__).predicate'></a>

`predicate` [System.Linq.Expressions.Expression&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Linq.Expressions.Expression-1 'System.Linq.Expressions.Expression`1')[System.Func&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Func-2 'System.Func`2')[TEntity](System.Linq.QueryableExtensions.md#System.Linq.QueryableExtensions.WhereIf_TEntity_(thisSystem.Linq.IQueryable_TEntity_,bool,System.Linq.Expressions.Expression_System.Func_TEntity,bool__).TEntity 'System.Linq.QueryableExtensions.WhereIf<TEntity>(this System.Linq.IQueryable<TEntity>, bool, System.Linq.Expressions.Expression<System.Func<TEntity,bool>>).TEntity')[,](https://docs.microsoft.com/en-us/dotnet/api/System.Func-2 'System.Func`2')[System.Boolean](https://docs.microsoft.com/en-us/dotnet/api/System.Boolean 'System.Boolean')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Func-2 'System.Func`2')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Linq.Expressions.Expression-1 'System.Linq.Expressions.Expression`1')

The predicate.

#### Returns
[System.Linq.IQueryable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Linq.IQueryable-1 'System.Linq.IQueryable`1')[TEntity](System.Linq.QueryableExtensions.md#System.Linq.QueryableExtensions.WhereIf_TEntity_(thisSystem.Linq.IQueryable_TEntity_,bool,System.Linq.Expressions.Expression_System.Func_TEntity,bool__).TEntity 'System.Linq.QueryableExtensions.WhereIf<TEntity>(this System.Linq.IQueryable<TEntity>, bool, System.Linq.Expressions.Expression<System.Func<TEntity,bool>>).TEntity')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Linq.IQueryable-1 'System.Linq.IQueryable`1')