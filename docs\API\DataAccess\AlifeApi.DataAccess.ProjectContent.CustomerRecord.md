#### [AlifeApi.DataAccess](index.md 'index')
### [AlifeApi.DataAccess.ProjectContent](AlifeApi.DataAccess.ProjectContent.md 'AlifeApi.DataAccess.ProjectContent')

## CustomerRecord Class

客戶互動或訪談記錄表

```csharp
public class CustomerRecord
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; CustomerRecord
### Properties

<a name='AlifeApi.DataAccess.ProjectContent.CustomerRecord.CreatedTime'></a>

## CustomerRecord.CreatedTime Property

此記錄的創建時間

```csharp
public System.DateTime CreatedTime { get; set; }
```

#### Property Value
[System.DateTime](https://docs.microsoft.com/en-us/dotnet/api/System.DateTime 'System.DateTime')

<a name='AlifeApi.DataAccess.ProjectContent.CustomerRecord.CreatedUserInfoId'></a>

## CustomerRecord.CreatedUserInfoId Property

此記錄創建人員的 UserInfo ID (VARCHAR(15) 外鍵)

```csharp
public string CreatedUserInfoId { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContent.CustomerRecord.CustomerId'></a>

## CustomerRecord.CustomerId Property

關聯的客戶ID (外鍵)

```csharp
public int CustomerId { get; set; }
```

#### Property Value
[System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')

<a name='AlifeApi.DataAccess.ProjectContent.CustomerRecord.CustomerLevel'></a>

## CustomerRecord.CustomerLevel Property

客戶分級或標籤

```csharp
public string CustomerLevel { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContent.CustomerRecord.CustomerRecordId'></a>

## CustomerRecord.CustomerRecordId Property

記錄唯一識別碼 (主鍵)

```csharp
public int CustomerRecordId { get; set; }
```

#### Property Value
[System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')

<a name='AlifeApi.DataAccess.ProjectContent.CustomerRecord.HandledBy'></a>

## CustomerRecord.HandledBy Property

負責此次互動的人員資訊

```csharp
public string HandledBy { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContent.CustomerRecord.Notes'></a>

## CustomerRecord.Notes Property

詳細的記錄內容

```csharp
public string Notes { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContent.CustomerRecord.RecordedAt'></a>

## CustomerRecord.RecordedAt Property

互動發生的實際時間或記錄時間

```csharp
public System.DateTime RecordedAt { get; set; }
```

#### Property Value
[System.DateTime](https://docs.microsoft.com/en-us/dotnet/api/System.DateTime 'System.DateTime')

<a name='AlifeApi.DataAccess.ProjectContent.CustomerRecord.RecordType'></a>

## CustomerRecord.RecordType Property

互動或記錄的類型

```csharp
public string RecordType { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContent.CustomerRecord.UpdatedTime'></a>

## CustomerRecord.UpdatedTime Property

此記錄的最後更新時間

```csharp
public System.DateTime UpdatedTime { get; set; }
```

#### Property Value
[System.DateTime](https://docs.microsoft.com/en-us/dotnet/api/System.DateTime 'System.DateTime')

<a name='AlifeApi.DataAccess.ProjectContent.CustomerRecord.UpdatedUserInfoId'></a>

## CustomerRecord.UpdatedUserInfoId Property

此記錄最後更新人員的 UserInfo ID (VARCHAR(15) 外鍵)

```csharp
public string UpdatedUserInfoId { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')