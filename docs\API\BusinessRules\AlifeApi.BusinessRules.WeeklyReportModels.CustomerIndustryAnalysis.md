#### [AlifeApi.BusinessRules](index.md 'index')
### [AlifeApi.BusinessRules.WeeklyReportModels](AlifeApi.BusinessRules.WeeklyReportModels.md 'AlifeApi.BusinessRules.WeeklyReportModels')

## CustomerIndustryAnalysis Class

客戶行業分析

```csharp
public class CustomerIndustryAnalysis
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; CustomerIndustryAnalysis
### Properties

<a name='AlifeApi.BusinessRules.WeeklyReportModels.CustomerIndustryAnalysis.Industry'></a>

## CustomerIndustryAnalysis.Industry Property

行業

```csharp
public string Industry { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.WeeklyReportModels.CustomerIndustryAnalysis.ReturnVisitCount'></a>

## CustomerIndustryAnalysis.ReturnVisitCount Property

回訪數

```csharp
public int ReturnVisitCount { get; set; }
```

#### Property Value
[System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')

<a name='AlifeApi.BusinessRules.WeeklyReportModels.CustomerIndustryAnalysis.ReturnVisitPercentage'></a>

## CustomerIndustryAnalysis.ReturnVisitPercentage Property

回訪佔比

```csharp
public decimal ReturnVisitPercentage { get; set; }
```

#### Property Value
[System.Decimal](https://docs.microsoft.com/en-us/dotnet/api/System.Decimal 'System.Decimal')

<a name='AlifeApi.BusinessRules.WeeklyReportModels.CustomerIndustryAnalysis.TransactionCount'></a>

## CustomerIndustryAnalysis.TransactionCount Property

成交數

```csharp
public int TransactionCount { get; set; }
```

#### Property Value
[System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')

<a name='AlifeApi.BusinessRules.WeeklyReportModels.CustomerIndustryAnalysis.TransactionPercentage'></a>

## CustomerIndustryAnalysis.TransactionPercentage Property

成交佔比

```csharp
public decimal TransactionPercentage { get; set; }
```

#### Property Value
[System.Decimal](https://docs.microsoft.com/en-us/dotnet/api/System.Decimal 'System.Decimal')

<a name='AlifeApi.BusinessRules.WeeklyReportModels.CustomerIndustryAnalysis.VisitorCount'></a>

## CustomerIndustryAnalysis.VisitorCount Property

來客數

```csharp
public int VisitorCount { get; set; }
```

#### Property Value
[System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')

<a name='AlifeApi.BusinessRules.WeeklyReportModels.CustomerIndustryAnalysis.VisitorPercentage'></a>

## CustomerIndustryAnalysis.VisitorPercentage Property

來客佔比

```csharp
public decimal VisitorPercentage { get; set; }
```

#### Property Value
[System.Decimal](https://docs.microsoft.com/en-us/dotnet/api/System.Decimal 'System.Decimal')