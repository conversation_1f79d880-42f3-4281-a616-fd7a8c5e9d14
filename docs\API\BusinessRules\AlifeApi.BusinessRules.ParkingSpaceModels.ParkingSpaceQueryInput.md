#### [AlifeApi.BusinessRules](index.md 'index')
### [AlifeApi.BusinessRules.ParkingSpaceModels](AlifeApi.BusinessRules.ParkingSpaceModels.md 'AlifeApi.BusinessRules.ParkingSpaceModels')

## ParkingSpaceQueryInput Class

停車位列表查詢輸入模型

```csharp
public class ParkingSpaceQueryInput : AlifeApi.BusinessRules.Infrastructure.PagedListInput
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; [PagedListInput](AlifeApi.BusinessRules.Infrastructure.PagedListInput.md 'AlifeApi.BusinessRules.Infrastructure.PagedListInput') &#129106; ParkingSpaceQueryInput
### Properties

<a name='AlifeApi.BusinessRules.ParkingSpaceModels.ParkingSpaceQueryInput.BuildingId'></a>

## ParkingSpaceQueryInput.BuildingId Property

建築物ID (篩選)

```csharp
public System.Nullable<int> BuildingId { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

<a name='AlifeApi.BusinessRules.ParkingSpaceModels.ParkingSpaceQueryInput.Location'></a>

## ParkingSpaceQueryInput.Location Property

車位位置 (篩選)

```csharp
public string? Location { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.ParkingSpaceModels.ParkingSpaceQueryInput.SiteCode'></a>

## ParkingSpaceQueryInput.SiteCode Property

案場代碼 (篩選)

```csharp
public string? SiteCode { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.ParkingSpaceModels.ParkingSpaceQueryInput.SpaceNumber'></a>

## ParkingSpaceQueryInput.SpaceNumber Property

車位編號 (篩選)

```csharp
public string? SpaceNumber { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.ParkingSpaceModels.ParkingSpaceQueryInput.SpaceType'></a>

## ParkingSpaceQueryInput.SpaceType Property

車位類型 (篩選)

```csharp
public string? SpaceType { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')