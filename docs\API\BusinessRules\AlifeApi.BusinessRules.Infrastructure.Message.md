#### [AlifeApi.BusinessRules](index.md 'index')
### [AlifeApi.BusinessRules.Infrastructure](AlifeApi.BusinessRules.Infrastructure.md 'AlifeApi.BusinessRules.Infrastructure')

## Message Enum

系統訊息

```csharp
public enum Message
```
### Fields

<a name='AlifeApi.BusinessRules.Infrastructure.Message.AccountAlreadyExist'></a>

`AccountAlreadyExist` 122

帳號已存在 AccountAlreadyExist

<a name='AlifeApi.BusinessRules.Infrastructure.Message.AccountFail'></a>

`AccountFail` 131

帳號輸入錯誤

<a name='AlifeApi.BusinessRules.Infrastructure.Message.AccountLoginFailTooManyTimeNeedToWait'></a>

`AccountLoginFailTooManyTimeNeedToWait` 1001

密碼輸入錯誤太多次，請稍等

<a name='AlifeApi.BusinessRules.Infrastructure.Message.CaptchaFail'></a>

`CaptchaFail` 117

CaptchaFail

<a name='AlifeApi.BusinessRules.Infrastructure.Message.CheckAccountIsValid'></a>

`CheckAccountIsValid` 133

請確認帳號是否停用或不符合使用效期

<a name='AlifeApi.BusinessRules.Infrastructure.Message.DefaultPassword'></a>

`DefaultPassword` 1002

預設密碼

<a name='AlifeApi.BusinessRules.Infrastructure.Message.Http_400_BadRequest'></a>

`Http_400_BadRequest` 400

HTTP 400 Bad Request

<a name='AlifeApi.BusinessRules.Infrastructure.Message.Http_401_Unauthorized'></a>

`Http_401_Unauthorized` 401

HTTP 401 Http_401_Unauthorized

<a name='AlifeApi.BusinessRules.Infrastructure.Message.Http_403_Forbidden'></a>

`Http_403_Forbidden` 403

HTTP 403 Http_403_Forbidden

<a name='AlifeApi.BusinessRules.Infrastructure.Message.Http_404_NotFound'></a>

`Http_404_NotFound` 404

HTTP 404 Not Found

<a name='AlifeApi.BusinessRules.Infrastructure.Message.Http_500_InternalServerError'></a>

`Http_500_InternalServerError` 500

HTTP 500 Internal Server Error

<a name='AlifeApi.BusinessRules.Infrastructure.Message.Http_501_NotImplemented'></a>

`Http_501_NotImplemented` 501

HTTP 501 Not Implemented

<a name='AlifeApi.BusinessRules.Infrastructure.Message.InputDataFormatError'></a>

`InputDataFormatError` 410

非法輸入格式

<a name='AlifeApi.BusinessRules.Infrastructure.Message.LoginInfoWithoutCaptcha'></a>

`LoginInfoWithoutCaptcha` 120

登入資訊無 Captcha

<a name='AlifeApi.BusinessRules.Infrastructure.Message.PasswordChange'></a>

`PasswordChange` 134

密碼長時間未變更

<a name='AlifeApi.BusinessRules.Infrastructure.Message.PasswordFail'></a>

`PasswordFail` 132

密碼輸入錯誤

<a name='AlifeApi.BusinessRules.Infrastructure.Message.PasswordUsedRecently'></a>

`PasswordUsedRecently` 1000

密碼與前幾次相同

<a name='AlifeApi.BusinessRules.Infrastructure.Message.Success'></a>

`Success` 0

成功

<a name='AlifeApi.BusinessRules.Infrastructure.Message.SystemError'></a>

`SystemError` 999

系統異常