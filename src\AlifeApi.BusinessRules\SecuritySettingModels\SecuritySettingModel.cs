﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AlifeApi.BusinessRules.SecuritySettingModels
{
    public class SecuritySettingModel
    {
        /// <summary>
        /// 鍵
        /// </summary>
        [Required]
        public string Key { get; set; }

        /// <summary>
        /// 值
        /// </summary>
        [Required]
        public string Value { get; set; }

        /// <summary>
        /// 詳細名稱
        /// </summary>
        [Required]
        public string Name { get; set; }

        /// <summary>
        /// 是否啟用
        /// </summary>
        [Required]
        public bool Enabled { get; set; }
    }
}
