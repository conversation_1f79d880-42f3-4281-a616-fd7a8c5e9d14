#### [AlifeApi.BusinessRules](index.md 'index')
### [AlifeApi.BusinessRules.OwnerModels](AlifeApi.BusinessRules.OwnerModels.md 'AlifeApi.BusinessRules.OwnerModels')

## OwnerListGetInput Class

查詢業主列表輸入模型

```csharp
public class OwnerListGetInput : AlifeApi.BusinessRules.Infrastructure.PagedListInput
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; [PagedListInput](AlifeApi.BusinessRules.Infrastructure.PagedListInput.md 'AlifeApi.BusinessRules.Infrastructure.PagedListInput') &#129106; OwnerListGetInput
### Properties

<a name='AlifeApi.BusinessRules.OwnerModels.OwnerListGetInput.CompanyName'></a>

## OwnerListGetInput.CompanyName Property

公司名稱 (模糊查詢)

```csharp
public string? CompanyName { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.OwnerModels.OwnerListGetInput.IdentificationNumber'></a>

## OwnerListGetInput.IdentificationNumber Property

證號 (完全比對)

```csharp
public string? IdentificationNumber { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.OwnerModels.OwnerListGetInput.PersonType'></a>

## OwnerListGetInput.PersonType Property

人別 (完全比對)

```csharp
public string? PersonType { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.OwnerModels.OwnerListGetInput.ResponsiblePerson'></a>

## OwnerListGetInput.ResponsiblePerson Property

負責人姓名 (模糊查詢)

```csharp
public string? ResponsiblePerson { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')