#### [AlifeApi.BusinessRules](index.md 'index')
### [AlifeApi.BusinessRules.FloorModels](AlifeApi.BusinessRules.FloorModels.md 'AlifeApi.BusinessRules.FloorModels')

## FloorService Class

樓層服務

```csharp
public class FloorService : AlifeApi.BusinessRules.Infrastructure.ServiceBase<AlifeApi.DataAccess.ProjectContent.alifeContext>
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; [AlifeApi.Common.InfrastructureBase](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.Common.InfrastructureBase 'AlifeApi.Common.InfrastructureBase') &#129106; [AlifeApi.BusinessRules.Infrastructure.ServiceBase&lt;](AlifeApi.BusinessRules.Infrastructure.ServiceBase_TDbContext_.md 'AlifeApi.BusinessRules.Infrastructure.ServiceBase<TDbContext>')[AlifeApi.DataAccess.ProjectContent.alifeContext](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.DataAccess.ProjectContent.alifeContext 'AlifeApi.DataAccess.ProjectContent.alifeContext')[&gt;](AlifeApi.BusinessRules.Infrastructure.ServiceBase_TDbContext_.md 'AlifeApi.BusinessRules.Infrastructure.ServiceBase<TDbContext>') &#129106; FloorService
### Methods

<a name='AlifeApi.BusinessRules.FloorModels.FloorService.CreateFloorAsync(AlifeApi.BusinessRules.FloorModels.FloorCreateInput)'></a>

## FloorService.CreateFloorAsync(FloorCreateInput) Method

建立樓層

```csharp
public System.Threading.Tasks.Task<int> CreateFloorAsync(AlifeApi.BusinessRules.FloorModels.FloorCreateInput input);
```
#### Parameters

<a name='AlifeApi.BusinessRules.FloorModels.FloorService.CreateFloorAsync(AlifeApi.BusinessRules.FloorModels.FloorCreateInput).input'></a>

`input` [FloorCreateInput](AlifeApi.BusinessRules.FloorModels.FloorCreateInput.md 'AlifeApi.BusinessRules.FloorModels.FloorCreateInput')

樓層建立輸入資料

#### Returns
[System.Threading.Tasks.Task&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')[System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')  
新建樓層的ID

<a name='AlifeApi.BusinessRules.FloorModels.FloorService.DeleteFloorAsync(int)'></a>

## FloorService.DeleteFloorAsync(int) Method

刪除樓層

```csharp
public System.Threading.Tasks.Task DeleteFloorAsync(int floorId);
```
#### Parameters

<a name='AlifeApi.BusinessRules.FloorModels.FloorService.DeleteFloorAsync(int).floorId'></a>

`floorId` [System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')

樓層ID

#### Returns
[System.Threading.Tasks.Task](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task 'System.Threading.Tasks.Task')

<a name='AlifeApi.BusinessRules.FloorModels.FloorService.GetFloorByIdAsync(int)'></a>

## FloorService.GetFloorByIdAsync(int) Method

根據ID取得樓層詳細資料

```csharp
public System.Threading.Tasks.Task<AlifeApi.BusinessRules.FloorModels.FloorOutput?> GetFloorByIdAsync(int floorId);
```
#### Parameters

<a name='AlifeApi.BusinessRules.FloorModels.FloorService.GetFloorByIdAsync(int).floorId'></a>

`floorId` [System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')

樓層ID

#### Returns
[System.Threading.Tasks.Task&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')[FloorOutput](AlifeApi.BusinessRules.FloorModels.FloorOutput.md 'AlifeApi.BusinessRules.FloorModels.FloorOutput')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')  
樓層詳細資料

<a name='AlifeApi.BusinessRules.FloorModels.FloorService.GetFloorDropdownListAsync(AlifeApi.BusinessRules.FloorModels.FloorDropdownInput)'></a>

## FloorService.GetFloorDropdownListAsync(FloorDropdownInput) Method

根據建築物ID取得樓層下拉選單列表

```csharp
public System.Threading.Tasks.Task<System.Collections.Generic.List<AlifeApi.BusinessRules.FloorModels.FloorDropdownOutput>> GetFloorDropdownListAsync(AlifeApi.BusinessRules.FloorModels.FloorDropdownInput input);
```
#### Parameters

<a name='AlifeApi.BusinessRules.FloorModels.FloorService.GetFloorDropdownListAsync(AlifeApi.BusinessRules.FloorModels.FloorDropdownInput).input'></a>

`input` [FloorDropdownInput](AlifeApi.BusinessRules.FloorModels.FloorDropdownInput.md 'AlifeApi.BusinessRules.FloorModels.FloorDropdownInput')

樓層下拉選單輸入資料 (包含 BuildingId)

#### Returns
[System.Threading.Tasks.Task&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')[System.Collections.Generic.List&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Collections.Generic.List-1 'System.Collections.Generic.List`1')[FloorDropdownOutput](AlifeApi.BusinessRules.FloorModels.FloorDropdownOutput.md 'AlifeApi.BusinessRules.FloorModels.FloorDropdownOutput')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Collections.Generic.List-1 'System.Collections.Generic.List`1')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')  
樓層下拉選單列表

<a name='AlifeApi.BusinessRules.FloorModels.FloorService.GetFloorListAsync(AlifeApi.BusinessRules.FloorModels.FloorQueryInput)'></a>

## FloorService.GetFloorListAsync(FloorQueryInput) Method

取得指定建築物的樓層列表 (分頁)

```csharp
public System.Threading.Tasks.Task<AlifeApi.BusinessRules.Infrastructure.PagedListOutput<AlifeApi.BusinessRules.FloorModels.FloorListOutput>> GetFloorListAsync(AlifeApi.BusinessRules.FloorModels.FloorQueryInput input);
```
#### Parameters

<a name='AlifeApi.BusinessRules.FloorModels.FloorService.GetFloorListAsync(AlifeApi.BusinessRules.FloorModels.FloorQueryInput).input'></a>

`input` [FloorQueryInput](AlifeApi.BusinessRules.FloorModels.FloorQueryInput.md 'AlifeApi.BusinessRules.FloorModels.FloorQueryInput')

查詢條件 (必須包含 BuildingId)

#### Returns
[System.Threading.Tasks.Task&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')[AlifeApi.BusinessRules.Infrastructure.PagedListOutput&lt;](AlifeApi.BusinessRules.Infrastructure.PagedListOutput_TDetail_.md 'AlifeApi.BusinessRules.Infrastructure.PagedListOutput<TDetail>')[FloorListOutput](AlifeApi.BusinessRules.FloorModels.FloorListOutput.md 'AlifeApi.BusinessRules.FloorModels.FloorListOutput')[&gt;](AlifeApi.BusinessRules.Infrastructure.PagedListOutput_TDetail_.md 'AlifeApi.BusinessRules.Infrastructure.PagedListOutput<TDetail>')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')  
分頁的樓層列表

<a name='AlifeApi.BusinessRules.FloorModels.FloorService.UpdateFloorAsync(int,AlifeApi.BusinessRules.FloorModels.FloorUpdateInput)'></a>

## FloorService.UpdateFloorAsync(int, FloorUpdateInput) Method

更新樓層資訊

```csharp
public System.Threading.Tasks.Task UpdateFloorAsync(int floorId, AlifeApi.BusinessRules.FloorModels.FloorUpdateInput input);
```
#### Parameters

<a name='AlifeApi.BusinessRules.FloorModels.FloorService.UpdateFloorAsync(int,AlifeApi.BusinessRules.FloorModels.FloorUpdateInput).floorId'></a>

`floorId` [System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')

樓層ID

<a name='AlifeApi.BusinessRules.FloorModels.FloorService.UpdateFloorAsync(int,AlifeApi.BusinessRules.FloorModels.FloorUpdateInput).input'></a>

`input` [FloorUpdateInput](AlifeApi.BusinessRules.FloorModels.FloorUpdateInput.md 'AlifeApi.BusinessRules.FloorModels.FloorUpdateInput')

樓層更新輸入資料

#### Returns
[System.Threading.Tasks.Task](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task 'System.Threading.Tasks.Task')