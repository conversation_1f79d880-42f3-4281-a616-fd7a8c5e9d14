﻿using System;
using System.Collections.Generic;

namespace AlifeApi.DataAccess.ProjectContent
{
    /// <summary>
    /// 供應商檔案資料表，用於儲存與供應商相關的檔案資訊
    /// </summary>
    public partial class SupplierFile
    {
        /// <summary>
        /// 檔案唯一識別碼，自動遞增
        /// </summary>
        public int SupplierFileId { get; set; }
        /// <summary>
        /// 關聯的供應商識別碼，外鍵
        /// </summary>
        public int SupplierId { get; set; }
        /// <summary>
        /// 表單類別（例如：合約、證明文件）
        /// </summary>
        public string FormType { get; set; }
        /// <summary>
        /// 檔案名稱
        /// </summary>
        public string FileName { get; set; }
        /// <summary>
        /// 檔案儲存路徑
        /// </summary>
        public string FilePath { get; set; }
        /// <summary>
        /// 備註
        /// </summary>
        public string Remark { get; set; }
        /// <summary>
        /// 承辦人姓名
        /// </summary>
        public string Agent { get; set; }
        /// <summary>
        /// 資料創建人識別碼（參考 UserInfo 表的 UserInfoId）
        /// </summary>
        public string CreatedUserInfoId { get; set; }
        /// <summary>
        /// 資料更新人識別碼（參考 UserInfo 表的 UserInfoId）
        /// </summary>
        public string UpdatedUserInfoId { get; set; }
        /// <summary>
        /// 資料更新時間
        /// </summary>
        public DateTime UpdatedTime { get; set; }
        /// <summary>
        /// 資料創建時間
        /// </summary>
        public DateTime CreatedTime { get; set; }

        public virtual Supplier Supplier { get; set; }
    }
}
