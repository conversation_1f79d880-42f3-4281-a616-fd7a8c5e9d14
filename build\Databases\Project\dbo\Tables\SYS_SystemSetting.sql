﻿CREATE TABLE [dbo].[SYS_SystemSetting] (
    [Type]          VARCHAR (50)   NOT NULL,
    [Key]           VARCHAR (50)   NOT NULL,
    [Value]         NVARCHAR (100) NULL,
    [Name]          NVARCHAR (50)  NOT NULL,
    [IsEnabled]     BIT            CONSTRAINT [DF_SYS_SystemSetting_IsEnabled] DEFAULT ((1)) NOT NULL,
    [CreatedUserId] VARCHAR (15)   NOT NULL,
    [CreatedTime]   DATETIME       NOT NULL,
    [UpdatedUserId] VARCHAR (15)   NOT NULL,
    [UpdatedTime]   DATETIME       NOT NULL,
    CONSTRAINT [PK_SYS_SystemSetting_1] PRIMARY KEY CLUSTERED ([Type] ASC, [Key] ASC)
);




GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'系統設定資料', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'SYS_SystemSetting';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'鍵', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'SYS_SystemSetting', @level2type = N'COLUMN', @level2name = N'Key';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'值', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'SYS_SystemSetting', @level2type = N'COLUMN', @level2name = N'Value';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'詳細名稱', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'SYS_SystemSetting', @level2type = N'COLUMN', @level2name = N'Name';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'建立者', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'SYS_SystemSetting', @level2type = N'COLUMN', @level2name = N'CreatedUserId';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'建立時間', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'SYS_SystemSetting', @level2type = N'COLUMN', @level2name = N'CreatedTime';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'更新者', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'SYS_SystemSetting', @level2type = N'COLUMN', @level2name = N'UpdatedUserId';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'更新時間', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'SYS_SystemSetting', @level2type = N'COLUMN', @level2name = N'UpdatedTime';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'類別', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'SYS_SystemSetting', @level2type = N'COLUMN', @level2name = N'Type';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'是否啟用', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'SYS_SystemSetting', @level2type = N'COLUMN', @level2name = N'IsEnabled';

