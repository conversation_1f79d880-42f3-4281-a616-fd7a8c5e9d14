﻿using AlifeApi.BusinessRules.Infrastructure;
using AlifeApi.BusinessRules.ProblemReportModels;
using Microsoft.AspNetCore.Mvc;

namespace AlifeApi.WebApi.Controllers
{
    /// <summary>
    /// 問題回報管理(ProblemReport)
    /// </summary>
    public class ProblemReportController : AuthenticatedController
    {
        ///// <summary>
        ///// 問題回報服務
        ///// </summary>
        //private readonly ProblemReportService _problemReportService;

        ///// <summary>
        ///// Initializes a new instance of the <see cref="ProblemReportController"/> class.
        ///// </summary>
        ///// <param name="problemReportService">The problem report service.</param>
        ///// <exception cref="ArgumentNullException">problemReportService</exception>
        //public ProblemReportController(ProblemReportService problemReportService)
        //{
        //    _problemReportService = problemReportService ?? throw new ArgumentNullException(nameof(problemReportService));
        //}

        ///// <summary>
        ///// 新增問題回報
        ///// </summary>
        ///// <param name="input">The input.</param>
        ///// <remarks>
        ///// ProblemType : 1 問題 2 需求 <br></br>
        ///// </remarks>
        //[HttpPost]
        //public async Task CreateUserReportAsync(UserReportCreateInput input)
        //    => await _problemReportService.CreateUserReportAsync(input);

        ///// <summary>
        ///// 取得問題回報管理清單
        ///// </summary>
        ///// <param name="input">The input.</param>
        ///// <returns></returns>
        ///// <remarks>
        ///// ProblemType : 1 問題 2 需求 <br></br>
        ///// ProcessStatus : 1 未處理 2 受理中 3 已處理 4 不處理 <br></br>
        ///// </remarks>
        //[HttpPost]
        //public async Task<PagedListOutput<ProblemReportListItemGetOutput>> GetProblemReportListAsync(ProblemReportListGetInput input)
        //    => await _problemReportService.GetProblemReportListAsync(input);

        ///// <summary>
        ///// 回覆問題回報
        ///// </summary>
        ///// <param name="input">The input.</param>
        ///// <remarks>
        ///// ProblemType : 1 問題 2 需求 <br></br>
        ///// ProcessStatus : 1 未處理 2 受理中 3 已處理 4 不處理 <br></br>
        ///// </remarks>
        //[HttpPost]
        //public async Task ReplyReportAsync(ReportReplyInput input)
        //    => await _problemReportService.ReplyReportAsync(input);
    }
}
