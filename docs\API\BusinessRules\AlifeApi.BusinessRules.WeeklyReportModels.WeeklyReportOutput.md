#### [AlifeApi.BusinessRules](index.md 'index')
### [AlifeApi.BusinessRules.WeeklyReportModels](AlifeApi.BusinessRules.WeeklyReportModels.md 'AlifeApi.BusinessRules.WeeklyReportModels')

## WeeklyReportOutput Class

週報統計結果輸出DTO

```csharp
public class WeeklyReportOutput
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; WeeklyReportOutput
### Properties

<a name='AlifeApi.BusinessRules.WeeklyReportModels.WeeklyReportOutput.BusinessResults'></a>

## WeeklyReportOutput.BusinessResults Property

本週經營成果統計

```csharp
public AlifeApi.BusinessRules.WeeklyReportModels.WeeklyBusinessResultStatistics BusinessResults { get; set; }
```

#### Property Value
[WeeklyBusinessResultStatistics](AlifeApi.BusinessRules.WeeklyReportModels.WeeklyBusinessResultStatistics.md 'AlifeApi.BusinessRules.WeeklyReportModels.WeeklyBusinessResultStatistics')

<a name='AlifeApi.BusinessRules.WeeklyReportModels.WeeklyReportOutput.CustomerAnalysis'></a>

## WeeklyReportOutput.CustomerAnalysis Property

客戶背景分析統計

```csharp
public AlifeApi.BusinessRules.WeeklyReportModels.WeeklyCustomerAnalysisStatistics CustomerAnalysis { get; set; }
```

#### Property Value
[WeeklyCustomerAnalysisStatistics](AlifeApi.BusinessRules.WeeklyReportModels.WeeklyCustomerAnalysisStatistics.md 'AlifeApi.BusinessRules.WeeklyReportModels.WeeklyCustomerAnalysisStatistics')

<a name='AlifeApi.BusinessRules.WeeklyReportModels.WeeklyReportOutput.EndDate'></a>

## WeeklyReportOutput.EndDate Property

週報結束日期 (週日)

```csharp
public System.DateTime EndDate { get; set; }
```

#### Property Value
[System.DateTime](https://docs.microsoft.com/en-us/dotnet/api/System.DateTime 'System.DateTime')

<a name='AlifeApi.BusinessRules.WeeklyReportModels.WeeklyReportOutput.SalesStatistics'></a>

## WeeklyReportOutput.SalesStatistics Property

銷售統計

```csharp
public AlifeApi.BusinessRules.WeeklyReportModels.WeeklySalesStatistics SalesStatistics { get; set; }
```

#### Property Value
[WeeklySalesStatistics](AlifeApi.BusinessRules.WeeklyReportModels.WeeklySalesStatistics.md 'AlifeApi.BusinessRules.WeeklyReportModels.WeeklySalesStatistics')

<a name='AlifeApi.BusinessRules.WeeklyReportModels.WeeklyReportOutput.SiteCode'></a>

## WeeklyReportOutput.SiteCode Property

案場編號

```csharp
public string SiteCode { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.WeeklyReportModels.WeeklyReportOutput.SiteName'></a>

## WeeklyReportOutput.SiteName Property

案場名稱

```csharp
public string? SiteName { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.WeeklyReportModels.WeeklyReportOutput.StartDate'></a>

## WeeklyReportOutput.StartDate Property

週報開始日期 (週一)

```csharp
public System.DateTime StartDate { get; set; }
```

#### Property Value
[System.DateTime](https://docs.microsoft.com/en-us/dotnet/api/System.DateTime 'System.DateTime')

<a name='AlifeApi.BusinessRules.WeeklyReportModels.WeeklyReportOutput.Title'></a>

## WeeklyReportOutput.Title Property

週報標題

```csharp
public string Title { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.WeeklyReportModels.WeeklyReportOutput.WeekNumber'></a>

## WeeklyReportOutput.WeekNumber Property

週次

```csharp
public int WeekNumber { get; set; }
```

#### Property Value
[System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')

<a name='AlifeApi.BusinessRules.WeeklyReportModels.WeeklyReportOutput.Year'></a>

## WeeklyReportOutput.Year Property

年份

```csharp
public int Year { get; set; }
```

#### Property Value
[System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')