﻿using System;
using System.Collections.Generic;

namespace AlifeApi.DataAccess.ProjectContent
{
    /// <summary>
    /// 角色群組使用者關聯表，用於儲存角色群組與使用者的關聯關係。
    /// </summary>
    public partial class SysRoleGroupUser
    {
        /// <summary>
        /// 系統名稱，主鍵的一部分，用於區分不同系統。
        /// </summary>
        public string System { get; set; }
        /// <summary>
        /// 角色群組識別編號，主鍵的一部分，對應 SYS_RoleGroup 表的 RoleGroupId。
        /// </summary>
        public string RoleGroupId { get; set; }
        /// <summary>
        /// 使用者編號，主鍵的一部分，對應 UserInfo 表的 UserInfoId。
        /// </summary>
        public string UserInfoId { get; set; }

        public virtual UserInfo UserInfo { get; set; }
    }
}
