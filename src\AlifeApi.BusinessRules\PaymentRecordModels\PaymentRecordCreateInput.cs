using System;
using System.ComponentModel.DataAnnotations;

namespace AlifeApi.BusinessRules.PaymentRecordModels
{
    /// <summary>
    /// 收款記錄建立輸入模型
    /// </summary>
    public class PaymentRecordCreateInput
    {
        [Required(ErrorMessage = "缺少訂單ID")]
        public int OrderId { get; set; }

        [Required(ErrorMessage = "缺少收款日期")]
        public DateOnly PaymentDate { get; set; }

        [Required(ErrorMessage = "缺少收款金額")]
        [Range(0.01, double.MaxValue, ErrorMessage = "收款金額必須大於 0")]
        public decimal Amount { get; set; }

        [Required(ErrorMessage = "缺少款別")]
        public string PaymentType { get; set; } = null!;

        [Required(ErrorMessage = "缺少收款方式")]
        public string PaymentMethod { get; set; } = null!;

        public string? AttachmentPath { get; set; }
        public string? Remarks { get; set; }
        public decimal? HandlingFee { get; set; }
        public bool? TransferredToDeveloper { get; set; }
    }
} 
