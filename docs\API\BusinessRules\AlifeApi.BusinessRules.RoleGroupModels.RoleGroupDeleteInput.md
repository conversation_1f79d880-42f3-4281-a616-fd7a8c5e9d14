#### [AlifeApi.BusinessRules](index.md 'index')
### [AlifeApi.BusinessRules.RoleGroupModels](AlifeApi.BusinessRules.RoleGroupModels.md 'AlifeApi.BusinessRules.RoleGroupModels')

## RoleGroupDeleteInput Class

刪除角色權限

```csharp
public class RoleGroupDeleteInput
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; RoleGroupDeleteInput
### Properties

<a name='AlifeApi.BusinessRules.RoleGroupModels.RoleGroupDeleteInput.Id'></a>

## RoleGroupDeleteInput.Id Property

ID

```csharp
public string Id { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')