﻿using System.Text.Json.Serialization;
using AlifeApi.BusinessRules.Infrastructure;
using AlifeApi.Common.Util;
using AlifeApi.DataAccess.LogContext;
using AlifeApi.DataAccess.ProjectContext;
using AlifeApi.WebApi.DependencyInjection;
using AlifeApi.WebApi.Filters;
using AlifeApi.WebApi.Options;
using AlifeApi.WebApi.Schedule;
using AlifeApi.WebApi.SignalR;
using AlifeApi.WebApi.Util;
using Microsoft.AspNetCore.Http.Features;
using Microsoft.AspNetCore.HttpOverrides;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.ModelBinding;
using Microsoft.EntityFrameworkCore;
using Quartz;
using Quartz.Impl;
using Quartz.Spi;
using LogcontextPg;
using AlifeApi.DataAccess.ProjectContent;

var builder = WebApplication.CreateBuilder(args);

// Add services to the container.

builder.Services.AddControllers()
    .AddMvcOptions(options =>
    {
        // 順序很重要，後進先出
        options.Filters.Add<ExceptionFilter>();
        options.Filters.Add<ApiResultFilter>();
        options.Filters.Add<LogFilter>();
    })
    .AddJsonOptions(options =>
    {
        // 充許屬性數量不足傳入
        options.JsonSerializerOptions.AllowTrailingCommas = true;
        // 因為 .NET 屬性規範是大駝峰，但是其他語言大部分是小駝峰
        // 所以 .NET Core 預設會轉回小駝峰，讓使用端可以符合他們語言的使用習慣
        // 但目前前端大部分的模板是大駝峰，所以關掉小駝峰轉換
        options.JsonSerializerOptions.PropertyNamingPolicy = null;
        options.JsonSerializerOptions.DefaultIgnoreCondition = JsonIgnoreCondition.WhenWritingNull;
        // 添加 DateOnly 轉換器
        options.JsonSerializerOptions.Converters.Add(new DateOnlyJsonConverter());
        options.JsonSerializerOptions.Converters.Add(new NullableDateOnlyJsonConverter());
    });
// Learn more about configuring Swagger/OpenAPI at https://aka.ms/aspnetcore/swashbuckle
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen(options =>
{
    options.IncludeXmlCommentsFromPattern("AlifeApi.*.xml");
    options.AddJwtSecurity();
    // 將 Swagger 的 Object Schema Id 改為包含 Namespace，避免 Class 名稱相同
    options.CustomSchemaIds(x => x.FullName);
});

// 增加接收表單最大長度
builder.Services.Configure<FormOptions>(options =>
{
    options.KeyLengthLimit = int.MaxValue;
    options.ValueCountLimit = int.MaxValue;
    options.ValueLengthLimit = int.MaxValue;
    options.MultipartHeadersLengthLimit = int.MaxValue;
});

builder.Services.AddCors(options =>
{
    options.AddDefaultPolicy(builder =>
    {
        builder
            .WithOrigins(
            "http://localhost:3000",
            "https://localhost:3000")     // 您的前端應用程式網址
            .AllowCredentials()                       // 允許憑證
            .AllowAnyMethod()
            .AllowAnyHeader();
    });
});

builder.Services.Configure<ApiBehaviorOptions>(options =>
{
    options.InvalidModelStateResponseFactory = actionContext =>
    {
        IEnumerable<ModelError> errors = actionContext.ModelState.SelectMany(x => x.Value.Errors)
                    .Where(x => !string.IsNullOrEmpty(x.ErrorMessage + " " + x.Exception));

        return new JsonResult(new ApiResult
        {
            Reponse = Message.InputDataFormatError,
            Exception = string.Join(" ", errors.Select(x => x.ErrorMessage))
        });
    };
});
builder.Services.AddHttpContextAccessor();
builder.Services.AddHttpClient("", httpClient =>
{
    httpClient.DefaultRequestHeaders.ConnectionClose = true;
});

builder.Services.Configure<ForwardedHeadersOptions>(options =>
{
    options.ForwardedHeaders = ForwardedHeaders.XForwardedFor | ForwardedHeaders.XForwardedProto;
});

builder.Services.AddJwtBearer(builder.Configuration.GetSection(JwtOptions.OptionsName));

builder.Services.AddOptions<SystemOptions>()
    .Bind(builder.Configuration.GetSection(SystemOptions.OptionsName))
    .ValidateDataAnnotations()
    .ValidateOnStart();

//builder.Services.AddDbContext<ProjectContext>(options =>
//{
//    options.UseSqlServer(builder.Configuration.GetConnectionString("DefaultConnection"));
//});
builder.Services.AddDbContext<alifeContext>(options =>
{
    options.UseNpgsql(builder.Configuration.GetConnectionString("PostgresqlConnection"));
});
//builder.Services.AddDbContext<LogContext>(options =>
//{
//    options.UseSqlServer(builder.Configuration.GetConnectionString("LogConnection"));
//});
builder.Services.AddDbContext<LogContextPg>(options =>
{
    options.UseNpgsql(builder.Configuration.GetConnectionString("PostgresqlLogConnection"));
});
builder.Services.AddDependencies("AlifeApi*.dll");

// 有需要額外定義，則在 appsettings 定義 Encryption 的設定
builder.Services.AddScoped<IEncryptionHelper, AesEncryptionHelper>();
builder.Services.AddOptions<EncryptionOptions>()
    .Bind(builder.Configuration.GetSection(EncryptionOptions.OptionsName))
    .ValidateDataAnnotations()
    .ValidateOnStart();

builder.Services.AddDistributedMemoryCache();
builder.Services.AddSession(options =>
{
    options.IdleTimeout = TimeSpan.FromMinutes(25);
    options.Cookie.HttpOnly = true;
    options.Cookie.SameSite = SameSiteMode.None;  // 允許跨站請求
    options.Cookie.SecurePolicy = CookieSecurePolicy.Always;  // 只在 HTTPS 傳送
});

builder.Services.AddScoped<ICurrentUser, CurrentUser>();
builder.Services.AddScoped<IImageStorageService, ImageStorageService>();


builder.Services.AddSignalR();


//builder.Services.AddTransient<AutoLogOutSchedule>();
//builder.Services.AddTransient<IdleAccountSchedule>();
//builder.Services.AddSingleton<IJobFactory, JobFactory>();
//builder.Services.AddSingleton<ISchedulerFactory, StdSchedulerFactory>();
//builder.Services.AddSingleton<QuartzJobRunner>();

var app = builder.Build();

// Configure the HTTP request pipeline.
//if (app.Environment.IsDevelopment() || app.Environment.IsStaging())
//{
app.UseSwagger();
app.UseSwaggerUI();
//}

// 新增設定：應用 HTTP 8080，不啟用 HTTPS
//builder.WebHost.UseUrls("http://*:8080");

app.UseCors();
app.UseAuthentication();
app.UseAuthorization();
app.UseSession();
app.UseStaticFiles();
// 获取 QuartzJobRunner 实例并启动 Quartz 调度器
//var jobRunner = app.Services.GetRequiredService<QuartzJobRunner>();
//jobRunner.Start().Wait(); // 这里使用了阻塞方式，实际应用中应该根据需要选择适当的方式

app.MapControllers();
//app.MapHub<ConnectHub>("/onlinecount");

app.Run();
