﻿using AlifeApi.BusinessRules.BulletinsModels;
using AlifeApi.BusinessRules.Infrastructure;
using AlifeApi.Common.Util;
using Microsoft.AspNetCore.Mvc;

namespace AlifeA<PERSON>.WebApi.Controllers
{
    /// <summary>
    /// 公告作業
    /// </summary>
    public class BulletinsController : AuthenticatedController
    {
        //private readonly BulletinsService _bulletinsService;


        ///// <summary>
        ///// Initializes a new instance of the <see cref="BulletinsController"/> class.
        ///// </summary>
        ///// <param name="bulletinsService">The role group service.</param>
        ///// <exception cref="ArgumentNullException">roleGroupService</exception>
        //public BulletinsController(BulletinsService bulletinsService)
        //{
        //    _bulletinsService = bulletinsService ?? throw new ArgumentNullException(nameof(bulletinsService));
        //}

        ///// <summary>
        ///// 新增系統公告
        ///// </summary>
        ///// <param name="input">The input.</param>
        ///// <returns>公告作業</returns>
        //[HttpPost]
        //public async Task<List<CreateBulletinsDataResult>> CreateBulletinsAsync(List<CreateBulletinsDataCondition> inputs)
        //    => await _bulletinsService.CreateBulletins(inputs);


        ///// <summary>
        ///// 取得所有公告
        ///// </summary>
        ///// <param name="input">The input.</param>
        ///// <returns>公告作業</returns>
        //[HttpPost]
        //public async Task<PagedListOutput<GetBulletinsListResult>> GetBulletinsListAsync(GetBulletinsListCondition input)
        //  => await _bulletinsService.GetBulletinsList(input);

        ///// <summary>
        ///// 取得公告明細
        ///// </summary>
        ///// <param name="input">The input.</param>
        ///// <returns>公告作業</returns>
        //[HttpPost]
        //public async Task<GetBulletinsResult> GetBulletinsAsync(BulletinsCondition input)
        //  => await _bulletinsService.GetBulletins(input);

        ///// <summary>
        ///// 修改公告
        ///// </summary>
        ///// <param name="input">The input.</param>
        ///// <returns>公告作業</returns>
        //[HttpPost]
        //public async Task UpdateBulletinsAsync(UpdateBulletinsCondition input)
        //    => await _bulletinsService.UpdateBulletins(input);

        ///// <summary>
        ///// 刪除公告
        ///// </summary>
        ///// <param name="input">The input.</param>
        ///// <returns>公告作業</returns>
        //[HttpDelete]
        //public async Task DeleteBulletinsAsync(BulletinsCondition input)
        //    => await _bulletinsService.DeleteBulletins(input);

        ///// <summary>
        ///// 下載系統公告所有附件
        ///// </summary>
        ///// <param name="input">The input.</param>
        ///// <returns>公告作業</returns>
        //[HttpPost]
        //public async Task<IActionResult> DownloadBulletinAllAttachAsync(BulletinsCondition input)
        //{
        //    var filePathList = await _bulletinsService.GetBulletinAttachmentAllFile(input.BL_Id);

        //    if (filePathList != null && filePathList.Any())
        //    {

        //        await _bulletinsService.RecordDownloadMultipleBulletinAttachment(filePathList.Select(x => x.BLA_Id).ToList());
        //        //回傳zip
        //        var zipBytes = ZipUtils.CreateZipByBase64(filePathList.Select(x => new ZipUtils.FileList() { FileName = x.FileName, File = x.File }).ToList());

        //        return new FileContentResult(zipBytes, System.Net.Mime.MediaTypeNames.Application.Zip)
        //        {
        //            FileDownloadName = $"公告附件-{DateTime.Now.ToString("yyyyMMddhhmm")}.zip"
        //        };
        //    }
        //    return StatusCode(204);
        //}
    }
}
