#### [AlifeApi.BusinessRules](index.md 'index')
### [AlifeApi.BusinessRules.CrmOptionModels](AlifeApi.BusinessRules.CrmOptionModels.md 'AlifeApi.BusinessRules.CrmOptionModels')

## CrmOptionDropdownOutput Class

CRM選項下拉選單輸出模型

```csharp
public class CrmOptionDropdownOutput
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; CrmOptionDropdownOutput
### Properties

<a name='AlifeApi.BusinessRules.CrmOptionModels.CrmOptionDropdownOutput.Name'></a>

## CrmOptionDropdownOutput.Name Property

選項值 (顯示文字)

```csharp
public string Name { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.CrmOptionModels.CrmOptionDropdownOutput.SortOrder'></a>

## CrmOptionDropdownOutput.SortOrder Property

排序順序

```csharp
public int SortOrder { get; set; }
```

#### Property Value
[System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')

<a name='AlifeApi.BusinessRules.CrmOptionModels.CrmOptionDropdownOutput.Value'></a>

## CrmOptionDropdownOutput.Value Property

選項ID (選項值)

```csharp
public long Value { get; set; }
```

#### Property Value
[System.Int64](https://docs.microsoft.com/en-us/dotnet/api/System.Int64 'System.Int64')