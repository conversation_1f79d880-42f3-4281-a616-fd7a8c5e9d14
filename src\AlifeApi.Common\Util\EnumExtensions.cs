﻿using System.ComponentModel;
using System.Reflection;

namespace System
{
    /// <summary>
    /// Enum 擴充方法
    /// </summary>
    public static class EnumExtensions
    {
        /// <summary>
        /// 取得 Enum DescriptionAttribute 的 Description
        /// </summary>
        /// <param name="value"></param>
        /// <returns></returns>
        public static string GetDescription(this Enum value)
        {
            FieldInfo fieldInfo = value.GetType().GetField(value.ToString());

            if (fieldInfo is not null)
            {
                DescriptionAttribute attr =
                    (DescriptionAttribute)fieldInfo.GetCustomAttributes(typeof(DescriptionAttribute), false).FirstOrDefault();

                if (attr is not null)
                {
                    return attr.Description;
                }
            }

            return value.ToString();
        }
    }
}
