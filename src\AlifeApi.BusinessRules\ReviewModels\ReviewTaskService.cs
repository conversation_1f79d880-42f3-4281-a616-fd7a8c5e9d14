﻿using AlifeApi.BusinessRules.Infrastructure;
using AlifeApi.DataAccess.ProjectContent;
using Microsoft.EntityFrameworkCore;

namespace AlifeApi.BusinessRules.ReviewModels
{
    /// <summary>
    /// 審核流程服務
    /// </summary>
    public class ReviewTaskService : ServiceBase<alifeContext>
    {
        public ReviewTaskService(IServiceProvider serviceProvider, alifeContext dbContext) 
            : base(serviceProvider, dbContext)
        {
        }

        /// <summary>
        /// 建立審核流程
        /// </summary>
        /// <param name="input">審核流程輸入資料</param>
        /// <returns>審核流程ID</returns>
        public async Task<int> CreateReviewTaskAsync(ReviewTaskInput input)
        {
            // 建立審核任務
            var task = new ReviewTask
            {
                Title = input.Name.Length > 255 ? input.Name.Substring(0, 255) : input.Name,
                Description = input.Description,
                CreatedTime = DateTime.Now,
                CreatedUserInfoId = CurrentUser.UserId,
                Status = "enable"
            };

            Db.ReviewTasks.Add(task);
            await Db.SaveChangesAsync();

            // 建立審核步驟
            for (int i = 0; i < input.Steps.Count; i++)
            {
                var stepInput = input.Steps[i];
                var step = new ReviewStep
                {
                    TaskId = task.TaskId,
                    Name = stepInput.Name,
                    Sequence = i + 1,
                    TimeLimit = stepInput.TimeLimit,
                    Status = "waiting"
                };

                Db.ReviewSteps.Add(step);
                await Db.SaveChangesAsync();

                // 建立審核人員
                foreach (var approverId in stepInput.ApproverIds)
                {
                    var approver = new ReviewApprover
                    {
                        StepId = step.StepId,
                        UserInfoId = approverId,
                        IsRequired = true
                    };

                    Db.ReviewApprovers.Add(approver);
                }

                // 建立通知設定
                if (stepInput.SystemNotification)
                {
                    var systemNotification = new NotificationSetting
                    {
                        StepId = step.StepId,
                        NotifyType = "system",
                        Enabled = true
                    };
                    Db.NotificationSettings.Add(systemNotification);
                }

                if (stepInput.EmailNotification)
                {
                    var emailNotification = new NotificationSetting
                    {
                        StepId = step.StepId,
                        NotifyType = "email",
                        Enabled = true
                    };
                    Db.NotificationSettings.Add(emailNotification);
                }

                if (stepInput.SmsNotification)
                {
                    var smsNotification = new NotificationSetting
                    {
                        StepId = step.StepId,
                        NotifyType = "sms",
                        Enabled = true
                    };
                    Db.NotificationSettings.Add(smsNotification);
                }
            }

            await Db.SaveChangesAsync();
            return task.TaskId;
        }

        /// <summary>
        /// 取得審核流程清單
        /// </summary>
        /// <param name="input">查詢條件</param>
        /// <returns>審核流程清單</returns>
        public async Task<PagedListOutput<ReviewTaskOutput>> GetReviewTasksAsync(ReviewTaskListInput input)
        {
            var query = from t in Db.ReviewTasks
                       where t.Status != "deleted"  // 只查詢非刪除狀態的任務
                       select new
                       {
                           Task = t,
                           StepCount = t.ReviewSteps.Count(s => s.Status != "deleted")
                       };

            // 套用篩選條件
            if (!string.IsNullOrEmpty(input.Name))
            {
                query = query.Where(x => x.Task.Title.Contains(input.Name));
            }

            if (!string.IsNullOrEmpty(input.CreatedUserInfoId))
            {
                query = query.Where(x => x.Task.CreatedUserInfoId == input.CreatedUserInfoId);
            }

            if (!string.IsNullOrEmpty(input.Status))
            {
                query = query.Where(x => x.Task.Status == input.Status);
            }

            if (input.StartDate.HasValue)
                query = query.Where(x => x.Task.CreatedTime >= input.StartDate);
            if (input.EndDate.HasValue)
                query = query.Where(x => x.Task.CreatedTime <= input.EndDate);

            int recordCount = query.Count();

            PagedListOutput<ReviewTaskOutput> list = await query
                .OrderByDescending(x => x.Task.CreatedTime)
                .Select(x => new ReviewTaskOutput
                {
                    TaskId = x.Task.TaskId,
                    Name = x.Task.Title,
                    Description = x.Task.Description,
                    TotalStep = x.StepCount,
                    CreatedTime = x.Task.CreatedTime ?? DateTime.MinValue,
                    CreatedUserInfoId = x.Task.CreatedUserInfoId,
                    Status = x.Task.Status
                }).ToPagedListOutputAsync(input);

            list.RecordCount = recordCount;

            return list;
        }

        /// <summary>
        /// 取得審核流程詳細資料
        /// </summary>
        /// <param name="taskId">審核流程ID</param>
        /// <returns>審核流程詳細資料</returns>
        public async Task<ReviewTaskDetailOutput> GetReviewTaskDetailAsync(int taskId)
        {
            var task = await Db.ReviewTasks
                .Where(x => x.TaskId == taskId && x.Status != "deleted")
                .Select(x => new ReviewTaskDetailOutput
                {
                    TaskId = x.TaskId,
                    Name = x.Title,
                    Description = x.Description,
                    CreatedTime = x.CreatedTime ?? DateTime.MinValue,
                    CreatedUserInfoId = x.CreatedUserInfoId,
                    Status = x.Status,
                    Deadline = x.Deadline,
                    Steps = x.ReviewSteps
                        .Where(s => s.Status != "deleted")
                        .OrderBy(s => s.Sequence)
                        .Select(s => new ReviewStepInput
                        {
                            Name = s.Name,
                            TimeLimit = s.TimeLimit ?? 0,
                            ApproverIds = s.ReviewApprovers
                                .Where(a => a.IsRequired == true)
                                .Select(a => a.UserInfoId)
                                .ToList(),
                            SystemNotification = s.NotificationSettings
                                .Where(n => n.NotifyType == "system")
                                .Select(n => n.Enabled)
                                .FirstOrDefault() ?? false,
                            EmailNotification = s.NotificationSettings
                                .Where(n => n.NotifyType == "email")
                                .Select(n => n.Enabled)
                                .FirstOrDefault() ?? false,
                            SmsNotification = s.NotificationSettings
                                .Where(n => n.NotifyType == "sms")
                                .Select(n => n.Enabled)
                                .FirstOrDefault() ?? false
                        })
                        .ToList()
                })
                .FirstOrDefaultAsync();

            return task;
        }

        /// <summary>
        /// 更新審核流程
        /// </summary>
        /// <param name="taskId">審核流程ID</param>
        /// <param name="input">審核流程更新輸入資料</param>
        /// <returns>審核流程ID</returns>
        public async Task<int> UpdateReviewTaskAsync(int taskId, ReviewTaskUpdateInput input)
        {
            var task = await Db.ReviewTasks
                .Include(x => x.ReviewSteps)
                    .ThenInclude(s => s.ReviewApprovers)
                .Include(x => x.ReviewSteps)
                    .ThenInclude(s => s.NotificationSettings)
                .FirstOrDefaultAsync(x => x.TaskId == taskId && x.Status != "deleted");

            if (task == null)
            {
                throw new Exception("找不到指定的審核流程");
            }

            // 更新任務基本資訊
            task.Title = input.Name.Length > 255 ? input.Name.Substring(0, 255) : input.Name;
            task.Description = input.Description;
            if (input.Status != null)
            {
                task.Status = input.Status;
            }
            task.Deadline = input.Deadline;

            // 刪除所有相關的步驟、審核人員和通知設定
            foreach (var step in task.ReviewSteps.ToList())
            {
                // 刪除審核人員
                Db.ReviewApprovers.RemoveRange(step.ReviewApprovers);
                
                // 刪除通知設定
                Db.NotificationSettings.RemoveRange(step.NotificationSettings);
                
                // 刪除步驟
                Db.ReviewSteps.Remove(step);
            }

            await Db.SaveChangesAsync();

            // 建立新的審核步驟
            for (int i = 0; i < input.Steps.Count; i++)
            {
                var stepInput = input.Steps[i];
                var step = new ReviewStep
                {
                    TaskId = task.TaskId,
                    Name = stepInput.Name,
                    Sequence = i + 1,
                    TimeLimit = stepInput.TimeLimit,
                    Status = "waiting"
                };

                Db.ReviewSteps.Add(step);
                await Db.SaveChangesAsync();

                // 建立審核人員
                foreach (var approverId in stepInput.ApproverIds)
                {
                    var approver = new ReviewApprover
                    {
                        StepId = step.StepId,
                        UserInfoId = approverId,
                        IsRequired = true
                    };

                    Db.ReviewApprovers.Add(approver);
                }

                // 建立通知設定
                if (stepInput.SystemNotification)
                {
                    var systemNotification = new NotificationSetting
                    {
                        StepId = step.StepId,
                        NotifyType = "system",
                        Enabled = true
                    };
                    Db.NotificationSettings.Add(systemNotification);
                }

                if (stepInput.EmailNotification)
                {
                    var emailNotification = new NotificationSetting
                    {
                        StepId = step.StepId,
                        NotifyType = "email",
                        Enabled = true
                    };
                    Db.NotificationSettings.Add(emailNotification);
                }

                if (stepInput.SmsNotification)
                {
                    var smsNotification = new NotificationSetting
                    {
                        StepId = step.StepId,
                        NotifyType = "sms",
                        Enabled = true
                    };
                    Db.NotificationSettings.Add(smsNotification);
                }
            }

            await Db.SaveChangesAsync();
            return task.TaskId;
        }

        /// <summary>
        /// 刪除審核流程
        /// </summary>
        /// <param name="taskId">審核流程ID</param>
        public async Task DeleteReviewTaskAsync(int taskId)
        {
            using var transaction = await Db.Database.BeginTransactionAsync();
            try
            {
                var task = await Db.ReviewTasks
                    .Include(x => x.ReviewSteps)
                        .ThenInclude(s => s.ReviewApprovers)
                    .Include(x => x.ReviewSteps)
                        .ThenInclude(s => s.NotificationSettings)
                    .FirstOrDefaultAsync(x => x.TaskId == taskId && x.Status != "deleted");

                if (task == null)
                {
                    throw new Exception("找不到指定的審核流程");
                }

                // 標記所有相關記錄為已刪除
                foreach (var step in task.ReviewSteps)
                {
                    // 標記審核人員為已刪除
                    foreach (var approver in step.ReviewApprovers)
                    {
                        approver.IsRequired = false;
                        Db.ReviewApprovers.Update(approver);
                    }

                    // 標記通知設定為已停用
                    foreach (var notification in step.NotificationSettings)
                    {
                        notification.Enabled = false;
                        Db.NotificationSettings.Update(notification);
                    }

                    // 標記步驟為已刪除
                    step.Status = "deleted";
                    Db.ReviewSteps.Update(step);
                }

                // 標記任務為已刪除
                task.Status = "deleted";
                Db.ReviewTasks.Update(task);

                await Db.SaveChangesAsync();
                await transaction.CommitAsync();
            }
            catch (Exception)
            {
                await transaction.RollbackAsync();
                throw;
            }
        }

        /// <summary>
        /// 取得審核流程歷史記錄
        /// </summary>
        /// <param name="taskId">審核流程ID</param>
        /// <returns>歷史記錄清單</returns>
        public async Task<List<ReviewHistoryOutput>> GetReviewHistoryAsync(int taskId)
        {
            var history = await Db.ReviewHistories
                .Where(h => h.TaskId == taskId)
                .OrderByDescending(h => h.Timestamp)
                .Select(h => new ReviewHistoryOutput
                {
                    HistoryId = h.HistoryId,
                    TaskId = h.TaskId ?? 0,
                    StepId = h.StepId,
                    StepName = h.StepId != null 
                        ? Db.ReviewSteps.FirstOrDefault(s => s.StepId == h.StepId).Name 
                        : null,
                    UserInfoId = h.UserInfoId,
                    UserName = Db.UserInfos.FirstOrDefault(u => u.UserInfoId == h.UserInfoId).Name,
                    Action = h.Action,
                    Comment = h.Comment,
                    Timestamp = h.Timestamp
                })
                .ToListAsync();

            return history;
        }

        /// <summary>
        /// 添加審核歷史記錄
        /// </summary>
        /// <param name="input">審核歷史記錄輸入資料</param>
        public async Task AddReviewHistoryAsync(ReviewHistoryInput input)
        {
            var history = new ReviewHistory
            {
                TaskId = input.TaskId,
                StepId = input.StepId,
                UserInfoId = CurrentUser.UserId,
                Action = input.Action,
                Comment = input.Comment,
                Timestamp = DateTime.Now
            };

            Db.ReviewHistories.Add(history);
            await Db.SaveChangesAsync();
        }

        /// <summary>
        /// 獲取下一步驟資訊
        /// </summary>
        /// <param name="taskId">審核流程ID</param>
        /// <returns>下一步驟資訊</returns>
        public async Task<ReviewNextStepOutput> GetNextStepAsync(int taskId)
        {
            var task = await Db.ReviewTasks
                .Include(x => x.ReviewSteps)
                    .ThenInclude(s => s.ReviewApprovers)
                .FirstOrDefaultAsync(x => x.TaskId == taskId && x.Status != "deleted");

            if (task == null)
            {
                throw new Exception("找不到指定的審核流程");
            }

            // 獲取所有未刪除的步驟，按順序排序
            var steps = task.ReviewSteps
                .Where(s => s.Status != "deleted")
                .OrderBy(s => s.Sequence)
                .ToList();

            // 找出當前待處理的步驟（狀態為waiting的第一個步驟）
            var nextStep = steps.FirstOrDefault(s => s.Status == "waiting");

            // 如果沒有待處理的步驟，表示流程已完成或全部拒絕
            if (nextStep == null)
            {
                return null;
            }

            // 獲取該步驟的審核人員資訊
            return new ReviewNextStepOutput
            {
                StepId = nextStep.StepId,
                StepName = nextStep.Name,
                Sequence = nextStep.Sequence,
                Approvers = nextStep.ReviewApprovers
                    .Where(a => a.IsRequired == true)
                    .Select(a => new ReviewApproverOutput
                    {
                        ApproverId = a.ApproverId,
                        UserInfoId = a.UserInfoId
                    })
                    .ToList()
            };
        }

        /// <summary>
        /// 獲取審核人員清單
        /// </summary>
        /// <returns>審核人員清單</returns>
        public async Task<List<ReviewDeptGroupOutput>> GetReviewUsersAsync()
        {
            var users = await (from u in Db.UserInfos
                             join ud in Db.UserDepartments on u.UserInfoId equals ud.UserInfoId
                             join d in Db.Departments on new { CompanyId = u.CompanyId, DepartmentId = ud.DepartmentId } equals new { d.CompanyId, d.DepartmentId }
                             join uj in Db.UserJobTitles on u.UserInfoId equals uj.UserInfoId
                             join j in Db.JobTitles on uj.JobTitleId equals j.JobTitleId
                             where u.Status && ud.IsPrimary == true
                             orderby d.Name, u.Name
                             select new ReviewUserOutput
                             {
                                 UserInfoId = u.UserInfoId,
                                 UserName = u.Name,
                                 DeptId = d.DepartmentId,
                                 DeptName = d.Name,
                                 GradeCode = j.JobTitleId,
                                 GradeName = j.Name
                             })
                             .ToListAsync();

            // 在內存中進行分組
            var deptGroups = users
                .GroupBy(x => x.DeptName)
                .OrderBy(g => g.Key)
                .Select(g => new ReviewDeptGroupOutput
                {
                    Dept = new ReviewDeptOutput { DeptName = g.Key },
                    Users = g.OrderBy(x => x.UserName).ToList()
                })
                .ToList();

            return deptGroups;
        }
    }
} 
