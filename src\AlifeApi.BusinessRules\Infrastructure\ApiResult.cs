﻿using System.Text.Json.Serialization;

namespace AlifeApi.BusinessRules.Infrastructure
{
    /// <summary>
    /// API 回傳的通用結果
    /// </summary>
    /// <typeparam name="TBody">回應資料的型別</typeparam>
    /// <remarks>前端模板接受大駝峰命名，但這個 API 的屬性應該使用小駝峰命名</remarks>
    public class ApiResult<TBody>
    {
        /// <summary>
        /// 回傳資料
        /// </summary>
        [JsonPropertyName("body")]
        public TBody Body { get; set; }

        /// <summary>
        /// 系統訊息，預設 <c>Success</c>
        /// </summary>
        [JsonIgnore]
        public Enum Reponse { get; set; } = Infrastructure.Message.Success;

        /// <summary>
        /// 系統訊息的描述
        /// </summary>
        [JsonPropertyName("message")]
        public string Message => Reponse.GetDescription();

        /// <summary>
        /// 系統訊息的代碼
        /// </summary>
        [JsonPropertyName("code")]
        public string Code => Convert.ToInt32(Reponse).ToString("000");

        /// <summary>
        /// 是否成功
        /// </summary>
        [JsonPropertyName("isSuccess")]
        public bool IsSuccess => true; // 別問我為什麼回傳 true，我看舊專案看不出什麼情況下會是 false，但前端需要這參數

        /// <summary>
        /// 例外或資料檢核結果
        /// </summary>
        [JsonPropertyName("exception")]
        public string Exception { get; set; }
    }

    /// <summary>
    /// API 回傳的通用結果
    /// </summary>
    public class ApiResult : ApiResult<object>
    {
    }
}
