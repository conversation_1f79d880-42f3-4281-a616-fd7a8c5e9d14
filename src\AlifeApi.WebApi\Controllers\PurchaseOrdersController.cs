using System;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using AlifeApi.BusinessRules.PurchaseOrderModels;
using AlifeApi.BusinessRules.Infrastructure;

namespace AlifeApi.WebApi.Controllers
{
    /// <summary>
    /// 買賣預定單管理
    /// </summary>
    public class PurchaseOrdersController : AuthenticatedController
    {
        private readonly PurchaseOrderService _purchaseOrderService;

        public PurchaseOrdersController(PurchaseOrderService purchaseOrderService)
        {
            _purchaseOrderService = purchaseOrderService ?? throw new ArgumentNullException(nameof(purchaseOrderService));
        }

        /// <summary>
        /// 取得買賣預定單列表 (分頁)
        /// </summary>
        /// <param name="input">查詢條件</param>
        /// <returns>分頁的買賣預定單列表</returns>
        [HttpPost]
        public async Task<ActionResult<PagedListOutput<PurchaseOrderListOutput>>> GetPurchaseOrders([FromBody] PurchaseOrderQueryInput input)
        {
            var result = await _purchaseOrderService.GetPurchaseOrderListAsync(input);
            return Ok(result);
        }

        /// <summary>
        /// 根據訂單ID取得詳細資訊
        /// </summary>
        /// <param name="orderId">訂單ID</param>
        /// <returns>訂單詳細資訊</returns>
        [HttpGet("{orderId}")]
        public async Task<ActionResult<PurchaseOrderOutput>> GetPurchaseOrder(int orderId)
        {
            var result = await _purchaseOrderService.GetPurchaseOrderByIdAsync(orderId);
            if (result == null)
                return NotFound();
            return Ok(result);
        }

        /// <summary>
        /// 新增買賣預定單
        /// </summary>
        /// <param name="input">訂單建立輸入資料</param>
        /// <returns>新增的訂單ID</returns>
        [HttpPost]
        public async Task<ActionResult<int>> CreatePurchaseOrder([FromBody] PurchaseOrderCreateInput input)
        {
            try
            {
                var id = await _purchaseOrderService.CreatePurchaseOrderAsync(input);
                return CreatedAtAction(nameof(GetPurchaseOrder), new { orderId = id }, new { OrderId = id });
            }
            catch (Exception ex)
            {
                return BadRequest(new { message = ex.Message });
            }
        }

        /// <summary>
        /// 更新買賣預定單
        /// </summary>
        /// <param name="orderId">訂單ID</param>
        /// <param name="input">訂單更新輸入資料</param>
        /// <returns>NoContent</returns>
        [HttpPut("{orderId}")]
        public async Task<ActionResult> UpdatePurchaseOrder(int orderId, [FromBody] PurchaseOrderUpdateInput input)
        {
            try
            {
                await _purchaseOrderService.UpdatePurchaseOrderAsync(orderId, input);
                return NoContent();
            }
            catch (Exception ex)
            {
                return BadRequest(new { message = ex.Message });
            }
        }

        /// <summary>
        /// 刪除買賣預定單
        /// </summary>
        /// <param name="orderId">訂單ID</param>
        /// <returns>NoContent</returns>
        [HttpDelete("{orderId}")]
        public async Task<ActionResult> DeletePurchaseOrder(int orderId)
        {
            try
            {
                await _purchaseOrderService.DeletePurchaseOrderAsync(orderId);
                return NoContent();
            }
            catch (Exception ex)
            {
                return BadRequest(new { message = ex.Message });
            }
        }

        /// <summary>
        /// 更新訂單業務流程日期 (售、足、簽、請、領)
        /// </summary>
        /// <param name="orderId">訂單ID</param>
        /// <param name="input">業務日期更新輸入</param>
        /// <returns>NoContent</returns>
        [HttpPut("{orderId}/business-dates")]
        public async Task<ActionResult> UpdateBusinessDates(int orderId, [FromBody] BusinessDateUpdateInput input)
        {
            try
            {
                await _purchaseOrderService.UpdateBusinessDatesAsync(orderId, input);
                return NoContent();
            }
            catch (Exception ex)
            {
                return BadRequest(new { message = ex.Message });
            }
        }
    }

    /// <summary>
    /// 業務日期更新輸入模型
    /// </summary>
    public class BusinessDateUpdateInput
    {
        /// <summary>
        /// 實際售出日期 (狀態：售)
        /// </summary>
        public DateOnly? SaleDate { get; set; }

        /// <summary>
        /// 足訂日期 (狀態：足)
        /// </summary>
        public DateOnly? DepositFullPaidDate { get; set; }

        /// <summary>
        /// 實際簽約日期 (狀態：簽)
        /// </summary>
        public DateOnly? ContractSignedDate { get; set; }

        /// <summary>
        /// 請款日期 (狀態：請)
        /// </summary>
        public DateOnly? RequestDate { get; set; }

        /// <summary>
        /// 領款日期 (狀態：領)
        /// </summary>
        public DateOnly? ReceiveDate { get; set; }

        /// <summary>
        /// 退訂日期
        /// </summary>
        public DateOnly? CancellationDate { get; set; }

        /// <summary>
        /// 交屋日期
        /// </summary>
        public DateOnly? HandoverDate { get; set; }

        /// <summary>
        /// 尾款繳清日期
        /// </summary>
        public DateOnly? FinalPaymentDate { get; set; }
    }
} 
