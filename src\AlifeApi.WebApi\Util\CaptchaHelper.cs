﻿using AlifeApi.Common.DependencyInjection;
using AlifeApi.Common.Util;
using SkiaSharp;

namespace AlifeApi.WebApi.Util
{
    public class CaptchaHelper : ISingletonDependency
    {
        private const string Chars = "0123456789";

        /// <summary>
        /// 圖片寬度
        /// </summary>
        private const int ImageWidth = 150;

        /// <summary>
        /// 圖片高度
        /// </summary>
        private const int ImageHeight = 30;

        /// <summary>
        /// 文字顏色深度，數值越高越亮 越低越暗 0-255
        /// </summary>
        private const int TextColorDepth = 80;

        /// <summary>
        /// 干擾線最淺深度，數值越高越亮 越低越暗 0-255
        /// </summary>
        private const byte InterferenceColorMinDepth = 100;

        /// <summary>
        /// 干擾線最深深度，數值越高越亮 越低越暗 0-255
        /// </summary>
        private const byte InterferenceColorMaxDepth = 200;

        /// <summary>
        /// 背景顏色
        /// </summary>
        private static readonly SKColor BackGroundColor = SKColors.White;

        /// <summary>
        /// 隨機每個驗證碼字元的字體列表
        /// </summary>
        private static readonly List<SKTypeface> fonts = GetFonts();

        private static List<SKTypeface> GetFonts()
        {
            string[] fontNames = new string[] {
                "Arial", "Arial Black", "Calibri", "Cambria", "Verdana",
                "Trebuchet MS", "Palatino Linotype", "Georgia", "Constantia",
                "Consolas", "Comic Sans MS", "Century Gothic", "Candara",
                "Courier New", "Times New Roman"
            };

            return fontNames
                .Select(x => SKTypeface.FromFamilyName(x, SKFontStyleWeight.Bold, SKFontStyleWidth.Normal, SKFontStyleSlant.Italic))
                .ToList();
        }

        /// <inheritdoc/>
        public string GenerateRandomText(int textLength = 6)
        {
            return new string(Enumerable.Repeat(Chars, textLength)
                  .Select(s => s[RandomUtils.Next(s.Length - 1)]).ToArray());
        }

        /// <inheritdoc/>
        public byte[] GenerateCaptchaImage(string text)
        {
            using SKBitmap bitmap = new(ImageWidth, ImageHeight);
            using SKCanvas canvas = new(bitmap);
            canvas.Clear(BackGroundColor);

            List<SKPaint> paints = new();
            float totalWidth = 0;
            foreach (char c in text)
            {
                SKPaint paint = new()
                {
                    Typeface = fonts[RandomUtils.Next(0, fonts.Count - 1)],
                    TextSize = 26,
                    IsAntialias = true,
                    Color = GetRandomColor(0, TextColorDepth),
                    TextSkewX = (float)RandomUtils.Next(-1, 1) / 4,
                };

                paints.Add(paint);

                totalWidth += paint.MeasureText(c.ToString()) + 5; // 包括間距
            }
            totalWidth -= 10;

            float x = (ImageWidth - totalWidth) / 2;

            // 用迴圈目的為讓每一個字的顏色跟角度都不一樣
            for (int i = 0; i < text.Length; i++)
            {
                SKPaint paint = paints[i];

                float y = (ImageHeight + paint.FontMetrics.Descent - paint.FontMetrics.Ascent) / 2 - paint.FontMetrics.Descent;
                canvas.DrawText(text[i].ToString(), x, y, paint);

                x += paint.MeasureText(text[i].ToString()) + 5; // 調整間距
            }

            //InterferenceLines(canvas, text.Length);

            using MemoryStream ms = new();
            bitmap.Encode(ms, SKEncodedImageFormat.Png, 100);

            return ms.ToArray();
        }

        /// <summary>
        /// 隨機劃出干擾線
        /// </summary>
        /// <param name="canvas">畫布</param>
        /// <param name="lineCount">干擾線數量</param>
        private static void InterferenceLines(SKCanvas canvas, int lineCount)
        {
            for (int lineIndex = 0; lineIndex < lineCount; lineIndex++)
            {
                SKPaint paint = new()
                {
                    Color = GetRandomColor(InterferenceColorMinDepth, InterferenceColorMaxDepth),
                    StrokeWidth = 1
                };

                SKPoint startPoint = new(RandomUtils.Next(0, ImageWidth), RandomUtils.Next(0, ImageHeight));
                int pointCount = RandomUtils.Next(2, 5);
                for (int pointIndex = 0; pointIndex < pointCount; pointIndex++)
                {
                    SKPoint endPoint = new(RandomUtils.Next(0, ImageWidth), RandomUtils.Next(0, ImageHeight));
                    canvas.DrawLine(startPoint, endPoint, paint);

                    startPoint = endPoint;
                }
            }
        }

        /// <summary>
        /// 隨機產生顏色
        /// </summary>
        /// <param name="minDepth">顏色最淺的深度</param>
        /// <param name="maxDepth">顏色最深的深度</param>
        /// <returns>顏色</returns>
        private static SKColor GetRandomColor(byte minDepth, byte maxDepth)
        {
            int red = RandomUtils.Next(minDepth, maxDepth);
            int green = RandomUtils.Next(minDepth, maxDepth);
            int blue = red + green > 400 ? 0 : 400 - red - green;
            blue = blue > maxDepth ? maxDepth : blue;

            return new SKColor((byte)red, (byte)green, (byte)blue);
        }
    }
}
