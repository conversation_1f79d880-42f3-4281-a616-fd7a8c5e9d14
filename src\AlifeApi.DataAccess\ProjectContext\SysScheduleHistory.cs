﻿using System;
using System.Collections.Generic;

namespace AlifeApi.DataAccess.ProjectContext
{
    public partial class SysScheduleHistory
    {
        public long SsId { get; set; }

        /// <summary>
        /// 排程名稱
        /// </summary>
        public string SsName { get; set; }

        /// <summary>
        /// 排程開始時間
        /// </summary>
        public DateTime SsStartTime { get; set; }

        /// <summary>
        /// 排程結束時間
        /// </summary>
        public DateTime SsEndTime { get; set; }

        /// <summary>
        /// 錯誤訊息
        /// </summary>
        public string SsError { get; set; }

    }
}
