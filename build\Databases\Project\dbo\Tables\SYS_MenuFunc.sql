﻿CREATE TABLE [dbo].[SYS_MenuFunc] (
    [System]       VARCHAR (30)  NOT NULL,
    [FuncId]       VARCHAR (50)  NOT NULL,
    [FuncName]     NVARCHAR (50) NOT NULL,
    [FuncOrder]    INT           NOT NULL,
    [ParentFuncId] VARCHAR (50)  NOT NULL,
    CONSTRAINT [PK_SYS_MenuFunc] PRIMARY KEY CLUSTERED ([System] ASC, [FuncId] ASC)
);


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'系統選單資料', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'SYS_MenuFunc';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'系統名稱', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'SYS_MenuFunc', @level2type = N'COLUMN', @level2name = N'System';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'系統項目識別編號', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'SYS_MenuFunc', @level2type = N'COLUMN', @level2name = N'FuncId';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'系統項目名稱', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'SYS_MenuFunc', @level2type = N'COLUMN', @level2name = N'FuncName';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'項目排序', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'SYS_MenuFunc', @level2type = N'COLUMN', @level2name = N'FuncOrder';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'項目路徑', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'SYS_MenuFunc', @level2type = N'COLUMN', @level2name = N'ParentFuncId';

