﻿using System;
using System.Collections.Generic;

namespace AlifeApi.DataAccess.ProjectContent
{
    /// <summary>
    /// 供應商資料表，用於儲存供應商的基本資訊
    /// </summary>
    public partial class Supplier
    {
        public Supplier()
        {
            SupplierFiles = new HashSet<SupplierFile>();
        }

        /// <summary>
        /// 供應商唯一識別碼，自動遞增
        /// </summary>
        public int SupplierId { get; set; }
        /// <summary>
        /// 供應商名稱（個人或公司名稱）
        /// </summary>
        public string Name { get; set; }
        /// <summary>
        /// 人別（個人或法人，例如：個人、公司）
        /// </summary>
        public string PersonType { get; set; }
        /// <summary>
        /// 負責人姓名
        /// </summary>
        public string ContactPerson { get; set; }
        /// <summary>
        /// 證號類型（例如：身分證、統一編號）
        /// </summary>
        public string IdType { get; set; }
        /// <summary>
        /// 證號（身分證號或統一編號）
        /// </summary>
        public string IdNumber { get; set; }
        /// <summary>
        /// 公司電話
        /// </summary>
        public string CompanyPhone { get; set; }
        /// <summary>
        /// 通訊地址
        /// </summary>
        public string Address { get; set; }
        /// <summary>
        /// 聯絡窗口姓名
        /// </summary>
        public string ContactName { get; set; }
        /// <summary>
        /// 聯絡電話1
        /// </summary>
        public string ContactPhone1 { get; set; }
        /// <summary>
        /// 聯絡電話2
        /// </summary>
        public string ContactPhone2 { get; set; }
        /// <summary>
        /// 電子郵件
        /// </summary>
        public string Email { get; set; }
        /// <summary>
        /// 銀行名稱
        /// </summary>
        public string BankName { get; set; }
        /// <summary>
        /// 銀行分行名稱
        /// </summary>
        public string BankBranch { get; set; }
        /// <summary>
        /// 收款戶名
        /// </summary>
        public string AccountName { get; set; }
        /// <summary>
        /// 資料創建人識別碼（參考 UserInfo 表的 UserInfoId）
        /// </summary>
        public string CreatedUserInfoId { get; set; }
        /// <summary>
        /// 資料更新人識別碼（參考 UserInfo 表的 UserInfoId）
        /// </summary>
        public string UpdatedUserInfoId { get; set; }
        /// <summary>
        /// 資料更新時間
        /// </summary>
        public DateTime UpdatedTime { get; set; }
        /// <summary>
        /// 資料創建時間
        /// </summary>
        public DateTime CreatedTime { get; set; }

        public virtual ICollection<SupplierFile> SupplierFiles { get; set; }
    }
}
