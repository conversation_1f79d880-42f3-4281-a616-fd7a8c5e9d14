#### [AlifeApi.WebApi](index.md 'index')
### [AlifeApi.WebApi.Controllers](AlifeApi.WebApi.Controllers.md 'AlifeApi.WebApi.Controllers')

## SalesControlUnitUpdateInput Class

銷控表單位完整更新輸入

```csharp
public class SalesControlUnitUpdateInput
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; SalesControlUnitUpdateInput
### Properties

<a name='AlifeApi.WebApi.Controllers.SalesControlUnitUpdateInput.Area'></a>

## SalesControlUnitUpdateInput.Area Property

坪數 (如果有變更)

```csharp
public System.Nullable<decimal> Area { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.Decimal](https://docs.microsoft.com/en-us/dotnet/api/System.Decimal 'System.Decimal')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

<a name='AlifeApi.WebApi.Controllers.SalesControlUnitUpdateInput.OrderId'></a>

## SalesControlUnitUpdateInput.OrderId Property

如果狀態變更涉及訂單，可能需要的訂單ID

```csharp
public System.Nullable<int> OrderId { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

<a name='AlifeApi.WebApi.Controllers.SalesControlUnitUpdateInput.PurchaseInfo'></a>

## SalesControlUnitUpdateInput.PurchaseInfo Property

購買者資訊 (客戶姓名或聯絡資訊)

```csharp
public string? PurchaseInfo { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.WebApi.Controllers.SalesControlUnitUpdateInput.Remarks'></a>

## SalesControlUnitUpdateInput.Remarks Property

備註

```csharp
public string? Remarks { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.WebApi.Controllers.SalesControlUnitUpdateInput.SalePrice'></a>

## SalesControlUnitUpdateInput.SalePrice Property

售價

```csharp
public System.Nullable<decimal> SalePrice { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.Decimal](https://docs.microsoft.com/en-us/dotnet/api/System.Decimal 'System.Decimal')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

<a name='AlifeApi.WebApi.Controllers.SalesControlUnitUpdateInput.Status'></a>

## SalesControlUnitUpdateInput.Status Property

銷售狀態 (售、足、簽、請、領、可售、保留)

```csharp
public string Status { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.WebApi.Controllers.SalesControlUnitUpdateInput.UnitType'></a>

## SalesControlUnitUpdateInput.UnitType Property

房型 (如果有變更)

```csharp
public string? UnitType { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')