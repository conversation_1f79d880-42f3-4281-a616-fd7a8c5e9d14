#### [AlifeApi.BusinessRules](index.md 'index')

## AlifeApi.BusinessRules.SalesModels Namespace

| Classes | |
| :--- | :--- |
| [BuildingSalesData](AlifeApi.BusinessRules.SalesModels.BuildingSalesData.md 'AlifeApi.BusinessRules.SalesModels.BuildingSalesData') | 建築銷控表資料 |
| [ColumnSummary](AlifeApi.BusinessRules.SalesModels.ColumnSummary.md 'AlifeApi.BusinessRules.SalesModels.ColumnSummary') | 戶別統計摘要 (A戶、B戶等的統計) |
| [FloorInfo](AlifeApi.BusinessRules.SalesModels.FloorInfo.md 'AlifeApi.BusinessRules.SalesModels.FloorInfo') | 樓層資訊 |
| [FloorSummary](AlifeApi.BusinessRules.SalesModels.FloorSummary.md 'AlifeApi.BusinessRules.SalesModels.FloorSummary') | 樓層統計摘要 |
| [SalesControlInput](AlifeApi.BusinessRules.SalesModels.SalesControlInput.md 'AlifeApi.BusinessRules.SalesModels.SalesControlInput') | 銷控表查詢輸入模型 |
| [SalesService](AlifeApi.BusinessRules.SalesModels.SalesService.md 'AlifeApi.BusinessRules.SalesModels.SalesService') | 銷控表服務 |
| [SalesSummaryData](AlifeApi.BusinessRules.SalesModels.SalesSummaryData.md 'AlifeApi.BusinessRules.SalesModels.SalesSummaryData') | 銷售統計摘要資料 |
| [TotalSummary](AlifeApi.BusinessRules.SalesModels.TotalSummary.md 'AlifeApi.BusinessRules.SalesModels.TotalSummary') | 總體統計摘要 |
| [UnitInfo](AlifeApi.BusinessRules.SalesModels.UnitInfo.md 'AlifeApi.BusinessRules.SalesModels.UnitInfo') | 房屋單位資訊 |
| [UnitSalesStatus](AlifeApi.BusinessRules.SalesModels.UnitSalesStatus.md 'AlifeApi.BusinessRules.SalesModels.UnitSalesStatus') | 房屋單位銷售狀態<br/>業務流程：可售 -> 售(訂金未付足) -> 足(補足訂金) -> 簽(已簽合約)<br/>請款和領款不再是狀態，而是透過 RequestDate 和 ReceiveDate 欄位記錄 |
