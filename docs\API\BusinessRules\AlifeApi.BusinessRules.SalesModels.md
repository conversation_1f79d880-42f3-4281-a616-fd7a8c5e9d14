#### [AlifeApi.BusinessRules](index.md 'index')

## AlifeApi.BusinessRules.SalesModels Namespace

| Classes | |
| :--- | :--- |
| [FloorInfo](AlifeApi.BusinessRules.SalesModels.FloorInfo.md 'AlifeApi.BusinessRules.SalesModels.FloorInfo') | 樓層資訊 |
| [SalesControlInput](AlifeApi.BusinessRules.SalesModels.SalesControlInput.md 'AlifeApi.BusinessRules.SalesModels.SalesControlInput') | 銷控表查詢輸入模型 |
| [SalesService](AlifeApi.BusinessRules.SalesModels.SalesService.md 'AlifeApi.BusinessRules.SalesModels.SalesService') | - 2024/07/26  車位銷售管理<br/>- 關聯 PurchaseOrder.Id and PurchaseOrderItems<br/>- 車位透過 PurchaseOrderItems.ParkingSpaceId 關聯到 ParkingSpaces<br/>- 移除 PurchaseOrderParkingSpaces 表<br/>- 移除 PurchaseOrder 中的 UnitId, PropertyPrice, ParkingSpacePrice 欄位 |
| [SalesSummaryData](AlifeApi.BusinessRules.SalesModels.SalesSummaryData.md 'AlifeApi.BusinessRules.SalesModels.SalesSummaryData') | 銷售統計摘要資料 |
| [UnitInfo](AlifeApi.BusinessRules.SalesModels.UnitInfo.md 'AlifeApi.BusinessRules.SalesModels.UnitInfo') | 房屋單位資訊 |
| [UnitSalesStatus](AlifeApi.BusinessRules.SalesModels.UnitSalesStatus.md 'AlifeApi.BusinessRules.SalesModels.UnitSalesStatus') | 房屋單位銷售狀態<br/>業務流程：可售 -> 售(訂金未付足) -> 足(補足訂金) -> 簽(已簽合約)<br/>請款和領款不再是狀態，而是透過 RequestDate 和 ReceiveDate 欄位記錄 |
