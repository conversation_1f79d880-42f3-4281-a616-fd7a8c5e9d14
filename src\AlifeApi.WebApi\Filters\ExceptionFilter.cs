﻿using AlifeApi.BusinessRules.Infrastructure;
using AlifeApi.Common.DataAnnotations;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;
using System;

namespace AlifeApi.WebApi.Filters
{
    public class ExceptionFilter : IExceptionFilter
    {
        public void OnException(ExceptionContext context)
        {
            ApiResult result = new();
            if (context.Exception is KyException)
            {
                result = new()
                {
                    Reponse = Message.Http_400_BadRequest,
                    Exception = context.Exception.Message
                };
            }
            else
            {
                result = new()
                {
                    Reponse = Message.SystemError,
                    Exception = context.Exception.Message
                };
            }

            context.Result = new JsonResult(result);
            context.ExceptionHandled = true;
        }
    }
}
