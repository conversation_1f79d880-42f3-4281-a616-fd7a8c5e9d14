﻿using AlifeApi.Common;
using AlifeApi.Common.DependencyInjection;
using Microsoft.EntityFrameworkCore;

namespace AlifeApi.BusinessRules.Infrastructure
{
    /// <summary>
    /// The service base.
    /// </summary>
    /// <typeparam name="TDbContext">The type of the database context.</typeparam>
    public abstract class ServiceBase<TDbContext> : InfrastructureBase, IScopedDependency
        where TDbContext : DbContext
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="ServiceBase{TDbContext}"/> class.
        /// </summary>
        /// <param name="serviceProvider">The service provider.</param>
        /// <param name="dbContext">The database context.</param>
        /// <exception cref="ArgumentNullException">dbContext</exception>
        public ServiceBase(IServiceProvider serviceProvider, TDbContext dbContext) : base(serviceProvider)
        {
            Db = dbContext ?? throw new ArgumentNullException(nameof(dbContext));
        }

        /// <summary>
        /// Gets or sets the database context.
        /// </summary>
        protected TDbContext Db { get; set; }

        protected ICurrentUser CurrentUser => LazyServiceProvider.GetService<ICurrentUser>();
    }
}
