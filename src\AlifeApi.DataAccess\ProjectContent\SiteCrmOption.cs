﻿using System;
using System.Collections.Generic;

namespace AlifeApi.DataAccess.ProjectContent
{
    /// <summary>
    /// 儲存每個案場在不同CRM選項類型下的具體選項值
    /// </summary>
    public partial class SiteCrmOption
    {
        /// <summary>
        /// 主鍵，選項值唯一識別碼
        /// </summary>
        public long SiteCrmOptionId { get; set; }
        /// <summary>
        /// 關聯的案場代碼 (參考 Sites 表的 SiteCode)
        /// </summary>
        public string SiteCode { get; set; }
        /// <summary>
        /// 關聯的選項類型ID (參考 CrmOptionTypes 表)
        /// </summary>
        public long CrmOptionTypeId { get; set; }
        /// <summary>
        /// 實際顯示在下拉選單中的文字 (例如: 20-30坪, 3房2廳)
        /// </summary>
        public string OptionValue { get; set; }
        /// <summary>
        /// 在同一個下拉選單中的顯示順序，數字越小越前面
        /// </summary>
        public int SortOrder { get; set; }
        /// <summary>
        /// 此選項是否啟用
        /// </summary>
        public bool? IsActive { get; set; }
        /// <summary>
        /// 建立者，記錄創建此資料的員工編號。
        /// </summary>
        public string CreatedUserInfoId { get; set; }
        /// <summary>
        /// 創建時間，記錄資料創建的時間。
        /// </summary>
        public DateTime CreateTime { get; set; }
        /// <summary>
        /// 更新者，記錄最後更新此資料的員工編號。
        /// </summary>
        public string UpdatedUserInfoId { get; set; }
        /// <summary>
        /// 更新時間，記錄資料最後更新的時間。
        /// </summary>
        public DateTime UpdateTime { get; set; }
    }
}
