﻿using AlifeApi.BusinessRules.RoleGroupModels;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.Json.Serialization;
using System.Threading.Tasks;

namespace AlifeApi.BusinessRules.DeptModels
{
    public class DeptListResult
    {
        /// <summary>
        /// 使用者單位
        /// </summary>
        public string DeptId { get; set; }

        /// <summary>
        /// 使用者單位
        /// </summary>
        public string DeptName { get; set; }

        /// <summary>
        /// 上級使用者單位
        /// </summary>
        public string ParentDeptId { get; set; }

        /// <summary>
        /// 上級使用者單位
        /// </summary>
        public string ParentDeptName { get; set; }

        /// <summary>
        /// 部門主管
        /// </summary>
        public string LeaderUserId { get; set; }

        /// <summary>
        /// 建立人帳號
        /// </summary>
        [JsonPropertyName("CreateUserId")]
        public string CreatedUserId { get; set; }

        /// <summary>
        /// 建立時間
        /// </summary>
        [JsonPropertyName("CreateDate")]
        public DateTime CreatedTime { get; set; }

        /// <summary>
        /// 更新者帳號
        /// </summary>
        [JsonPropertyName("UpdateUserId")]
        public string UpdatedUserId { get; set; }

        /// <summary>
        /// 更新時間
        /// </summary>
        [JsonPropertyName("UpdateDate")]
        public DateTime UpdatedTime { get; set; }

        /// <summary>
        /// 是否刪除
        /// </summary>
        public bool IsDisabled { get; set; }

        /// <summary>
        /// 部門成員清單
        /// </summary>
        [JsonPropertyName("UserList")]
        public IEnumerable<string> UserIds { get; set; } = Enumerable.Empty<string>();

        /// <summary>
        /// 部門權限
        /// </summary>
        [JsonIgnore]
        public IEnumerable<string> FuncIds { get; set; } = Enumerable.Empty<string>();

        /// <summary>
        /// 部門權限
        /// </summary>
        public string Permission => string.Join(',', FuncIds);

        /// <summary>
        /// 權限樹
        /// </summary>
        [JsonPropertyName("PermissionTree")]
        public IEnumerable<MenuTreeOutput> MenuTrees { get; set; } = Enumerable.Empty<MenuTreeOutput>();

    }
}
