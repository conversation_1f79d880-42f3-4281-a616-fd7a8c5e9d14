﻿using AlifeApi.BusinessRules.Infrastructure;
using AlifeApi.DataAccess.ProjectContent;
using Microsoft.EntityFrameworkCore;
using System;
using System.Linq;
using System.Threading.Tasks;
using OfficeOpenXml;
using System.IO;
using AlifeApi.BusinessRules.FloorModels;
using AlifeApi.BusinessRules.ParkingSpaceModels;
using System.Collections.Generic;

namespace AlifeApi.BusinessRules.BuildingModels
{
    /// <summary>
    /// 建築物服務
    /// </summary>
    public class BuildingService : ServiceBase<alifeContext>
    {
        public BuildingService(IServiceProvider serviceProvider, alifeContext dbContext)
            : base(serviceProvider, dbContext)
        {
        }

        /// <summary>
        /// 建立建築物
        /// </summary>
        /// <param name="input">建築物建立輸入資料</param>
        /// <returns>新建建築物的ID</returns>
        public async Task<int> CreateBuildingAsync(BuildingCreateInput input)
        {
            ArgumentNullException.ThrowIfNull(input);

            // 檢查 SiteCode 是否存在
            if (!await Db.Sites.AnyAsync(s => s.SiteCode == input.SiteCode))
            {
                throw new Exception($"指定的案場代碼 '{input.SiteCode}' 不存在。");
            }

            var building = new Building
            {
                SiteCode = input.SiteCode,
                BuildingName = input.BuildingName,
                TotalAboveGroundFloors = input.TotalAboveGroundFloors,
                TotalBelowGroundFloors = input.TotalBelowGroundFloors,
                BuildingType = input.BuildingType,
                CompletionDate = input.CompletionDate,
                Remarks = input.Remarks,
                CreatedTime = DateTime.Now,
                UpdatedTime = DateTime.Now,
                CreatedUserInfoId = CurrentUser.UserId,
                UpdatedUserInfoId = CurrentUser.UserId
            };

            Db.Buildings.Add(building);
            await Db.SaveChangesAsync();

            return building.BuildingId;
        }

        /// <summary>
        /// 取得建築物列表 (分頁)
        /// </summary>
        /// <param name="input">查詢條件</param>
        /// <returns>分頁的建築物列表</returns>
        public async Task<PagedListOutput<BuildingListOutput>> GetBuildingListAsync(BuildingQueryInput input)
        {
            ArgumentNullException.ThrowIfNull(input);

            var query = Db.Buildings.AsQueryable();

            // 篩選 SiteCode
            if (!string.IsNullOrEmpty(input.SiteCode))
            {
                query = query.Where(b => b.SiteCode == input.SiteCode);
            }

            // 篩選 BuildingName (模糊查詢)
            if (!string.IsNullOrEmpty(input.BuildingName))
            {
                query = query.Where(b => b.BuildingName.Contains(input.BuildingName));
            }

            // 篩選 BuildingType
            if (!string.IsNullOrEmpty(input.BuildingType))
            {
                query = query.Where(b => b.BuildingType == input.BuildingType);
            }

            var pagedResult = await query
                .OrderByDescending(b => b.CreatedTime) // 可依需求調整排序欄位
                .Select(b => new BuildingListOutput
                {
                    BuildingId = b.BuildingId,
                    SiteCode = b.SiteCode,
                    // SiteName 會在稍後填充
                    BuildingName = b.BuildingName,
                    TotalAboveGroundFloors = b.TotalAboveGroundFloors,
                    TotalBelowGroundFloors = b.TotalBelowGroundFloors,
                    BuildingType = b.BuildingType,
                    CompletionDate = b.CompletionDate,
                    CreatedTime = b.CreatedTime
                })
                .ToPagedListOutputAsync(input);

            if (pagedResult.Details.Any()) // 僅當有資料時才查詢 SiteName
            {
                var siteCodes = pagedResult.Details.Select(b => b.SiteCode).Distinct().ToList();
                var siteNames = await Db.Sites
                                         .Where(s => siteCodes.Contains(s.SiteCode))
                                         .ToDictionaryAsync(s => s.SiteCode, s => s.SiteName);

                foreach (var buildingOutput in pagedResult.Details)
                {
                    if (siteNames.TryGetValue(buildingOutput.SiteCode, out var name))
                    {
                        buildingOutput.SiteName = name;
                    }
                }
            }

            return pagedResult;
        }

        /// <summary>
        /// 根據ID取得建築物詳細資料
        /// </summary>
        /// <param name="buildingId">建築物ID</param>
        /// <returns>建築物詳細資料</returns>
        public async Task<BuildingOutput?> GetBuildingByIdAsync(int buildingId)
        {
            var building = await Db.Buildings
                .Where(b => b.BuildingId == buildingId)
                .Select(b => new BuildingOutput
                {
                    BuildingId = b.BuildingId,
                    SiteCode = b.SiteCode,
                    BuildingName = b.BuildingName,
                    TotalAboveGroundFloors = b.TotalAboveGroundFloors,
                    TotalBelowGroundFloors = b.TotalBelowGroundFloors,
                    BuildingType = b.BuildingType,
                    CompletionDate = b.CompletionDate,
                    Remarks = b.Remarks,
                    CreatedTime = b.CreatedTime,
                    UpdatedTime = b.UpdatedTime,
                    CreatedUserInfoId = b.CreatedUserInfoId,
                    UpdatedUserInfoId = b.UpdatedUserInfoId
                })
                .FirstOrDefaultAsync();

            if (building == null)
            {
                return null;
            }

            // 查詢並填入建立者和更新者名稱 (如果需要)
            var userIds = new[] { building.CreatedUserInfoId, building.UpdatedUserInfoId }.Distinct().ToList();
            var users = await Db.UserInfos
                .Where(u => userIds.Contains(u.UserInfoId))
                .Select(u => new { u.UserInfoId, u.Name })
                .ToListAsync();
            var userMap = users.ToDictionary(u => u.UserInfoId, u => u.Name);

            if (userMap.TryGetValue(building.CreatedUserInfoId, out var createdName))
            {
                building.CreatedUserName = createdName;
            }
            if (userMap.TryGetValue(building.UpdatedUserInfoId, out var updatedName))
            {
                building.UpdatedUserName = updatedName;
            }

            return building;
        }

        /// <summary>
        /// 更新建築物資訊
        /// </summary>
        /// <param name="buildingId">建築物ID</param>
        /// <param name="input">建築物更新輸入資料</param>
        public async Task UpdateBuildingAsync(int buildingId, BuildingUpdateInput input)
        {
            ArgumentNullException.ThrowIfNull(input);

            var building = await Db.Buildings.FindAsync(buildingId);

            if (building == null)
            {
                throw new Exception($"找不到指定的建築物 (ID: {buildingId})");
            }

            building.BuildingName = input.BuildingName;
            building.TotalAboveGroundFloors = input.TotalAboveGroundFloors;
            building.TotalBelowGroundFloors = input.TotalBelowGroundFloors;
            building.BuildingType = input.BuildingType;
            building.CompletionDate = input.CompletionDate;
            building.Remarks = input.Remarks;
            building.UpdatedTime = DateTime.Now;
            building.UpdatedUserInfoId = CurrentUser.UserId;

            await Db.SaveChangesAsync();
        }

        /// <summary>
        /// 刪除建築物
        /// </summary>
        /// <param name="buildingId">建築物ID</param>
        public async Task DeleteBuildingAsync(int buildingId)
        {
            var building = await Db.Buildings.FindAsync(buildingId);

            if (building == null)
            {
                // 允許刪除不存在的紀錄，使其具有幂等性
                // throw new Exception($"找不到指定的建築物 (ID: {buildingId})");
                 return;
            }

            // 檢查是否有相關連的樓層，若有則不允許刪除 (因為 SQL 沒有設定 FK 限制)
            bool hasRelatedFloors = await Db.Floors.AnyAsync(f => f.BuildingId == buildingId);
            if (hasRelatedFloors)
            {
                throw new Exception($"無法刪除建築物 (ID: {buildingId})，因為其下尚有關聯的樓層。請先移除相關樓層。");
            }

             // 檢查是否有相關連的停車位，若有則不允許刪除
            bool hasRelatedParkingSpaces = await Db.ParkingSpaces.AnyAsync(p => p.BuildingId == buildingId);
            if (hasRelatedParkingSpaces)
            {
                throw new Exception($"無法刪除建築物 (ID: {buildingId})，因為其下尚有關聯的停車位。請先移除相關停車位。");
            }

            Db.Buildings.Remove(building);
            await Db.SaveChangesAsync();
        }

        /// <summary>
        /// 從 Excel 檔批次匯入建築物、樓層及車位
        /// </summary>
        /// <param name="excelStream">包含三個工作表的 Excel 檔："建築物","樓層","車位"，且各欄位須符合範本格式</param>
        public async Task BulkImportFromExcelAsync(Stream excelStream)
        {
            ExcelPackage.LicenseContext = LicenseContext.NonCommercial;
            using var package = new ExcelPackage(excelStream);
            var workbook = package.Workbook;

            var buildingSheet = workbook.Worksheets["建築物"] ?? throw new Exception("找不到名稱為 '建築物' 的工作表");
            var floorSheet = workbook.Worksheets["樓層"] ?? throw new Exception("找不到名稱為 '樓層' 的工作表");
            var parkingSheet = workbook.Worksheets["車位"] ?? throw new Exception("找不到名稱為 '車位' 的工作表");

            await using var transaction = await Db.Database.BeginTransactionAsync();
            try
            {
                // 匯入建築物
                for (int row = 2; row <= buildingSheet.Dimension.End.Row; row++)
                {
                    var bInput = new BuildingCreateInput
                    {
                        SiteCode = buildingSheet.Cells[row, 1].Text.Trim(),
                        BuildingName = buildingSheet.Cells[row, 2].Text.Trim(),
                        TotalAboveGroundFloors = int.TryParse(buildingSheet.Cells[row, 3].Text, out var a) ? a : null,
                        TotalBelowGroundFloors = int.TryParse(buildingSheet.Cells[row, 4].Text, out var b) ? b : null,
                        BuildingType = string.IsNullOrWhiteSpace(buildingSheet.Cells[row, 5].Text) ? null : buildingSheet.Cells[row, 5].Text.Trim(),
                        CompletionDate = DateOnly.TryParse(buildingSheet.Cells[row, 6].Text, out var cd) ? cd : null,
                        Remarks = string.IsNullOrWhiteSpace(buildingSheet.Cells[row, 7].Text) ? null : buildingSheet.Cells[row, 7].Text.Trim(),
                    };
                    await CreateBuildingAsync(bInput);
                }

                // 匯入樓層
                var floorService = LazyServiceProvider.GetService<FloorService>();
                for (int row = 2; row <= floorSheet.Dimension.End.Row; row++)
                {
                    var fInput = new FloorCreateInput
                    {
                        BuildingId = int.Parse(floorSheet.Cells[row, 1].Text),
                        SiteCode = floorSheet.Cells[row, 2].Text.Trim(),
                        FloorLabel = floorSheet.Cells[row, 3].Text.Trim(),
                        FloorLevel = int.Parse(floorSheet.Cells[row, 4].Text),
                        FloorType = floorSheet.Cells[row, 5].Text.Trim(),
                        FloorHeight = decimal.TryParse(floorSheet.Cells[row, 6].Text, out var fh) ? fh : null,
                        Remarks = string.IsNullOrWhiteSpace(floorSheet.Cells[row, 7].Text) ? null : floorSheet.Cells[row, 7].Text.Trim(),
                    };
                    await floorService.CreateFloorAsync(fInput);
                }

                // 匯入車位
                var psService = LazyServiceProvider.GetService<ParkingSpaceService>();
                for (int row = 2; row <= parkingSheet.Dimension.End.Row; row++)
                {
                    var pInput = new ParkingSpaceCreateInput
                    {
                        SiteCode = parkingSheet.Cells[row, 1].Text.Trim(),
                        BuildingId = int.Parse(parkingSheet.Cells[row, 2].Text),
                        FloorId = int.Parse(parkingSheet.Cells[row, 3].Text),
                        SpaceNumber = parkingSheet.Cells[row, 4].Text.Trim(),
                        SpaceType = parkingSheet.Cells[row, 5].Text.Trim(),
                        Dimensions = string.IsNullOrWhiteSpace(parkingSheet.Cells[row, 6].Text) ? null : parkingSheet.Cells[row, 6].Text.Trim(),
                        Location = string.IsNullOrWhiteSpace(parkingSheet.Cells[row, 7].Text) ? null : parkingSheet.Cells[row, 7].Text.Trim(),
                        Remarks = string.IsNullOrWhiteSpace(parkingSheet.Cells[row, 8].Text) ? null : parkingSheet.Cells[row, 8].Text.Trim(),
                    };
                    await psService.CreateParkingSpaceAsync(pInput);
                }

                await transaction.CommitAsync();
            }
            catch
            {
                await transaction.RollbackAsync();
                throw;
            }
        }

        /// <summary>
        /// 取得建築物下拉選單
        /// </summary>
        /// <param name="input">查詢條件</param>
        /// <returns>建築物下拉選單列表</returns>
        public async Task<List<BuildingDropdownOutput>> GetBuildingDropdownListAsync(BuildingDropdownInput input)
        {
            ArgumentNullException.ThrowIfNull(input, nameof(input));

            var query = Db.Buildings.AsQueryable();
            if (!string.IsNullOrEmpty(input.SiteCode))
            {
                query = query.Where(b => b.SiteCode == input.SiteCode);
            }

            return await query
                .OrderBy(b => b.BuildingName)
                .Select(b => new BuildingDropdownOutput
                {
                    Name = b.BuildingName,
                    Value = b.BuildingId
                })
                .ToListAsync();
        }
    }
} 
