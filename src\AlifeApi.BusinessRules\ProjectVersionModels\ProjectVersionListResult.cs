﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AlifeApi.BusinessRules.ProjectVersionModels
{
    public class ProjectVersionListResult : ProjectVersionBase
    {
        /// <summary>
        /// 版本號唯一值
        /// </summary>
        public int P_ID { get; set; }

        /// <summary>
        /// 系統專案版號
        /// </summary>
        [MaxLength(256)]
        public string Version { get; set; }

        /// <summary>
        /// 版本發布時間(DateTime)
        /// </summary>
        public DateTime? LastUpdateTime { get; set; }

        /// <summary>
        /// 版本發佈人
        /// </summary>
        public string LastUpdateUser { get; set; }

        /// <summary>
        /// Git唯一值
        /// </summary>
        public string VersionHash { get; set; }

        /// <summary>
        /// 更新內容
        /// </summary>
        public string Content { get; set; }

        
    }
}
