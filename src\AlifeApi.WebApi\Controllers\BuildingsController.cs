﻿using System;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using AlifeApi.BusinessRules.BuildingModels;
using AlifeApi.BusinessRules.FloorModels;
using AlifeApi.BusinessRules.CommonModels;
using AlifeApi.BusinessRules.Infrastructure;
using Microsoft.AspNetCore.Http;
using System.Collections.Generic;

namespace AlifeApi.WebApi.Controllers
{
    /// <summary>
    /// 建築物管理
    /// </summary>
    public class BuildingsController : AuthenticatedController
    {
        private readonly BuildingService _buildingService;
        private readonly FloorService _floorService;

        public BuildingsController(BuildingService buildingService, FloorService floorService)
        {
            _buildingService = buildingService ?? throw new ArgumentNullException(nameof(buildingService));
            _floorService = floorService ?? throw new ArgumentNullException(nameof(floorService));
        }

        /// <summary>
        /// 取得建築物列表 (分頁)
        /// </summary>
        /// <param name="input">查詢條件</param>
        /// <returns>分頁的建築物列表</returns>
        [HttpPost]
        public async Task<ActionResult<PagedListOutput<BuildingListOutput>>> GetBuildings([FromBody] BuildingQueryInput input)
        {
            var result = await _buildingService.GetBuildingListAsync(input);
            return Ok(result);
        }

        /// <summary>
        /// 根據建築物ID取得詳細資訊
        /// </summary>
        /// <param name="buildingId">建築物ID</param>
        /// <returns>建築物詳細資訊</returns>
        [HttpGet("{buildingId}")]
        public async Task<ActionResult<BuildingOutput>> GetBuilding(int buildingId)
        {
            var result = await _buildingService.GetBuildingByIdAsync(buildingId);
            if (result == null)
                return NotFound();
            return Ok(result);
        }

        /// <summary>
        /// 新增建築物
        /// </summary>
        /// <param name="input">建築物建立輸入資料</param>
        /// <returns>新增的建築物ID</returns>
        [HttpPost]
        public async Task<ActionResult<int>> CreateBuilding([FromBody] BuildingCreateInput input)
        {
            try
            {
                var id = await _buildingService.CreateBuildingAsync(input);
                return CreatedAtAction(nameof(GetBuilding), new { buildingId = id }, new { BuildingId = id });
            }
            catch (Exception ex)
            {
                return BadRequest(new { message = ex.Message });
            }
        }

        /// <summary>
        /// 更新建築物
        /// </summary>
        /// <param name="buildingId">建築物ID</param>
        /// <param name="input">建築物更新輸入資料</param>
        /// <returns>NoContent</returns>
        [HttpPut("{buildingId}")]
        public async Task<ActionResult> UpdateBuilding(int buildingId, [FromBody] BuildingUpdateInput input)
        {
            try
            {
                await _buildingService.UpdateBuildingAsync(buildingId, input);
                return NoContent();
            }
            catch (Exception ex)
            {
                return BadRequest(new { message = ex.Message });
            }
        }

        /// <summary>
        /// 刪除建築物
        /// </summary>
        /// <param name="buildingId">建築物ID</param>
        /// <returns>NoContent</returns>
        [HttpDelete("{buildingId}")]
        public async Task<ActionResult> DeleteBuilding(int buildingId)
        {
            try
            {
                await _buildingService.DeleteBuildingAsync(buildingId);
                return NoContent();
            }
            catch (Exception ex)
            {
                return BadRequest(new { message = ex.Message });
            }
        }

        /// <summary>
        /// 批次匯入建築物、樓層及車位
        /// </summary>
        /// <param name="file">包含三個工作表 (建築物, 樓層, 車位) 的 Excel 檔案</param>
        [HttpPost("Import")]
        public async Task<IActionResult> ImportFromExcel([FromForm] IFormFile file)
        {
            if (file == null || file.Length == 0)
            {
                return BadRequest("未提供有效的檔案");
            }
            try
            {
                using var stream = file.OpenReadStream();
                await _buildingService.BulkImportFromExcelAsync(stream);
                return Ok();
            }
            catch (Exception ex)
            {
                return BadRequest(new { message = ex.Message });
            }
        }

        /// <summary>
        /// 取得通用下拉選單 (建築物或樓層)
        /// </summary>
        /// <param name="input">通用查詢條件 (包含 SiteCode? 或 BuildingId?)</param>
        /// <returns>建築物下拉選單列表或樓層下拉選單列表</returns>
        [HttpPost]
        public async Task<IActionResult> GetCommonDropdownList([FromBody] CommonDropdownInput input)
        {
            try
            {
                if (!string.IsNullOrEmpty(input.SiteCode) && !input.BuildingId.HasValue)
                {
                    var buildingDropdownInput = new BuildingDropdownInput { SiteCode = input.SiteCode };
                    var buildingResult = await _buildingService.GetBuildingDropdownListAsync(buildingDropdownInput);
                    return Ok(buildingResult);
                }
                else if (input.BuildingId.HasValue && string.IsNullOrEmpty(input.SiteCode))
                {
                    var floorDropdownInput = new FloorDropdownInput { BuildingId = input.BuildingId.Value };
                    var floorResult = await _floorService.GetFloorDropdownListAsync(floorDropdownInput);
                    return Ok(floorResult);
                }
                else if (!string.IsNullOrEmpty(input.SiteCode) && input.BuildingId.HasValue)
                {
                    var buildingDropdownInput = new BuildingDropdownInput { SiteCode = input.SiteCode };
                    var buildingResult = await _buildingService.GetBuildingDropdownListAsync(buildingDropdownInput);
                    return Ok(buildingResult);
                }
                else
                {
                    return BadRequest("請提供 SiteCode (查詢建築物) 或 BuildingId (查詢樓層)。");
                }
            }
            catch (Exception ex)
            {
                return BadRequest(new { message = ex.Message });
            }
        }
    }
} 
