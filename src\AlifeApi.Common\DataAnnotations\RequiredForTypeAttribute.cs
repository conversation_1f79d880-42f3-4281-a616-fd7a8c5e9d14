﻿using System.ComponentModel.DataAnnotations;

namespace AlifeApi.Common.DataAnnotations
{
    /// <summary>
    /// 僅對指定的類型執行必填驗證的屬性
    /// </summary>
    /// <seealso cref="RequiredAttribute" />
    [AttributeUsage(AttributeTargets.Property | AttributeTargets.Field | AttributeTargets.Parameter, AllowMultiple = false)]
    public class RequiredForTypeAttribute : RequiredAttribute
    {
        private readonly Type[] _targetTypes;

        /// <summary>
        /// 初始化
        /// </summary>
        /// <param name="targetTypes">必填驗證應用的目標類型</param>
        public RequiredForTypeAttribute(params Type[] targetTypes)
        {
            this._targetTypes = targetTypes ?? throw new ArgumentNullException(nameof(targetTypes));
        }

        /// <summary>
        /// 確定指定的值對象是否有效
        /// </summary>
        /// <param name="value">要驗證的值對象</param>
        /// <returns>如果值對象有效，則為 true；否則為 false</returns>
        public override bool IsValid(object value)
        {
            return base.IsValid(value);
        }

        /// <summary>
        /// 驗證屬性值
        /// </summary>
        /// <param name="value">要驗證的屬性值</param>
        /// <param name="validationContext">表示要驗證的內容的上下文</param>
        /// <returns>驗證結果。</returns>
        protected override ValidationResult IsValid(object value, ValidationContext validationContext)
        {
            if (!_targetTypes.Contains(validationContext.ObjectType) || IsValid(value))
            {
                return ValidationResult.Success;
            }

            string[] memberNames = validationContext.MemberName != null
                ? new string[] { validationContext.MemberName } : null;
            return new ValidationResult(FormatErrorMessage(validationContext.DisplayName), memberNames);
        }
    }
}
