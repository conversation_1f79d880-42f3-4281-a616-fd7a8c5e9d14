#### [AlifeApi.BusinessRules](index.md 'index')
### [AlifeApi.BusinessRules.CategoryModels](AlifeApi.BusinessRules.CategoryModels.md 'AlifeApi.BusinessRules.CategoryModels')

## MediumCategoryInput Class

中分類資料輸入 DTO

```csharp
public class MediumCategoryInput
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; MediumCategoryInput
### Properties

<a name='AlifeApi.BusinessRules.CategoryModels.MediumCategoryInput.Description'></a>

## MediumCategoryInput.Description Property

中分類描述

```csharp
public string Description { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.CategoryModels.MediumCategoryInput.IsActive'></a>

## MediumCategoryInput.IsActive Property

是否啟用

```csharp
public bool IsActive { get; set; }
```

#### Property Value
[System.Boolean](https://docs.microsoft.com/en-us/dotnet/api/System.Boolean 'System.Boolean')

<a name='AlifeApi.BusinessRules.CategoryModels.MediumCategoryInput.LargeCategoryId'></a>

## MediumCategoryInput.LargeCategoryId Property

所屬大分類ID

```csharp
public long LargeCategoryId { get; set; }
```

#### Property Value
[System.Int64](https://docs.microsoft.com/en-us/dotnet/api/System.Int64 'System.Int64')

<a name='AlifeApi.BusinessRules.CategoryModels.MediumCategoryInput.Name'></a>

## MediumCategoryInput.Name Property

中分類名稱

```csharp
public string Name { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.CategoryModels.MediumCategoryInput.SortOrder'></a>

## MediumCategoryInput.SortOrder Property

排序順序

```csharp
public int SortOrder { get; set; }
```

#### Property Value
[System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')