#### [AlifeApi.BusinessRules](index.md 'index')
### [AlifeApi.BusinessRules.UserDeptModels](AlifeApi.BusinessRules.UserDeptModels.md 'AlifeApi.BusinessRules.UserDeptModels')

## UserDeptCreateInput Class

新增部門階層

```csharp
public class UserDeptCreateInput
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; UserDeptCreateInput

Derived  
&#8627; [UserDeptUpdateInput](AlifeApi.BusinessRules.UserDeptModels.UserDeptUpdateInput.md 'AlifeApi.BusinessRules.UserDeptModels.UserDeptUpdateInput')
### Properties

<a name='AlifeApi.BusinessRules.UserDeptModels.UserDeptCreateInput.DeptId'></a>

## UserDeptCreateInput.DeptId Property

部門代號

```csharp
public string DeptId { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.UserDeptModels.UserDeptCreateInput.DeptName'></a>

## UserDeptCreateInput.DeptName Property

部門代號名稱

```csharp
public string DeptName { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.UserDeptModels.UserDeptCreateInput.IsDisabled'></a>

## UserDeptCreateInput.IsDisabled Property

是否停用

```csharp
public System.Nullable<bool> IsDisabled { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.Boolean](https://docs.microsoft.com/en-us/dotnet/api/System.Boolean 'System.Boolean')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

<a name='AlifeApi.BusinessRules.UserDeptModels.UserDeptCreateInput.LeaderUserId'></a>

## UserDeptCreateInput.LeaderUserId Property

部門主管員工編號

```csharp
public string LeaderUserId { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.UserDeptModels.UserDeptCreateInput.ParentDeptId'></a>

## UserDeptCreateInput.ParentDeptId Property

上層部門代號

```csharp
public string ParentDeptId { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')