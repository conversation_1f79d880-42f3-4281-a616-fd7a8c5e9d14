﻿using System;
using System.Collections.Generic;

namespace AlifeApi.DataAccess.ProjectContext
{
    public partial class UserGradePermission
    {
        /// <summary>
        /// 系統名稱
        /// </summary>
        public string System { get; set; }

        /// <summary>
        /// 職稱識別編號
        /// </summary>
        public string GradeId { get; set; }

        /// <summary>
        /// 系統項目識別編號
        /// </summary>
        public string FuncId { get; set; }

        public virtual UserGrade Grade { get; set; }

        public virtual SysMenuFunc SysMenuFunc { get; set; }

    }
}
