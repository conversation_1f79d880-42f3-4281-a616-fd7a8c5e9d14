using System;
using System.ComponentModel.DataAnnotations;

namespace AlifeApi.BusinessRules.ParkingSpaceModels
{
    /// <summary>
    /// 停車位更新輸入模型
    /// </summary>
    public class ParkingSpaceUpdateInput
    {
        [Required(ErrorMessage = "缺少車位類型")]
        public string SpaceType { get; set; } = null!;

        public string? Dimensions { get; set; }
        
        /// <summary>
        /// 車位詳細位置描述 (例如 "靠近電梯", "角落位置")
        /// </summary>
        public string? Location { get; set; }
        
        public string? Remarks { get; set; }
    }
} 
