using System;
using System.ComponentModel.DataAnnotations;

namespace AlifeApi.BusinessRules.ParkingSpaceModels
{
    /// <summary>
    /// 停車位更新輸入模型
    /// </summary>
    public class ParkingSpaceUpdateInput
    {
        public int ParkingSpaceId { get; set; }
        public int FloorId { get; set; }
        public string SpaceNumber { get; set; }
        public string SpaceType { get; set; }
        public string Remarks { get; set; }
        public decimal? ListPrice { get; set; }
        public decimal? MinimumPrice { get; set; }
        public decimal? TransactionPrice { get; set; }
        public string Status { get; set; }
    }
} 
