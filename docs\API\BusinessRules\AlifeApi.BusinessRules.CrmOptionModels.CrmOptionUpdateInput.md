#### [AlifeApi.BusinessRules](index.md 'index')
### [AlifeApi.BusinessRules.CrmOptionModels](AlifeApi.BusinessRules.CrmOptionModels.md 'AlifeApi.BusinessRules.CrmOptionModels')

## CrmOptionUpdateInput Class

CRM選項更新輸入模型

```csharp
public class CrmOptionUpdateInput
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; CrmOptionUpdateInput
### Properties

<a name='AlifeApi.BusinessRules.CrmOptionModels.CrmOptionUpdateInput.IsActive'></a>

## CrmOptionUpdateInput.IsActive Property

是否啟用

```csharp
public bool IsActive { get; set; }
```

#### Property Value
[System.Boolean](https://docs.microsoft.com/en-us/dotnet/api/System.Boolean 'System.Boolean')

<a name='AlifeApi.BusinessRules.CrmOptionModels.CrmOptionUpdateInput.OptionValue'></a>

## CrmOptionUpdateInput.OptionValue Property

選項值

```csharp
public string OptionValue { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.CrmOptionModels.CrmOptionUpdateInput.SortOrder'></a>

## CrmOptionUpdateInput.SortOrder Property

排序順序

```csharp
public int SortOrder { get; set; }
```

#### Property Value
[System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')