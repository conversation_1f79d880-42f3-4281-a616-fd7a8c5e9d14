#### [AlifeApi.Common](index.md 'index')

## AlifeApi.Common.DependencyInjection Namespace

| Classes | |
| :--- | :--- |
| [LazyServiceProvider](AlifeApi.Common.DependencyInjection.LazyServiceProvider.md 'AlifeApi.Common.DependencyInjection.LazyServiceProvider') | Provides a lazy service provider. |

| Interfaces | |
| :--- | :--- |
| [IScopedDependency](AlifeApi.Common.DependencyInjection.IScopedDependency.md 'AlifeApi.Common.DependencyInjection.IScopedDependency') | Defines the interface for scoped dependencies. Scoped dependencies are created once per request. |
| [ISingletonDependency](AlifeApi.Common.DependencyInjection.ISingletonDependency.md 'AlifeApi.Common.DependencyInjection.ISingletonDependency') | Defines the interface for singleton dependencies. Singleton dependencies are created once and shared throughout the application lifetime. |
| [ITransientDependency](AlifeApi.Common.DependencyInjection.ITransientDependency.md 'AlifeApi.Common.DependencyInjection.ITransientDependency') | Defines the interface for transient dependencies. Transient dependencies are created each time they are requested. |
