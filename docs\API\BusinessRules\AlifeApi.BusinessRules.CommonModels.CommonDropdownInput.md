#### [AlifeApi.BusinessRules](index.md 'index')
### [AlifeApi.BusinessRules.CommonModels](AlifeApi.BusinessRules.CommonModels.md 'AlifeApi.BusinessRules.CommonModels')

## CommonDropdownInput Class

通用下拉選單輸入資料

```csharp
public class CommonDropdownInput
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; CommonDropdownInput
### Properties

<a name='AlifeApi.BusinessRules.CommonModels.CommonDropdownInput.BuildingId'></a>

## CommonDropdownInput.BuildingId Property

建築物ID (可選)

```csharp
public System.Nullable<int> BuildingId { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

<a name='AlifeApi.BusinessRules.CommonModels.CommonDropdownInput.SiteCode'></a>

## CommonDropdownInput.SiteCode Property

案場代碼 (可選)

```csharp
public string? SiteCode { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')