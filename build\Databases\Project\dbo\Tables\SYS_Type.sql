﻿CREATE TABLE [dbo].[SYS_Type] (
    [Type]     VARCHAR (50)   NOT NULL,
    [TypeDesc] NVARCHAR (300) NULL,
    [CanEdit]  BIT            NOT NULL,
    CONSTRAINT [PK_SYS_Type] PRIMARY KEY CLUSTERED ([Type] ASC)
);


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'代碼類型資料', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'SYS_Type';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'類型', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'SYS_Type', @level2type = N'COLUMN', @level2name = N'Type';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'描述', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'SYS_Type', @level2type = N'COLUMN', @level2name = N'TypeDesc';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'是否可編輯', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'SYS_Type', @level2type = N'COLUMN', @level2name = N'CanEdit';

