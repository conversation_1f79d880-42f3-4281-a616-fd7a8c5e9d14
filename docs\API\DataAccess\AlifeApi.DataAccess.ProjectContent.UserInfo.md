#### [AlifeApi.DataAccess](index.md 'index')
### [AlifeApi.DataAccess.ProjectContent](AlifeApi.DataAccess.ProjectContent.md 'AlifeApi.DataAccess.ProjectContent')

## UserInfo Class

員工基本資料表，用於儲存員工的個人資訊、聯絡方式等相關資料，角色、部門、職位資訊透過關聯表記錄。

```csharp
public class UserInfo
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; UserInfo
### Properties

<a name='AlifeApi.DataAccess.ProjectContent.UserInfo.BirthDate'></a>

## UserInfo.BirthDate Property

出生日期。

```csharp
public System.Nullable<System.DateOnly> BirthDate { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.DateOnly](https://docs.microsoft.com/en-us/dotnet/api/System.DateOnly 'System.DateOnly')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

<a name='AlifeApi.DataAccess.ProjectContent.UserInfo.CompanyId'></a>

## UserInfo.CompanyId Property

公司編號，對應 Company 表的 CompanyId，記錄員工所屬的主要公司。

```csharp
public string CompanyId { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContent.UserInfo.CreatedTime'></a>

## UserInfo.CreatedTime Property

創建時間，記錄資料創建的時間。

```csharp
public System.DateTime CreatedTime { get; set; }
```

#### Property Value
[System.DateTime](https://docs.microsoft.com/en-us/dotnet/api/System.DateTime 'System.DateTime')

<a name='AlifeApi.DataAccess.ProjectContent.UserInfo.CreatedUserInfoId'></a>

## UserInfo.CreatedUserInfoId Property

建立者，記錄創建此資料的員工編號。

```csharp
public string CreatedUserInfoId { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContent.UserInfo.Email'></a>

## UserInfo.Email Property

電子郵件地址。

```csharp
public string Email { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContent.UserInfo.EmergencyContactName'></a>

## UserInfo.EmergencyContactName Property

緊急聯絡人姓名。

```csharp
public string EmergencyContactName { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContent.UserInfo.EmergencyContactPhone'></a>

## UserInfo.EmergencyContactPhone Property

緊急聯絡人電話。

```csharp
public string EmergencyContactPhone { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContent.UserInfo.EmergencyContactRelation'></a>

## UserInfo.EmergencyContactRelation Property

與緊急聯絡人的關係，例如父母、配偶等。

```csharp
public string EmergencyContactRelation { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContent.UserInfo.Gender'></a>

## UserInfo.Gender Property

性別，例如 M（男）、F（女）。

```csharp
public string Gender { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContent.UserInfo.HireDate'></a>

## UserInfo.HireDate Property

到職日期。

```csharp
public System.Nullable<System.DateOnly> HireDate { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.DateOnly](https://docs.microsoft.com/en-us/dotnet/api/System.DateOnly 'System.DateOnly')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

<a name='AlifeApi.DataAccess.ProjectContent.UserInfo.Identity'></a>

## UserInfo.Identity Property

身分證字號

```csharp
public string Identity { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContent.UserInfo.IsEmailNotificationEnabled'></a>

## UserInfo.IsEmailNotificationEnabled Property

是否需要發送電子郵件通知，true 表示是，false 表示否。

```csharp
public System.Nullable<bool> IsEmailNotificationEnabled { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.Boolean](https://docs.microsoft.com/en-us/dotnet/api/System.Boolean 'System.Boolean')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

<a name='AlifeApi.DataAccess.ProjectContent.UserInfo.IsInside'></a>

## UserInfo.IsInside Property

使否為內場/外場人員

```csharp
public System.Nullable<bool> IsInside { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.Boolean](https://docs.microsoft.com/en-us/dotnet/api/System.Boolean 'System.Boolean')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

<a name='AlifeApi.DataAccess.ProjectContent.UserInfo.IsM365'></a>

## UserInfo.IsM365 Property

是否為 M365 帳號，true 表示是，false 表示否。

```csharp
public System.Nullable<bool> IsM365 { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.Boolean](https://docs.microsoft.com/en-us/dotnet/api/System.Boolean 'System.Boolean')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

<a name='AlifeApi.DataAccess.ProjectContent.UserInfo.LastLoginIp'></a>

## UserInfo.LastLoginIp Property

最後一次登入的 IP 地址。

```csharp
public string LastLoginIp { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContent.UserInfo.LastLoginTime'></a>

## UserInfo.LastLoginTime Property

最後一次登入的時間。

```csharp
public System.Nullable<System.DateTime> LastLoginTime { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.DateTime](https://docs.microsoft.com/en-us/dotnet/api/System.DateTime 'System.DateTime')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

<a name='AlifeApi.DataAccess.ProjectContent.UserInfo.LastLogoutTime'></a>

## UserInfo.LastLogoutTime Property

最後一次登出的時間。

```csharp
public System.Nullable<System.DateTime> LastLogoutTime { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.DateTime](https://docs.microsoft.com/en-us/dotnet/api/System.DateTime 'System.DateTime')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

<a name='AlifeApi.DataAccess.ProjectContent.UserInfo.LoginFailedCount'></a>

## UserInfo.LoginFailedCount Property

登入失敗次數加總，用於追蹤密碼錯誤次數。

```csharp
public System.Nullable<short> LoginFailedCount { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.Int16](https://docs.microsoft.com/en-us/dotnet/api/System.Int16 'System.Int16')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

<a name='AlifeApi.DataAccess.ProjectContent.UserInfo.MailingAddress'></a>

## UserInfo.MailingAddress Property

通訊地址。

```csharp
public string MailingAddress { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContent.UserInfo.MobileNumber'></a>

## UserInfo.MobileNumber Property

手機號碼。

```csharp
public string MobileNumber { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContent.UserInfo.Name'></a>

## UserInfo.Name Property

員工姓名。

```csharp
public string Name { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContent.UserInfo.Password'></a>

## UserInfo.Password Property

密碼，儲存加密後的密碼內容。

```csharp
public string Password { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContent.UserInfo.RegisteredAddress'></a>

## UserInfo.RegisteredAddress Property

戶籍地址。

```csharp
public string RegisteredAddress { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContent.UserInfo.ServiceUnit'></a>

## UserInfo.ServiceUnit Property

所屬案場。

```csharp
public string ServiceUnit { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContent.UserInfo.Status'></a>

## UserInfo.Status Property

狀態，true 表示啟用，false 表示停用。

```csharp
public bool Status { get; set; }
```

#### Property Value
[System.Boolean](https://docs.microsoft.com/en-us/dotnet/api/System.Boolean 'System.Boolean')

<a name='AlifeApi.DataAccess.ProjectContent.UserInfo.TelephoneNumber'></a>

## UserInfo.TelephoneNumber Property

電話號碼。

```csharp
public string TelephoneNumber { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContent.UserInfo.UpdatedTime'></a>

## UserInfo.UpdatedTime Property

更新時間，記錄資料最後更新的時間。

```csharp
public System.DateTime UpdatedTime { get; set; }
```

#### Property Value
[System.DateTime](https://docs.microsoft.com/en-us/dotnet/api/System.DateTime 'System.DateTime')

<a name='AlifeApi.DataAccess.ProjectContent.UserInfo.UpdatedUserInfoId'></a>

## UserInfo.UpdatedUserInfoId Property

更新者，記錄最後更新此資料的員工編號。

```csharp
public string UpdatedUserInfoId { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContent.UserInfo.UserInfoId'></a>

## UserInfo.UserInfoId Property

員工編號，主鍵，用於唯一識別員工。

```csharp
public string UserInfoId { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')