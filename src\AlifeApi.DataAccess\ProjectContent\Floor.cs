﻿using System;
using System.Collections.Generic;

namespace AlifeApi.DataAccess.ProjectContent
{
    /// <summary>
    /// 儲存建築物內每個樓層的資訊，包含住宅/商業樓層和停車場樓層。
    /// </summary>
    public partial class Floor
    {
        /// <summary>
        /// 樓層唯一識別碼 (主鍵, 自動遞增)。
        /// </summary>
        public int FloorId { get; set; }
        /// <summary>
        /// 所屬建築物識別碼 (參考 Buildings 表)。
        /// </summary>
        public int BuildingId { get; set; }
        /// <summary>
        /// 所屬案場編號 (參考 Sites 表) - 方便查詢用。
        /// </summary>
        public string SiteCode { get; set; }
        /// <summary>
        /// 樓層的顯示標示 (例如: &quot;15F&quot;, &quot;1F&quot;, &quot;店面&quot;, &quot;P1F&quot;, &quot;B1&quot;)。
        /// </summary>
        public string FloorLabel { get; set; }
        /// <summary>
        /// 樓層的數值，用於排序 (例如: 15, 1, 0, -1, -2)。
        /// </summary>
        public int FloorLevel { get; set; }
        /// <summary>
        /// 樓層主要用途，**用於區分住宅/商業樓層與停車場樓層** (例如: &quot;住宅&quot;, &quot;商業&quot;, &quot;停車場&quot; - 建議關聯 SYS_Code)。
        /// </summary>
        public string FloorType { get; set; }
        /// <summary>
        /// 樓層高度 (米)。
        /// </summary>
        public decimal? FloorHeight { get; set; }
        /// <summary>
        /// 備註。
        /// </summary>
        public string Remarks { get; set; }
        /// <summary>
        /// 資料創建時間 (TIMESTAMP WITHOUT TIME ZONE)。
        /// </summary>
        public DateTime CreatedTime { get; set; }
        /// <summary>
        /// 資料最後更新時間 (TIMESTAMP WITHOUT TIME ZONE)。
        /// </summary>
        public DateTime UpdatedTime { get; set; }
        /// <summary>
        /// 建立者 (參考 UserInfo 表)。
        /// </summary>
        public string CreatedUserInfoId { get; set; }
        /// <summary>
        /// 更新者 (參考 UserInfo 表)。
        /// </summary>
        public string UpdatedUserInfoId { get; set; }
    }
}
