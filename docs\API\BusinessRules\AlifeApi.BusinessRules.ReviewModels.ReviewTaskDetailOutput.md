#### [AlifeApi.BusinessRules](index.md 'index')
### [AlifeApi.BusinessRules.ReviewModels](AlifeApi.BusinessRules.ReviewModels.md 'AlifeApi.BusinessRules.ReviewModels')

## ReviewTaskDetailOutput Class

審核流程詳細資料輸出模型

```csharp
public class ReviewTaskDetailOutput : AlifeApi.BusinessRules.ReviewModels.ReviewTaskOutput
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; [ReviewTaskOutput](AlifeApi.BusinessRules.ReviewModels.ReviewTaskOutput.md 'AlifeApi.BusinessRules.ReviewModels.ReviewTaskOutput') &#129106; ReviewTaskDetailOutput
### Properties

<a name='AlifeApi.BusinessRules.ReviewModels.ReviewTaskDetailOutput.Deadline'></a>

## ReviewTaskDetailOutput.Deadline Property

截止日期

```csharp
public System.Nullable<System.DateTime> Deadline { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.DateTime](https://docs.microsoft.com/en-us/dotnet/api/System.DateTime 'System.DateTime')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

<a name='AlifeApi.BusinessRules.ReviewModels.ReviewTaskDetailOutput.Steps'></a>

## ReviewTaskDetailOutput.Steps Property

審核步驟清單

```csharp
public System.Collections.Generic.List<AlifeApi.BusinessRules.ReviewModels.ReviewStepInput> Steps { get; set; }
```

#### Property Value
[System.Collections.Generic.List&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Collections.Generic.List-1 'System.Collections.Generic.List`1')[ReviewStepInput](AlifeApi.BusinessRules.ReviewModels.ReviewStepInput.md 'AlifeApi.BusinessRules.ReviewModels.ReviewStepInput')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Collections.Generic.List-1 'System.Collections.Generic.List`1')