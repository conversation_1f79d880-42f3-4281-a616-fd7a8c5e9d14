#### [AlifeApi.BusinessRules](index.md 'index')
### [AlifeApi.BusinessRules.Infrastructure](AlifeApi.BusinessRules.Infrastructure.md 'AlifeApi.BusinessRules.Infrastructure')

## ICurrentUser Interface

表示當前使用者的介面

```csharp
public interface ICurrentUser
```
### Properties

<a name='AlifeApi.BusinessRules.Infrastructure.ICurrentUser.DeptId'></a>

## ICurrentUser.DeptId Property

部門 ID

```csharp
string? DeptId { get; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.Infrastructure.ICurrentUser.GradeCode'></a>

## ICurrentUser.GradeCode Property

職別

```csharp
string? GradeCode { get; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.Infrastructure.ICurrentUser.IPAddress'></a>

## ICurrentUser.IPAddress Property

Gets or sets the ip address.

```csharp
string IPAddress { get; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.Infrastructure.ICurrentUser.RoleIds'></a>

## ICurrentUser.RoleIds Property

角色 ID 的集合

```csharp
System.Collections.Generic.IEnumerable<string> RoleIds { get; }
```

#### Property Value
[System.Collections.Generic.IEnumerable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Collections.Generic.IEnumerable-1 'System.Collections.Generic.IEnumerable`1')[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Collections.Generic.IEnumerable-1 'System.Collections.Generic.IEnumerable`1')

<a name='AlifeApi.BusinessRules.Infrastructure.ICurrentUser.System'></a>

## ICurrentUser.System Property

系統

```csharp
string System { get; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.Infrastructure.ICurrentUser.UserId'></a>

## ICurrentUser.UserId Property

使用者 ID

```csharp
string UserId { get; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')