#### [AlifeApi.BusinessRules](index.md 'index')
### [AlifeApi.BusinessRules.UserInfoModels](AlifeApi.BusinessRules.UserInfoModels.md 'AlifeApi.BusinessRules.UserInfoModels')

## UserInfoDropdownInput Class

使用者下拉選單查詢條件

```csharp
public class UserInfoDropdownInput
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; UserInfoDropdownInput
### Properties

<a name='AlifeApi.BusinessRules.UserInfoModels.UserInfoDropdownInput.DepartmentId'></a>

## UserInfoDropdownInput.DepartmentId Property

部門ID

```csharp
public string DepartmentId { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.UserInfoModels.UserInfoDropdownInput.JobTitleId'></a>

## UserInfoDropdownInput.JobTitleId Property

職稱ID

```csharp
public string JobTitleId { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.UserInfoModels.UserInfoDropdownInput.Name'></a>

## UserInfoDropdownInput.Name Property

使用者姓名

```csharp
public string Name { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.UserInfoModels.UserInfoDropdownInput.RoleGroupId'></a>

## UserInfoDropdownInput.RoleGroupId Property

角色ID

```csharp
public string RoleGroupId { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')