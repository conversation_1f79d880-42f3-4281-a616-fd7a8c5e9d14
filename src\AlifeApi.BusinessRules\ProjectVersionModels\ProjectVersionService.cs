﻿using AlifeApi.BusinessRules.Infrastructure;
using AlifeApi.BusinessRules.LogAuditModels;
using AlifeApi.BusinessRules.UserInfoModels;
using AlifeApi.DataAccess.ProjectContext;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

//namespace AlifeApi.BusinessRules.ProjectVersionModels
//{
//    public class ProjectVersionService : ServiceBase<ProjectContext>
//    {
//        public ProjectVersionService(IServiceProvider serviceProvider, ProjectContext dbContext) : base(serviceProvider, dbContext)
//        {
//        }

//        public async Task<ProjectVersionOutput> GetProjectVersion(ProjectVersionBase input)
//        {
//            ArgumentNullException.ThrowIfNull(input, nameof(input));

//            ProjectVersionOutput output = new();

//            var res = await Db.SysProjectVersion.AsNoTracking().Where(f => f.PProjectName == input.ProjectName).OrderByDescending(x => x.PCrDateTime).Select(s => new ProjectVersionOutput
//            {
//                ProjectName = s.PProjectName,
//                Version = s.PVersionName,
//                LastUpdateTime = s.PCrDateTime
//            }).FirstOrDefaultAsync();

//            if (res != null)
//            {
//                output.Response = Message.Success;
//                output.ProjectName = res.ProjectName;
//                output.Version = res.Version;
//                output.LastUpdateTime = res.LastUpdateTime;
//            }
//            else
//            {
//                output.Response = Message.Http_400_BadRequest;
//            }
//            return output;
//        }

//        public async Task<PagedListOutput<ProjectVersionListResult>> GetProjectVersionList(ProjectVersionListCondition input)
//        {
//            ArgumentNullException.ThrowIfNull(input, nameof(input));

//            PagedListOutput<ProjectVersionListResult> output = await Db.SysProjectVersion.AsNoTracking()
//                .WhereIf(!string.IsNullOrEmpty(input.SearchContent),x => x.PProjectName.Contains(input.SearchContent) || x.PVersionHash.Contains(input.SearchContent) || x.PVersionName.Contains(input.SearchContent))
//                .WhereIf(input.SearchStartDate.HasValue, x => x.PCrDateTime >= input.SearchStartDate)
//                .WhereIf(input.SearchEndDate.HasValue, x => x.PCrDateTime <= input.SearchEndDate)
//                .Select(s => new ProjectVersionListResult
//                {
//                    P_ID = s.PId,
//                    ProjectName = s.PProjectName,
//                    Version = s.PVersionName,
//                    LastUpdateTime = s.PCrDateTime,
//                    Content = s.PContent,
//                    LastUpdateUser = s.PCrUser,
//                    VersionHash = s.PVersionHash
//                }).ToPagedListOutputAsync(input);

//            return output;
//        }

//        public async Task SyncProjectVersion(SyncProjectVersion input)
//        {
//            ArgumentNullException.ThrowIfNull(input, nameof(input));

//            var check = Db.SysProjectVersion.AsNoTracking().AsQueryable().Where(x => x.PProjectName == input.ProjectName).ToList();
//            if (!check.Any())
//            {
//                var version = new SysProjectVersion()
//                {
//                    PProjectName = input.ProjectName,
//                    PVersionHash = input.VersionHash,
//                    PContent = input.Content,
//                    PCrUser = input.LastUpdateUser,
//                    PVersionName = DateTime.Now.ToString("v.yy.MM.dd.001"),
//                    PCrDateTime = DateTime.Now
//                };
//                Db.SysProjectVersion.Add(version);
//            }
//            else
//            {
//                var OldVer = check.Where(x => x.PVersionHash == input.VersionHash).FirstOrDefault();
//                if (OldVer != null)
//                {
//                    var version = new SysProjectVersion()
//                    {
//                        PProjectName = OldVer.PProjectName,
//                        PVersionHash = OldVer.PVersionHash,
//                        PVersionName = OldVer.PVersionName,
//                        PCrDateTime = DateTime.Now,
//                        PContent = OldVer.PContent,
//                        PCrUser = OldVer.PCrUser
//                    };
//                    Db.SysProjectVersion.Add(version);
//                }
//                else
//                {
//                    var todaycount = check.Count(x => ((DateTime)x.PCrDateTime).Year == DateTime.Now.Year && ((DateTime)x.PCrDateTime).Month == DateTime.Now.Month && ((DateTime)x.PCrDateTime).Day == DateTime.Now.Day);
//                    var version = new SysProjectVersion()
//                    {
//                        PProjectName = input.ProjectName,
//                        PVersionHash = input.VersionHash,
//                        PContent = input.Content,
//                        PCrUser = input.LastUpdateUser,
//                        PVersionName = DateTime.Now.ToString($"v.yy.MM.dd.{todaycount.ToString("000")}"),
//                        PCrDateTime = DateTime.Now
//                    };
//                    Db.SysProjectVersion.Add(version);
//                }
//            }
//            await Db.SaveChangesAsync();
//        }
       
//    }
//}
