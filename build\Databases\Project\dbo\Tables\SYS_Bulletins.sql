﻿CREATE TABLE [dbo].[SYS_Bulletins] (
    [BL_Id]           INT             IDENTITY (1, 1) NOT NULL,
    [BL_Title]        NVARCHAR (200)  NULL,
    [BL_Content]      NVARCHAR (1000) NULL,
    [BL_PostDateFrom] DATE            NULL,
    [BL_PostDateToxx] DATE            NULL,
    [BL_IsTop]        BIT             NOT NULL,
    [BL_IsEnable]     BIT             NOT NULL,
    [BL_CrUser]       VARCHAR (15)    NULL,
    [BL_CrDatetime]   DATETIME        NULL,
    [BL_UpUser]       VARCHAR (15)    NULL,
    [BL_UpDatetime]   DATETIME        NULL,
    CONSTRAINT [PK_SYS_Bulletins] PRIMARY KEY CLUSTERED ([BL_Id] ASC)
);


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'更新時間', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'SYS_Bulletins', @level2type = N'COLUMN', @level2name = N'BL_UpDatetime';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'更新者', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'SYS_Bulletins', @level2type = N'COLUMN', @level2name = N'BL_UpUser';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'建立時間', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'SYS_Bulletins', @level2type = N'COLUMN', @level2name = N'BL_CrDatetime';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'建立者', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'SYS_Bulletins', @level2type = N'COLUMN', @level2name = N'BL_CrUser';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'是否上架', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'SYS_Bulletins', @level2type = N'COLUMN', @level2name = N'BL_IsEnable';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'是否置頂', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'SYS_Bulletins', @level2type = N'COLUMN', @level2name = N'BL_IsTop';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'結束日期', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'SYS_Bulletins', @level2type = N'COLUMN', @level2name = N'BL_PostDateToxx';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'啟用日期', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'SYS_Bulletins', @level2type = N'COLUMN', @level2name = N'BL_PostDateFrom';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'公告內容', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'SYS_Bulletins', @level2type = N'COLUMN', @level2name = N'BL_Content';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'公告標題', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'SYS_Bulletins', @level2type = N'COLUMN', @level2name = N'BL_Title';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'公告流水號', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'SYS_Bulletins', @level2type = N'COLUMN', @level2name = N'BL_Id';

