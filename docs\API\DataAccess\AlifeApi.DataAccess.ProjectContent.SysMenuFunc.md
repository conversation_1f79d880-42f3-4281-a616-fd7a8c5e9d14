#### [AlifeApi.DataAccess](index.md 'index')
### [AlifeApi.DataAccess.ProjectContent](AlifeApi.DataAccess.ProjectContent.md 'AlifeApi.DataAccess.ProjectContent')

## SysMenuFunc Class

系統功能選單表，用於儲存系統的功能項目及其層級結構。

```csharp
public class SysMenuFunc
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; SysMenuFunc
### Properties

<a name='AlifeApi.DataAccess.ProjectContent.SysMenuFunc.FuncId'></a>

## SysMenuFunc.FuncId Property

功能項目識別編號，主鍵的一部分，用於唯一識別功能項目。

```csharp
public string FuncId { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContent.SysMenuFunc.FuncName'></a>

## SysMenuFunc.FuncName Property

功能項目名稱。

```csharp
public string FuncName { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContent.SysMenuFunc.FuncOrder'></a>

## SysMenuFunc.FuncOrder Property

功能項目排序，用於控制選單顯示順序。

```csharp
public int FuncOrder { get; set; }
```

#### Property Value
[System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')

<a name='AlifeApi.DataAccess.ProjectContent.SysMenuFunc.ParentFuncId'></a>

## SysMenuFunc.ParentFuncId Property

上層功能項目識別編號，用於表示功能項目的層級結構。

```csharp
public string ParentFuncId { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContent.SysMenuFunc.System'></a>

## SysMenuFunc.System Property

系統名稱，主鍵的一部分，用於區分不同系統。

```csharp
public string System { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')