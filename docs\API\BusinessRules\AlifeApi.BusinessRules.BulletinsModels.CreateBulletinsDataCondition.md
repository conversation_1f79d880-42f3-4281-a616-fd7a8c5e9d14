#### [AlifeApi.BusinessRules](index.md 'index')
### [AlifeApi.BusinessRules.BulletinsModels](AlifeApi.BusinessRules.BulletinsModels.md 'AlifeApi.BusinessRules.BulletinsModels')

## CreateBulletinsDataCondition Class

```csharp
public class CreateBulletinsDataCondition
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; CreateBulletinsDataCondition
### Properties

<a name='AlifeApi.BusinessRules.BulletinsModels.CreateBulletinsDataCondition.AttachList'></a>

## CreateBulletinsDataCondition.AttachList Property

公告附檔清單

```csharp
public System.Collections.Generic.List<AlifeApi.BusinessRules.BulletinsModels.AttachListItem> AttachList { get; set; }
```

#### Property Value
[System.Collections.Generic.List&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Collections.Generic.List-1 'System.Collections.Generic.List`1')[AttachListItem](AlifeApi.BusinessRules.BulletinsModels.AttachListItem.md 'AlifeApi.BusinessRules.BulletinsModels.AttachListItem')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Collections.Generic.List-1 'System.Collections.Generic.List`1')

<a name='AlifeApi.BusinessRules.BulletinsModels.CreateBulletinsDataCondition.Content'></a>

## CreateBulletinsDataCondition.Content Property

公告內容

```csharp
public string Content { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.BulletinsModels.CreateBulletinsDataCondition.IsTop'></a>

## CreateBulletinsDataCondition.IsTop Property

是否至頂 [Required]

```csharp
public bool IsTop { get; set; }
```

#### Property Value
[System.Boolean](https://docs.microsoft.com/en-us/dotnet/api/System.Boolean 'System.Boolean')

<a name='AlifeApi.BusinessRules.BulletinsModels.CreateBulletinsDataCondition.PostDateFrom'></a>

## CreateBulletinsDataCondition.PostDateFrom Property

公告開始時間

```csharp
public System.Nullable<System.DateTime> PostDateFrom { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.DateTime](https://docs.microsoft.com/en-us/dotnet/api/System.DateTime 'System.DateTime')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

<a name='AlifeApi.BusinessRules.BulletinsModels.CreateBulletinsDataCondition.PostDateTo'></a>

## CreateBulletinsDataCondition.PostDateTo Property

公告結束時間

```csharp
public System.Nullable<System.DateTime> PostDateTo { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.DateTime](https://docs.microsoft.com/en-us/dotnet/api/System.DateTime 'System.DateTime')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

<a name='AlifeApi.BusinessRules.BulletinsModels.CreateBulletinsDataCondition.Title'></a>

## CreateBulletinsDataCondition.Title Property

公告標題

```csharp
public string Title { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')