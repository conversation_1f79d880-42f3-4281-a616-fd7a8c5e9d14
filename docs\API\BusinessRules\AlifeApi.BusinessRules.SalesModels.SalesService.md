#### [AlifeApi.BusinessRules](index.md 'index')
### [AlifeApi.BusinessRules.SalesModels](AlifeApi.BusinessRules.SalesModels.md 'AlifeApi.BusinessRules.SalesModels')

## SalesService Class

銷控表服務

```csharp
public class SalesService : AlifeApi.BusinessRules.Infrastructure.ServiceBase<AlifeApi.DataAccess.ProjectContent.alifeContext>
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; [AlifeApi.Common.InfrastructureBase](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.Common.InfrastructureBase 'AlifeApi.Common.InfrastructureBase') &#129106; [AlifeApi.BusinessRules.Infrastructure.ServiceBase&lt;](AlifeApi.BusinessRules.Infrastructure.ServiceBase_TDbContext_.md 'AlifeApi.BusinessRules.Infrastructure.ServiceBase<TDbContext>')[AlifeApi.DataAccess.ProjectContent.alifeContext](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.DataAccess.ProjectContent.alifeContext 'AlifeApi.DataAccess.ProjectContent.alifeContext')[&gt;](AlifeApi.BusinessRules.Infrastructure.ServiceBase_TDbContext_.md 'AlifeApi.BusinessRules.Infrastructure.ServiceBase<TDbContext>') &#129106; SalesService
### Methods

<a name='AlifeApi.BusinessRules.SalesModels.SalesService.CalculateColumnSummary(AlifeApi.BusinessRules.SalesModels.BuildingSalesData)'></a>

## SalesService.CalculateColumnSummary(BuildingSalesData) Method

計算戶別統計摘要 (A戶、B戶等)

```csharp
private static void CalculateColumnSummary(AlifeApi.BusinessRules.SalesModels.BuildingSalesData result);
```
#### Parameters

<a name='AlifeApi.BusinessRules.SalesModels.SalesService.CalculateColumnSummary(AlifeApi.BusinessRules.SalesModels.BuildingSalesData).result'></a>

`result` [BuildingSalesData](AlifeApi.BusinessRules.SalesModels.BuildingSalesData.md 'AlifeApi.BusinessRules.SalesModels.BuildingSalesData')

銷控表資料

<a name='AlifeApi.BusinessRules.SalesModels.SalesService.CalculateFloorSummary(AlifeApi.BusinessRules.SalesModels.FloorInfo)'></a>

## SalesService.CalculateFloorSummary(FloorInfo) Method

計算樓層統計摘要

```csharp
private static void CalculateFloorSummary(AlifeApi.BusinessRules.SalesModels.FloorInfo floorInfo);
```
#### Parameters

<a name='AlifeApi.BusinessRules.SalesModels.SalesService.CalculateFloorSummary(AlifeApi.BusinessRules.SalesModels.FloorInfo).floorInfo'></a>

`floorInfo` [FloorInfo](AlifeApi.BusinessRules.SalesModels.FloorInfo.md 'AlifeApi.BusinessRules.SalesModels.FloorInfo')

樓層資訊

<a name='AlifeApi.BusinessRules.SalesModels.SalesService.CalculateListSummary(global__SalesControlListOutput)'></a>

## SalesService.CalculateListSummary(SalesControlListOutput) Method

計算列表統計摘要

```csharp
private void CalculateListSummary(global::SalesControlListOutput result);
```
#### Parameters

<a name='AlifeApi.BusinessRules.SalesModels.SalesService.CalculateListSummary(global__SalesControlListOutput).result'></a>

`result` [SalesControlListOutput](https://docs.microsoft.com/en-us/dotnet/api/SalesControlListOutput 'SalesControlListOutput')

銷控表列表輸出結果

<a name='AlifeApi.BusinessRules.SalesModels.SalesService.CalculateParkingTransactionPrice(AlifeApi.DataAccess.ProjectContent.PurchaseOrder,System.Collections.Generic.Dictionary_int,System.Nullable_decimal__)'></a>

## SalesService.CalculateParkingTransactionPrice(PurchaseOrder, Dictionary<int,Nullable<decimal>>) Method

計算車位成交價總額

```csharp
private System.Nullable<decimal> CalculateParkingTransactionPrice(AlifeApi.DataAccess.ProjectContent.PurchaseOrder order, System.Collections.Generic.Dictionary<int,System.Nullable<decimal>> parkingUnits);
```
#### Parameters

<a name='AlifeApi.BusinessRules.SalesModels.SalesService.CalculateParkingTransactionPrice(AlifeApi.DataAccess.ProjectContent.PurchaseOrder,System.Collections.Generic.Dictionary_int,System.Nullable_decimal__).order'></a>

`order` [AlifeApi.DataAccess.ProjectContent.PurchaseOrder](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.DataAccess.ProjectContent.PurchaseOrder 'AlifeApi.DataAccess.ProjectContent.PurchaseOrder')

訂單資料

<a name='AlifeApi.BusinessRules.SalesModels.SalesService.CalculateParkingTransactionPrice(AlifeApi.DataAccess.ProjectContent.PurchaseOrder,System.Collections.Generic.Dictionary_int,System.Nullable_decimal__).parkingUnits'></a>

`parkingUnits` [System.Collections.Generic.Dictionary&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Collections.Generic.Dictionary-2 'System.Collections.Generic.Dictionary`2')[System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')[,](https://docs.microsoft.com/en-us/dotnet/api/System.Collections.Generic.Dictionary-2 'System.Collections.Generic.Dictionary`2')[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.Decimal](https://docs.microsoft.com/en-us/dotnet/api/System.Decimal 'System.Decimal')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Collections.Generic.Dictionary-2 'System.Collections.Generic.Dictionary`2')

車位單位價格字典

#### Returns
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.Decimal](https://docs.microsoft.com/en-us/dotnet/api/System.Decimal 'System.Decimal')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')  
車位成交價總額

<a name='AlifeApi.BusinessRules.SalesModels.SalesService.CalculateTotalSummary(AlifeApi.BusinessRules.SalesModels.BuildingSalesData)'></a>

## SalesService.CalculateTotalSummary(BuildingSalesData) Method

計算總體統計摘要

```csharp
private static void CalculateTotalSummary(AlifeApi.BusinessRules.SalesModels.BuildingSalesData result);
```
#### Parameters

<a name='AlifeApi.BusinessRules.SalesModels.SalesService.CalculateTotalSummary(AlifeApi.BusinessRules.SalesModels.BuildingSalesData).result'></a>

`result` [BuildingSalesData](AlifeApi.BusinessRules.SalesModels.BuildingSalesData.md 'AlifeApi.BusinessRules.SalesModels.BuildingSalesData')

銷控表資料

<a name='AlifeApi.BusinessRules.SalesModels.SalesService.DetermineUnitStatus(AlifeApi.DataAccess.ProjectContent.Unit,AlifeApi.DataAccess.ProjectContent.PurchaseOrder)'></a>

## SalesService.DetermineUnitStatus(Unit, PurchaseOrder) Method

根據房屋單位資料和訂單資料，判斷目前的銷售狀態  
業務流程：可售 -> 售(訂金未付足) -> 足(補足訂金) -> 簽(已簽合約)  
請款和領款不再是狀態，而是透過 RequestDate 和 ReceiveDate 欄位記錄

```csharp
private static string DetermineUnitStatus(AlifeApi.DataAccess.ProjectContent.Unit unit, AlifeApi.DataAccess.ProjectContent.PurchaseOrder? latestOrder);
```
#### Parameters

<a name='AlifeApi.BusinessRules.SalesModels.SalesService.DetermineUnitStatus(AlifeApi.DataAccess.ProjectContent.Unit,AlifeApi.DataAccess.ProjectContent.PurchaseOrder).unit'></a>

`unit` [AlifeApi.DataAccess.ProjectContent.Unit](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.DataAccess.ProjectContent.Unit 'AlifeApi.DataAccess.ProjectContent.Unit')

房屋單位資料

<a name='AlifeApi.BusinessRules.SalesModels.SalesService.DetermineUnitStatus(AlifeApi.DataAccess.ProjectContent.Unit,AlifeApi.DataAccess.ProjectContent.PurchaseOrder).latestOrder'></a>

`latestOrder` [AlifeApi.DataAccess.ProjectContent.PurchaseOrder](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.DataAccess.ProjectContent.PurchaseOrder 'AlifeApi.DataAccess.ProjectContent.PurchaseOrder')

最新有效訂單

#### Returns
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')  
銷售狀態

<a name='AlifeApi.BusinessRules.SalesModels.SalesService.GetPurchasedParkingSpacesString(AlifeApi.DataAccess.ProjectContent.PurchaseOrder,System.Collections.Generic.Dictionary_int,string_)'></a>

## SalesService.GetPurchasedParkingSpacesString(PurchaseOrder, Dictionary<int,string>) Method

輔助方法：將車位資訊轉換為字符串格式

```csharp
private string GetPurchasedParkingSpacesString(AlifeApi.DataAccess.ProjectContent.PurchaseOrder? latestOrder, System.Collections.Generic.Dictionary<int,string> parkingSpaces);
```
#### Parameters

<a name='AlifeApi.BusinessRules.SalesModels.SalesService.GetPurchasedParkingSpacesString(AlifeApi.DataAccess.ProjectContent.PurchaseOrder,System.Collections.Generic.Dictionary_int,string_).latestOrder'></a>

`latestOrder` [AlifeApi.DataAccess.ProjectContent.PurchaseOrder](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.DataAccess.ProjectContent.PurchaseOrder 'AlifeApi.DataAccess.ProjectContent.PurchaseOrder')

最新有效訂單

<a name='AlifeApi.BusinessRules.SalesModels.SalesService.GetPurchasedParkingSpacesString(AlifeApi.DataAccess.ProjectContent.PurchaseOrder,System.Collections.Generic.Dictionary_int,string_).parkingSpaces'></a>

`parkingSpaces` [System.Collections.Generic.Dictionary&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Collections.Generic.Dictionary-2 'System.Collections.Generic.Dictionary`2')[System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')[,](https://docs.microsoft.com/en-us/dotnet/api/System.Collections.Generic.Dictionary-2 'System.Collections.Generic.Dictionary`2')[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Collections.Generic.Dictionary-2 'System.Collections.Generic.Dictionary`2')

車位資訊字典

#### Returns
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')  
車位資訊字符串

<a name='AlifeApi.BusinessRules.SalesModels.SalesService.GetSalesControlAsync(AlifeApi.BusinessRules.SalesModels.SalesControlInput)'></a>

## SalesService.GetSalesControlAsync(SalesControlInput) Method

取得銷控表資料

```csharp
public System.Threading.Tasks.Task<AlifeApi.BusinessRules.SalesModels.BuildingSalesData> GetSalesControlAsync(AlifeApi.BusinessRules.SalesModels.SalesControlInput input);
```
#### Parameters

<a name='AlifeApi.BusinessRules.SalesModels.SalesService.GetSalesControlAsync(AlifeApi.BusinessRules.SalesModels.SalesControlInput).input'></a>

`input` [SalesControlInput](AlifeApi.BusinessRules.SalesModels.SalesControlInput.md 'AlifeApi.BusinessRules.SalesModels.SalesControlInput')

銷控表查詢條件

#### Returns
[System.Threading.Tasks.Task&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')[BuildingSalesData](AlifeApi.BusinessRules.SalesModels.BuildingSalesData.md 'AlifeApi.BusinessRules.SalesModels.BuildingSalesData')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')  
銷控表格式的資料

<a name='AlifeApi.BusinessRules.SalesModels.SalesService.GetSalesControlListAsync(global__SalesControlListInput)'></a>

## SalesService.GetSalesControlListAsync(SalesControlListInput) Method

取得銷控表列表格式資料（只返回有訂單的資料）

```csharp
public System.Threading.Tasks.Task<global::SalesControlListOutput> GetSalesControlListAsync(global::SalesControlListInput input);
```
#### Parameters

<a name='AlifeApi.BusinessRules.SalesModels.SalesService.GetSalesControlListAsync(global__SalesControlListInput).input'></a>

`input` [SalesControlListInput](https://docs.microsoft.com/en-us/dotnet/api/SalesControlListInput 'SalesControlListInput')

查詢條件

#### Returns
[System.Threading.Tasks.Task&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')[SalesControlListOutput](https://docs.microsoft.com/en-us/dotnet/api/SalesControlListOutput 'SalesControlListOutput')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')  
銷控表列表格式資料

<a name='AlifeApi.BusinessRules.SalesModels.SalesService.GetSitesSalesStatisticsAsync(string)'></a>

## SalesService.GetSitesSalesStatisticsAsync(string) Method

取得案場銷售統計

```csharp
public System.Threading.Tasks.Task<AlifeApi.BusinessRules.SalesModels.SalesSummaryData> GetSitesSalesStatisticsAsync(string siteCode);
```
#### Parameters

<a name='AlifeApi.BusinessRules.SalesModels.SalesService.GetSitesSalesStatisticsAsync(string).siteCode'></a>

`siteCode` [System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

案場編號

#### Returns
[System.Threading.Tasks.Task&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')[SalesSummaryData](AlifeApi.BusinessRules.SalesModels.SalesSummaryData.md 'AlifeApi.BusinessRules.SalesModels.SalesSummaryData')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')  
銷售統計資料

<a name='AlifeApi.BusinessRules.SalesModels.SalesService.LogPurchaseOrderHistoryAsync(int,string,string,string,string,string)'></a>

## SalesService.LogPurchaseOrderHistoryAsync(int, string, string, string, string, string) Method

記錄訂單歷史 (用於SalesService中的訂單相關歷史記錄)

```csharp
private System.Threading.Tasks.Task LogPurchaseOrderHistoryAsync(int orderId, string actionType, string? oldStatus, string? newStatus, string contentRecord, string userInfoId);
```
#### Parameters

<a name='AlifeApi.BusinessRules.SalesModels.SalesService.LogPurchaseOrderHistoryAsync(int,string,string,string,string,string).orderId'></a>

`orderId` [System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')

訂單ID

<a name='AlifeApi.BusinessRules.SalesModels.SalesService.LogPurchaseOrderHistoryAsync(int,string,string,string,string,string).actionType'></a>

`actionType` [System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

操作類型

<a name='AlifeApi.BusinessRules.SalesModels.SalesService.LogPurchaseOrderHistoryAsync(int,string,string,string,string,string).oldStatus'></a>

`oldStatus` [System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

舊狀態

<a name='AlifeApi.BusinessRules.SalesModels.SalesService.LogPurchaseOrderHistoryAsync(int,string,string,string,string,string).newStatus'></a>

`newStatus` [System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

新狀態

<a name='AlifeApi.BusinessRules.SalesModels.SalesService.LogPurchaseOrderHistoryAsync(int,string,string,string,string,string).contentRecord'></a>

`contentRecord` [System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

記錄內容

<a name='AlifeApi.BusinessRules.SalesModels.SalesService.LogPurchaseOrderHistoryAsync(int,string,string,string,string,string).userInfoId'></a>

`userInfoId` [System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

操作使用者ID

#### Returns
[System.Threading.Tasks.Task](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task 'System.Threading.Tasks.Task')

<a name='AlifeApi.BusinessRules.SalesModels.SalesService.LogUnitStatusChangeAsync(int,string,string,string,string)'></a>

## SalesService.LogUnitStatusChangeAsync(int, string, string, string, string) Method

記錄房屋單位狀態變更歷史

```csharp
private System.Threading.Tasks.Task LogUnitStatusChangeAsync(int unitId, string oldStatus, string newStatus, string userInfoId, string description);
```
#### Parameters

<a name='AlifeApi.BusinessRules.SalesModels.SalesService.LogUnitStatusChangeAsync(int,string,string,string,string).unitId'></a>

`unitId` [System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')

房屋單位ID

<a name='AlifeApi.BusinessRules.SalesModels.SalesService.LogUnitStatusChangeAsync(int,string,string,string,string).oldStatus'></a>

`oldStatus` [System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

舊狀態

<a name='AlifeApi.BusinessRules.SalesModels.SalesService.LogUnitStatusChangeAsync(int,string,string,string,string).newStatus'></a>

`newStatus` [System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

新狀態

<a name='AlifeApi.BusinessRules.SalesModels.SalesService.LogUnitStatusChangeAsync(int,string,string,string,string).userInfoId'></a>

`userInfoId` [System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

操作使用者ID

<a name='AlifeApi.BusinessRules.SalesModels.SalesService.LogUnitStatusChangeAsync(int,string,string,string,string).description'></a>

`description` [System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

變更描述

#### Returns
[System.Threading.Tasks.Task](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task 'System.Threading.Tasks.Task')

<a name='AlifeApi.BusinessRules.SalesModels.SalesService.UpdateSalesControlUnitAsync(int,dynamic,string)'></a>

## SalesService.UpdateSalesControlUnitAsync(int, dynamic, string) Method

銷控表單位完整狀態更新 (先更新訂單狀態，再更新Units狀態)

```csharp
public System.Threading.Tasks.Task UpdateSalesControlUnitAsync(int unitId, dynamic input, string userInfoId);
```
#### Parameters

<a name='AlifeApi.BusinessRules.SalesModels.SalesService.UpdateSalesControlUnitAsync(int,dynamic,string).unitId'></a>

`unitId` [System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')

房屋單位ID

<a name='AlifeApi.BusinessRules.SalesModels.SalesService.UpdateSalesControlUnitAsync(int,dynamic,string).input'></a>

`input` [dynamic](https://docs.microsoft.com/en-us/dotnet/csharp/programming-guide/types/using-type-dynamic 'https://docs.microsoft.com/en-us/dotnet/csharp/programming-guide/types/using-type-dynamic')

銷控表單位更新輸入

<a name='AlifeApi.BusinessRules.SalesModels.SalesService.UpdateSalesControlUnitAsync(int,dynamic,string).userInfoId'></a>

`userInfoId` [System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

執行更新的使用者ID

#### Returns
[System.Threading.Tasks.Task](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task 'System.Threading.Tasks.Task')

<a name='AlifeApi.BusinessRules.SalesModels.SalesService.UpdateUnitSalesStatusAsync(int,string,string)'></a>

## SalesService.UpdateUnitSalesStatusAsync(int, string, string) Method

更新房屋單位的銷售狀態  
此方法用於在創建或更新訂單後，同步更新相關房屋單位的狀態

```csharp
public System.Threading.Tasks.Task UpdateUnitSalesStatusAsync(int unitId, string newStatus, string userInfoId);
```
#### Parameters

<a name='AlifeApi.BusinessRules.SalesModels.SalesService.UpdateUnitSalesStatusAsync(int,string,string).unitId'></a>

`unitId` [System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')

房屋單位ID

<a name='AlifeApi.BusinessRules.SalesModels.SalesService.UpdateUnitSalesStatusAsync(int,string,string).newStatus'></a>

`newStatus` [System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

新的銷售狀態

<a name='AlifeApi.BusinessRules.SalesModels.SalesService.UpdateUnitSalesStatusAsync(int,string,string).userInfoId'></a>

`userInfoId` [System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

執行更新的使用者ID

#### Returns
[System.Threading.Tasks.Task](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task 'System.Threading.Tasks.Task')