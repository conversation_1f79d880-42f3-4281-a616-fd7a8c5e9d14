#### [AlifeApi.DataAccess](index.md 'index')
### [AlifeApi.DataAccess.ProjectContent](AlifeApi.DataAccess.ProjectContent.md 'AlifeApi.DataAccess.ProjectContent')

## SysRoleGroupPermission Class

角色群組權限表，用於儲存角色群組對系統功能的權限設置。

```csharp
public class SysRoleGroupPermission
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; SysRoleGroupPermission
### Properties

<a name='AlifeApi.DataAccess.ProjectContent.SysRoleGroupPermission.Crud'></a>

## SysRoleGroupPermission.Crud Property

CRUD 權限，儲存對功能的創建(C)、讀取(R)、更新(U)、刪除(D)權限設定。

```csharp
public string Crud { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContent.SysRoleGroupPermission.FuncId'></a>

## SysRoleGroupPermission.FuncId Property

功能項目識別編號，主鍵的一部分，對應 SYS_MenuFunc 表的 FuncId。

```csharp
public string FuncId { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContent.SysRoleGroupPermission.IsActive'></a>

## SysRoleGroupPermission.IsActive Property

是否啟用，true 表示啟用，false 表示停用，預設為 false。

```csharp
public bool IsActive { get; set; }
```

#### Property Value
[System.Boolean](https://docs.microsoft.com/en-us/dotnet/api/System.Boolean 'System.Boolean')

<a name='AlifeApi.DataAccess.ProjectContent.SysRoleGroupPermission.RoleGroupId'></a>

## SysRoleGroupPermission.RoleGroupId Property

角色群組識別編號，主鍵的一部分，對應 SYS_RoleGroup 表的 RoleGroupId。

```csharp
public string RoleGroupId { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContent.SysRoleGroupPermission.System'></a>

## SysRoleGroupPermission.System Property

系統名稱，主鍵的一部分，用於區分不同系統。

```csharp
public string System { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')