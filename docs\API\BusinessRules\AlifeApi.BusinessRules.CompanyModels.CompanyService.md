#### [AlifeApi.BusinessRules](index.md 'index')
### [AlifeApi.BusinessRules.CompanyModels](AlifeApi.BusinessRules.CompanyModels.md 'AlifeApi.BusinessRules.CompanyModels')

## CompanyService Class

公司服務

```csharp
public class CompanyService : AlifeApi.BusinessRules.Infrastructure.ServiceBase<AlifeApi.DataAccess.ProjectContent.alifeContext>
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; [AlifeApi.Common.InfrastructureBase](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.Common.InfrastructureBase 'AlifeApi.Common.InfrastructureBase') &#129106; [AlifeApi.BusinessRules.Infrastructure.ServiceBase&lt;](AlifeApi.BusinessRules.Infrastructure.ServiceBase_TDbContext_.md 'AlifeApi.BusinessRules.Infrastructure.ServiceBase<TDbContext>')[AlifeApi.DataAccess.ProjectContent.alifeContext](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.DataAccess.ProjectContent.alifeContext 'AlifeApi.DataAccess.ProjectContent.alifeContext')[&gt;](AlifeApi.BusinessRules.Infrastructure.ServiceBase_TDbContext_.md 'AlifeApi.BusinessRules.Infrastructure.ServiceBase<TDbContext>') &#129106; CompanyService
### Constructors

<a name='AlifeApi.BusinessRules.CompanyModels.CompanyService.CompanyService(System.IServiceProvider,AlifeApi.DataAccess.ProjectContent.alifeContext)'></a>

## CompanyService(IServiceProvider, alifeContext) Constructor

建構函數

```csharp
public CompanyService(System.IServiceProvider serviceProvider, AlifeApi.DataAccess.ProjectContent.alifeContext dbContext);
```
#### Parameters

<a name='AlifeApi.BusinessRules.CompanyModels.CompanyService.CompanyService(System.IServiceProvider,AlifeApi.DataAccess.ProjectContent.alifeContext).serviceProvider'></a>

`serviceProvider` [System.IServiceProvider](https://docs.microsoft.com/en-us/dotnet/api/System.IServiceProvider 'System.IServiceProvider')

<a name='AlifeApi.BusinessRules.CompanyModels.CompanyService.CompanyService(System.IServiceProvider,AlifeApi.DataAccess.ProjectContent.alifeContext).dbContext'></a>

`dbContext` [AlifeApi.DataAccess.ProjectContent.alifeContext](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.DataAccess.ProjectContent.alifeContext 'AlifeApi.DataAccess.ProjectContent.alifeContext')
### Methods

<a name='AlifeApi.BusinessRules.CompanyModels.CompanyService.GetCompanyDropdownListAsync()'></a>

## CompanyService.GetCompanyDropdownListAsync() Method

取得公司下拉選單

```csharp
public System.Threading.Tasks.Task<System.Collections.Generic.List<AlifeApi.BusinessRules.CompanyModels.CompanyDropdownOutput>> GetCompanyDropdownListAsync();
```

#### Returns
[System.Threading.Tasks.Task&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')[System.Collections.Generic.List&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Collections.Generic.List-1 'System.Collections.Generic.List`1')[CompanyDropdownOutput](AlifeApi.BusinessRules.CompanyModels.CompanyDropdownOutput.md 'AlifeApi.BusinessRules.CompanyModels.CompanyDropdownOutput')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Collections.Generic.List-1 'System.Collections.Generic.List`1')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')  
公司下拉選單列表