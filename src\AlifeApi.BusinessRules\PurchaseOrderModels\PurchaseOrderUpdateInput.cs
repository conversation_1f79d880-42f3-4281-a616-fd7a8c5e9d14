using System;
using System.ComponentModel.DataAnnotations;

namespace AlifeApi.BusinessRules.PurchaseOrderModels
{
    /// <summary>
    /// 買賣預定單更新輸入模型
    /// </summary>
    public class PurchaseOrderUpdateInput
    {
        [Required(ErrorMessage = "缺少訂單日期")]
        public DateOnly OrderDate { get; set; }

        public string? SalesAgencyName { get; set; }
        public string? SalesAgencyMobile { get; set; }
        public string? SalesAgencyLandline { get; set; }
        public string? SalesAgencyEmail { get; set; }

        public decimal? LandShareArea { get; set; }
        public decimal? PropertyPrice { get; set; }
        public decimal? ParkingSpacePrice { get; set; }

        [Required(ErrorMessage = "缺少交易總價")]
        public decimal TotalPrice { get; set; }

        public decimal? DepositAmount { get; set; }
        public decimal? DepositPaidAmount { get; set; }
        public string? DepositPaymentMethod { get; set; }
        public string? DepositPayee { get; set; }
        public DateTime? DepositDueDate { get; set; }
        public decimal? DepositBalanceAmount { get; set; }
        public DateTime? ContractSigningAppointment { get; set; }
        public decimal? ContractSigningAmount { get; set; }
        public bool? ConsentToDataUsage { get; set; }
        public string? OrderRemarks { get; set; }
        public string? SaleType { get; set; }

        [Required(ErrorMessage = "缺少訂單狀態")]
        public string Status { get; set; } = null!;

        public DateOnly? SaleDate { get; set; }
        public DateOnly? DepositFullPaidDate { get; set; }
        public DateOnly? ContractSignedDate { get; set; }
        public DateOnly? CancellationDate { get; set; }
        public DateOnly? HandoverDate { get; set; }
        public DateOnly? FinalPaymentDate { get; set; }
        public DateOnly? RequestDate { get; set; }
        public DateOnly? ReceiveDate { get; set; }
    }
} 
