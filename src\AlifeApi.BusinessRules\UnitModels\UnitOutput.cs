using System;

namespace AlifeApi.BusinessRules.UnitModels
{
    /// <summary>
    /// 房屋單位詳細輸出模型
    /// </summary>
    public class UnitOutput
    {
        public int UnitId { get; set; }
        public int FloorId { get; set; }
        public int BuildingId { get; set; }
        public string SiteCode { get; set; } = null!;
        public string UnitNumber { get; set; } = null!;
        public string UnitType { get; set; } = null!;
        public string Layout { get; set; }
        public string Orientation { get; set; }
        public decimal? MainArea { get; set; }
        public decimal? AuxiliaryArea { get; set; }
        public decimal? BalconyArea { get; set; }
        public decimal? AwningArea { get; set; }
        public decimal? SmallPublicArea { get; set; }
        public decimal? LargePublicArea { get; set; }
        public decimal? PublicAreaShare { get; set; }
        public decimal TotalArea { get; set; }
        public decimal? ListPrice { get; set; }
        public decimal? MinimumPrice { get; set; }
        public decimal? TransactionPrice { get; set; }
        public string Status { get; set; } = null!;
        public bool? IsPublicAreaIncluded { get; set; }
        public string Remarks { get; set; }
        public DateTime CreatedTime { get; set; }
        public DateTime UpdatedTime { get; set; }
        public string CreatedUserInfoId { get; set; } = null!;
        public string UpdatedUserInfoId { get; set; } = null!;
        public string CreatedUserName { get; set; }
        public string UpdatedUserName { get; set; }
        public string? FloorLabel { get; set; }
        public string? BuildingName { get; set; }
        public string? SiteName { get; set; }
    }
}
