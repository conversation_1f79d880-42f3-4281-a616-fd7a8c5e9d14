#### [AlifeApi.BusinessRules](index.md 'index')
### [AlifeApi.BusinessRules.FloorModels](AlifeApi.BusinessRules.FloorModels.md 'AlifeApi.BusinessRules.FloorModels')

## FloorQueryInput Class

樓層列表查詢輸入模型

```csharp
public class FloorQueryInput : AlifeApi.BusinessRules.Infrastructure.PagedListInput
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; [PagedListInput](AlifeApi.BusinessRules.Infrastructure.PagedListInput.md 'AlifeApi.BusinessRules.Infrastructure.PagedListInput') &#129106; FloorQueryInput
### Properties

<a name='AlifeApi.BusinessRules.FloorModels.FloorQueryInput.FloorType'></a>

## FloorQueryInput.FloorType Property

樓層類型 (篩選)

```csharp
public string? FloorType { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')