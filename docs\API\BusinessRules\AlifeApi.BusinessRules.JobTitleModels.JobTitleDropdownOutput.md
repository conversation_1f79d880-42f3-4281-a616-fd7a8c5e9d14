#### [AlifeApi.BusinessRules](index.md 'index')
### [AlifeApi.BusinessRules.JobTitleModels](AlifeApi.BusinessRules.JobTitleModels.md 'AlifeApi.BusinessRules.JobTitleModels')

## JobTitleDropdownOutput Class

職位下拉選單輸出資料

```csharp
public class JobTitleDropdownOutput
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; JobTitleDropdownOutput
### Properties

<a name='AlifeApi.BusinessRules.JobTitleModels.JobTitleDropdownOutput.Name'></a>

## JobTitleDropdownOutput.Name Property

名稱

```csharp
public string Name { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.JobTitleModels.JobTitleDropdownOutput.Value'></a>

## JobTitleDropdownOutput.Value Property

值

```csharp
public string Value { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')