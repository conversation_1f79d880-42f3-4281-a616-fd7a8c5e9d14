﻿using System;
using System.Collections.Generic;

namespace AlifeApi.DataAccess.ProjectContent
{
    /// <summary>
    /// 公司資料表，用於儲存公司基本資訊。
    /// </summary>
    public partial class Company
    {
        public Company()
        {
            Departments = new HashSet<Department>();
        }

        /// <summary>
        /// 公司編號，主鍵，用於唯一識別公司。
        /// </summary>
        public string CompanyId { get; set; }
        /// <summary>
        /// 公司名稱。
        /// </summary>
        public string Name { get; set; }
        /// <summary>
        /// 創建時間，記錄資料創建的時間。
        /// </summary>
        public DateTime CreatedTime { get; set; }
        /// <summary>
        /// 更新時間，記錄資料最後更新的時間。
        /// </summary>
        public DateTime UpdatedTime { get; set; }
        /// <summary>
        /// 建立者，記錄創建此資料的員工編號。
        /// </summary>
        public string CreatedUserInfoId { get; set; }
        /// <summary>
        /// 更新者，記錄最後更新此資料的員工編號。
        /// </summary>
        public string UpdatedUserInfoId { get; set; }

        public virtual ICollection<Department> Departments { get; set; }
    }
}
