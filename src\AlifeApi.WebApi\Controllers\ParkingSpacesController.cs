using System;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using AlifeApi.BusinessRules.ParkingSpaceModels;
using AlifeApi.BusinessRules.Infrastructure;

namespace AlifeApi.WebApi.Controllers
{
    /// <summary>
    /// 停車位管理
    /// </summary>
    public class ParkingSpacesController : AuthenticatedController
    {
        private readonly ParkingSpaceService _parkingSpaceService;

        public ParkingSpacesController(ParkingSpaceService parkingSpaceService)
        {
            _parkingSpaceService = parkingSpaceService ?? throw new ArgumentNullException(nameof(parkingSpaceService));
        }

        /// <summary>
        /// 取得停車位列表 (分頁)
        /// </summary>
        /// <param name="input">查詢條件</param>
        /// <returns>分頁的停車位列表</returns>
        [HttpPost]
        public async Task<ActionResult<PagedListOutput<ParkingSpaceListOutput>>> GetParkingSpaces([FromBody] ParkingSpaceQueryInput input)
        {
            var result = await _parkingSpaceService.GetParkingSpaceListAsync(input);
            return Ok(result);
        }

        /// <summary>
        /// 根據停車位ID取得詳細資訊
        /// </summary>
        /// <param name="parkingSpaceId">停車位ID</param>
        /// <returns>停車位詳細資訊</returns>
        [HttpGet("{parkingSpaceId}")]
        public async Task<ActionResult<ParkingSpaceOutput>> GetParkingSpace(int parkingSpaceId)
        {
            var result = await _parkingSpaceService.GetParkingSpaceByIdAsync(parkingSpaceId);
            if (result == null)
                return NotFound();
            return Ok(result);
        }

        /// <summary>
        /// 新增停車位
        /// </summary>
        /// <param name="input">停車位建立輸入資料</param>
        /// <returns>新增的停車位ID</returns>
        [HttpPost]
        public async Task<ActionResult<int>> CreateParkingSpace([FromBody] ParkingSpaceCreateInput input)
        {
            try
            {
                var id = await _parkingSpaceService.CreateParkingSpaceAsync(input);
                return CreatedAtAction(nameof(GetParkingSpace), new { parkingSpaceId = id }, new { ParkingSpaceId = id });
            }
            catch (Exception ex)
            {
                return BadRequest(new { message = ex.Message });
            }
        }

        /// <summary>
        /// 更新停車位
        /// </summary>
        /// <param name="parkingSpaceId">停車位ID</param>
        /// <param name="input">停車位更新輸入資料</param>
        /// <returns>NoContent</returns>
        [HttpPut("{parkingSpaceId}")]
        public async Task<ActionResult> UpdateParkingSpace(int parkingSpaceId, [FromBody] ParkingSpaceUpdateInput input)
        {
            try
            {
                await _parkingSpaceService.UpdateParkingSpaceAsync(parkingSpaceId, input);
                return NoContent();
            }
            catch (Exception ex)
            {
                return BadRequest(new { message = ex.Message });
            }
        }

        /// <summary>
        /// 刪除停車位
        /// </summary>
        /// <param name="parkingSpaceId">停車位ID</param>
        /// <returns>NoContent</returns>
        [HttpDelete("{parkingSpaceId}")]
        public async Task<ActionResult> DeleteParkingSpace(int parkingSpaceId)
        {
            try
            {
                await _parkingSpaceService.DeleteParkingSpaceAsync(parkingSpaceId);
                return NoContent();
            }
            catch (Exception ex)
            {
                return BadRequest(new { message = ex.Message });
            }
        }
    }
} 
