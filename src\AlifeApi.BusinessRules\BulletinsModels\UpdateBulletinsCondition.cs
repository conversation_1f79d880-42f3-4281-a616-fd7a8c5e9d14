﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AlifeApi.BusinessRules.BulletinsModels
{
    public class UpdateBulletinsCondition:BulletinsCondition
    {
        /// <summary>
        /// 公告標題
        /// </summary>
        [MaxLength(200, ErrorMessage = "標題長度不能超過200個字符")]
        public string Title { get; set; }
        /// <summary>
        /// 公告內容
        /// </summary>
        [MaxLength(1000, ErrorMessage = "內容長度不能超過1000個字符")]
        public string Content { get; set; }
        /// <summary>
        /// 公告是否至頂
        /// </summary>
        public bool? IsTop { get; set; }
        /// <summary>
        /// 公告開始日期
        /// </summary>
        public DateTime? PostDateFrom { get; set; }
        /// <summary>
        /// 公告結束日期
        /// </summary>
        public DateTime? PostDateTo { get; set; }
        /// <summary>
        /// 公告附件清單
        /// </summary>
        public List<AttachListItem> AttachList { get; set; } = new List<AttachListItem>();
    }
}
