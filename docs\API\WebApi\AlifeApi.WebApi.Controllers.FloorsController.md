#### [<PERSON>feApi.WebApi](index.md 'index')
### [AlifeApi.WebApi.Controllers](AlifeApi.WebApi.Controllers.md 'AlifeApi.WebApi.Controllers')

## FloorsController Class

樓層管理

```csharp
public class FloorsController : AlifeApi.WebApi.Controllers.AuthenticatedController
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; [Microsoft.AspNetCore.Mvc.ControllerBase](https://docs.microsoft.com/en-us/dotnet/api/Microsoft.AspNetCore.Mvc.ControllerBase 'Microsoft.AspNetCore.Mvc.ControllerBase') &#129106; [Microsoft.AspNetCore.Mvc.Controller](https://docs.microsoft.com/en-us/dotnet/api/Microsoft.AspNetCore.Mvc.Controller 'Microsoft.AspNetCore.Mvc.Controller') &#129106; [AlifeApi.WebApi.Controllers.BasicController](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.WebApi.Controllers.BasicController 'AlifeApi.WebApi.Controllers.BasicController') &#129106; [AlifeApi.WebApi.Controllers.AuthenticatedController](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.WebApi.Controllers.AuthenticatedController 'AlifeApi.WebApi.Controllers.AuthenticatedController') &#129106; FloorsController
### Methods

<a name='AlifeApi.WebApi.Controllers.FloorsController.CreateFloor(AlifeApi.BusinessRules.FloorModels.FloorCreateInput)'></a>

## FloorsController.CreateFloor(FloorCreateInput) Method

新增樓層

```csharp
public System.Threading.Tasks.Task<Microsoft.AspNetCore.Mvc.ActionResult<int>> CreateFloor(AlifeApi.BusinessRules.FloorModels.FloorCreateInput input);
```
#### Parameters

<a name='AlifeApi.WebApi.Controllers.FloorsController.CreateFloor(AlifeApi.BusinessRules.FloorModels.FloorCreateInput).input'></a>

`input` [AlifeApi.BusinessRules.FloorModels.FloorCreateInput](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.BusinessRules.FloorModels.FloorCreateInput 'AlifeApi.BusinessRules.FloorModels.FloorCreateInput')

樓層建立輸入資料

#### Returns
[System.Threading.Tasks.Task&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')[Microsoft.AspNetCore.Mvc.ActionResult&lt;](https://docs.microsoft.com/en-us/dotnet/api/Microsoft.AspNetCore.Mvc.ActionResult-1 'Microsoft.AspNetCore.Mvc.ActionResult`1')[System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/Microsoft.AspNetCore.Mvc.ActionResult-1 'Microsoft.AspNetCore.Mvc.ActionResult`1')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')  
新增的樓層ID

<a name='AlifeApi.WebApi.Controllers.FloorsController.DeleteFloor(int)'></a>

## FloorsController.DeleteFloor(int) Method

刪除樓層

```csharp
public System.Threading.Tasks.Task<Microsoft.AspNetCore.Mvc.ActionResult> DeleteFloor(int floorId);
```
#### Parameters

<a name='AlifeApi.WebApi.Controllers.FloorsController.DeleteFloor(int).floorId'></a>

`floorId` [System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')

樓層ID

#### Returns
[System.Threading.Tasks.Task&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')[Microsoft.AspNetCore.Mvc.ActionResult](https://docs.microsoft.com/en-us/dotnet/api/Microsoft.AspNetCore.Mvc.ActionResult 'Microsoft.AspNetCore.Mvc.ActionResult')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')  
NoContent

<a name='AlifeApi.WebApi.Controllers.FloorsController.GetFloor(int)'></a>

## FloorsController.GetFloor(int) Method

根據樓層ID取得詳細資訊

```csharp
public System.Threading.Tasks.Task<Microsoft.AspNetCore.Mvc.ActionResult<AlifeApi.BusinessRules.FloorModels.FloorOutput>> GetFloor(int floorId);
```
#### Parameters

<a name='AlifeApi.WebApi.Controllers.FloorsController.GetFloor(int).floorId'></a>

`floorId` [System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')

樓層ID

#### Returns
[System.Threading.Tasks.Task&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')[Microsoft.AspNetCore.Mvc.ActionResult&lt;](https://docs.microsoft.com/en-us/dotnet/api/Microsoft.AspNetCore.Mvc.ActionResult-1 'Microsoft.AspNetCore.Mvc.ActionResult`1')[AlifeApi.BusinessRules.FloorModels.FloorOutput](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.BusinessRules.FloorModels.FloorOutput 'AlifeApi.BusinessRules.FloorModels.FloorOutput')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/Microsoft.AspNetCore.Mvc.ActionResult-1 'Microsoft.AspNetCore.Mvc.ActionResult`1')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')  
樓層詳細資訊

<a name='AlifeApi.WebApi.Controllers.FloorsController.GetFloors(AlifeApi.BusinessRules.FloorModels.FloorQueryInput)'></a>

## FloorsController.GetFloors(FloorQueryInput) Method

取得樓層列表 (分頁)

```csharp
public System.Threading.Tasks.Task<Microsoft.AspNetCore.Mvc.ActionResult<AlifeApi.BusinessRules.Infrastructure.PagedListOutput<AlifeApi.BusinessRules.FloorModels.FloorListOutput>>> GetFloors(AlifeApi.BusinessRules.FloorModels.FloorQueryInput input);
```
#### Parameters

<a name='AlifeApi.WebApi.Controllers.FloorsController.GetFloors(AlifeApi.BusinessRules.FloorModels.FloorQueryInput).input'></a>

`input` [AlifeApi.BusinessRules.FloorModels.FloorQueryInput](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.BusinessRules.FloorModels.FloorQueryInput 'AlifeApi.BusinessRules.FloorModels.FloorQueryInput')

查詢條件

#### Returns
[System.Threading.Tasks.Task&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')[Microsoft.AspNetCore.Mvc.ActionResult&lt;](https://docs.microsoft.com/en-us/dotnet/api/Microsoft.AspNetCore.Mvc.ActionResult-1 'Microsoft.AspNetCore.Mvc.ActionResult`1')[AlifeApi.BusinessRules.Infrastructure.PagedListOutput&lt;](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.BusinessRules.Infrastructure.PagedListOutput-1 'AlifeApi.BusinessRules.Infrastructure.PagedListOutput`1')[AlifeApi.BusinessRules.FloorModels.FloorListOutput](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.BusinessRules.FloorModels.FloorListOutput 'AlifeApi.BusinessRules.FloorModels.FloorListOutput')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.BusinessRules.Infrastructure.PagedListOutput-1 'AlifeApi.BusinessRules.Infrastructure.PagedListOutput`1')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/Microsoft.AspNetCore.Mvc.ActionResult-1 'Microsoft.AspNetCore.Mvc.ActionResult`1')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')  
分頁的樓層列表

<a name='AlifeApi.WebApi.Controllers.FloorsController.UpdateFloor(int,AlifeApi.BusinessRules.FloorModels.FloorUpdateInput)'></a>

## FloorsController.UpdateFloor(int, FloorUpdateInput) Method

更新樓層

```csharp
public System.Threading.Tasks.Task<Microsoft.AspNetCore.Mvc.ActionResult> UpdateFloor(int floorId, AlifeApi.BusinessRules.FloorModels.FloorUpdateInput input);
```
#### Parameters

<a name='AlifeApi.WebApi.Controllers.FloorsController.UpdateFloor(int,AlifeApi.BusinessRules.FloorModels.FloorUpdateInput).floorId'></a>

`floorId` [System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')

樓層ID

<a name='AlifeApi.WebApi.Controllers.FloorsController.UpdateFloor(int,AlifeApi.BusinessRules.FloorModels.FloorUpdateInput).input'></a>

`input` [AlifeApi.BusinessRules.FloorModels.FloorUpdateInput](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.BusinessRules.FloorModels.FloorUpdateInput 'AlifeApi.BusinessRules.FloorModels.FloorUpdateInput')

樓層更新輸入資料

#### Returns
[System.Threading.Tasks.Task&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')[Microsoft.AspNetCore.Mvc.ActionResult](https://docs.microsoft.com/en-us/dotnet/api/Microsoft.AspNetCore.Mvc.ActionResult 'Microsoft.AspNetCore.Mvc.ActionResult')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')  
NoContent