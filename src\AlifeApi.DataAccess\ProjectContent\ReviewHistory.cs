﻿using System;
using System.Collections.Generic;

namespace AlifeApi.DataAccess.ProjectContent
{
    /// <summary>
    /// 審核歷史記錄表，用於儲存審核任務的歷史操作記錄。
    /// </summary>
    public partial class ReviewHistory
    {
        /// <summary>
        /// 歷史記錄流水號，主鍵，自動遞增，用於唯一識別歷史記錄。
        /// </summary>
        public int HistoryId { get; set; }
        /// <summary>
        /// 任務流水號，對應 ReviewTasks 表的 TaskId。
        /// </summary>
        public int? TaskId { get; set; }
        /// <summary>
        /// 步驟流水號，對應 ReviewSteps 表的 StepId。
        /// </summary>
        public int? StepId { get; set; }
        /// <summary>
        /// 使用者編號，對應 UserInfo 表的 UserInfoId。
        /// </summary>
        public string UserInfoId { get; set; }
        /// <summary>
        /// 操作類型，可選值為 approve（批准）、reject（拒絕）、return（退回）、comment（評論）。
        /// </summary>
        public string Action { get; set; }
        /// <summary>
        /// 操作評論，提供操作的詳細說明或意見。
        /// </summary>
        public string Comment { get; set; }
        /// <summary>
        /// 操作時間，記錄操作發生的時間，預設為當前時間。
        /// </summary>
        public DateTime? Timestamp { get; set; }
    }
}
