#### [AlifeApi.DataAccess](index.md 'index')
### [AlifeApi.DataAccess.ProjectContent](AlifeApi.DataAccess.ProjectContent.md 'AlifeApi.DataAccess.ProjectContent')

## SysCode Class

系統代碼表，用於儲存系統中的標準化代碼資訊，例如狀態碼、類型碼等。

```csharp
public class SysCode
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; SysCode
### Properties

<a name='AlifeApi.DataAccess.ProjectContent.SysCode.Code'></a>

## SysCode.Code Property

代碼值，主鍵的一部分，用於唯一識別具體代碼。

```csharp
public string Code { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='<PERSON>feApi.DataAccess.ProjectContent.SysCode.CodeDesc'></a>

## SysCode.CodeDesc Property

代碼描述，提供代碼的詳細解釋。

```csharp
public string CodeDesc { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContent.SysCode.CodeOrder'></a>

## SysCode.CodeOrder Property

排序序號，用於控制代碼的顯示順序。

```csharp
public System.Nullable<short> CodeOrder { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.Int16](https://docs.microsoft.com/en-us/dotnet/api/System.Int16 'System.Int16')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

<a name='AlifeApi.DataAccess.ProjectContent.SysCode.CreatedTime'></a>

## SysCode.CreatedTime Property

創建時間，記錄資料創建的時間。

```csharp
public System.DateTime CreatedTime { get; set; }
```

#### Property Value
[System.DateTime](https://docs.microsoft.com/en-us/dotnet/api/System.DateTime 'System.DateTime')

<a name='AlifeApi.DataAccess.ProjectContent.SysCode.CreatedUserInfoId'></a>

## SysCode.CreatedUserInfoId Property

建立者，記錄創建此資料的員工編號。

```csharp
public string CreatedUserInfoId { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContent.SysCode.IsActive'></a>

## SysCode.IsActive Property

是否啟用，true 表示啟用，false 表示停用，預設為 true。

```csharp
public System.Nullable<bool> IsActive { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.Boolean](https://docs.microsoft.com/en-us/dotnet/api/System.Boolean 'System.Boolean')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

<a name='AlifeApi.DataAccess.ProjectContent.SysCode.ParentCode'></a>

## SysCode.ParentCode Property

上層代碼，可為 NULL，表示代碼的層級關係。

```csharp
public string ParentCode { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContent.SysCode.Type'></a>

## SysCode.Type Property

代碼類型，主鍵的一部分，用於區分不同的代碼類別。

```csharp
public string Type { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContent.SysCode.UpdatedTime'></a>

## SysCode.UpdatedTime Property

更新時間，記錄資料最後更新的時間。

```csharp
public System.DateTime UpdatedTime { get; set; }
```

#### Property Value
[System.DateTime](https://docs.microsoft.com/en-us/dotnet/api/System.DateTime 'System.DateTime')

<a name='AlifeApi.DataAccess.ProjectContent.SysCode.UpdatedUserInfoId'></a>

## SysCode.UpdatedUserInfoId Property

更新者，記錄最後更新此資料的員工編號。

```csharp
public string UpdatedUserInfoId { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')