#### [AlifeApi.BusinessRules](index.md 'index')
### [AlifeApi.BusinessRules.DepartmentModels](AlifeApi.BusinessRules.DepartmentModels.md 'AlifeApi.BusinessRules.DepartmentModels')

## DepartmentOutput Class

部門資料輸出

```csharp
public class DepartmentOutput
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; DepartmentOutput
### Properties

<a name='AlifeApi.BusinessRules.DepartmentModels.DepartmentOutput.CompanyId'></a>

## DepartmentOutput.CompanyId Property

公司ID

```csharp
public string CompanyId { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.DepartmentModels.DepartmentOutput.CompanyName'></a>

## DepartmentOutput.CompanyName Property

公司名稱

```csharp
public string CompanyName { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.DepartmentModels.DepartmentOutput.CreatedTime'></a>

## DepartmentOutput.CreatedTime Property

建立時間

```csharp
public System.DateTime CreatedTime { get; set; }
```

#### Property Value
[System.DateTime](https://docs.microsoft.com/en-us/dotnet/api/System.DateTime 'System.DateTime')

<a name='AlifeApi.BusinessRules.DepartmentModels.DepartmentOutput.CreatedUserId'></a>

## DepartmentOutput.CreatedUserId Property

建立者ID

```csharp
public string CreatedUserId { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.DepartmentModels.DepartmentOutput.CreatedUserName'></a>

## DepartmentOutput.CreatedUserName Property

建立者名稱

```csharp
public string CreatedUserName { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.DepartmentModels.DepartmentOutput.DepartmentId'></a>

## DepartmentOutput.DepartmentId Property

部門ID

```csharp
public string DepartmentId { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.DepartmentModels.DepartmentOutput.Name'></a>

## DepartmentOutput.Name Property

部門名稱

```csharp
public string Name { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.DepartmentModels.DepartmentOutput.UpdatedTime'></a>

## DepartmentOutput.UpdatedTime Property

更新時間

```csharp
public System.DateTime UpdatedTime { get; set; }
```

#### Property Value
[System.DateTime](https://docs.microsoft.com/en-us/dotnet/api/System.DateTime 'System.DateTime')

<a name='AlifeApi.BusinessRules.DepartmentModels.DepartmentOutput.UpdatedUserId'></a>

## DepartmentOutput.UpdatedUserId Property

更新者ID

```csharp
public string UpdatedUserId { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.DepartmentModels.DepartmentOutput.UpdatedUserName'></a>

## DepartmentOutput.UpdatedUserName Property

更新者名稱

```csharp
public string UpdatedUserName { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')