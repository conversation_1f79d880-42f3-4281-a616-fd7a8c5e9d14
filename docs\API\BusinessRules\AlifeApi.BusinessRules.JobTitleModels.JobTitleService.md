#### [AlifeApi.BusinessRules](index.md 'index')
### [AlifeApi.BusinessRules.JobTitleModels](AlifeApi.BusinessRules.JobTitleModels.md 'AlifeApi.BusinessRules.JobTitleModels')

## JobTitleService Class

職位服務

```csharp
public class JobTitleService : AlifeApi.BusinessRules.Infrastructure.ServiceBase<AlifeApi.DataAccess.ProjectContent.alifeContext>
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; [AlifeApi.Common.InfrastructureBase](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.Common.InfrastructureBase 'AlifeApi.Common.InfrastructureBase') &#129106; [AlifeApi.BusinessRules.Infrastructure.ServiceBase&lt;](AlifeApi.BusinessRules.Infrastructure.ServiceBase_TDbContext_.md 'AlifeApi.BusinessRules.Infrastructure.ServiceBase<TDbContext>')[AlifeApi.DataAccess.ProjectContent.alifeContext](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.DataAccess.ProjectContent.alifeContext 'AlifeApi.DataAccess.ProjectContent.alifeContext')[&gt;](AlifeApi.BusinessRules.Infrastructure.ServiceBase_TDbContext_.md 'AlifeApi.BusinessRules.Infrastructure.ServiceBase<TDbContext>') &#129106; JobTitleService
### Constructors

<a name='AlifeApi.BusinessRules.JobTitleModels.JobTitleService.JobTitleService(System.IServiceProvider,AlifeApi.DataAccess.ProjectContent.alifeContext)'></a>

## JobTitleService(IServiceProvider, alifeContext) Constructor

建構函數

```csharp
public JobTitleService(System.IServiceProvider serviceProvider, AlifeApi.DataAccess.ProjectContent.alifeContext dbContext);
```
#### Parameters

<a name='AlifeApi.BusinessRules.JobTitleModels.JobTitleService.JobTitleService(System.IServiceProvider,AlifeApi.DataAccess.ProjectContent.alifeContext).serviceProvider'></a>

`serviceProvider` [System.IServiceProvider](https://docs.microsoft.com/en-us/dotnet/api/System.IServiceProvider 'System.IServiceProvider')

<a name='AlifeApi.BusinessRules.JobTitleModels.JobTitleService.JobTitleService(System.IServiceProvider,AlifeApi.DataAccess.ProjectContent.alifeContext).dbContext'></a>

`dbContext` [AlifeApi.DataAccess.ProjectContent.alifeContext](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.DataAccess.ProjectContent.alifeContext 'AlifeApi.DataAccess.ProjectContent.alifeContext')
### Methods

<a name='AlifeApi.BusinessRules.JobTitleModels.JobTitleService.GetJobTitleDropdownListAsync(AlifeApi.BusinessRules.JobTitleModels.JobTitleDropdownInput)'></a>

## JobTitleService.GetJobTitleDropdownListAsync(JobTitleDropdownInput) Method

取得職位下拉選單

```csharp
public System.Threading.Tasks.Task<System.Collections.Generic.List<AlifeApi.BusinessRules.JobTitleModels.JobTitleDropdownOutput>> GetJobTitleDropdownListAsync(AlifeApi.BusinessRules.JobTitleModels.JobTitleDropdownInput input);
```
#### Parameters

<a name='AlifeApi.BusinessRules.JobTitleModels.JobTitleService.GetJobTitleDropdownListAsync(AlifeApi.BusinessRules.JobTitleModels.JobTitleDropdownInput).input'></a>

`input` [JobTitleDropdownInput](AlifeApi.BusinessRules.JobTitleModels.JobTitleDropdownInput.md 'AlifeApi.BusinessRules.JobTitleModels.JobTitleDropdownInput')

查詢條件

#### Returns
[System.Threading.Tasks.Task&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')[System.Collections.Generic.List&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Collections.Generic.List-1 'System.Collections.Generic.List`1')[JobTitleDropdownOutput](AlifeApi.BusinessRules.JobTitleModels.JobTitleDropdownOutput.md 'AlifeApi.BusinessRules.JobTitleModels.JobTitleDropdownOutput')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Collections.Generic.List-1 'System.Collections.Generic.List`1')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')  
職位下拉選單列表