#### [AlifeApi.WebApi](index.md 'index')
### [AlifeApi.WebApi.Controllers](AlifeApi.WebApi.Controllers.md 'AlifeApi.WebApi.Controllers')

## ReviewTaskController Class

審核流程控制器

```csharp
public class ReviewTaskController : AlifeApi.WebApi.Controllers.AuthenticatedController
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; [Microsoft.AspNetCore.Mvc.ControllerBase](https://docs.microsoft.com/en-us/dotnet/api/Microsoft.AspNetCore.Mvc.ControllerBase 'Microsoft.AspNetCore.Mvc.ControllerBase') &#129106; [Microsoft.AspNetCore.Mvc.Controller](https://docs.microsoft.com/en-us/dotnet/api/Microsoft.AspNetCore.Mvc.Controller 'Microsoft.AspNetCore.Mvc.Controller') &#129106; [AlifeApi.WebApi.Controllers.BasicController](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.WebApi.Controllers.BasicController 'AlifeApi.WebApi.Controllers.BasicController') &#129106; [AlifeApi.WebApi.Controllers.AuthenticatedController](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.WebApi.Controllers.AuthenticatedController 'AlifeApi.WebApi.Controllers.AuthenticatedController') &#129106; ReviewTaskController
### Constructors

<a name='AlifeApi.WebApi.Controllers.ReviewTaskController.ReviewTaskController(AlifeApi.BusinessRules.ReviewModels.ReviewTaskService)'></a>

## ReviewTaskController(ReviewTaskService) Constructor

建構函數

```csharp
public ReviewTaskController(AlifeApi.BusinessRules.ReviewModels.ReviewTaskService reviewTaskService);
```
#### Parameters

<a name='AlifeApi.WebApi.Controllers.ReviewTaskController.ReviewTaskController(AlifeApi.BusinessRules.ReviewModels.ReviewTaskService).reviewTaskService'></a>

`reviewTaskService` [AlifeApi.BusinessRules.ReviewModels.ReviewTaskService](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.BusinessRules.ReviewModels.ReviewTaskService 'AlifeApi.BusinessRules.ReviewModels.ReviewTaskService')

審核流程服務
### Methods

<a name='AlifeApi.WebApi.Controllers.ReviewTaskController.AddReviewHistory(AlifeApi.BusinessRules.ReviewModels.ReviewHistoryInput)'></a>

## ReviewTaskController.AddReviewHistory(ReviewHistoryInput) Method

添加審核歷史記錄

```csharp
public System.Threading.Tasks.Task<Microsoft.AspNetCore.Mvc.ActionResult> AddReviewHistory(AlifeApi.BusinessRules.ReviewModels.ReviewHistoryInput input);
```
#### Parameters

<a name='AlifeApi.WebApi.Controllers.ReviewTaskController.AddReviewHistory(AlifeApi.BusinessRules.ReviewModels.ReviewHistoryInput).input'></a>

`input` [AlifeApi.BusinessRules.ReviewModels.ReviewHistoryInput](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.BusinessRules.ReviewModels.ReviewHistoryInput 'AlifeApi.BusinessRules.ReviewModels.ReviewHistoryInput')

審核歷史記錄輸入資料

#### Returns
[System.Threading.Tasks.Task&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')[Microsoft.AspNetCore.Mvc.ActionResult](https://docs.microsoft.com/en-us/dotnet/api/Microsoft.AspNetCore.Mvc.ActionResult 'Microsoft.AspNetCore.Mvc.ActionResult')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')

<a name='AlifeApi.WebApi.Controllers.ReviewTaskController.CreateReviewTask(AlifeApi.BusinessRules.ReviewModels.ReviewTaskInput)'></a>

## ReviewTaskController.CreateReviewTask(ReviewTaskInput) Method

建立審核流程

```csharp
public System.Threading.Tasks.Task<Microsoft.AspNetCore.Mvc.ActionResult<int>> CreateReviewTask(AlifeApi.BusinessRules.ReviewModels.ReviewTaskInput input);
```
#### Parameters

<a name='AlifeApi.WebApi.Controllers.ReviewTaskController.CreateReviewTask(AlifeApi.BusinessRules.ReviewModels.ReviewTaskInput).input'></a>

`input` [AlifeApi.BusinessRules.ReviewModels.ReviewTaskInput](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.BusinessRules.ReviewModels.ReviewTaskInput 'AlifeApi.BusinessRules.ReviewModels.ReviewTaskInput')

審核流程輸入資料

#### Returns
[System.Threading.Tasks.Task&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')[Microsoft.AspNetCore.Mvc.ActionResult&lt;](https://docs.microsoft.com/en-us/dotnet/api/Microsoft.AspNetCore.Mvc.ActionResult-1 'Microsoft.AspNetCore.Mvc.ActionResult`1')[System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/Microsoft.AspNetCore.Mvc.ActionResult-1 'Microsoft.AspNetCore.Mvc.ActionResult`1')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')  
審核流程ID

<a name='AlifeApi.WebApi.Controllers.ReviewTaskController.DeleteReviewTask(int)'></a>

## ReviewTaskController.DeleteReviewTask(int) Method

刪除審核流程

```csharp
public System.Threading.Tasks.Task<Microsoft.AspNetCore.Mvc.ActionResult> DeleteReviewTask(int taskId);
```
#### Parameters

<a name='AlifeApi.WebApi.Controllers.ReviewTaskController.DeleteReviewTask(int).taskId'></a>

`taskId` [System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')

審核流程ID

#### Returns
[System.Threading.Tasks.Task&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')[Microsoft.AspNetCore.Mvc.ActionResult](https://docs.microsoft.com/en-us/dotnet/api/Microsoft.AspNetCore.Mvc.ActionResult 'Microsoft.AspNetCore.Mvc.ActionResult')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')

<a name='AlifeApi.WebApi.Controllers.ReviewTaskController.GetNextStep(int)'></a>

## ReviewTaskController.GetNextStep(int) Method

獲取下一步驟資訊

```csharp
public System.Threading.Tasks.Task<Microsoft.AspNetCore.Mvc.ActionResult<AlifeApi.BusinessRules.ReviewModels.ReviewNextStepOutput>> GetNextStep(int taskId);
```
#### Parameters

<a name='AlifeApi.WebApi.Controllers.ReviewTaskController.GetNextStep(int).taskId'></a>

`taskId` [System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')

審核流程ID

#### Returns
[System.Threading.Tasks.Task&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')[Microsoft.AspNetCore.Mvc.ActionResult&lt;](https://docs.microsoft.com/en-us/dotnet/api/Microsoft.AspNetCore.Mvc.ActionResult-1 'Microsoft.AspNetCore.Mvc.ActionResult`1')[AlifeApi.BusinessRules.ReviewModels.ReviewNextStepOutput](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.BusinessRules.ReviewModels.ReviewNextStepOutput 'AlifeApi.BusinessRules.ReviewModels.ReviewNextStepOutput')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/Microsoft.AspNetCore.Mvc.ActionResult-1 'Microsoft.AspNetCore.Mvc.ActionResult`1')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')  
下一步驟資訊

<a name='AlifeApi.WebApi.Controllers.ReviewTaskController.GetReviewHistory(int)'></a>

## ReviewTaskController.GetReviewHistory(int) Method

取得審核流程歷史記錄

```csharp
public System.Threading.Tasks.Task<Microsoft.AspNetCore.Mvc.ActionResult<System.Collections.Generic.List<AlifeApi.BusinessRules.ReviewModels.ReviewHistoryOutput>>> GetReviewHistory(int taskId);
```
#### Parameters

<a name='AlifeApi.WebApi.Controllers.ReviewTaskController.GetReviewHistory(int).taskId'></a>

`taskId` [System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')

審核流程ID

#### Returns
[System.Threading.Tasks.Task&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')[Microsoft.AspNetCore.Mvc.ActionResult&lt;](https://docs.microsoft.com/en-us/dotnet/api/Microsoft.AspNetCore.Mvc.ActionResult-1 'Microsoft.AspNetCore.Mvc.ActionResult`1')[System.Collections.Generic.List&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Collections.Generic.List-1 'System.Collections.Generic.List`1')[AlifeApi.BusinessRules.ReviewModels.ReviewHistoryOutput](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.BusinessRules.ReviewModels.ReviewHistoryOutput 'AlifeApi.BusinessRules.ReviewModels.ReviewHistoryOutput')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Collections.Generic.List-1 'System.Collections.Generic.List`1')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/Microsoft.AspNetCore.Mvc.ActionResult-1 'Microsoft.AspNetCore.Mvc.ActionResult`1')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')  
歷史記錄清單

<a name='AlifeApi.WebApi.Controllers.ReviewTaskController.GetReviewTaskDetail(int)'></a>

## ReviewTaskController.GetReviewTaskDetail(int) Method

取得審核流程詳細資料

```csharp
public System.Threading.Tasks.Task<Microsoft.AspNetCore.Mvc.ActionResult<AlifeApi.BusinessRules.ReviewModels.ReviewTaskDetailOutput>> GetReviewTaskDetail(int taskId);
```
#### Parameters

<a name='AlifeApi.WebApi.Controllers.ReviewTaskController.GetReviewTaskDetail(int).taskId'></a>

`taskId` [System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')

審核流程ID

#### Returns
[System.Threading.Tasks.Task&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')[Microsoft.AspNetCore.Mvc.ActionResult&lt;](https://docs.microsoft.com/en-us/dotnet/api/Microsoft.AspNetCore.Mvc.ActionResult-1 'Microsoft.AspNetCore.Mvc.ActionResult`1')[AlifeApi.BusinessRules.ReviewModels.ReviewTaskDetailOutput](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.BusinessRules.ReviewModels.ReviewTaskDetailOutput 'AlifeApi.BusinessRules.ReviewModels.ReviewTaskDetailOutput')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/Microsoft.AspNetCore.Mvc.ActionResult-1 'Microsoft.AspNetCore.Mvc.ActionResult`1')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')  
審核流程詳細資料

<a name='AlifeApi.WebApi.Controllers.ReviewTaskController.GetReviewTasksAsync(AlifeApi.BusinessRules.ReviewModels.ReviewTaskListInput)'></a>

## ReviewTaskController.GetReviewTasksAsync(ReviewTaskListInput) Method

取得審核流程清單

```csharp
public System.Threading.Tasks.Task<AlifeApi.BusinessRules.Infrastructure.PagedListOutput<AlifeApi.BusinessRules.ReviewModels.ReviewTaskOutput>> GetReviewTasksAsync(AlifeApi.BusinessRules.ReviewModels.ReviewTaskListInput input);
```
#### Parameters

<a name='AlifeApi.WebApi.Controllers.ReviewTaskController.GetReviewTasksAsync(AlifeApi.BusinessRules.ReviewModels.ReviewTaskListInput).input'></a>

`input` [AlifeApi.BusinessRules.ReviewModels.ReviewTaskListInput](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.BusinessRules.ReviewModels.ReviewTaskListInput 'AlifeApi.BusinessRules.ReviewModels.ReviewTaskListInput')

查詢條件

#### Returns
[System.Threading.Tasks.Task&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')[AlifeApi.BusinessRules.Infrastructure.PagedListOutput&lt;](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.BusinessRules.Infrastructure.PagedListOutput-1 'AlifeApi.BusinessRules.Infrastructure.PagedListOutput`1')[AlifeApi.BusinessRules.ReviewModels.ReviewTaskOutput](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.BusinessRules.ReviewModels.ReviewTaskOutput 'AlifeApi.BusinessRules.ReviewModels.ReviewTaskOutput')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.BusinessRules.Infrastructure.PagedListOutput-1 'AlifeApi.BusinessRules.Infrastructure.PagedListOutput`1')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')  
審核流程清單

<a name='AlifeApi.WebApi.Controllers.ReviewTaskController.GetReviewUsers()'></a>

## ReviewTaskController.GetReviewUsers() Method

獲取審核人員清單

```csharp
public System.Threading.Tasks.Task<Microsoft.AspNetCore.Mvc.ActionResult<System.Collections.Generic.List<AlifeApi.BusinessRules.ReviewModels.ReviewUserOutput>>> GetReviewUsers();
```

#### Returns
[System.Threading.Tasks.Task&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')[Microsoft.AspNetCore.Mvc.ActionResult&lt;](https://docs.microsoft.com/en-us/dotnet/api/Microsoft.AspNetCore.Mvc.ActionResult-1 'Microsoft.AspNetCore.Mvc.ActionResult`1')[System.Collections.Generic.List&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Collections.Generic.List-1 'System.Collections.Generic.List`1')[AlifeApi.BusinessRules.ReviewModels.ReviewUserOutput](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.BusinessRules.ReviewModels.ReviewUserOutput 'AlifeApi.BusinessRules.ReviewModels.ReviewUserOutput')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Collections.Generic.List-1 'System.Collections.Generic.List`1')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/Microsoft.AspNetCore.Mvc.ActionResult-1 'Microsoft.AspNetCore.Mvc.ActionResult`1')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')  
審核人員清單

<a name='AlifeApi.WebApi.Controllers.ReviewTaskController.UpdateReviewTask(int,AlifeApi.BusinessRules.ReviewModels.ReviewTaskUpdateInput)'></a>

## ReviewTaskController.UpdateReviewTask(int, ReviewTaskUpdateInput) Method

更新審核流程

```csharp
public System.Threading.Tasks.Task<Microsoft.AspNetCore.Mvc.ActionResult<int>> UpdateReviewTask(int taskId, AlifeApi.BusinessRules.ReviewModels.ReviewTaskUpdateInput input);
```
#### Parameters

<a name='AlifeApi.WebApi.Controllers.ReviewTaskController.UpdateReviewTask(int,AlifeApi.BusinessRules.ReviewModels.ReviewTaskUpdateInput).taskId'></a>

`taskId` [System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')

審核流程ID

<a name='AlifeApi.WebApi.Controllers.ReviewTaskController.UpdateReviewTask(int,AlifeApi.BusinessRules.ReviewModels.ReviewTaskUpdateInput).input'></a>

`input` [AlifeApi.BusinessRules.ReviewModels.ReviewTaskUpdateInput](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.BusinessRules.ReviewModels.ReviewTaskUpdateInput 'AlifeApi.BusinessRules.ReviewModels.ReviewTaskUpdateInput')

審核流程更新輸入資料

#### Returns
[System.Threading.Tasks.Task&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')[Microsoft.AspNetCore.Mvc.ActionResult&lt;](https://docs.microsoft.com/en-us/dotnet/api/Microsoft.AspNetCore.Mvc.ActionResult-1 'Microsoft.AspNetCore.Mvc.ActionResult`1')[System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/Microsoft.AspNetCore.Mvc.ActionResult-1 'Microsoft.AspNetCore.Mvc.ActionResult`1')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')  
審核流程ID