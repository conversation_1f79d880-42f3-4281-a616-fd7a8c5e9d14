#### [AlifeApi.WebApi](index.md 'index')
### [AlifeApi.WebApi.Controllers](AlifeApi.WebApi.Controllers.md 'AlifeApi.WebApi.Controllers')

## MediumCategoriesController Class

中分類管理

```csharp
public class MediumCategoriesController : AlifeApi.WebApi.Controllers.AuthenticatedController
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; [Microsoft.AspNetCore.Mvc.ControllerBase](https://docs.microsoft.com/en-us/dotnet/api/Microsoft.AspNetCore.Mvc.ControllerBase 'Microsoft.AspNetCore.Mvc.ControllerBase') &#129106; [Microsoft.AspNetCore.Mvc.Controller](https://docs.microsoft.com/en-us/dotnet/api/Microsoft.AspNetCore.Mvc.Controller 'Microsoft.AspNetCore.Mvc.Controller') &#129106; [AlifeApi.WebApi.Controllers.BasicController](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.WebApi.Controllers.BasicController 'AlifeApi.WebApi.Controllers.BasicController') &#129106; [AlifeApi.WebApi.Controllers.AuthenticatedController](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.WebApi.Controllers.AuthenticatedController 'AlifeApi.WebApi.Controllers.AuthenticatedController') &#129106; MediumCategoriesController
### Methods

<a name='AlifeApi.WebApi.Controllers.MediumCategoriesController.CreateMediumCategory(AlifeApi.BusinessRules.CategoryModels.MediumCategoryInput)'></a>

## MediumCategoriesController.CreateMediumCategory(MediumCategoryInput) Method

新增中分類

```csharp
public System.Threading.Tasks.Task<Microsoft.AspNetCore.Mvc.IActionResult> CreateMediumCategory(AlifeApi.BusinessRules.CategoryModels.MediumCategoryInput input);
```
#### Parameters

<a name='AlifeApi.WebApi.Controllers.MediumCategoriesController.CreateMediumCategory(AlifeApi.BusinessRules.CategoryModels.MediumCategoryInput).input'></a>

`input` [AlifeApi.BusinessRules.CategoryModels.MediumCategoryInput](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.BusinessRules.CategoryModels.MediumCategoryInput 'AlifeApi.BusinessRules.CategoryModels.MediumCategoryInput')

新增中分類輸入資料

#### Returns
[System.Threading.Tasks.Task&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')[Microsoft.AspNetCore.Mvc.IActionResult](https://docs.microsoft.com/en-us/dotnet/api/Microsoft.AspNetCore.Mvc.IActionResult 'Microsoft.AspNetCore.Mvc.IActionResult')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')  
新增的中分類ID

<a name='AlifeApi.WebApi.Controllers.MediumCategoriesController.DeleteMediumCategory(long)'></a>

## MediumCategoriesController.DeleteMediumCategory(long) Method

刪除中分類

```csharp
public System.Threading.Tasks.Task<Microsoft.AspNetCore.Mvc.IActionResult> DeleteMediumCategory(long mediumCategoryId);
```
#### Parameters

<a name='AlifeApi.WebApi.Controllers.MediumCategoriesController.DeleteMediumCategory(long).mediumCategoryId'></a>

`mediumCategoryId` [System.Int64](https://docs.microsoft.com/en-us/dotnet/api/System.Int64 'System.Int64')

要刪除的中分類ID

#### Returns
[System.Threading.Tasks.Task&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')[Microsoft.AspNetCore.Mvc.IActionResult](https://docs.microsoft.com/en-us/dotnet/api/Microsoft.AspNetCore.Mvc.IActionResult 'Microsoft.AspNetCore.Mvc.IActionResult')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')  
成功時返回 Ok，失敗時返回 NotFound 或 BadRequest

<a name='AlifeApi.WebApi.Controllers.MediumCategoriesController.GetMediumCategories(AlifeApi.BusinessRules.CategoryModels.MediumCategoryQueryInput)'></a>

## MediumCategoriesController.GetMediumCategories(MediumCategoryQueryInput) Method

取得中分類列表 (分頁)

```csharp
public System.Threading.Tasks.Task<Microsoft.AspNetCore.Mvc.IActionResult> GetMediumCategories(AlifeApi.BusinessRules.CategoryModels.MediumCategoryQueryInput input);
```
#### Parameters

<a name='AlifeApi.WebApi.Controllers.MediumCategoriesController.GetMediumCategories(AlifeApi.BusinessRules.CategoryModels.MediumCategoryQueryInput).input'></a>

`input` [AlifeApi.BusinessRules.CategoryModels.MediumCategoryQueryInput](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.BusinessRules.CategoryModels.MediumCategoryQueryInput 'AlifeApi.BusinessRules.CategoryModels.MediumCategoryQueryInput')

查詢條件

#### Returns
[System.Threading.Tasks.Task&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')[Microsoft.AspNetCore.Mvc.IActionResult](https://docs.microsoft.com/en-us/dotnet/api/Microsoft.AspNetCore.Mvc.IActionResult 'Microsoft.AspNetCore.Mvc.IActionResult')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')  
分頁的中分類列表

<a name='AlifeApi.WebApi.Controllers.MediumCategoriesController.GetMediumCategory(long)'></a>

## MediumCategoriesController.GetMediumCategory(long) Method

根據中分類ID取得單一中分類資訊

```csharp
public System.Threading.Tasks.Task<Microsoft.AspNetCore.Mvc.IActionResult> GetMediumCategory(long mediumCategoryId);
```
#### Parameters

<a name='AlifeApi.WebApi.Controllers.MediumCategoriesController.GetMediumCategory(long).mediumCategoryId'></a>

`mediumCategoryId` [System.Int64](https://docs.microsoft.com/en-us/dotnet/api/System.Int64 'System.Int64')

中分類ID

#### Returns
[System.Threading.Tasks.Task&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')[Microsoft.AspNetCore.Mvc.IActionResult](https://docs.microsoft.com/en-us/dotnet/api/Microsoft.AspNetCore.Mvc.IActionResult 'Microsoft.AspNetCore.Mvc.IActionResult')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')  
中分類詳細資訊

<a name='AlifeApi.WebApi.Controllers.MediumCategoriesController.GetMediumCategoryDropdown(System.Nullable_long_)'></a>

## MediumCategoriesController.GetMediumCategoryDropdown(Nullable<long>) Method

取得中分類下拉選單列表

```csharp
public System.Threading.Tasks.Task<Microsoft.AspNetCore.Mvc.IActionResult> GetMediumCategoryDropdown(System.Nullable<long> largeCategoryId=null);
```
#### Parameters

<a name='AlifeApi.WebApi.Controllers.MediumCategoriesController.GetMediumCategoryDropdown(System.Nullable_long_).largeCategoryId'></a>

`largeCategoryId` [System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.Int64](https://docs.microsoft.com/en-us/dotnet/api/System.Int64 'System.Int64')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

可選的大分類ID，用於篩選特定大分類下的中分類

#### Returns
[System.Threading.Tasks.Task&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')[Microsoft.AspNetCore.Mvc.IActionResult](https://docs.microsoft.com/en-us/dotnet/api/Microsoft.AspNetCore.Mvc.IActionResult 'Microsoft.AspNetCore.Mvc.IActionResult')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')  
啟用的中分類下拉選單列表

<a name='AlifeApi.WebApi.Controllers.MediumCategoriesController.UpdateMediumCategory(long,AlifeApi.BusinessRules.CategoryModels.MediumCategoryInput)'></a>

## MediumCategoriesController.UpdateMediumCategory(long, MediumCategoryInput) Method

更新中分類資料

```csharp
public System.Threading.Tasks.Task<Microsoft.AspNetCore.Mvc.IActionResult> UpdateMediumCategory(long mediumCategoryId, AlifeApi.BusinessRules.CategoryModels.MediumCategoryInput input);
```
#### Parameters

<a name='AlifeApi.WebApi.Controllers.MediumCategoriesController.UpdateMediumCategory(long,AlifeApi.BusinessRules.CategoryModels.MediumCategoryInput).mediumCategoryId'></a>

`mediumCategoryId` [System.Int64](https://docs.microsoft.com/en-us/dotnet/api/System.Int64 'System.Int64')

要更新的中分類ID

<a name='AlifeApi.WebApi.Controllers.MediumCategoriesController.UpdateMediumCategory(long,AlifeApi.BusinessRules.CategoryModels.MediumCategoryInput).input'></a>

`input` [AlifeApi.BusinessRules.CategoryModels.MediumCategoryInput](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.BusinessRules.CategoryModels.MediumCategoryInput 'AlifeApi.BusinessRules.CategoryModels.MediumCategoryInput')

更新的中分類資料

#### Returns
[System.Threading.Tasks.Task&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')[Microsoft.AspNetCore.Mvc.IActionResult](https://docs.microsoft.com/en-us/dotnet/api/Microsoft.AspNetCore.Mvc.IActionResult 'Microsoft.AspNetCore.Mvc.IActionResult')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')  
成功時返回 Ok，失敗時返回 NotFound 或 BadRequest