#### [AlifeApi.BusinessRules](index.md 'index')
### [AlifeApi.BusinessRules.SiteModels](AlifeApi.BusinessRules.SiteModels.md 'AlifeApi.BusinessRules.SiteModels')

## SiteDropdownOutput Class

案場下拉選單輸出

```csharp
public class SiteDropdownOutput
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; SiteDropdownOutput
### Properties

<a name='AlifeApi.BusinessRules.SiteModels.SiteDropdownOutput.Name'></a>

## SiteDropdownOutput.Name Property

顯示名稱

```csharp
public string Name { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.SiteModels.SiteDropdownOutput.Value'></a>

## SiteDropdownOutput.Value Property

案場代碼

```csharp
public string Value { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')