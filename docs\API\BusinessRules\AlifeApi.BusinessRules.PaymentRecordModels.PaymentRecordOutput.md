#### [AlifeApi.BusinessRules](index.md 'index')
### [AlifeApi.BusinessRules.PaymentRecordModels](AlifeApi.BusinessRules.PaymentRecordModels.md 'AlifeApi.BusinessRules.PaymentRecordModels')

## PaymentRecordOutput Class

收款記錄詳細輸出模型

```csharp
public class PaymentRecordOutput
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; PaymentRecordOutput
### Properties

<a name='AlifeApi.BusinessRules.PaymentRecordModels.PaymentRecordOutput.Amount'></a>

## PaymentRecordOutput.Amount Property

收款金額

```csharp
public decimal Amount { get; set; }
```

#### Property Value
[System.Decimal](https://docs.microsoft.com/en-us/dotnet/api/System.Decimal 'System.Decimal')

<a name='AlifeApi.BusinessRules.PaymentRecordModels.PaymentRecordOutput.AttachmentPath'></a>

## PaymentRecordOutput.AttachmentPath Property

附件路徑

```csharp
public string? AttachmentPath { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.PaymentRecordModels.PaymentRecordOutput.CreatedTime'></a>

## PaymentRecordOutput.CreatedTime Property

建立時間

```csharp
public System.DateTime CreatedTime { get; set; }
```

#### Property Value
[System.DateTime](https://docs.microsoft.com/en-us/dotnet/api/System.DateTime 'System.DateTime')

<a name='AlifeApi.BusinessRules.PaymentRecordModels.PaymentRecordOutput.CreatedUserInfoId'></a>

## PaymentRecordOutput.CreatedUserInfoId Property

建立者 UserInfoId

```csharp
public string CreatedUserInfoId { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.PaymentRecordModels.PaymentRecordOutput.CreatedUserName'></a>

## PaymentRecordOutput.CreatedUserName Property

建立者名稱 (選填)

```csharp
public string? CreatedUserName { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.PaymentRecordModels.PaymentRecordOutput.HandlingFee'></a>

## PaymentRecordOutput.HandlingFee Property

手續費

```csharp
public System.Nullable<decimal> HandlingFee { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.Decimal](https://docs.microsoft.com/en-us/dotnet/api/System.Decimal 'System.Decimal')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

<a name='AlifeApi.BusinessRules.PaymentRecordModels.PaymentRecordOutput.OrderId'></a>

## PaymentRecordOutput.OrderId Property

訂單ID

```csharp
public int OrderId { get; set; }
```

#### Property Value
[System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')

<a name='AlifeApi.BusinessRules.PaymentRecordModels.PaymentRecordOutput.OrderNumber'></a>

## PaymentRecordOutput.OrderNumber Property

訂單號碼 (選填)

```csharp
public string? OrderNumber { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.PaymentRecordModels.PaymentRecordOutput.PaymentDate'></a>

## PaymentRecordOutput.PaymentDate Property

收款日期

```csharp
public System.DateOnly PaymentDate { get; set; }
```

#### Property Value
[System.DateOnly](https://docs.microsoft.com/en-us/dotnet/api/System.DateOnly 'System.DateOnly')

<a name='AlifeApi.BusinessRules.PaymentRecordModels.PaymentRecordOutput.PaymentMethod'></a>

## PaymentRecordOutput.PaymentMethod Property

收款方式

```csharp
public string PaymentMethod { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.PaymentRecordModels.PaymentRecordOutput.PaymentRecordId'></a>

## PaymentRecordOutput.PaymentRecordId Property

收款紀錄唯一識別碼

```csharp
public int PaymentRecordId { get; set; }
```

#### Property Value
[System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')

<a name='AlifeApi.BusinessRules.PaymentRecordModels.PaymentRecordOutput.PaymentType'></a>

## PaymentRecordOutput.PaymentType Property

款別

```csharp
public string PaymentType { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.PaymentRecordModels.PaymentRecordOutput.Remarks'></a>

## PaymentRecordOutput.Remarks Property

備註

```csharp
public string? Remarks { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.PaymentRecordModels.PaymentRecordOutput.TransferredToDeveloper'></a>

## PaymentRecordOutput.TransferredToDeveloper Property

是否已轉給建商

```csharp
public System.Nullable<bool> TransferredToDeveloper { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.Boolean](https://docs.microsoft.com/en-us/dotnet/api/System.Boolean 'System.Boolean')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

<a name='AlifeApi.BusinessRules.PaymentRecordModels.PaymentRecordOutput.UpdatedTime'></a>

## PaymentRecordOutput.UpdatedTime Property

更新時間

```csharp
public System.DateTime UpdatedTime { get; set; }
```

#### Property Value
[System.DateTime](https://docs.microsoft.com/en-us/dotnet/api/System.DateTime 'System.DateTime')

<a name='AlifeApi.BusinessRules.PaymentRecordModels.PaymentRecordOutput.UpdatedUserInfoId'></a>

## PaymentRecordOutput.UpdatedUserInfoId Property

更新者 UserInfoId

```csharp
public string UpdatedUserInfoId { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.PaymentRecordModels.PaymentRecordOutput.UpdatedUserName'></a>

## PaymentRecordOutput.UpdatedUserName Property

更新者名稱 (選填)

```csharp
public string? UpdatedUserName { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')