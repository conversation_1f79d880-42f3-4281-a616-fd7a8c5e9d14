#### [<PERSON>feApi.WebApi](index.md 'index')
### [AlifeApi.WebApi.Controllers](AlifeApi.WebApi.Controllers.md 'AlifeApi.WebApi.Controllers')

## AuthorizationController Class

權限驗證(Authorization)

```csharp
public class AuthorizationController : AlifeApi.WebApi.Controllers.BasicController
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; [Microsoft.AspNetCore.Mvc.ControllerBase](https://docs.microsoft.com/en-us/dotnet/api/Microsoft.AspNetCore.Mvc.ControllerBase 'Microsoft.AspNetCore.Mvc.ControllerBase') &#129106; [Microsoft.AspNetCore.Mvc.Controller](https://docs.microsoft.com/en-us/dotnet/api/Microsoft.AspNetCore.Mvc.Controller 'Microsoft.AspNetCore.Mvc.Controller') &#129106; [AlifeApi.WebApi.Controllers.BasicController](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.WebApi.Controllers.BasicController 'AlifeApi.WebApi.Controllers.BasicController') &#129106; AuthorizationController
### Methods

<a name='AlifeApi.WebApi.Controllers.AuthorizationController.Captcha(AlifeApi.WebApi.Util.CaptchaHelper,AlifeApi.Common.Util.IEncryptionHelper)'></a>

## AuthorizationController.Captcha(CaptchaHelper, IEncryptionHelper) Method

取得網頁登入驗證碼圖形

```csharp
public Microsoft.AspNetCore.Mvc.FileContentResult Captcha(AlifeApi.WebApi.Util.CaptchaHelper captchaHelper, AlifeApi.Common.Util.IEncryptionHelper encryptionHelper);
```
#### Parameters

<a name='AlifeApi.WebApi.Controllers.AuthorizationController.Captcha(AlifeApi.WebApi.Util.CaptchaHelper,AlifeApi.Common.Util.IEncryptionHelper).captchaHelper'></a>

`captchaHelper` [CaptchaHelper](AlifeApi.WebApi.Util.CaptchaHelper.md 'AlifeApi.WebApi.Util.CaptchaHelper')

The captcha helper.

<a name='AlifeApi.WebApi.Controllers.AuthorizationController.Captcha(AlifeApi.WebApi.Util.CaptchaHelper,AlifeApi.Common.Util.IEncryptionHelper).encryptionHelper'></a>

`encryptionHelper` [AlifeApi.Common.Util.IEncryptionHelper](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.Common.Util.IEncryptionHelper 'AlifeApi.Common.Util.IEncryptionHelper')

The encryption helper.

#### Returns
[Microsoft.AspNetCore.Mvc.FileContentResult](https://docs.microsoft.com/en-us/dotnet/api/Microsoft.AspNetCore.Mvc.FileContentResult 'Microsoft.AspNetCore.Mvc.FileContentResult')  
圖形驗證碼的 FileContentResult

#### Exceptions

[System.ArgumentNullException](https://docs.microsoft.com/en-us/dotnet/api/System.ArgumentNullException 'System.ArgumentNullException')  
captchaHelper

[System.ArgumentNullException](https://docs.microsoft.com/en-us/dotnet/api/System.ArgumentNullException 'System.ArgumentNullException')  
encryptionHelper

<a name='AlifeApi.WebApi.Controllers.AuthorizationController.RefreshTokenAsync(string)'></a>

## AuthorizationController.RefreshTokenAsync(string) Method

刷新 Token

```csharp
public AlifeApi.WebApi.Models.AuthorizationModels.RefreshTokenOutput RefreshTokenAsync(string token);
```
#### Parameters

<a name='AlifeApi.WebApi.Controllers.AuthorizationController.RefreshTokenAsync(string).token'></a>

`token` [System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

The token.

#### Returns
[RefreshTokenOutput](AlifeApi.WebApi.Models.AuthorizationModels.RefreshTokenOutput.md 'AlifeApi.WebApi.Models.AuthorizationModels.RefreshTokenOutput')  
Token info.