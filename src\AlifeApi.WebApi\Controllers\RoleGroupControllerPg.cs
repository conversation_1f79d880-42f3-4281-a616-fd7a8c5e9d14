using AlifeApi.BusinessRules.Infrastructure;
using AlifeApi.BusinessRules.RoleGroupModels;
using Microsoft.AspNetCore.Mvc;

namespace AlifeApi.WebApi.Controllers
{
    /// <summary>
    /// 角色權限管理（PostgreSQL 版本）
    /// </summary>
    public class RoleGroupControllerPg : AuthenticatedController
    {
        /// <summary>
        /// 角色權限服務
        /// </summary>
        private readonly RoleGroupServicePg _roleGroupService;

        /// <summary>
        /// 建構函數
        /// </summary>
        /// <param name="roleGroupService">角色權限服務</param>
        /// <exception cref="ArgumentNullException">roleGroupService</exception>
        public RoleGroupControllerPg(RoleGroupServicePg roleGroupService)
        {
            _roleGroupService = roleGroupService ?? throw new ArgumentNullException(nameof(roleGroupService));
        }

        /// <summary>
        /// 取得角色權限列表
        /// </summary>
        /// <param name="input">查詢條件</param>
        /// <returns>角色權限列表</returns>
        [HttpPost("GetList")]
        public async Task<PagedListOutput<RoleGroupOutputPg>> GetRoleGroupListAsync([FromBody] RoleGroupListGetInputPg input)
            => await _roleGroupService.GetRoleGroupListAsync(input);

        /// <summary>
        /// 根據ID獲取角色權限詳細資訊
        /// </summary>
        /// <param name="id">角色ID</param>
        /// <param name="siteCode">案場代碼</param>
        /// <returns>角色權限詳細資訊</returns>
        [HttpGet("{id}")]
        public async Task<ActionResult<RoleGroupOutputPg>> GetRoleGroupByIdAsync(string id, [FromQuery] string siteCode = null)
        {
            var roleGroup = await _roleGroupService.GetRoleGroupByIdAsync(id, siteCode);
            if (roleGroup == null)
            {
                return NotFound();
            }
            return Ok(roleGroup);
        }

        /// <summary>
        /// 新增角色權限
        /// </summary>
        /// <param name="input">角色權限輸入資料</param>
        /// <returns>執行結果</returns>
        [HttpPost("Create")]
        public async Task<ActionResult> CreateRoleGroupAsync([FromBody] RoleGroupCreateInputPg input)
        {
            try
            {
                await _roleGroupService.CreateRoleGroupAsync(input);
                return Ok();
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }

        /// <summary>
        /// 修改角色權限功能
        /// </summary>
        /// <param name="input">角色權限更新輸入資料</param>
        /// <returns>執行結果</returns>
        [HttpPost("UpdatePermission")]
        public async Task<ActionResult> UpdateRoleGroupPermissionAsync([FromBody] RoleGroupPermissionUpdateInputPg input)
        {
            try
            {
                await _roleGroupService.UpdateRoleGroupPermissionAsync(input);
                return Ok();
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }

        /// <summary>
        /// 修改角色權限使用者
        /// </summary>
        /// <param name="input">角色權限使用者更新輸入資料</param>
        /// <returns>執行結果</returns>
        [HttpPost("UpdateUser")]
        public async Task<ActionResult> UpdateRoleGroupUserAsync([FromBody] RoleGroupUserUpdateInputPg input)
        {
            try
            {
                await _roleGroupService.UpdateRoleGroupUserAsync(input);
                return Ok();
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }

        /// <summary>
        /// 刪除角色
        /// </summary>
        /// <param name="input">角色刪除輸入資料</param>
        /// <returns>執行結果</returns>
        [HttpPost("Delete")]
        public async Task<ActionResult> DeleteRoleGroupAsync([FromBody] RoleGroupDeleteInputPg input)
        {
            try
            {
                await _roleGroupService.DeleteRoleGroupAsync(input);
                return Ok();
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }

        /// <summary>
        /// 取得權限樹
        /// </summary>
        /// <returns>權限樹</returns>
        [HttpGet("MenuTree")]
        public async Task<ActionResult<IEnumerable<MenuTreeOutput>>> GetMenuTreeAsync()
            => Ok(await _roleGroupService.GetMenuTreesAsync());

        /// <summary>
        /// 取得角色群組下拉選單
        /// </summary>
        /// <param name="input">查詢條件</param>
        /// <returns>角色群組下拉選單列表</returns>
        [HttpPost]
        public async Task<ActionResult<List<RoleGroupDropdownOutput>>> GetRoleGroupDropdownListAsync([FromBody] RoleGroupDropdownInputPg input)
        {
            try
            {
                var result = await _roleGroupService.GetRoleGroupDropdownListAsync(input);
                return Ok(result);
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }
    }
} 
