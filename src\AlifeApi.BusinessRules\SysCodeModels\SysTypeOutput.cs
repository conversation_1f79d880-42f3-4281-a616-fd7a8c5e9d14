﻿using System.Text.Json.Serialization;

namespace AlifeApi.BusinessRules.SysCodeModels
{
    /// <summary>
    /// SysType 類型
    /// </summary>
    public class SysTypeOutput
    {
        /// <summary>
        /// 值
        /// </summary>
        [JsonPropertyName("value")]
        public string Value { get; set; }

        /// <summary>
        /// 選項
        /// </summary>
        [JsonPropertyName("label")]
        public string Label { get; set; }

        /// <summary>
        /// 是否作廢
        /// </summary>
        /// <remarks>
        /// 沒作用的欄位，前人不知道從哪抄的，拿掉又怕前端程式會死
        /// </remarks>
        [JsonPropertyName("Sts")]
        public bool IsDisabled { get; set; }
    }
}
