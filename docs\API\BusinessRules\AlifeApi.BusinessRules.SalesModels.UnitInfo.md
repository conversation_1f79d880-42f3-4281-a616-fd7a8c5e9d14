#### [AlifeApi.BusinessRules](index.md 'index')
### [AlifeApi.BusinessRules.SalesModels](AlifeApi.BusinessRules.SalesModels.md 'AlifeApi.BusinessRules.SalesModels')

## UnitInfo Class

房屋單位資訊

```csharp
public class UnitInfo
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; UnitInfo
### Properties

<a name='AlifeApi.BusinessRules.SalesModels.UnitInfo.Area'></a>

## UnitInfo.Area Property

坪數

```csharp
public System.Nullable<decimal> Area { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.Decimal](https://docs.microsoft.com/en-us/dotnet/api/System.Decimal 'System.Decimal')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

<a name='AlifeApi.BusinessRules.SalesModels.UnitInfo.BuildingName'></a>

## UnitInfo.BuildingName Property

建築物名稱

```csharp
public string? BuildingName { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.SalesModels.UnitInfo.CustomerName'></a>

## UnitInfo.CustomerName Property

客戶姓名

```csharp
public string? CustomerName { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.SalesModels.UnitInfo.DisplayValue'></a>

## UnitInfo.DisplayValue Property

用於摘要行顯示的數值

```csharp
public object? DisplayValue { get; set; }
```

#### Property Value
[System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object')

<a name='AlifeApi.BusinessRules.SalesModels.UnitInfo.FloorLabel'></a>

## UnitInfo.FloorLabel Property

樓層名稱

```csharp
public string? FloorLabel { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.SalesModels.UnitInfo.PriceRegistrationSubmissionDate'></a>

## UnitInfo.PriceRegistrationSubmissionDate Property

實價登錄提交日期

```csharp
public System.Nullable<System.DateOnly> PriceRegistrationSubmissionDate { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.DateOnly](https://docs.microsoft.com/en-us/dotnet/api/System.DateOnly 'System.DateOnly')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

<a name='AlifeApi.BusinessRules.SalesModels.UnitInfo.PurchasedParkingSpaces'></a>

## UnitInfo.PurchasedParkingSpaces Property

購買的車位列表（車位編號，以逗號分隔）

```csharp
public string? PurchasedParkingSpaces { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.SalesModels.UnitInfo.ReceiveDate'></a>

## UnitInfo.ReceiveDate Property

領款日期 (已向業主領到費用的日期)

```csharp
public System.Nullable<System.DateOnly> ReceiveDate { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.DateOnly](https://docs.microsoft.com/en-us/dotnet/api/System.DateOnly 'System.DateOnly')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

<a name='AlifeApi.BusinessRules.SalesModels.UnitInfo.RequestDate'></a>

## UnitInfo.RequestDate Property

請款日期 (向業主請費用的日期)

```csharp
public System.Nullable<System.DateOnly> RequestDate { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.DateOnly](https://docs.microsoft.com/en-us/dotnet/api/System.DateOnly 'System.DateOnly')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

<a name='AlifeApi.BusinessRules.SalesModels.UnitInfo.SaleDate'></a>

## UnitInfo.SaleDate Property

售出日期 (實際銷售日期)

```csharp
public System.Nullable<System.DateOnly> SaleDate { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.DateOnly](https://docs.microsoft.com/en-us/dotnet/api/System.DateOnly 'System.DateOnly')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

<a name='AlifeApi.BusinessRules.SalesModels.UnitInfo.SalePrice'></a>

## UnitInfo.SalePrice Property

成交價格

```csharp
public System.Nullable<decimal> SalePrice { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.Decimal](https://docs.microsoft.com/en-us/dotnet/api/System.Decimal 'System.Decimal')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

<a name='AlifeApi.BusinessRules.SalesModels.UnitInfo.Status'></a>

## UnitInfo.Status Property

銷售狀態

```csharp
public string Status { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.SalesModels.UnitInfo.UnitId'></a>

## UnitInfo.UnitId Property

房屋單位ID

```csharp
public int UnitId { get; set; }
```

#### Property Value
[System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')

<a name='AlifeApi.BusinessRules.SalesModels.UnitInfo.UnitNumber'></a>

## UnitInfo.UnitNumber Property

戶號 (例如: "A", "B", "C")

```csharp
public string UnitNumber { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.SalesModels.UnitInfo.UnitType'></a>

## UnitInfo.UnitType Property

房型 (格局)

```csharp
public string? UnitType { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')