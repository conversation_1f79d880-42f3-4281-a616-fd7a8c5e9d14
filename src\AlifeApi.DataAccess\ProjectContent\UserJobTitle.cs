﻿using System;
using System.Collections.Generic;

namespace AlifeApi.DataAccess.ProjectContent
{
    /// <summary>
    /// 員工職位關聯表，用於儲存員工與職位的關聯關係，支持員工擁有複數職位。
    /// </summary>
    public partial class UserJobTitle
    {
        /// <summary>
        /// 員工編號，主鍵的一部分，對應 UserInfo 表的 UserInfoId。
        /// </summary>
        public string UserInfoId { get; set; }
        /// <summary>
        /// 職位編號，主鍵的一部分，對應 JobTitles 表的 JobTitleId。
        /// </summary>
        public string JobTitleId { get; set; }
        /// <summary>
        /// 是否為主要職位，true 表示是，false 表示否，預設為 false。
        /// </summary>
        public bool? IsPrimary { get; set; }

        public virtual UserInfo UserInfo { get; set; }
    }
}
