#### [AlifeApi.BusinessRules](index.md 'index')
### [AlifeApi.BusinessRules.ReviewModels](AlifeApi.BusinessRules.ReviewModels.md 'AlifeApi.BusinessRules.ReviewModels')

## ReviewTaskOutput Class

審核流程輸出資料

```csharp
public class ReviewTaskOutput
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; ReviewTaskOutput

Derived  
&#8627; [ReviewTaskDetailOutput](AlifeApi.BusinessRules.ReviewModels.ReviewTaskDetailOutput.md 'AlifeApi.BusinessRules.ReviewModels.ReviewTaskDetailOutput')
### Properties

<a name='AlifeApi.BusinessRules.ReviewModels.ReviewTaskOutput.CreatedTime'></a>

## ReviewTaskOutput.CreatedTime Property

創建時間

```csharp
public System.DateTime CreatedTime { get; set; }
```

#### Property Value
[System.DateTime](https://docs.microsoft.com/en-us/dotnet/api/System.DateTime 'System.DateTime')

<a name='AlifeApi.BusinessRules.ReviewModels.ReviewTaskOutput.CreatedUserInfoId'></a>

## ReviewTaskOutput.CreatedUserInfoId Property

創建者ID

```csharp
public string CreatedUserInfoId { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.ReviewModels.ReviewTaskOutput.Description'></a>

## ReviewTaskOutput.Description Property

描述

```csharp
public string Description { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.ReviewModels.ReviewTaskOutput.Name'></a>

## ReviewTaskOutput.Name Property

流程名稱

```csharp
public string Name { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.ReviewModels.ReviewTaskOutput.Status'></a>

## ReviewTaskOutput.Status Property

狀態

```csharp
public string Status { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.ReviewModels.ReviewTaskOutput.TaskId'></a>

## ReviewTaskOutput.TaskId Property

審核流程ID

```csharp
public int TaskId { get; set; }
```

#### Property Value
[System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')

<a name='AlifeApi.BusinessRules.ReviewModels.ReviewTaskOutput.TotalStep'></a>

## ReviewTaskOutput.TotalStep Property

步驟總數

```csharp
public int TotalStep { get; set; }
```

#### Property Value
[System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')

<a name='AlifeApi.BusinessRules.ReviewModels.ReviewTaskOutput.UpdatedTime'></a>

## ReviewTaskOutput.UpdatedTime Property

更新時間

```csharp
public System.DateTime UpdatedTime { get; set; }
```

#### Property Value
[System.DateTime](https://docs.microsoft.com/en-us/dotnet/api/System.DateTime 'System.DateTime')

<a name='AlifeApi.BusinessRules.ReviewModels.ReviewTaskOutput.UpdatedUserInfoId'></a>

## ReviewTaskOutput.UpdatedUserInfoId Property

更新者ID

```csharp
public string UpdatedUserInfoId { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')