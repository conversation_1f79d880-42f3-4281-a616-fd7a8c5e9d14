#### [AlifeApi.BusinessRules](index.md 'index')
### [AlifeApi.BusinessRules.ParkingSpaceModels](AlifeApi.BusinessRules.ParkingSpaceModels.md 'AlifeApi.BusinessRules.ParkingSpaceModels')

## ParkingSpaceBasicInfoOutput Class

停車位基本資訊輸出模型 (用於 PurchaseOrderOutput)

```csharp
public class ParkingSpaceBasicInfoOutput
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; ParkingSpaceBasicInfoOutput
### Properties

<a name='AlifeApi.BusinessRules.ParkingSpaceModels.ParkingSpaceBasicInfoOutput.BuildingName'></a>

## ParkingSpaceBasicInfoOutput.BuildingName Property

建築物名稱 (選填)

```csharp
public string? BuildingName { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.ParkingSpaceModels.ParkingSpaceBasicInfoOutput.FloorLabel'></a>

## ParkingSpaceBasicInfoOutput.FloorLabel Property

樓層標示 (選填)

```csharp
public string? FloorLabel { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.ParkingSpaceModels.ParkingSpaceBasicInfoOutput.ParkingSpaceId'></a>

## ParkingSpaceBasicInfoOutput.ParkingSpaceId Property

車位唯一識別碼

```csharp
public int ParkingSpaceId { get; set; }
```

#### Property Value
[System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')

<a name='AlifeApi.BusinessRules.ParkingSpaceModels.ParkingSpaceBasicInfoOutput.SpaceNumber'></a>

## ParkingSpaceBasicInfoOutput.SpaceNumber Property

車位編號

```csharp
public string SpaceNumber { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.ParkingSpaceModels.ParkingSpaceBasicInfoOutput.SpaceType'></a>

## ParkingSpaceBasicInfoOutput.SpaceType Property

車位類型

```csharp
public string SpaceType { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')