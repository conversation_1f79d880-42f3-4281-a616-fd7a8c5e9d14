#### [AlifeApi.BusinessRules](index.md 'index')
### [AlifeApi.BusinessRules.SecuritySettingModels](AlifeApi.BusinessRules.SecuritySettingModels.md 'AlifeApi.BusinessRules.SecuritySettingModels')

## SecuritySettingModel Class

```csharp
public class SecuritySettingModel
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; SecuritySettingModel
### Properties

<a name='AlifeApi.BusinessRules.SecuritySettingModels.SecuritySettingModel.Enabled'></a>

## SecuritySettingModel.Enabled Property

是否啟用

```csharp
public bool Enabled { get; set; }
```

#### Property Value
[System.Boolean](https://docs.microsoft.com/en-us/dotnet/api/System.Boolean 'System.Boolean')

<a name='AlifeApi.BusinessRules.SecuritySettingModels.SecuritySettingModel.Key'></a>

## SecuritySettingModel.Key Property

鍵

```csharp
public string Key { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.SecuritySettingModels.SecuritySettingModel.Name'></a>

## SecuritySettingModel.Name Property

詳細名稱

```csharp
public string Name { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.SecuritySettingModels.SecuritySettingModel.Value'></a>

## SecuritySettingModel.Value Property

值

```csharp
public string Value { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')