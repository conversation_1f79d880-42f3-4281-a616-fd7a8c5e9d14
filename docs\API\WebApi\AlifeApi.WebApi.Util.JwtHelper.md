#### [AlifeApi.WebApi](index.md 'index')
### [AlifeApi.WebApi.Util](AlifeApi.WebApi.Util.md 'AlifeApi.WebApi.Util')

## JwtHelper Class

```csharp
public class JwtHelper : AlifeApi.BusinessRules.Infrastructure.ServiceBase<AlifeApi.DataAccess.ProjectContent.alifeContext>
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; [AlifeApi.Common.InfrastructureBase](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.Common.InfrastructureBase 'AlifeApi.Common.InfrastructureBase') &#129106; [AlifeApi.BusinessRules.Infrastructure.ServiceBase&lt;](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.BusinessRules.Infrastructure.ServiceBase-1 'AlifeApi.BusinessRules.Infrastructure.ServiceBase`1')[AlifeApi.DataAccess.ProjectContent.alifeContext](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.DataAccess.ProjectContent.alifeContext 'AlifeApi.DataAccess.ProjectContent.alifeContext')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.BusinessRules.Infrastructure.ServiceBase-1 'AlifeApi.BusinessRules.Infrastructure.ServiceBase`1') &#129106; JwtHelper
### Methods

<a name='AlifeApi.WebApi.Util.JwtHelper.ConvertUnixTimeStampToDateTimeOffset(long)'></a>

## JwtHelper.ConvertUnixTimeStampToDateTimeOffset(long) Method

將 Unix 時間戳轉換為目標時區的 DateTimeOffset。

```csharp
private static System.DateTime ConvertUnixTimeStampToDateTimeOffset(long unixTimeStamp);
```
#### Parameters

<a name='AlifeApi.WebApi.Util.JwtHelper.ConvertUnixTimeStampToDateTimeOffset(long).unixTimeStamp'></a>

`unixTimeStamp` [System.Int64](https://docs.microsoft.com/en-us/dotnet/api/System.Int64 'System.Int64')

Unix 時間戳。

#### Returns
[System.DateTime](https://docs.microsoft.com/en-us/dotnet/api/System.DateTime 'System.DateTime')  
目標時區的 DateTimeOffset。

<a name='AlifeApi.WebApi.Util.JwtHelper.GenerateToken(string,string,string,System.Collections.Generic.IEnumerable_string_)'></a>

## JwtHelper.GenerateToken(string, string, string, IEnumerable<string>) Method

產生 Token

```csharp
public string GenerateToken(string userId, string deptId, string gradeCoe, System.Collections.Generic.IEnumerable<string> roleIds);
```
#### Parameters

<a name='AlifeApi.WebApi.Util.JwtHelper.GenerateToken(string,string,string,System.Collections.Generic.IEnumerable_string_).userId'></a>

`userId` [System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

The user identifier.

<a name='AlifeApi.WebApi.Util.JwtHelper.GenerateToken(string,string,string,System.Collections.Generic.IEnumerable_string_).deptId'></a>

`deptId` [System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

The dept identifier.

<a name='AlifeApi.WebApi.Util.JwtHelper.GenerateToken(string,string,string,System.Collections.Generic.IEnumerable_string_).gradeCoe'></a>

`gradeCoe` [System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

The grade coe.

<a name='AlifeApi.WebApi.Util.JwtHelper.GenerateToken(string,string,string,System.Collections.Generic.IEnumerable_string_).roleIds'></a>

`roleIds` [System.Collections.Generic.IEnumerable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Collections.Generic.IEnumerable-1 'System.Collections.Generic.IEnumerable`1')[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Collections.Generic.IEnumerable-1 'System.Collections.Generic.IEnumerable`1')

The role ids.

#### Returns
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')  
JWT Token

<a name='AlifeApi.WebApi.Util.JwtHelper.GenerateTokenV2(string)'></a>

## JwtHelper.GenerateTokenV2(string) Method

產生 Token

```csharp
public string GenerateTokenV2(string userId);
```
#### Parameters

<a name='AlifeApi.WebApi.Util.JwtHelper.GenerateTokenV2(string).userId'></a>

`userId` [System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

The user identifier.

#### Returns
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')  
JWT Token

<a name='AlifeApi.WebApi.Util.JwtHelper.GetTokenExpirationTime(string)'></a>

## JwtHelper.GetTokenExpirationTime(string) Method

取回 Token 的有效日期時間

```csharp
public System.Nullable<System.DateTime> GetTokenExpirationTime(string token);
```
#### Parameters

<a name='AlifeApi.WebApi.Util.JwtHelper.GetTokenExpirationTime(string).token'></a>

`token` [System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

The token.

#### Returns
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.DateTime](https://docs.microsoft.com/en-us/dotnet/api/System.DateTime 'System.DateTime')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

<a name='AlifeApi.WebApi.Util.JwtHelper.GetTokenInfo(string)'></a>

## JwtHelper.GetTokenInfo(string) Method

取回 Token 的有效日期時間

```csharp
public (string,System.Nullable<System.DateTime>) GetTokenInfo(string token);
```
#### Parameters

<a name='AlifeApi.WebApi.Util.JwtHelper.GetTokenInfo(string).token'></a>

`token` [System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

The token.

#### Returns
[&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.ValueTuple 'System.ValueTuple')[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')[,](https://docs.microsoft.com/en-us/dotnet/api/System.ValueTuple 'System.ValueTuple')[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.DateTime](https://docs.microsoft.com/en-us/dotnet/api/System.DateTime 'System.DateTime')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.ValueTuple 'System.ValueTuple')