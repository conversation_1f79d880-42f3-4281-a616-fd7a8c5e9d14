#### [AlifeApi.DataAccess](index.md 'index')
### [AlifeApi.DataAccess.ProjectContent](AlifeApi.DataAccess.ProjectContent.md 'AlifeApi.DataAccess.ProjectContent')

## Company Class

公司資料表，用於儲存公司基本資訊。

```csharp
public class Company
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; Company
### Properties

<a name='AlifeApi.DataAccess.ProjectContent.Company.CompanyId'></a>

## Company.CompanyId Property

公司編號，主鍵，用於唯一識別公司。

```csharp
public string CompanyId { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContent.Company.CreatedTime'></a>

## Company.CreatedTime Property

創建時間，記錄資料創建的時間。

```csharp
public System.DateTime CreatedTime { get; set; }
```

#### Property Value
[System.DateTime](https://docs.microsoft.com/en-us/dotnet/api/System.DateTime 'System.DateTime')

<a name='AlifeApi.DataAccess.ProjectContent.Company.CreatedUserInfoId'></a>

## Company.CreatedUserInfoId Property

建立者，記錄創建此資料的員工編號。

```csharp
public string CreatedUserInfoId { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContent.Company.Name'></a>

## Company.Name Property

公司名稱。

```csharp
public string Name { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContent.Company.UpdatedTime'></a>

## Company.UpdatedTime Property

更新時間，記錄資料最後更新的時間。

```csharp
public System.DateTime UpdatedTime { get; set; }
```

#### Property Value
[System.DateTime](https://docs.microsoft.com/en-us/dotnet/api/System.DateTime 'System.DateTime')

<a name='AlifeApi.DataAccess.ProjectContent.Company.UpdatedUserInfoId'></a>

## Company.UpdatedUserInfoId Property

更新者，記錄最後更新此資料的員工編號。

```csharp
public string UpdatedUserInfoId { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')