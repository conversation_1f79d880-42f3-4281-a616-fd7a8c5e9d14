using AlifeApi.BusinessRules.Infrastructure;
using AlifeApi.BusinessRules.SiteModels;
using Microsoft.AspNetCore.Mvc;

namespace AlifeApi.WebApi.Controllers
{
    /// <summary>
    /// 案場管理
    /// </summary>
    public class SiteController : AuthenticatedController
    {
        private readonly SiteService _siteService;

        /// <summary>
        /// 建構函數
        /// </summary>
        public SiteController(SiteService siteService)
        {
            _siteService = siteService ?? throw new ArgumentNullException(nameof(siteService));
        }

        /// <summary>
        /// 取得案場列表
        /// </summary>
        /// <param name="input">查詢條件</param>
        /// <returns>案場列表</returns>
        [HttpPost("GetList")]
        public async Task<PagedListOutput<SiteOutput>> GetSiteListAsync([FromBody] SiteListGetInput input)
            => await _siteService.GetSiteListAsync(input);

        /// <summary>
        /// 根據代碼取得案場
        /// </summary>
        /// <param name="siteCode">案場代碼</param>
        /// <returns>案場資訊</returns>
        [HttpGet("{siteCode}")]
        public async Task<ActionResult<SiteOutput>> GetSiteByCodeAsync(string siteCode)
        {
            var site = await _siteService.GetSiteByCodeAsync(siteCode);
            if (site == null)
            {
                return NotFound();
            }
            return Ok(site);
        }

        /// <summary>
        /// 創建案場
        /// </summary>
        /// <param name="input">案場創建輸入</param>
        /// <returns>執行結果</returns>
        [HttpPost("Create")]
        public async Task<ActionResult> CreateSiteAsync([FromBody] SiteCreateInput input)
        {
            try
            {
                await _siteService.CreateSiteAsync(input);
                return Ok();
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }

        /// <summary>
        /// 更新案場
        /// </summary>
        /// <param name="input">案場更新輸入</param>
        /// <returns>執行結果</returns>
        [HttpPost("Update")]
        public async Task<ActionResult> UpdateSiteAsync([FromBody] SiteUpdateInput input)
        {
            try
            {
                await _siteService.UpdateSiteAsync(input);
                return Ok();
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }

        /// <summary>
        /// 刪除案場
        /// </summary>
        /// <param name="siteCode">案場代碼</param>
        /// <returns>執行結果</returns>
        [HttpDelete("{siteCode}")]
        public async Task<ActionResult> DeleteSiteAsync(string siteCode)
        {
            try
            {
                await _siteService.DeleteSiteAsync(siteCode);
                return Ok();
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }

        /// <summary>
        /// 取得案場下拉選單
        /// </summary>
        /// <returns>案場下拉選單列表</returns>
        [HttpGet("Dropdown")]
        public async Task<ActionResult<List<SiteDropdownOutput>>> GetSiteDropdownListAsync()
        {
            try
            {
                var result = await _siteService.GetSiteDropdownListAsync();
                return Ok(result);
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }
    }
} 
