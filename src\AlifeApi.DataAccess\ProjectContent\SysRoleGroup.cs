﻿using System;
using System.Collections.Generic;

namespace AlifeApi.DataAccess.ProjectContent
{
    /// <summary>
    /// 角色群組表，用於儲存系統中的角色群組資訊，支援與特定案場關聯。
    /// </summary>
    public partial class SysRoleGroup
    {
        /// <summary>
        /// 系統名稱，主鍵的一部分，用於區分不同系統。
        /// </summary>
        public string System { get; set; }
        /// <summary>
        /// 案場號碼，可為 NULL 表示全局角色，對應 Sites 表的 SiteCode。
        /// </summary>
        public string SiteCode { get; set; }
        /// <summary>
        /// 角色群組識別編號，主鍵的一部分，用於唯一識別角色群組。
        /// </summary>
        public string RoleGroupId { get; set; }
        /// <summary>
        /// 角色群組名稱。
        /// </summary>
        public string Name { get; set; }
        /// <summary>
        /// 是否為管理者角色，true 表示是，false 表示否。
        /// </summary>
        public bool IsAdmin { get; set; }
        /// <summary>
        /// 建立者，記錄創建此角色群組的員工編號。
        /// </summary>
        public string CreatedUserInfoId { get; set; }
        /// <summary>
        /// 創建時間，記錄角色群組創建的時間。
        /// </summary>
        public DateTime CreatedTime { get; set; }
        /// <summary>
        /// 更新者，記錄最後更新此角色群組的員工編號。
        /// </summary>
        public string UpdatedUserInfoId { get; set; }
        /// <summary>
        /// 更新時間，記錄角色群組最後更新的時間。
        /// </summary>
        public DateTime UpdatedTime { get; set; }
    }
}
