﻿using System;
using System.Collections.Generic;

namespace AlifeApi.DataAccess.ProjectContent
{
    /// <summary>
    /// 停車位基本資訊表 - 僅儲存車位的物理特性和位置資訊，銷售相關資訊由 Units 表管理
    /// </summary>
    public partial class ParkingSpace
    {
        /// <summary>
        /// 停車位唯一識別碼 (主鍵, 自動遞增)。
        /// </summary>
        public int ParkingSpaceId { get; set; }
        /// <summary>
        /// 所屬案場編號 (參考 Sites 表)。
        /// </summary>
        public string SiteCode { get; set; }
        /// <summary>
        /// 所屬建築物識別碼 (參考 Buildings 表)。
        /// </summary>
        public int BuildingId { get; set; }
        /// <summary>
        /// 所在樓層識別碼 (**參考 Floors 表中 FloorType 為 &apos;停車場&apos; 的樓層ID**, e.g., P1F, B1)。
        /// </summary>
        public int FloorId { get; set; }
        /// <summary>
        /// 車位編號 (例如 &quot;A01&quot;, &quot;B15&quot;, &quot;50&quot;)
        /// </summary>
        public string SpaceNumber { get; set; }
        /// <summary>
        /// 車位類型 (例如 &quot;坡道平面&quot;, &quot;機械&quot;, &quot;大型&quot;, &quot;小型&quot;, &quot;殘障&quot; - 建議關聯 SYS_Code)
        /// </summary>
        public string SpaceType { get; set; }
        /// <summary>
        /// 車位尺寸 (例如 &quot;250x550cm&quot;)。
        /// </summary>
        public string Dimensions { get; set; }
        /// <summary>
        /// 備註 (例如 &quot;柱子較多&quot;, &quot;出入較便利&quot;)
        /// </summary>
        public string Remarks { get; set; }
        /// <summary>
        /// 資料創建時間 (TIMESTAMP WITHOUT TIME ZONE)。
        /// </summary>
        public DateTime CreatedTime { get; set; }
        /// <summary>
        /// 資料最後更新時間 (TIMESTAMP WITHOUT TIME ZONE)。
        /// </summary>
        public DateTime UpdatedTime { get; set; }
        /// <summary>
        /// 建立者 (參考 UserInfo 表)。
        /// </summary>
        public string CreatedUserInfoId { get; set; }
        /// <summary>
        /// 更新者 (參考 UserInfo 表)。
        /// </summary>
        public string UpdatedUserInfoId { get; set; }
        /// <summary>
        /// 車位詳細位置描述 (例如 &quot;靠近電梯&quot;, &quot;角落位置&quot;)
        /// </summary>
        public string Location { get; set; }
    }
}
