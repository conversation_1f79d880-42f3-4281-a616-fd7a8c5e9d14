using System.Collections.Generic;

namespace AlifeApi.BusinessRules.ReviewModels
{
    /// <summary>
    /// 下一步驟輸出資料
    /// </summary>
    public class ReviewNextStepOutput
    {
        /// <summary>
        /// 步驟ID
        /// </summary>
        public int StepId { get; set; }

        /// <summary>
        /// 步驟名稱
        /// </summary>
        public string StepName { get; set; }

        /// <summary>
        /// 步驟順序
        /// </summary>
        public int Sequence { get; set; }

        /// <summary>
        /// 審核人員清單
        /// </summary>
        public List<ReviewApproverOutput> Approvers { get; set; }
    }
} 
