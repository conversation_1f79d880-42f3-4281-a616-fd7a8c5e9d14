#### [AlifeApi.BusinessRules](index.md 'index')
### [AlifeApi.BusinessRules.CategoryModels](AlifeApi.BusinessRules.CategoryModels.md 'AlifeApi.BusinessRules.CategoryModels')

## SmallCategoryDropdownOutput Class

小分類下拉選單輸出

```csharp
public class SmallCategoryDropdownOutput
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; SmallCategoryDropdownOutput
### Properties

<a name='AlifeApi.BusinessRules.CategoryModels.SmallCategoryDropdownOutput.LargeCategoryId'></a>

## SmallCategoryDropdownOutput.LargeCategoryId Property

所屬大分類ID

```csharp
public long LargeCategoryId { get; set; }
```

#### Property Value
[System.Int64](https://docs.microsoft.com/en-us/dotnet/api/System.Int64 'System.Int64')

<a name='AlifeApi.BusinessRules.CategoryModels.SmallCategoryDropdownOutput.LargeCategoryName'></a>

## SmallCategoryDropdownOutput.LargeCategoryName Property

所屬大分類名稱

```csharp
public string LargeCategoryName { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.CategoryModels.SmallCategoryDropdownOutput.MediumCategoryId'></a>

## SmallCategoryDropdownOutput.MediumCategoryId Property

所屬中分類ID

```csharp
public long MediumCategoryId { get; set; }
```

#### Property Value
[System.Int64](https://docs.microsoft.com/en-us/dotnet/api/System.Int64 'System.Int64')

<a name='AlifeApi.BusinessRules.CategoryModels.SmallCategoryDropdownOutput.MediumCategoryName'></a>

## SmallCategoryDropdownOutput.MediumCategoryName Property

所屬中分類名稱

```csharp
public string MediumCategoryName { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.CategoryModels.SmallCategoryDropdownOutput.Name'></a>

## SmallCategoryDropdownOutput.Name Property

小分類名稱

```csharp
public string Name { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.CategoryModels.SmallCategoryDropdownOutput.SmallCategoryId'></a>

## SmallCategoryDropdownOutput.SmallCategoryId Property

小分類ID

```csharp
public long SmallCategoryId { get; set; }
```

#### Property Value
[System.Int64](https://docs.microsoft.com/en-us/dotnet/api/System.Int64 'System.Int64')

<a name='AlifeApi.BusinessRules.CategoryModels.SmallCategoryDropdownOutput.SortOrder'></a>

## SmallCategoryDropdownOutput.SortOrder Property

排序順序

```csharp
public int SortOrder { get; set; }
```

#### Property Value
[System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')