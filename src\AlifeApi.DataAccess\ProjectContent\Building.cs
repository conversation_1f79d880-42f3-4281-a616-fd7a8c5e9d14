﻿using System;
using System.Collections.Generic;

namespace AlifeApi.DataAccess.ProjectContent
{
    /// <summary>
    /// 儲存案場內每一棟獨立建築的基本資訊。
    /// </summary>
    public partial class Building
    {
        /// <summary>
        /// 建築物唯一識別碼 (主鍵, 自動遞增)。
        /// </summary>
        public int BuildingId { get; set; }
        /// <summary>
        /// 所屬案場編號 (參考 Sites 表)。
        /// </summary>
        public string SiteCode { get; set; }
        /// <summary>
        /// 建築物名稱或編號 (例如: &quot;A棟&quot;, &quot;帝寶一期&quot;, &quot;Building 1&quot;)。
        /// </summary>
        public string BuildingName { get; set; }
        /// <summary>
        /// 該棟地上總樓層數。
        /// </summary>
        public int? TotalAboveGroundFloors { get; set; }
        /// <summary>
        /// 該棟地下總樓層數。
        /// </summary>
        public int? TotalBelowGroundFloors { get; set; }
        /// <summary>
        /// 建築類型 (例如: &quot;住宅&quot;, &quot;商辦&quot;, &quot;住商混合&quot; - 建議關聯 SYS_Code)。
        /// </summary>
        public string BuildingType { get; set; }
        /// <summary>
        /// 預計或實際完工日期。
        /// </summary>
        public DateOnly? CompletionDate { get; set; }
        /// <summary>
        /// 備註。
        /// </summary>
        public string Remarks { get; set; }
        /// <summary>
        /// 資料創建時間 (TIMESTAMP WITHOUT TIME ZONE)。
        /// </summary>
        public DateTime CreatedTime { get; set; }
        /// <summary>
        /// 資料最後更新時間 (TIMESTAMP WITHOUT TIME ZONE)。
        /// </summary>
        public DateTime UpdatedTime { get; set; }
        /// <summary>
        /// 建立者 (參考 UserInfo 表)。
        /// </summary>
        public string CreatedUserInfoId { get; set; }
        /// <summary>
        /// 更新者 (參考 UserInfo 表)。
        /// </summary>
        public string UpdatedUserInfoId { get; set; }
    }
}
