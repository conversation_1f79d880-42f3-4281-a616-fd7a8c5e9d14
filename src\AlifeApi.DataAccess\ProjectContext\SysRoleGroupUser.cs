﻿using System;
using System.Collections.Generic;

namespace AlifeApi.DataAccess.ProjectContext
{
    public partial class SysRoleGroupUser
    {
        /// <summary>
        /// 系統名稱
        /// </summary>
        public string System { get; set; }

        /// <summary>
        /// 角色群組
        /// </summary>
        public string RoleGroupId { get; set; }

        /// <summary>
        /// 權限群組人員
        /// </summary>
        public string UserId { get; set; }

        public virtual SysRoleGroup SysRoleGroup { get; set; }

        public virtual UserInfo User { get; set; }

    }
}
