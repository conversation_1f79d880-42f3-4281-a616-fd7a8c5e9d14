#### [AlifeApi.BusinessRules](index.md 'index')
### [AlifeApi.BusinessRules.Infrastructure](AlifeApi.BusinessRules.Infrastructure.md 'AlifeApi.BusinessRules.Infrastructure')

## ApiResult<TBody> Class

API 回傳的通用結果

```csharp
public class ApiResult<TBody>
```
#### Type parameters

<a name='AlifeApi.BusinessRules.Infrastructure.ApiResult_TBody_.TBody'></a>

`TBody`

回應資料的型別

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; ApiResult<TBody>

Derived  
&#8627; [ApiResult](AlifeApi.BusinessRules.Infrastructure.ApiResult.md 'AlifeApi.BusinessRules.Infrastructure.ApiResult')

### Remarks
前端模板接受大駝峰命名，但這個 API 的屬性應該使用小駝峰命名
### Properties

<a name='AlifeApi.BusinessRules.Infrastructure.ApiResult_TBody_.Body'></a>

## ApiResult<TBody>.Body Property

回傳資料

```csharp
public TBody Body { get; set; }
```

#### Property Value
[TBody](AlifeApi.BusinessRules.Infrastructure.ApiResult_TBody_.md#AlifeApi.BusinessRules.Infrastructure.ApiResult_TBody_.TBody 'AlifeApi.BusinessRules.Infrastructure.ApiResult<TBody>.TBody')

<a name='AlifeApi.BusinessRules.Infrastructure.ApiResult_TBody_.Code'></a>

## ApiResult<TBody>.Code Property

系統訊息的代碼

```csharp
public string Code { get; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.Infrastructure.ApiResult_TBody_.Exception'></a>

## ApiResult<TBody>.Exception Property

例外或資料檢核結果

```csharp
public string Exception { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.Infrastructure.ApiResult_TBody_.IsSuccess'></a>

## ApiResult<TBody>.IsSuccess Property

是否成功

```csharp
public bool IsSuccess { get; }
```

#### Property Value
[System.Boolean](https://docs.microsoft.com/en-us/dotnet/api/System.Boolean 'System.Boolean')

<a name='AlifeApi.BusinessRules.Infrastructure.ApiResult_TBody_.Message'></a>

## ApiResult<TBody>.Message Property

系統訊息的描述

```csharp
public string Message { get; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.Infrastructure.ApiResult_TBody_.Reponse'></a>

## ApiResult<TBody>.Reponse Property

系統訊息，預設 `Success`

```csharp
public System.Enum Reponse { get; set; }
```

#### Property Value
[System.Enum](https://docs.microsoft.com/en-us/dotnet/api/System.Enum 'System.Enum')