﻿using System;
using System.Collections.Generic;

namespace AlifeApi.DataAccess.ProjectContent
{
    /// <summary>
    /// 訂單項目表，記錄一張訂單包含的所有購買項目（房屋或車位）
    /// </summary>
    public partial class PurchaseOrderItem
    {
        public long OrderItemId { get; set; }
        public int OrderId { get; set; }
        /// <summary>
        /// 關聯的房屋單位ID (參考 Units 表)，如果此項目是車位，則為 NULL
        /// </summary>
        public int? UnitId { get; set; }
        /// <summary>
        /// 關聯的車位銷售資訊ID (參考 ParkingSpaces 表)，如果此項目是房屋，則為 NULL
        /// </summary>
        public int? ParkingSpaceId { get; set; }
        /// <summary>
        /// 成交價格
        /// </summary>
        public decimal ItemPrice { get; set; }
        /// <summary>
        /// 建立者 (參考 UserInfo 表)。
        /// </summary>
        public string CreatedUserInfoId { get; set; }
        /// <summary>
        /// 資料創建時間 (TIMESTAMP WITHOUT TIME ZONE)。
        /// </summary>
        public DateTime CreateTime { get; set; }
    }
}
