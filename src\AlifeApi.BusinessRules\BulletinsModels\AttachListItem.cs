﻿using System.ComponentModel.DataAnnotations;
using AlifeApi.Common.Util;

namespace AlifeApi.BusinessRules.BulletinsModels
{
    public class AttachListItem
    {
        /// <summary>
        /// 公告附件檔案名稱(須附副檔名)
        /// </summary>
        [MaxLength(300, ErrorMessage = "檔案名稱不能超過300個字符")]
        public string FileName { get; set; }

        /// <summary>
        /// 附件
        /// </summary>
        [LogIgnore]
        public byte[] File { get; set; }

    }
}
