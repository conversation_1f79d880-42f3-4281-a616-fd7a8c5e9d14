﻿using AlifeApi.BusinessRules.Infrastructure;
using AlifeApi.BusinessRules.SysCodeModels;
using AlifeApi.Common.Util;
using AlifeApi.DataAccess.ProjectContent;
using Microsoft.EntityFrameworkCore;

namespace AlifeApi.BusinessRules.SysCodeModels
{
    /// <summary>
    /// SYSCode
    /// </summary>
    public class SysCodeService : ServiceBase<alifeContext>
    {
        public SysCodeService(IServiceProvider serviceProvider, alifeContext dbContext)
            : base(serviceProvider, dbContext)
        {
        }

        /// <summary>
        /// 新增代碼
        /// </summary>
        /// <param name="input">The input.</param>
        /// <exception cref="ArgumentNullException">input</exception>
        public async Task CreateSysCodeAsync(SysCodeCreateInput input)
        {
            ArgumentNullException.ThrowIfNull(input, nameof(input));

            if (string.IsNullOrEmpty(input.ParentCode))
            {
                input.ParentCode = null;
            }
            else
            {
                input.ParentCode = (await Db.SysCodes.SingleAsync(x => x.Code == input.ParentCode))?.Code;
            }

            int order = Db.SysCodes.Where(x => x.Type == input.Type)
                .AsEnumerable()
                .Max(x => int.Parse(x.Code)) + 1;

            Db.SysCodes.Add(new SysCode
            {
                Type = input.Type,
                Code = order.ToString(),
                ParentCode = input.ParentCode,
                CodeDesc = input.Desc,
                CodeOrder = (short)order,
                CreatedUserInfoId = CurrentUser.UserId,
                CreatedTime = DateTime.Now,
                UpdatedUserInfoId = CurrentUser.UserId,
                UpdatedTime = DateTime.Now,
            });

            await Db.SaveChangesAsync();
        }

        /// <summary>
        /// 編輯、作廢代碼
        /// </summary>
        /// <param name="input">The input.</param>
        /// <exception cref="ArgumentNullException">input</exception>
        public async Task UpdateSysCodeAsync(SysCodeUpdateInput input)
        {
            ArgumentNullException.ThrowIfNull(input, nameof(input));

            var entity = await Db.SysCodes
                .FirstOrDefaultAsync(s => s.Type == input.Type && s.Code == input.Code);
            if (entity == null)
                throw new Exception($"找不到代碼 {input.Type}-{input.Code}");

            ValueMapper<SysCodeUpdateInput, SysCode> mapper = new(input, entity);
            mapper.MapIfHasValue(x => x.Desc, x => x.CodeDesc);
            mapper.MapIfHasValue(x => x.IsActive, x => x.IsActive);

            await Db.SaveChangesAsync();
        }

        /// <summary>
        /// 依 Type 取得系統代碼
        /// </summary>
        /// <param name="input">The input.</param>
        /// <returns>系統代碼</returns>
        /// <exception cref="ArgumentNullException">input</exception>
        public async Task<PagedListOutput<SysCodeOutput>> GetSysCodeListByTypeAsync(SysCodeByTypeGetInput input)
        {
            ArgumentNullException.ThrowIfNull(input, nameof(input));

            if (string.IsNullOrEmpty(input.Type))
            {
                return new PagedListOutput<SysCodeOutput>(input);
            }

            IQueryable<SysCode> source = Db.SysCodes.AsNoTracking()
                .Where(x => x.Type == input.Type && x.ParentCode == null);

            if (input.SortOrderInfos?.Any() != true)
            {
                source = source.OrderBy(x => x.CodeOrder);
            }

            return await source
                .Select(x => new SysCodeOutput
                {
                    Value = x.Code,
                    Label = x.CodeDesc,
                    IsActive = x.IsActive ?? true,
                    Order = x.CodeOrder ?? 0
                })
                .ToPagedListOutputAsync(input);
        }

        /// <summary>
        /// 取得所有 SysCode 類型 (不分頁)
        /// </summary>
        public async Task<List<string>> GetAllSysCodeTypesAsync()
        {
            return await Db.SysCodes
                .AsNoTracking()
                .Select(x => x.Type)
                .Distinct()
                .ToListAsync();
        }

        /// <summary>
        /// 根據類型取得所有 SysCode 條目 (不分頁)
        /// </summary>
        public async Task<List<SysCodeOutput>> GetSysCodesByTypeAsync(string type)
        {
            return await Db.SysCodes
                .AsNoTracking()
                .Where(x => x.Type == type)
                .Select(x => new SysCodeOutput
                {
                    Value = x.Code,
                    Label = x.CodeDesc,
                    IsActive = x.IsActive ?? true,
                    Order = x.CodeOrder ?? 0
                })
                .ToListAsync();
        }
    }
}
