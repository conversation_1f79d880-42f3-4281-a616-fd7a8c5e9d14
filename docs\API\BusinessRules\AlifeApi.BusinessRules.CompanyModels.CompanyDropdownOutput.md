#### [AlifeApi.BusinessRules](index.md 'index')
### [AlifeApi.BusinessRules.CompanyModels](AlifeApi.BusinessRules.CompanyModels.md 'AlifeApi.BusinessRules.CompanyModels')

## CompanyDropdownOutput Class

公司下拉選單輸出資料

```csharp
public class CompanyDropdownOutput
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; CompanyDropdownOutput
### Properties

<a name='AlifeApi.BusinessRules.CompanyModels.CompanyDropdownOutput.Name'></a>

## CompanyDropdownOutput.Name Property

名稱

```csharp
public string Name { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.CompanyModels.CompanyDropdownOutput.Value'></a>

## CompanyDropdownOutput.Value Property

值

```csharp
public string Value { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')