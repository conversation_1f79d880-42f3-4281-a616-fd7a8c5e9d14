﻿CREATE TABLE [dbo].[UserPasswordHistory] (
    [Id]            BIGINT        IDENTITY (1, 1) NOT NULL,
    [UserId]        VARCHAR (15)  NOT NULL,
    [Pw]            VARCHAR (255) NOT NULL,
    [CreatedUserId] VARCHAR (15)  NOT NULL,
    [CreatedTime]   DATETIME      NOT NULL,
    CONSTRAINT [PK_UserPasswordHistory] PRIMARY KEY CLUSTERED ([Id] ASC),
    CONSTRAINT [FK_UserPasswordHistory_UserInfo] FOREIGN KEY ([UserId]) REFERENCES [dbo].[UserInfo] ([Id])
);




GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'密碼歷史資料', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'UserPasswordHistory';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'流水號', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'UserPasswordHistory', @level2type = N'COLUMN', @level2name = N'Id';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'員工編號', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'UserPasswordHistory', @level2type = N'COLUMN', @level2name = N'UserId';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'密碼', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'UserPasswordHistory', @level2type = N'COLUMN', @level2name = N'Pw';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'建立者', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'UserPasswordHistory', @level2type = N'COLUMN', @level2name = N'CreatedUserId';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'建立時間', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'UserPasswordHistory', @level2type = N'COLUMN', @level2name = N'CreatedTime';

