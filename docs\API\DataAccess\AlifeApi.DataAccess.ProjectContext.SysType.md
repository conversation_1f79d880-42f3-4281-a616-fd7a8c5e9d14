#### [AlifeApi.DataAccess](index.md 'index')
### [AlifeApi.DataAccess.ProjectContext](AlifeApi.DataAccess.ProjectContext.md 'AlifeApi.DataAccess.ProjectContext')

## SysType Class

代碼類型資料

```csharp
public class SysType
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; SysType
### Properties

<a name='AlifeApi.DataAccess.ProjectContext.SysType.CanEdit'></a>

## SysType.CanEdit Property

是否可編輯

```csharp
public bool CanEdit { get; set; }
```

#### Property Value
[System.Boolean](https://docs.microsoft.com/en-us/dotnet/api/System.Boolean 'System.Boolean')

<a name='AlifeApi.DataAccess.ProjectContext.SysType.Type'></a>

## SysType.Type Property

類型

```csharp
public string Type { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContext.SysType.TypeDesc'></a>

## SysType.TypeDesc Property

描述

```csharp
public string TypeDesc { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')