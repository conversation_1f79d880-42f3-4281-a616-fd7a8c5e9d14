#### [AlifeApi.BusinessRules](index.md 'index')
### [AlifeApi.BusinessRules.UserInfoModels](AlifeApi.BusinessRules.UserInfoModels.md 'AlifeApi.BusinessRules.UserInfoModels')

## UserInfoCreateInput Class

新增人員request資料模型

```csharp
public class UserInfoCreateInput
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; UserInfoCreateInput

Derived  
&#8627; [UserInfoUpdateInput](AlifeApi.BusinessRules.UserInfoModels.UserInfoUpdateInput.md 'AlifeApi.BusinessRules.UserInfoModels.UserInfoUpdateInput')
### Properties

<a name='AlifeApi.BusinessRules.UserInfoModels.UserInfoCreateInput.DeptId'></a>

## UserInfoCreateInput.DeptId Property

部門

```csharp
public string DeptId { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.UserInfoModels.UserInfoCreateInput.GradeCode'></a>

## UserInfoCreateInput.GradeCode Property

職別

```csharp
public string GradeCode { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.UserInfoModels.UserInfoCreateInput.Roles'></a>

## UserInfoCreateInput.Roles Property

角色

```csharp
public System.Collections.Generic.IEnumerable<string> Roles { get; set; }
```

#### Property Value
[System.Collections.Generic.IEnumerable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Collections.Generic.IEnumerable-1 'System.Collections.Generic.IEnumerable`1')[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Collections.Generic.IEnumerable-1 'System.Collections.Generic.IEnumerable`1')

<a name='AlifeApi.BusinessRules.UserInfoModels.UserInfoCreateInput.UserEmail'></a>

## UserInfoCreateInput.UserEmail Property

電子郵件

```csharp
public string UserEmail { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.UserInfoModels.UserInfoCreateInput.UserId'></a>

## UserInfoCreateInput.UserId Property

員工編號

```csharp
public string UserId { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.UserInfoModels.UserInfoCreateInput.UserIdNo'></a>

## UserInfoCreateInput.UserIdNo Property

身分證字號

```csharp
public string UserIdNo { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.UserInfoModels.UserInfoCreateInput.UserName'></a>

## UserInfoCreateInput.UserName Property

人員姓名

```csharp
public string UserName { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.UserInfoModels.UserInfoCreateInput.UserPw'></a>

## UserInfoCreateInput.UserPw Property

密碼

```csharp
public string UserPw { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')