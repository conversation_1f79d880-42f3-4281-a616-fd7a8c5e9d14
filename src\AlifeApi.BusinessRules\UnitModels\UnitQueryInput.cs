using System;
using System.ComponentModel.DataAnnotations;
using AlifeApi.BusinessRules.Infrastructure;

namespace AlifeApi.BusinessRules.UnitModels
{
    /// <summary>
    /// 房屋單位列表查詢輸入模型
    /// </summary>
    public class UnitQueryInput : PagedListInput
    {
        [Required(ErrorMessage = "必須提供樓層ID")]
        public int FloorId { get; set; }

        /// <summary>
        /// 戶號 (篩選)
        /// </summary>
        public string? UnitNumber { get; set; }

        /// <summary>
        /// 狀態 (篩選)
        /// </summary>
        public string? Status { get; set; }

        /// <summary>
        /// 最小總坪數 (篩選)
        /// </summary>
        public decimal? MinTotalArea { get; set; }

        /// <summary>
        /// 最大總坪數 (篩選)
        /// </summary>
        public decimal? MaxTotalArea { get; set; }
    }
} 
