#### [AlifeApi.BusinessRules](index.md 'index')
### [AlifeApi.BusinessRules.BulletinsModels](AlifeApi.BusinessRules.BulletinsModels.md 'AlifeApi.BusinessRules.BulletinsModels')

## GetBulletinsResult Class

```csharp
public class GetBulletinsResult
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; GetBulletinsResult
### Properties

<a name='AlifeApi.BusinessRules.BulletinsModels.GetBulletinsResult.AttachList'></a>

## GetBulletinsResult.AttachList Property

公告附檔清單

```csharp
public System.Collections.Generic.List<AlifeApi.BusinessRules.BulletinsModels.AttachListItem> AttachList { get; set; }
```

#### Property Value
[System.Collections.Generic.List&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Collections.Generic.List-1 'System.Collections.Generic.List`1')[AttachListItem](AlifeApi.BusinessRules.BulletinsModels.AttachListItem.md 'AlifeApi.BusinessRules.BulletinsModels.AttachListItem')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Collections.Generic.List-1 'System.Collections.Generic.List`1')

<a name='AlifeApi.BusinessRules.BulletinsModels.GetBulletinsResult.BL_Id'></a>

## GetBulletinsResult.BL_Id Property

公告唯一識別值

```csharp
public int BL_Id { get; set; }
```

#### Property Value
[System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')

<a name='AlifeApi.BusinessRules.BulletinsModels.GetBulletinsResult.Content'></a>

## GetBulletinsResult.Content Property

公告內文

```csharp
public string Content { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.BulletinsModels.GetBulletinsResult.CreateDate'></a>

## GetBulletinsResult.CreateDate Property

建立時間

```csharp
public System.Nullable<System.DateTime> CreateDate { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.DateTime](https://docs.microsoft.com/en-us/dotnet/api/System.DateTime 'System.DateTime')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

<a name='AlifeApi.BusinessRules.BulletinsModels.GetBulletinsResult.CreateUserId'></a>

## GetBulletinsResult.CreateUserId Property

建立者帳號

```csharp
public string CreateUserId { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.BulletinsModels.GetBulletinsResult.IsRead'></a>

## GetBulletinsResult.IsRead Property

是否看過

```csharp
public bool IsRead { get; set; }
```

#### Property Value
[System.Boolean](https://docs.microsoft.com/en-us/dotnet/api/System.Boolean 'System.Boolean')

<a name='AlifeApi.BusinessRules.BulletinsModels.GetBulletinsResult.IsTop'></a>

## GetBulletinsResult.IsTop Property

是否至頂

```csharp
public bool IsTop { get; set; }
```

#### Property Value
[System.Boolean](https://docs.microsoft.com/en-us/dotnet/api/System.Boolean 'System.Boolean')

<a name='AlifeApi.BusinessRules.BulletinsModels.GetBulletinsResult.PostFrom'></a>

## GetBulletinsResult.PostFrom Property

公告起始日期

```csharp
public System.Nullable<System.DateTime> PostFrom { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.DateTime](https://docs.microsoft.com/en-us/dotnet/api/System.DateTime 'System.DateTime')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

<a name='AlifeApi.BusinessRules.BulletinsModels.GetBulletinsResult.PostTo'></a>

## GetBulletinsResult.PostTo Property

公告結束日期

```csharp
public System.Nullable<System.DateTime> PostTo { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.DateTime](https://docs.microsoft.com/en-us/dotnet/api/System.DateTime 'System.DateTime')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

<a name='AlifeApi.BusinessRules.BulletinsModels.GetBulletinsResult.Title'></a>

## GetBulletinsResult.Title Property

公告標題

```csharp
public string Title { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.BulletinsModels.GetBulletinsResult.UpdateDate'></a>

## GetBulletinsResult.UpdateDate Property

更新時間

```csharp
public System.Nullable<System.DateTime> UpdateDate { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.DateTime](https://docs.microsoft.com/en-us/dotnet/api/System.DateTime 'System.DateTime')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

<a name='AlifeApi.BusinessRules.BulletinsModels.GetBulletinsResult.UpdateUserId'></a>

## GetBulletinsResult.UpdateUserId Property

更新者帳號

```csharp
public string UpdateUserId { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.BulletinsModels.GetBulletinsResult.ViewCount'></a>

## GetBulletinsResult.ViewCount Property

瀏覽次數

```csharp
public int ViewCount { get; set; }
```

#### Property Value
[System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')