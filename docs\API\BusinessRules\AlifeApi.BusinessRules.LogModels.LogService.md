#### [AlifeApi.BusinessRules](index.md 'index')
### [AlifeApi.BusinessRules.LogModels](AlifeApi.BusinessRules.LogModels.md 'AlifeApi.BusinessRules.LogModels')

## LogService Class

LOG 商業邏輯

```csharp
public class LogService : AlifeApi.BusinessRules.Infrastructure.ServiceBase<LogcontextPg.LogContextPg>
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; [AlifeApi.Common.InfrastructureBase](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.Common.InfrastructureBase 'AlifeApi.Common.InfrastructureBase') &#129106; [AlifeApi.BusinessRules.Infrastructure.ServiceBase&lt;](AlifeApi.BusinessRules.Infrastructure.ServiceBase_TDbContext_.md 'AlifeApi.BusinessRules.Infrastructure.ServiceBase<TDbContext>')[LogcontextPg.LogContextPg](https://docs.microsoft.com/en-us/dotnet/api/LogcontextPg.LogContextPg 'LogcontextPg.LogContextPg')[&gt;](AlifeApi.BusinessRules.Infrastructure.ServiceBase_TDbContext_.md 'AlifeApi.BusinessRules.Infrastructure.ServiceBase<TDbContext>') &#129106; LogService
### Constructors

<a name='AlifeApi.BusinessRules.LogModels.LogService.LogService(System.IServiceProvider,LogcontextPg.LogContextPg)'></a>

## LogService(IServiceProvider, LogContextPg) Constructor

Initializes a new instance of the [LogService](AlifeApi.BusinessRules.LogModels.LogService.md 'AlifeApi.BusinessRules.LogModels.LogService') class.

```csharp
public LogService(System.IServiceProvider serviceProvider, LogcontextPg.LogContextPg dbContext);
```
#### Parameters

<a name='AlifeApi.BusinessRules.LogModels.LogService.LogService(System.IServiceProvider,LogcontextPg.LogContextPg).serviceProvider'></a>

`serviceProvider` [System.IServiceProvider](https://docs.microsoft.com/en-us/dotnet/api/System.IServiceProvider 'System.IServiceProvider')

The service provider.

<a name='AlifeApi.BusinessRules.LogModels.LogService.LogService(System.IServiceProvider,LogcontextPg.LogContextPg).dbContext'></a>

`dbContext` [LogcontextPg.LogContextPg](https://docs.microsoft.com/en-us/dotnet/api/LogcontextPg.LogContextPg 'LogcontextPg.LogContextPg')

The database context.
### Methods

<a name='AlifeApi.BusinessRules.LogModels.LogService.CreateApiLogAsync(AlifeApi.BusinessRules.LogModels.ApiLogCreateInput)'></a>

## LogService.CreateApiLogAsync(ApiLogCreateInput) Method

Writes the API log asynchronous.

```csharp
public System.Threading.Tasks.Task CreateApiLogAsync(AlifeApi.BusinessRules.LogModels.ApiLogCreateInput input);
```
#### Parameters

<a name='AlifeApi.BusinessRules.LogModels.LogService.CreateApiLogAsync(AlifeApi.BusinessRules.LogModels.ApiLogCreateInput).input'></a>

`input` [ApiLogCreateInput](AlifeApi.BusinessRules.LogModels.ApiLogCreateInput.md 'AlifeApi.BusinessRules.LogModels.ApiLogCreateInput')

The input.

#### Returns
[System.Threading.Tasks.Task](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task 'System.Threading.Tasks.Task')