﻿using System;
using System.Collections.Generic;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata;

namespace AlifeApi.DataAccess.LogContext
{
    public partial class LogContext : DbContext
    {
        public LogContext(DbContextOptions<LogContext> options) : base(options)
        {
        }

        public virtual DbSet<SysApiLog> SysApiLog { get; set; }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            modelBuilder.Entity<SysApiLog>(entity =>
            {
                entity.ToTable("SYS_ApiLog");

                entity.Property(e => e.ActionName)
                    .IsRequired()
                    .HasMaxLength(100)
                    .IsUnicode(false)
                    .HasComment("執行方法名稱");

                entity.Property(e => e.ControllerName)
                    .IsRequired()
                    .HasMaxLength(100)
                    .IsUnicode(false)
                    .HasComment("控制器名稱");

                entity.Property(e => e.Exception)
                    .IsRequired()
                    .HasComment("錯誤異常");

                entity.Property(e => e.ExecutedTime)
                    .HasColumnType("datetime")
                    .HasComment("操作時間");

                entity.Property(e => e.Headers)
                    .IsRequired()
                    .HasMaxLength(4000)
                    .HasComment("請求標頭");

                entity.Property(e => e.InputData)
                    .IsRequired()
                    .HasComment("輸入資料");

                entity.Property(e => e.OutputData)
                    .IsRequired()
                    .HasComment("輸出資料");

                entity.Property(e => e.Seconds)
                    .HasColumnType("decimal(5, 2)")
                    .HasComment("執行時間（秒）");

                entity.Property(e => e.SessionId)
                    .IsRequired()
                    .HasMaxLength(50)
                    .IsUnicode(false)
                    .HasComment("Session ID");

                entity.Property(e => e.Source)
                    .IsRequired()
                    .HasMaxLength(200)
                    .IsUnicode(false)
                    .HasComment("請求來源");

                entity.Property(e => e.System)
                    .IsRequired()
                    .HasMaxLength(30)
                    .IsUnicode(false)
                    .HasComment("系統名稱");
            });

            OnModelCreatingPartial(modelBuilder);
        }

        partial void OnModelCreatingPartial(ModelBuilder modelBuilder);
    }
}
