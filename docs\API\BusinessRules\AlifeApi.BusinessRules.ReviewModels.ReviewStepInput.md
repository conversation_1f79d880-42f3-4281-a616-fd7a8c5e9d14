#### [AlifeApi.BusinessRules](index.md 'index')
### [AlifeApi.BusinessRules.ReviewModels](AlifeApi.BusinessRules.ReviewModels.md 'AlifeApi.BusinessRules.ReviewModels')

## ReviewStepInput Class

審核步驟輸入模型

```csharp
public class ReviewStepInput
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; ReviewStepInput
### Properties

<a name='AlifeApi.BusinessRules.ReviewModels.ReviewStepInput.ApproverIds'></a>

## ReviewStepInput.ApproverIds Property

審核人員清單

```csharp
public System.Collections.Generic.List<string> ApproverIds { get; set; }
```

#### Property Value
[System.Collections.Generic.List&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Collections.Generic.List-1 'System.Collections.Generic.List`1')[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Collections.Generic.List-1 'System.Collections.Generic.List`1')

<a name='AlifeApi.BusinessRules.ReviewModels.ReviewStepInput.EmailNotification'></a>

## ReviewStepInput.EmailNotification Property

電子郵件通知

```csharp
public bool EmailNotification { get; set; }
```

#### Property Value
[System.Boolean](https://docs.microsoft.com/en-us/dotnet/api/System.Boolean 'System.Boolean')

<a name='AlifeApi.BusinessRules.ReviewModels.ReviewStepInput.Name'></a>

## ReviewStepInput.Name Property

步驟名稱

```csharp
public string Name { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.ReviewModels.ReviewStepInput.SmsNotification'></a>

## ReviewStepInput.SmsNotification Property

簡訊通知

```csharp
public bool SmsNotification { get; set; }
```

#### Property Value
[System.Boolean](https://docs.microsoft.com/en-us/dotnet/api/System.Boolean 'System.Boolean')

<a name='AlifeApi.BusinessRules.ReviewModels.ReviewStepInput.SystemNotification'></a>

## ReviewStepInput.SystemNotification Property

系統通知

```csharp
public bool SystemNotification { get; set; }
```

#### Property Value
[System.Boolean](https://docs.microsoft.com/en-us/dotnet/api/System.Boolean 'System.Boolean')

<a name='AlifeApi.BusinessRules.ReviewModels.ReviewStepInput.TimeLimit'></a>

## ReviewStepInput.TimeLimit Property

時限（小時）

```csharp
public int TimeLimit { get; set; }
```

#### Property Value
[System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')