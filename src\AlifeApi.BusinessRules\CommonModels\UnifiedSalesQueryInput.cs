using AlifeApi.BusinessRules.Infrastructure;
using System.Collections.Generic;

namespace <PERSON>feApi.BusinessRules.CommonModels
{
    /// <summary>
    /// 統一銷售查詢輸入
    /// </summary>
    public class UnifiedSalesQueryInput : PagedListInput
    {
        /// <summary>
        /// 案場編號
        /// </summary>
        public string SiteCode { get; set; }

        /// <summary>
        /// 建築物ID
        /// </summary>
        public int? BuildingId { get; set; }

        /// <summary>
        /// 關鍵字查詢
        /// </summary>
        public string? Keyword { get; set; }

        /// <summary>
        /// 狀態列表
        /// </summary>
        public List<string>? Status { get; set; }

        /// <summary>
        /// 排序欄位
        /// </summary>
        public string? SortBy { get; set; }

        /// <summary>
        /// 排序方向 (asc/desc)
        /// </summary>
        public string? SortDirection { get; set; }

        public int Skip { get; set; } = 0;
        public int Take { get; set; } = 10;
    }
} 
