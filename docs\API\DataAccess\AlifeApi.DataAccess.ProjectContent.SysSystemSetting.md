#### [AlifeApi.DataAccess](index.md 'index')
### [AlifeApi.DataAccess.ProjectContent](AlifeApi.DataAccess.ProjectContent.md 'AlifeApi.DataAccess.ProjectContent')

## SysSystemSetting Class

系統設定表，用於儲存系統的各種設定鍵值對資訊。

```csharp
public class SysSystemSetting
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; SysSystemSetting
### Properties

<a name='AlifeApi.DataAccess.ProjectContent.SysSystemSetting.CreatedTime'></a>

## SysSystemSetting.CreatedTime Property

創建時間，記錄設定創建的時間。

```csharp
public System.DateTime CreatedTime { get; set; }
```

#### Property Value
[System.DateTime](https://docs.microsoft.com/en-us/dotnet/api/System.DateTime 'System.DateTime')

<a name='AlifeApi.DataAccess.ProjectContent.SysSystemSetting.CreatedUserInfoId'></a>

## SysSystemSetting.CreatedUserInfoId Property

建立者，記錄創建此設定的員工編號，對應 UserInfo 表的 UserInfoId。

```csharp
public string CreatedUserInfoId { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContent.SysSystemSetting.IsEnabled'></a>

## SysSystemSetting.IsEnabled Property

是否啟用，true 表示啟用，false 表示停用，預設為 true。

```csharp
public System.Nullable<bool> IsEnabled { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.Boolean](https://docs.microsoft.com/en-us/dotnet/api/System.Boolean 'System.Boolean')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

<a name='AlifeApi.DataAccess.ProjectContent.SysSystemSetting.Key'></a>

## SysSystemSetting.Key Property

鍵，主鍵的一部分，用於唯一識別系統設定項目。

```csharp
public string Key { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContent.SysSystemSetting.Name'></a>

## SysSystemSetting.Name Property

詳細名稱，描述設定的具體用途或含義。

```csharp
public string Name { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContent.SysSystemSetting.Type'></a>

## SysSystemSetting.Type Property

類別，用於區分不同的設定類型。

```csharp
public string Type { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContent.SysSystemSetting.UpdatedTime'></a>

## SysSystemSetting.UpdatedTime Property

更新時間，記錄設定最後更新的時間。

```csharp
public System.DateTime UpdatedTime { get; set; }
```

#### Property Value
[System.DateTime](https://docs.microsoft.com/en-us/dotnet/api/System.DateTime 'System.DateTime')

<a name='AlifeApi.DataAccess.ProjectContent.SysSystemSetting.UpdatedUserInfoId'></a>

## SysSystemSetting.UpdatedUserInfoId Property

更新者，記錄最後更新此設定的員工編號，對應 UserInfo 表的 UserInfoId。

```csharp
public string UpdatedUserInfoId { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContent.SysSystemSetting.Value'></a>

## SysSystemSetting.Value Property

值，儲存設定的具體值，可為空。

```csharp
public string Value { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')