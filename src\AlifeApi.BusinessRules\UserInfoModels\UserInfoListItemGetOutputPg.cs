using System.Text.Json.Serialization;

namespace AlifeApi.BusinessRules.UserInfoModels
{
    /// <summary>
    /// 使用者列表項目輸出資料 (PostgreSQL版本)
    /// </summary>
    public class UserInfoListItemGetOutputPg
    {
        /// <summary>
        /// 使用者ID
        /// </summary>
        [JsonPropertyName("UserInfoId")]
        public string UserInfoId { get; set; }

        /// <summary>
        /// 姓名
        /// </summary>
        [JsonPropertyName("Name")]
        public string Name { get; set; }

        /// <summary>
        /// 電子郵件
        /// </summary>
        [JsonPropertyName("Email")]
        public string Email { get; set; }

        /// <summary>
        /// 手機
        /// </summary>
        [JsonPropertyName("MobileNumber")]
        public string MobileNumber { get; set; }

        /// <summary>
        /// 部門ID
        /// </summary>
        [JsonPropertyName("DepartmentId")]
        public string DepartmentId { get; set; }

        /// <summary>
        /// 部門名稱
        /// </summary>
        [JsonPropertyName("DepartmentName")]
        public string DepartmentName { get; set; }

        /// <summary>
        /// 職稱ID
        /// </summary>
        [JsonPropertyName("JobTitleId")]
        public string JobTitleId { get; set; }

        /// <summary>
        /// 職稱名稱
        /// </summary>
        [JsonPropertyName("JobTitleName")]
        public string JobTitleName { get; set; }

        /// <summary>
        /// 帳號狀態
        /// </summary>
        [JsonPropertyName("Status")]
        public bool Status { get; set; }

        /// <summary>
        /// 是否為管理員
        /// </summary>
        [JsonPropertyName("IsAdmin")]
        public bool IsAdmin { get; set; }

        /// <summary>
        /// 最後登入時間
        /// </summary>
        [JsonPropertyName("LastLoginTime")]
        public DateTime? LastLoginTime { get; set; }

        /// <summary>
        /// 最後登入IP
        /// </summary>
        [JsonPropertyName("LastLoginIp")]
        public string LastLoginIp { get; set; }

        /// <summary>
        /// 登入失敗次數
        /// </summary>
        [JsonPropertyName("LoginFailedCount")]
        public int LoginFailedCount { get; set; }

        /// <summary>
        /// 是否鎖定
        /// </summary>
        [JsonPropertyName("IsLock")]
        public bool IsLock { get; set; }

        /// <summary>
        /// 角色群組列表
        /// </summary>
        [JsonPropertyName("RoleGroups")]
        public List<RoleGroupOutput> RoleGroups { get; set; } = new();

        /// <summary>
        /// 公司ID
        /// </summary>
        [JsonPropertyName("CompanyId")]
        public string CompanyId { get; set; }

        /// <summary>
        /// 性別
        /// </summary>
        [JsonPropertyName("Gender")]
        public string Gender { get; set; }

        /// <summary>
        /// 出生日期
        /// </summary>
        [JsonPropertyName("BirthDate")]
        public DateTime? BirthDate { get; set; }

        /// <summary>
        /// 電話號碼
        /// </summary>
        [JsonPropertyName("TelephoneNumber")]
        public string TelephoneNumber { get; set; }

        /// <summary>
        /// 戶籍地址
        /// </summary>
        [JsonPropertyName("RegisteredAddress")]
        public string RegisteredAddress { get; set; }

        /// <summary>
        /// 通訊地址
        /// </summary>
        [JsonPropertyName("MailingAddress")]
        public string MailingAddress { get; set; }

        /// <summary>
        /// 緊急聯絡人姓名
        /// </summary>
        [JsonPropertyName("EmergencyContactName")]
        public string EmergencyContactName { get; set; }

        /// <summary>
        /// 緊急聯絡人電話
        /// </summary>
        [JsonPropertyName("EmergencyContactPhone")]
        public string EmergencyContactPhone { get; set; }

        /// <summary>
        /// 與緊急聯絡人的關係
        /// </summary>
        [JsonPropertyName("EmergencyContactRelation")]
        public string EmergencyContactRelation { get; set; }

        /// <summary>
        /// 服務單位
        /// </summary>
        [JsonPropertyName("ServiceUnit")]
        public string ServiceUnit { get; set; }

        /// <summary>
        /// 到職日期
        /// </summary>
        [JsonPropertyName("HireDate")]
        public DateTime? HireDate { get; set; }

        /// <summary>
        /// 最後登出時間
        /// </summary>
        [JsonPropertyName("LastLogoutTime")]
        public DateTime? LastLogoutTime { get; set; }

        /// <summary>
        /// 是否為內場/外場人員
        /// </summary>
        [JsonPropertyName("IsInside")]
        public bool? IsInside { get; set; }

        /// <summary>
        /// 是否為 M365 帳號
        /// </summary>
        [JsonPropertyName("IsM365")]
        public bool? IsM365 { get; set; }

        /// <summary>
        /// 是否需要發送電子郵件通知
        /// </summary>
        [JsonPropertyName("IsEmailNotificationEnabled")]
        public bool? IsEmailNotificationEnabled { get; set; }

        /// <summary>
        /// 身分證字號
        /// </summary>
        [JsonPropertyName("Identity")]
        public string Identity { get; set; }
    }

    /// <summary>
    /// 角色群組輸出資料
    /// </summary>
    public class RoleGroupOutput
    {
        /// <summary>
        /// 角色群組ID
        /// </summary>
        [JsonPropertyName("RoleGroupId")]
        public string RoleGroupId { get; set; }

        /// <summary>
        /// 名稱
        /// </summary>
        [JsonPropertyName("Name")]
        public string Name { get; set; }
    }
} 
