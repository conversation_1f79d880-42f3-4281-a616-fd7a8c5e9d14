﻿CREATE TABLE [dbo].[UserGrade] (
    [Id]            VARCHAR (20)  NOT NULL,
    [GradeName]     NVARCHAR (30) NOT NULL,
    [ParentGradeId] VARCHAR (20)  NULL,
    [IsDisabled]    BIT           NOT NULL,
    [CreatedUserId] VARCHAR (15)  NOT NULL,
    [CreatedTime]   DATETIME      NOT NULL,
    [UpdatedUserId] VARCHAR (15)  NOT NULL,
    [UpdatedTime]   DATETIME      NOT NULL,
    CONSTRAINT [PK_UserGrade] PRIMARY KEY CLUSTERED ([Id] ASC),
    CONSTRAINT [FK_UserGrade_UserGrade] FOREIGN KEY ([ParentGradeId]) REFERENCES [dbo].[UserGrade] ([Id])
);


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'更新時間', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'UserGrade', @level2type = N'COLUMN', @level2name = N'UpdatedTime';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'更新者', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'UserGrade', @level2type = N'COLUMN', @level2name = N'UpdatedUserId';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'建立時間', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'UserGrade', @level2type = N'COLUMN', @level2name = N'CreatedTime';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'建立者', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'UserGrade', @level2type = N'COLUMN', @level2name = N'CreatedUserId';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'是否刪除', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'UserGrade', @level2type = N'COLUMN', @level2name = N'IsDisabled';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'上層職稱代碼', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'UserGrade', @level2type = N'COLUMN', @level2name = N'ParentGradeId';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'職稱', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'UserGrade', @level2type = N'COLUMN', @level2name = N'GradeName';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'職稱代碼', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'UserGrade', @level2type = N'COLUMN', @level2name = N'Id';

