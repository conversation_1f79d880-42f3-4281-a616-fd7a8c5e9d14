﻿using System;
using System.Collections.Generic;

namespace AlifeApi.DataAccess.ProjectContent
{
    /// <summary>
    /// 專案支出紀錄表，記錄每個案場的各項開銷
    /// </summary>
    public partial class ProjectExpense
    {
        /// <summary>
        /// 主鍵，支出紀錄唯一識別碼
        /// </summary>
        public long ProjectExpenseId { get; set; }
        /// <summary>
        /// 案場代碼，指明此筆支出屬於哪個案場
        /// </summary>
        public string SiteCode { get; set; }
        /// <summary>
        /// 關聯的小分類ID (需由應用程式確保其有效性)
        /// </summary>
        public long SmallCategoryId { get; set; }
        /// <summary>
        /// 支出金額
        /// </summary>
        public decimal Amount { get; set; }
        /// <summary>
        /// 支出發生的日期
        /// </summary>
        public DateOnly ExpenseDate { get; set; }
        /// <summary>
        /// 關於此筆支出的額外文字描述或備註
        /// </summary>
        public string Description { get; set; }
        /// <summary>
        /// 建立者，記錄創建此資料的員工編號。
        /// </summary>
        public string CreatedUserInfoId { get; set; }
        /// <summary>
        /// 創建時間，記錄資料創建的時間。
        /// </summary>
        public DateTime CreateTime { get; set; }
        /// <summary>
        /// 更新者，記錄最後更新此資料的員工編號。
        /// </summary>
        public string UpdatedUserInfoId { get; set; }
        /// <summary>
        /// 更新時間，記錄資料最後更新的時間。
        /// </summary>
        public DateTime UpdateTime { get; set; }
    }
}
