using System;

namespace AlifeApi.BusinessRules.CrmOptionModels
{
    /// <summary>
    /// CRM選項詳細輸出模型
    /// </summary>
    public class CrmOptionOutput
    {
        /// <summary>
        /// CRM選項ID
        /// </summary>
        public long SiteCrmOptionId { get; set; }

        /// <summary>
        /// 案場代碼
        /// </summary>
        public string SiteCode { get; set; } = null!;

        /// <summary>
        /// 案場名稱
        /// </summary>
        public string? SiteName { get; set; }

        /// <summary>
        /// CRM選項類型ID
        /// </summary>
        public long CrmOptionTypeId { get; set; }

        /// <summary>
        /// CRM選項類型名稱
        /// </summary>
        public string? CrmOptionTypeName { get; set; }

        /// <summary>
        /// 選項值
        /// </summary>
        public string OptionValue { get; set; } = null!;

        /// <summary>
        /// 排序順序
        /// </summary>
        public int SortOrder { get; set; }

        /// <summary>
        /// 是否啟用
        /// </summary>
        public bool? IsActive { get; set; }

        /// <summary>
        /// 建立時間
        /// </summary>
        public DateTime CreateTime { get; set; }

        /// <summary>
        /// 更新時間
        /// </summary>
        public DateTime UpdateTime { get; set; }

        /// <summary>
        /// 建立者ID
        /// </summary>
        public string CreatedUserInfoId { get; set; } = null!;

        /// <summary>
        /// 建立者姓名
        /// </summary>
        public string? CreatedUserName { get; set; }

        /// <summary>
        /// 更新者ID
        /// </summary>
        public string UpdatedUserInfoId { get; set; } = null!;

        /// <summary>
        /// 更新者姓名
        /// </summary>
        public string? UpdatedUserName { get; set; }
    }
}
