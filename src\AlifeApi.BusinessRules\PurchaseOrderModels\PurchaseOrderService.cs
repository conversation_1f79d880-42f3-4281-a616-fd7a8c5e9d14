﻿using AlifeApi.BusinessRules.Infrastructure;
using AlifeApi.BusinessRules.ParkingSpaceModels;
using AlifeApi.DataAccess.ProjectContent;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace AlifeApi.BusinessRules.PurchaseOrderModels
{
    /// <summary>
    /// 買賣預定單服務
    /// </summary>
    public class PurchaseOrderService : ServiceBase<alifeContext>
    {
        public PurchaseOrderService(IServiceProvider serviceProvider, alifeContext dbContext)
            : base(serviceProvider, dbContext)
        {
        }

        /// <summary>
        /// 建立買賣預定單
        /// </summary>
        /// <param name="input">買賣預定單建立輸入資料</param>
        /// <returns>新建買賣預定單的ID</returns>
        public async Task<int> CreatePurchaseOrderAsync(PurchaseOrderCreateInput input)
        {
            ArgumentNullException.ThrowIfNull(input);

            Unit? unit = null;
            List<ParkingSpace> parkingSpaces = new();

            // 檢查客戶是否存在
            if (!await Db.Customers.AnyAsync(c => c.CustomerId == input.CustomerId))
            {
                throw new Exception($"指定的客戶ID '{input.CustomerId}' 不存在。");
            }

            // 檢查房屋單位 (如果有提供)
            if (input.UnitId.HasValue)
            {
                unit = await Db.Units.FirstOrDefaultAsync(u => u.UnitId == input.UnitId.Value);
                if (unit == null)
                {
                    throw new Exception($"指定的房屋單位ID '{input.UnitId.Value}' 不存在。");
                }

                // 檢查房屋單位狀態是否可售
                if (unit.Status != "可售") // 根據實際狀態調整
                {
                    throw new Exception($"房屋單位 '{unit.UnitNumber}' 目前狀態為 '{unit.Status}'，無法建立訂單。");
                }

                // 驗證 Unit 的 SiteCode 是否與訂單一致
                if (unit.SiteCode != input.SiteCode)
                {
                    throw new Exception($"房屋單位的案場 '{unit.SiteCode}' 與訂單案場 '{input.SiteCode}' 不符。");
                }
            }

            // 檢查停車位 (如果有提供)
            if (!string.IsNullOrEmpty(input.PurchasedParkingSpaceIds))
            {
                var parkingSpaceIds = ParkingSpaceService.ParseParkingSpaceIds(input.PurchasedParkingSpaceIds);
                if (parkingSpaceIds.Any())
                {
                    var foundIds = await Db.ParkingSpaces
                        .Where(p => parkingSpaceIds.Contains(p.ParkingSpaceId))
                        .Select(p => p.ParkingSpaceId)
                        .ToListAsync();

                    parkingSpaces = await Db.ParkingSpaces
                        .Where(p => parkingSpaceIds.Contains(p.ParkingSpaceId))
                        .ToListAsync();

                    if (foundIds.Count != parkingSpaceIds.Count)
                    {
                        var notFoundIds = parkingSpaceIds.Except(foundIds);
                        throw new Exception($"找不到以下停車位ID: {string.Join(", ", notFoundIds)}");
                    }

                    // 檢查車位的銷售狀態 (現在從Units表查詢)
                    var parkingSpaceUnitStatuses = await Db.Units
                        .Where(u => u.UnitType == "車位" && 
                                   u.AssociatedParkingSpaceIds != null &&
                                   parkingSpaceIds.Any(psId => u.AssociatedParkingSpaceIds.Contains(psId.ToString())))
                        .Select(u => new { u.UnitId, u.Status, u.AssociatedParkingSpaceIds })
                        .ToListAsync();

                    var unavailableSpaces = new List<(int SpaceId, string SpaceNumber, string Status)>();
                    foreach (var psId in parkingSpaceIds)
                    {
                        var unitStatus = parkingSpaceUnitStatuses.FirstOrDefault(u => 
                            u.AssociatedParkingSpaceIds != null && 
                            u.AssociatedParkingSpaceIds.Split(',', StringSplitOptions.RemoveEmptyEntries | StringSplitOptions.TrimEntries)
                                .Contains(psId.ToString()));
                        
                        if (unitStatus != null && unitStatus.Status != "可售")
                        {
                            var space = parkingSpaces.FirstOrDefault(ps => ps.ParkingSpaceId == psId);
                            if (space != null)
                            {
                                unavailableSpaces.Add((psId, space.SpaceNumber, unitStatus.Status));
                            }
                        }
                    }

                    if (unavailableSpaces.Any())
                    {
                        throw new Exception($"以下車位目前無法建立訂單 (狀態非可售): {string.Join(", ", unavailableSpaces.Select(s => $"{s.SpaceNumber}({s.SpaceId})：{s.Status}"))}");
                    }
                    
                     // 驗證 ParkingSpace 的 SiteCode 是否與訂單一致
                    var mismatchedSiteSpaces = parkingSpaces.Where(p => p.SiteCode != input.SiteCode).ToList();
                    if (mismatchedSiteSpaces.Any())
                    {
                        throw new Exception($"以下停車位的案場與訂單案場 '{input.SiteCode}' 不符: {string.Join(", ", mismatchedSiteSpaces.Select(p => $"{p.SpaceNumber}(ID:{p.ParkingSpaceId}, Site:{p.SiteCode})"))}");
                    }
                }
            }

            // 生成 OrderNumber (這裡使用簡單示例，實際應有更複雜的規則)
            string orderNumber = $"{input.SiteCode}-{DateTime.Now:yyyyMMddHHmmss}-{input.CustomerId}";

            var order = new PurchaseOrder
            {
                OrderNumber = orderNumber,
                OrderDate = input.OrderDate,
                SiteCode = input.SiteCode,
                CustomerId = input.CustomerId,
                SalesAgencyName = input.SalesAgencyName,
                SalesAgencyMobile = input.SalesAgencyMobile,
                SalesAgencyLandline = input.SalesAgencyLandline,
                SalesAgencyEmail = input.SalesAgencyEmail,
                UnitId = input.UnitId,
                LandShareArea = input.LandShareArea,
                PurchasedParkingSpaceIds = input.PurchasedParkingSpaceIds, // 儲存原始 ID 字串
                PropertyPrice = input.PropertyPrice,
                ParkingSpacePrice = input.ParkingSpacePrice,
                TotalPrice = input.TotalPrice,
                DepositAmount = input.DepositAmount,
                DepositPaidAmount = input.DepositPaidAmount,
                DepositPaymentMethod = input.DepositPaymentMethod,
                DepositPayee = input.DepositPayee,
                DepositDueDate = input.DepositDueDate,
                DepositBalanceAmount = input.DepositBalanceAmount,
                ContractSigningAppointment = input.ContractSigningAppointment,
                ContractSigningAmount = input.ContractSigningAmount,
                ConsentToDataUsage = input.ConsentToDataUsage,
                OrderRemarks = input.OrderRemarks,
                SalespersonUserInfoId = input.SalespersonUserInfoId,
                SaleType = input.SaleType,
                Status = input.Status, // 訂單初始狀態
                CreatedTime = DateTime.Now,
                UpdatedTime = DateTime.Now,
                CreatedUserInfoId = CurrentUser.UserId,
                UpdatedUserInfoId = CurrentUser.UserId
            };

            // **重要：更新 Unit 的狀態**
            // 現在車位銷售狀態也統一由Units表管理
            string bookedStatus = "已預訂";

            if (unit != null && unit.Status == "可售")
            {
                unit.Status = bookedStatus;
                unit.TransactionPrice = input.PropertyPrice; // 或 TotalPrice，根據業務邏輯
                unit.UpdatedTime = DateTime.Now;
                unit.UpdatedUserInfoId = CurrentUser.UserId;
                Db.Units.Update(unit);
            }

            // 更新車位對應的Units記錄狀態
            if (!string.IsNullOrEmpty(input.PurchasedParkingSpaceIds))
            {
                var parkingSpaceIds = ParkingSpaceService.ParseParkingSpaceIds(input.PurchasedParkingSpaceIds);
                if (parkingSpaceIds.Any())
                {
                    var parkingSpaceUnits = await Db.Units
                        .Where(u => u.UnitType == "車位" && 
                                   u.AssociatedParkingSpaceIds != null &&
                                   parkingSpaceIds.Any(psId => u.AssociatedParkingSpaceIds.Contains(psId.ToString())))
                        .ToListAsync();

                    foreach (var parkingUnit in parkingSpaceUnits)
            {
                        if (parkingUnit.Status == "可售")
                {
                            parkingUnit.Status = bookedStatus;
                            parkingUnit.TransactionPrice = input.ParkingSpacePrice / parkingSpaceIds.Count; // 平均分配車位價格
                            parkingUnit.UpdatedTime = DateTime.Now;
                            parkingUnit.UpdatedUserInfoId = CurrentUser.UserId;
                            Db.Units.Update(parkingUnit);
                        }
                    }
                }
            }

            Db.PurchaseOrders.Add(order);

            // 使用事務確保原子性
            using var transaction = await Db.Database.BeginTransactionAsync();
            try
            {
                await Db.SaveChangesAsync();
                
                // 記錄訂單創建歷史
                await LogPurchaseOrderHistoryAsync(order.OrderId, "CREATE", null, order.Status, 
                    $"創建新訂單 - 客戶ID: {order.CustomerId}, 房屋單位ID: {order.UnitId}, 總價: {order.TotalPrice:C}", 
                    CurrentUser.UserId);
                
                await transaction.CommitAsync();
                return order.OrderId;
            }
            catch
            {
                await transaction.RollbackAsync();
                throw; // 重新拋出異常
            }
        }

        /// <summary>
        /// 取得買賣預定單列表 (分頁)
        /// </summary>
        /// <param name="input">查詢條件</param>
        /// <returns>分頁的買賣預定單列表</returns>
        public async Task<PagedListOutput<PurchaseOrderListOutput>> GetPurchaseOrderListAsync(PurchaseOrderQueryInput input)
        {
            ArgumentNullException.ThrowIfNull(input);

            // 移除 Include
            var query = Db.PurchaseOrders.AsQueryable();

            // --- Apply Basic Filters First (on PurchaseOrder table) ---
            if (!string.IsNullOrEmpty(input.SiteCode))
                query = query.Where(po => po.SiteCode == input.SiteCode);
            if (input.CustomerId.HasValue)
                query = query.Where(po => po.CustomerId == input.CustomerId.Value);
            if (input.UnitId.HasValue)
                query = query.Where(po => po.UnitId == input.UnitId.Value);
             if (!string.IsNullOrEmpty(input.OrderNumber))
                query = query.Where(po => po.OrderNumber == input.OrderNumber);
            if (input.OrderDateStart.HasValue)
                query = query.Where(po => po.OrderDate >= input.OrderDateStart.Value);
            if (input.OrderDateEnd.HasValue)
                query = query.Where(po => po.OrderDate <= input.OrderDateEnd.Value);
            if (!string.IsNullOrEmpty(input.SalespersonUserInfoId))
                query = query.Where(po => po.SalespersonUserInfoId == input.SalespersonUserInfoId);
             if (!string.IsNullOrEmpty(input.Status))
                query = query.Where(po => po.Status == input.Status);
                
            // 新增的日期範圍查詢
            if (input.SaleDateStart.HasValue)
                query = query.Where(po => po.SaleDate >= input.SaleDateStart.Value);
            if (input.SaleDateEnd.HasValue)
                query = query.Where(po => po.SaleDate <= input.SaleDateEnd.Value);
            if (input.DepositFullPaidDateStart.HasValue)
                query = query.Where(po => po.DepositFullPaidDate >= input.DepositFullPaidDateStart.Value);
            if (input.DepositFullPaidDateEnd.HasValue)
                query = query.Where(po => po.DepositFullPaidDate <= input.DepositFullPaidDateEnd.Value);
            if (input.ContractSignedDateStart.HasValue)
                query = query.Where(po => po.ContractSignedDate >= input.ContractSignedDateStart.Value);
            if (input.ContractSignedDateEnd.HasValue)
                query = query.Where(po => po.ContractSignedDate <= input.ContractSignedDateEnd.Value);
            if (input.CancellationDateStart.HasValue)
                query = query.Where(po => po.CancellationDate >= input.CancellationDateStart.Value);
            if (input.CancellationDateEnd.HasValue)
                query = query.Where(po => po.CancellationDate <= input.CancellationDateEnd.Value);
            // -------------

             // Apply filters that require joins (need to be careful with performance)
             if (!string.IsNullOrEmpty(input.CustomerName))
                 query = query.Where(po => Db.Customers.Any(c => c.CustomerId == po.CustomerId && c.Name.Contains(input.CustomerName)));
             if (!string.IsNullOrEmpty(input.UnitNumber))
                 query = query.Where(po => po.UnitId.HasValue && Db.Units.Any(u => u.UnitId == po.UnitId && u.UnitNumber.Contains(input.UnitNumber)));
             if (!string.IsNullOrEmpty(input.SalespersonName))
                 query = query.Where(po => Db.UserInfos.Any(ui => ui.UserInfoId == po.SalespersonUserInfoId && ui.Name.Contains(input.SalespersonName)));


            // Select 基本資料
            var projectedQuery = query
                .OrderByDescending(po => po.OrderDate) // 按訂單日期排序
                 // Select 到包含內部 ParkingSpaceIds 的臨時 DTO
                .Select(po => new PurchaseOrderListOutputInternal
                {
                    OrderId = po.OrderId,
                    OrderNumber = po.OrderNumber,
                    OrderDate = po.OrderDate,
                    SiteCode = po.SiteCode,
                    CustomerId = po.CustomerId,
                    UnitId = po.UnitId,
                    TotalPrice = po.TotalPrice,
                    SalespersonUserInfoId = po.SalespersonUserInfoId,
                    Status = po.Status,
                    CreatedTime = po.CreatedTime,
                     // 暫存 ParkingSpaceIds 字串，後續處理
                    PurchasedParkingSpaceIdsInternal = po.PurchasedParkingSpaceIds ?? string.Empty,
                     // 其他需要 Join 的欄位先設為 null
                    CustomerName = null,
                    UnitNumber = null,
                    BuildingName = null,
                    FloorLabel = null,
                    SalespersonName = null
                });

            // 執行分頁查詢
            var pagedResult = await projectedQuery.ToPagedListOutputAsync(input);

             // 手動查詢關聯資料並填充
             if (pagedResult.Details.Any())
             {
                 var customerIds = pagedResult.Details.Select(o => o.CustomerId).Distinct().ToList();
                 var unitIds = pagedResult.Details.Where(o => o.UnitId.HasValue).Select(o => o.UnitId!.Value).Distinct().ToList();
                 var salespersonIds = pagedResult.Details.Select(o => o.SalespersonUserInfoId).Distinct().ToList();

                 var customers = await Db.Customers
                                     .Where(c => customerIds.Contains(c.CustomerId))
                                     .Select(c => new { c.CustomerId, c.Name })
                                     .ToDictionaryAsync(c => c.CustomerId, c => c.Name);

                 var units = await Db.Units
                                 .Where(u => unitIds.Contains(u.UnitId))
                                  // 查詢 Unit 時一併查詢 Floor 和 Building
                                 .Select(u => new {
                                     u.UnitId,
                                     u.UnitNumber,
                                     u.FloorId,
                                     u.BuildingId,
                                     FloorLabel = Db.Floors.Where(f => f.FloorId == u.FloorId).Select(f => f.FloorLabel).FirstOrDefault(), // 子查詢
                                     BuildingName = Db.Buildings.Where(b => b.BuildingId == u.BuildingId).Select(b => b.BuildingName).FirstOrDefault() // 子查詢
                                 })
                                 .ToDictionaryAsync(u => u.UnitId);

                 var salespersons = await Db.UserInfos
                                        .Where(ui => salespersonIds.Contains(ui.UserInfoId))
                                        .Select(ui => new { ui.UserInfoId, ui.Name })
                                        .ToDictionaryAsync(ui => ui.UserInfoId, ui => ui.Name);

                 // 處理停車位編號 (與之前邏輯類似)
                 var orderIdsWithSpaces = pagedResult.Details
                     .Where(o => !string.IsNullOrEmpty(o.PurchasedParkingSpaceIdsInternal))
                     .ToDictionary(o => o.OrderId, o => o.PurchasedParkingSpaceIdsInternal!.Split(',', StringSplitOptions.RemoveEmptyEntries | StringSplitOptions.TrimEntries).Select(int.Parse).ToList());

                 Dictionary<int, string> spaceInfos = new();
                 if (orderIdsWithSpaces.Any())
                 {
                     var allSpaceIds = orderIdsWithSpaces.Values.SelectMany(ids => ids).Distinct().ToList();
                     if(allSpaceIds.Any()) {
                         spaceInfos = await Db.ParkingSpaces
                             .Where(ps => allSpaceIds.Contains(ps.ParkingSpaceId))
                             .Select(ps => new { ps.ParkingSpaceId, ps.SpaceNumber })
                             .ToDictionaryAsync(ps => ps.ParkingSpaceId, ps => ps.SpaceNumber);
                     }
                 }

                 foreach (var orderOutput in pagedResult.Details)
                 {
                     if (customers.TryGetValue(orderOutput.CustomerId, out var customerName))
                     {
                         orderOutput.CustomerName = customerName;
                     }
                     if (orderOutput.UnitId.HasValue && units.TryGetValue(orderOutput.UnitId.Value, out var unitInfo))
                     {
                         orderOutput.UnitNumber = unitInfo.UnitNumber;
                         orderOutput.BuildingName = unitInfo.BuildingName;
                         orderOutput.FloorLabel = unitInfo.FloorLabel;
                     }
                     if (salespersons.TryGetValue(orderOutput.SalespersonUserInfoId, out var salespersonName))
                     {
                         orderOutput.SalespersonName = salespersonName;
                     }

                     // 填入停車位號碼
                     if (orderIdsWithSpaces.TryGetValue(orderOutput.OrderId, out var spaceIds))
                     {
                         var spaceNumbers = spaceIds
                             .Select(id => spaceInfos.TryGetValue(id, out var number) ? number : id.ToString())
                             .ToList();
                         orderOutput.PurchasedParkingSpaceNumbers = string.Join(", ", spaceNumbers);
                     }
                 }
             }

            // 將結果轉換回基礎 DTO (移除 Internal 屬性)
            var finalResult = new PagedListOutput<PurchaseOrderListOutput>(input)
            {
                RecordCount = pagedResult.RecordCount,
                 Details = pagedResult.Details.Select(o => new PurchaseOrderListOutput {
                     OrderId = o.OrderId,
                     OrderNumber = o.OrderNumber,
                     OrderDate = o.OrderDate,
                     SiteCode = o.SiteCode,
                     CustomerId = o.CustomerId,
                     CustomerName = o.CustomerName,
                     UnitId = o.UnitId,
                     UnitNumber = o.UnitNumber,
                     BuildingName = o.BuildingName,
                     FloorLabel = o.FloorLabel,
                     TotalPrice = o.TotalPrice,
                     SalespersonUserInfoId = o.SalespersonUserInfoId,
                     SalespersonName = o.SalespersonName,
                     Status = o.Status,
                     CreatedTime = o.CreatedTime,
                     PurchasedParkingSpaceNumbers = o.PurchasedParkingSpaceNumbers
                 }).ToList()
            };

            return finalResult;
        }


        /// <summary>
        /// 根據ID取得買賣預定單詳細資料
        /// </summary>
        /// <param name="orderId">買賣預定單ID</param>
        /// <returns>買賣預定單詳細資料</returns>
        public async Task<PurchaseOrderOutput?> GetPurchaseOrderByIdAsync(int orderId)
        {
             // 移除 Include
            var order = await Db.PurchaseOrders
                .Where(po => po.OrderId == orderId)
                .Select(po => new PurchaseOrderOutput // Select 到 DTO
                {
                    OrderId = po.OrderId,
                    OrderNumber = po.OrderNumber,
                    OrderDate = po.OrderDate,
                    SiteCode = po.SiteCode,
                    CustomerId = po.CustomerId,
                    SalesAgencyName = po.SalesAgencyName,
                    SalesAgencyMobile = po.SalesAgencyMobile,
                    SalesAgencyLandline = po.SalesAgencyLandline,
                    SalesAgencyEmail = po.SalesAgencyEmail,
                    UnitId = po.UnitId,
                    LandShareArea = po.LandShareArea,
                    PurchasedParkingSpaceIds = po.PurchasedParkingSpaceIds,
                    PropertyPrice = po.PropertyPrice,
                    ParkingSpacePrice = po.ParkingSpacePrice,
                    TotalPrice = po.TotalPrice,
                    DepositAmount = po.DepositAmount,
                    DepositPaidAmount = po.DepositPaidAmount,
                    DepositPaymentMethod = po.DepositPaymentMethod,
                    DepositPayee = po.DepositPayee,
                    DepositDueDate = po.DepositDueDate,
                    DepositBalanceAmount = po.DepositBalanceAmount,
                    ContractSigningAppointment = po.ContractSigningAppointment,
                    ContractSigningAmount = po.ContractSigningAmount,
                    ConsentToDataUsage = po.ConsentToDataUsage,
                    OrderRemarks = po.OrderRemarks,
                    SalespersonUserInfoId = po.SalespersonUserInfoId,
                    SaleType = po.SaleType,
                    Status = po.Status,
                    SaleDate = po.SaleDate,
                    DepositFullPaidDate = po.DepositFullPaidDate,
                    ContractSignedDate = po.ContractSignedDate,
                    CancellationDate = po.CancellationDate,
                    HandoverDate = po.HandoverDate,
                    FinalPaymentDate = po.FinalPaymentDate,
                    RequestDate = po.RequestDate,
                    ReceiveDate = po.ReceiveDate,
                    CreatedTime = po.CreatedTime,
                    UpdatedTime = po.UpdatedTime,
                    CreatedUserInfoId = po.CreatedUserInfoId,
                    UpdatedUserInfoId = po.UpdatedUserInfoId
                     // 關聯欄位稍後填充
                })
                .FirstOrDefaultAsync();

            if (order == null)
            {
                return null;
            }

             // 手動查詢關聯資料
             var customer = await Db.Customers.Where(c => c.CustomerId == order.CustomerId).Select(c => new { c.Name, c.PhoneNumber }).FirstOrDefaultAsync();
             if(customer != null) {
                 order.CustomerName = customer.Name;
                 order.CustomerPhoneNumber = customer.PhoneNumber;
             }

             if(order.UnitId.HasValue) {
                 var unitInfo = await Db.Units
                                    .Where(u => u.UnitId == order.UnitId.Value)
                                     .Select(u => new {
                                         u.UnitNumber,
                                         u.Layout,
                                         u.TotalArea,
                                         FloorLabel = Db.Floors.Where(f => f.FloorId == u.FloorId).Select(f => f.FloorLabel).FirstOrDefault(),
                                         BuildingName = Db.Buildings.Where(b => b.BuildingId == u.BuildingId).Select(b => b.BuildingName).FirstOrDefault()
                                     }).FirstOrDefaultAsync();
                 if(unitInfo != null) {
                     order.UnitNumber = unitInfo.UnitNumber;
                     order.UnitLayout = unitInfo.Layout;
                     order.UnitTotalArea = unitInfo.TotalArea;
                     order.FloorLabel = unitInfo.FloorLabel;
                     order.BuildingName = unitInfo.BuildingName;
                 }
             }

             var userIds = new[] { order.SalespersonUserInfoId, order.CreatedUserInfoId, order.UpdatedUserInfoId }.Where(id => !string.IsNullOrEmpty(id)).Distinct().ToList();
             if (userIds.Any())
             {
                 var users = await Db.UserInfos
                                     .Where(ui => userIds.Contains(ui.UserInfoId))
                                     .Select(ui => new { ui.UserInfoId, ui.Name })
                                     .ToDictionaryAsync(ui => ui.UserInfoId, ui => ui.Name);

                 if (users.TryGetValue(order.SalespersonUserInfoId, out var salespersonName))
                     order.SalespersonName = salespersonName;
                 if (users.TryGetValue(order.CreatedUserInfoId, out var createdName))
                     order.CreatedUserName = createdName;
                 if (!string.IsNullOrEmpty(order.UpdatedUserInfoId) && users.TryGetValue(order.UpdatedUserInfoId, out var updatedName))
                     order.UpdatedUserName = updatedName;
             }


            // 查詢並填入購買的停車位詳細資訊
            if (!string.IsNullOrEmpty(order.PurchasedParkingSpaceIds))
            {
                var parkingSpaceIds = order.PurchasedParkingSpaceIds.Split(',', StringSplitOptions.RemoveEmptyEntries | StringSplitOptions.TrimEntries)
                                           .Select(int.Parse).Distinct().ToList();
                if (parkingSpaceIds.Any())
                {
                    order.PurchasedParkingSpaces = await Db.ParkingSpaces
                         // 手動 Join Floor 和 Building
                        .Where(ps => parkingSpaceIds.Contains(ps.ParkingSpaceId))
                        .Select(ps => new ParkingSpaceBasicInfoOutput
                        {
                            ParkingSpaceId = ps.ParkingSpaceId,
                            SpaceNumber = ps.SpaceNumber,
                            SpaceType = ps.SpaceType,
                             // 子查詢獲取 FloorLabel 和 BuildingName
                            FloorLabel = Db.Floors.Where(f => f.FloorId == ps.FloorId).Select(f => f.FloorLabel).FirstOrDefault(),
                            BuildingName = Db.Buildings.Where(b => b.BuildingId == ps.BuildingId).Select(b => b.BuildingName).FirstOrDefault()
                        })
                        .ToListAsync();
                }
            }

            return order;
        }

        /// <summary>
        /// 更新買賣預定單資訊
        /// </summary>
        /// <param name="orderId">買賣預定單ID</param>
        /// <param name="input">買賣預定單更新輸入資料</param>
        public async Task UpdatePurchaseOrderAsync(int orderId, PurchaseOrderUpdateInput input)
        {
            ArgumentNullException.ThrowIfNull(input);

             // 移除 Include，在需要時手動查詢
            var order = await Db.PurchaseOrders
                              .FirstOrDefaultAsync(po => po.OrderId == orderId);

            if (order == null)
            {
                throw new Exception($"找不到指定的買賣預定單 (ID: {orderId})");
            }

            // --- 狀態變更邏輯 (重要) ---
            string previousStatus = order.Status;
            string newStatus = input.Status;
            bool statusChanged = previousStatus != newStatus;

            // 如果訂單從活動狀態變為取消狀態
            string cancelledStatus = "已取消";
            string availableStatus = "可售";
            List<string> activeStatuses = new List<string> { "預訂中", "已轉訂", "已簽約" };

            if (statusChanged && newStatus == cancelledStatus && activeStatuses.Contains(previousStatus))
            {
                // 手動檢查是否有收款記錄
                bool hasPaymentRecords = await Db.PaymentRecords.AnyAsync(pr => pr.OrderId == orderId);
                if (hasPaymentRecords)
                {
                    Console.WriteLine($"警告: 訂單 (ID: {orderId}) 已有收款記錄，但仍被標記為取消。");
                }

                // 回滾 Unit 狀態 (手動查詢 Unit)
                if (order.UnitId.HasValue)
                {
                    var unitToUpdate = await Db.Units.FindAsync(order.UnitId.Value);
                    if (unitToUpdate != null)
                    {
                        string currentBookedStatus = "已預訂"; // 假設鎖定狀態
                        if(unitToUpdate.Status == currentBookedStatus)
                        {
                            unitToUpdate.Status = availableStatus;
                            unitToUpdate.TransactionPrice = null;
                            unitToUpdate.UpdatedTime = DateTime.Now;
                            unitToUpdate.UpdatedUserInfoId = CurrentUser.UserId;
                            Db.Units.Update(unitToUpdate);
                        }
                        else {
                            Console.WriteLine($"警告: 訂單 (ID: {orderId}) 取消，但關聯房屋 (ID: {order.UnitId}) 狀態為 '{unitToUpdate.Status}'，未回滾為 '{availableStatus}'。");
                        }
                    }
                }

                // 回滾車位Units狀態 (現在車位銷售狀態由Units表管理)
                if (!string.IsNullOrEmpty(order.PurchasedParkingSpaceIds))
                {
                    var parkingSpaceIds = ParkingSpaceService.ParseParkingSpaceIds(order.PurchasedParkingSpaceIds);
                    if (parkingSpaceIds.Any())
                    {
                        var parkingSpaceUnits = await Db.Units
                            .Where(u => u.UnitType == "車位" && 
                                       u.AssociatedParkingSpaceIds != null &&
                                       parkingSpaceIds.Any(psId => u.AssociatedParkingSpaceIds.Contains(psId.ToString())))
                            .ToListAsync();

                        string currentBookedStatus = "已預訂"; // 假設鎖定狀態
                        foreach (var parkingUnit in parkingSpaceUnits)
                        {
                             if(parkingUnit.Status == currentBookedStatus)
                             {
                                parkingUnit.Status = availableStatus;
                                parkingUnit.TransactionPrice = null;
                                parkingUnit.UpdatedTime = DateTime.Now;
                                parkingUnit.UpdatedUserInfoId = CurrentUser.UserId;
                                Db.Units.Update(parkingUnit);
                             }
                              else {
                                 Console.WriteLine($"警告: 訂單 (ID: {orderId}) 取消，但關聯車位單位 (ID: {parkingUnit.UnitId}) 狀態為 '{parkingUnit.Status}'，未回滾為 '{availableStatus}'。");
                             }
                        }
                    }
                }
            }
            else if (statusChanged && activeStatuses.Contains(newStatus) && previousStatus == cancelledStatus)
            {
                 throw new NotSupportedException("不支援從已取消狀態直接恢復訂單，請重新建立訂單。");
            }
            // --------------------------

            // 更新訂單基本資訊
            order.OrderDate = input.OrderDate;
            order.SalesAgencyName = input.SalesAgencyName;
            order.SalesAgencyMobile = input.SalesAgencyMobile;
            order.SalesAgencyLandline = input.SalesAgencyLandline;
            order.SalesAgencyEmail = input.SalesAgencyEmail;
            order.LandShareArea = input.LandShareArea;
            order.PropertyPrice = input.PropertyPrice;
            order.ParkingSpacePrice = input.ParkingSpacePrice;
            order.TotalPrice = input.TotalPrice;
            order.DepositAmount = input.DepositAmount;
            order.DepositPaidAmount = input.DepositPaidAmount;
            order.DepositPaymentMethod = input.DepositPaymentMethod;
            order.DepositPayee = input.DepositPayee;
            order.DepositDueDate = input.DepositDueDate;
            order.DepositBalanceAmount = input.DepositBalanceAmount;
            order.ContractSigningAppointment = input.ContractSigningAppointment;
            order.ContractSigningAmount = input.ContractSigningAmount;
            order.ConsentToDataUsage = input.ConsentToDataUsage;
            order.OrderRemarks = input.OrderRemarks;
            order.SaleType = input.SaleType;
            order.Status = input.Status; // 更新狀態
            
            // 更新新的業務日期欄位
            order.SaleDate = input.SaleDate;
            order.DepositFullPaidDate = input.DepositFullPaidDate;
            order.ContractSignedDate = input.ContractSignedDate;
            order.CancellationDate = input.CancellationDate;
            order.HandoverDate = input.HandoverDate;
            order.FinalPaymentDate = input.FinalPaymentDate;
            order.RequestDate = input.RequestDate;
            order.ReceiveDate = input.ReceiveDate;
            
            order.UpdatedTime = DateTime.Now;
            order.UpdatedUserInfoId = CurrentUser.UserId;

            // 使用事務保存更改
            using var transaction = await Db.Database.BeginTransactionAsync();
            try
            {
                await Db.SaveChangesAsync();
                
                // 記錄訂單更新歷史
                if (statusChanged)
                {
                    await LogPurchaseOrderHistoryAsync(orderId, "STATUS_CHANGE", previousStatus, newStatus, 
                        $"訂單狀態變更：'{previousStatus}' → '{newStatus}'", CurrentUser.UserId);
                }
                else
                {
                    await LogPurchaseOrderHistoryAsync(orderId, "MODIFY", order.Status, order.Status, 
                        $"更新訂單資訊 - 總價: {order.TotalPrice:C}", CurrentUser.UserId);
                }
                
                await transaction.CommitAsync();
            }
            catch
            {
                await transaction.RollbackAsync();
                throw;
            }
        }

        /// <summary>
        /// 刪除買賣預定單 (軟刪除或硬刪除，取決於業務邏輯)
        /// </summary>
        /// <param name="orderId">買賣預定單ID</param>
        /// <remarks>
        /// 強烈建議不要物理刪除訂單，尤其是已有收款記錄的訂單。
        /// 通常使用狀態欄位 (例如 Status = '已作廢') 來標記。
        /// 如果確實需要物理刪除，務必確保相關的 Unit/ParkingSpace 狀態已正確回滾。
        /// 此處實作硬刪除，但有檢查。
        /// </remarks>
        public async Task DeletePurchaseOrderAsync(int orderId)
        {
            // 移除 Include
            var order = await Db.PurchaseOrders
                              .FirstOrDefaultAsync(po => po.OrderId == orderId);

            if (order == null)
            {
                return; // 允許刪除不存在的紀錄
            }

            // 手動檢查是否有收款記錄
             bool hasPaymentRecords = await Db.PaymentRecords.AnyAsync(pr => pr.OrderId == orderId);
            if (hasPaymentRecords)
            {
                throw new Exception($"無法刪除訂單 (ID: {orderId})，因為已有收款記錄。請將訂單狀態設為 '已取消' 或 '已作廢'。");
            }

             // 如果訂單狀態不是 '已取消' 或類似的非活動狀態，也建議不要物理刪除
             string cancelledStatus = "已取消"; // 根據實際狀態調整
             if (order.Status != cancelledStatus) {
                  Console.WriteLine($"警告: 正在物理刪除狀態為 '{order.Status}' 的訂單 (ID: {orderId})。請確保關聯的房屋/車位狀態已處理。");
                 // ... (可加入強制回滾狀態邏輯) ...
             }

            // 記錄訂單刪除歷史
            await LogPurchaseOrderHistoryAsync(orderId, "DELETE", order.Status, null, 
                $"刪除訂單 - 客戶ID: {order.CustomerId}, 房屋單位ID: {order.UnitId}", 
                CurrentUser.UserId);

            Db.PurchaseOrders.Remove(order);
            await Db.SaveChangesAsync(); // 實際執行刪除
        }

        /// <summary>
        /// 更新訂單業務流程日期
        /// </summary>
        /// <param name="orderId">訂單ID</param>
        /// <param name="input">業務日期更新輸入</param>
        /// <returns></returns>
        public async Task UpdateBusinessDatesAsync(int orderId, dynamic input)
        {
            var order = await Db.PurchaseOrders.FirstOrDefaultAsync(po => po.OrderId == orderId);
            if (order == null)
            {
                throw new Exception($"找不到指定的買賣預定單 (ID: {orderId})");
            }

            var inputType = input.GetType();
            var changeRecords = new List<string>();
            
            // 使用反射來更新對應的業務日期欄位
            foreach (var property in inputType.GetProperties())
            {
                var value = property.GetValue(input);
                if (value != null)
                {
                    var orderProperty = typeof(PurchaseOrder).GetProperty(property.Name);
                    if (orderProperty != null && orderProperty.CanWrite)
                    {
                        var oldValue = orderProperty.GetValue(order);
                        if (!Equals(oldValue, value))
                        {
                            orderProperty.SetValue(order, value);
                            changeRecords.Add($"{property.Name}: {oldValue?.ToString() ?? "null"} → {value}");
                        }
                    }
                }
            }

            if (changeRecords.Any())
            {
                order.UpdatedTime = DateTime.Now;
                order.UpdatedUserInfoId = CurrentUser.UserId;

                // 記錄業務日期更新歷史
                await LogPurchaseOrderHistoryAsync(orderId, "DATE_UPDATE", null, null, 
                    $"更新業務日期: {string.Join(", ", changeRecords)}", CurrentUser.UserId);

                await Db.SaveChangesAsync();
            }
        }

        /// <summary>
        /// 記錄訂單歷史
        /// </summary>
        /// <param name="orderId">訂單ID</param>
        /// <param name="actionType">操作類型</param>
        /// <param name="oldStatus">舊狀態</param>
        /// <param name="newStatus">新狀態</param>
        /// <param name="contentRecord">記錄內容</param>
        /// <param name="userInfoId">操作使用者ID</param>
        /// <returns></returns>
        private async Task LogPurchaseOrderHistoryAsync(int orderId, string actionType, string? oldStatus, string? newStatus, string contentRecord, string userInfoId)
        {
            var history = new PurchaseOrdersHistory
            {
                OrderId = orderId,
                ActionType = actionType,
                OldStatus = oldStatus,
                NewStatus = newStatus,
                ContentRecord = contentRecord,
                CreatedUserInfoId = userInfoId,
                CreatedTime = DateTime.UtcNow
            };

            Db.PurchaseOrdersHistories.Add(history);
            await Db.SaveChangesAsync();
        }

        // 可選: 輔助方法用於回滾狀態
        // private async Task RollbackUnitAndParkingSpaceStatus(PurchaseOrder order) { ... }
    }

    // 為了 GetPurchaseOrderListAsync 中的 Select 和 ToPagedListOutputAsync 正常工作
    // 需要臨時定義一個包含內部欄位的類
    public class PurchaseOrderListOutputInternal : PurchaseOrderListOutput
    {
        public string? PurchasedParkingSpaceIdsInternal { get; set; }
    }
} 
