﻿using AlifeApi.BusinessRules.Infrastructure;
using AlifeApi.BusinessRules.ParkingSpaceModels;
using AlifeApi.DataAccess.ProjectContent;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace AlifeApi.BusinessRules.PurchaseOrderModels
{
    /// <summary>
    /// 買賣預定單服務
    /// </summary>
    public class PurchaseOrderService : ServiceBase<alifeContext>
    {
        public PurchaseOrderService(IServiceProvider serviceProvider, alifeContext dbContext)
            : base(serviceProvider, dbContext)
        {
        }

        /// <summary>
        /// 建立買賣預定單
        /// </summary>
        /// <param name="input">買賣預定單建立輸入資料</param>
        /// <returns>新建買賣預定單的ID</returns>
        public async Task<int> CreatePurchaseOrderAsync(PurchaseOrderCreateInput input)
        {
            ArgumentNullException.ThrowIfNull(input);

            Unit? unit = null;
            List<ParkingSpace> parkingSpaces = new();

            // 檢查客戶是否存在
            if (!await Db.Customers.AnyAsync(c => c.CustomerId == input.CustomerId))
            {
                throw new Exception($"指定的客戶ID '{input.CustomerId}' 不存在。");
            }

            // 檢查房屋單位 (如果有提供)
            if (input.UnitId.HasValue)
            {
                unit = await Db.Units.FirstOrDefaultAsync(u => u.UnitId == input.UnitId.Value);
                if (unit == null)
                {
                    throw new Exception($"指定的房屋單位ID '{input.UnitId.Value}' 不存在。");
                }

                // 檢查房屋單位狀態是否可售
                if (unit.Status != "可售")
                {
                    throw new Exception($"房屋單位 '{unit.UnitNumber}' 目前狀態為 '{unit.Status}'，無法建立訂單。");
                }

                // 驗證 Unit 的 SiteCode 是否與訂單一致
                if (unit.SiteCode != input.SiteCode)
                {
                    throw new Exception($"房屋單位的案場 '{unit.SiteCode}' 與訂單案場 '{input.SiteCode}' 不符。");
                }
            }

            // 檢查停車位 (如果有提供)
            List<int> parkingSpaceIds = new();
            if (!string.IsNullOrEmpty(input.PurchasedParkingSpaceIds))
            {
                parkingSpaceIds = ParkingSpaceService.ParseParkingSpaceIds(input.PurchasedParkingSpaceIds);
                if (parkingSpaceIds.Any())
                {
                    parkingSpaces = await Db.ParkingSpaces
                        .Where(p => parkingSpaceIds.Contains(p.ParkingSpaceId))
                        .ToListAsync();

                    if (parkingSpaces.Count != parkingSpaceIds.Count)
                    {
                        var foundIds = parkingSpaces.Select(p => p.ParkingSpaceId).ToList();
                        var notFoundIds = parkingSpaceIds.Except(foundIds);
                        throw new Exception($"找不到以下停車位ID: {string.Join(", ", notFoundIds)}");
                    }

                    // 檢查車位的銷售狀態
                    var unavailableSpaces = parkingSpaces.Where(ps => ps.Status != "可售").ToList();
                    if (unavailableSpaces.Any())
                    {
                        throw new Exception($"以下車位目前無法建立訂單 (狀態非可售): {string.Join(", ", unavailableSpaces.Select(s => $"{s.SpaceNumber}({s.ParkingSpaceId})：{s.Status}"))}");
                    }
                    
                    // 驗證 ParkingSpace 的 SiteCode 是否與訂單一致
                    var mismatchedSiteSpaces = parkingSpaces.Where(p => p.SiteCode != input.SiteCode).ToList();
                    if (mismatchedSiteSpaces.Any())
                    {
                        throw new Exception($"以下停車位的案場與訂單案場 '{input.SiteCode}' 不符: {string.Join(", ", mismatchedSiteSpaces.Select(p => $"{p.SpaceNumber}(ID:{p.ParkingSpaceId}, Site:{p.SiteCode})"))}");
                    }
                }
            }

            // 生成 OrderNumber (這裡使用簡單示例，實際應有更複雜的規則)
            string orderNumber = $"{input.SiteCode}-{DateTime.Now:yyyyMMddHHmmss}-{input.CustomerId}";

            var order = new PurchaseOrder
            {
                OrderNumber = orderNumber,
                OrderDate = input.OrderDate,
                SiteCode = input.SiteCode,
                CustomerId = input.CustomerId,
                SalesAgencyName = input.SalesAgencyName,
                SalesAgencyMobile = input.SalesAgencyMobile,
                SalesAgencyLandline = input.SalesAgencyLandline,
                SalesAgencyEmail = input.SalesAgencyEmail,
                LandShareArea = input.LandShareArea,
                TotalPrice = input.TotalPrice,
                DepositAmount = input.DepositAmount,
                DepositPaidAmount = input.DepositPaidAmount,
                DepositPaymentMethod = input.DepositPaymentMethod,
                DepositPayee = input.DepositPayee,
                DepositDueDate = input.DepositDueDate,
                DepositBalanceAmount = input.DepositBalanceAmount,
                ContractSigningAppointment = input.ContractSigningAppointment,
                ContractSigningAmount = input.ContractSigningAmount,
                ConsentToDataUsage = input.ConsentToDataUsage,
                OrderRemarks = input.OrderRemarks,
                SalespersonUserInfoId = input.SalespersonUserInfoId,
                SaleType = input.SaleType,
                Status = input.Status, // 訂單初始狀態
                CreatedTime = DateTime.Now,
                UpdatedTime = DateTime.Now,
                CreatedUserInfoId = CurrentUser.UserId,
                UpdatedUserInfoId = CurrentUser.UserId
            };

            // 使用事務確保原子性
            using var transaction = await Db.Database.BeginTransactionAsync();
            try
            {
                Db.PurchaseOrders.Add(order);
                await Db.SaveChangesAsync();

                // 建立訂單項目
                var orderItems = new List<PurchaseOrderItem>();

                // 房屋單位項目
                if (input.UnitId.HasValue)
                {
                    orderItems.Add(new PurchaseOrderItem
                    {
                        OrderId = order.OrderId,
                        UnitId = input.UnitId.Value,
                        ParkingSpaceId = null,
                        ItemPrice = input.PropertyPrice ?? 0,
                        CreatedUserInfoId = CurrentUser.UserId,
                        CreateTime = DateTime.Now
                    });

                    // 更新房屋單位狀態
                    if (unit != null)
                    {
                        unit.Status = "已預訂";
                        unit.TransactionPrice = input.PropertyPrice;
                        unit.UpdatedTime = DateTime.Now;
                        unit.UpdatedUserInfoId = CurrentUser.UserId;
                        Db.Units.Update(unit);
                    }
                }

                // 車位項目
                if (parkingSpaceIds.Any())
                {
                    decimal avgPrice = (input.ParkingSpacePrice ?? 0) / parkingSpaceIds.Count;
                    foreach (var ps in parkingSpaces)
                    {
                        orderItems.Add(new PurchaseOrderItem
                        {
                            OrderId = order.OrderId,
                            UnitId = null,
                            ParkingSpaceId = (int)ps.ParkingSpaceId,
                            ItemPrice = avgPrice,
                            CreatedUserInfoId = CurrentUser.UserId,
                            CreateTime = DateTime.Now
                        });

                        // 更新車位狀態
                        ps.Status = "已預訂";
                        ps.TransactionPrice = avgPrice;
                        ps.UpdatedTime = DateTime.Now;
                        ps.UpdatedUserInfoId = CurrentUser.UserId;
                        Db.ParkingSpaces.Update(ps);
                    }
                }

                Db.PurchaseOrderItems.AddRange(orderItems);
                await Db.SaveChangesAsync();
                
                // 記錄訂單創建歷史
                await LogPurchaseOrderHistoryAsync(order.OrderId, "CREATE", null, order.Status, 
                    $"創建新訂單 - 客戶ID: {order.CustomerId}, 房屋單位ID: {input.UnitId}, 總價: {order.TotalPrice:C}", 
                    CurrentUser.UserId);
                
                await transaction.CommitAsync();
                return order.OrderId;
            }
            catch
            {
                await transaction.RollbackAsync();
                throw;
            }
        }

        /// <summary>
        /// 取得買賣預定單列表 (分頁)
        /// </summary>
        /// <param name="input">查詢條件</param>
        /// <returns>分頁的買賣預定單列表</returns>
        public async Task<PagedListOutput<PurchaseOrderListOutput>> GetPurchaseOrderListAsync(PurchaseOrderQueryInput input)
        {
            ArgumentNullException.ThrowIfNull(input);

            var query = Db.PurchaseOrders.AsQueryable();

            // 基本篩選
            if (!string.IsNullOrEmpty(input.SiteCode))
                query = query.Where(po => po.SiteCode == input.SiteCode);
            if (input.CustomerId.HasValue)
                query = query.Where(po => po.CustomerId == input.CustomerId.Value);
            if (!string.IsNullOrEmpty(input.OrderNumber))
                query = query.Where(po => po.OrderNumber.Contains(input.OrderNumber));
            if (input.SaleDateStart.HasValue)
                query = query.Where(po => po.SaleDate >= input.SaleDateStart.Value);
            if (input.SaleDateEnd.HasValue)
                query = query.Where(po => po.SaleDate <= input.SaleDateEnd.Value);
            if (!string.IsNullOrEmpty(input.Status))
                query = query.Where(po => po.Status == input.Status);
                
            // 日期範圍查詢
            if (input.DepositFullPaidDateStart.HasValue)
                query = query.Where(po => po.DepositFullPaidDate >= input.DepositFullPaidDateStart.Value);
            if (input.DepositFullPaidDateEnd.HasValue)
                query = query.Where(po => po.DepositFullPaidDate <= input.DepositFullPaidDateEnd.Value);
            if (input.ContractSignedDateStart.HasValue)
                query = query.Where(po => po.ContractSignedDate >= input.ContractSignedDateStart.Value);
            if (input.ContractSignedDateEnd.HasValue)
                query = query.Where(po => po.ContractSignedDate <= input.ContractSignedDateEnd.Value);
            if (input.CancellationDateStart.HasValue)
                query = query.Where(po => po.CancellationDate >= input.CancellationDateStart.Value);
            if (input.CancellationDateEnd.HasValue)
                query = query.Where(po => po.CancellationDate <= input.CancellationDateEnd.Value);

            // 需要 join 的篩選條件
            if (!string.IsNullOrEmpty(input.CustomerName))
                query = query.Where(po => Db.Customers.Any(c => c.CustomerId == po.CustomerId && c.Name.Contains(input.CustomerName)));
            if (!string.IsNullOrEmpty(input.UnitNumber))
                query = query.Where(po => Db.PurchaseOrderItems.Any(poi => poi.OrderId == po.OrderId && poi.UnitId.HasValue &&
                    Db.Units.Any(u => u.UnitId == poi.UnitId && u.UnitNumber.Contains(input.UnitNumber))));
            if (!string.IsNullOrEmpty(input.SalespersonName))
                query = query.Where(po => Db.UserInfos.Any(ui => ui.UserInfoId == po.SalespersonUserInfoId && ui.Name.Contains(input.SalespersonName)));

            // 投影到 DTO
            var projectedQuery = query
                .OrderByDescending(po => po.OrderDate)
                .Select(po => new PurchaseOrderListOutputInternal
                {
                    OrderId = po.OrderId,
                    OrderNumber = po.OrderNumber,
                    OrderDate = po.OrderDate,
                    SiteCode = po.SiteCode,
                    CustomerId = po.CustomerId,
                    UnitId = null, // 將通過 PurchaseOrderItems 查詢
                    TotalPrice = po.TotalPrice,
                    SalespersonUserInfoId = po.SalespersonUserInfoId,
                    Status = po.Status,
                    CreatedTime = po.CreatedTime,
                    PurchasedParkingSpaceIdsInternal = string.Empty,
                    CustomerName = null,
                    UnitNumber = null,
                    BuildingName = null,
                    FloorLabel = null,
                    SalespersonName = null
                });

            // 執行分頁查詢
            var pagedResult = await projectedQuery.ToPagedListOutputAsync(input);

            // 手動查詢關聯資料
            if (pagedResult.Details.Any())
            {
                var orderIds = pagedResult.Details.Select(o => o.OrderId).ToList();

                // 查詢訂單項目
                var orderItems = await Db.PurchaseOrderItems
                    .Where(poi => orderIds.Contains(poi.OrderId))
                    .ToListAsync();

                // 填充房屋單位ID
                foreach (var order in pagedResult.Details)
                {
                    var unitItem = orderItems.FirstOrDefault(oi => oi.OrderId == order.OrderId && oi.UnitId.HasValue);
                    if (unitItem != null)
                    {
                        order.UnitId = unitItem.UnitId;
                    }
                }

                // 查詢其他關聯資料
                var customerIds = pagedResult.Details.Select(o => o.CustomerId).Distinct().ToList();
                var unitIds = pagedResult.Details.Where(o => o.UnitId.HasValue).Select(o => o.UnitId!.Value).Distinct().ToList();
                var salespersonIds = pagedResult.Details.Select(o => o.SalespersonUserInfoId).Distinct().ToList();

                var customers = await Db.Customers
                    .Where(c => customerIds.Contains(c.CustomerId))
                    .ToDictionaryAsync(c => c.CustomerId, c => c.Name);

                var units = await Db.Units
                    .Where(u => unitIds.Contains(u.UnitId))
                    .Select(u => new {
                        u.UnitId,
                        u.UnitNumber,
                        FloorLabel = Db.Floors.Where(f => f.FloorId == u.FloorId).Select(f => f.FloorLabel).FirstOrDefault(),
                        BuildingName = Db.Buildings.Where(b => b.BuildingId == u.BuildingId).Select(b => b.BuildingName).FirstOrDefault()
                    })
                    .ToDictionaryAsync(u => u.UnitId);

                var salespersons = await Db.UserInfos
                    .Where(ui => salespersonIds.Contains(ui.UserInfoId))
                    .ToDictionaryAsync(ui => ui.UserInfoId, ui => ui.Name);

                // 處理車位資訊
                var parkingSpaceItems = orderItems.Where(oi => oi.ParkingSpaceId.HasValue).ToList();
                var parkingSpaceIds = parkingSpaceItems.Select(oi => oi.ParkingSpaceId!.Value).ToList();
                
                Dictionary<long, string> spaceInfos = new();
                if (parkingSpaceIds.Any())
                {
                    var longParkingSpaceIds = parkingSpaceIds.Select(id => (long)id).ToList();
                    spaceInfos = await (from ps in Db.ParkingSpaces
                                      where longParkingSpaceIds.Contains(ps.ParkingSpaceId)
                                      select new { ps.ParkingSpaceId, ps.SpaceNumber })
                                     .ToDictionaryAsync(x => (long)x.ParkingSpaceId, x => x.SpaceNumber ?? string.Empty);
                }

                // 填充所有關聯資料
                foreach (var orderOutput in pagedResult.Details)
                {
                    if (customers.TryGetValue(orderOutput.CustomerId, out var customerName))
                        orderOutput.CustomerName = customerName;

                    if (orderOutput.UnitId.HasValue && units.TryGetValue(orderOutput.UnitId.Value, out var unitInfo))
                    {
                        orderOutput.UnitNumber = unitInfo.UnitNumber;
                        orderOutput.BuildingName = unitInfo.BuildingName;
                        orderOutput.FloorLabel = unitInfo.FloorLabel;
                    }

                    if (salespersons.TryGetValue(orderOutput.SalespersonUserInfoId, out var salespersonName))
                        orderOutput.SalespersonName = salespersonName;

                    // 填充車位資訊
                    var orderParkingItems = parkingSpaceItems.Where(oi => oi.OrderId == orderOutput.OrderId);
                    if (orderParkingItems.Any())
                    {
                        var spaceNumbers = orderParkingItems
                            .Where(oi => oi.ParkingSpaceId.HasValue && spaceInfos.ContainsKey(oi.ParkingSpaceId.Value))
                            .Select(oi => spaceInfos[oi.ParkingSpaceId!.Value])
                            .ToList();
                        orderOutput.PurchasedParkingSpaceNumbers = string.Join(", ", spaceNumbers);
                    }
                }
            }

            // 轉換為最終結果
            var finalResult = new PagedListOutput<PurchaseOrderListOutput>(input)
            {
                RecordCount = pagedResult.RecordCount,
                Details = pagedResult.Details.Select(o => new PurchaseOrderListOutput {
                    OrderId = o.OrderId,
                    OrderNumber = o.OrderNumber,
                    OrderDate = o.OrderDate,
                    SiteCode = o.SiteCode,
                    CustomerId = o.CustomerId,
                    CustomerName = o.CustomerName,
                    UnitId = o.UnitId,
                    UnitNumber = o.UnitNumber,
                    BuildingName = o.BuildingName,
                    FloorLabel = o.FloorLabel,
                    TotalPrice = o.TotalPrice,
                    SalespersonUserInfoId = o.SalespersonUserInfoId,
                    SalespersonName = o.SalespersonName,
                    Status = o.Status,
                    CreatedTime = o.CreatedTime,
                    PurchasedParkingSpaceNumbers = o.PurchasedParkingSpaceNumbers
                }).ToList()
            };

            return finalResult;
        }

        /// <summary>
        /// 根據ID取得買賣預定單詳細資料
        /// </summary>
        /// <param name="orderId">買賣預定單ID</param>
        /// <returns>買賣預定單詳細資料</returns>  
        public async Task<PurchaseOrderOutput?> GetPurchaseOrderByIdAsync(int orderId)
        {
            var order = await Db.PurchaseOrders
                .Where(po => po.OrderId == orderId)
                .Select(po => new PurchaseOrderOutput
                {
                    OrderId = po.OrderId,
                    OrderNumber = po.OrderNumber,
                    OrderDate = po.OrderDate,
                    SiteCode = po.SiteCode,
                    CustomerId = po.CustomerId,
                    SalesAgencyName = po.SalesAgencyName,
                    SalesAgencyMobile = po.SalesAgencyMobile,
                    SalesAgencyLandline = po.SalesAgencyLandline,
                    SalesAgencyEmail = po.SalesAgencyEmail,
                    UnitId = null, // 將通過 PurchaseOrderItems 查詢
                    LandShareArea = po.LandShareArea,
                    PurchasedParkingSpaceIds = null,
                    PropertyPrice = null, // 將通過 PurchaseOrderItems 查詢
                    ParkingSpacePrice = null, // 將通過 PurchaseOrderItems 查詢
                    TotalPrice = po.TotalPrice,
                    DepositAmount = po.DepositAmount,
                    DepositPaidAmount = po.DepositPaidAmount,
                    DepositPaymentMethod = po.DepositPaymentMethod,
                    DepositPayee = po.DepositPayee,
                    DepositDueDate = po.DepositDueDate,
                    DepositBalanceAmount = po.DepositBalanceAmount,
                    ContractSigningAppointment = po.ContractSigningAppointment,
                    ContractSigningAmount = po.ContractSigningAmount,
                    ConsentToDataUsage = po.ConsentToDataUsage,
                    OrderRemarks = po.OrderRemarks,
                    SalespersonUserInfoId = po.SalespersonUserInfoId,
                    SaleType = po.SaleType,
                    Status = po.Status,
                    SaleDate = po.SaleDate,
                    DepositFullPaidDate = po.DepositFullPaidDate,
                    ContractSignedDate = po.ContractSignedDate,
                    CancellationDate = po.CancellationDate,
                    HandoverDate = po.HandoverDate,
                    FinalPaymentDate = po.FinalPaymentDate,
                    RequestDate = po.RequestDate,
                    ReceiveDate = po.ReceiveDate,
                    CreatedTime = po.CreatedTime,
                    UpdatedTime = po.UpdatedTime,
                    CreatedUserInfoId = po.CreatedUserInfoId,
                    UpdatedUserInfoId = po.UpdatedUserInfoId
                })
                .FirstOrDefaultAsync();

            if (order == null)
                return null;

            // 查詢訂單項目
            var orderItems = await Db.PurchaseOrderItems
                .Where(poi => poi.OrderId == orderId)
                .ToListAsync();

            // 填充房屋和車位資訊
            var unitItem = orderItems.FirstOrDefault(oi => oi.UnitId.HasValue);
            if (unitItem != null)
            {
                order.UnitId = unitItem.UnitId;
                order.PropertyPrice = unitItem.ItemPrice;
            }

            var parkingItems = orderItems.Where(oi => oi.ParkingSpaceId.HasValue).ToList();
            if (parkingItems.Any())
            {
                order.ParkingSpacePrice = parkingItems.Sum(pi => pi.ItemPrice);
                
                // 查詢車位詳細資訊
                var parkingSpaceIds = parkingItems.Select(pi => (long)pi.ParkingSpaceId!.Value).ToList();
                order.PurchasedParkingSpaces = await (from ps in Db.ParkingSpaces
                                                    join f in Db.Floors on ps.FloorId equals f.FloorId
                                                    join b in Db.Buildings on ps.BuildingId equals b.BuildingId
                                                    where parkingSpaceIds.Contains(ps.ParkingSpaceId)
                                                    select new ParkingSpaceBasicInfoOutput
                                                    {
                                                        ParkingSpaceId = (int)ps.ParkingSpaceId,
                                                        SpaceNumber = ps.SpaceNumber,
                                                        SpaceType = ps.SpaceType,
                                                        FloorLabel = f.FloorLabel,
                                                        BuildingName = b.BuildingName
                                                    }).ToListAsync();

                var parkingSpaceIdsStr = string.Join(",", parkingSpaceIds);
                order.PurchasedParkingSpaceIds = parkingSpaceIdsStr;
            }

            // 查詢其他關聯資料
            var customer = await Db.Customers.Where(c => c.CustomerId == order.CustomerId).Select(c => new { c.Name, c.PhoneNumber }).FirstOrDefaultAsync();
            if (customer != null)
            {
                order.CustomerName = customer.Name;
                order.CustomerPhoneNumber = customer.PhoneNumber;
            }

            if (order.UnitId.HasValue)
            {
                var unitInfo = await Db.Units
                    .Where(u => u.UnitId == order.UnitId.Value)
                    .Select(u => new {
                        u.UnitNumber,
                        u.Layout,
                        u.TotalArea,
                        FloorLabel = Db.Floors.Where(f => f.FloorId == u.FloorId).Select(f => f.FloorLabel).FirstOrDefault(),
                        BuildingName = Db.Buildings.Where(b => b.BuildingId == u.BuildingId).Select(b => b.BuildingName).FirstOrDefault()
                    }).FirstOrDefaultAsync();
                if (unitInfo != null)
                {
                    order.UnitNumber = unitInfo.UnitNumber;
                    order.UnitLayout = unitInfo.Layout;
                    order.UnitTotalArea = unitInfo.TotalArea;
                    order.FloorLabel = unitInfo.FloorLabel;
                    order.BuildingName = unitInfo.BuildingName;
                }
            }

            var userIds = new[] { order.SalespersonUserInfoId, order.CreatedUserInfoId, order.UpdatedUserInfoId }
                .Where(id => !string.IsNullOrEmpty(id))
                .Distinct()
                .ToList();
            if (userIds.Any())
            {
                var users = await Db.UserInfos
                    .Where(ui => userIds.Contains(ui.UserInfoId))
                    .ToDictionaryAsync(ui => ui.UserInfoId, ui => ui.Name);

                if (users.TryGetValue(order.SalespersonUserInfoId, out var salespersonName))
                    order.SalespersonName = salespersonName;
                if (users.TryGetValue(order.CreatedUserInfoId, out var createdName))
                    order.CreatedUserName = createdName;
                if (!string.IsNullOrEmpty(order.UpdatedUserInfoId) && users.TryGetValue(order.UpdatedUserInfoId, out var updatedName))
                    order.UpdatedUserName = updatedName;
            }

            return order;
        }

        /// <summary>
        /// 更新買賣預定單資訊
        /// </summary>
        /// <param name="orderId">買賣預定單ID</param>
        /// <param name="input">買賣預定單更新輸入資料</param>
        public async Task UpdatePurchaseOrderAsync(int orderId, PurchaseOrderUpdateInput input)
        {
            var order = await Db.PurchaseOrders.Include(po => po.PurchaseOrderItems).FirstOrDefaultAsync(po => po.OrderId == orderId);
            if (order == null)
            {
                throw new Exception("找不到指定的訂單。");
            }

            order.OrderDate = input.OrderDate;
            order.SalesAgencyName = input.SalesAgencyName;
            order.SalesAgencyMobile = input.SalesAgencyMobile;
            order.SalesAgencyLandline = input.SalesAgencyLandline;
            order.SalesAgencyEmail = input.SalesAgencyEmail;
            order.LandShareArea = input.LandShareArea;
            order.TotalPrice = input.TotalPrice;
            order.DepositAmount = input.DepositAmount;
            order.DepositPaidAmount = input.DepositPaidAmount;
            order.DepositPaymentMethod = input.DepositPaymentMethod;
            order.DepositPayee = input.DepositPayee;
            order.DepositDueDate = input.DepositDueDate;
            order.DepositBalanceAmount = input.DepositBalanceAmount;
            order.ContractSigningAppointment = input.ContractSigningAppointment;
            order.ContractSigningAmount = input.ContractSigningAmount;
            order.ConsentToDataUsage = input.ConsentToDataUsage;
            order.OrderRemarks = input.OrderRemarks;
            order.SaleType = input.SaleType;
            order.Status = input.Status;
            order.UpdatedTime = DateTime.Now;
            order.UpdatedUserInfoId = CurrentUser.UserId;

            var itemsToRemove = new List<PurchaseOrderItem>();
            var newUnitId = input.UnitId;
            var newParkingSpaceIds = ParkingSpaceService.ParseParkingSpaceIds(input.PurchasedParkingSpaceIds);

            foreach (var item in order.PurchaseOrderItems)
            {
                if (item.UnitId.HasValue && item.UnitId != newUnitId)
                {
                    var unit = await Db.Units.FindAsync(item.UnitId.Value);
                    if (unit != null)
                    {
                        unit.Status = "可售";
                        Db.Units.Update(unit);
                    }
                    itemsToRemove.Add(item);
                }
                else if (item.ParkingSpaceId.HasValue && !newParkingSpaceIds.Contains(item.ParkingSpaceId.Value))
                {
                    var ps = await Db.ParkingSpaces.FindAsync(item.ParkingSpaceId.Value);
                    if (ps != null)
                    {
                        ps.Status = "可售";
                        Db.ParkingSpaces.Update(ps);
                    }
                    itemsToRemove.Add(item);
                }
            }
            Db.PurchaseOrderItems.RemoveRange(itemsToRemove);

            if (newUnitId.HasValue && !order.PurchaseOrderItems.Any(i => i.UnitId == newUnitId))
            {
                order.PurchaseOrderItems.Add(new PurchaseOrderItem
                {
                    UnitId = newUnitId,
                    ItemPrice = input.PropertyPrice ?? 0,
                    CreatedUserInfoId = CurrentUser.UserId,
                    CreateTime = DateTime.Now
                });
                var unit = await Db.Units.FindAsync(newUnitId.Value);
                if (unit != null)
                {
                    unit.Status = "已預訂";
                    Db.Units.Update(unit);
                }
            }

            var existingParkingIds = order.PurchaseOrderItems.Where(i => i.ParkingSpaceId.HasValue).Select(i => i.ParkingSpaceId.Value).ToList();
            var parkingIdsToAdd = newParkingSpaceIds.Except(existingParkingIds).ToList();
            if (parkingIdsToAdd.Any())
            {
                decimal avgPrice = newParkingSpaceIds.Any() ? (input.ParkingSpacePrice ?? 0) / newParkingSpaceIds.Count : 0;
                foreach (var psId in parkingIdsToAdd)
                {
                    order.PurchaseOrderItems.Add(new PurchaseOrderItem
                    {
                        ParkingSpaceId = psId,
                        ItemPrice = avgPrice,
                        CreatedUserInfoId = CurrentUser.UserId,
                        CreateTime = DateTime.Now
                    });
                    var ps = await Db.ParkingSpaces.FindAsync(psId);
                    if (ps != null)
                    {
                        ps.Status = "已預訂";
                        Db.ParkingSpaces.Update(ps);
                    }
                }
            }

            if (input.PropertyPrice.HasValue)
            {
                var unitItem = order.PurchaseOrderItems.FirstOrDefault(oi => oi.UnitId.HasValue);
                if (unitItem != null) unitItem.ItemPrice = input.PropertyPrice.Value;
            }
            if (input.ParkingSpacePrice.HasValue)
            {
                var parkingItems = order.PurchaseOrderItems.Where(oi => oi.ParkingSpaceId.HasValue).ToList();
                if (parkingItems.Any())
                {
                    decimal avgPrice = input.ParkingSpacePrice.Value / parkingItems.Count;
                    foreach (var item in parkingItems)
                    {
                        item.ItemPrice = avgPrice;
                    }
                }
            }
            
            await Db.SaveChangesAsync();
            await LogPurchaseOrderHistoryAsync(orderId, "UPDATE", order.Status, input.Status, 
                "更新訂單內容", CurrentUser.UserId);
        }

        /// <summary>
        /// 刪除買賣預定單
        /// </summary>
        /// <param name="orderId">買賣預定單ID</param>
        public async Task DeletePurchaseOrderAsync(int orderId)
        {
            var order = await Db.PurchaseOrders.Include(po => po.PurchaseOrderItems).FirstOrDefaultAsync(po => po.OrderId == orderId);
            if (order == null)
            {
                throw new Exception("找不到指定的訂單。");
            }

            foreach (var item in order.PurchaseOrderItems)
            {
                if (item.UnitId.HasValue)
                {
                    var unit = await Db.Units.FindAsync(item.UnitId.Value);
                    if (unit != null)
                    {
                        unit.Status = "可售";
                        Db.Units.Update(unit);
                    }
                }
                if (item.ParkingSpaceId.HasValue)
                {
                    var ps = await Db.ParkingSpaces.FindAsync(item.ParkingSpaceId.Value);
                    if (ps != null)
                    {
                        ps.Status = "可售";
                        Db.ParkingSpaces.Update(ps);
                    }
                }
            }

            Db.PurchaseOrders.Remove(order);
            await Db.SaveChangesAsync();
            await LogPurchaseOrderHistoryAsync(orderId, "DELETE", order.Status, null, 
                "刪除訂單", CurrentUser.UserId);
        }

        /// <summary>
        /// 更新訂單業務流程日期
        /// </summary>
        /// <param name="orderId">訂單ID</param>
        /// <param name="input">業務日期更新輸入</param>
        public async Task UpdateBusinessDatesAsync(int orderId, dynamic input)
        {
            var order = await Db.PurchaseOrders.FindAsync(orderId);
            if (order == null)
            {
                throw new Exception("找不到指定的訂單。");
            }

            if (input.SaleDate != null) order.SaleDate = input.SaleDate;
            if (input.DepositFullPaidDate != null) order.DepositFullPaidDate = input.DepositFullPaidDate;
            if (input.ContractSignedDate != null) order.ContractSignedDate = input.ContractSignedDate;
            if (input.CancellationDate != null) order.CancellationDate = input.CancellationDate;
            if (input.HandoverDate != null) order.HandoverDate = input.HandoverDate;
            if (input.FinalPaymentDate != null) order.FinalPaymentDate = input.FinalPaymentDate;
            if (input.RequestDate != null) order.RequestDate = input.RequestDate;
            if (input.ReceiveDate != null) order.ReceiveDate = input.ReceiveDate;
            if (input.PriceRegistrationSubmissionDate != null) order.PriceRegistrationSubmissionDate = input.PriceRegistrationSubmissionDate;

            order.UpdatedTime = DateTime.Now;
            order.UpdatedUserInfoId = CurrentUser.UserId;

            await Db.SaveChangesAsync();
            await LogPurchaseOrderHistoryAsync(orderId, "UPDATE_DATES", order.Status, order.Status, 
                "更新訂單業務日期", CurrentUser.UserId);
        }

        /// <summary>
        /// 記錄訂單歷史
        /// </summary>
        private async Task LogPurchaseOrderHistoryAsync(int orderId, string actionType, string? oldStatus, string? newStatus, string contentRecord, string userInfoId)
        {
            var history = new PurchaseOrdersHistory
            {
                OrderId = orderId,
                ActionType = actionType,
                OldStatus = oldStatus,
                NewStatus = newStatus,
                ContentRecord = contentRecord,
                CreatedTime = DateTime.Now,
                CreatedUserInfoId = userInfoId
            };
            Db.PurchaseOrdersHistories.Add(history);
            await Db.SaveChangesAsync();
        }
    }

    // 為了 GetPurchaseOrderListAsync 中的 Select 和 ToPagedListOutputAsync 正常工作
    public class PurchaseOrderListOutputInternal : PurchaseOrderListOutput
    {
        public string? PurchasedParkingSpaceIdsInternal { get; set; }
    }
} 
