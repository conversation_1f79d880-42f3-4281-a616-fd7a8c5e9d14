﻿using System;
using System.Collections.Generic;

namespace AlifeApi.DataAccess.ProjectContent
{
    /// <summary>
    /// 員工部門關聯表，用於儲存員工與部門的關聯關係，支持員工隸屬多個部門。
    /// </summary>
    public partial class UserDepartment
    {
        /// <summary>
        /// 員工編號，主鍵的一部分，對應 UserInfo 表的 UserInfoId。
        /// </summary>
        public string UserInfoId { get; set; }
        /// <summary>
        /// 部門編號，主鍵的一部分，對應 Departments 表的 DepartmentId。
        /// </summary>
        public string DepartmentId { get; set; }
        /// <summary>
        /// 是否為主要部門，true 表示是，false 表示否，預設為 false。
        /// </summary>
        public bool? IsPrimary { get; set; }

        public virtual UserInfo UserInfo { get; set; }
    }
}
