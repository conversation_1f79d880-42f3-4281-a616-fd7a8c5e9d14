using System;
using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;

namespace AlifeApi.BusinessRules.CustomerModels
{
    /// <summary>
    /// 客戶訪談紀錄輸入 DTO
    /// </summary>
    public class CustomerRecordInput
    {
        /// <summary>
        /// 記錄唯一識別碼 (用於更新，新增時為 0 或 null)
        /// </summary>
        [JsonPropertyName("CustomerRecordId")]
        public int? CustomerRecordId { get; set; } // 使用 nullable int

        /// <summary>
        /// 互動或記錄的類型
        /// </summary>
        [JsonPropertyName("RecordType")]
        [Required(ErrorMessage = "記錄類型為必填")]
        public string RecordType { get; set; }

        /// <summary>
        /// 詳細的記錄內容
        /// </summary>
        [JsonPropertyName("Notes")]
        [Required(ErrorMessage = "記錄內容為必填")]
        public string Notes { get; set; }

        /// <summary>
        /// 互動發生的實際時間或記錄時間 (預設為當前時間)
        /// </summary>
        [JsonPropertyName("RecordedAt")]
        public DateTime? RecordedAt { get; set; } // 允許空值，由服務端處理預設值

        /// <summary>
        /// 客戶分級或標籤
        /// </summary>
        [JsonPropertyName("CustomerLevel")]
        public string CustomerLevel { get; set; }

        /// <summary>
        /// 負責此次互動的人員資訊 (通常是登入者，但允許前端指定)
        /// </summary>
        [JsonPropertyName("HandledBy")]
        public string HandledBy { get; set; }
    }
} 
