#### [AlifeApi.BusinessRules](index.md 'index')
### [AlifeApi.BusinessRules.CategoryModels](AlifeApi.BusinessRules.CategoryModels.md 'AlifeApi.BusinessRules.CategoryModels')

## MediumCategoryListOutput Class

中分類列表輸出 DTO (摘要資訊)

```csharp
public class MediumCategoryListOutput
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; MediumCategoryListOutput
### Properties

<a name='AlifeApi.BusinessRules.CategoryModels.MediumCategoryListOutput.CreateTime'></a>

## MediumCategoryListOutput.CreateTime Property

建立時間

```csharp
public System.DateTime CreateTime { get; set; }
```

#### Property Value
[System.DateTime](https://docs.microsoft.com/en-us/dotnet/api/System.DateTime 'System.DateTime')

<a name='AlifeApi.BusinessRules.CategoryModels.MediumCategoryListOutput.Description'></a>

## MediumCategoryListOutput.Description Property

中分類描述

```csharp
public string Description { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.CategoryModels.MediumCategoryListOutput.IsActive'></a>

## MediumCategoryListOutput.IsActive Property

是否啟用

```csharp
public System.Nullable<bool> IsActive { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.Boolean](https://docs.microsoft.com/en-us/dotnet/api/System.Boolean 'System.Boolean')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

<a name='AlifeApi.BusinessRules.CategoryModels.MediumCategoryListOutput.LargeCategoryId'></a>

## MediumCategoryListOutput.LargeCategoryId Property

所屬大分類ID

```csharp
public long LargeCategoryId { get; set; }
```

#### Property Value
[System.Int64](https://docs.microsoft.com/en-us/dotnet/api/System.Int64 'System.Int64')

<a name='AlifeApi.BusinessRules.CategoryModels.MediumCategoryListOutput.LargeCategoryName'></a>

## MediumCategoryListOutput.LargeCategoryName Property

所屬大分類名稱

```csharp
public string LargeCategoryName { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.CategoryModels.MediumCategoryListOutput.MediumCategoryId'></a>

## MediumCategoryListOutput.MediumCategoryId Property

中分類ID

```csharp
public long MediumCategoryId { get; set; }
```

#### Property Value
[System.Int64](https://docs.microsoft.com/en-us/dotnet/api/System.Int64 'System.Int64')

<a name='AlifeApi.BusinessRules.CategoryModels.MediumCategoryListOutput.Name'></a>

## MediumCategoryListOutput.Name Property

中分類名稱

```csharp
public string Name { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.CategoryModels.MediumCategoryListOutput.SortOrder'></a>

## MediumCategoryListOutput.SortOrder Property

排序順序

```csharp
public int SortOrder { get; set; }
```

#### Property Value
[System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')