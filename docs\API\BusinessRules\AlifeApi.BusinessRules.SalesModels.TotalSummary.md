#### [AlifeApi.BusinessRules](index.md 'index')
### [AlifeApi.BusinessRules.SalesModels](AlifeApi.BusinessRules.SalesModels.md 'AlifeApi.BusinessRules.SalesModels')

## TotalSummary Class

總體統計摘要

```csharp
public class TotalSummary
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; TotalSummary
### Properties

<a name='AlifeApi.BusinessRules.SalesModels.TotalSummary.OverallSalesRate'></a>

## TotalSummary.OverallSalesRate Property

整體去化百分比

```csharp
public string OverallSalesRate { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.SalesModels.TotalSummary.TotalAvailable'></a>

## TotalSummary.TotalAvailable Property

總可售戶數

```csharp
public int TotalAvailable { get; set; }
```

#### Property Value
[System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')

<a name='AlifeApi.BusinessRules.SalesModels.TotalSummary.TotalReserved'></a>

## TotalSummary.TotalReserved Property

總保留戶數

```csharp
public int TotalReserved { get; set; }
```

#### Property Value
[System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')

<a name='AlifeApi.BusinessRules.SalesModels.TotalSummary.TotalSold'></a>

## TotalSummary.TotalSold Property

總已售戶數

```csharp
public int TotalSold { get; set; }
```

#### Property Value
[System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')