#### [AlifeApi.BusinessRules](index.md 'index')
### [AlifeApi.BusinessRules.UserInfoModels](AlifeApi.BusinessRules.UserInfoModels.md 'AlifeApi.BusinessRules.UserInfoModels')

## UserInfoListItemGetOutput Class

```csharp
public class UserInfoListItemGetOutput
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; UserInfoListItemGetOutput
### Properties

<a name='AlifeApi.BusinessRules.UserInfoModels.UserInfoListItemGetOutput.DeptId'></a>

## UserInfoListItemGetOutput.DeptId Property

使用者單位

```csharp
public string DeptId { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.UserInfoModels.UserInfoListItemGetOutput.DeptName'></a>

## UserInfoListItemGetOutput.DeptName Property

使用者單位

```csharp
public string DeptName { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.UserInfoModels.UserInfoListItemGetOutput.Email'></a>

## UserInfoListItemGetOutput.Email Property

Email

```csharp
public string Email { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.UserInfoModels.UserInfoListItemGetOutput.GradeCode'></a>

## UserInfoListItemGetOutput.GradeCode Property

使用者職稱

```csharp
public string GradeCode { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.UserInfoModels.UserInfoListItemGetOutput.GradeName'></a>

## UserInfoListItemGetOutput.GradeName Property

使用者職稱

```csharp
public string GradeName { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.UserInfoModels.UserInfoListItemGetOutput.Id'></a>

## UserInfoListItemGetOutput.Id Property

員工編號

```csharp
public string Id { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.UserInfoModels.UserInfoListItemGetOutput.IsAdmin'></a>

## UserInfoListItemGetOutput.IsAdmin Property

是否為管理者

```csharp
public bool IsAdmin { get; set; }
```

#### Property Value
[System.Boolean](https://docs.microsoft.com/en-us/dotnet/api/System.Boolean 'System.Boolean')

<a name='AlifeApi.BusinessRules.UserInfoModels.UserInfoListItemGetOutput.IsEnabled'></a>

## UserInfoListItemGetOutput.IsEnabled Property

是否啟用

```csharp
public bool IsEnabled { get; set; }
```

#### Property Value
[System.Boolean](https://docs.microsoft.com/en-us/dotnet/api/System.Boolean 'System.Boolean')

<a name='AlifeApi.BusinessRules.UserInfoModels.UserInfoListItemGetOutput.IsLock'></a>

## UserInfoListItemGetOutput.IsLock Property

是否鎖定

```csharp
public bool IsLock { get; set; }
```

#### Property Value
[System.Boolean](https://docs.microsoft.com/en-us/dotnet/api/System.Boolean 'System.Boolean')

<a name='AlifeApi.BusinessRules.UserInfoModels.UserInfoListItemGetOutput.LastLoginIP'></a>

## UserInfoListItemGetOutput.LastLoginIP Property

最新登入 IP

```csharp
public string LastLoginIP { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.UserInfoModels.UserInfoListItemGetOutput.LastLoginTime'></a>

## UserInfoListItemGetOutput.LastLoginTime Property

最新登入時間

```csharp
public System.Nullable<System.DateTime> LastLoginTime { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.DateTime](https://docs.microsoft.com/en-us/dotnet/api/System.DateTime 'System.DateTime')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

<a name='AlifeApi.BusinessRules.UserInfoModels.UserInfoListItemGetOutput.LoginFailedCount'></a>

## UserInfoListItemGetOutput.LoginFailedCount Property

登入失敗次數加總

```csharp
public short LoginFailedCount { get; set; }
```

#### Property Value
[System.Int16](https://docs.microsoft.com/en-us/dotnet/api/System.Int16 'System.Int16')

<a name='AlifeApi.BusinessRules.UserInfoModels.UserInfoListItemGetOutput.Roles'></a>

## UserInfoListItemGetOutput.Roles Property

角色群組

```csharp
public System.Collections.Generic.IEnumerable<AlifeApi.BusinessRules.RoleGroupModels.RoleOutput> Roles { get; set; }
```

#### Property Value
[System.Collections.Generic.IEnumerable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Collections.Generic.IEnumerable-1 'System.Collections.Generic.IEnumerable`1')[RoleOutput](AlifeApi.BusinessRules.RoleGroupModels.RoleOutput.md 'AlifeApi.BusinessRules.RoleGroupModels.RoleOutput')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Collections.Generic.IEnumerable-1 'System.Collections.Generic.IEnumerable`1')

<a name='AlifeApi.BusinessRules.UserInfoModels.UserInfoListItemGetOutput.UserIdNo'></a>

## UserInfoListItemGetOutput.UserIdNo Property

使用者員編

```csharp
public string UserIdNo { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.UserInfoModels.UserInfoListItemGetOutput.UserName'></a>

## UserInfoListItemGetOutput.UserName Property

使用者名稱

```csharp
public string UserName { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.UserInfoModels.UserInfoListItemGetOutput.UserPw'></a>

## UserInfoListItemGetOutput.UserPw Property

使用者密碼

```csharp
public string UserPw { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')