#### [AlifeApi.BusinessRules](index.md 'index')
### [AlifeApi.BusinessRules.ParkingSpaceModels](AlifeApi.BusinessRules.ParkingSpaceModels.md 'AlifeApi.BusinessRules.ParkingSpaceModels')

## ParkingSpaceOutput Class

停車位詳細輸出模型

```csharp
public class ParkingSpaceOutput
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; ParkingSpaceOutput
### Properties

<a name='AlifeApi.BusinessRules.ParkingSpaceModels.ParkingSpaceOutput.BuildingName'></a>

## ParkingSpaceOutput.BuildingName Property

建築物名稱 (選填)

```csharp
public string? BuildingName { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.ParkingSpaceModels.ParkingSpaceOutput.CreatedUserName'></a>

## ParkingSpaceOutput.CreatedUserName Property

建立者名稱 (選填)

```csharp
public string? CreatedUserName { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.ParkingSpaceModels.ParkingSpaceOutput.FloorLabel'></a>

## ParkingSpaceOutput.FloorLabel Property

樓層標示 (選填)

```csharp
public string? FloorLabel { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.ParkingSpaceModels.ParkingSpaceOutput.Location'></a>

## ParkingSpaceOutput.Location Property

車位詳細位置描述 (例如 "靠近電梯", "角落位置")

```csharp
public string? Location { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.ParkingSpaceModels.ParkingSpaceOutput.UpdatedUserName'></a>

## ParkingSpaceOutput.UpdatedUserName Property

更新者名稱 (選填)

```csharp
public string? UpdatedUserName { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')