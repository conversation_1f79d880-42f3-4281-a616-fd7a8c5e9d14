﻿using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;

namespace AlifeApi.BusinessRules.SysCodeModels
{
    /// <summary>
    /// 編輯、作廢 SysCode 代碼
    /// </summary>
    public class SysCodeUpdateInput
    {
        /// <summary>
        /// 代碼（取得資料用)
        /// </summary>
        public string Code { get; set; }

        /// <summary>
        /// 代碼類型（取得資料用)
        /// </summary>
        public string Type { get; set; }

        /// <summary>
        /// 代碼名稱
        /// </summary>
        [MaxLength(300)]
        public string Desc { get; set; }

        /// <summary>
        /// 代碼是否啟用
        /// </summary>
        [JsonPropertyName("IsActive")]
        public bool? IsActive { get; set; }
    }
}
