#### [AlifeApi.WebApi](index.md 'index')
### [AlifeApi.WebApi.Controllers](AlifeApi.WebApi.Controllers.md 'AlifeApi.WebApi.Controllers')

## SiteController Class

案場管理

```csharp
public class SiteController : AlifeApi.WebApi.Controllers.AuthenticatedController
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; [Microsoft.AspNetCore.Mvc.ControllerBase](https://docs.microsoft.com/en-us/dotnet/api/Microsoft.AspNetCore.Mvc.ControllerBase 'Microsoft.AspNetCore.Mvc.ControllerBase') &#129106; [Microsoft.AspNetCore.Mvc.Controller](https://docs.microsoft.com/en-us/dotnet/api/Microsoft.AspNetCore.Mvc.Controller 'Microsoft.AspNetCore.Mvc.Controller') &#129106; [AlifeApi.WebApi.Controllers.BasicController](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.WebApi.Controllers.BasicController 'AlifeApi.WebApi.Controllers.BasicController') &#129106; [AlifeApi.WebApi.Controllers.AuthenticatedController](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.WebApi.Controllers.AuthenticatedController 'AlifeApi.WebApi.Controllers.AuthenticatedController') &#129106; SiteController
### Constructors

<a name='AlifeApi.WebApi.Controllers.SiteController.SiteController(AlifeApi.BusinessRules.SiteModels.SiteService)'></a>

## SiteController(SiteService) Constructor

建構函數

```csharp
public SiteController(AlifeApi.BusinessRules.SiteModels.SiteService siteService);
```
#### Parameters

<a name='AlifeApi.WebApi.Controllers.SiteController.SiteController(AlifeApi.BusinessRules.SiteModels.SiteService).siteService'></a>

`siteService` [AlifeApi.BusinessRules.SiteModels.SiteService](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.BusinessRules.SiteModels.SiteService 'AlifeApi.BusinessRules.SiteModels.SiteService')
### Methods

<a name='AlifeApi.WebApi.Controllers.SiteController.CreateSiteAsync(AlifeApi.BusinessRules.SiteModels.SiteCreateInput)'></a>

## SiteController.CreateSiteAsync(SiteCreateInput) Method

創建案場

```csharp
public System.Threading.Tasks.Task<Microsoft.AspNetCore.Mvc.ActionResult> CreateSiteAsync(AlifeApi.BusinessRules.SiteModels.SiteCreateInput input);
```
#### Parameters

<a name='AlifeApi.WebApi.Controllers.SiteController.CreateSiteAsync(AlifeApi.BusinessRules.SiteModels.SiteCreateInput).input'></a>

`input` [AlifeApi.BusinessRules.SiteModels.SiteCreateInput](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.BusinessRules.SiteModels.SiteCreateInput 'AlifeApi.BusinessRules.SiteModels.SiteCreateInput')

案場創建輸入

#### Returns
[System.Threading.Tasks.Task&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')[Microsoft.AspNetCore.Mvc.ActionResult](https://docs.microsoft.com/en-us/dotnet/api/Microsoft.AspNetCore.Mvc.ActionResult 'Microsoft.AspNetCore.Mvc.ActionResult')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')  
執行結果

<a name='AlifeApi.WebApi.Controllers.SiteController.DeleteSiteAsync(string)'></a>

## SiteController.DeleteSiteAsync(string) Method

刪除案場

```csharp
public System.Threading.Tasks.Task<Microsoft.AspNetCore.Mvc.ActionResult> DeleteSiteAsync(string siteCode);
```
#### Parameters

<a name='AlifeApi.WebApi.Controllers.SiteController.DeleteSiteAsync(string).siteCode'></a>

`siteCode` [System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

案場代碼

#### Returns
[System.Threading.Tasks.Task&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')[Microsoft.AspNetCore.Mvc.ActionResult](https://docs.microsoft.com/en-us/dotnet/api/Microsoft.AspNetCore.Mvc.ActionResult 'Microsoft.AspNetCore.Mvc.ActionResult')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')  
執行結果

<a name='AlifeApi.WebApi.Controllers.SiteController.GetSiteByCodeAsync(string)'></a>

## SiteController.GetSiteByCodeAsync(string) Method

根據代碼取得案場

```csharp
public System.Threading.Tasks.Task<Microsoft.AspNetCore.Mvc.ActionResult<AlifeApi.BusinessRules.SiteModels.SiteOutput>> GetSiteByCodeAsync(string siteCode);
```
#### Parameters

<a name='AlifeApi.WebApi.Controllers.SiteController.GetSiteByCodeAsync(string).siteCode'></a>

`siteCode` [System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

案場代碼

#### Returns
[System.Threading.Tasks.Task&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')[Microsoft.AspNetCore.Mvc.ActionResult&lt;](https://docs.microsoft.com/en-us/dotnet/api/Microsoft.AspNetCore.Mvc.ActionResult-1 'Microsoft.AspNetCore.Mvc.ActionResult`1')[AlifeApi.BusinessRules.SiteModels.SiteOutput](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.BusinessRules.SiteModels.SiteOutput 'AlifeApi.BusinessRules.SiteModels.SiteOutput')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/Microsoft.AspNetCore.Mvc.ActionResult-1 'Microsoft.AspNetCore.Mvc.ActionResult`1')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')  
案場資訊

<a name='AlifeApi.WebApi.Controllers.SiteController.GetSiteDropdownListAsync()'></a>

## SiteController.GetSiteDropdownListAsync() Method

取得案場下拉選單

```csharp
public System.Threading.Tasks.Task<Microsoft.AspNetCore.Mvc.ActionResult<System.Collections.Generic.List<AlifeApi.BusinessRules.SiteModels.SiteDropdownOutput>>> GetSiteDropdownListAsync();
```

#### Returns
[System.Threading.Tasks.Task&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')[Microsoft.AspNetCore.Mvc.ActionResult&lt;](https://docs.microsoft.com/en-us/dotnet/api/Microsoft.AspNetCore.Mvc.ActionResult-1 'Microsoft.AspNetCore.Mvc.ActionResult`1')[System.Collections.Generic.List&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Collections.Generic.List-1 'System.Collections.Generic.List`1')[AlifeApi.BusinessRules.SiteModels.SiteDropdownOutput](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.BusinessRules.SiteModels.SiteDropdownOutput 'AlifeApi.BusinessRules.SiteModels.SiteDropdownOutput')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Collections.Generic.List-1 'System.Collections.Generic.List`1')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/Microsoft.AspNetCore.Mvc.ActionResult-1 'Microsoft.AspNetCore.Mvc.ActionResult`1')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')  
案場下拉選單列表

<a name='AlifeApi.WebApi.Controllers.SiteController.GetSiteListAsync(AlifeApi.BusinessRules.SiteModels.SiteListGetInput)'></a>

## SiteController.GetSiteListAsync(SiteListGetInput) Method

取得案場列表

```csharp
public System.Threading.Tasks.Task<AlifeApi.BusinessRules.Infrastructure.PagedListOutput<AlifeApi.BusinessRules.SiteModels.SiteOutput>> GetSiteListAsync(AlifeApi.BusinessRules.SiteModels.SiteListGetInput input);
```
#### Parameters

<a name='AlifeApi.WebApi.Controllers.SiteController.GetSiteListAsync(AlifeApi.BusinessRules.SiteModels.SiteListGetInput).input'></a>

`input` [AlifeApi.BusinessRules.SiteModels.SiteListGetInput](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.BusinessRules.SiteModels.SiteListGetInput 'AlifeApi.BusinessRules.SiteModels.SiteListGetInput')

查詢條件

#### Returns
[System.Threading.Tasks.Task&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')[AlifeApi.BusinessRules.Infrastructure.PagedListOutput&lt;](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.BusinessRules.Infrastructure.PagedListOutput-1 'AlifeApi.BusinessRules.Infrastructure.PagedListOutput`1')[AlifeApi.BusinessRules.SiteModels.SiteOutput](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.BusinessRules.SiteModels.SiteOutput 'AlifeApi.BusinessRules.SiteModels.SiteOutput')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.BusinessRules.Infrastructure.PagedListOutput-1 'AlifeApi.BusinessRules.Infrastructure.PagedListOutput`1')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')  
案場列表

<a name='AlifeApi.WebApi.Controllers.SiteController.UpdateSiteAsync(AlifeApi.BusinessRules.SiteModels.SiteUpdateInput)'></a>

## SiteController.UpdateSiteAsync(SiteUpdateInput) Method

更新案場

```csharp
public System.Threading.Tasks.Task<Microsoft.AspNetCore.Mvc.ActionResult> UpdateSiteAsync(AlifeApi.BusinessRules.SiteModels.SiteUpdateInput input);
```
#### Parameters

<a name='AlifeApi.WebApi.Controllers.SiteController.UpdateSiteAsync(AlifeApi.BusinessRules.SiteModels.SiteUpdateInput).input'></a>

`input` [AlifeApi.BusinessRules.SiteModels.SiteUpdateInput](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.BusinessRules.SiteModels.SiteUpdateInput 'AlifeApi.BusinessRules.SiteModels.SiteUpdateInput')

案場更新輸入

#### Returns
[System.Threading.Tasks.Task&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')[Microsoft.AspNetCore.Mvc.ActionResult](https://docs.microsoft.com/en-us/dotnet/api/Microsoft.AspNetCore.Mvc.ActionResult 'Microsoft.AspNetCore.Mvc.ActionResult')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')  
執行結果