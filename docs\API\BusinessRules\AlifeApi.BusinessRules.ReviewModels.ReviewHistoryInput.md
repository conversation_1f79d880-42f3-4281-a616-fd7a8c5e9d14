#### [AlifeApi.BusinessRules](index.md 'index')
### [AlifeApi.BusinessRules.ReviewModels](AlifeApi.BusinessRules.ReviewModels.md 'AlifeApi.BusinessRules.ReviewModels')

## ReviewHistoryInput Class

審核歷史記錄輸入模型

```csharp
public class ReviewHistoryInput
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; ReviewHistoryInput
### Properties

<a name='AlifeApi.BusinessRules.ReviewModels.ReviewHistoryInput.Action'></a>

## ReviewHistoryInput.Action Property

操作類型

```csharp
public string Action { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.ReviewModels.ReviewHistoryInput.Comment'></a>

## ReviewHistoryInput.Comment Property

評論內容

```csharp
public string Comment { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.ReviewModels.ReviewHistoryInput.StepId'></a>

## ReviewHistoryInput.StepId Property

審核步驟ID

```csharp
public System.Nullable<int> StepId { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

<a name='AlifeApi.BusinessRules.ReviewModels.ReviewHistoryInput.TaskId'></a>

## ReviewHistoryInput.TaskId Property

審核任務ID

```csharp
public int TaskId { get; set; }
```

#### Property Value
[System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')