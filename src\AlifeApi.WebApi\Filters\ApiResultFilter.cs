﻿using System.Reflection;
using AlifeApi.BusinessRules.Infrastructure;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;

namespace AlifeApi.WebApi.Filters
{
    public class ApiResultFilter : ResultFilterAttribute
    {
        public override void OnResultExecuting(ResultExecutingContext context)
        {
            if (!context.ModelState.IsValid)
            {
                return;
            }

            if (context.Result is EmptyResult || context.Result is OkResult)
            {
                context.Result = new JsonResult(new ApiResult
                {
                    Reponse = Message.Success
                });
            }
            else if (context.Result is ObjectResult objectResult
                && (objectResult.Value is null || !objectResult.Value.GetType().IsAssignableTo(typeof(ApiResult))))
            {
                bool isCustomEnum = objectResult.Value is not null
                    && objectResult.Value.GetType().IsAssignableTo(typeof(IApiMessage));
                if (isCustomEnum)
                {
                    ApiResult apiResult = new()
                    {
                        Reponse = ((IApiMessage)objectResult.Value).Response
                    };

                    if (apiResult.Code == "000")
                    {
                        bool hasOtherProperties = objectResult.Value.GetType()
                            .GetProperties(BindingFlags.Public | BindingFlags.Instance)
                            .Length > 1;
                        apiResult.Body = hasOtherProperties
                            ? objectResult.Value
                            : null;
                    }
                    context.Result = new JsonResult(apiResult);
                }
                else
                {
                    context.Result = new JsonResult(new ApiResult
                    {
                        Reponse = objectResult.StatusCode == 400
                            ? Message.SystemError
                            : Message.Success,
                        Body = objectResult.Value
                    });
                }
            }
            else if (context.Result is StatusCodeResult codeResult)
            {
                if (codeResult.StatusCode < 400 || codeResult.StatusCode >= 600)
                {
                    return;
                }

                context.Result = new JsonResult(new ApiResult
                {
                    Reponse = Enum.TryParse(typeof(Message), codeResult.StatusCode.ToString(), out object message)
                        ? (Message)message : Message.SystemError
                });
            }
        }
    }
}
