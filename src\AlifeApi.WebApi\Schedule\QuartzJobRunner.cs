﻿using Quartz.Spi;
using Quartz;

namespace AlifeApi.WebApi.Schedule
{
    public class QuartzJobRunner
    {
        private readonly ISchedulerFactory _schedulerFactory;
        private readonly IJobFactory _jobFactory;
        /// <summary>
        /// 系統設定
        /// </summary>
        private IConfiguration _configuration;
        private IScheduler _scheduler;

        public QuartzJobRunner(ISchedulerFactory schedulerFactory, IJobFactory jobFactory, IConfiguration configuration)
        {
            _schedulerFactory = schedulerFactory;
            _jobFactory = jobFactory;
            _configuration = configuration;
        }

        public async Task Start()
        {
            _scheduler = await _schedulerFactory.GetScheduler();
            _scheduler.JobFactory = _jobFactory;

            await _scheduler.Start();


            var schduleLst = _configuration.GetSection("ScheduleWork:Schedules").Get<List<SchedulesModel>>().Where(item => !string.IsNullOrEmpty(item.ClassType) && item.Enabld);
            if (schduleLst.Any())
            {
                List<int> Times = schduleLst.Select(x => x.CycleSeconds).Distinct().ToList();

                foreach (var t in Times)
                {

                    schduleLst.Where(x => x.CycleSeconds == t).ToList().ForEach(async item =>
                    {
                        Type type = Type.GetType(item.ClassType);

                        if (type != null)
                        {
                            var jobTrigger = TriggerBuilder.Create()
             .WithIdentity($"{item.ClassType.Split('.').Last()}", $"Group{t}")
             .WithSimpleSchedule(x => x.WithIntervalInSeconds(t).RepeatForever())
             .Build();
                            var job = JobBuilder.Create()
                  .OfType(type) // 将获取的 Type 对象传递给 OfType() 方法
                  .WithIdentity($"{item.ClassType.Split('.').Last()}", $"Group{t}")
                  .Build();
                            await _scheduler.ScheduleJob(job, jobTrigger);
                        }
                    });
                }
            }
        }

        public async Task Stop()
        {
            if (_scheduler != null)
            {
                await _scheduler.Shutdown();
            }
        }
    }
}
