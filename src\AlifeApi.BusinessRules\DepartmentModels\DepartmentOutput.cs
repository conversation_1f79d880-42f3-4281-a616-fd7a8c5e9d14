using System;
using System.Text.Json.Serialization;

namespace AlifeApi.BusinessRules.DepartmentModels
{
    /// <summary>
    /// 部門資料輸出
    /// </summary>
    public class DepartmentOutput
    {
        /// <summary>
        /// 公司ID
        /// </summary>
        [JsonPropertyName("CompanyId")]
        public string CompanyId { get; set; }

        /// <summary>
        /// 公司名稱
        /// </summary>
        [JsonPropertyName("CompanyName")]
        public string CompanyName { get; set; }

        /// <summary>
        /// 部門ID
        /// </summary>
        [JsonPropertyName("DepartmentId")]
        public string DepartmentId { get; set; }

        /// <summary>
        /// 部門名稱
        /// </summary>
        [JsonPropertyName("Name")]
        public string Name { get; set; }

        /// <summary>
        /// 建立時間
        /// </summary>
        [JsonPropertyName("CreatedTime")]
        public DateTime CreatedTime { get; set; }

        /// <summary>
        /// 建立者ID
        /// </summary>
        [JsonPropertyName("CreatedUserId")]
        public string CreatedUserId { get; set; }

        /// <summary>
        /// 建立者名稱
        /// </summary>
        [JsonPropertyName("CreatedUserName")]
        public string CreatedUserName { get; set; }

        /// <summary>
        /// 更新時間
        /// </summary>
        [JsonPropertyName("UpdatedTime")]
        public DateTime UpdatedTime { get; set; }

        /// <summary>
        /// 更新者ID
        /// </summary>
        [JsonPropertyName("UpdatedUserId")]
        public string UpdatedUserId { get; set; }

        /// <summary>
        /// 更新者名稱
        /// </summary>
        [JsonPropertyName("UpdatedUserName")]
        public string UpdatedUserName { get; set; }
    }
} 
