#### [AlifeApi.WebApi](index.md 'index')
### [AlifeApi.WebApi.Controllers](AlifeApi.WebApi.Controllers.md 'AlifeApi.WebApi.Controllers')

## LargeCategoriesController Class

大分類管理

```csharp
public class LargeCategoriesController : AlifeApi.WebApi.Controllers.AuthenticatedController
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; [Microsoft.AspNetCore.Mvc.ControllerBase](https://docs.microsoft.com/en-us/dotnet/api/Microsoft.AspNetCore.Mvc.ControllerBase 'Microsoft.AspNetCore.Mvc.ControllerBase') &#129106; [Microsoft.AspNetCore.Mvc.Controller](https://docs.microsoft.com/en-us/dotnet/api/Microsoft.AspNetCore.Mvc.Controller 'Microsoft.AspNetCore.Mvc.Controller') &#129106; [AlifeApi.WebApi.Controllers.BasicController](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.WebApi.Controllers.BasicController 'AlifeApi.WebApi.Controllers.BasicController') &#129106; [AlifeApi.WebApi.Controllers.AuthenticatedController](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.WebApi.Controllers.AuthenticatedController 'AlifeApi.WebApi.Controllers.AuthenticatedController') &#129106; LargeCategoriesController
### Methods

<a name='AlifeApi.WebApi.Controllers.LargeCategoriesController.CreateLargeCategory(AlifeApi.BusinessRules.CategoryModels.LargeCategoryInput)'></a>

## LargeCategoriesController.CreateLargeCategory(LargeCategoryInput) Method

新增大分類

```csharp
public System.Threading.Tasks.Task<Microsoft.AspNetCore.Mvc.IActionResult> CreateLargeCategory(AlifeApi.BusinessRules.CategoryModels.LargeCategoryInput input);
```
#### Parameters

<a name='AlifeApi.WebApi.Controllers.LargeCategoriesController.CreateLargeCategory(AlifeApi.BusinessRules.CategoryModels.LargeCategoryInput).input'></a>

`input` [AlifeApi.BusinessRules.CategoryModels.LargeCategoryInput](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.BusinessRules.CategoryModels.LargeCategoryInput 'AlifeApi.BusinessRules.CategoryModels.LargeCategoryInput')

新增大分類輸入資料

#### Returns
[System.Threading.Tasks.Task&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')[Microsoft.AspNetCore.Mvc.IActionResult](https://docs.microsoft.com/en-us/dotnet/api/Microsoft.AspNetCore.Mvc.IActionResult 'Microsoft.AspNetCore.Mvc.IActionResult')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')  
新增的大分類ID

<a name='AlifeApi.WebApi.Controllers.LargeCategoriesController.DeleteLargeCategory(long)'></a>

## LargeCategoriesController.DeleteLargeCategory(long) Method

刪除大分類

```csharp
public System.Threading.Tasks.Task<Microsoft.AspNetCore.Mvc.IActionResult> DeleteLargeCategory(long largeCategoryId);
```
#### Parameters

<a name='AlifeApi.WebApi.Controllers.LargeCategoriesController.DeleteLargeCategory(long).largeCategoryId'></a>

`largeCategoryId` [System.Int64](https://docs.microsoft.com/en-us/dotnet/api/System.Int64 'System.Int64')

要刪除的大分類ID

#### Returns
[System.Threading.Tasks.Task&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')[Microsoft.AspNetCore.Mvc.IActionResult](https://docs.microsoft.com/en-us/dotnet/api/Microsoft.AspNetCore.Mvc.IActionResult 'Microsoft.AspNetCore.Mvc.IActionResult')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')  
成功時返回 Ok，失敗時返回 NotFound 或 BadRequest

<a name='AlifeApi.WebApi.Controllers.LargeCategoriesController.GetLargeCategories(AlifeApi.BusinessRules.CategoryModels.LargeCategoryQueryInput)'></a>

## LargeCategoriesController.GetLargeCategories(LargeCategoryQueryInput) Method

取得大分類列表 (分頁)

```csharp
public System.Threading.Tasks.Task<Microsoft.AspNetCore.Mvc.IActionResult> GetLargeCategories(AlifeApi.BusinessRules.CategoryModels.LargeCategoryQueryInput input);
```
#### Parameters

<a name='AlifeApi.WebApi.Controllers.LargeCategoriesController.GetLargeCategories(AlifeApi.BusinessRules.CategoryModels.LargeCategoryQueryInput).input'></a>

`input` [AlifeApi.BusinessRules.CategoryModels.LargeCategoryQueryInput](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.BusinessRules.CategoryModels.LargeCategoryQueryInput 'AlifeApi.BusinessRules.CategoryModels.LargeCategoryQueryInput')

查詢條件

#### Returns
[System.Threading.Tasks.Task&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')[Microsoft.AspNetCore.Mvc.IActionResult](https://docs.microsoft.com/en-us/dotnet/api/Microsoft.AspNetCore.Mvc.IActionResult 'Microsoft.AspNetCore.Mvc.IActionResult')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')  
分頁的大分類列表

<a name='AlifeApi.WebApi.Controllers.LargeCategoriesController.GetLargeCategory(long)'></a>

## LargeCategoriesController.GetLargeCategory(long) Method

根據大分類ID取得單一大分類資訊

```csharp
public System.Threading.Tasks.Task<Microsoft.AspNetCore.Mvc.IActionResult> GetLargeCategory(long largeCategoryId);
```
#### Parameters

<a name='AlifeApi.WebApi.Controllers.LargeCategoriesController.GetLargeCategory(long).largeCategoryId'></a>

`largeCategoryId` [System.Int64](https://docs.microsoft.com/en-us/dotnet/api/System.Int64 'System.Int64')

大分類ID

#### Returns
[System.Threading.Tasks.Task&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')[Microsoft.AspNetCore.Mvc.IActionResult](https://docs.microsoft.com/en-us/dotnet/api/Microsoft.AspNetCore.Mvc.IActionResult 'Microsoft.AspNetCore.Mvc.IActionResult')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')  
大分類詳細資訊

<a name='AlifeApi.WebApi.Controllers.LargeCategoriesController.GetLargeCategoryDropdown()'></a>

## LargeCategoriesController.GetLargeCategoryDropdown() Method

取得大分類下拉選單列表

```csharp
public System.Threading.Tasks.Task<Microsoft.AspNetCore.Mvc.IActionResult> GetLargeCategoryDropdown();
```

#### Returns
[System.Threading.Tasks.Task&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')[Microsoft.AspNetCore.Mvc.IActionResult](https://docs.microsoft.com/en-us/dotnet/api/Microsoft.AspNetCore.Mvc.IActionResult 'Microsoft.AspNetCore.Mvc.IActionResult')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')  
啟用的大分類下拉選單列表

<a name='AlifeApi.WebApi.Controllers.LargeCategoriesController.UpdateLargeCategory(long,AlifeApi.BusinessRules.CategoryModels.LargeCategoryInput)'></a>

## LargeCategoriesController.UpdateLargeCategory(long, LargeCategoryInput) Method

更新大分類資料

```csharp
public System.Threading.Tasks.Task<Microsoft.AspNetCore.Mvc.IActionResult> UpdateLargeCategory(long largeCategoryId, AlifeApi.BusinessRules.CategoryModels.LargeCategoryInput input);
```
#### Parameters

<a name='AlifeApi.WebApi.Controllers.LargeCategoriesController.UpdateLargeCategory(long,AlifeApi.BusinessRules.CategoryModels.LargeCategoryInput).largeCategoryId'></a>

`largeCategoryId` [System.Int64](https://docs.microsoft.com/en-us/dotnet/api/System.Int64 'System.Int64')

要更新的大分類ID

<a name='AlifeApi.WebApi.Controllers.LargeCategoriesController.UpdateLargeCategory(long,AlifeApi.BusinessRules.CategoryModels.LargeCategoryInput).input'></a>

`input` [AlifeApi.BusinessRules.CategoryModels.LargeCategoryInput](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.BusinessRules.CategoryModels.LargeCategoryInput 'AlifeApi.BusinessRules.CategoryModels.LargeCategoryInput')

更新的大分類資料

#### Returns
[System.Threading.Tasks.Task&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')[Microsoft.AspNetCore.Mvc.IActionResult](https://docs.microsoft.com/en-us/dotnet/api/Microsoft.AspNetCore.Mvc.IActionResult 'Microsoft.AspNetCore.Mvc.IActionResult')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')  
成功時返回 Ok，失敗時返回 NotFound 或 BadRequest