#### [AlifeApi.BusinessRules](index.md 'index')
### [AlifeApi.BusinessRules.CrmOptionModels](AlifeApi.BusinessRules.CrmOptionModels.md 'AlifeApi.BusinessRules.CrmOptionModels')

## CrmOptionListOutput Class

CRM選項列表輸出模型

```csharp
public class CrmOptionListOutput
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; CrmOptionListOutput
### Properties

<a name='AlifeApi.BusinessRules.CrmOptionModels.CrmOptionListOutput.CreateTime'></a>

## CrmOptionListOutput.CreateTime Property

建立時間

```csharp
public System.DateTime CreateTime { get; set; }
```

#### Property Value
[System.DateTime](https://docs.microsoft.com/en-us/dotnet/api/System.DateTime 'System.DateTime')

<a name='AlifeApi.BusinessRules.CrmOptionModels.CrmOptionListOutput.CrmOptionTypeId'></a>

## CrmOptionListOutput.CrmOptionTypeId Property

CRM選項類型ID

```csharp
public long CrmOptionTypeId { get; set; }
```

#### Property Value
[System.Int64](https://docs.microsoft.com/en-us/dotnet/api/System.Int64 'System.Int64')

<a name='AlifeApi.BusinessRules.CrmOptionModels.CrmOptionListOutput.CrmOptionTypeName'></a>

## CrmOptionListOutput.CrmOptionTypeName Property

CRM選項類型名稱

```csharp
public string? CrmOptionTypeName { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.CrmOptionModels.CrmOptionListOutput.IsActive'></a>

## CrmOptionListOutput.IsActive Property

是否啟用

```csharp
public System.Nullable<bool> IsActive { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.Boolean](https://docs.microsoft.com/en-us/dotnet/api/System.Boolean 'System.Boolean')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

<a name='AlifeApi.BusinessRules.CrmOptionModels.CrmOptionListOutput.OptionValue'></a>

## CrmOptionListOutput.OptionValue Property

選項值

```csharp
public string OptionValue { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.CrmOptionModels.CrmOptionListOutput.SiteCode'></a>

## CrmOptionListOutput.SiteCode Property

案場代碼

```csharp
public string SiteCode { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.CrmOptionModels.CrmOptionListOutput.SiteCrmOptionId'></a>

## CrmOptionListOutput.SiteCrmOptionId Property

CRM選項ID

```csharp
public long SiteCrmOptionId { get; set; }
```

#### Property Value
[System.Int64](https://docs.microsoft.com/en-us/dotnet/api/System.Int64 'System.Int64')

<a name='AlifeApi.BusinessRules.CrmOptionModels.CrmOptionListOutput.SiteName'></a>

## CrmOptionListOutput.SiteName Property

案場名稱

```csharp
public string? SiteName { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.CrmOptionModels.CrmOptionListOutput.SortOrder'></a>

## CrmOptionListOutput.SortOrder Property

排序順序

```csharp
public int SortOrder { get; set; }
```

#### Property Value
[System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')