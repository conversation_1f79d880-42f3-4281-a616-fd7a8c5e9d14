﻿using AlifeApi.BusinessRules.Infrastructure;
using AlifeApi.DataAccess.ProjectContent;
using Microsoft.EntityFrameworkCore;

namespace AlifeApi.BusinessRules.RoleGroupModels
{
    /// <summary>
    /// 角色權限商業邏輯（PostgreSQL 版本）
    /// </summary>
    public class RoleGroupServicePg : ServiceBase<alifeContext>
    {
        public RoleGroupServicePg(IServiceProvider serviceProvider, alifeContext dbContext) : base(serviceProvider, dbContext)
        {
        }

        /// <summary>
        /// 新增角色權限
        /// </summary>
        /// <param name="input">角色權限輸入資料</param>
        /// <exception cref="ArgumentNullException">input</exception>
        public async Task CreateRoleGroupAsync(RoleGroupCreateInputPg input)
        {
            ArgumentNullException.ThrowIfNull(input, nameof(input));

            SysRoleGroup role = new()
            {
                System = "Alife",
                RoleGroupId = Guid.NewGuid().ToString(),
                Name = input.Name,
                SiteCode = input.SiteCode, // 設置角色關聯的案場
                IsAdmin = input.IsAdmin,
                CreatedUserInfoId = CurrentUser.UserId,
                CreatedTime = DateTime.Now,
                UpdatedUserInfoId = CurrentUser.UserId,
                UpdatedTime = DateTime.Now,
            };

            Db.SysRoleGroups.Add(role);

            if (!string.IsNullOrEmpty(input.Permissions))
            {
                foreach (string funcId in input.FuncIds)
                {
                    Db.SysRoleGroupPermissions.Add(new SysRoleGroupPermission
                    {
                        System = "Alife",
                        RoleGroupId = role.RoleGroupId,
                        FuncId = funcId,
                        IsActive = true, // 預設啟用權限
                        Crud = "CRUD"    // 預設給予完整權限
                    });
                }
            }

            // 新增使用者關聯
            if (input.UserIds.Any())
            {
                foreach (string userId in input.UserIds.Distinct())
                {
                    Db.SysRoleGroupUsers.Add(new SysRoleGroupUser
                    {
                        System = "Alife",
                        RoleGroupId = role.RoleGroupId,
                        UserInfoId = userId
                    });
                }
            }

            try
            {
                await Db.SaveChangesAsync();
            }
            catch (DbUpdateException ex)
            {
                Console.WriteLine("Error: " + ex.Message);
                Console.WriteLine("Inner Exception: " + ex.InnerException?.Message);
                throw; // 重新丟擲以便進一步處理
            }
        }

        /// <summary>
        /// 修改角色權限功能
        /// </summary>
        /// <param name="input">角色權限更新輸入資料</param>
        /// <exception cref="ArgumentNullException">input</exception>
        public async Task UpdateRoleGroupPermissionAsync(RoleGroupPermissionUpdateInputPg input)
        {
            ArgumentNullException.ThrowIfNull(input, nameof(input));

            // 取得角色資訊
            SysRoleGroup role = await Db.SysRoleGroups
                .SingleOrDefaultAsync(x => x.System == "Alife" && 
                                     x.RoleGroupId == input.Id && 
                                     (string.IsNullOrEmpty(input.SiteCode) || x.SiteCode == input.SiteCode));

            if (role == null)
            {
                throw new Exception("找不到指定的角色");
            }

            // 更新角色基本資訊
            role.Name = input.Name;
            role.IsAdmin = input.IsAdmin;
            role.UpdatedUserInfoId = CurrentUser.UserId;
            role.UpdatedTime = DateTime.Now;

            if (!string.IsNullOrEmpty(input.SiteCode))
            {
                role.SiteCode = input.SiteCode;
            }

            // 更新角色權限
            if (input.FuncIds.Any())
            {
                // 取得現有權限
                List<SysRoleGroupPermission> permissions = await Db.SysRoleGroupPermissions
                    .Where(x => x.System == "Alife" && x.RoleGroupId == input.Id)
                    .ToListAsync();

                // 刪除所有現有權限
                Db.SysRoleGroupPermissions.RemoveRange(permissions);

                // 新增新權限
                foreach (string funcId in input.FuncIds.Distinct())
                {
                    Db.SysRoleGroupPermissions.Add(new SysRoleGroupPermission
                    {
                        System = "Alife",
                        RoleGroupId = input.Id,
                        FuncId = funcId,
                        IsActive = true,
                        Crud = "CRUD"
                    });
                }
            }

            // 更新使用者關聯
            if (input.UserIds.Any())
            {
                // 取得現有使用者關聯
                List<SysRoleGroupUser> roleUsers = await Db.SysRoleGroupUsers
                    .Where(x => x.System == "Alife" && x.RoleGroupId == input.Id)
                    .ToListAsync();

                // 刪除所有現有使用者關聯
                Db.SysRoleGroupUsers.RemoveRange(roleUsers);

                // 新增新的使用者關聯
                foreach (string userId in input.UserIds.Distinct())
                {
                    Db.SysRoleGroupUsers.Add(new SysRoleGroupUser
                    {
                        System = "Alife",
                        RoleGroupId = input.Id,
                        UserInfoId = userId
                    });
                }
            }

            await Db.SaveChangesAsync();
        }

        /// <summary>
        /// 修改角色權限使用者
        /// </summary>
        /// <param name="input">角色權限使用者更新輸入資料</param>
        /// <exception cref="ArgumentNullException">input</exception>
        public async Task UpdateRoleGroupUserAsync(RoleGroupUserUpdateInputPg input)
        {
            ArgumentNullException.ThrowIfNull(input, nameof(input));

            // 確認角色存在
            SysRoleGroup role = await Db.SysRoleGroups
                .SingleOrDefaultAsync(x => x.System == "Alife" && 
                                     x.RoleGroupId == input.Id && 
                                     (string.IsNullOrEmpty(input.SiteCode) || x.SiteCode == input.SiteCode));

            if (role == null)
            {
                throw new Exception("找不到指定的角色");
            }

            // 更新角色的最後修改資訊
            role.UpdatedUserInfoId = CurrentUser.UserId;
            role.UpdatedTime = DateTime.Now;

            if (input.UserIds.Any())
            {
                // 取得現有使用者關聯
                List<SysRoleGroupUser> roleUsers = await Db.SysRoleGroupUsers
                    .Where(x => x.System == "Alife" && x.RoleGroupId == input.Id)
                    .ToListAsync();

                // 刪除所有現有使用者關聯
                Db.SysRoleGroupUsers.RemoveRange(roleUsers);

                // 新增新的使用者關聯
                foreach (string userId in input.UserIds.Distinct())
                {
                    Db.SysRoleGroupUsers.Add(new SysRoleGroupUser
                    {
                        System = "Alife",
                        RoleGroupId = input.Id,
                        UserInfoId = userId
                    });
                }
            }

            await Db.SaveChangesAsync();
        }

        /// <summary>
        /// 刪除角色
        /// </summary>
        /// <param name="input">角色刪除輸入資料</param>
        /// <exception cref="ArgumentNullException">input</exception>
        public async Task DeleteRoleGroupAsync(RoleGroupDeleteInputPg input)
        {
            ArgumentNullException.ThrowIfNull(input, nameof(input));

            // 確認角色存在
            SysRoleGroup role = await Db.SysRoleGroups
                .SingleOrDefaultAsync(x => x.System == "Alife" && 
                                     x.RoleGroupId == input.Id && 
                                     (string.IsNullOrEmpty(input.SiteCode) || x.SiteCode == input.SiteCode));

            if (role == null)
            {
                throw new Exception("找不到指定的角色");
            }

            // 刪除與角色相關的所有權限設定
            var permissions = await Db.SysRoleGroupPermissions
                .Where(x => x.System == "Alife" && x.RoleGroupId == input.Id)
                .ToListAsync();
            Db.SysRoleGroupPermissions.RemoveRange(permissions);

            // 刪除與角色相關的所有使用者關聯
            var users = await Db.SysRoleGroupUsers
                .Where(x => x.System == "Alife" && x.RoleGroupId == input.Id)
                .ToListAsync();
            Db.SysRoleGroupUsers.RemoveRange(users);

            // 刪除角色
            Db.SysRoleGroups.Remove(role);

            await Db.SaveChangesAsync();
        }

        /// <summary>
        /// 取得選單樹
        /// </summary>
        /// <returns>選單樹</returns>
        public async Task<IEnumerable<MenuTreeOutput>> GetMenuTreesAsync(IEnumerable<string> userFuncIds = null, string rootMenu = "")
        {
            List<SysMenuFunc> allFuncs = await Db.SysMenuFuncs.ToListAsync();

            return GetMenuTreesInternal(allFuncs, userFuncIds, rootMenu);
        }

        private static IEnumerable<MenuTreeOutput> GetMenuTreesInternal(
            IEnumerable<SysMenuFunc> allFuncs, IEnumerable<string> userFuncIds = null, string rootMenu = "")
        {
            Dictionary<string, List<SysMenuFunc>> funcMap = allFuncs
                .Where(m => m.ParentFuncId != null)  // 过滤掉 ParentFuncId 为 null 的项目
                .GroupBy(m => m.ParentFuncId)
                .ToDictionary(g => g.Key, g => g.ToList());

            return GetMenuTreesInternal(funcMap, userFuncIds, rootMenu);
        }

        private static IEnumerable<MenuTreeOutput> GetMenuTreesInternal(
            Dictionary<string, List<SysMenuFunc>> funcMap, IEnumerable<string> userFuncIds, string currentFuncId)
        {
            if (!funcMap.TryGetValue(currentFuncId, out List<SysMenuFunc> currentLevelMenus))
            {
                return Enumerable.Empty<MenuTreeOutput>();
            }

            return currentLevelMenus.OrderBy(x => x.FuncOrder)
                .Select(x =>
                {
                    IEnumerable<MenuTreeOutput> childrens =
                        GetMenuTreesInternal(funcMap, userFuncIds, x.FuncId);
                    
                    // 還原 Selected 的原始邏輯:
                    // If node has children, selected = all children are selected.
                    // If node is a leaf, selected = node is explicitly in userFuncIds.
                    bool isSelected = childrens.Any() 
                        ? childrens.All(y => y.Selected) // 還原此處邏輯
                        : (userFuncIds is not null && userFuncIds.Any(y => y == x.FuncId));

                    return new MenuTreeOutput
                    {
                        Id = x.FuncId,
                        Name = x.FuncName,
                        Childrens = childrens,
                        Selected = isSelected
                    };
                });
        }

        /// <summary>
        /// 取得登入用的選單樹 (父節點強制 Selected = true)
        /// </summary>
        /// <param name="userFuncIds">使用者擁有的功能ID列表</param>
        /// <param name="rootMenu">根選單ID</param>
        /// <returns>登入用的選單樹</returns>
        public async Task<IEnumerable<MenuTreeOutput>> GetLoginMenuTreesAsync(IEnumerable<string> userFuncIds = null, string rootMenu = "")
        {
            List<SysMenuFunc> allFuncs = await Db.SysMenuFuncs.ToListAsync();
            return GetLoginMenuTreesInternal(allFuncs, userFuncIds, rootMenu);
        }

        private static IEnumerable<MenuTreeOutput> GetLoginMenuTreesInternal(
            IEnumerable<SysMenuFunc> allFuncs, IEnumerable<string> userFuncIds = null, string rootMenu = "")
        {
            Dictionary<string, List<SysMenuFunc>> funcMap = allFuncs
                .Where(m => m.ParentFuncId != null)
                .GroupBy(m => m.ParentFuncId)
                .ToDictionary(g => g.Key, g => g.ToList());

            return GetLoginMenuTreesInternal(funcMap, userFuncIds, rootMenu); // 呼叫新的內部遞迴方法
        }

        private static IEnumerable<MenuTreeOutput> GetLoginMenuTreesInternal(
            Dictionary<string, List<SysMenuFunc>> funcMap, IEnumerable<string> userFuncIds, string currentFuncId)
        {
            if (!funcMap.TryGetValue(currentFuncId, out List<SysMenuFunc> currentLevelMenus))
            {
                return Enumerable.Empty<MenuTreeOutput>();
            }

            return currentLevelMenus.OrderBy(x => x.FuncOrder)
                .Select(x =>
                {
                    IEnumerable<MenuTreeOutput> childrens =
                        GetLoginMenuTreesInternal(funcMap, userFuncIds, x.FuncId); // 遞迴呼叫自身
                    
                    // 登入用 Selected 的邏輯：
                    // 如果節點有子節點，Selected 為 true。
                    // 如果節點是葉節點，Selected 取決於 userFuncIds。
                    bool isSelected = childrens.Any() 
                        ? true 
                        : (userFuncIds is not null && userFuncIds.Any(y => y == x.FuncId));

                    return new MenuTreeOutput
                    {
                        Id = x.FuncId,
                        Name = x.FuncName,
                        Childrens = childrens,
                        Selected = isSelected
                    };
                });
        }

        /// <summary>
        /// 取得角色權限清單
        /// </summary>
        /// <param name="input">查詢條件</param>
        /// <returns>角色權限清單</returns>
        /// <exception cref="ArgumentNullException">input</exception>
        public async Task<PagedListOutput<RoleGroupOutputPg>> GetRoleGroupListAsync(RoleGroupListGetInputPg input)
        {
            ArgumentNullException.ThrowIfNull(input, nameof(input));

            // 取得所有功能選單資訊
            List<SysMenuFunc> allFuncs = await Db.SysMenuFuncs.ToListAsync();

            // 查詢角色群組資料
            var query = Db.SysRoleGroups
                .Where(r => r.System == "Alife");

            // 如果指定了特定案場，則篩選該案場的角色
            if (!string.IsNullOrEmpty(input.SiteCode))
            {
                query = query.Where(r => r.SiteCode == input.SiteCode);
            }

            // 如果需要包含全局角色
            if (input.IncludeGlobalRoles)
            {
                query = query.Where(r => r.SiteCode == input.SiteCode || r.SiteCode == null);
            }

            // 分頁查詢角色群組資料
            var roles = await query
                .OrderBy(r => r.Name)
                .Select(r => new RoleGroupOutputPg
                {
                    RoleGroupId = r.RoleGroupId,
                    Name = r.Name,
                    SiteCode = r.SiteCode,
                    SiteName = Db.Sites
                        .Where(s => s.SiteCode == r.SiteCode)
                        .Select(s => s.SiteName)
                        .FirstOrDefault(),
                    IsAdmin = r.IsAdmin,
                    CreatedUserId = r.CreatedUserInfoId,
                    CreatedUserName = Db.UserInfos
                        .Where(u => u.UserInfoId == r.CreatedUserInfoId)
                        .Select(u => u.Name)
                        .FirstOrDefault(),
                    CreatedTime = r.CreatedTime,
                    UpdatedUserId = r.UpdatedUserInfoId,
                    UpdatedUserName = Db.UserInfos
                        .Where(u => u.UserInfoId == r.UpdatedUserInfoId)
                        .Select(u => u.Name)
                        .FirstOrDefault(),
                    UpdatedTime = r.UpdatedTime,
                    FuncIds = Db.SysRoleGroupPermissions
                        .Where(p => p.System == r.System && p.RoleGroupId == r.RoleGroupId && p.IsActive)
                        .Select(p => p.FuncId)
                        .ToList(),
                    UserIds = Db.SysRoleGroupUsers
                        .Where(u => u.System == r.System && u.RoleGroupId == r.RoleGroupId)
                        .Select(u => u.UserInfoId)
                        .ToList()
                })
                .ToPagedListOutputAsync(input);

            // 設置選單樹
            foreach (var role in roles.Details)
            {
                role.MenuTrees = GetMenuTreesInternal(allFuncs, role.FuncIds, "");
            }

            return roles;
        }

        /// <summary>
        /// 根據ID取得角色權限詳細資訊
        /// </summary>
        /// <param name="id">角色ID</param>
        /// <param name="siteCode">案場代碼</param>
        /// <returns>角色權限詳細資訊</returns>
        public async Task<RoleGroupOutputPg> GetRoleGroupByIdAsync(string id, string siteCode = null)
        {
            var query = Db.SysRoleGroups
                .Where(r => r.System == "Alife" && r.RoleGroupId == id);

            if (!string.IsNullOrEmpty(siteCode))
            {
                query = query.Where(r => r.SiteCode == siteCode);
            }

            var role = await query.FirstOrDefaultAsync();

            if (role == null)
            {
                return null;
            }

            // 獲取功能權限
            var funcIds = await Db.SysRoleGroupPermissions
                .Where(p => p.System == role.System && p.RoleGroupId == role.RoleGroupId && p.IsActive)
                .Select(p => p.FuncId)
                .ToListAsync();

            // 獲取使用者關聯
            var userIds = await Db.SysRoleGroupUsers
                .Where(u => u.System == role.System && u.RoleGroupId == role.RoleGroupId)
                .Select(u => u.UserInfoId)
                .ToListAsync();

            // 獲取所有功能選單
            var allFuncs = await Db.SysMenuFuncs.ToListAsync();

            // 獲取使用者名稱
            var createdUser = await Db.UserInfos
                .Where(u => u.UserInfoId == role.CreatedUserInfoId)
                .Select(u => u.Name)
                .FirstOrDefaultAsync();

            var updatedUser = await Db.UserInfos
                .Where(u => u.UserInfoId == role.UpdatedUserInfoId)
                .Select(u => u.Name)
                .FirstOrDefaultAsync();

            // 獲取案場名稱
            string siteName = null;
            if (!string.IsNullOrEmpty(role.SiteCode))
            {
                siteName = await Db.Sites
                    .Where(s => s.SiteCode == role.SiteCode)
                    .Select(s => s.SiteName)
                    .FirstOrDefaultAsync();
            }

            return new RoleGroupOutputPg
            {
                RoleGroupId = role.RoleGroupId,
                Name = role.Name,
                SiteCode = role.SiteCode,
                SiteName = siteName,
                IsAdmin = role.IsAdmin,
                CreatedUserId = role.CreatedUserInfoId,
                CreatedUserName = createdUser,
                CreatedTime = role.CreatedTime,
                UpdatedUserId = role.UpdatedUserInfoId,
                UpdatedUserName = updatedUser,
                UpdatedTime = role.UpdatedTime,
                FuncIds = funcIds,
                UserIds = userIds,
                MenuTrees = GetMenuTreesInternal(allFuncs, funcIds, "")
            };
        }

        /// <summary>
        /// 取得角色群組下拉選單
        /// </summary>
        /// <param name="input">查詢條件</param>
        /// <returns>角色群組下拉選單列表</returns>
        public async Task<List<RoleGroupDropdownOutput>> GetRoleGroupDropdownListAsync(RoleGroupDropdownInputPg input)
        {
            var query = Db.SysRoleGroups
                .Where(x => x.System == "Alife");

            // 根據 SiteCode 篩選
            if (!string.IsNullOrEmpty(input.SiteCode))
            {
                // 如果提供了 SiteCode，則篩選指定案場的角色
                query = query.Where(x => x.SiteCode == input.SiteCode);
            }
            else
            {
                // 如果 SiteCode 是 null 或空字串，則篩選 SiteCode 是 null 或空字串的角色
                query = query.Where(x => string.IsNullOrEmpty(x.SiteCode));
            }

            return await query
                .OrderBy(x => x.Name)
                .Select(x => new RoleGroupDropdownOutput
                {
                    Name = x.Name,
                    Value = x.RoleGroupId
                })
                .ToListAsync();
        }
    }
} 
