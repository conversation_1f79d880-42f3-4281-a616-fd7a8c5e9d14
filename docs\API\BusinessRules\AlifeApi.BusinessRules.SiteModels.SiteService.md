#### [AlifeApi.BusinessRules](index.md 'index')
### [AlifeApi.BusinessRules.SiteModels](AlifeApi.BusinessRules.SiteModels.md 'AlifeApi.BusinessRules.SiteModels')

## SiteService Class

案場服務

```csharp
public class SiteService : AlifeApi.BusinessRules.Infrastructure.ServiceBase<AlifeApi.DataAccess.ProjectContent.alifeContext>
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; [AlifeApi.Common.InfrastructureBase](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.Common.InfrastructureBase 'AlifeApi.Common.InfrastructureBase') &#129106; [AlifeApi.BusinessRules.Infrastructure.ServiceBase&lt;](AlifeApi.BusinessRules.Infrastructure.ServiceBase_TDbContext_.md 'AlifeApi.BusinessRules.Infrastructure.ServiceBase<TDbContext>')[AlifeApi.DataAccess.ProjectContent.alifeContext](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.DataAccess.ProjectContent.alifeContext 'AlifeApi.DataAccess.ProjectContent.alifeContext')[&gt;](AlifeApi.BusinessRules.Infrastructure.ServiceBase_TDbContext_.md 'AlifeApi.BusinessRules.Infrastructure.ServiceBase<TDbContext>') &#129106; SiteService
### Constructors

<a name='AlifeApi.BusinessRules.SiteModels.SiteService.SiteService(System.IServiceProvider,AlifeApi.DataAccess.ProjectContent.alifeContext)'></a>

## SiteService(IServiceProvider, alifeContext) Constructor

建構函數

```csharp
public SiteService(System.IServiceProvider serviceProvider, AlifeApi.DataAccess.ProjectContent.alifeContext dbContext);
```
#### Parameters

<a name='AlifeApi.BusinessRules.SiteModels.SiteService.SiteService(System.IServiceProvider,AlifeApi.DataAccess.ProjectContent.alifeContext).serviceProvider'></a>

`serviceProvider` [System.IServiceProvider](https://docs.microsoft.com/en-us/dotnet/api/System.IServiceProvider 'System.IServiceProvider')

<a name='AlifeApi.BusinessRules.SiteModels.SiteService.SiteService(System.IServiceProvider,AlifeApi.DataAccess.ProjectContent.alifeContext).dbContext'></a>

`dbContext` [AlifeApi.DataAccess.ProjectContent.alifeContext](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.DataAccess.ProjectContent.alifeContext 'AlifeApi.DataAccess.ProjectContent.alifeContext')
### Methods

<a name='AlifeApi.BusinessRules.SiteModels.SiteService.CreateSiteAsync(AlifeApi.BusinessRules.SiteModels.SiteCreateInput)'></a>

## SiteService.CreateSiteAsync(SiteCreateInput) Method

創建案場

```csharp
public System.Threading.Tasks.Task CreateSiteAsync(AlifeApi.BusinessRules.SiteModels.SiteCreateInput input);
```
#### Parameters

<a name='AlifeApi.BusinessRules.SiteModels.SiteService.CreateSiteAsync(AlifeApi.BusinessRules.SiteModels.SiteCreateInput).input'></a>

`input` [SiteCreateInput](AlifeApi.BusinessRules.SiteModels.SiteCreateInput.md 'AlifeApi.BusinessRules.SiteModels.SiteCreateInput')

案場創建輸入

#### Returns
[System.Threading.Tasks.Task](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task 'System.Threading.Tasks.Task')

<a name='AlifeApi.BusinessRules.SiteModels.SiteService.DeleteSiteAsync(string)'></a>

## SiteService.DeleteSiteAsync(string) Method

刪除案場

```csharp
public System.Threading.Tasks.Task DeleteSiteAsync(string siteCode);
```
#### Parameters

<a name='AlifeApi.BusinessRules.SiteModels.SiteService.DeleteSiteAsync(string).siteCode'></a>

`siteCode` [System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

案場代碼

#### Returns
[System.Threading.Tasks.Task](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task 'System.Threading.Tasks.Task')

<a name='AlifeApi.BusinessRules.SiteModels.SiteService.GetSiteByCodeAsync(string)'></a>

## SiteService.GetSiteByCodeAsync(string) Method

根據代碼取得案場

```csharp
public System.Threading.Tasks.Task<AlifeApi.BusinessRules.SiteModels.SiteOutput> GetSiteByCodeAsync(string siteCode);
```
#### Parameters

<a name='AlifeApi.BusinessRules.SiteModels.SiteService.GetSiteByCodeAsync(string).siteCode'></a>

`siteCode` [System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

案場代碼

#### Returns
[System.Threading.Tasks.Task&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')[SiteOutput](AlifeApi.BusinessRules.SiteModels.SiteOutput.md 'AlifeApi.BusinessRules.SiteModels.SiteOutput')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')  
案場資訊

<a name='AlifeApi.BusinessRules.SiteModels.SiteService.GetSiteDropdownListAsync()'></a>

## SiteService.GetSiteDropdownListAsync() Method

取得案場下拉選單

```csharp
public System.Threading.Tasks.Task<System.Collections.Generic.List<AlifeApi.BusinessRules.SiteModels.SiteDropdownOutput>> GetSiteDropdownListAsync();
```

#### Returns
[System.Threading.Tasks.Task&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')[System.Collections.Generic.List&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Collections.Generic.List-1 'System.Collections.Generic.List`1')[SiteDropdownOutput](AlifeApi.BusinessRules.SiteModels.SiteDropdownOutput.md 'AlifeApi.BusinessRules.SiteModels.SiteDropdownOutput')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Collections.Generic.List-1 'System.Collections.Generic.List`1')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')  
案場下拉選單列表

<a name='AlifeApi.BusinessRules.SiteModels.SiteService.GetSiteListAsync(AlifeApi.BusinessRules.SiteModels.SiteListGetInput)'></a>

## SiteService.GetSiteListAsync(SiteListGetInput) Method

取得案場列表

```csharp
public System.Threading.Tasks.Task<AlifeApi.BusinessRules.Infrastructure.PagedListOutput<AlifeApi.BusinessRules.SiteModels.SiteOutput>> GetSiteListAsync(AlifeApi.BusinessRules.SiteModels.SiteListGetInput input);
```
#### Parameters

<a name='AlifeApi.BusinessRules.SiteModels.SiteService.GetSiteListAsync(AlifeApi.BusinessRules.SiteModels.SiteListGetInput).input'></a>

`input` [SiteListGetInput](AlifeApi.BusinessRules.SiteModels.SiteListGetInput.md 'AlifeApi.BusinessRules.SiteModels.SiteListGetInput')

查詢條件

#### Returns
[System.Threading.Tasks.Task&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')[AlifeApi.BusinessRules.Infrastructure.PagedListOutput&lt;](AlifeApi.BusinessRules.Infrastructure.PagedListOutput_TDetail_.md 'AlifeApi.BusinessRules.Infrastructure.PagedListOutput<TDetail>')[SiteOutput](AlifeApi.BusinessRules.SiteModels.SiteOutput.md 'AlifeApi.BusinessRules.SiteModels.SiteOutput')[&gt;](AlifeApi.BusinessRules.Infrastructure.PagedListOutput_TDetail_.md 'AlifeApi.BusinessRules.Infrastructure.PagedListOutput<TDetail>')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')  
案場列表

<a name='AlifeApi.BusinessRules.SiteModels.SiteService.UpdateSiteAsync(AlifeApi.BusinessRules.SiteModels.SiteUpdateInput)'></a>

## SiteService.UpdateSiteAsync(SiteUpdateInput) Method

更新案場

```csharp
public System.Threading.Tasks.Task UpdateSiteAsync(AlifeApi.BusinessRules.SiteModels.SiteUpdateInput input);
```
#### Parameters

<a name='AlifeApi.BusinessRules.SiteModels.SiteService.UpdateSiteAsync(AlifeApi.BusinessRules.SiteModels.SiteUpdateInput).input'></a>

`input` [SiteUpdateInput](AlifeApi.BusinessRules.SiteModels.SiteUpdateInput.md 'AlifeApi.BusinessRules.SiteModels.SiteUpdateInput')

案場更新輸入

#### Returns
[System.Threading.Tasks.Task](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task 'System.Threading.Tasks.Task')