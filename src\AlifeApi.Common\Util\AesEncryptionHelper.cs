﻿using System.Security.Cryptography;
using System.Text;
using System.Web;
using Microsoft.Extensions.Options;

namespace AlifeApi.Common.Util
{
    public class AesEncryptionHelper : IEncryptionHelper
    {
        private readonly EncryptionOptions _encryptionOptions;
        private byte[] _encodedKey;
        private byte[] _encodedVector;

        public AesEncryptionHelper(IOptionsSnapshot<EncryptionOptions> encryptionOptionsAccessor)
        {
            _encryptionOptions = encryptionOptionsAccessor?.Value ?? throw new ArgumentNullException(nameof(encryptionOptionsAccessor));
        }

        public string Key => _encryptionOptions.Key;

        public byte[] EncodedKey
        {
            get
            {
                _encodedKey ??= Convert.FromBase64String(Key);
                return _encodedKey;
            }
        }

        public string Vector => _encryptionOptions.Vector;

        public byte[] EncodedVector
        {
            get
            {
                _encodedVector ??= Convert.FromBase64String(Vector);
                return _encodedVector;
            }
        }

        public string Encrypt(string unencrypted)
        {
            ArgumentNullException.ThrowIfNull(unencrypted, nameof(unencrypted));

            return Convert.ToBase64String(Encrypt(Encoding.Unicode.GetBytes(unencrypted)));
        }

        public byte[] Encrypt(byte[] buffer)
        {
            ArgumentNullException.ThrowIfNull(buffer, nameof(buffer));

            using Aes aes = Aes.Create();
            using ICryptoTransform encryptor = aes.CreateEncryptor(EncodedKey, EncodedVector);

            return encryptor.TransformFinalBlock(buffer, 0, buffer.Length);
        }

        public string Decrypt(string encrypted)
        {
            ArgumentNullException.ThrowIfNull(encrypted, nameof(encrypted));

            return Encoding.Unicode.GetString(Decrypt(Convert.FromBase64String(encrypted.Replace(" ", "+"))));
        }

        public byte[] Decrypt(byte[] buffer)
        {
            ArgumentNullException.ThrowIfNull(buffer, nameof(buffer));

            using Aes aes = Aes.Create();
            using ICryptoTransform decryptor = aes.CreateDecryptor(EncodedKey, EncodedVector);

            return decryptor.TransformFinalBlock(buffer, 0, buffer.Length);
        }

        public string EncryptToUrl(string unencrypted)
        {
            return HttpUtility.UrlEncode(Encrypt(unencrypted));
        }

        public string DecryptFromUrl(string encrypted)
        {
            return Decrypt(HttpUtility.UrlDecode(encrypted));
        }
    }
}
