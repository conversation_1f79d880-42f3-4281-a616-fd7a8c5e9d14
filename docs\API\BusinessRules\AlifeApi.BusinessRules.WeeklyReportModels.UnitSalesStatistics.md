#### [AlifeApi.BusinessRules](index.md 'index')
### [AlifeApi.BusinessRules.WeeklyReportModels](AlifeApi.BusinessRules.WeeklyReportModels.md 'AlifeApi.BusinessRules.WeeklyReportModels')

## UnitSalesStatistics Class

房屋銷售統計

```csharp
public class UnitSalesStatistics
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; UnitSalesStatistics
### Properties

<a name='AlifeApi.BusinessRules.WeeklyReportModels.UnitSalesStatistics.AvailableAmount'></a>

## UnitSalesStatistics.AvailableAmount Property

可售金額

```csharp
public decimal AvailableAmount { get; set; }
```

#### Property Value
[System.Decimal](https://docs.microsoft.com/en-us/dotnet/api/System.Decimal 'System.Decimal')

<a name='AlifeApi.BusinessRules.WeeklyReportModels.UnitSalesStatistics.AvailableUnits'></a>

## UnitSalesStatistics.AvailableUnits Property

可售戶數

```csharp
public int AvailableUnits { get; set; }
```

#### Property Value
[System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')

<a name='AlifeApi.BusinessRules.WeeklyReportModels.UnitSalesStatistics.SalesRate'></a>

## UnitSalesStatistics.SalesRate Property

銷售率

```csharp
public decimal SalesRate { get; set; }
```

#### Property Value
[System.Decimal](https://docs.microsoft.com/en-us/dotnet/api/System.Decimal 'System.Decimal')

<a name='AlifeApi.BusinessRules.WeeklyReportModels.UnitSalesStatistics.SignedTotal'></a>

## UnitSalesStatistics.SignedTotal Property

已簽約總數

```csharp
public int SignedTotal { get; set; }
```

#### Property Value
[System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')

<a name='AlifeApi.BusinessRules.WeeklyReportModels.UnitSalesStatistics.SignedTotalTransactionAmount'></a>

## UnitSalesStatistics.SignedTotalTransactionAmount Property

已簽約總成交金額

```csharp
public decimal SignedTotalTransactionAmount { get; set; }
```

#### Property Value
[System.Decimal](https://docs.microsoft.com/en-us/dotnet/api/System.Decimal 'System.Decimal')

<a name='AlifeApi.BusinessRules.WeeklyReportModels.UnitSalesStatistics.SoldMinimumAmount'></a>

## UnitSalesStatistics.SoldMinimumAmount Property

已售底價金額

```csharp
public decimal SoldMinimumAmount { get; set; }
```

#### Property Value
[System.Decimal](https://docs.microsoft.com/en-us/dotnet/api/System.Decimal 'System.Decimal')

<a name='AlifeApi.BusinessRules.WeeklyReportModels.UnitSalesStatistics.SoldUnits'></a>

## UnitSalesStatistics.SoldUnits Property

已售戶數

```csharp
public int SoldUnits { get; set; }
```

#### Property Value
[System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')

<a name='AlifeApi.BusinessRules.WeeklyReportModels.UnitSalesStatistics.UnsoldAmount'></a>

## UnitSalesStatistics.UnsoldAmount Property

未售金額

```csharp
public decimal UnsoldAmount { get; set; }
```

#### Property Value
[System.Decimal](https://docs.microsoft.com/en-us/dotnet/api/System.Decimal 'System.Decimal')

<a name='AlifeApi.BusinessRules.WeeklyReportModels.UnitSalesStatistics.UnsoldRate'></a>

## UnitSalesStatistics.UnsoldRate Property

未售率

```csharp
public decimal UnsoldRate { get; set; }
```

#### Property Value
[System.Decimal](https://docs.microsoft.com/en-us/dotnet/api/System.Decimal 'System.Decimal')

<a name='AlifeApi.BusinessRules.WeeklyReportModels.UnitSalesStatistics.UnsoldUnits'></a>

## UnitSalesStatistics.UnsoldUnits Property

未售戶數

```csharp
public int UnsoldUnits { get; set; }
```

#### Property Value
[System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')