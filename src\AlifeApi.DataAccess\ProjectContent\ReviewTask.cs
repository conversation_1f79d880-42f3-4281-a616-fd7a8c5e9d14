﻿using System;
using System.Collections.Generic;

namespace AlifeApi.DataAccess.ProjectContent
{
    /// <summary>
    /// 審核任務表，用於儲存審核任務的基本資訊，與特定案場關聯。
    /// </summary>
    public partial class ReviewTask
    {
        public ReviewTask()
        {
            ReviewSteps = new HashSet<ReviewStep>();
        }

        /// <summary>
        /// 任務流水號，主鍵，自動遞增，用於唯一識別審核任務。
        /// </summary>
        public int TaskId { get; set; }
        /// <summary>
        /// 案場號碼，與 Sites 表的 SiteCode 關聯，指定任務的案場。
        /// </summary>
        public string SiteCode { get; set; }
        /// <summary>
        /// 任務標題，描述任務的主要內容。
        /// </summary>
        public string Title { get; set; }
        /// <summary>
        /// 任務描述，提供任務的詳細資訊。
        /// </summary>
        public string Description { get; set; }
        /// <summary>
        /// 創建時間，記錄任務創建的時間，預設為當前時間。
        /// </summary>
        public DateTime? CreatedTime { get; set; }
        /// <summary>
        /// 截止日期，任務必須完成的時間。
        /// </summary>
        public DateTime? Deadline { get; set; }
        /// <summary>
        /// 任務狀態，enable（啟用）、disable（停用）、deleted(刪除)
        /// </summary>
        public string Status { get; set; }
        /// <summary>
        /// 創建者編號，對應 UserInfo 表的 UserInfoId。
        /// </summary>
        public string CreatedUserInfoId { get; set; }

        public virtual ICollection<ReviewStep> ReviewSteps { get; set; }
    }
}
