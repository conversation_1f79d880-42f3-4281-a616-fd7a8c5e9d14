#### [AlifeApi.DataAccess](index.md 'index')
### [AlifeApi.DataAccess.ProjectContext](AlifeApi.DataAccess.ProjectContext.md 'AlifeApi.DataAccess.ProjectContext')

## UserInfo Class

人員基本資料

```csharp
public class UserInfo
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; UserInfo
### Properties

<a name='AlifeApi.DataAccess.ProjectContext.UserInfo.CreatedTime'></a>

## UserInfo.CreatedTime Property

建立時間

```csharp
public System.DateTime CreatedTime { get; set; }
```

#### Property Value
[System.DateTime](https://docs.microsoft.com/en-us/dotnet/api/System.DateTime 'System.DateTime')

<a name='AlifeApi.DataAccess.ProjectContext.UserInfo.CreatedUserId'></a>

## UserInfo.CreatedUserId Property

建立者

```csharp
public string CreatedUserId { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContext.UserInfo.DeptId'></a>

## UserInfo.DeptId Property

部門

```csharp
public string DeptId { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContext.UserInfo.Email'></a>

## UserInfo.Email Property

電子郵件

```csharp
public string Email { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContext.UserInfo.EnabledTtime'></a>

## UserInfo.EnabledTtime Property

啟用時間

```csharp
public System.Nullable<System.DateTime> EnabledTtime { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.DateTime](https://docs.microsoft.com/en-us/dotnet/api/System.DateTime 'System.DateTime')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

<a name='AlifeApi.DataAccess.ProjectContext.UserInfo.GradeCode'></a>

## UserInfo.GradeCode Property

職位

```csharp
public string GradeCode { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContext.UserInfo.Id'></a>

## UserInfo.Id Property

員工編號

```csharp
public string Id { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContext.UserInfo.IdNo'></a>

## UserInfo.IdNo Property

身分證字號

```csharp
public string IdNo { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContext.UserInfo.IsDisabled'></a>

## UserInfo.IsDisabled Property

是否刪除

```csharp
public bool IsDisabled { get; set; }
```

#### Property Value
[System.Boolean](https://docs.microsoft.com/en-us/dotnet/api/System.Boolean 'System.Boolean')

<a name='AlifeApi.DataAccess.ProjectContext.UserInfo.IsEnabled'></a>

## UserInfo.IsEnabled Property

是否啟用

```csharp
public bool IsEnabled { get; set; }
```

#### Property Value
[System.Boolean](https://docs.microsoft.com/en-us/dotnet/api/System.Boolean 'System.Boolean')

<a name='AlifeApi.DataAccess.ProjectContext.UserInfo.LastLoginIp'></a>

## UserInfo.LastLoginIp Property

最後一次登入IP

```csharp
public string LastLoginIp { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContext.UserInfo.LastLoginTime'></a>

## UserInfo.LastLoginTime Property

最後登入時間

```csharp
public System.Nullable<System.DateTime> LastLoginTime { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.DateTime](https://docs.microsoft.com/en-us/dotnet/api/System.DateTime 'System.DateTime')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

<a name='AlifeApi.DataAccess.ProjectContext.UserInfo.LastLogoutTime'></a>

## UserInfo.LastLogoutTime Property

最後登出時間

```csharp
public System.Nullable<System.DateTime> LastLogoutTime { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.DateTime](https://docs.microsoft.com/en-us/dotnet/api/System.DateTime 'System.DateTime')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

<a name='AlifeApi.DataAccess.ProjectContext.UserInfo.LoginFailedCount'></a>

## UserInfo.LoginFailedCount Property

登入失敗次數加總

```csharp
public short LoginFailedCount { get; set; }
```

#### Property Value
[System.Int16](https://docs.microsoft.com/en-us/dotnet/api/System.Int16 'System.Int16')

<a name='AlifeApi.DataAccess.ProjectContext.UserInfo.Name'></a>

## UserInfo.Name Property

人員姓名

```csharp
public string Name { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContext.UserInfo.Pw'></a>

## UserInfo.Pw Property

密碼

```csharp
public string Pw { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContext.UserInfo.UpdatedTime'></a>

## UserInfo.UpdatedTime Property

更新時間

```csharp
public System.DateTime UpdatedTime { get; set; }
```

#### Property Value
[System.DateTime](https://docs.microsoft.com/en-us/dotnet/api/System.DateTime 'System.DateTime')

<a name='AlifeApi.DataAccess.ProjectContext.UserInfo.UpdatedUserId'></a>

## UserInfo.UpdatedUserId Property

更新者

```csharp
public string UpdatedUserId { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')