﻿using System.Text;
using System.Xml.Linq;
using System.Xml.XPath;
using Microsoft.OpenApi.Models;
using Swashbuckle.AspNetCore.SwaggerGen;

namespace AlifeApi.WebApi.Filters
{
    /// <summary>
    /// Swagger schema filter to modify description of enum types so they
    /// show the XML docs attached to each member of the enum.
    /// </summary>
    /// <remarks>
    /// ref: https://stackoverflow.com/questions/53282170/swaggerui-not-display-enum-summary-description-c-sharp-net-core
    /// </remarks>
    public class EnumSchemaFilter : ISchemaFilter
    {
        private readonly XDocument xmlComments;

        /// <summary>
        /// Initialize schema filter.
        /// </summary>
        /// <param name="xmlComments">Document containing XML docs for enum members.</param>
        public EnumSchemaFilter(XDocument xmlComments)
          => this.xmlComments = xmlComments;

        /// <summary>
        /// Apply this schema filter.
        /// </summary>
        /// <param name="schema">Target schema object.</param>
        /// <param name="context">Schema filter context.</param>
        public void Apply(OpenApiSchema schema, SchemaFilterContext context)
        {
            Type enumType = context.Type;

            if (!enumType.IsEnum) return;

            StringBuilder sb = new(schema.Description);

            sb.AppendLine("<p>Possible values:</p>");
            sb.AppendLine("<ul>");

            foreach (string enumMemberName in Enum.GetNames(enumType))
            {
                string fullEnumMemberName = $"F:{enumType.FullName}.{enumMemberName}";

                string enumMemberDescription = xmlComments.XPathEvaluate(
                  $"normalize-space(//member[@name = '{fullEnumMemberName}']/summary/text())"
                ) as string;

                if (string.IsNullOrEmpty(enumMemberDescription)) continue;

                sb.AppendLine($"<li><b>{Convert.ToInt32(Enum.Parse(enumType, enumMemberName))}</b>: {enumMemberDescription}</li>");
            }

            sb.AppendLine("</ul>");

            schema.Description = sb.ToString();
        }
    }
}
