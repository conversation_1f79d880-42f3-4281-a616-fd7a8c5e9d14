#### [AlifeApi.BusinessRules](index.md 'index')
### [AlifeApi.BusinessRules.WeeklyReportModels](AlifeApi.BusinessRules.WeeklyReportModels.md 'AlifeApi.BusinessRules.WeeklyReportModels')

## CustomerAgeAnalysis Class

客戶年齡分析

```csharp
public class CustomerAgeAnalysis
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; CustomerAgeAnalysis
### Properties

<a name='AlifeApi.BusinessRules.WeeklyReportModels.CustomerAgeAnalysis.AgeRange'></a>

## CustomerAgeAnalysis.AgeRange Property

年齡區間

```csharp
public string AgeRange { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.WeeklyReportModels.CustomerAgeAnalysis.ReturnVisitCount'></a>

## CustomerAgeAnalysis.ReturnVisitCount Property

回訪數

```csharp
public int ReturnVisitCount { get; set; }
```

#### Property Value
[System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')

<a name='AlifeApi.BusinessRules.WeeklyReportModels.CustomerAgeAnalysis.ReturnVisitPercentage'></a>

## CustomerAgeAnalysis.ReturnVisitPercentage Property

回訪佔比

```csharp
public decimal ReturnVisitPercentage { get; set; }
```

#### Property Value
[System.Decimal](https://docs.microsoft.com/en-us/dotnet/api/System.Decimal 'System.Decimal')

<a name='AlifeApi.BusinessRules.WeeklyReportModels.CustomerAgeAnalysis.TransactionCount'></a>

## CustomerAgeAnalysis.TransactionCount Property

成交數

```csharp
public int TransactionCount { get; set; }
```

#### Property Value
[System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')

<a name='AlifeApi.BusinessRules.WeeklyReportModels.CustomerAgeAnalysis.TransactionPercentage'></a>

## CustomerAgeAnalysis.TransactionPercentage Property

成交佔比

```csharp
public decimal TransactionPercentage { get; set; }
```

#### Property Value
[System.Decimal](https://docs.microsoft.com/en-us/dotnet/api/System.Decimal 'System.Decimal')

<a name='AlifeApi.BusinessRules.WeeklyReportModels.CustomerAgeAnalysis.VisitorCount'></a>

## CustomerAgeAnalysis.VisitorCount Property

來客數

```csharp
public int VisitorCount { get; set; }
```

#### Property Value
[System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')

<a name='AlifeApi.BusinessRules.WeeklyReportModels.CustomerAgeAnalysis.VisitorPercentage'></a>

## CustomerAgeAnalysis.VisitorPercentage Property

來客佔比

```csharp
public decimal VisitorPercentage { get; set; }
```

#### Property Value
[System.Decimal](https://docs.microsoft.com/en-us/dotnet/api/System.Decimal 'System.Decimal')