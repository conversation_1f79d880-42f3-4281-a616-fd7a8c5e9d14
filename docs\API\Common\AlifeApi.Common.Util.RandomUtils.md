#### [AlifeApi.Common](index.md 'index')
### [AlifeApi.Common.Util](AlifeApi.Common.Util.md 'AlifeApi.Common.Util')

## RandomUtils Class

產生亂數數值工具

```csharp
public static class RandomUtils
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; RandomUtils
### Methods

<a name='AlifeApi.Common.Util.RandomUtils.Next()'></a>

## RandomUtils.Next() Method

產生一個非負數的亂數

```csharp
public static int Next();
```

#### Returns
[System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')

<a name='AlifeApi.Common.Util.RandomUtils.Next(int)'></a>

## RandomUtils.Next(int) Method

產生一個非負數且最大值 max 以下的亂數

```csharp
public static int Next(int max);
```
#### Parameters

<a name='AlifeApi.Common.Util.RandomUtils.Next(int).max'></a>

`max` [System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')

最大值

#### Returns
[System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')

<a name='AlifeApi.Common.Util.RandomUtils.Next(int,int)'></a>

## RandomUtils.Next(int, int) Method

產生一個非負數且最小值在 min 以上最大值在 max 以下的亂數

```csharp
public static int Next(int min, int max);
```
#### Parameters

<a name='AlifeApi.Common.Util.RandomUtils.Next(int,int).min'></a>

`min` [System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')

最小值

<a name='AlifeApi.Common.Util.RandomUtils.Next(int,int).max'></a>

`max` [System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')

最大值

#### Returns
[System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')

<a name='AlifeApi.Common.Util.RandomUtils.NextDouble()'></a>

## RandomUtils.NextDouble() Method

產生一個非負數的double

```csharp
public static double NextDouble();
```

#### Returns
[System.Double](https://docs.microsoft.com/en-us/dotnet/api/System.Double 'System.Double')