#### [AlifeApi.DataAccess](index.md 'index')
### [AlifeApi.DataAccess.ProjectContext](AlifeApi.DataAccess.ProjectContext.md 'AlifeApi.DataAccess.ProjectContext')

## SysCode Class

系統代碼資料

```csharp
public class SysCode
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; SysCode
### Properties

<a name='AlifeApi.DataAccess.ProjectContext.SysCode.Code'></a>

## SysCode.Code Property

代碼

```csharp
public string Code { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContext.SysCode.CodeDesc'></a>

## SysCode.CodeDesc Property

內容

```csharp
public string CodeDesc { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContext.SysCode.CodeOrder'></a>

## SysCode.CodeOrder Property

排序

```csharp
public short CodeOrder { get; set; }
```

#### Property Value
[System.Int16](https://docs.microsoft.com/en-us/dotnet/api/System.Int16 'System.Int16')

<a name='AlifeApi.DataAccess.ProjectContext.SysCode.CreatedTime'></a>

## SysCode.CreatedTime Property

建立時間

```csharp
public System.DateTime CreatedTime { get; set; }
```

#### Property Value
[System.DateTime](https://docs.microsoft.com/en-us/dotnet/api/System.DateTime 'System.DateTime')

<a name='AlifeApi.DataAccess.ProjectContext.SysCode.CreatedUserId'></a>

## SysCode.CreatedUserId Property

建立者

```csharp
public string CreatedUserId { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContext.SysCode.IsDisabled'></a>

## SysCode.IsDisabled Property

是否作廢

```csharp
public bool IsDisabled { get; set; }
```

#### Property Value
[System.Boolean](https://docs.microsoft.com/en-us/dotnet/api/System.Boolean 'System.Boolean')

<a name='AlifeApi.DataAccess.ProjectContext.SysCode.ParentCode'></a>

## SysCode.ParentCode Property

上層代碼

```csharp
public string ParentCode { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContext.SysCode.Type'></a>

## SysCode.Type Property

類型

```csharp
public string Type { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContext.SysCode.UpdatedTime'></a>

## SysCode.UpdatedTime Property

更新時間

```csharp
public System.DateTime UpdatedTime { get; set; }
```

#### Property Value
[System.DateTime](https://docs.microsoft.com/en-us/dotnet/api/System.DateTime 'System.DateTime')

<a name='AlifeApi.DataAccess.ProjectContext.SysCode.UpdatedUserId'></a>

## SysCode.UpdatedUserId Property

新增者

```csharp
public string UpdatedUserId { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')