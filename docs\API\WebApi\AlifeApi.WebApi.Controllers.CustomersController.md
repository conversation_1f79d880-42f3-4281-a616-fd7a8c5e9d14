#### [<PERSON>feApi.WebApi](index.md 'index')
### [AlifeApi.WebApi.Controllers](AlifeApi.WebApi.Controllers.md 'AlifeApi.WebApi.Controllers')

## CustomersController Class

客戶管理

```csharp
public class CustomersController : AlifeApi.WebApi.Controllers.AuthenticatedController
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; [Microsoft.AspNetCore.Mvc.ControllerBase](https://docs.microsoft.com/en-us/dotnet/api/Microsoft.AspNetCore.Mvc.ControllerBase 'Microsoft.AspNetCore.Mvc.ControllerBase') &#129106; [Microsoft.AspNetCore.Mvc.Controller](https://docs.microsoft.com/en-us/dotnet/api/Microsoft.AspNetCore.Mvc.Controller 'Microsoft.AspNetCore.Mvc.Controller') &#129106; [AlifeApi.WebApi.Controllers.BasicController](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.WebApi.Controllers.BasicController 'AlifeApi.WebApi.Controllers.BasicController') &#129106; [AlifeApi.WebApi.Controllers.AuthenticatedController](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.WebApi.Controllers.AuthenticatedController 'AlifeApi.WebApi.Controllers.AuthenticatedController') &#129106; CustomersController
### Methods

<a name='AlifeApi.WebApi.Controllers.CustomersController.CreateCustomer(AlifeApi.BusinessRules.CustomerModels.CustomerInput)'></a>

## CustomersController.CreateCustomer(CustomerInput) Method

新增客戶

```csharp
public System.Threading.Tasks.Task<Microsoft.AspNetCore.Mvc.IActionResult> CreateCustomer(AlifeApi.BusinessRules.CustomerModels.CustomerInput input);
```
#### Parameters

<a name='AlifeApi.WebApi.Controllers.CustomersController.CreateCustomer(AlifeApi.BusinessRules.CustomerModels.CustomerInput).input'></a>

`input` [AlifeApi.BusinessRules.CustomerModels.CustomerInput](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.BusinessRules.CustomerModels.CustomerInput 'AlifeApi.BusinessRules.CustomerModels.CustomerInput')

新增客戶輸入資料

#### Returns
[System.Threading.Tasks.Task&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')[Microsoft.AspNetCore.Mvc.IActionResult](https://docs.microsoft.com/en-us/dotnet/api/Microsoft.AspNetCore.Mvc.IActionResult 'Microsoft.AspNetCore.Mvc.IActionResult')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')  
新增的客戶ID

<a name='AlifeApi.WebApi.Controllers.CustomersController.GetCustomer(string)'></a>

## CustomersController.GetCustomer(string) Method

根據客戶ID取得單一客戶資訊

```csharp
public System.Threading.Tasks.Task<Microsoft.AspNetCore.Mvc.IActionResult> GetCustomer(string customerId);
```
#### Parameters

<a name='AlifeApi.WebApi.Controllers.CustomersController.GetCustomer(string).customerId'></a>

`customerId` [System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

客戶ID

#### Returns
[System.Threading.Tasks.Task&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')[Microsoft.AspNetCore.Mvc.IActionResult](https://docs.microsoft.com/en-us/dotnet/api/Microsoft.AspNetCore.Mvc.IActionResult 'Microsoft.AspNetCore.Mvc.IActionResult')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')  
客戶詳細資訊

<a name='AlifeApi.WebApi.Controllers.CustomersController.GetCustomers(AlifeApi.BusinessRules.CustomerModels.CustomerQueryInput)'></a>

## CustomersController.GetCustomers(CustomerQueryInput) Method

取得客戶列表 (分頁)

```csharp
public System.Threading.Tasks.Task<Microsoft.AspNetCore.Mvc.IActionResult> GetCustomers(AlifeApi.BusinessRules.CustomerModels.CustomerQueryInput input);
```
#### Parameters

<a name='AlifeApi.WebApi.Controllers.CustomersController.GetCustomers(AlifeApi.BusinessRules.CustomerModels.CustomerQueryInput).input'></a>

`input` [AlifeApi.BusinessRules.CustomerModels.CustomerQueryInput](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.BusinessRules.CustomerModels.CustomerQueryInput 'AlifeApi.BusinessRules.CustomerModels.CustomerQueryInput')

查詢條件

#### Returns
[System.Threading.Tasks.Task&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')[Microsoft.AspNetCore.Mvc.IActionResult](https://docs.microsoft.com/en-us/dotnet/api/Microsoft.AspNetCore.Mvc.IActionResult 'Microsoft.AspNetCore.Mvc.IActionResult')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')  
分頁的客戶列表

<a name='AlifeApi.WebApi.Controllers.CustomersController.UpdateCustomer(string,AlifeApi.BusinessRules.CustomerModels.CustomerInput)'></a>

## CustomersController.UpdateCustomer(string, CustomerInput) Method

更新客戶資料 (包含訪談紀錄的新增/更新)

```csharp
public System.Threading.Tasks.Task<Microsoft.AspNetCore.Mvc.IActionResult> UpdateCustomer(string customerId, AlifeApi.BusinessRules.CustomerModels.CustomerInput input);
```
#### Parameters

<a name='AlifeApi.WebApi.Controllers.CustomersController.UpdateCustomer(string,AlifeApi.BusinessRules.CustomerModels.CustomerInput).customerId'></a>

`customerId` [System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

要更新的客戶ID

<a name='AlifeApi.WebApi.Controllers.CustomersController.UpdateCustomer(string,AlifeApi.BusinessRules.CustomerModels.CustomerInput).input'></a>

`input` [AlifeApi.BusinessRules.CustomerModels.CustomerInput](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.BusinessRules.CustomerModels.CustomerInput 'AlifeApi.BusinessRules.CustomerModels.CustomerInput')

更新的客戶資料 (包含基本資料和訪談紀錄列表)

#### Returns
[System.Threading.Tasks.Task&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')[Microsoft.AspNetCore.Mvc.IActionResult](https://docs.microsoft.com/en-us/dotnet/api/Microsoft.AspNetCore.Mvc.IActionResult 'Microsoft.AspNetCore.Mvc.IActionResult')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')  
成功時返回 NoContent (204)，失敗時返回 NotFound 或 BadRequest