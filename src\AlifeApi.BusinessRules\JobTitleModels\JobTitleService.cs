﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using AlifeApi.BusinessRules.Infrastructure;
using AlifeApi.DataAccess.ProjectContent;
using Microsoft.EntityFrameworkCore;

namespace AlifeApi.BusinessRules.JobTitleModels
{
    /// <summary>
    /// 職位服務
    /// </summary>
    public class JobTitleService : ServiceBase<alifeContext>
    {
        /// <summary>
        /// 建構函數
        /// </summary>
        public JobTitleService(IServiceProvider serviceProvider, alifeContext dbContext) 
            : base(serviceProvider, dbContext)
        {
        }

        /// <summary>
        /// 取得職位下拉選單
        /// </summary>
        /// <param name="input">查詢條件</param>
        /// <returns>職位下拉選單列表</returns>
        public async Task<List<JobTitleDropdownOutput>> GetJobTitleDropdownListAsync(JobTitleDropdownInput input)
        {
            var query = Db.JobTitles.AsQueryable();
            
            if (!string.IsNullOrEmpty(input.DepartmentId))
            {
                query = query.Where(x => x.DepartmentId == input.DepartmentId);
            }

            return await query
                .OrderBy(x => x.Name)
                .Select(x => new JobTitleDropdownOutput
                {
                    Name = x.Name,
                    Value = x.JobTitleId
                })
                .ToListAsync();
        }
    }
} 
