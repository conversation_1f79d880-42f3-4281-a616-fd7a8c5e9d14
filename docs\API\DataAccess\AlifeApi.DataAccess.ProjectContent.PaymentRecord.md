#### [AlifeApi.DataAccess](index.md 'index')
### [AlifeApi.DataAccess.ProjectContent](AlifeApi.DataAccess.ProjectContent.md 'AlifeApi.DataAccess.ProjectContent')

## PaymentRecord Class

記錄針對某一筆 PurchaseOrder 的分期收款明細。

```csharp
public class PaymentRecord
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; PaymentRecord
### Properties

<a name='AlifeApi.DataAccess.ProjectContent.PaymentRecord.Amount'></a>

## PaymentRecord.Amount Property

本次收款的金額 (新台幣)。

```csharp
public decimal Amount { get; set; }
```

#### Property Value
[System.Decimal](https://docs.microsoft.com/en-us/dotnet/api/System.Decimal 'System.Decimal')

<a name='AlifeApi.DataAccess.ProjectContent.PaymentRecord.AttachmentPath'></a>

## PaymentRecord.AttachmentPath Property

相關檔案 (如收據掃描檔、匯款單) 的儲存路徑。

```csharp
public string AttachmentPath { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContent.PaymentRecord.BankAccountNumber'></a>

## PaymentRecord.BankAccountNumber Property

匯款帳號末五碼或支票帳號。

```csharp
public string BankAccountNumber { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContent.PaymentRecord.BankName'></a>

## PaymentRecord.BankName Property

匯款銀行或支票開立銀行。

```csharp
public string BankName { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContent.PaymentRecord.CheckDueDate'></a>

## PaymentRecord.CheckDueDate Property

支票到期日 (如果付款方式是支票)。

```csharp
public System.Nullable<System.DateOnly> CheckDueDate { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.DateOnly](https://docs.microsoft.com/en-us/dotnet/api/System.DateOnly 'System.DateOnly')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

<a name='AlifeApi.DataAccess.ProjectContent.PaymentRecord.CheckNumber'></a>

## PaymentRecord.CheckNumber Property

支票號碼 (如果付款方式是支票)。

```csharp
public string CheckNumber { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContent.PaymentRecord.CreatedTime'></a>

## PaymentRecord.CreatedTime Property

記錄創建時間 (TIMESTAMP WITHOUT TIME ZONE)。

```csharp
public System.DateTime CreatedTime { get; set; }
```

#### Property Value
[System.DateTime](https://docs.microsoft.com/en-us/dotnet/api/System.DateTime 'System.DateTime')

<a name='AlifeApi.DataAccess.ProjectContent.PaymentRecord.CreatedUserInfoId'></a>

## PaymentRecord.CreatedUserInfoId Property

建立此收款記錄的人員ID (參考 UserInfo 表)。

```csharp
public string CreatedUserInfoId { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContent.PaymentRecord.HandlingFee'></a>

## PaymentRecord.HandlingFee Property

本次收款可能產生的手續費 (例如刷卡手續費，可選)。

```csharp
public System.Nullable<decimal> HandlingFee { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.Decimal](https://docs.microsoft.com/en-us/dotnet/api/System.Decimal 'System.Decimal')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

<a name='AlifeApi.DataAccess.ProjectContent.PaymentRecord.IsReceived'></a>

## PaymentRecord.IsReceived Property

標記此筆款項是否已確認入帳/兌現 (True/False)。

```csharp
public System.Nullable<bool> IsReceived { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.Boolean](https://docs.microsoft.com/en-us/dotnet/api/System.Boolean 'System.Boolean')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

<a name='AlifeApi.DataAccess.ProjectContent.PaymentRecord.OrderId'></a>

## PaymentRecord.OrderId Property

關聯的訂單 ID (參考 PurchaseOrders 表)。

```csharp
public int OrderId { get; set; }
```

#### Property Value
[System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')

<a name='AlifeApi.DataAccess.ProjectContent.PaymentRecord.PaymentDate'></a>

## PaymentRecord.PaymentDate Property

實際收款的日期 (或預計收款日)。

```csharp
public System.DateOnly PaymentDate { get; set; }
```

#### Property Value
[System.DateOnly](https://docs.microsoft.com/en-us/dotnet/api/System.DateOnly 'System.DateOnly')

<a name='AlifeApi.DataAccess.ProjectContent.PaymentRecord.PaymentMethod'></a>

## PaymentRecord.PaymentMethod Property

本次收款的方式 (例如: 現金, 匯款, 支票 - 建議關lungen SYS_Code)。

```csharp
public string PaymentMethod { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContent.PaymentRecord.PaymentRecordId'></a>

## PaymentRecord.PaymentRecordId Property

收款記錄唯一識別碼 (主鍵, 自動遞增)。

```csharp
public int PaymentRecordId { get; set; }
```

#### Property Value
[System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')

<a name='AlifeApi.DataAccess.ProjectContent.PaymentRecord.PaymentType'></a>

## PaymentRecord.PaymentType Property

本次收款的款項類別 (例如: 定金, 簽約金, 工程款 - 建議關聯 SYS_Code)。

```csharp
public string PaymentType { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContent.PaymentRecord.Remarks'></a>

## PaymentRecord.Remarks Property

收款備註 (例如：特定期數說明)。

```csharp
public string Remarks { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContent.PaymentRecord.TransferredToDeveloper'></a>

## PaymentRecord.TransferredToDeveloper Property

該筆款項是否已轉交給建設公司 (True/False，可選)。

```csharp
public System.Nullable<bool> TransferredToDeveloper { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.Boolean](https://docs.microsoft.com/en-us/dotnet/api/System.Boolean 'System.Boolean')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

<a name='AlifeApi.DataAccess.ProjectContent.PaymentRecord.UpdatedTime'></a>

## PaymentRecord.UpdatedTime Property

記錄最後更新時間 (TIMESTAMP WITHOUT TIME ZONE)。

```csharp
public System.DateTime UpdatedTime { get; set; }
```

#### Property Value
[System.DateTime](https://docs.microsoft.com/en-us/dotnet/api/System.DateTime 'System.DateTime')

<a name='AlifeApi.DataAccess.ProjectContent.PaymentRecord.UpdatedUserInfoId'></a>

## PaymentRecord.UpdatedUserInfoId Property

最後更新此收款記錄的人員ID (參考 UserInfo 表)。

```csharp
public string UpdatedUserInfoId { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')