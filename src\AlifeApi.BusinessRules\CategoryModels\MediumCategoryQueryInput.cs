using AlifeApi.BusinessRules.Infrastructure;
using System.Text.Json.Serialization;

namespace AlifeApi.BusinessRules.CategoryModels
{
    /// <summary>
    /// 中分類查詢輸入 DTO
    /// </summary>
    public class MediumCategoryQueryInput : PagedListInput
    {
        /// <summary>
        /// 所屬大分類ID (篩選條件)
        /// </summary>
        [JsonPropertyName("LargeCategoryId")]
        public long? LargeCategoryId { get; set; }

        /// <summary>
        /// 中分類名稱 (模糊查詢)
        /// </summary>
        [JsonPropertyName("Name")]
        public string Name { get; set; }

        /// <summary>
        /// 是否啟用 (null表示不篩選)
        /// </summary>
        [JsonPropertyName("IsActive")]
        public bool? IsActive { get; set; }
    }
} 
