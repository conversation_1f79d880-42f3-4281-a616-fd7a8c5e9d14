﻿CREATE TABLE [dbo].[UserDept] (
    [Id]            VARCHAR (20)  NOT NULL,
    [DeptName]      NVARCHAR (30) NOT NULL,
    [ParentDeptId]  VARCHAR (20)  NULL,
    [LeaderUserId]  VARCHAR (15)  NULL,
    [IsDisabled]    BIT           NOT NULL,
    [CreatedUserId] VARCHAR (15)  NOT NULL,
    [CreatedTime]   DATETIME      NOT NULL,
    [UpdatedUserId] VARCHAR (15)  NOT NULL,
    [UpdatedTime]   DATETIME      NOT NULL,
    CONSTRAINT [PK_UserDept] PRIMARY KEY CLUSTERED ([Id] ASC),
    CONSTRAINT [FK_UserDept_UserDept] FOREIGN KEY ([ParentDeptId]) REFERENCES [dbo].[UserDept] ([Id]),
    CONSTRAINT [FK_UserDept_UserInfo] FOREIGN KEY ([LeaderUserId]) REFERENCES [dbo].[UserInfo] ([Id])
);


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'組織部門資料', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'UserDept';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'部門代碼', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'UserDept', @level2type = N'COLUMN', @level2name = N'Id';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'部門名稱', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'UserDept', @level2type = N'COLUMN', @level2name = N'DeptName';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'上層部門代碼', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'UserDept', @level2type = N'COLUMN', @level2name = N'ParentDeptId';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'部門主管員工編號', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'UserDept', @level2type = N'COLUMN', @level2name = N'LeaderUserId';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'是否刪除', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'UserDept', @level2type = N'COLUMN', @level2name = N'IsDisabled';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'建立者', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'UserDept', @level2type = N'COLUMN', @level2name = N'CreatedUserId';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'建立時間', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'UserDept', @level2type = N'COLUMN', @level2name = N'CreatedTime';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'更新者', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'UserDept', @level2type = N'COLUMN', @level2name = N'UpdatedUserId';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'更新時間', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'UserDept', @level2type = N'COLUMN', @level2name = N'UpdatedTime';

