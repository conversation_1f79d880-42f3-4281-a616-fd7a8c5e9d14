using AlifeApi.BusinessRules.Infrastructure;

namespace AlifeApi.BusinessRules.CrmOptionModels
{
    /// <summary>
    /// CRM選項查詢輸入模型
    /// </summary>
    public class CrmOptionQueryInput : PagedListInput
    {
        /// <summary>
        /// 案場代碼 (篩選條件)
        /// </summary>
        public string? SiteCode { get; set; }

        /// <summary>
        /// CRM選項類型ID (篩選條件)
        /// </summary>
        public long? CrmOptionTypeId { get; set; }

        /// <summary>
        /// 選項值 (模糊查詢)
        /// </summary>
        public string? OptionValue { get; set; }

        /// <summary>
        /// 是否啟用 (篩選條件)
        /// </summary>
        public bool? IsActive { get; set; }
    }
}
