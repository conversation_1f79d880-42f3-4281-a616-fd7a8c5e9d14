#### [AlifeApi.BusinessRules](index.md 'index')
### [AlifeApi.BusinessRules.SalesModels](AlifeApi.BusinessRules.SalesModels.md 'AlifeApi.BusinessRules.SalesModels')

## FloorSummary Class

樓層統計摘要

```csharp
public class FloorSummary
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; FloorSummary
### Properties

<a name='AlifeApi.BusinessRules.SalesModels.FloorSummary.Available'></a>

## FloorSummary.Available Property

可售數量

```csharp
public int Available { get; set; }
```

#### Property Value
[System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')

<a name='AlifeApi.BusinessRules.SalesModels.FloorSummary.Reserved'></a>

## FloorSummary.Reserved Property

保留數量 (包含保留和樣品屋)

```csharp
public int Reserved { get; set; }
```

#### Property Value
[System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')

<a name='AlifeApi.BusinessRules.SalesModels.FloorSummary.SalesRate'></a>

## FloorSummary.SalesRate Property

去化百分比 (字串格式，如 "66%" 或 "-")

```csharp
public string SalesRate { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.SalesModels.FloorSummary.Sold'></a>

## FloorSummary.Sold Property

已售數量 (包含售和定)

```csharp
public int Sold { get; set; }
```

#### Property Value
[System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')