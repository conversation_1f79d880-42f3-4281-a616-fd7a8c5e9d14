#### [AlifeApi.BusinessRules](index.md 'index')
### [AlifeApi.BusinessRules.Infrastructure](AlifeApi.BusinessRules.Infrastructure.md 'AlifeApi.BusinessRules.Infrastructure')

## ServiceBase<TDbContext> Class

The service base.

```csharp
public abstract class ServiceBase<TDbContext> : AlifeApi.Common.InfrastructureBase,
AlifeApi.Common.DependencyInjection.IScopedDependency
    where TDbContext : Microsoft.EntityFrameworkCore.DbContext
```
#### Type parameters

<a name='AlifeApi.BusinessRules.Infrastructure.ServiceBase_TDbContext_.TDbContext'></a>

`TDbContext`

The type of the database context.

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; [AlifeApi.Common.InfrastructureBase](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.Common.InfrastructureBase 'AlifeApi.Common.InfrastructureBase') &#129106; ServiceBase<TDbContext>

Derived  
&#8627; [BuildingService](AlifeApi.BusinessRules.BuildingModels.BuildingService.md 'AlifeApi.BusinessRules.BuildingModels.BuildingService')  
&#8627; [LargeCategoryService](AlifeApi.BusinessRules.CategoryModels.LargeCategoryService.md 'AlifeApi.BusinessRules.CategoryModels.LargeCategoryService')  
&#8627; [MediumCategoryService](AlifeApi.BusinessRules.CategoryModels.MediumCategoryService.md 'AlifeApi.BusinessRules.CategoryModels.MediumCategoryService')  
&#8627; [SmallCategoryService](AlifeApi.BusinessRules.CategoryModels.SmallCategoryService.md 'AlifeApi.BusinessRules.CategoryModels.SmallCategoryService')  
&#8627; [SalesManagementService](AlifeApi.BusinessRules.CommonModels.SalesManagementService.md 'AlifeApi.BusinessRules.CommonModels.SalesManagementService')  
&#8627; [CompanyService](AlifeApi.BusinessRules.CompanyModels.CompanyService.md 'AlifeApi.BusinessRules.CompanyModels.CompanyService')  
&#8627; [CrmOptionService](AlifeApi.BusinessRules.CrmOptionModels.CrmOptionService.md 'AlifeApi.BusinessRules.CrmOptionModels.CrmOptionService')  
&#8627; [CustomerService](AlifeApi.BusinessRules.CustomerModels.CustomerService.md 'AlifeApi.BusinessRules.CustomerModels.CustomerService')  
&#8627; [DailyReportService](AlifeApi.BusinessRules.DailyReportModels.DailyReportService.md 'AlifeApi.BusinessRules.DailyReportModels.DailyReportService')  
&#8627; [DepartmentService](AlifeApi.BusinessRules.DepartmentModels.DepartmentService.md 'AlifeApi.BusinessRules.DepartmentModels.DepartmentService')  
&#8627; [FloorService](AlifeApi.BusinessRules.FloorModels.FloorService.md 'AlifeApi.BusinessRules.FloorModels.FloorService')  
&#8627; [JobTitleService](AlifeApi.BusinessRules.JobTitleModels.JobTitleService.md 'AlifeApi.BusinessRules.JobTitleModels.JobTitleService')  
&#8627; [LoginServicePg](AlifeApi.BusinessRules.LoginModels.LoginServicePg.md 'AlifeApi.BusinessRules.LoginModels.LoginServicePg')  
&#8627; [LogService](AlifeApi.BusinessRules.LogModels.LogService.md 'AlifeApi.BusinessRules.LogModels.LogService')  
&#8627; [OwnerService](AlifeApi.BusinessRules.OwnerModels.OwnerService.md 'AlifeApi.BusinessRules.OwnerModels.OwnerService')  
&#8627; [ParkingSpaceService](AlifeApi.BusinessRules.ParkingSpaceModels.ParkingSpaceService.md 'AlifeApi.BusinessRules.ParkingSpaceModels.ParkingSpaceService')  
&#8627; [PaymentRecordService](AlifeApi.BusinessRules.PaymentRecordModels.PaymentRecordService.md 'AlifeApi.BusinessRules.PaymentRecordModels.PaymentRecordService')  
&#8627; [PurchaseOrderService](AlifeApi.BusinessRules.PurchaseOrderModels.PurchaseOrderService.md 'AlifeApi.BusinessRules.PurchaseOrderModels.PurchaseOrderService')  
&#8627; [ReviewTaskService](AlifeApi.BusinessRules.ReviewModels.ReviewTaskService.md 'AlifeApi.BusinessRules.ReviewModels.ReviewTaskService')  
&#8627; [RoleGroupServicePg](AlifeApi.BusinessRules.RoleGroupModels.RoleGroupServicePg.md 'AlifeApi.BusinessRules.RoleGroupModels.RoleGroupServicePg')  
&#8627; [SalesService](AlifeApi.BusinessRules.SalesModels.SalesService.md 'AlifeApi.BusinessRules.SalesModels.SalesService')  
&#8627; [SiteService](AlifeApi.BusinessRules.SiteModels.SiteService.md 'AlifeApi.BusinessRules.SiteModels.SiteService')  
&#8627; [SupplierService](AlifeApi.BusinessRules.SupplierModels.SupplierService.md 'AlifeApi.BusinessRules.SupplierModels.SupplierService')  
&#8627; [SysCodeService](AlifeApi.BusinessRules.SysCodeModels.SysCodeService.md 'AlifeApi.BusinessRules.SysCodeModels.SysCodeService')  
&#8627; [PasswordManager](AlifeApi.BusinessRules.UserInfoModels.PasswordManager.md 'AlifeApi.BusinessRules.UserInfoModels.PasswordManager')  
&#8627; [UserInfoServicePg](AlifeApi.BusinessRules.UserInfoModels.UserInfoServicePg.md 'AlifeApi.BusinessRules.UserInfoModels.UserInfoServicePg')  
&#8627; [UserRecordService](AlifeApi.BusinessRules.UserRecordModels.UserRecordService.md 'AlifeApi.BusinessRules.UserRecordModels.UserRecordService')  
&#8627; [WeeklyReportService](AlifeApi.BusinessRules.WeeklyReportModels.WeeklyReportService.md 'AlifeApi.BusinessRules.WeeklyReportModels.WeeklyReportService')

Implements [AlifeApi.Common.DependencyInjection.IScopedDependency](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.Common.DependencyInjection.IScopedDependency 'AlifeApi.Common.DependencyInjection.IScopedDependency')
### Constructors

<a name='AlifeApi.BusinessRules.Infrastructure.ServiceBase_TDbContext_.ServiceBase(System.IServiceProvider,TDbContext)'></a>

## ServiceBase(IServiceProvider, TDbContext) Constructor

Initializes a new instance of the [ServiceBase&lt;TDbContext&gt;](AlifeApi.BusinessRules.Infrastructure.ServiceBase_TDbContext_.md 'AlifeApi.BusinessRules.Infrastructure.ServiceBase<TDbContext>') class.

```csharp
public ServiceBase(System.IServiceProvider serviceProvider, TDbContext dbContext);
```
#### Parameters

<a name='AlifeApi.BusinessRules.Infrastructure.ServiceBase_TDbContext_.ServiceBase(System.IServiceProvider,TDbContext).serviceProvider'></a>

`serviceProvider` [System.IServiceProvider](https://docs.microsoft.com/en-us/dotnet/api/System.IServiceProvider 'System.IServiceProvider')

The service provider.

<a name='AlifeApi.BusinessRules.Infrastructure.ServiceBase_TDbContext_.ServiceBase(System.IServiceProvider,TDbContext).dbContext'></a>

`dbContext` [TDbContext](AlifeApi.BusinessRules.Infrastructure.ServiceBase_TDbContext_.md#AlifeApi.BusinessRules.Infrastructure.ServiceBase_TDbContext_.TDbContext 'AlifeApi.BusinessRules.Infrastructure.ServiceBase<TDbContext>.TDbContext')

The database context.

#### Exceptions

[System.ArgumentNullException](https://docs.microsoft.com/en-us/dotnet/api/System.ArgumentNullException 'System.ArgumentNullException')  
dbContext
### Properties

<a name='AlifeApi.BusinessRules.Infrastructure.ServiceBase_TDbContext_.Db'></a>

## ServiceBase<TDbContext>.Db Property

Gets or sets the database context.

```csharp
protected TDbContext Db { get; set; }
```

#### Property Value
[TDbContext](AlifeApi.BusinessRules.Infrastructure.ServiceBase_TDbContext_.md#AlifeApi.BusinessRules.Infrastructure.ServiceBase_TDbContext_.TDbContext 'AlifeApi.BusinessRules.Infrastructure.ServiceBase<TDbContext>.TDbContext')