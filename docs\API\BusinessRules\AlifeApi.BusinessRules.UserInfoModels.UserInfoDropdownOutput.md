#### [AlifeApi.BusinessRules](index.md 'index')
### [AlifeApi.BusinessRules.UserInfoModels](AlifeApi.BusinessRules.UserInfoModels.md 'AlifeApi.BusinessRules.UserInfoModels')

## UserInfoDropdownOutput Class

```csharp
public class UserInfoDropdownOutput
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; UserInfoDropdownOutput
### Properties

<a name='AlifeApi.BusinessRules.UserInfoModels.UserInfoDropdownOutput.Name'></a>

## UserInfoDropdownOutput.Name Property

顯示名稱

```csharp
public string Name { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.UserInfoModels.UserInfoDropdownOutput.Value'></a>

## UserInfoDropdownOutput.Value Property

UserId

```csharp
public string Value { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')