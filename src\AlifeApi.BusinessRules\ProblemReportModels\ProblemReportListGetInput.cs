﻿using System.Text.Json.Serialization;
using AlifeApi.BusinessRules.Infrastructure;

namespace AlifeApi.BusinessRules.ProblemReportModels
{
    /// <summary>
    /// 問題回報列表
    /// </summary>
    public class ProblemReportListGetInput : PagedListInput
    {
        /// <summary>
        /// 搜尋開始日期
        /// </summary>
        [JsonPropertyName("QueryDateTimeStart")]
        public DateTime? QueryStartDate { get; set; }

        /// <summary>
        /// 搜尋結束日期
        /// </summary>
        [JsonPropertyName("QueryDateTimeEnd")]
        public DateTime? QueryEndDate { get; set; }
    }
}
