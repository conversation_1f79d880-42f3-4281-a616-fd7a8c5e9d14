#### [AlifeApi.BusinessRules](index.md 'index')
### [AlifeApi.BusinessRules.Infrastructure](AlifeApi.BusinessRules.Infrastructure.md 'AlifeApi.BusinessRules.Infrastructure')

## RecordDisableInput<T> Class

停用/啟用資料的參數

```csharp
public class RecordDisableInput<T>
    where T : struct, System.ValueType, System.ValueType
```
#### Type parameters

<a name='AlifeApi.BusinessRules.Infrastructure.RecordDisableInput_T_.T'></a>

`T`

記錄 ID 的類型

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; RecordDisableInput<T>
### Properties

<a name='AlifeApi.BusinessRules.Infrastructure.RecordDisableInput_T_.Id'></a>

## RecordDisableInput<T>.Id Property

Gets or sets the identifier.

```csharp
public System.Nullable<T> Id { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[T](AlifeApi.BusinessRules.Infrastructure.RecordDisableInput_T_.md#AlifeApi.BusinessRules.Infrastructure.RecordDisableInput_T_.T 'AlifeApi.BusinessRules.Infrastructure.RecordDisableInput<T>.T')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

<a name='AlifeApi.BusinessRules.Infrastructure.RecordDisableInput_T_.IsDisabled'></a>

## RecordDisableInput<T>.IsDisabled Property

Gets or sets the is disabled.

```csharp
public System.Nullable<bool> IsDisabled { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.Boolean](https://docs.microsoft.com/en-us/dotnet/api/System.Boolean 'System.Boolean')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')