#### [AlifeApi.BusinessRules](index.md 'index')
### [AlifeApi.BusinessRules.RoleGroupModels](AlifeApi.BusinessRules.RoleGroupModels.md 'AlifeApi.BusinessRules.RoleGroupModels')

## RoleGroupDropdownInputPg Class

角色群組下拉選單查詢輸入

```csharp
public class RoleGroupDropdownInputPg
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; RoleGroupDropdownInputPg
### Properties

<a name='AlifeApi.BusinessRules.RoleGroupModels.RoleGroupDropdownInputPg.SiteCode'></a>

## RoleGroupDropdownInputPg.SiteCode Property

案場代碼 (可選)

```csharp
public string SiteCode { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')