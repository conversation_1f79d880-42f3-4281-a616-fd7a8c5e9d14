#### [AlifeApi.DataAccess](index.md 'index')
### [AlifeApi.DataAccess.ProjectContent](AlifeApi.DataAccess.ProjectContent.md 'AlifeApi.DataAccess.ProjectContent')

## UserJobTitle Class

員工職位關聯表，用於儲存員工與職位的關聯關係，支持員工擁有複數職位。

```csharp
public class UserJobTitle
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; UserJobTitle
### Properties

<a name='AlifeApi.DataAccess.ProjectContent.UserJobTitle.IsPrimary'></a>

## UserJobTitle.IsPrimary Property

是否為主要職位，true 表示是，false 表示否，預設為 false。

```csharp
public System.Nullable<bool> IsPrimary { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.Boolean](https://docs.microsoft.com/en-us/dotnet/api/System.Boolean 'System.Boolean')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

<a name='AlifeApi.DataAccess.ProjectContent.UserJobTitle.JobTitleId'></a>

## UserJobTitle.JobTitleId Property

職位編號，主鍵的一部分，對應 JobTitles 表的 JobTitleId。

```csharp
public string JobTitleId { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContent.UserJobTitle.UserInfoId'></a>

## UserJobTitle.UserInfoId Property

員工編號，主鍵的一部分，對應 UserInfo 表的 UserInfoId。

```csharp
public string UserInfoId { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')