﻿using System.Text.Json.Serialization;

namespace AlifeApi.BusinessRules.Infrastructure
{
    /// <summary>
    /// 分頁列表輸入
    /// </summary>
    public abstract class PagedListInput
    {
        /// <summary>
        /// 使用分頁模式
        /// </summary>
        public bool UsingPaging { get; set; }

        /// <summary>
        /// 每頁筆數
        /// </summary>
        [JsonPropertyName("NumberOfPperPage")]
        public int NumberOfPerPage { get; set; }

        /// <summary>
        /// 頁次索引
        /// </summary>
        public int PageIndex { get; set; } = 1;

        /// <summary>
        /// 排序資訊
        /// </summary>
        public IEnumerable<SortOrderInfo> SortOrderInfos { get; set; } = Enumerable.Empty<SortOrderInfo>();

        /// <summary>
        /// 過濾資訊
        /// </summary>
        public IEnumerable<SearchTermInfo> SearchTermInfos { get; set; } = Enumerable.Empty<SearchTermInfo>();
    }
}
