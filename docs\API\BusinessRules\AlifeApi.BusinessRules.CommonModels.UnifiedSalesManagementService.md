#### [AlifeApi.BusinessRules](index.md 'index')
### [AlifeApi.BusinessRules.CommonModels](AlifeApi.BusinessRules.CommonModels.md 'AlifeApi.BusinessRules.CommonModels')

## UnifiedSalesManagementService Class

統一銷售管理服務 - 統一處理"可售/保留(房屋資料)"和"可售/保留(車位資料)"

```csharp
public class UnifiedSalesManagementService : AlifeApi.BusinessRules.Infrastructure.ServiceBase<AlifeApi.DataAccess.ProjectContent.alifeContext>
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; [AlifeApi.Common.InfrastructureBase](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.Common.InfrastructureBase 'AlifeApi.Common.InfrastructureBase') &#129106; [AlifeApi.BusinessRules.Infrastructure.ServiceBase&lt;](AlifeApi.BusinessRules.Infrastructure.ServiceBase_TDbContext_.md 'AlifeApi.BusinessRules.Infrastructure.ServiceBase<TDbContext>')[AlifeApi.DataAccess.ProjectContent.alifeContext](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.DataAccess.ProjectContent.alifeContext 'AlifeApi.DataAccess.ProjectContent.alifeContext')[&gt;](AlifeApi.BusinessRules.Infrastructure.ServiceBase_TDbContext_.md 'AlifeApi.BusinessRules.Infrastructure.ServiceBase<TDbContext>') &#129106; UnifiedSalesManagementService
### Methods

<a name='AlifeApi.BusinessRules.CommonModels.UnifiedSalesManagementService.BuildParkingQuery(AlifeApi.BusinessRules.CommonModels.UnifiedSalesQueryInput)'></a>

## UnifiedSalesManagementService.BuildParkingQuery(UnifiedSalesQueryInput) Method

建立車位查詢

```csharp
private System.Linq.IQueryable<AlifeApi.DataAccess.ProjectContent.ParkingSpace> BuildParkingQuery(AlifeApi.BusinessRules.CommonModels.UnifiedSalesQueryInput input);
```
#### Parameters

<a name='AlifeApi.BusinessRules.CommonModels.UnifiedSalesManagementService.BuildParkingQuery(AlifeApi.BusinessRules.CommonModels.UnifiedSalesQueryInput).input'></a>

`input` [UnifiedSalesQueryInput](AlifeApi.BusinessRules.CommonModels.UnifiedSalesQueryInput.md 'AlifeApi.BusinessRules.CommonModels.UnifiedSalesQueryInput')

#### Returns
[System.Linq.IQueryable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Linq.IQueryable-1 'System.Linq.IQueryable`1')[AlifeApi.DataAccess.ProjectContent.ParkingSpace](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.DataAccess.ProjectContent.ParkingSpace 'AlifeApi.DataAccess.ProjectContent.ParkingSpace')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Linq.IQueryable-1 'System.Linq.IQueryable`1')

<a name='AlifeApi.BusinessRules.CommonModels.UnifiedSalesManagementService.BuildUnitQuery(AlifeApi.BusinessRules.CommonModels.UnifiedSalesQueryInput)'></a>

## UnifiedSalesManagementService.BuildUnitQuery(UnifiedSalesQueryInput) Method

建立房屋查詢

```csharp
private System.Linq.IQueryable<AlifeApi.DataAccess.ProjectContent.Unit> BuildUnitQuery(AlifeApi.BusinessRules.CommonModels.UnifiedSalesQueryInput input);
```
#### Parameters

<a name='AlifeApi.BusinessRules.CommonModels.UnifiedSalesManagementService.BuildUnitQuery(AlifeApi.BusinessRules.CommonModels.UnifiedSalesQueryInput).input'></a>

`input` [UnifiedSalesQueryInput](AlifeApi.BusinessRules.CommonModels.UnifiedSalesQueryInput.md 'AlifeApi.BusinessRules.CommonModels.UnifiedSalesQueryInput')

#### Returns
[System.Linq.IQueryable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Linq.IQueryable-1 'System.Linq.IQueryable`1')[AlifeApi.DataAccess.ProjectContent.Unit](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.DataAccess.ProjectContent.Unit 'AlifeApi.DataAccess.ProjectContent.Unit')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Linq.IQueryable-1 'System.Linq.IQueryable`1')

<a name='AlifeApi.BusinessRules.CommonModels.UnifiedSalesManagementService.FillRelatedDataAsync(System.Collections.Generic.List_AlifeApi.BusinessRules.CommonModels.UnifiedSalesItemOutput_)'></a>

## UnifiedSalesManagementService.FillRelatedDataAsync(List<UnifiedSalesItemOutput>) Method

填充關聯資料（案場名稱、建築物名稱、樓層標示）

```csharp
private System.Threading.Tasks.Task FillRelatedDataAsync(System.Collections.Generic.List<AlifeApi.BusinessRules.CommonModels.UnifiedSalesItemOutput> items);
```
#### Parameters

<a name='AlifeApi.BusinessRules.CommonModels.UnifiedSalesManagementService.FillRelatedDataAsync(System.Collections.Generic.List_AlifeApi.BusinessRules.CommonModels.UnifiedSalesItemOutput_).items'></a>

`items` [System.Collections.Generic.List&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Collections.Generic.List-1 'System.Collections.Generic.List`1')[UnifiedSalesItemOutput](AlifeApi.BusinessRules.CommonModels.UnifiedSalesItemOutput.md 'AlifeApi.BusinessRules.CommonModels.UnifiedSalesItemOutput')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Collections.Generic.List-1 'System.Collections.Generic.List`1')

#### Returns
[System.Threading.Tasks.Task](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task 'System.Threading.Tasks.Task')

<a name='AlifeApi.BusinessRules.CommonModels.UnifiedSalesManagementService.GetAvailableItemsAsync(AlifeApi.BusinessRules.CommonModels.UnifiedSalesQueryInput)'></a>

## UnifiedSalesManagementService.GetAvailableItemsAsync(UnifiedSalesQueryInput) Method

取得可售物件列表

```csharp
public System.Threading.Tasks.Task<AlifeApi.BusinessRules.Infrastructure.PagedListOutput<AlifeApi.BusinessRules.CommonModels.UnifiedSalesItemOutput>> GetAvailableItemsAsync(AlifeApi.BusinessRules.CommonModels.UnifiedSalesQueryInput input);
```
#### Parameters

<a name='AlifeApi.BusinessRules.CommonModels.UnifiedSalesManagementService.GetAvailableItemsAsync(AlifeApi.BusinessRules.CommonModels.UnifiedSalesQueryInput).input'></a>

`input` [UnifiedSalesQueryInput](AlifeApi.BusinessRules.CommonModels.UnifiedSalesQueryInput.md 'AlifeApi.BusinessRules.CommonModels.UnifiedSalesQueryInput')

#### Returns
[System.Threading.Tasks.Task&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')[AlifeApi.BusinessRules.Infrastructure.PagedListOutput&lt;](AlifeApi.BusinessRules.Infrastructure.PagedListOutput_TDetail_.md 'AlifeApi.BusinessRules.Infrastructure.PagedListOutput<TDetail>')[UnifiedSalesItemOutput](AlifeApi.BusinessRules.CommonModels.UnifiedSalesItemOutput.md 'AlifeApi.BusinessRules.CommonModels.UnifiedSalesItemOutput')[&gt;](AlifeApi.BusinessRules.Infrastructure.PagedListOutput_TDetail_.md 'AlifeApi.BusinessRules.Infrastructure.PagedListOutput<TDetail>')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')

<a name='AlifeApi.BusinessRules.CommonModels.UnifiedSalesManagementService.GetParkingSpaceStatisticsFromUnitsAsync(string)'></a>

## UnifiedSalesManagementService.GetParkingSpaceStatisticsFromUnitsAsync(string) Method

從Units表獲取車位統計資料

```csharp
private System.Threading.Tasks.Task<global::ParkingSpaceSalesStatistics> GetParkingSpaceStatisticsFromUnitsAsync(string siteCode);
```
#### Parameters

<a name='AlifeApi.BusinessRules.CommonModels.UnifiedSalesManagementService.GetParkingSpaceStatisticsFromUnitsAsync(string).siteCode'></a>

`siteCode` [System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

#### Returns
[System.Threading.Tasks.Task&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')[ParkingSpaceSalesStatistics](https://docs.microsoft.com/en-us/dotnet/api/ParkingSpaceSalesStatistics 'ParkingSpaceSalesStatistics')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')

<a name='AlifeApi.BusinessRules.CommonModels.UnifiedSalesManagementService.GetReservedItemsAsync(AlifeApi.BusinessRules.CommonModels.UnifiedSalesQueryInput)'></a>

## UnifiedSalesManagementService.GetReservedItemsAsync(UnifiedSalesQueryInput) Method

取得保留物件列表

```csharp
public System.Threading.Tasks.Task<AlifeApi.BusinessRules.Infrastructure.PagedListOutput<AlifeApi.BusinessRules.CommonModels.UnifiedSalesItemOutput>> GetReservedItemsAsync(AlifeApi.BusinessRules.CommonModels.UnifiedSalesQueryInput input);
```
#### Parameters

<a name='AlifeApi.BusinessRules.CommonModels.UnifiedSalesManagementService.GetReservedItemsAsync(AlifeApi.BusinessRules.CommonModels.UnifiedSalesQueryInput).input'></a>

`input` [UnifiedSalesQueryInput](AlifeApi.BusinessRules.CommonModels.UnifiedSalesQueryInput.md 'AlifeApi.BusinessRules.CommonModels.UnifiedSalesQueryInput')

#### Returns
[System.Threading.Tasks.Task&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')[AlifeApi.BusinessRules.Infrastructure.PagedListOutput&lt;](AlifeApi.BusinessRules.Infrastructure.PagedListOutput_TDetail_.md 'AlifeApi.BusinessRules.Infrastructure.PagedListOutput<TDetail>')[UnifiedSalesItemOutput](AlifeApi.BusinessRules.CommonModels.UnifiedSalesItemOutput.md 'AlifeApi.BusinessRules.CommonModels.UnifiedSalesItemOutput')[&gt;](AlifeApi.BusinessRules.Infrastructure.PagedListOutput_TDetail_.md 'AlifeApi.BusinessRules.Infrastructure.PagedListOutput<TDetail>')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')

<a name='AlifeApi.BusinessRules.CommonModels.UnifiedSalesManagementService.GetUnifiedSalesListAsync(AlifeApi.BusinessRules.CommonModels.UnifiedSalesQueryInput)'></a>

## UnifiedSalesManagementService.GetUnifiedSalesListAsync(UnifiedSalesQueryInput) Method

取得統一的銷售物件列表（包含房屋和車位）

```csharp
public System.Threading.Tasks.Task<AlifeApi.BusinessRules.Infrastructure.PagedListOutput<AlifeApi.BusinessRules.CommonModels.UnifiedSalesItemOutput>> GetUnifiedSalesListAsync(AlifeApi.BusinessRules.CommonModels.UnifiedSalesQueryInput input);
```
#### Parameters

<a name='AlifeApi.BusinessRules.CommonModels.UnifiedSalesManagementService.GetUnifiedSalesListAsync(AlifeApi.BusinessRules.CommonModels.UnifiedSalesQueryInput).input'></a>

`input` [UnifiedSalesQueryInput](AlifeApi.BusinessRules.CommonModels.UnifiedSalesQueryInput.md 'AlifeApi.BusinessRules.CommonModels.UnifiedSalesQueryInput')

查詢條件

#### Returns
[System.Threading.Tasks.Task&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')[AlifeApi.BusinessRules.Infrastructure.PagedListOutput&lt;](AlifeApi.BusinessRules.Infrastructure.PagedListOutput_TDetail_.md 'AlifeApi.BusinessRules.Infrastructure.PagedListOutput<TDetail>')[UnifiedSalesItemOutput](AlifeApi.BusinessRules.CommonModels.UnifiedSalesItemOutput.md 'AlifeApi.BusinessRules.CommonModels.UnifiedSalesItemOutput')[&gt;](AlifeApi.BusinessRules.Infrastructure.PagedListOutput_TDetail_.md 'AlifeApi.BusinessRules.Infrastructure.PagedListOutput<TDetail>')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')  
統一的銷售物件列表

<a name='AlifeApi.BusinessRules.CommonModels.UnifiedSalesManagementService.GetUnifiedSalesStatisticsAsync(string)'></a>

## UnifiedSalesManagementService.GetUnifiedSalesStatisticsAsync(string) Method

取得銷售統計

```csharp
public System.Threading.Tasks.Task<AlifeApi.BusinessRules.CommonModels.UnifiedSalesStatistics> GetUnifiedSalesStatisticsAsync(string siteCode);
```
#### Parameters

<a name='AlifeApi.BusinessRules.CommonModels.UnifiedSalesManagementService.GetUnifiedSalesStatisticsAsync(string).siteCode'></a>

`siteCode` [System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

#### Returns
[System.Threading.Tasks.Task&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')[UnifiedSalesStatistics](AlifeApi.BusinessRules.CommonModels.UnifiedSalesStatistics.md 'AlifeApi.BusinessRules.CommonModels.UnifiedSalesStatistics')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')