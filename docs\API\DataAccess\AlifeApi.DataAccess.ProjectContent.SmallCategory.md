#### [AlifeApi.DataAccess](index.md 'index')
### [AlifeApi.DataAccess.ProjectContent](AlifeApi.DataAccess.ProjectContent.md 'AlifeApi.DataAccess.ProjectContent')

## SmallCategory Class

商品小分類資料表

```csharp
public class SmallCategory
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; SmallCategory
### Properties

<a name='AlifeApi.DataAccess.ProjectContent.SmallCategory.CreatedUserInfoId'></a>

## SmallCategory.CreatedUserInfoId Property

建立者，記錄創建此資料的員工編號。

```csharp
public string CreatedUserInfoId { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContent.SmallCategory.CreateTime'></a>

## SmallCategory.CreateTime Property

創建時間，記錄資料創建的時間。

```csharp
public System.DateTime CreateTime { get; set; }
```

#### Property Value
[System.DateTime](https://docs.microsoft.com/en-us/dotnet/api/System.DateTime 'System.DateTime')

<a name='AlifeApi.DataAccess.ProjectContent.SmallCategory.Description'></a>

## SmallCategory.Description Property

小分類的詳細文字描述

```csharp
public string Description { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContent.SmallCategory.IsActive'></a>

## SmallCategory.IsActive Property

是否啟用此分類 (TRUE: 啟用, FALSE: 停用)

```csharp
public System.Nullable<bool> IsActive { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.Boolean](https://docs.microsoft.com/en-us/dotnet/api/System.Boolean 'System.Boolean')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

<a name='AlifeApi.DataAccess.ProjectContent.SmallCategory.MediumCategoryId'></a>

## SmallCategory.MediumCategoryId Property

所屬中分類的ID，需由應用程式確保其有效性

```csharp
public long MediumCategoryId { get; set; }
```

#### Property Value
[System.Int64](https://docs.microsoft.com/en-us/dotnet/api/System.Int64 'System.Int64')

<a name='AlifeApi.DataAccess.ProjectContent.SmallCategory.Name'></a>

## SmallCategory.Name Property

小分類的顯示名稱

```csharp
public string Name { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContent.SmallCategory.SmallCategoryId'></a>

## SmallCategory.SmallCategoryId Property

主鍵，小分類唯一識別碼

```csharp
public long SmallCategoryId { get; set; }
```

#### Property Value
[System.Int64](https://docs.microsoft.com/en-us/dotnet/api/System.Int64 'System.Int64')

<a name='AlifeApi.DataAccess.ProjectContent.SmallCategory.SortOrder'></a>

## SmallCategory.SortOrder Property

在同一個中分類下的排序順序，數字越小越前面

```csharp
public int SortOrder { get; set; }
```

#### Property Value
[System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')

<a name='AlifeApi.DataAccess.ProjectContent.SmallCategory.UpdatedUserInfoId'></a>

## SmallCategory.UpdatedUserInfoId Property

更新者，記錄最後更新此資料的員工編號。

```csharp
public string UpdatedUserInfoId { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContent.SmallCategory.UpdateTime'></a>

## SmallCategory.UpdateTime Property

更新時間，記錄資料最後更新的時間。

```csharp
public System.DateTime UpdateTime { get; set; }
```

#### Property Value
[System.DateTime](https://docs.microsoft.com/en-us/dotnet/api/System.DateTime 'System.DateTime')