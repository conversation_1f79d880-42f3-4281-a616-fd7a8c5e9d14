using AlifeApi.BusinessRules.Infrastructure;
using AlifeApi.BusinessRules.SupplierModels;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Threading.Tasks;

namespace AlifeApi.WebApi.Controllers
{
    /// <summary>
    /// 供應商管理
    /// </summary>
    public class SuppliersController : AuthenticatedController
    {
        private readonly SupplierService _supplierService;

        public SuppliersController(SupplierService supplierService)
        {
            _supplierService = supplierService ?? throw new ArgumentNullException(nameof(supplierService));
        }

        /// <summary>
        /// 取得供應商列表 (分頁)
        /// </summary>
        /// <param name="input">查詢條件</param>
        /// <returns>分頁的供應商列表</returns>
        [HttpPost]
        public async Task<ActionResult<PagedListOutput<SupplierListOutput>>> GetSuppliers([FromBody] SupplierQueryInput input)
        {
            var result = await _supplierService.GetSupplierListAsync(input);
            return Ok(result);
        }

        /// <summary>
        /// 根據供應商ID取得詳細資訊
        /// </summary>
        /// <param name="supplierId">供應商ID</param>
        /// <returns>供應商詳細資訊</returns>
        [HttpGet("{supplierId}")]
        public async Task<ActionResult<SupplierOutput>> GetSupplier(int supplierId)
        {
            var result = await _supplierService.GetSupplierByIdAsync(supplierId);
            if (result == null)
                return NotFound(new { message = $"找不到ID為 {supplierId} 的供應商" });
            return Ok(result);
        }

        /// <summary>
        /// 新增供應商
        /// </summary>
        /// <param name="input">供應商建立輸入資料</param>
        /// <returns>新增的供應商ID</returns>
        [HttpPost]
        public async Task<ActionResult<object>> CreateSupplier([FromForm] SupplierCreateInput input)
        {
            try
            {
                var id = await _supplierService.CreateSupplierAsync(input);
                return CreatedAtAction(nameof(GetSupplier), new { supplierId = id }, new { SupplierId = id });
            }
            catch (ArgumentException ex) // 捕捉業務邏輯驗證錯誤，例如名稱重複
            {
                return BadRequest(new { message = ex.Message });
            }
            catch (Exception ex) // 其他未預期錯誤
            {
                // Log the exception (using a logger, not Console.WriteLine in production)
                Console.WriteLine($"建立供應商時發生錯誤: {ex}");
                return StatusCode(500, new { message = "建立供應商時發生內部錯誤，請稍後再試。" });
            }
        }

        /// <summary>
        /// 更新供應商
        /// </summary>
        /// <param name="supplierId">供應商ID</param>
        /// <param name="input">供應商更新輸入資料</param>
        /// <returns>NoContent</returns>
        [HttpPut("{supplierId}")]
        public async Task<ActionResult> UpdateSupplier(int supplierId, [FromForm] SupplierUpdateInput input)
        {
            try
            {
                await _supplierService.UpdateSupplierAsync(supplierId, input);
                return Ok();
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }

        /// <summary>
        /// 刪除供應商
        /// </summary>
        /// <param name="supplierId">供應商ID</param>
        /// <returns>NoContent</returns>
        [HttpDelete("{supplierId}")]
        public async Task<ActionResult> DeleteSupplier(int supplierId)
        {
            try
            {
                await _supplierService.DeleteSupplierAsync(supplierId);
                return Ok();
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }
    }
} 
