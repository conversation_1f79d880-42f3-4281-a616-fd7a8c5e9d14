﻿PRINT N'正在建立 資料表 [dbo].[SYS_Bulletins]...';


GO
CREATE TABLE [dbo].[SYS_Bulletins] (
    [BL_Id]           INT             IDENTITY (1, 1) NOT NULL,
    [BL_Title]        NVARCHAR (200)  NULL,
    [BL_Content]      NVARCHAR (1000) NULL,
    [BL_PostDateFrom] DATE            NULL,
    [BL_PostDateToxx] DATE            NULL,
    [BL_IsTop]        BIT             NOT NULL,
    [BL_IsEnable]     BIT             NOT NULL,
    [BL_CrUser]       VARCHAR (15)    NULL,
    [BL_CrDatetime]   DATETIME        NULL,
    [BL_UpUser]       VARCHAR (15)    NULL,
    [BL_UpDatetime]   DATETIME        NULL,
    CONSTRAINT [PK_SYS_Bulletins] PRIMARY KEY CLUSTERED ([BL_Id] ASC)
);


GO
PRINT N'正在建立 資料表 [dbo].[SYS_BulletinsAttach]...';


GO
CREATE TABLE [dbo].[SYS_BulletinsAttach] (
    [BLA_Id]         INT             IDENTITY (1, 1) NOT NULL,
    [BL_Id]          INT             NOT NULL,
    [BLA_OName]      NVARCHAR (300)  NULL,
    [BLA_File]       VARBINARY (MAX) NULL,
    [BLA_FileType]   VARCHAR (100)   NULL,
    [BLA_FileSzie]   INT             NULL,
    [BLA_CrUser]     VARCHAR (15)    NULL,
    [BLA_CrDatetime] DATETIME        NULL,
    CONSTRAINT [PK_SYS_BulletinsAttach] PRIMARY KEY CLUSTERED ([BLA_Id] ASC)
);


GO
PRINT N'正在建立 資料表 [dbo].[SYS_BulletinsAttachDownloadRecord]...';


GO
CREATE TABLE [dbo].[SYS_BulletinsAttachDownloadRecord] (
    [BLA_Id]           INT          NOT NULL,
    [UserId]           VARCHAR (15) NOT NULL,
    [DownloadCount]    INT          NOT NULL,
    [LastDownloadDate] DATETIME     NOT NULL,
    CONSTRAINT [PK_SYS_BulletinsAttachDownloadRecord] PRIMARY KEY CLUSTERED ([BLA_Id] ASC, [UserId] ASC)
);


GO
PRINT N'正在建立 資料表 [dbo].[SYS_BulletinsClickRecord]...';


GO
CREATE TABLE [dbo].[SYS_BulletinsClickRecord] (
    [BL_Id]        INT          NOT NULL,
    [UserId]       VARCHAR (15) NOT NULL,
    [LastViewDate] DATETIME     NOT NULL,
    [ViewCount]    INT          NOT NULL,
    CONSTRAINT [PK_SYS_BulletinsClickRecord] PRIMARY KEY CLUSTERED ([BL_Id] ASC, [UserId] ASC)
);


GO
PRINT N'正在建立 資料表 [dbo].[SYS_Code]...';


GO
CREATE TABLE [dbo].[SYS_Code] (
    [Type]          VARCHAR (50)   NOT NULL,
    [Code]          VARCHAR (30)   NOT NULL,
    [ParentCode]    VARCHAR (30)   NULL,
    [CodeDesc]      NVARCHAR (300) NOT NULL,
    [IsDisabled]    BIT            NOT NULL,
    [CodeOrder]     SMALLINT       NOT NULL,
    [CreatedUserId] VARCHAR (15)   NOT NULL,
    [CreatedTime]   DATETIME       NOT NULL,
    [UpdatedUserId] VARCHAR (15)   NOT NULL,
    [UpdatedTime]   DATETIME       NOT NULL,
    CONSTRAINT [PK_SYS_CODE] PRIMARY KEY CLUSTERED ([Type] ASC, [Code] ASC)
);


GO
PRINT N'正在建立 資料表 [dbo].[SYS_MenuFunc]...';


GO
CREATE TABLE [dbo].[SYS_MenuFunc] (
    [System]       VARCHAR (30)  NOT NULL,
    [FuncId]       VARCHAR (50)  NOT NULL,
    [FuncName]     NVARCHAR (50) NOT NULL,
    [FuncOrder]    INT           NOT NULL,
    [ParentFuncId] VARCHAR (50)  NOT NULL,
    CONSTRAINT [PK_SYS_MenuFunc] PRIMARY KEY CLUSTERED ([System] ASC, [FuncId] ASC)
);


GO
PRINT N'正在建立 資料表 [dbo].[SYS_ProblemReport]...';


GO
CREATE TABLE [dbo].[SYS_ProblemReport] (
    [Id]             BIGINT         IDENTITY (1, 1) NOT NULL,
    [DeptId]         VARCHAR (20)   NOT NULL,
    [System]         VARCHAR (30)   NOT NULL,
    [Menu]           NVARCHAR (50)  NOT NULL,
    [Subject]        NVARCHAR (200) NOT NULL,
    [ProblemContent] NVARCHAR (200) NOT NULL,
    [ProcessStatus]  VARCHAR (10)   NOT NULL,
    [ProblemType]    VARCHAR (10)   NOT NULL,
    [ReplyContent]   NVARCHAR (200) NOT NULL,
    [ReplyUserId]    VARCHAR (15)   NULL,
    [ReplyTime]      DATETIME       NULL,
    [CreatedUserId]  VARCHAR (15)   NOT NULL,
    [CreatedTime]    DATETIME       NOT NULL,
    [UpdatedUserId]  VARCHAR (15)   NOT NULL,
    [UpdatedTime]    DATETIME       NOT NULL,
    CONSTRAINT [PK_SYS_ProblemReport] PRIMARY KEY CLUSTERED ([Id] ASC)
);


GO
PRINT N'正在建立 資料表 [dbo].[SYS_ProjectVersion]...';


GO
CREATE TABLE [dbo].[SYS_ProjectVersion] (
    [P_ID]          INT            IDENTITY (1, 1) NOT NULL,
    [P_projectName] VARCHAR (256)  NOT NULL,
    [P_versionHash] VARCHAR (256)  NULL,
    [P_versionName] VARCHAR (256)  NULL,
    [P_CR_DateTime] DATETIME       NULL,
    [P_Cr_User]     NVARCHAR (50)  NULL,
    [P_Content]     NVARCHAR (MAX) NULL,
    CONSTRAINT [PK_Project_Version] PRIMARY KEY CLUSTERED ([P_ID] ASC)
);


GO
PRINT N'正在建立 資料表 [dbo].[SYS_RoleGroup]...';


GO
CREATE TABLE [dbo].[SYS_RoleGroup] (
    [System]        VARCHAR (30)  NOT NULL,
    [RoleGroupId]   VARCHAR (50)  NOT NULL,
    [Name]          NVARCHAR (50) NOT NULL,
    [IsAdmin]       BIT           NOT NULL,
    [CreatedUserId] VARCHAR (15)  NOT NULL,
    [CreatedTime]   DATETIME      NOT NULL,
    [UpdatedUserId] VARCHAR (15)  NOT NULL,
    [UpdatedTime]   DATETIME      NOT NULL,
    CONSTRAINT [PK_SYS_RoleGroup_1] PRIMARY KEY CLUSTERED ([System] ASC, [RoleGroupId] ASC)
);


GO
PRINT N'正在建立 資料表 [dbo].[SYS_RoleGroupPermission]...';


GO
CREATE TABLE [dbo].[SYS_RoleGroupPermission] (
    [System]      VARCHAR (30) NOT NULL,
    [RoleGroupId] VARCHAR (50) NOT NULL,
    [FuncId]      VARCHAR (50) NOT NULL,
    CONSTRAINT [PK_SYS_RoleGroupPermission_1] PRIMARY KEY CLUSTERED ([System] ASC, [RoleGroupId] ASC, [FuncId] ASC)
);


GO
PRINT N'正在建立 資料表 [dbo].[SYS_RoleGroupUser]...';


GO
CREATE TABLE [dbo].[SYS_RoleGroupUser] (
    [System]      VARCHAR (30) NOT NULL,
    [RoleGroupId] VARCHAR (50) NOT NULL,
    [UserId]      VARCHAR (15) NOT NULL,
    CONSTRAINT [PK_SYS_RoleGroupUser] PRIMARY KEY CLUSTERED ([System] ASC, [RoleGroupId] ASC, [UserId] ASC)
);


GO
PRINT N'正在建立 資料表 [dbo].[SYS_ScheduleHistory]...';


GO
CREATE TABLE [dbo].[SYS_ScheduleHistory] (
    [SS_ID]        BIGINT        IDENTITY (1, 1) NOT NULL,
    [SS_Name]      VARCHAR (50)  NOT NULL,
    [SS_StartTime] DATETIME      NOT NULL,
    [SS_EndTime]   DATETIME      NOT NULL,
    [SS_Error]     VARCHAR (MAX) NULL,
    CONSTRAINT [PK_SYS_ScheduleHistory] PRIMARY KEY CLUSTERED ([SS_ID] ASC)
);


GO
PRINT N'正在建立 資料表 [dbo].[SYS_SystemSetting]...';


GO
CREATE TABLE [dbo].[SYS_SystemSetting] (
    [Type]          VARCHAR (50)   NOT NULL,
    [Key]           VARCHAR (50)   NOT NULL,
    [Value]         NVARCHAR (100) NULL,
    [Name]          NVARCHAR (50)  NOT NULL,
    [IsEnabled]     BIT            NOT NULL,
    [CreatedUserId] VARCHAR (15)   NOT NULL,
    [CreatedTime]   DATETIME       NOT NULL,
    [UpdatedUserId] VARCHAR (15)   NOT NULL,
    [UpdatedTime]   DATETIME       NOT NULL,
    CONSTRAINT [PK_SYS_SystemSetting_1] PRIMARY KEY CLUSTERED ([Type] ASC, [Key] ASC)
);


GO
PRINT N'正在建立 資料表 [dbo].[SYS_Type]...';


GO
CREATE TABLE [dbo].[SYS_Type] (
    [Type]     VARCHAR (50)   NOT NULL,
    [TypeDesc] NVARCHAR (300) NULL,
    [CanEdit]  BIT            NOT NULL,
    CONSTRAINT [PK_SYS_Type] PRIMARY KEY CLUSTERED ([Type] ASC)
);


GO
PRINT N'正在建立 資料表 [dbo].[SYS_UserRecord]...';


GO
CREATE TABLE [dbo].[SYS_UserRecord] (
    [Id]          BIGINT         IDENTITY (1, 1) NOT NULL,
    [UserId]      VARCHAR (15)   NOT NULL,
    [DeptId]      VARCHAR (20)   NOT NULL,
    [GradeCode]   NVARCHAR (72)  NOT NULL,
    [RecordTime]  DATETIME       NOT NULL,
    [RecordEvent] NVARCHAR (50)  NOT NULL,
    [InputData]   NVARCHAR (MAX) NOT NULL,
    [IP]          VARCHAR (40)   NOT NULL,
    CONSTRAINT [PK_SYS_UserRecord] PRIMARY KEY CLUSTERED ([Id] ASC)
);


GO
PRINT N'正在建立 資料表 [dbo].[UserDept]...';


GO
CREATE TABLE [dbo].[UserDept] (
    [Id]            VARCHAR (20)  NOT NULL,
    [DeptName]      NVARCHAR (30) NOT NULL,
    [ParentDeptId]  VARCHAR (20)  NULL,
    [LeaderUserId]  VARCHAR (15)  NULL,
    [IsDisabled]    BIT           NOT NULL,
    [CreatedUserId] VARCHAR (15)  NOT NULL,
    [CreatedTime]   DATETIME      NOT NULL,
    [UpdatedUserId] VARCHAR (15)  NOT NULL,
    [UpdatedTime]   DATETIME      NOT NULL,
    CONSTRAINT [PK_UserDept] PRIMARY KEY CLUSTERED ([Id] ASC)
);


GO
PRINT N'正在建立 資料表 [dbo].[UserDeptPermission]...';


GO
CREATE TABLE [dbo].[UserDeptPermission] (
    [System] VARCHAR (30) NOT NULL,
    [DeptId] VARCHAR (20) NOT NULL,
    [FuncId] VARCHAR (50) NOT NULL,
    CONSTRAINT [PK_UserDeptPermission] PRIMARY KEY CLUSTERED ([System] ASC, [DeptId] ASC, [FuncId] ASC)
);


GO
PRINT N'正在建立 資料表 [dbo].[UserGrade]...';


GO
CREATE TABLE [dbo].[UserGrade] (
    [Id]            VARCHAR (20)  NOT NULL,
    [GradeName]     NVARCHAR (30) NOT NULL,
    [ParentGradeId] VARCHAR (20)  NULL,
    [IsDisabled]    BIT           NOT NULL,
    [CreatedUserId] VARCHAR (15)  NOT NULL,
    [CreatedTime]   DATETIME      NOT NULL,
    [UpdatedUserId] VARCHAR (15)  NOT NULL,
    [UpdatedTime]   DATETIME      NOT NULL,
    CONSTRAINT [PK_UserGrade] PRIMARY KEY CLUSTERED ([Id] ASC)
);


GO
PRINT N'正在建立 資料表 [dbo].[UserGradePermission]...';


GO
CREATE TABLE [dbo].[UserGradePermission] (
    [System]  VARCHAR (30) NOT NULL,
    [GradeId] VARCHAR (20) NOT NULL,
    [FuncId]  VARCHAR (50) NOT NULL,
    CONSTRAINT [PK_UserGradePermission] PRIMARY KEY CLUSTERED ([System] ASC, [GradeId] ASC, [FuncId] ASC)
);


GO
PRINT N'正在建立 資料表 [dbo].[UserInfo]...';


GO
CREATE TABLE [dbo].[UserInfo] (
    [Id]               VARCHAR (15)  NOT NULL,
    [Name]             NVARCHAR (15) NOT NULL,
    [Pw]               VARCHAR (255) NOT NULL,
    [IdNo]             VARCHAR (15)  NOT NULL,
    [GradeCode]        VARCHAR (20)  NULL,
    [DeptId]           VARCHAR (20)  NULL,
    [Email]            VARCHAR (254) NOT NULL,
    [IsEnabled]        BIT           NOT NULL,
    [IsDisabled]       BIT           NOT NULL,
    [EnabledTtime]     DATETIME      NULL,
    [LastLoginIP]      VARCHAR (50)  NOT NULL,
    [LastLoginTime]    DATETIME      NULL,
    [LastLogoutTime]   DATETIME      NULL,
    [LoginFailedCount] SMALLINT      NOT NULL,
    [CreatedUserId]    VARCHAR (15)  NOT NULL,
    [CreatedTime]      DATETIME      NOT NULL,
    [UpdatedUserId]    VARCHAR (15)  NOT NULL,
    [UpdatedTime]      DATETIME      NOT NULL,
    CONSTRAINT [PK_UserInfo] PRIMARY KEY CLUSTERED ([Id] ASC)
);


GO
PRINT N'正在建立 資料表 [dbo].[UserPasswordHistory]...';


GO
CREATE TABLE [dbo].[UserPasswordHistory] (
    [Id]            BIGINT        IDENTITY (1, 1) NOT NULL,
    [UserId]        VARCHAR (15)  NOT NULL,
    [Pw]            VARCHAR (255) NOT NULL,
    [CreatedUserId] VARCHAR (15)  NOT NULL,
    [CreatedTime]   DATETIME      NOT NULL,
    CONSTRAINT [PK_UserPasswordHistory] PRIMARY KEY CLUSTERED ([Id] ASC)
);


GO
PRINT N'正在建立 預設條件約束 [dbo].[DF_SYS_SystemSetting_IsEnabled]...';


GO
ALTER TABLE [dbo].[SYS_SystemSetting]
    ADD CONSTRAINT [DF_SYS_SystemSetting_IsEnabled] DEFAULT ((1)) FOR [IsEnabled];


GO
PRINT N'正在建立 預設條件約束 [dbo].[DF_UserInfo_IsDisabled]...';


GO
ALTER TABLE [dbo].[UserInfo]
    ADD CONSTRAINT [DF_UserInfo_IsDisabled] DEFAULT ((0)) FOR [IsDisabled];


GO
PRINT N'正在建立 外部索引鍵 [dbo].[FK_SYS_BulletinsAttach_SYS_Bulletins]...';


GO
ALTER TABLE [dbo].[SYS_BulletinsAttach] WITH NOCHECK
    ADD CONSTRAINT [FK_SYS_BulletinsAttach_SYS_Bulletins] FOREIGN KEY ([BL_Id]) REFERENCES [dbo].[SYS_Bulletins] ([BL_Id]) ON DELETE CASCADE ON UPDATE CASCADE;


GO
PRINT N'正在建立 外部索引鍵 [dbo].[FK_SYS_BulletinsAttachDownloadRecord_SYS_BulletinsAttach]...';


GO
ALTER TABLE [dbo].[SYS_BulletinsAttachDownloadRecord] WITH NOCHECK
    ADD CONSTRAINT [FK_SYS_BulletinsAttachDownloadRecord_SYS_BulletinsAttach] FOREIGN KEY ([BLA_Id]) REFERENCES [dbo].[SYS_BulletinsAttach] ([BLA_Id]) ON DELETE CASCADE ON UPDATE CASCADE;


GO
PRINT N'正在建立 外部索引鍵 [dbo].[FK_SYS_BulletinsClickRecord_SYS_Bulletins]...';


GO
ALTER TABLE [dbo].[SYS_BulletinsClickRecord] WITH NOCHECK
    ADD CONSTRAINT [FK_SYS_BulletinsClickRecord_SYS_Bulletins] FOREIGN KEY ([BL_Id]) REFERENCES [dbo].[SYS_Bulletins] ([BL_Id]) ON DELETE CASCADE ON UPDATE CASCADE;


GO
PRINT N'正在建立 外部索引鍵 [dbo].[FK_SYS_Code_SYS_Code]...';


GO
ALTER TABLE [dbo].[SYS_Code] WITH NOCHECK
    ADD CONSTRAINT [FK_SYS_Code_SYS_Code] FOREIGN KEY ([Type], [ParentCode]) REFERENCES [dbo].[SYS_Code] ([Type], [Code]);


GO
PRINT N'正在建立 外部索引鍵 [dbo].[FK_SYS_ProblemReport_UserDept]...';


GO
ALTER TABLE [dbo].[SYS_ProblemReport] WITH NOCHECK
    ADD CONSTRAINT [FK_SYS_ProblemReport_UserDept] FOREIGN KEY ([DeptId]) REFERENCES [dbo].[UserDept] ([Id]);


GO
PRINT N'正在建立 外部索引鍵 [dbo].[FK_SYS_ProblemReport_UserInfo_Reply]...';


GO
ALTER TABLE [dbo].[SYS_ProblemReport] WITH NOCHECK
    ADD CONSTRAINT [FK_SYS_ProblemReport_UserInfo_Reply] FOREIGN KEY ([ReplyUserId]) REFERENCES [dbo].[UserInfo] ([Id]);


GO
PRINT N'正在建立 外部索引鍵 [dbo].[FK_SYS_RoleGroupPermission_SYS_RoleGroup]...';


GO
ALTER TABLE [dbo].[SYS_RoleGroupPermission] WITH NOCHECK
    ADD CONSTRAINT [FK_SYS_RoleGroupPermission_SYS_RoleGroup] FOREIGN KEY ([System], [RoleGroupId]) REFERENCES [dbo].[SYS_RoleGroup] ([System], [RoleGroupId]) ON DELETE CASCADE;


GO
PRINT N'正在建立 外部索引鍵 [dbo].[FK_SYS_RolePermission_SYS_MenuFunc]...';


GO
ALTER TABLE [dbo].[SYS_RoleGroupPermission] WITH NOCHECK
    ADD CONSTRAINT [FK_SYS_RolePermission_SYS_MenuFunc] FOREIGN KEY ([System], [FuncId]) REFERENCES [dbo].[SYS_MenuFunc] ([System], [FuncId]) ON DELETE CASCADE;


GO
PRINT N'正在建立 外部索引鍵 [dbo].[FK_SYS_RoleGroupUser_SYS_RoleGroup]...';


GO
ALTER TABLE [dbo].[SYS_RoleGroupUser] WITH NOCHECK
    ADD CONSTRAINT [FK_SYS_RoleGroupUser_SYS_RoleGroup] FOREIGN KEY ([System], [RoleGroupId]) REFERENCES [dbo].[SYS_RoleGroup] ([System], [RoleGroupId]) ON DELETE CASCADE;


GO
PRINT N'正在建立 外部索引鍵 [dbo].[FK_SYS_RoleGroupUser_UserInfo]...';


GO
ALTER TABLE [dbo].[SYS_RoleGroupUser] WITH NOCHECK
    ADD CONSTRAINT [FK_SYS_RoleGroupUser_UserInfo] FOREIGN KEY ([UserId]) REFERENCES [dbo].[UserInfo] ([Id]);


GO
PRINT N'正在建立 外部索引鍵 [dbo].[FK_SYS_UserRecord_UserDept]...';


GO
ALTER TABLE [dbo].[SYS_UserRecord] WITH NOCHECK
    ADD CONSTRAINT [FK_SYS_UserRecord_UserDept] FOREIGN KEY ([DeptId]) REFERENCES [dbo].[UserDept] ([Id]);


GO
PRINT N'正在建立 外部索引鍵 [dbo].[FK_SYS_UserRecord_UserInfo]...';


GO
ALTER TABLE [dbo].[SYS_UserRecord] WITH NOCHECK
    ADD CONSTRAINT [FK_SYS_UserRecord_UserInfo] FOREIGN KEY ([UserId]) REFERENCES [dbo].[UserInfo] ([Id]);


GO
PRINT N'正在建立 外部索引鍵 [dbo].[FK_UserDept_UserDept]...';


GO
ALTER TABLE [dbo].[UserDept] WITH NOCHECK
    ADD CONSTRAINT [FK_UserDept_UserDept] FOREIGN KEY ([ParentDeptId]) REFERENCES [dbo].[UserDept] ([Id]);


GO
PRINT N'正在建立 外部索引鍵 [dbo].[FK_UserDept_UserInfo]...';


GO
ALTER TABLE [dbo].[UserDept] WITH NOCHECK
    ADD CONSTRAINT [FK_UserDept_UserInfo] FOREIGN KEY ([LeaderUserId]) REFERENCES [dbo].[UserInfo] ([Id]);


GO
PRINT N'正在建立 外部索引鍵 [dbo].[FK_UserDeptPermission_SYS_MenuFunc]...';


GO
ALTER TABLE [dbo].[UserDeptPermission] WITH NOCHECK
    ADD CONSTRAINT [FK_UserDeptPermission_SYS_MenuFunc] FOREIGN KEY ([System], [FuncId]) REFERENCES [dbo].[SYS_MenuFunc] ([System], [FuncId]);


GO
PRINT N'正在建立 外部索引鍵 [dbo].[FK_UserDeptPermission_UserDept]...';


GO
ALTER TABLE [dbo].[UserDeptPermission] WITH NOCHECK
    ADD CONSTRAINT [FK_UserDeptPermission_UserDept] FOREIGN KEY ([DeptId]) REFERENCES [dbo].[UserDept] ([Id]);


GO
PRINT N'正在建立 外部索引鍵 [dbo].[FK_UserGrade_UserGrade]...';


GO
ALTER TABLE [dbo].[UserGrade] WITH NOCHECK
    ADD CONSTRAINT [FK_UserGrade_UserGrade] FOREIGN KEY ([ParentGradeId]) REFERENCES [dbo].[UserGrade] ([Id]);


GO
PRINT N'正在建立 外部索引鍵 [dbo].[FK_UserGradePermission_SYS_MenuFunc]...';


GO
ALTER TABLE [dbo].[UserGradePermission] WITH NOCHECK
    ADD CONSTRAINT [FK_UserGradePermission_SYS_MenuFunc] FOREIGN KEY ([System], [FuncId]) REFERENCES [dbo].[SYS_MenuFunc] ([System], [FuncId]);


GO
PRINT N'正在建立 外部索引鍵 [dbo].[FK_UserGradePermission_UserGrade]...';


GO
ALTER TABLE [dbo].[UserGradePermission] WITH NOCHECK
    ADD CONSTRAINT [FK_UserGradePermission_UserGrade] FOREIGN KEY ([GradeId]) REFERENCES [dbo].[UserGrade] ([Id]);


GO
PRINT N'正在建立 外部索引鍵 [dbo].[FK_UserInfo_UserDept]...';


GO
ALTER TABLE [dbo].[UserInfo] WITH NOCHECK
    ADD CONSTRAINT [FK_UserInfo_UserDept] FOREIGN KEY ([DeptId]) REFERENCES [dbo].[UserDept] ([Id]);


GO
PRINT N'正在建立 外部索引鍵 [dbo].[FK_UserInfo_UserGrade]...';


GO
ALTER TABLE [dbo].[UserInfo] WITH NOCHECK
    ADD CONSTRAINT [FK_UserInfo_UserGrade] FOREIGN KEY ([GradeCode]) REFERENCES [dbo].[UserGrade] ([Id]);


GO
PRINT N'正在建立 外部索引鍵 [dbo].[FK_UserPasswordHistory_UserInfo]...';


GO
ALTER TABLE [dbo].[UserPasswordHistory] WITH NOCHECK
    ADD CONSTRAINT [FK_UserPasswordHistory_UserInfo] FOREIGN KEY ([UserId]) REFERENCES [dbo].[UserInfo] ([Id]);


GO
PRINT N'正在建立 擴充屬性 [dbo].[SYS_Bulletins].[BL_Id].[MS_Description]...';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'公告流水號', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'SYS_Bulletins', @level2type = N'COLUMN', @level2name = N'BL_Id';


GO
PRINT N'正在建立 擴充屬性 [dbo].[SYS_Bulletins].[BL_Title].[MS_Description]...';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'公告標題', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'SYS_Bulletins', @level2type = N'COLUMN', @level2name = N'BL_Title';


GO
PRINT N'正在建立 擴充屬性 [dbo].[SYS_Bulletins].[BL_Content].[MS_Description]...';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'公告內容', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'SYS_Bulletins', @level2type = N'COLUMN', @level2name = N'BL_Content';


GO
PRINT N'正在建立 擴充屬性 [dbo].[SYS_Bulletins].[BL_PostDateFrom].[MS_Description]...';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'啟用日期', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'SYS_Bulletins', @level2type = N'COLUMN', @level2name = N'BL_PostDateFrom';


GO
PRINT N'正在建立 擴充屬性 [dbo].[SYS_Bulletins].[BL_PostDateToxx].[MS_Description]...';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'結束日期', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'SYS_Bulletins', @level2type = N'COLUMN', @level2name = N'BL_PostDateToxx';


GO
PRINT N'正在建立 擴充屬性 [dbo].[SYS_Bulletins].[BL_IsTop].[MS_Description]...';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'是否置頂', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'SYS_Bulletins', @level2type = N'COLUMN', @level2name = N'BL_IsTop';


GO
PRINT N'正在建立 擴充屬性 [dbo].[SYS_Bulletins].[BL_IsEnable].[MS_Description]...';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'是否上架', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'SYS_Bulletins', @level2type = N'COLUMN', @level2name = N'BL_IsEnable';


GO
PRINT N'正在建立 擴充屬性 [dbo].[SYS_Bulletins].[BL_CrUser].[MS_Description]...';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'建立者', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'SYS_Bulletins', @level2type = N'COLUMN', @level2name = N'BL_CrUser';


GO
PRINT N'正在建立 擴充屬性 [dbo].[SYS_Bulletins].[BL_CrDatetime].[MS_Description]...';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'建立時間', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'SYS_Bulletins', @level2type = N'COLUMN', @level2name = N'BL_CrDatetime';


GO
PRINT N'正在建立 擴充屬性 [dbo].[SYS_Bulletins].[BL_UpUser].[MS_Description]...';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'更新者', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'SYS_Bulletins', @level2type = N'COLUMN', @level2name = N'BL_UpUser';


GO
PRINT N'正在建立 擴充屬性 [dbo].[SYS_Bulletins].[BL_UpDatetime].[MS_Description]...';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'更新時間', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'SYS_Bulletins', @level2type = N'COLUMN', @level2name = N'BL_UpDatetime';


GO
PRINT N'正在建立 擴充屬性 [dbo].[SYS_BulletinsAttach].[BLA_Id].[MS_Description]...';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'公告附檔流水號', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'SYS_BulletinsAttach', @level2type = N'COLUMN', @level2name = N'BLA_Id';


GO
PRINT N'正在建立 擴充屬性 [dbo].[SYS_BulletinsAttach].[BL_Id].[MS_Description]...';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'公告流水號', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'SYS_BulletinsAttach', @level2type = N'COLUMN', @level2name = N'BL_Id';


GO
PRINT N'正在建立 擴充屬性 [dbo].[SYS_BulletinsAttach].[BLA_OName].[MS_Description]...';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'檔案原名', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'SYS_BulletinsAttach', @level2type = N'COLUMN', @level2name = N'BLA_OName';


GO
PRINT N'正在建立 擴充屬性 [dbo].[SYS_BulletinsAttach].[BLA_File].[MS_Description]...';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'檔案', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'SYS_BulletinsAttach', @level2type = N'COLUMN', @level2name = N'BLA_File';


GO
PRINT N'正在建立 擴充屬性 [dbo].[SYS_BulletinsAttach].[BLA_FileType].[MS_Description]...';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'檔案類型', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'SYS_BulletinsAttach', @level2type = N'COLUMN', @level2name = N'BLA_FileType';


GO
PRINT N'正在建立 擴充屬性 [dbo].[SYS_BulletinsAttach].[BLA_CrUser].[MS_Description]...';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'建立者', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'SYS_BulletinsAttach', @level2type = N'COLUMN', @level2name = N'BLA_CrUser';


GO
PRINT N'正在建立 擴充屬性 [dbo].[SYS_BulletinsAttach].[BLA_CrDatetime].[MS_Description]...';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'建立時間', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'SYS_BulletinsAttach', @level2type = N'COLUMN', @level2name = N'BLA_CrDatetime';


GO
PRINT N'正在建立 擴充屬性 [dbo].[SYS_Code].[MS_Description]...';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'系統代碼資料', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'SYS_Code';


GO
PRINT N'正在建立 擴充屬性 [dbo].[SYS_Code].[Type].[MS_Description]...';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'類型', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'SYS_Code', @level2type = N'COLUMN', @level2name = N'Type';


GO
PRINT N'正在建立 擴充屬性 [dbo].[SYS_Code].[Code].[MS_Description]...';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'代碼', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'SYS_Code', @level2type = N'COLUMN', @level2name = N'Code';


GO
PRINT N'正在建立 擴充屬性 [dbo].[SYS_Code].[ParentCode].[MS_Description]...';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'上層代碼', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'SYS_Code', @level2type = N'COLUMN', @level2name = N'ParentCode';


GO
PRINT N'正在建立 擴充屬性 [dbo].[SYS_Code].[CodeDesc].[MS_Description]...';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'內容', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'SYS_Code', @level2type = N'COLUMN', @level2name = N'CodeDesc';


GO
PRINT N'正在建立 擴充屬性 [dbo].[SYS_Code].[IsDisabled].[MS_Description]...';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'是否作廢', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'SYS_Code', @level2type = N'COLUMN', @level2name = N'IsDisabled';


GO
PRINT N'正在建立 擴充屬性 [dbo].[SYS_Code].[CodeOrder].[MS_Description]...';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'排序', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'SYS_Code', @level2type = N'COLUMN', @level2name = N'CodeOrder';


GO
PRINT N'正在建立 擴充屬性 [dbo].[SYS_Code].[CreatedUserId].[MS_Description]...';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'建立者', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'SYS_Code', @level2type = N'COLUMN', @level2name = N'CreatedUserId';


GO
PRINT N'正在建立 擴充屬性 [dbo].[SYS_Code].[CreatedTime].[MS_Description]...';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'建立時間', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'SYS_Code', @level2type = N'COLUMN', @level2name = N'CreatedTime';


GO
PRINT N'正在建立 擴充屬性 [dbo].[SYS_Code].[UpdatedUserId].[MS_Description]...';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'新增者', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'SYS_Code', @level2type = N'COLUMN', @level2name = N'UpdatedUserId';


GO
PRINT N'正在建立 擴充屬性 [dbo].[SYS_Code].[UpdatedTime].[MS_Description]...';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'更新時間', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'SYS_Code', @level2type = N'COLUMN', @level2name = N'UpdatedTime';


GO
PRINT N'正在建立 擴充屬性 [dbo].[SYS_MenuFunc].[MS_Description]...';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'系統選單資料', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'SYS_MenuFunc';


GO
PRINT N'正在建立 擴充屬性 [dbo].[SYS_MenuFunc].[System].[MS_Description]...';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'系統名稱', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'SYS_MenuFunc', @level2type = N'COLUMN', @level2name = N'System';


GO
PRINT N'正在建立 擴充屬性 [dbo].[SYS_MenuFunc].[FuncId].[MS_Description]...';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'系統項目識別編號', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'SYS_MenuFunc', @level2type = N'COLUMN', @level2name = N'FuncId';


GO
PRINT N'正在建立 擴充屬性 [dbo].[SYS_MenuFunc].[FuncName].[MS_Description]...';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'系統項目名稱', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'SYS_MenuFunc', @level2type = N'COLUMN', @level2name = N'FuncName';


GO
PRINT N'正在建立 擴充屬性 [dbo].[SYS_MenuFunc].[FuncOrder].[MS_Description]...';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'項目排序', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'SYS_MenuFunc', @level2type = N'COLUMN', @level2name = N'FuncOrder';


GO
PRINT N'正在建立 擴充屬性 [dbo].[SYS_MenuFunc].[ParentFuncId].[MS_Description]...';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'項目路徑', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'SYS_MenuFunc', @level2type = N'COLUMN', @level2name = N'ParentFuncId';


GO
PRINT N'正在建立 擴充屬性 [dbo].[SYS_ProblemReport].[MS_Description]...';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'平台問題回報資料', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'SYS_ProblemReport';


GO
PRINT N'正在建立 擴充屬性 [dbo].[SYS_ProblemReport].[Id].[MS_Description]...';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'流水號', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'SYS_ProblemReport', @level2type = N'COLUMN', @level2name = N'Id';


GO
PRINT N'正在建立 擴充屬性 [dbo].[SYS_ProblemReport].[DeptId].[MS_Description]...';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'單位代碼', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'SYS_ProblemReport', @level2type = N'COLUMN', @level2name = N'DeptId';


GO
PRINT N'正在建立 擴充屬性 [dbo].[SYS_ProblemReport].[Menu].[MS_Description]...';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'功能頁籤', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'SYS_ProblemReport', @level2type = N'COLUMN', @level2name = N'Menu';


GO
PRINT N'正在建立 擴充屬性 [dbo].[SYS_ProblemReport].[Subject].[MS_Description]...';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'問題主旨', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'SYS_ProblemReport', @level2type = N'COLUMN', @level2name = N'Subject';


GO
PRINT N'正在建立 擴充屬性 [dbo].[SYS_ProblemReport].[ProblemContent].[MS_Description]...';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'問題內容', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'SYS_ProblemReport', @level2type = N'COLUMN', @level2name = N'ProblemContent';


GO
PRINT N'正在建立 擴充屬性 [dbo].[SYS_ProblemReport].[ProcessStatus].[MS_Description]...';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'處理狀態', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'SYS_ProblemReport', @level2type = N'COLUMN', @level2name = N'ProcessStatus';


GO
PRINT N'正在建立 擴充屬性 [dbo].[SYS_ProblemReport].[ProblemType].[MS_Description]...';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'問題類型', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'SYS_ProblemReport', @level2type = N'COLUMN', @level2name = N'ProblemType';


GO
PRINT N'正在建立 擴充屬性 [dbo].[SYS_ProblemReport].[ReplyContent].[MS_Description]...';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'處理回報', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'SYS_ProblemReport', @level2type = N'COLUMN', @level2name = N'ReplyContent';


GO
PRINT N'正在建立 擴充屬性 [dbo].[SYS_ProblemReport].[ReplyUserId].[MS_Description]...';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'處理人員', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'SYS_ProblemReport', @level2type = N'COLUMN', @level2name = N'ReplyUserId';


GO
PRINT N'正在建立 擴充屬性 [dbo].[SYS_ProblemReport].[CreatedUserId].[MS_Description]...';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'建立者', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'SYS_ProblemReport', @level2type = N'COLUMN', @level2name = N'CreatedUserId';


GO
PRINT N'正在建立 擴充屬性 [dbo].[SYS_ProblemReport].[CreatedTime].[MS_Description]...';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'建立時間', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'SYS_ProblemReport', @level2type = N'COLUMN', @level2name = N'CreatedTime';


GO
PRINT N'正在建立 擴充屬性 [dbo].[SYS_ProblemReport].[UpdatedUserId].[MS_Description]...';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'更新者', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'SYS_ProblemReport', @level2type = N'COLUMN', @level2name = N'UpdatedUserId';


GO
PRINT N'正在建立 擴充屬性 [dbo].[SYS_ProblemReport].[UpdatedTime].[MS_Description]...';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'更新時間', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'SYS_ProblemReport', @level2type = N'COLUMN', @level2name = N'UpdatedTime';


GO
PRINT N'正在建立 擴充屬性 [dbo].[SYS_ProjectVersion].[P_projectName].[MS_Description]...';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'專案代號', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'SYS_ProjectVersion', @level2type = N'COLUMN', @level2name = N'P_projectName';


GO
PRINT N'正在建立 擴充屬性 [dbo].[SYS_ProjectVersion].[P_versionHash].[MS_Description]...';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'git唯一值', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'SYS_ProjectVersion', @level2type = N'COLUMN', @level2name = N'P_versionHash';


GO
PRINT N'正在建立 擴充屬性 [dbo].[SYS_ProjectVersion].[P_versionName].[MS_Description]...';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'版號', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'SYS_ProjectVersion', @level2type = N'COLUMN', @level2name = N'P_versionName';


GO
PRINT N'正在建立 擴充屬性 [dbo].[SYS_ProjectVersion].[P_CR_DateTime].[MS_Description]...';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'版更時間', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'SYS_ProjectVersion', @level2type = N'COLUMN', @level2name = N'P_CR_DateTime';


GO
PRINT N'正在建立 擴充屬性 [dbo].[SYS_ProjectVersion].[P_Cr_User].[MS_Description]...';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'上版人員', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'SYS_ProjectVersion', @level2type = N'COLUMN', @level2name = N'P_Cr_User';


GO
PRINT N'正在建立 擴充屬性 [dbo].[SYS_ProjectVersion].[P_Content].[MS_Description]...';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'更新內容', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'SYS_ProjectVersion', @level2type = N'COLUMN', @level2name = N'P_Content';


GO
PRINT N'正在建立 擴充屬性 [dbo].[SYS_RoleGroup].[MS_Description]...';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'角色群組資料', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'SYS_RoleGroup';


GO
PRINT N'正在建立 擴充屬性 [dbo].[SYS_RoleGroup].[System].[MS_Description]...';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'系統名稱', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'SYS_RoleGroup', @level2type = N'COLUMN', @level2name = N'System';


GO
PRINT N'正在建立 擴充屬性 [dbo].[SYS_RoleGroup].[RoleGroupId].[MS_Description]...';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'角色群組識別編號', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'SYS_RoleGroup', @level2type = N'COLUMN', @level2name = N'RoleGroupId';


GO
PRINT N'正在建立 擴充屬性 [dbo].[SYS_RoleGroup].[Name].[MS_Description]...';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'角色群組名稱', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'SYS_RoleGroup', @level2type = N'COLUMN', @level2name = N'Name';


GO
PRINT N'正在建立 擴充屬性 [dbo].[SYS_RoleGroup].[IsAdmin].[MS_Description]...';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'是否為管理者', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'SYS_RoleGroup', @level2type = N'COLUMN', @level2name = N'IsAdmin';


GO
PRINT N'正在建立 擴充屬性 [dbo].[SYS_RoleGroup].[CreatedUserId].[MS_Description]...';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'建立時間', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'SYS_RoleGroup', @level2type = N'COLUMN', @level2name = N'CreatedUserId';


GO
PRINT N'正在建立 擴充屬性 [dbo].[SYS_RoleGroup].[CreatedTime].[MS_Description]...';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'建立者', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'SYS_RoleGroup', @level2type = N'COLUMN', @level2name = N'CreatedTime';


GO
PRINT N'正在建立 擴充屬性 [dbo].[SYS_RoleGroup].[UpdatedUserId].[MS_Description]...';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'更新時間', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'SYS_RoleGroup', @level2type = N'COLUMN', @level2name = N'UpdatedUserId';


GO
PRINT N'正在建立 擴充屬性 [dbo].[SYS_RoleGroup].[UpdatedTime].[MS_Description]...';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'更新者', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'SYS_RoleGroup', @level2type = N'COLUMN', @level2name = N'UpdatedTime';


GO
PRINT N'正在建立 擴充屬性 [dbo].[SYS_RoleGroupPermission].[System].[MS_Description]...';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'系統名稱', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'SYS_RoleGroupPermission', @level2type = N'COLUMN', @level2name = N'System';


GO
PRINT N'正在建立 擴充屬性 [dbo].[SYS_RoleGroupPermission].[RoleGroupId].[MS_Description]...';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'角色群組識別編號', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'SYS_RoleGroupPermission', @level2type = N'COLUMN', @level2name = N'RoleGroupId';


GO
PRINT N'正在建立 擴充屬性 [dbo].[SYS_RoleGroupPermission].[FuncId].[MS_Description]...';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'系統項目識別編號', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'SYS_RoleGroupPermission', @level2type = N'COLUMN', @level2name = N'FuncId';


GO
PRINT N'正在建立 擴充屬性 [dbo].[SYS_RoleGroupUser].[System].[MS_Description]...';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'系統名稱', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'SYS_RoleGroupUser', @level2type = N'COLUMN', @level2name = N'System';


GO
PRINT N'正在建立 擴充屬性 [dbo].[SYS_RoleGroupUser].[RoleGroupId].[MS_Description]...';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'角色群組', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'SYS_RoleGroupUser', @level2type = N'COLUMN', @level2name = N'RoleGroupId';


GO
PRINT N'正在建立 擴充屬性 [dbo].[SYS_RoleGroupUser].[UserId].[MS_Description]...';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'權限群組人員', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'SYS_RoleGroupUser', @level2type = N'COLUMN', @level2name = N'UserId';


GO
PRINT N'正在建立 擴充屬性 [dbo].[SYS_ScheduleHistory].[SS_Name].[MS_Description]...';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'排程名稱', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'SYS_ScheduleHistory', @level2type = N'COLUMN', @level2name = N'SS_Name';


GO
PRINT N'正在建立 擴充屬性 [dbo].[SYS_ScheduleHistory].[SS_StartTime].[MS_Description]...';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'排程開始時間', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'SYS_ScheduleHistory', @level2type = N'COLUMN', @level2name = N'SS_StartTime';


GO
PRINT N'正在建立 擴充屬性 [dbo].[SYS_ScheduleHistory].[SS_EndTime].[MS_Description]...';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'排程結束時間', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'SYS_ScheduleHistory', @level2type = N'COLUMN', @level2name = N'SS_EndTime';


GO
PRINT N'正在建立 擴充屬性 [dbo].[SYS_ScheduleHistory].[SS_Error].[MS_Description]...';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'錯誤訊息', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'SYS_ScheduleHistory', @level2type = N'COLUMN', @level2name = N'SS_Error';


GO
PRINT N'正在建立 擴充屬性 [dbo].[SYS_SystemSetting].[MS_Description]...';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'系統設定資料', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'SYS_SystemSetting';


GO
PRINT N'正在建立 擴充屬性 [dbo].[SYS_SystemSetting].[Type].[MS_Description]...';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'類別', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'SYS_SystemSetting', @level2type = N'COLUMN', @level2name = N'Type';


GO
PRINT N'正在建立 擴充屬性 [dbo].[SYS_SystemSetting].[Key].[MS_Description]...';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'鍵', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'SYS_SystemSetting', @level2type = N'COLUMN', @level2name = N'Key';


GO
PRINT N'正在建立 擴充屬性 [dbo].[SYS_SystemSetting].[Value].[MS_Description]...';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'值', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'SYS_SystemSetting', @level2type = N'COLUMN', @level2name = N'Value';


GO
PRINT N'正在建立 擴充屬性 [dbo].[SYS_SystemSetting].[Name].[MS_Description]...';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'詳細名稱', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'SYS_SystemSetting', @level2type = N'COLUMN', @level2name = N'Name';


GO
PRINT N'正在建立 擴充屬性 [dbo].[SYS_SystemSetting].[IsEnabled].[MS_Description]...';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'是否啟用', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'SYS_SystemSetting', @level2type = N'COLUMN', @level2name = N'IsEnabled';


GO
PRINT N'正在建立 擴充屬性 [dbo].[SYS_SystemSetting].[CreatedUserId].[MS_Description]...';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'建立者', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'SYS_SystemSetting', @level2type = N'COLUMN', @level2name = N'CreatedUserId';


GO
PRINT N'正在建立 擴充屬性 [dbo].[SYS_SystemSetting].[CreatedTime].[MS_Description]...';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'建立時間', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'SYS_SystemSetting', @level2type = N'COLUMN', @level2name = N'CreatedTime';


GO
PRINT N'正在建立 擴充屬性 [dbo].[SYS_SystemSetting].[UpdatedUserId].[MS_Description]...';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'更新者', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'SYS_SystemSetting', @level2type = N'COLUMN', @level2name = N'UpdatedUserId';


GO
PRINT N'正在建立 擴充屬性 [dbo].[SYS_SystemSetting].[UpdatedTime].[MS_Description]...';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'更新時間', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'SYS_SystemSetting', @level2type = N'COLUMN', @level2name = N'UpdatedTime';


GO
PRINT N'正在建立 擴充屬性 [dbo].[SYS_Type].[MS_Description]...';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'代碼類型資料', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'SYS_Type';


GO
PRINT N'正在建立 擴充屬性 [dbo].[SYS_Type].[Type].[MS_Description]...';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'類型', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'SYS_Type', @level2type = N'COLUMN', @level2name = N'Type';


GO
PRINT N'正在建立 擴充屬性 [dbo].[SYS_Type].[TypeDesc].[MS_Description]...';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'描述', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'SYS_Type', @level2type = N'COLUMN', @level2name = N'TypeDesc';


GO
PRINT N'正在建立 擴充屬性 [dbo].[SYS_Type].[CanEdit].[MS_Description]...';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'是否可編輯', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'SYS_Type', @level2type = N'COLUMN', @level2name = N'CanEdit';


GO
PRINT N'正在建立 擴充屬性 [dbo].[SYS_UserRecord].[MS_Description]...';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'使用者操作紀錄', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'SYS_UserRecord';


GO
PRINT N'正在建立 擴充屬性 [dbo].[SYS_UserRecord].[Id].[MS_Description]...';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'流水號', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'SYS_UserRecord', @level2type = N'COLUMN', @level2name = N'Id';


GO
PRINT N'正在建立 擴充屬性 [dbo].[SYS_UserRecord].[UserId].[MS_Description]...';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'使用者', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'SYS_UserRecord', @level2type = N'COLUMN', @level2name = N'UserId';


GO
PRINT N'正在建立 擴充屬性 [dbo].[SYS_UserRecord].[DeptId].[MS_Description]...';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'使用者部門', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'SYS_UserRecord', @level2type = N'COLUMN', @level2name = N'DeptId';


GO
PRINT N'正在建立 擴充屬性 [dbo].[SYS_UserRecord].[GradeCode].[MS_Description]...';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'職位', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'SYS_UserRecord', @level2type = N'COLUMN', @level2name = N'GradeCode';


GO
PRINT N'正在建立 擴充屬性 [dbo].[SYS_UserRecord].[RecordTime].[MS_Description]...';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'紀錄時間', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'SYS_UserRecord', @level2type = N'COLUMN', @level2name = N'RecordTime';


GO
PRINT N'正在建立 擴充屬性 [dbo].[SYS_UserRecord].[RecordEvent].[MS_Description]...';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'紀錄API', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'SYS_UserRecord', @level2type = N'COLUMN', @level2name = N'RecordEvent';


GO
PRINT N'正在建立 擴充屬性 [dbo].[SYS_UserRecord].[InputData].[MS_Description]...';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'輸入資料', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'SYS_UserRecord', @level2type = N'COLUMN', @level2name = N'InputData';


GO
PRINT N'正在建立 擴充屬性 [dbo].[SYS_UserRecord].[IP].[MS_Description]...';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'IP', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'SYS_UserRecord', @level2type = N'COLUMN', @level2name = N'IP';


GO
PRINT N'正在建立 擴充屬性 [dbo].[UserDept].[MS_Description]...';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'組織部門資料', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'UserDept';


GO
PRINT N'正在建立 擴充屬性 [dbo].[UserDept].[Id].[MS_Description]...';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'部門代碼', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'UserDept', @level2type = N'COLUMN', @level2name = N'Id';


GO
PRINT N'正在建立 擴充屬性 [dbo].[UserDept].[DeptName].[MS_Description]...';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'部門名稱', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'UserDept', @level2type = N'COLUMN', @level2name = N'DeptName';


GO
PRINT N'正在建立 擴充屬性 [dbo].[UserDept].[ParentDeptId].[MS_Description]...';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'上層部門代碼', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'UserDept', @level2type = N'COLUMN', @level2name = N'ParentDeptId';


GO
PRINT N'正在建立 擴充屬性 [dbo].[UserDept].[LeaderUserId].[MS_Description]...';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'部門主管員工編號', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'UserDept', @level2type = N'COLUMN', @level2name = N'LeaderUserId';


GO
PRINT N'正在建立 擴充屬性 [dbo].[UserDept].[IsDisabled].[MS_Description]...';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'是否刪除', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'UserDept', @level2type = N'COLUMN', @level2name = N'IsDisabled';


GO
PRINT N'正在建立 擴充屬性 [dbo].[UserDept].[CreatedUserId].[MS_Description]...';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'建立者', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'UserDept', @level2type = N'COLUMN', @level2name = N'CreatedUserId';


GO
PRINT N'正在建立 擴充屬性 [dbo].[UserDept].[CreatedTime].[MS_Description]...';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'建立時間', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'UserDept', @level2type = N'COLUMN', @level2name = N'CreatedTime';


GO
PRINT N'正在建立 擴充屬性 [dbo].[UserDept].[UpdatedUserId].[MS_Description]...';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'更新者', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'UserDept', @level2type = N'COLUMN', @level2name = N'UpdatedUserId';


GO
PRINT N'正在建立 擴充屬性 [dbo].[UserDept].[UpdatedTime].[MS_Description]...';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'更新時間', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'UserDept', @level2type = N'COLUMN', @level2name = N'UpdatedTime';


GO
PRINT N'正在建立 擴充屬性 [dbo].[UserDeptPermission].[System].[MS_Description]...';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'系統名稱', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'UserDeptPermission', @level2type = N'COLUMN', @level2name = N'System';


GO
PRINT N'正在建立 擴充屬性 [dbo].[UserDeptPermission].[DeptId].[MS_Description]...';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'部門識別編號', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'UserDeptPermission', @level2type = N'COLUMN', @level2name = N'DeptId';


GO
PRINT N'正在建立 擴充屬性 [dbo].[UserDeptPermission].[FuncId].[MS_Description]...';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'系統項目識別編號', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'UserDeptPermission', @level2type = N'COLUMN', @level2name = N'FuncId';


GO
PRINT N'正在建立 擴充屬性 [dbo].[UserGrade].[Id].[MS_Description]...';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'職稱代碼', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'UserGrade', @level2type = N'COLUMN', @level2name = N'Id';


GO
PRINT N'正在建立 擴充屬性 [dbo].[UserGrade].[GradeName].[MS_Description]...';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'職稱', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'UserGrade', @level2type = N'COLUMN', @level2name = N'GradeName';


GO
PRINT N'正在建立 擴充屬性 [dbo].[UserGrade].[ParentGradeId].[MS_Description]...';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'上層職稱代碼', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'UserGrade', @level2type = N'COLUMN', @level2name = N'ParentGradeId';


GO
PRINT N'正在建立 擴充屬性 [dbo].[UserGrade].[IsDisabled].[MS_Description]...';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'是否刪除', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'UserGrade', @level2type = N'COLUMN', @level2name = N'IsDisabled';


GO
PRINT N'正在建立 擴充屬性 [dbo].[UserGrade].[CreatedUserId].[MS_Description]...';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'建立者', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'UserGrade', @level2type = N'COLUMN', @level2name = N'CreatedUserId';


GO
PRINT N'正在建立 擴充屬性 [dbo].[UserGrade].[CreatedTime].[MS_Description]...';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'建立時間', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'UserGrade', @level2type = N'COLUMN', @level2name = N'CreatedTime';


GO
PRINT N'正在建立 擴充屬性 [dbo].[UserGrade].[UpdatedUserId].[MS_Description]...';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'更新者', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'UserGrade', @level2type = N'COLUMN', @level2name = N'UpdatedUserId';


GO
PRINT N'正在建立 擴充屬性 [dbo].[UserGrade].[UpdatedTime].[MS_Description]...';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'更新時間', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'UserGrade', @level2type = N'COLUMN', @level2name = N'UpdatedTime';


GO
PRINT N'正在建立 擴充屬性 [dbo].[UserGradePermission].[System].[MS_Description]...';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'系統名稱', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'UserGradePermission', @level2type = N'COLUMN', @level2name = N'System';


GO
PRINT N'正在建立 擴充屬性 [dbo].[UserGradePermission].[GradeId].[MS_Description]...';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'職稱識別編號', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'UserGradePermission', @level2type = N'COLUMN', @level2name = N'GradeId';


GO
PRINT N'正在建立 擴充屬性 [dbo].[UserGradePermission].[FuncId].[MS_Description]...';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'系統項目識別編號', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'UserGradePermission', @level2type = N'COLUMN', @level2name = N'FuncId';


GO
PRINT N'正在建立 擴充屬性 [dbo].[UserInfo].[MS_Description]...';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'人員基本資料', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'UserInfo';


GO
PRINT N'正在建立 擴充屬性 [dbo].[UserInfo].[Id].[MS_Description]...';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'員工編號', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'UserInfo', @level2type = N'COLUMN', @level2name = N'Id';


GO
PRINT N'正在建立 擴充屬性 [dbo].[UserInfo].[Name].[MS_Description]...';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'人員姓名', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'UserInfo', @level2type = N'COLUMN', @level2name = N'Name';


GO
PRINT N'正在建立 擴充屬性 [dbo].[UserInfo].[Pw].[MS_Description]...';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'密碼', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'UserInfo', @level2type = N'COLUMN', @level2name = N'Pw';


GO
PRINT N'正在建立 擴充屬性 [dbo].[UserInfo].[IdNo].[MS_Description]...';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'身分證字號', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'UserInfo', @level2type = N'COLUMN', @level2name = N'IdNo';


GO
PRINT N'正在建立 擴充屬性 [dbo].[UserInfo].[GradeCode].[MS_Description]...';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'職位', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'UserInfo', @level2type = N'COLUMN', @level2name = N'GradeCode';


GO
PRINT N'正在建立 擴充屬性 [dbo].[UserInfo].[DeptId].[MS_Description]...';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'部門', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'UserInfo', @level2type = N'COLUMN', @level2name = N'DeptId';


GO
PRINT N'正在建立 擴充屬性 [dbo].[UserInfo].[Email].[MS_Description]...';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'電子郵件', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'UserInfo', @level2type = N'COLUMN', @level2name = N'Email';


GO
PRINT N'正在建立 擴充屬性 [dbo].[UserInfo].[IsEnabled].[MS_Description]...';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'是否啟用', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'UserInfo', @level2type = N'COLUMN', @level2name = N'IsEnabled';


GO
PRINT N'正在建立 擴充屬性 [dbo].[UserInfo].[IsDisabled].[MS_Description]...';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'是否刪除', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'UserInfo', @level2type = N'COLUMN', @level2name = N'IsDisabled';


GO
PRINT N'正在建立 擴充屬性 [dbo].[UserInfo].[EnabledTtime].[MS_Description]...';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'啟用時間', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'UserInfo', @level2type = N'COLUMN', @level2name = N'EnabledTtime';


GO
PRINT N'正在建立 擴充屬性 [dbo].[UserInfo].[LastLoginIP].[MS_Description]...';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'最後一次登入IP', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'UserInfo', @level2type = N'COLUMN', @level2name = N'LastLoginIP';


GO
PRINT N'正在建立 擴充屬性 [dbo].[UserInfo].[LastLoginTime].[MS_Description]...';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'最後登入時間', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'UserInfo', @level2type = N'COLUMN', @level2name = N'LastLoginTime';


GO
PRINT N'正在建立 擴充屬性 [dbo].[UserInfo].[LastLogoutTime].[MS_Description]...';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'最後登出時間', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'UserInfo', @level2type = N'COLUMN', @level2name = N'LastLogoutTime';


GO
PRINT N'正在建立 擴充屬性 [dbo].[UserInfo].[LoginFailedCount].[MS_Description]...';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'登入失敗次數加總', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'UserInfo', @level2type = N'COLUMN', @level2name = N'LoginFailedCount';


GO
PRINT N'正在建立 擴充屬性 [dbo].[UserInfo].[CreatedUserId].[MS_Description]...';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'建立者', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'UserInfo', @level2type = N'COLUMN', @level2name = N'CreatedUserId';


GO
PRINT N'正在建立 擴充屬性 [dbo].[UserInfo].[CreatedTime].[MS_Description]...';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'建立時間', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'UserInfo', @level2type = N'COLUMN', @level2name = N'CreatedTime';


GO
PRINT N'正在建立 擴充屬性 [dbo].[UserInfo].[UpdatedUserId].[MS_Description]...';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'更新者', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'UserInfo', @level2type = N'COLUMN', @level2name = N'UpdatedUserId';


GO
PRINT N'正在建立 擴充屬性 [dbo].[UserInfo].[UpdatedTime].[MS_Description]...';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'更新時間', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'UserInfo', @level2type = N'COLUMN', @level2name = N'UpdatedTime';


GO
PRINT N'正在建立 擴充屬性 [dbo].[UserPasswordHistory].[MS_Description]...';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'密碼歷史資料', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'UserPasswordHistory';


GO
PRINT N'正在建立 擴充屬性 [dbo].[UserPasswordHistory].[Id].[MS_Description]...';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'流水號', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'UserPasswordHistory', @level2type = N'COLUMN', @level2name = N'Id';


GO
PRINT N'正在建立 擴充屬性 [dbo].[UserPasswordHistory].[UserId].[MS_Description]...';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'員工編號', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'UserPasswordHistory', @level2type = N'COLUMN', @level2name = N'UserId';


GO
PRINT N'正在建立 擴充屬性 [dbo].[UserPasswordHistory].[Pw].[MS_Description]...';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'密碼', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'UserPasswordHistory', @level2type = N'COLUMN', @level2name = N'Pw';


GO
PRINT N'正在建立 擴充屬性 [dbo].[UserPasswordHistory].[CreatedUserId].[MS_Description]...';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'建立者', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'UserPasswordHistory', @level2type = N'COLUMN', @level2name = N'CreatedUserId';


GO
PRINT N'正在建立 擴充屬性 [dbo].[UserPasswordHistory].[CreatedTime].[MS_Description]...';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'建立時間', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'UserPasswordHistory', @level2type = N'COLUMN', @level2name = N'CreatedTime';


GO
PRINT N'正在針對新建立的條件約束檢查現有資料';

GO
ALTER TABLE [dbo].[SYS_BulletinsAttach] WITH CHECK CHECK CONSTRAINT [FK_SYS_BulletinsAttach_SYS_Bulletins];

ALTER TABLE [dbo].[SYS_BulletinsAttachDownloadRecord] WITH CHECK CHECK CONSTRAINT [FK_SYS_BulletinsAttachDownloadRecord_SYS_BulletinsAttach];

ALTER TABLE [dbo].[SYS_BulletinsClickRecord] WITH CHECK CHECK CONSTRAINT [FK_SYS_BulletinsClickRecord_SYS_Bulletins];

ALTER TABLE [dbo].[SYS_Code] WITH CHECK CHECK CONSTRAINT [FK_SYS_Code_SYS_Code];

ALTER TABLE [dbo].[SYS_ProblemReport] WITH CHECK CHECK CONSTRAINT [FK_SYS_ProblemReport_UserDept];

ALTER TABLE [dbo].[SYS_ProblemReport] WITH CHECK CHECK CONSTRAINT [FK_SYS_ProblemReport_UserInfo_Reply];

ALTER TABLE [dbo].[SYS_RoleGroupPermission] WITH CHECK CHECK CONSTRAINT [FK_SYS_RoleGroupPermission_SYS_RoleGroup];

ALTER TABLE [dbo].[SYS_RoleGroupPermission] WITH CHECK CHECK CONSTRAINT [FK_SYS_RolePermission_SYS_MenuFunc];

ALTER TABLE [dbo].[SYS_RoleGroupUser] WITH CHECK CHECK CONSTRAINT [FK_SYS_RoleGroupUser_SYS_RoleGroup];

ALTER TABLE [dbo].[SYS_RoleGroupUser] WITH CHECK CHECK CONSTRAINT [FK_SYS_RoleGroupUser_UserInfo];

ALTER TABLE [dbo].[SYS_UserRecord] WITH CHECK CHECK CONSTRAINT [FK_SYS_UserRecord_UserDept];

ALTER TABLE [dbo].[SYS_UserRecord] WITH CHECK CHECK CONSTRAINT [FK_SYS_UserRecord_UserInfo];

ALTER TABLE [dbo].[UserDept] WITH CHECK CHECK CONSTRAINT [FK_UserDept_UserDept];

ALTER TABLE [dbo].[UserDept] WITH CHECK CHECK CONSTRAINT [FK_UserDept_UserInfo];

ALTER TABLE [dbo].[UserDeptPermission] WITH CHECK CHECK CONSTRAINT [FK_UserDeptPermission_SYS_MenuFunc];

ALTER TABLE [dbo].[UserDeptPermission] WITH CHECK CHECK CONSTRAINT [FK_UserDeptPermission_UserDept];

ALTER TABLE [dbo].[UserGrade] WITH CHECK CHECK CONSTRAINT [FK_UserGrade_UserGrade];

ALTER TABLE [dbo].[UserGradePermission] WITH CHECK CHECK CONSTRAINT [FK_UserGradePermission_SYS_MenuFunc];

ALTER TABLE [dbo].[UserGradePermission] WITH CHECK CHECK CONSTRAINT [FK_UserGradePermission_UserGrade];

ALTER TABLE [dbo].[UserInfo] WITH CHECK CHECK CONSTRAINT [FK_UserInfo_UserDept];

ALTER TABLE [dbo].[UserInfo] WITH CHECK CHECK CONSTRAINT [FK_UserInfo_UserGrade];

ALTER TABLE [dbo].[UserPasswordHistory] WITH CHECK CHECK CONSTRAINT [FK_UserPasswordHistory_UserInfo];


GO
PRINT N'更新完成。';