using System;
using System.ComponentModel.DataAnnotations;
using AlifeApi.BusinessRules.Infrastructure;

namespace AlifeApi.BusinessRules.PaymentRecordModels
{
    /// <summary>
    /// 收款記錄列表查詢輸入模型
    /// </summary>
    public class PaymentRecordQueryInput : PagedListInput
    {
        [Required(ErrorMessage = "必須提供訂單ID")]
        public int OrderId { get; set; }

        /// <summary>
        /// 收款日期起
        /// </summary>
        public DateOnly? PaymentDateStart { get; set; }

        /// <summary>
        /// 收款日期迄
        /// </summary>
        public DateOnly? PaymentDateEnd { get; set; }

        /// <summary>
        /// 款別
        /// </summary>
        public string? PaymentType { get; set; }
    }
} 
