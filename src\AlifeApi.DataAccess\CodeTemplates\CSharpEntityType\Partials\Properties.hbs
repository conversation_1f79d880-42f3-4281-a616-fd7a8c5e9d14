﻿{{#each properties}}
{{#if property-comment}}
        /// <summary>
{{property-comment}}
        /// </summary>
{{/if}}
{{#each property-annotations}}
        {{{property-annotation}}}
{{/each}}
        public {{property-type}} {{property-name}} { get; set; }{{#if nullable-reference-types }}{{#unless property-isnullable}} = null!;{{/unless}}{{/if}}

{{/each}}
{{#if nav-properties}}
{{#each nav-properties}}
{{#each nav-property-annotations}}
        {{{nav-property-annotation}}}
{{/each}}
{{#if nav-property-collection}}
        public virtual ICollection<{{nav-property-type}}> {{nav-property-name}} { get; set; }

{{else}}
        public virtual {{nav-property-type}} {{nav-property-name}} { get; set; }{{#if nullable-reference-types}}{{#unless nav-property-isnullable}} = null!;{{/unless}}{{/if}}

{{/if}}
{{/each}}
{{/if}}