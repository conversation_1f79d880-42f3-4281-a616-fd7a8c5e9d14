﻿CREATE TABLE [dbo].[SYS_ScheduleHistory] (
    [SS_ID]        BIGINT        IDENTITY (1, 1) NOT NULL,
    [SS_Name]      VARCHAR (50)  NOT NULL,
    [SS_StartTime] DATETIME      NOT NULL,
    [SS_EndTime]   DATETIME      NOT NULL,
    [SS_Error]     VARCHAR (MAX) NULL,
    CONSTRAINT [PK_SYS_ScheduleHistory] PRIMARY KEY CLUSTERED ([SS_ID] ASC)
);


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'錯誤訊息', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'SYS_ScheduleHistory', @level2type = N'COLUMN', @level2name = N'SS_Error';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'排程結束時間', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'SYS_ScheduleHistory', @level2type = N'COLUMN', @level2name = N'SS_EndTime';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'排程開始時間', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'SYS_ScheduleHistory', @level2type = N'COLUMN', @level2name = N'SS_StartTime';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'排程名稱', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'SYS_ScheduleHistory', @level2type = N'COLUMN', @level2name = N'SS_Name';

