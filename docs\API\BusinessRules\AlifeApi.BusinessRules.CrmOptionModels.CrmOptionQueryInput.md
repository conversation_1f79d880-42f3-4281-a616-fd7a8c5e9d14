#### [AlifeApi.BusinessRules](index.md 'index')
### [AlifeApi.BusinessRules.CrmOptionModels](AlifeApi.BusinessRules.CrmOptionModels.md 'AlifeApi.BusinessRules.CrmOptionModels')

## CrmOptionQueryInput Class

CRM選項查詢輸入模型

```csharp
public class CrmOptionQueryInput : AlifeApi.BusinessRules.Infrastructure.PagedListInput
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; [PagedListInput](AlifeApi.BusinessRules.Infrastructure.PagedListInput.md 'AlifeApi.BusinessRules.Infrastructure.PagedListInput') &#129106; CrmOptionQueryInput
### Properties

<a name='AlifeApi.BusinessRules.CrmOptionModels.CrmOptionQueryInput.CrmOptionTypeId'></a>

## CrmOptionQueryInput.CrmOptionTypeId Property

CRM選項類型ID (篩選條件)

```csharp
public System.Nullable<long> CrmOptionTypeId { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.Int64](https://docs.microsoft.com/en-us/dotnet/api/System.Int64 'System.Int64')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

<a name='AlifeApi.BusinessRules.CrmOptionModels.CrmOptionQueryInput.IsActive'></a>

## CrmOptionQueryInput.IsActive Property

是否啟用 (篩選條件)

```csharp
public System.Nullable<bool> IsActive { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.Boolean](https://docs.microsoft.com/en-us/dotnet/api/System.Boolean 'System.Boolean')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

<a name='AlifeApi.BusinessRules.CrmOptionModels.CrmOptionQueryInput.OptionValue'></a>

## CrmOptionQueryInput.OptionValue Property

選項值 (模糊查詢)

```csharp
public string? OptionValue { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.CrmOptionModels.CrmOptionQueryInput.SiteCode'></a>

## CrmOptionQueryInput.SiteCode Property

案場代碼 (篩選條件)

```csharp
public string? SiteCode { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')