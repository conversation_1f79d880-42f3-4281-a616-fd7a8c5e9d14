#### [AlifeApi.BusinessRules](index.md 'index')
### [AlifeApi.BusinessRules.ProblemReportModels](AlifeApi.BusinessRules.ProblemReportModels.md 'AlifeApi.BusinessRules.ProblemReportModels')

## UserReportCreateInput Class

新增問題回報 Request 資料模型

```csharp
public class UserReportCreateInput
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; UserReportCreateInput
### Properties

<a name='AlifeApi.BusinessRules.ProblemReportModels.UserReportCreateInput.Menu'></a>

## UserReportCreateInput.Menu Property

功能頁面

```csharp
public string Menu { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='<PERSON>feApi.BusinessRules.ProblemReportModels.UserReportCreateInput.ProblemType'></a>

## UserReportCreateInput.ProblemType Property

類型

```csharp
public string ProblemType { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.ProblemReportModels.UserReportCreateInput.ReportContent'></a>

## UserReportCreateInput.ReportContent Property

問題內容

```csharp
public string ReportContent { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.ProblemReportModels.UserReportCreateInput.Subject'></a>

## UserReportCreateInput.Subject Property

問題主旨

```csharp
public string Subject { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')