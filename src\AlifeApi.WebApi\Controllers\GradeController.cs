﻿using AlifeApi.BusinessRules.GradeModels;
using AlifeApi.BusinessRules.Infrastructure;
using Microsoft.AspNetCore.Mvc;

namespace <PERSON>feA<PERSON>.WebApi.Controllers
{
    /// <summary>
    /// 職稱作業
    /// </summary>
    public class GradeController : AuthenticatedController
    {
        //private readonly GradeService _gradeService;


        ///// <summary>
        ///// Initializes a new instance of the <see cref="GradeController"/> class.
        ///// </summary>
        ///// <param name="gradeService">The role group service.</param>
        ///// <exception cref="ArgumentNullException">roleGroupService</exception>
        //public GradeController(GradeService gradeService)
        //{
        //    _gradeService = gradeService ?? throw new ArgumentNullException(nameof(gradeService));
        //}

        ///// <summary>
        ///// 取得所有職稱
        ///// </summary>
        ///// <param name="input">The input.</param>
        ///// <returns>職稱作業</returns>
        //[HttpPost]
        //public async Task<PagedListOutput<GradeListResult>> GetGradeListAsync(GradeListCondition input)
        //    => await _gradeService.GetGradeList(input);

        ///// <summary>
        ///// 新增職稱
        ///// </summary>
        ///// <param name="input">The input.</param>
        ///// <returns>職稱作業</returns>
        //[HttpPost]
        //public async Task CreateGradeAsync(CreateGradeCondition input)
        //    => await _gradeService.CreateGrade(input);

        ///// <summary>
        ///// 變更職稱
        ///// </summary>
        ///// <param name="input">The input.</param>
        ///// <returns>職稱作業</returns>
        //[HttpPost]
        //public async Task UpdateGradeAsync(UpdateGradeCondition input)
        //    => await _gradeService.UpdateGrade(input);

        ///// <summary>
        ///// 修改職稱權限功能
        ///// </summary>
        ///// <param name="input">The input.</param>
        //[HttpPost]
        //public async Task UpdateGradePermissionAsync(UpdateGradePermissionCondition input)
        //    => await _gradeService.UpdateGradePermission(input);

        ///// <summary>
        ///// 刪除職稱(如果原本是刪除，打了會復原)
        ///// </summary>
        ///// <param name="input">The input.</param>
        ///// <returns>職稱作業</returns>
        //[HttpPost]
        //public async Task DeleteGradeAsync(DeleteGradeCondition input)
        //    => await _gradeService.DeleteGrade(input);


    }
}
