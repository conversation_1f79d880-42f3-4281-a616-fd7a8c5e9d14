using AlifeApi.BusinessRules.CategoryModels;
using AlifeApi.BusinessRules.Infrastructure;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Threading.Tasks;

namespace AlifeApi.WebApi.Controllers
{
    /// <summary>
    /// 大分類管理
    /// </summary>
    public class LargeCategoriesController : AuthenticatedController
    {
        private readonly LargeCategoryService _largeCategoryService;

        public LargeCategoriesController(LargeCategoryService largeCategoryService)
        {
            _largeCategoryService = largeCategoryService;
        }

        /// <summary>
        /// 取得大分類列表 (分頁)
        /// </summary>
        /// <param name="input">查詢條件</param>
        /// <returns>分頁的大分類列表</returns>
        [HttpPost]
        public async Task<IActionResult> GetLargeCategories([FromBody] LargeCategoryQueryInput input)
        {
            var result = await _largeCategoryService.GetLargeCategoryListAsync(input);
            return Ok(result);
        }

        /// <summary>
        /// 根據大分類ID取得單一大分類資訊
        /// </summary>
        /// <param name="largeCategoryId">大分類ID</param>
        /// <returns>大分類詳細資訊</returns>
        [HttpGet("{largeCategoryId}")]
        public async Task<IActionResult> GetLargeCategory(long largeCategoryId)
        {
            var result = await _largeCategoryService.GetLargeCategoryByIdAsync(largeCategoryId);
            if (result == null)
            {
                return NotFound();
            }
            return Ok(result);
        }

        /// <summary>
        /// 新增大分類
        /// </summary>
        /// <param name="input">新增大分類輸入資料</param>
        /// <returns>新增的大分類ID</returns>
        [HttpPost]
        public async Task<IActionResult> CreateLargeCategory([FromBody] LargeCategoryInput input)
        {
            try
            {
                var largeCategoryId = await _largeCategoryService.CreateLargeCategoryAsync(input);
                return CreatedAtAction(nameof(GetLargeCategory), new { largeCategoryId = largeCategoryId }, new { LargeCategoryId = largeCategoryId });
            }
            catch (Exception ex)
            {
                return BadRequest(new { message = ex.Message });
            }
        }

        /// <summary>
        /// 更新大分類資料
        /// </summary>
        /// <param name="largeCategoryId">要更新的大分類ID</param>
        /// <param name="input">更新的大分類資料</param>
        /// <returns>成功時返回 Ok，失敗時返回 NotFound 或 BadRequest</returns>
        [HttpPost("{largeCategoryId}")]
        public async Task<IActionResult> UpdateLargeCategory(long largeCategoryId, [FromBody] LargeCategoryInput input)
        {
            try
            {
                await _largeCategoryService.UpdateLargeCategoryAsync(largeCategoryId, input);
                return Ok();
            }
            catch (Exception ex) when (ex.Message.Contains("找不到"))
            {
                return NotFound(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                return BadRequest(new { message = ex.Message });
            }
        }

        /// <summary>
        /// 刪除大分類
        /// </summary>
        /// <param name="largeCategoryId">要刪除的大分類ID</param>
        /// <returns>成功時返回 Ok，失敗時返回 NotFound 或 BadRequest</returns>
        [HttpDelete("{largeCategoryId}")]
        public async Task<IActionResult> DeleteLargeCategory(long largeCategoryId)
        {
            try
            {
                await _largeCategoryService.DeleteLargeCategoryAsync(largeCategoryId);
                return Ok();
            }
            catch (Exception ex) when (ex.Message.Contains("找不到"))
            {
                return NotFound(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                return BadRequest(new { message = ex.Message });
            }
        }

        /// <summary>
        /// 取得大分類下拉選單列表
        /// </summary>
        /// <returns>啟用的大分類下拉選單列表</returns>
        [HttpGet]
        public async Task<IActionResult> GetLargeCategoryDropdown()
        {
            var result = await _largeCategoryService.GetLargeCategoryDropdownAsync();
            return Ok(result);
        }
    }
} 
