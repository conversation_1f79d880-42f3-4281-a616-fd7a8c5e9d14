#### [AlifeApi.BusinessRules](index.md 'index')
### [AlifeApi.BusinessRules.CategoryModels](AlifeApi.BusinessRules.CategoryModels.md 'AlifeApi.BusinessRules.CategoryModels')

## LargeCategoryDropdownOutput Class

大分類下拉選單輸出

```csharp
public class LargeCategoryDropdownOutput
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; LargeCategoryDropdownOutput
### Properties

<a name='AlifeApi.BusinessRules.CategoryModels.LargeCategoryDropdownOutput.LargeCategoryId'></a>

## LargeCategoryDropdownOutput.LargeCategoryId Property

大分類ID

```csharp
public long LargeCategoryId { get; set; }
```

#### Property Value
[System.Int64](https://docs.microsoft.com/en-us/dotnet/api/System.Int64 'System.Int64')

<a name='AlifeApi.BusinessRules.CategoryModels.LargeCategoryDropdownOutput.Name'></a>

## LargeCategoryDropdownOutput.Name Property

大分類名稱

```csharp
public string Name { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.CategoryModels.LargeCategoryDropdownOutput.SortOrder'></a>

## LargeCategoryDropdownOutput.SortOrder Property

排序順序

```csharp
public int SortOrder { get; set; }
```

#### Property Value
[System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')