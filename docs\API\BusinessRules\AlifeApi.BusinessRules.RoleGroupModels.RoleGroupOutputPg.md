#### [AlifeApi.BusinessRules](index.md 'index')
### [AlifeApi.BusinessRules.RoleGroupModels](AlifeApi.BusinessRules.RoleGroupModels.md 'AlifeApi.BusinessRules.RoleGroupModels')

## RoleGroupOutputPg Class

角色權限輸出（PostgreSQL 版本）

```csharp
public class RoleGroupOutputPg
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; RoleGroupOutputPg
### Properties

<a name='AlifeApi.BusinessRules.RoleGroupModels.RoleGroupOutputPg.CreatedTime'></a>

## RoleGroupOutputPg.CreatedTime Property

建立時間

```csharp
public System.DateTime CreatedTime { get; set; }
```

#### Property Value
[System.DateTime](https://docs.microsoft.com/en-us/dotnet/api/System.DateTime 'System.DateTime')

<a name='AlifeApi.BusinessRules.RoleGroupModels.RoleGroupOutputPg.CreatedUserId'></a>

## RoleGroupOutputPg.CreatedUserId Property

建立人帳號

```csharp
public string CreatedUserId { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.RoleGroupModels.RoleGroupOutputPg.CreatedUserName'></a>

## RoleGroupOutputPg.CreatedUserName Property

建立人姓名

```csharp
public string CreatedUserName { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.RoleGroupModels.RoleGroupOutputPg.FuncIds'></a>

## RoleGroupOutputPg.FuncIds Property

角色權限

```csharp
public System.Collections.Generic.IEnumerable<string> FuncIds { get; set; }
```

#### Property Value
[System.Collections.Generic.IEnumerable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Collections.Generic.IEnumerable-1 'System.Collections.Generic.IEnumerable`1')[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Collections.Generic.IEnumerable-1 'System.Collections.Generic.IEnumerable`1')

<a name='AlifeApi.BusinessRules.RoleGroupModels.RoleGroupOutputPg.IsAdmin'></a>

## RoleGroupOutputPg.IsAdmin Property

是否為管理者角色

```csharp
public bool IsAdmin { get; set; }
```

#### Property Value
[System.Boolean](https://docs.microsoft.com/en-us/dotnet/api/System.Boolean 'System.Boolean')

<a name='AlifeApi.BusinessRules.RoleGroupModels.RoleGroupOutputPg.MenuTrees'></a>

## RoleGroupOutputPg.MenuTrees Property

權限樹

```csharp
public System.Collections.Generic.IEnumerable<AlifeApi.BusinessRules.RoleGroupModels.MenuTreeOutput> MenuTrees { get; set; }
```

#### Property Value
[System.Collections.Generic.IEnumerable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Collections.Generic.IEnumerable-1 'System.Collections.Generic.IEnumerable`1')[MenuTreeOutput](AlifeApi.BusinessRules.RoleGroupModels.MenuTreeOutput.md 'AlifeApi.BusinessRules.RoleGroupModels.MenuTreeOutput')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Collections.Generic.IEnumerable-1 'System.Collections.Generic.IEnumerable`1')

<a name='AlifeApi.BusinessRules.RoleGroupModels.RoleGroupOutputPg.Name'></a>

## RoleGroupOutputPg.Name Property

角色群組名稱

```csharp
public string Name { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.RoleGroupModels.RoleGroupOutputPg.Permission'></a>

## RoleGroupOutputPg.Permission Property

角色權限

```csharp
public string Permission { get; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.RoleGroupModels.RoleGroupOutputPg.RoleGroupId'></a>

## RoleGroupOutputPg.RoleGroupId Property

角色ID

```csharp
public string RoleGroupId { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.RoleGroupModels.RoleGroupOutputPg.SiteCode'></a>

## RoleGroupOutputPg.SiteCode Property

案場代碼

```csharp
public string SiteCode { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.RoleGroupModels.RoleGroupOutputPg.SiteName'></a>

## RoleGroupOutputPg.SiteName Property

案場名稱

```csharp
public string SiteName { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.RoleGroupModels.RoleGroupOutputPg.UpdatedTime'></a>

## RoleGroupOutputPg.UpdatedTime Property

更新時間

```csharp
public System.DateTime UpdatedTime { get; set; }
```

#### Property Value
[System.DateTime](https://docs.microsoft.com/en-us/dotnet/api/System.DateTime 'System.DateTime')

<a name='AlifeApi.BusinessRules.RoleGroupModels.RoleGroupOutputPg.UpdatedUserId'></a>

## RoleGroupOutputPg.UpdatedUserId Property

更新者帳號

```csharp
public string UpdatedUserId { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.RoleGroupModels.RoleGroupOutputPg.UpdatedUserName'></a>

## RoleGroupOutputPg.UpdatedUserName Property

更新者姓名

```csharp
public string UpdatedUserName { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.RoleGroupModels.RoleGroupOutputPg.UserIds'></a>

## RoleGroupOutputPg.UserIds Property

權限擁有者清單

```csharp
public System.Collections.Generic.IEnumerable<string> UserIds { get; set; }
```

#### Property Value
[System.Collections.Generic.IEnumerable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Collections.Generic.IEnumerable-1 'System.Collections.Generic.IEnumerable`1')[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Collections.Generic.IEnumerable-1 'System.Collections.Generic.IEnumerable`1')