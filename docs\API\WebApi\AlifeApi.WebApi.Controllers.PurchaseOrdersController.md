#### [AlifeApi.WebApi](index.md 'index')
### [AlifeApi.WebApi.Controllers](AlifeApi.WebApi.Controllers.md 'AlifeApi.WebApi.Controllers')

## PurchaseOrdersController Class

買賣預定單管理

```csharp
public class PurchaseOrdersController : AlifeApi.WebApi.Controllers.AuthenticatedController
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; [Microsoft.AspNetCore.Mvc.ControllerBase](https://docs.microsoft.com/en-us/dotnet/api/Microsoft.AspNetCore.Mvc.ControllerBase 'Microsoft.AspNetCore.Mvc.ControllerBase') &#129106; [Microsoft.AspNetCore.Mvc.Controller](https://docs.microsoft.com/en-us/dotnet/api/Microsoft.AspNetCore.Mvc.Controller 'Microsoft.AspNetCore.Mvc.Controller') &#129106; [AlifeApi.WebApi.Controllers.BasicController](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.WebApi.Controllers.BasicController 'AlifeApi.WebApi.Controllers.BasicController') &#129106; [AlifeApi.WebApi.Controllers.AuthenticatedController](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.WebApi.Controllers.AuthenticatedController 'AlifeApi.WebApi.Controllers.AuthenticatedController') &#129106; PurchaseOrdersController
### Methods

<a name='AlifeApi.WebApi.Controllers.PurchaseOrdersController.CreatePurchaseOrder(AlifeApi.BusinessRules.PurchaseOrderModels.PurchaseOrderCreateInput)'></a>

## PurchaseOrdersController.CreatePurchaseOrder(PurchaseOrderCreateInput) Method

新增買賣預定單

```csharp
public System.Threading.Tasks.Task<Microsoft.AspNetCore.Mvc.ActionResult<int>> CreatePurchaseOrder(AlifeApi.BusinessRules.PurchaseOrderModels.PurchaseOrderCreateInput input);
```
#### Parameters

<a name='AlifeApi.WebApi.Controllers.PurchaseOrdersController.CreatePurchaseOrder(AlifeApi.BusinessRules.PurchaseOrderModels.PurchaseOrderCreateInput).input'></a>

`input` [AlifeApi.BusinessRules.PurchaseOrderModels.PurchaseOrderCreateInput](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.BusinessRules.PurchaseOrderModels.PurchaseOrderCreateInput 'AlifeApi.BusinessRules.PurchaseOrderModels.PurchaseOrderCreateInput')

訂單建立輸入資料

#### Returns
[System.Threading.Tasks.Task&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')[Microsoft.AspNetCore.Mvc.ActionResult&lt;](https://docs.microsoft.com/en-us/dotnet/api/Microsoft.AspNetCore.Mvc.ActionResult-1 'Microsoft.AspNetCore.Mvc.ActionResult`1')[System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/Microsoft.AspNetCore.Mvc.ActionResult-1 'Microsoft.AspNetCore.Mvc.ActionResult`1')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')  
新增的訂單ID

<a name='AlifeApi.WebApi.Controllers.PurchaseOrdersController.DeletePurchaseOrder(int)'></a>

## PurchaseOrdersController.DeletePurchaseOrder(int) Method

刪除買賣預定單

```csharp
public System.Threading.Tasks.Task<Microsoft.AspNetCore.Mvc.ActionResult> DeletePurchaseOrder(int orderId);
```
#### Parameters

<a name='AlifeApi.WebApi.Controllers.PurchaseOrdersController.DeletePurchaseOrder(int).orderId'></a>

`orderId` [System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')

訂單ID

#### Returns
[System.Threading.Tasks.Task&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')[Microsoft.AspNetCore.Mvc.ActionResult](https://docs.microsoft.com/en-us/dotnet/api/Microsoft.AspNetCore.Mvc.ActionResult 'Microsoft.AspNetCore.Mvc.ActionResult')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')  
NoContent

<a name='AlifeApi.WebApi.Controllers.PurchaseOrdersController.GetPurchaseOrder(int)'></a>

## PurchaseOrdersController.GetPurchaseOrder(int) Method

根據訂單ID取得詳細資訊

```csharp
public System.Threading.Tasks.Task<Microsoft.AspNetCore.Mvc.ActionResult<AlifeApi.BusinessRules.PurchaseOrderModels.PurchaseOrderOutput>> GetPurchaseOrder(int orderId);
```
#### Parameters

<a name='AlifeApi.WebApi.Controllers.PurchaseOrdersController.GetPurchaseOrder(int).orderId'></a>

`orderId` [System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')

訂單ID

#### Returns
[System.Threading.Tasks.Task&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')[Microsoft.AspNetCore.Mvc.ActionResult&lt;](https://docs.microsoft.com/en-us/dotnet/api/Microsoft.AspNetCore.Mvc.ActionResult-1 'Microsoft.AspNetCore.Mvc.ActionResult`1')[AlifeApi.BusinessRules.PurchaseOrderModels.PurchaseOrderOutput](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.BusinessRules.PurchaseOrderModels.PurchaseOrderOutput 'AlifeApi.BusinessRules.PurchaseOrderModels.PurchaseOrderOutput')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/Microsoft.AspNetCore.Mvc.ActionResult-1 'Microsoft.AspNetCore.Mvc.ActionResult`1')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')  
訂單詳細資訊

<a name='AlifeApi.WebApi.Controllers.PurchaseOrdersController.GetPurchaseOrders(AlifeApi.BusinessRules.PurchaseOrderModels.PurchaseOrderQueryInput)'></a>

## PurchaseOrdersController.GetPurchaseOrders(PurchaseOrderQueryInput) Method

取得買賣預定單列表 (分頁)

```csharp
public System.Threading.Tasks.Task<Microsoft.AspNetCore.Mvc.ActionResult<AlifeApi.BusinessRules.Infrastructure.PagedListOutput<AlifeApi.BusinessRules.PurchaseOrderModels.PurchaseOrderListOutput>>> GetPurchaseOrders(AlifeApi.BusinessRules.PurchaseOrderModels.PurchaseOrderQueryInput input);
```
#### Parameters

<a name='AlifeApi.WebApi.Controllers.PurchaseOrdersController.GetPurchaseOrders(AlifeApi.BusinessRules.PurchaseOrderModels.PurchaseOrderQueryInput).input'></a>

`input` [AlifeApi.BusinessRules.PurchaseOrderModels.PurchaseOrderQueryInput](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.BusinessRules.PurchaseOrderModels.PurchaseOrderQueryInput 'AlifeApi.BusinessRules.PurchaseOrderModels.PurchaseOrderQueryInput')

查詢條件

#### Returns
[System.Threading.Tasks.Task&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')[Microsoft.AspNetCore.Mvc.ActionResult&lt;](https://docs.microsoft.com/en-us/dotnet/api/Microsoft.AspNetCore.Mvc.ActionResult-1 'Microsoft.AspNetCore.Mvc.ActionResult`1')[AlifeApi.BusinessRules.Infrastructure.PagedListOutput&lt;](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.BusinessRules.Infrastructure.PagedListOutput-1 'AlifeApi.BusinessRules.Infrastructure.PagedListOutput`1')[AlifeApi.BusinessRules.PurchaseOrderModels.PurchaseOrderListOutput](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.BusinessRules.PurchaseOrderModels.PurchaseOrderListOutput 'AlifeApi.BusinessRules.PurchaseOrderModels.PurchaseOrderListOutput')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.BusinessRules.Infrastructure.PagedListOutput-1 'AlifeApi.BusinessRules.Infrastructure.PagedListOutput`1')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/Microsoft.AspNetCore.Mvc.ActionResult-1 'Microsoft.AspNetCore.Mvc.ActionResult`1')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')  
分頁的買賣預定單列表

<a name='AlifeApi.WebApi.Controllers.PurchaseOrdersController.UpdateBusinessDates(int,AlifeApi.WebApi.Controllers.BusinessDateUpdateInput)'></a>

## PurchaseOrdersController.UpdateBusinessDates(int, BusinessDateUpdateInput) Method

更新訂單業務流程日期 (售、足、簽、請、領)

```csharp
public System.Threading.Tasks.Task<Microsoft.AspNetCore.Mvc.ActionResult> UpdateBusinessDates(int orderId, AlifeApi.WebApi.Controllers.BusinessDateUpdateInput input);
```
#### Parameters

<a name='AlifeApi.WebApi.Controllers.PurchaseOrdersController.UpdateBusinessDates(int,AlifeApi.WebApi.Controllers.BusinessDateUpdateInput).orderId'></a>

`orderId` [System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')

訂單ID

<a name='AlifeApi.WebApi.Controllers.PurchaseOrdersController.UpdateBusinessDates(int,AlifeApi.WebApi.Controllers.BusinessDateUpdateInput).input'></a>

`input` [BusinessDateUpdateInput](AlifeApi.WebApi.Controllers.BusinessDateUpdateInput.md 'AlifeApi.WebApi.Controllers.BusinessDateUpdateInput')

業務日期更新輸入

#### Returns
[System.Threading.Tasks.Task&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')[Microsoft.AspNetCore.Mvc.ActionResult](https://docs.microsoft.com/en-us/dotnet/api/Microsoft.AspNetCore.Mvc.ActionResult 'Microsoft.AspNetCore.Mvc.ActionResult')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')  
NoContent

<a name='AlifeApi.WebApi.Controllers.PurchaseOrdersController.UpdatePurchaseOrder(int,AlifeApi.BusinessRules.PurchaseOrderModels.PurchaseOrderUpdateInput)'></a>

## PurchaseOrdersController.UpdatePurchaseOrder(int, PurchaseOrderUpdateInput) Method

更新買賣預定單

```csharp
public System.Threading.Tasks.Task<Microsoft.AspNetCore.Mvc.ActionResult> UpdatePurchaseOrder(int orderId, AlifeApi.BusinessRules.PurchaseOrderModels.PurchaseOrderUpdateInput input);
```
#### Parameters

<a name='AlifeApi.WebApi.Controllers.PurchaseOrdersController.UpdatePurchaseOrder(int,AlifeApi.BusinessRules.PurchaseOrderModels.PurchaseOrderUpdateInput).orderId'></a>

`orderId` [System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')

訂單ID

<a name='AlifeApi.WebApi.Controllers.PurchaseOrdersController.UpdatePurchaseOrder(int,AlifeApi.BusinessRules.PurchaseOrderModels.PurchaseOrderUpdateInput).input'></a>

`input` [AlifeApi.BusinessRules.PurchaseOrderModels.PurchaseOrderUpdateInput](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.BusinessRules.PurchaseOrderModels.PurchaseOrderUpdateInput 'AlifeApi.BusinessRules.PurchaseOrderModels.PurchaseOrderUpdateInput')

訂單更新輸入資料

#### Returns
[System.Threading.Tasks.Task&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')[Microsoft.AspNetCore.Mvc.ActionResult](https://docs.microsoft.com/en-us/dotnet/api/Microsoft.AspNetCore.Mvc.ActionResult 'Microsoft.AspNetCore.Mvc.ActionResult')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')  
NoContent