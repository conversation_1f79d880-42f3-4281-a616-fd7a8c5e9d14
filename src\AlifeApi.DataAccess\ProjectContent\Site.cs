﻿using System;
using System.Collections.Generic;

namespace AlifeApi.DataAccess.ProjectContent
{
    /// <summary>
    /// 案場資料表，用於儲存案場的基本資訊、區域資料和合約相關資訊。
    /// </summary>
    public partial class Site
    {
        /// <summary>
        /// 案場號碼，主鍵，用於唯一識別案場。
        /// </summary>
        public string SiteCode { get; set; }
        /// <summary>
        /// 執行公司別，對應 Company 表的 CompanyId，記錄案場所屬公司。
        /// </summary>
        public string CompanyId { get; set; }
        /// <summary>
        /// 案名，案場名稱。
        /// </summary>
        public string SiteName { get; set; }
        /// <summary>
        /// 推案型態，描述案場的推案方式或類型。
        /// </summary>
        public string PromotionType { get; set; }
        /// <summary>
        /// 主委，儲存單一人員 ID，對應 UserInfo 表的 UserInfoId。
        /// </summary>
        public string Chairman { get; set; }
        /// <summary>
        /// 副主委，儲存單一人員 ID，對應 UserInfo 表的 UserInfoId。
        /// </summary>
        public string ViceChairman { get; set; }
        /// <summary>
        /// 專案，儲存單一人員 ID，對應 UserInfo 表的 UserInfoId。
        /// </summary>
        public string ProjectManager { get; set; }
        /// <summary>
        /// 副專，儲存單一人員 ID，對應 UserInfo 表的 UserInfoId。
        /// </summary>
        public string DeputyProjectManager { get; set; }
        /// <summary>
        /// 業務，儲存以逗號分隔的字串（如 &quot;pm8327,bnv783,oia198027,...&quot;），對應多個 UserInfo 表的 UserInfoId。
        /// </summary>
        public string BusinessIds { get; set; }
        /// <summary>
        /// 跑單，儲存以逗號分隔的字串（如 &quot;pm8327,bnv783,oia198027,...&quot;），對應多個 UserInfo 表的 UserInfoId。
        /// </summary>
        public string RunnerIds { get; set; }
        /// <summary>
        /// 接待中心，案場的接待中心名稱或地址。
        /// </summary>
        public string ReceptionCenter { get; set; }
        /// <summary>
        /// 案場電話，案場的聯絡電話。
        /// </summary>
        public string SitePhone { get; set; }
        /// <summary>
        /// 經紀人，負責案場的經紀人名稱或編號。
        /// </summary>
        public string Broker { get; set; }
        /// <summary>
        /// 所在縣市，案場所在的縣市。
        /// </summary>
        public string City { get; set; }
        /// <summary>
        /// 所在區域，案場所在的區域或鄉鎮區。
        /// </summary>
        public string District { get; set; }
        /// <summary>
        /// 投資興建，開發商或投資方的名稱。
        /// </summary>
        public string Developer { get; set; }
        /// <summary>
        /// 基地位置，案場的具體地址。
        /// </summary>
        public string SiteLocation { get; set; }
        /// <summary>
        /// 基地面積，案場用地的面積，單位為平方公尺。
        /// </summary>
        public decimal? LandArea { get; set; }
        /// <summary>
        /// 使用分區，案場所在的使用分區類型。
        /// </summary>
        public string Zoning { get; set; }
        /// <summary>
        /// 公設比，公共設施比例（百分比）。
        /// </summary>
        public decimal? PublicFacilityRatio { get; set; }
        /// <summary>
        /// 結構，案場的建築結構類型。
        /// </summary>
        public string Structure { get; set; }
        /// <summary>
        /// 規劃樓層(上層)，地上樓層數。
        /// </summary>
        public string AboveGroundFloors { get; set; }
        /// <summary>
        /// 規劃樓層(下層)，地下樓層數。
        /// </summary>
        public string BelowGroundFloors { get; set; }
        /// <summary>
        /// 規劃戶車(店面)，規劃的店面戶數。
        /// </summary>
        public int? PlannedStoreUnits { get; set; }
        /// <summary>
        /// 規劃戶車(住家)，規劃的住宅戶數。
        /// </summary>
        public int? PlannedResidentialUnits { get; set; }
        /// <summary>
        /// 規劃戶車(車位)，規劃的車位數。
        /// </summary>
        public int? PlannedParkingSpaces { get; set; }
        /// <summary>
        /// 坪數，案場的單位面積（坪數）。
        /// </summary>
        public string UnitSize { get; set; }
        /// <summary>
        /// 全案總銷，案場的總銷售金額。
        /// </summary>
        public decimal? TotalSalePrice { get; set; }
        /// <summary>
        /// 車位類別，案場的車位類型（如機械車位、平面車位）。
        /// </summary>
        public string ParkingType { get; set; }
        /// <summary>
        /// 合約期間，案場合約的期間（天數或月數）。
        /// </summary>
        public DateOnly? ContractPeriod { get; set; }
        /// <summary>
        /// 展延期間，合約展延的期間（天數或月數）。
        /// </summary>
        public DateOnly? ExtensionPeriod { get; set; }
        /// <summary>
        /// 可售總銷，可供銷售的總金額。
        /// </summary>
        public decimal? SellableTotalPrice { get; set; }
        /// <summary>
        /// 服務費計算，服務費的計算方式。
        /// </summary>
        public string ServiceFeeCalculation { get; set; }
        /// <summary>
        /// 服務費率，服務費的比率（百分比）。
        /// </summary>
        public decimal? ServiceFeeRate { get; set; }
        /// <summary>
        /// 保留款，案場的保留款金額。
        /// </summary>
        public decimal? ReserveAmount { get; set; }
        /// <summary>
        /// 應編廣告預算，案場應編列的廣告預算金額。
        /// </summary>
        public decimal? AdvertisingBudget { get; set; }
        /// <summary>
        /// 廣告預算率，廣告預算的比率（百分比）。
        /// </summary>
        public decimal? AdvertisingBudgetRate { get; set; }
        /// <summary>
        /// 超價款分配，超價款的分配方式。
        /// </summary>
        public string ExcessPriceAllocation { get; set; }
        /// <summary>
        /// 已發包金額，已發包的金額。
        /// </summary>
        public decimal? ContractedAmount { get; set; }
        /// <summary>
        /// 控存率，控制儲備的比率（百分比）。
        /// </summary>
        public decimal? ControlReserveRate { get; set; }
        /// <summary>
        /// 已請款金額，已請款的金額，預設為 0。
        /// </summary>
        public decimal? PaidAmount { get; set; }
        /// <summary>
        /// 建立者，記錄創建此資料的員工編號，對應 UserInfo 表的 UserInfoId。
        /// </summary>
        public string CreatedUserInfoId { get; set; }
        /// <summary>
        /// 創建時間，記錄資料創建的時間。
        /// </summary>
        public DateTime CreatedTime { get; set; }
        /// <summary>
        /// 更新者，記錄最後更新此資料的員工編號，對應 UserInfo 表的 UserInfoId。
        /// </summary>
        public string UpdatedUserInfoId { get; set; }
        /// <summary>
        /// 更新時間，記錄資料最後更新的時間。
        /// </summary>
        public DateTime UpdatedTime { get; set; }

        public virtual UserInfo ChairmanNavigation { get; set; }
        public virtual UserInfo CreatedUserInfo { get; set; }
        public virtual UserInfo DeputyProjectManagerNavigation { get; set; }
        public virtual UserInfo ProjectManagerNavigation { get; set; }
        public virtual UserInfo UpdatedUserInfo { get; set; }
        public virtual UserInfo ViceChairmanNavigation { get; set; }
    }
}
