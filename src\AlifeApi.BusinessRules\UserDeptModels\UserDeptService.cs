﻿using AlifeApi.BusinessRules.Infrastructure;
using AlifeApi.Common.Util;
using AlifeApi.DataAccess.ProjectContext;
using Microsoft.EntityFrameworkCore;

//namespace AlifeApi.BusinessRules.UserDeptModels
//{
//    public class UserDeptService : ServiceBase<ProjectContext>
//    {
//        public UserDeptService(IServiceProvider serviceProvider, ProjectContext dbContext) : base(serviceProvider, dbContext)
//        {
//        }

//        /// <summary>
//        /// 新增部門階層
//        /// </summary>
//        /// <param name="input">The input.</param>
//        /// <returns>新增結果</returns>
//        public async Task<UserDeptUpdateOutput> CreateUserDeptAsync(UserDeptCreateInput input)
//        {
//            ArgumentNullException.ThrowIfNull(input, nameof(input));

//            UserDeptUpdateOutput output = new();

//            if (string.IsNullOrEmpty(input.ParentDeptId))
//            {
//                input.ParentDeptId = null;
//            }
//            else if (!await Db.UserDept.AnyAsync(x => x.Id == input.ParentDeptId))
//            {
//                // TODO 先暫時給這個
//                //output.Response = Message.InputNotFound;
//                return output;
//            }

//            if (await Db.UserDept.AnyAsync(x => x.Id == input.DeptId))
//            {
//                // TODO 先暫時給這個
//                //output.Response = Message.AlreadyExist;
//                return output;
//            }

//            Db.UserDept.Add(new UserDept
//            {
//                Id = input.DeptId,
//                DeptName = input.DeptName,
//                ParentDeptId = input.ParentDeptId,
//                IsDisabled = input.IsDisabled.Value,
//                LeaderUserId = string.IsNullOrEmpty(input.LeaderUserId)
//                    ? null : input.LeaderUserId,
//                CreatedUserId = CurrentUser.UserId,
//                CreatedTime = DateTime.Now,
//                UpdatedUserId = CurrentUser.UserId,
//                UpdatedTime = DateTime.Now,
//            });

//            await Db.SaveChangesAsync();

//            output.Response = Message.Success;
//            return output;
//        }

//        /// <summary>
//        /// 修改部門階層
//        /// </summary>
//        /// <param name="input">The input.</param>
//        /// <returns>修改結果</returns>
//        /// <exception cref="ArgumentNullException">input</exception>
//        public async Task<UserDeptUpdateOutput> UpdateUserDeptAsync(UserDeptUpdateInput input)
//        {
//            ArgumentNullException.ThrowIfNull(input, nameof(input));

//            UserDeptUpdateOutput output = new();

//            if (input.ParentDeptId is not null && !await Db.UserDept.AnyAsync(x => x.Id == input.ParentDeptId))
//            {
//                // TODO 先暫時給這個
//                // output.Response = Message.InputNotFound;
//                return output;
//            }

//            UserDept entity = await Db.UserDept.SingleOrDefaultAsync(x => x.Id == input.DeptId);

//            if (entity is null)
//            {
//                // output.Response = Message.InputNotFound;
//            }

//            ValueMapper<UserDeptUpdateInput, UserDept> mapper = new(input, entity);
//            mapper.MapIfHasValue(x => x.DeptName, x => x.DeptName);
//            mapper.MapIfHasValue(x => x.ParentDeptId, x => x.ParentDeptId);
//            mapper.MapIfHasValue(x => x.IsDisabled, x => x.IsDisabled);
//            mapper.MapIfHasValue(x => x.LeaderUserId, x => x.LeaderUserId);

//            entity.UpdatedUserId = CurrentUser.UserId;
//            entity.UpdatedTime = DateTime.Now;

//            await Db.SaveChangesAsync();

//            output.Response = Message.Success;
//            return output;
//        }

//        /// <summary>
//        /// 停用/啟用部門
//        /// </summary>
//        /// <param name="input">The input.</param>
//        /// <exception cref="ArgumentNullException">input</exception>
//        public async Task DisableUserDeptAsync(RecordDisableInputWithStringId input)
//        {
//            ArgumentNullException.ThrowIfNull(input, nameof(input));

//            UserDept entity = await Db.UserDept.SingleAsync(x => x.Id == input.Id);
//            entity.IsDisabled = input.IsDisabled.Value;

//            await Db.SaveChangesAsync();
//        }

//        /// <summary>
//        /// 取得部門清單
//        /// </summary>
//        /// <param name="input">The input.</param>
//        /// <returns>部門清單</returns>
//        /// <exception cref="ArgumentNullException">input</exception>
//        public async Task<PagedListOutput<UserDeptListItemGetOutput>> GetUserDeptListAsync(UserDeptListGetInput input)
//        {
//            ArgumentNullException.ThrowIfNull(input, nameof(input));

//            return await Db.UserDept.AsNoTracking()
//                .Select(x => new UserDeptListItemGetOutput
//                {
//                    DeptId = x.Id,
//                    DeptName = x.DeptName,
//                    ParentDeptId = x.ParentDeptId,
//                    ParentDeptName = x.ParentDept.DeptName,
//                    LeaderUserId = x.LeaderUserId,
//                    LeaderUserName = x.LeaderUser.Name,
//                    IsDisabled = x.IsDisabled,
//                })
//                .ToPagedListOutputAsync(input);

//        }
//    }
//}
