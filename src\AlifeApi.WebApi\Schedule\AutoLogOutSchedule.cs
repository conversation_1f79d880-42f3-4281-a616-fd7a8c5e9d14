﻿using AlifeApi.BusinessRules.UserInfoModels;
using AlifeApi.WebApi.Controllers;
using AlifeApi.WebApi.SignalR;
using Microsoft.AspNetCore.SignalR;
using Quartz;

namespace AlifeApi.WebApi.Schedule
{
    public class AutoLogOutSchedule : IJob
    {
        private readonly IHubContext<ConnectHub> hubcontext;

        public AutoLogOutSchedule(IHubContext<ConnectHub> hubcontext)
        {
            this.hubcontext = hubcontext ?? throw new ArgumentNullException(nameof(hubcontext));
        }

        public Task Execute(IJobExecutionContext context)
        {
            var allClient = ConnectHub.userConnections.Where(x => DateTime.Now > x.LogoutTime).ToList();
            foreach (var client in allClient)
            {
                hubcontext.Clients.Client(client.ConnectionId).SendAsync("ForceDisconnect").Wait();
            }            
            return Task.CompletedTask;
        }
    }
}
