﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AlifeApi.BusinessRules.DeptModels
{
    public class UpdateDeptPermissionCondition
    {

        /// <summary>
        /// 使用者單位
        /// </summary>
        public string DeptId { get; set; }

        /// <summary>
        /// 權限
        /// </summary>
        public string Permission { get; set; }

        /// <remarks>
        /// 前端會傳字串，只好這樣處理
        /// </remarks>
        public IEnumerable<string> FuncIds => Permission?.Split(',').Distinct() ?? Enumerable.Empty<string>();
    }
}
