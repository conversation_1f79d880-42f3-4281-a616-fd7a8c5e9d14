using System.Text.Json.Serialization;

namespace AlifeApi.BusinessRules.CategoryModels
{
    /// <summary>
    /// 中分類下拉選單輸出
    /// </summary>
    public class MediumCategoryDropdownOutput
    {
        /// <summary>
        /// 中分類ID
        /// </summary>
        [J<PERSON><PERSON>ropertyName("MediumCategoryId")]
        public long MediumCategoryId { get; set; }

        /// <summary>
        /// 所屬大分類ID
        /// </summary>
        [JsonPropertyName("LargeCategoryId")]
        public long LargeCategoryId { get; set; }

        /// <summary>
        /// 所屬大分類名稱
        /// </summary>
        [JsonPropertyName("LargeCategoryName")]
        public string LargeCategoryName { get; set; } = string.Empty;

        /// <summary>
        /// 中分類名稱
        /// </summary>
        [JsonPropertyName("Name")]
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// 排序順序
        /// </summary>
        [JsonPropertyName("SortOrder")]
        public int SortOrder { get; set; }
    }
} 
