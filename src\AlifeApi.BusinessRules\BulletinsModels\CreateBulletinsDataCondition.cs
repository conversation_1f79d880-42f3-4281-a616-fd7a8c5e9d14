﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AlifeApi.BusinessRules.BulletinsModels
{
    public class CreateBulletinsDataCondition
    {
        /// <summary>
        /// 公告標題
        /// </summary>  
        [Required, MaxLength(200, ErrorMessage = "標題長度不能超過200個字符")]
        public string Title { get; set; }
        /// <summary>
        /// 公告內容
        /// </summary>
        [Required, MaxLength(1000, ErrorMessage = "內容長度不能超過1000個字符")]
        public string Content { get; set; }
        /// <summary>
        /// 是否至頂 [Required]
        /// </summary>           
        public bool IsTop { get; set; }
        /// <summary>
        /// 公告開始時間
        /// </summary>
        public DateTime? PostDateFrom { get; set; }
        /// <summary>
        /// 公告結束時間
        /// </summary>
        public DateTime? PostDateTo { get; set; }
        /// <summary>
        /// 公告附檔清單
        /// </summary>
        public List<AttachListItem> AttachList { get; set; } = new List<AttachListItem>();
    }
}
