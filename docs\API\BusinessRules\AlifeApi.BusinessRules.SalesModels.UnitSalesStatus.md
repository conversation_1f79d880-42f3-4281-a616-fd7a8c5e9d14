#### [AlifeApi.BusinessRules](index.md 'index')
### [AlifeApi.BusinessRules.SalesModels](AlifeApi.BusinessRules.SalesModels.md 'AlifeApi.BusinessRules.SalesModels')

## UnitSalesStatus Class

房屋單位銷售狀態  
業務流程：可售 -> 售(訂金未付足) -> 足(補足訂金) -> 簽(已簽合約)  
請款和領款不再是狀態，而是透過 RequestDate 和 ReceiveDate 欄位記錄

```csharp
public static class UnitSalesStatus
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; UnitSalesStatus