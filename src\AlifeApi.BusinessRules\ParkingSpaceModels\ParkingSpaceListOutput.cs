using System;

namespace AlifeApi.BusinessRules.ParkingSpaceModels
{
    /// <summary>
    /// 停車位列表輸出項目
    /// </summary>
    public class ParkingSpaceListOutput
    {
        public int ParkingSpaceId { get; set; }
        public string SiteCode { get; set; } = null!;
        public string? SiteName { get; set; }
        public int BuildingId { get; set; }
        public int FloorId { get; set; }
        public string SpaceNumber { get; set; } = null!;
        public string SpaceType { get; set; } = null!;
        
        /// <summary>
        /// 車位尺寸 (例如 "250x550cm")
        /// </summary>
        public string? Dimensions { get; set; }
        
        /// <summary>
        /// 車位詳細位置描述 (例如 "靠近電梯", "角落位置")
        /// </summary>
        public string? Location { get; set; }
        
        /// <summary>
        /// 備註 (例如 "柱子較多", "出入較便利")
        /// </summary>
        public string? Remarks { get; set; }
        
        public DateTime CreatedTime { get; set; }

        // 額外顯示樓層與建築名稱 (選填)
        public string? FloorLabel { get; set; }
        public string? BuildingName { get; set; }
    }
} 
