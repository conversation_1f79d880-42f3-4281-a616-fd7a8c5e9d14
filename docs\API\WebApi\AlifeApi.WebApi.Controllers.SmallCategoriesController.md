#### [AlifeApi.WebApi](index.md 'index')
### [AlifeApi.WebApi.Controllers](AlifeApi.WebApi.Controllers.md 'AlifeApi.WebApi.Controllers')

## SmallCategoriesController Class

小分類管理

```csharp
public class SmallCategoriesController : AlifeApi.WebApi.Controllers.AuthenticatedController
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; [Microsoft.AspNetCore.Mvc.ControllerBase](https://docs.microsoft.com/en-us/dotnet/api/Microsoft.AspNetCore.Mvc.ControllerBase 'Microsoft.AspNetCore.Mvc.ControllerBase') &#129106; [Microsoft.AspNetCore.Mvc.Controller](https://docs.microsoft.com/en-us/dotnet/api/Microsoft.AspNetCore.Mvc.Controller 'Microsoft.AspNetCore.Mvc.Controller') &#129106; [AlifeApi.WebApi.Controllers.BasicController](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.WebApi.Controllers.BasicController 'AlifeApi.WebApi.Controllers.BasicController') &#129106; [AlifeApi.WebApi.Controllers.AuthenticatedController](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.WebApi.Controllers.AuthenticatedController 'AlifeApi.WebApi.Controllers.AuthenticatedController') &#129106; SmallCategoriesController
### Methods

<a name='AlifeApi.WebApi.Controllers.SmallCategoriesController.CreateSmallCategory(AlifeApi.BusinessRules.CategoryModels.SmallCategoryInput)'></a>

## SmallCategoriesController.CreateSmallCategory(SmallCategoryInput) Method

新增小分類

```csharp
public System.Threading.Tasks.Task<Microsoft.AspNetCore.Mvc.IActionResult> CreateSmallCategory(AlifeApi.BusinessRules.CategoryModels.SmallCategoryInput input);
```
#### Parameters

<a name='AlifeApi.WebApi.Controllers.SmallCategoriesController.CreateSmallCategory(AlifeApi.BusinessRules.CategoryModels.SmallCategoryInput).input'></a>

`input` [AlifeApi.BusinessRules.CategoryModels.SmallCategoryInput](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.BusinessRules.CategoryModels.SmallCategoryInput 'AlifeApi.BusinessRules.CategoryModels.SmallCategoryInput')

新增小分類輸入資料

#### Returns
[System.Threading.Tasks.Task&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')[Microsoft.AspNetCore.Mvc.IActionResult](https://docs.microsoft.com/en-us/dotnet/api/Microsoft.AspNetCore.Mvc.IActionResult 'Microsoft.AspNetCore.Mvc.IActionResult')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')  
新增的小分類ID

<a name='AlifeApi.WebApi.Controllers.SmallCategoriesController.DeleteSmallCategory(long)'></a>

## SmallCategoriesController.DeleteSmallCategory(long) Method

刪除小分類

```csharp
public System.Threading.Tasks.Task<Microsoft.AspNetCore.Mvc.IActionResult> DeleteSmallCategory(long smallCategoryId);
```
#### Parameters

<a name='AlifeApi.WebApi.Controllers.SmallCategoriesController.DeleteSmallCategory(long).smallCategoryId'></a>

`smallCategoryId` [System.Int64](https://docs.microsoft.com/en-us/dotnet/api/System.Int64 'System.Int64')

要刪除的小分類ID

#### Returns
[System.Threading.Tasks.Task&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')[Microsoft.AspNetCore.Mvc.IActionResult](https://docs.microsoft.com/en-us/dotnet/api/Microsoft.AspNetCore.Mvc.IActionResult 'Microsoft.AspNetCore.Mvc.IActionResult')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')  
成功時返回 Ok，失敗時返回 NotFound 或 BadRequest

<a name='AlifeApi.WebApi.Controllers.SmallCategoriesController.GetSmallCategories(AlifeApi.BusinessRules.CategoryModels.SmallCategoryQueryInput)'></a>

## SmallCategoriesController.GetSmallCategories(SmallCategoryQueryInput) Method

取得小分類列表 (分頁)

```csharp
public System.Threading.Tasks.Task<Microsoft.AspNetCore.Mvc.IActionResult> GetSmallCategories(AlifeApi.BusinessRules.CategoryModels.SmallCategoryQueryInput input);
```
#### Parameters

<a name='AlifeApi.WebApi.Controllers.SmallCategoriesController.GetSmallCategories(AlifeApi.BusinessRules.CategoryModels.SmallCategoryQueryInput).input'></a>

`input` [AlifeApi.BusinessRules.CategoryModels.SmallCategoryQueryInput](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.BusinessRules.CategoryModels.SmallCategoryQueryInput 'AlifeApi.BusinessRules.CategoryModels.SmallCategoryQueryInput')

查詢條件

#### Returns
[System.Threading.Tasks.Task&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')[Microsoft.AspNetCore.Mvc.IActionResult](https://docs.microsoft.com/en-us/dotnet/api/Microsoft.AspNetCore.Mvc.IActionResult 'Microsoft.AspNetCore.Mvc.IActionResult')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')  
分頁的小分類列表

<a name='AlifeApi.WebApi.Controllers.SmallCategoriesController.GetSmallCategory(long)'></a>

## SmallCategoriesController.GetSmallCategory(long) Method

根據小分類ID取得單一小分類資訊

```csharp
public System.Threading.Tasks.Task<Microsoft.AspNetCore.Mvc.IActionResult> GetSmallCategory(long smallCategoryId);
```
#### Parameters

<a name='AlifeApi.WebApi.Controllers.SmallCategoriesController.GetSmallCategory(long).smallCategoryId'></a>

`smallCategoryId` [System.Int64](https://docs.microsoft.com/en-us/dotnet/api/System.Int64 'System.Int64')

小分類ID

#### Returns
[System.Threading.Tasks.Task&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')[Microsoft.AspNetCore.Mvc.IActionResult](https://docs.microsoft.com/en-us/dotnet/api/Microsoft.AspNetCore.Mvc.IActionResult 'Microsoft.AspNetCore.Mvc.IActionResult')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')  
小分類詳細資訊

<a name='AlifeApi.WebApi.Controllers.SmallCategoriesController.GetSmallCategoryDropdown(System.Nullable_long_,System.Nullable_long_)'></a>

## SmallCategoriesController.GetSmallCategoryDropdown(Nullable<long>, Nullable<long>) Method

取得小分類下拉選單列表

```csharp
public System.Threading.Tasks.Task<Microsoft.AspNetCore.Mvc.IActionResult> GetSmallCategoryDropdown(System.Nullable<long> largeCategoryId=null, System.Nullable<long> mediumCategoryId=null);
```
#### Parameters

<a name='AlifeApi.WebApi.Controllers.SmallCategoriesController.GetSmallCategoryDropdown(System.Nullable_long_,System.Nullable_long_).largeCategoryId'></a>

`largeCategoryId` [System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.Int64](https://docs.microsoft.com/en-us/dotnet/api/System.Int64 'System.Int64')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

可選的大分類ID，用於篩選特定大分類下的小分類

<a name='AlifeApi.WebApi.Controllers.SmallCategoriesController.GetSmallCategoryDropdown(System.Nullable_long_,System.Nullable_long_).mediumCategoryId'></a>

`mediumCategoryId` [System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.Int64](https://docs.microsoft.com/en-us/dotnet/api/System.Int64 'System.Int64')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

可選的中分類ID，用於篩選特定中分類下的小分類

#### Returns
[System.Threading.Tasks.Task&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')[Microsoft.AspNetCore.Mvc.IActionResult](https://docs.microsoft.com/en-us/dotnet/api/Microsoft.AspNetCore.Mvc.IActionResult 'Microsoft.AspNetCore.Mvc.IActionResult')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')  
啟用的小分類下拉選單列表

<a name='AlifeApi.WebApi.Controllers.SmallCategoriesController.UpdateSmallCategory(long,AlifeApi.BusinessRules.CategoryModels.SmallCategoryInput)'></a>

## SmallCategoriesController.UpdateSmallCategory(long, SmallCategoryInput) Method

更新小分類資料

```csharp
public System.Threading.Tasks.Task<Microsoft.AspNetCore.Mvc.IActionResult> UpdateSmallCategory(long smallCategoryId, AlifeApi.BusinessRules.CategoryModels.SmallCategoryInput input);
```
#### Parameters

<a name='AlifeApi.WebApi.Controllers.SmallCategoriesController.UpdateSmallCategory(long,AlifeApi.BusinessRules.CategoryModels.SmallCategoryInput).smallCategoryId'></a>

`smallCategoryId` [System.Int64](https://docs.microsoft.com/en-us/dotnet/api/System.Int64 'System.Int64')

要更新的小分類ID

<a name='AlifeApi.WebApi.Controllers.SmallCategoriesController.UpdateSmallCategory(long,AlifeApi.BusinessRules.CategoryModels.SmallCategoryInput).input'></a>

`input` [AlifeApi.BusinessRules.CategoryModels.SmallCategoryInput](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.BusinessRules.CategoryModels.SmallCategoryInput 'AlifeApi.BusinessRules.CategoryModels.SmallCategoryInput')

更新的小分類資料

#### Returns
[System.Threading.Tasks.Task&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')[Microsoft.AspNetCore.Mvc.IActionResult](https://docs.microsoft.com/en-us/dotnet/api/Microsoft.AspNetCore.Mvc.IActionResult 'Microsoft.AspNetCore.Mvc.IActionResult')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')  
成功時返回 Ok，失敗時返回 NotFound 或 BadRequest