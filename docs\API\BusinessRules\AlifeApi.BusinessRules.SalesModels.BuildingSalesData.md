#### [AlifeApi.BusinessRules](index.md 'index')
### [AlifeApi.BusinessRules.SalesModels](AlifeApi.BusinessRules.SalesModels.md 'AlifeApi.BusinessRules.SalesModels')

## BuildingSalesData Class

建築銷控表資料

```csharp
public class BuildingSalesData
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; BuildingSalesData
### Properties

<a name='AlifeApi.BusinessRules.SalesModels.BuildingSalesData.BuildingId'></a>

## BuildingSalesData.BuildingId Property

建築物ID

```csharp
public System.Nullable<int> BuildingId { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

<a name='AlifeApi.BusinessRules.SalesModels.BuildingSalesData.BuildingName'></a>

## BuildingSalesData.BuildingName Property

建築物名稱

```csharp
public string? BuildingName { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.SalesModels.BuildingSalesData.ColumnSummary'></a>

## BuildingSalesData.ColumnSummary Property

戶別統計摘要 (A戶、B戶等)

```csharp
public System.Collections.Generic.List<AlifeApi.BusinessRules.SalesModels.ColumnSummary> ColumnSummary { get; set; }
```

#### Property Value
[System.Collections.Generic.List&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Collections.Generic.List-1 'System.Collections.Generic.List`1')[ColumnSummary](AlifeApi.BusinessRules.SalesModels.ColumnSummary.md 'AlifeApi.BusinessRules.SalesModels.ColumnSummary')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Collections.Generic.List-1 'System.Collections.Generic.List`1')

<a name='AlifeApi.BusinessRules.SalesModels.BuildingSalesData.Floors'></a>

## BuildingSalesData.Floors Property

樓層列表 (按樓層高低排序)

```csharp
public System.Collections.Generic.List<AlifeApi.BusinessRules.SalesModels.FloorInfo> Floors { get; set; }
```

#### Property Value
[System.Collections.Generic.List&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Collections.Generic.List-1 'System.Collections.Generic.List`1')[FloorInfo](AlifeApi.BusinessRules.SalesModels.FloorInfo.md 'AlifeApi.BusinessRules.SalesModels.FloorInfo')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Collections.Generic.List-1 'System.Collections.Generic.List`1')

<a name='AlifeApi.BusinessRules.SalesModels.BuildingSalesData.SiteCode'></a>

## BuildingSalesData.SiteCode Property

案場編號

```csharp
public string SiteCode { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.SalesModels.BuildingSalesData.TotalSummary'></a>

## BuildingSalesData.TotalSummary Property

總體統計摘要

```csharp
public AlifeApi.BusinessRules.SalesModels.TotalSummary TotalSummary { get; set; }
```

#### Property Value
[TotalSummary](AlifeApi.BusinessRules.SalesModels.TotalSummary.md 'AlifeApi.BusinessRules.SalesModels.TotalSummary')