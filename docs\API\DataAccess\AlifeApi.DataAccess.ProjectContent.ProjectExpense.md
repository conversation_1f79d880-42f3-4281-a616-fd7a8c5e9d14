#### [AlifeApi.DataAccess](index.md 'index')
### [AlifeApi.DataAccess.ProjectContent](AlifeApi.DataAccess.ProjectContent.md 'AlifeApi.DataAccess.ProjectContent')

## ProjectExpense Class

專案支出紀錄表，記錄每個案場的各項開銷

```csharp
public class ProjectExpense
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; ProjectExpense
### Properties

<a name='AlifeApi.DataAccess.ProjectContent.ProjectExpense.Amount'></a>

## ProjectExpense.Amount Property

支出金額

```csharp
public decimal Amount { get; set; }
```

#### Property Value
[System.Decimal](https://docs.microsoft.com/en-us/dotnet/api/System.Decimal 'System.Decimal')

<a name='AlifeApi.DataAccess.ProjectContent.ProjectExpense.CreatedUserInfoId'></a>

## ProjectExpense.CreatedUserInfoId Property

建立者，記錄創建此資料的員工編號。

```csharp
public string CreatedUserInfoId { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContent.ProjectExpense.CreateTime'></a>

## ProjectExpense.CreateTime Property

創建時間，記錄資料創建的時間。

```csharp
public System.DateTime CreateTime { get; set; }
```

#### Property Value
[System.DateTime](https://docs.microsoft.com/en-us/dotnet/api/System.DateTime 'System.DateTime')

<a name='AlifeApi.DataAccess.ProjectContent.ProjectExpense.Description'></a>

## ProjectExpense.Description Property

關於此筆支出的額外文字描述或備註

```csharp
public string Description { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContent.ProjectExpense.ExpenseDate'></a>

## ProjectExpense.ExpenseDate Property

支出發生的日期

```csharp
public System.DateOnly ExpenseDate { get; set; }
```

#### Property Value
[System.DateOnly](https://docs.microsoft.com/en-us/dotnet/api/System.DateOnly 'System.DateOnly')

<a name='AlifeApi.DataAccess.ProjectContent.ProjectExpense.ProjectExpenseId'></a>

## ProjectExpense.ProjectExpenseId Property

主鍵，支出紀錄唯一識別碼

```csharp
public long ProjectExpenseId { get; set; }
```

#### Property Value
[System.Int64](https://docs.microsoft.com/en-us/dotnet/api/System.Int64 'System.Int64')

<a name='AlifeApi.DataAccess.ProjectContent.ProjectExpense.SiteCode'></a>

## ProjectExpense.SiteCode Property

案場代碼，指明此筆支出屬於哪個案場

```csharp
public string SiteCode { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContent.ProjectExpense.SmallCategoryId'></a>

## ProjectExpense.SmallCategoryId Property

關聯的小分類ID (需由應用程式確保其有效性)

```csharp
public long SmallCategoryId { get; set; }
```

#### Property Value
[System.Int64](https://docs.microsoft.com/en-us/dotnet/api/System.Int64 'System.Int64')

<a name='AlifeApi.DataAccess.ProjectContent.ProjectExpense.UpdatedUserInfoId'></a>

## ProjectExpense.UpdatedUserInfoId Property

更新者，記錄最後更新此資料的員工編號。

```csharp
public string UpdatedUserInfoId { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContent.ProjectExpense.UpdateTime'></a>

## ProjectExpense.UpdateTime Property

更新時間，記錄資料最後更新的時間。

```csharp
public System.DateTime UpdateTime { get; set; }
```

#### Property Value
[System.DateTime](https://docs.microsoft.com/en-us/dotnet/api/System.DateTime 'System.DateTime')