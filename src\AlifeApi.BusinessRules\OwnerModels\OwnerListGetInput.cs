using AlifeApi.BusinessRules.Infrastructure;

namespace AlifeApi.BusinessRules.OwnerModels
{
    /// <summary>
    /// 查詢業主列表輸入模型
    /// </summary>
    public class OwnerListGetInput : PagedListInput
    {
        /// <summary>
        /// 公司名稱 (模糊查詢)
        /// </summary>
        public string? CompanyName { get; set; }

        /// <summary>
        /// 負責人姓名 (模糊查詢)
        /// </summary>
        public string? ResponsiblePerson { get; set; }

        /// <summary>
        /// 證號 (完全比對)
        /// </summary>
        public string? IdentificationNumber { get; set; }

        /// <summary>
        /// 人別 (完全比對)
        /// </summary>
        public string? PersonType { get; set; }
    }
} 
