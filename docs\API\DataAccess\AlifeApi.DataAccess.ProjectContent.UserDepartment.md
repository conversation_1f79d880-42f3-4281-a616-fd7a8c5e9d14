#### [AlifeApi.DataAccess](index.md 'index')
### [AlifeApi.DataAccess.ProjectContent](AlifeApi.DataAccess.ProjectContent.md 'AlifeApi.DataAccess.ProjectContent')

## UserDepartment Class

員工部門關聯表，用於儲存員工與部門的關聯關係，支持員工隸屬多個部門。

```csharp
public class UserDepartment
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; UserDepartment
### Properties

<a name='AlifeApi.DataAccess.ProjectContent.UserDepartment.DepartmentId'></a>

## UserDepartment.DepartmentId Property

部門編號，主鍵的一部分，對應 Departments 表的 DepartmentId。

```csharp
public string DepartmentId { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContent.UserDepartment.IsPrimary'></a>

## UserDepartment.IsPrimary Property

是否為主要部門，true 表示是，false 表示否，預設為 false。

```csharp
public System.Nullable<bool> IsPrimary { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.Boolean](https://docs.microsoft.com/en-us/dotnet/api/System.Boolean 'System.Boolean')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

<a name='AlifeApi.DataAccess.ProjectContent.UserDepartment.UserInfoId'></a>

## UserDepartment.UserInfoId Property

員工編號，主鍵的一部分，對應 UserInfo 表的 UserInfoId。

```csharp
public string UserInfoId { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')