#### [AlifeApi.BusinessRules](index.md 'index')
### [AlifeApi.BusinessRules.BuildingModels](AlifeApi.BusinessRules.BuildingModels.md 'AlifeApi.BusinessRules.BuildingModels')

## BuildingDropdownInput Class

建築物下拉選單輸入資料

```csharp
public class BuildingDropdownInput
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; BuildingDropdownInput
### Properties

<a name='AlifeApi.BusinessRules.BuildingModels.BuildingDropdownInput.SiteCode'></a>

## BuildingDropdownInput.SiteCode Property

案場代碼

```csharp
public string SiteCode { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')