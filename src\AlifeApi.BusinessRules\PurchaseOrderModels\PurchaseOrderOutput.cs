using System;
using System.Collections.Generic;
using AlifeApi.BusinessRules.PurchaseOrderModels;
using AlifeApi.BusinessRules.ParkingSpaceModels;

namespace AlifeApi.BusinessRules.PurchaseOrderModels
{
    /// <summary>
    /// 買賣預定單詳細輸出模型
    /// </summary>
    public class PurchaseOrderOutput
    {
        public int OrderId { get; set; }
        public string? OrderNumber { get; set; }
        public DateOnly OrderDate { get; set; }
        public string SiteCode { get; set; } = null!;
        public int CustomerId { get; set; }
        public string? CustomerName { get; set; }
        public string? CustomerPhoneNumber { get; set; }
        public string? SalesAgencyName { get; set; }
        public string? SalesAgencyMobile { get; set; }
        public string? SalesAgencyLandline { get; set; }
        public string? SalesAgencyEmail { get; set; }

        public int? UnitId { get; set; }
        public string? UnitNumber { get; set; }
        public string? UnitLayout { get; set; }
        public decimal? UnitTotalArea { get; set; }
        public string? BuildingName { get; set; }
        public string? FloorLabel { get; set; }

        public decimal? LandShareArea { get; set; }
        public string? PurchasedParkingSpaceIds { get; set; }
        public List<ParkingSpaceBasicInfoOutput> PurchasedParkingSpaces { get; set; } = new();

        public decimal? PropertyPrice { get; set; }
        public decimal? ParkingSpacePrice { get; set; }
        public decimal TotalPrice { get; set; }

        public decimal? DepositAmount { get; set; }
        public decimal? DepositPaidAmount { get; set; }
        public string? DepositPaymentMethod { get; set; }
        public string? DepositPayee { get; set; }
        public DateTime? DepositDueDate { get; set; }
        public decimal? DepositBalanceAmount { get; set; }
        public DateTime? ContractSigningAppointment { get; set; }
        public decimal? ContractSigningAmount { get; set; }
        public bool? ConsentToDataUsage { get; set; }
        public string? OrderRemarks { get; set; }

        public string SalespersonUserInfoId { get; set; } = null!;
        public string? SalespersonName { get; set; }
        public string? SaleType { get; set; }
        public string Status { get; set; } = null!;

        public DateOnly? SaleDate { get; set; }
        public DateOnly? DepositFullPaidDate { get; set; }
        public DateOnly? ContractSignedDate { get; set; }
        public DateOnly? CancellationDate { get; set; }
        public DateOnly? HandoverDate { get; set; }
        public DateOnly? FinalPaymentDate { get; set; }
        public DateOnly? RequestDate { get; set; }
        public DateOnly? ReceiveDate { get; set; }

        public DateTime CreatedTime { get; set; }
        public DateTime UpdatedTime { get; set; }
        public string CreatedUserInfoId { get; set; } = null!;
        public string UpdatedUserInfoId { get; set; } = null!;
        public string? CreatedUserName { get; set; }
        public string? UpdatedUserName { get; set; }
    }
} 
