#### [AlifeApi.BusinessRules](index.md 'index')
### [AlifeApi.BusinessRules.GradeModels](AlifeApi.BusinessRules.GradeModels.md 'AlifeApi.BusinessRules.GradeModels')

## CreateGradeCondition Class

```csharp
public class CreateGradeCondition
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; CreateGradeCondition
### Properties

<a name='AlifeApi.BusinessRules.GradeModels.CreateGradeCondition.FuncIds'></a>

## CreateGradeCondition.FuncIds Property

```csharp
public System.Collections.Generic.IEnumerable<string> FuncIds { get; }
```

#### Property Value
[System.Collections.Generic.IEnumerable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Collections.Generic.IEnumerable-1 'System.Collections.Generic.IEnumerable`1')[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Collections.Generic.IEnumerable-1 'System.Collections.Generic.IEnumerable`1')

### Remarks
前端會傳字串，只好這樣處理

<a name='AlifeApi.BusinessRules.GradeModels.CreateGradeCondition.GradeId'></a>

## CreateGradeCondition.GradeId Property

使用者職稱

```csharp
public string GradeId { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.GradeModels.CreateGradeCondition.GradeName'></a>

## CreateGradeCondition.GradeName Property

使用者職稱

```csharp
public string GradeName { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.GradeModels.CreateGradeCondition.ParentGradeId'></a>

## CreateGradeCondition.ParentGradeId Property

上級使用者職稱

```csharp
public string ParentGradeId { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.GradeModels.CreateGradeCondition.Permission'></a>

## CreateGradeCondition.Permission Property

權限

```csharp
public string Permission { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')