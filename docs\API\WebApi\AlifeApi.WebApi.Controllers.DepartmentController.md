#### [AlifeApi.WebApi](index.md 'index')
### [AlifeApi.WebApi.Controllers](AlifeApi.WebApi.Controllers.md 'AlifeApi.WebApi.Controllers')

## DepartmentController Class

部門管理

```csharp
public class DepartmentController : AlifeApi.WebApi.Controllers.AuthenticatedController
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; [Microsoft.AspNetCore.Mvc.ControllerBase](https://docs.microsoft.com/en-us/dotnet/api/Microsoft.AspNetCore.Mvc.ControllerBase 'Microsoft.AspNetCore.Mvc.ControllerBase') &#129106; [Microsoft.AspNetCore.Mvc.Controller](https://docs.microsoft.com/en-us/dotnet/api/Microsoft.AspNetCore.Mvc.Controller 'Microsoft.AspNetCore.Mvc.Controller') &#129106; [AlifeApi.WebApi.Controllers.BasicController](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.WebApi.Controllers.BasicController 'AlifeApi.WebApi.Controllers.BasicController') &#129106; [AlifeApi.WebApi.Controllers.AuthenticatedController](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.WebApi.Controllers.AuthenticatedController 'AlifeApi.WebApi.Controllers.AuthenticatedController') &#129106; DepartmentController
### Constructors

<a name='AlifeApi.WebApi.Controllers.DepartmentController.DepartmentController(AlifeApi.BusinessRules.DepartmentModels.DepartmentService)'></a>

## DepartmentController(DepartmentService) Constructor

建構函數

```csharp
public DepartmentController(AlifeApi.BusinessRules.DepartmentModels.DepartmentService departmentService);
```
#### Parameters

<a name='AlifeApi.WebApi.Controllers.DepartmentController.DepartmentController(AlifeApi.BusinessRules.DepartmentModels.DepartmentService).departmentService'></a>

`departmentService` [AlifeApi.BusinessRules.DepartmentModels.DepartmentService](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.BusinessRules.DepartmentModels.DepartmentService 'AlifeApi.BusinessRules.DepartmentModels.DepartmentService')
### Methods

<a name='AlifeApi.WebApi.Controllers.DepartmentController.CreateDepartmentAsync(AlifeApi.BusinessRules.DepartmentModels.DepartmentCreateInput)'></a>

## DepartmentController.CreateDepartmentAsync(DepartmentCreateInput) Method

新增部門

```csharp
public System.Threading.Tasks.Task<Microsoft.AspNetCore.Mvc.ActionResult> CreateDepartmentAsync(AlifeApi.BusinessRules.DepartmentModels.DepartmentCreateInput input);
```
#### Parameters

<a name='AlifeApi.WebApi.Controllers.DepartmentController.CreateDepartmentAsync(AlifeApi.BusinessRules.DepartmentModels.DepartmentCreateInput).input'></a>

`input` [AlifeApi.BusinessRules.DepartmentModels.DepartmentCreateInput](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.BusinessRules.DepartmentModels.DepartmentCreateInput 'AlifeApi.BusinessRules.DepartmentModels.DepartmentCreateInput')

新增部門輸入資料

#### Returns
[System.Threading.Tasks.Task&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')[Microsoft.AspNetCore.Mvc.ActionResult](https://docs.microsoft.com/en-us/dotnet/api/Microsoft.AspNetCore.Mvc.ActionResult 'Microsoft.AspNetCore.Mvc.ActionResult')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')  
執行結果

<a name='AlifeApi.WebApi.Controllers.DepartmentController.DeleteDepartmentAsync(string,string)'></a>

## DepartmentController.DeleteDepartmentAsync(string, string) Method

刪除部門

```csharp
public System.Threading.Tasks.Task<Microsoft.AspNetCore.Mvc.ActionResult<AlifeApi.BusinessRules.UserInfoModels.UserInfoUpdateOutput>> DeleteDepartmentAsync(string companyId, string departmentId);
```
#### Parameters

<a name='AlifeApi.WebApi.Controllers.DepartmentController.DeleteDepartmentAsync(string,string).companyId'></a>

`companyId` [System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

公司ID

<a name='AlifeApi.WebApi.Controllers.DepartmentController.DeleteDepartmentAsync(string,string).departmentId'></a>

`departmentId` [System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

部門ID

#### Returns
[System.Threading.Tasks.Task&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')[Microsoft.AspNetCore.Mvc.ActionResult&lt;](https://docs.microsoft.com/en-us/dotnet/api/Microsoft.AspNetCore.Mvc.ActionResult-1 'Microsoft.AspNetCore.Mvc.ActionResult`1')[AlifeApi.BusinessRules.UserInfoModels.UserInfoUpdateOutput](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.BusinessRules.UserInfoModels.UserInfoUpdateOutput 'AlifeApi.BusinessRules.UserInfoModels.UserInfoUpdateOutput')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/Microsoft.AspNetCore.Mvc.ActionResult-1 'Microsoft.AspNetCore.Mvc.ActionResult`1')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')  
執行結果訊息

<a name='AlifeApi.WebApi.Controllers.DepartmentController.GetDepartmentByIdAsync(string,string)'></a>

## DepartmentController.GetDepartmentByIdAsync(string, string) Method

根據公司ID和部門ID取得單一部門資訊

```csharp
public System.Threading.Tasks.Task<Microsoft.AspNetCore.Mvc.ActionResult<AlifeApi.BusinessRules.DepartmentModels.DepartmentOutput>> GetDepartmentByIdAsync(string companyId, string departmentId);
```
#### Parameters

<a name='AlifeApi.WebApi.Controllers.DepartmentController.GetDepartmentByIdAsync(string,string).companyId'></a>

`companyId` [System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

公司ID

<a name='AlifeApi.WebApi.Controllers.DepartmentController.GetDepartmentByIdAsync(string,string).departmentId'></a>

`departmentId` [System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

部門ID

#### Returns
[System.Threading.Tasks.Task&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')[Microsoft.AspNetCore.Mvc.ActionResult&lt;](https://docs.microsoft.com/en-us/dotnet/api/Microsoft.AspNetCore.Mvc.ActionResult-1 'Microsoft.AspNetCore.Mvc.ActionResult`1')[AlifeApi.BusinessRules.DepartmentModels.DepartmentOutput](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.BusinessRules.DepartmentModels.DepartmentOutput 'AlifeApi.BusinessRules.DepartmentModels.DepartmentOutput')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/Microsoft.AspNetCore.Mvc.ActionResult-1 'Microsoft.AspNetCore.Mvc.ActionResult`1')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')  
部門詳細資訊

<a name='AlifeApi.WebApi.Controllers.DepartmentController.GetDepartmentDropdownListAsync(AlifeApi.BusinessRules.DepartmentModels.DepartmentDropdownInput)'></a>

## DepartmentController.GetDepartmentDropdownListAsync(DepartmentDropdownInput) Method

取得部門下拉選單

```csharp
public System.Threading.Tasks.Task<Microsoft.AspNetCore.Mvc.ActionResult<System.Collections.Generic.List<AlifeApi.BusinessRules.DepartmentModels.DepartmentDropdownOutput>>> GetDepartmentDropdownListAsync(AlifeApi.BusinessRules.DepartmentModels.DepartmentDropdownInput input);
```
#### Parameters

<a name='AlifeApi.WebApi.Controllers.DepartmentController.GetDepartmentDropdownListAsync(AlifeApi.BusinessRules.DepartmentModels.DepartmentDropdownInput).input'></a>

`input` [AlifeApi.BusinessRules.DepartmentModels.DepartmentDropdownInput](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.BusinessRules.DepartmentModels.DepartmentDropdownInput 'AlifeApi.BusinessRules.DepartmentModels.DepartmentDropdownInput')

查詢條件

#### Returns
[System.Threading.Tasks.Task&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')[Microsoft.AspNetCore.Mvc.ActionResult&lt;](https://docs.microsoft.com/en-us/dotnet/api/Microsoft.AspNetCore.Mvc.ActionResult-1 'Microsoft.AspNetCore.Mvc.ActionResult`1')[System.Collections.Generic.List&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Collections.Generic.List-1 'System.Collections.Generic.List`1')[AlifeApi.BusinessRules.DepartmentModels.DepartmentDropdownOutput](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.BusinessRules.DepartmentModels.DepartmentDropdownOutput 'AlifeApi.BusinessRules.DepartmentModels.DepartmentDropdownOutput')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Collections.Generic.List-1 'System.Collections.Generic.List`1')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/Microsoft.AspNetCore.Mvc.ActionResult-1 'Microsoft.AspNetCore.Mvc.ActionResult`1')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')  
部門下拉選單列表

<a name='AlifeApi.WebApi.Controllers.DepartmentController.GetDepartmentListAsync(AlifeApi.BusinessRules.DepartmentModels.DepartmentListGetInput)'></a>

## DepartmentController.GetDepartmentListAsync(DepartmentListGetInput) Method

取得部門列表 (分頁)

```csharp
public System.Threading.Tasks.Task<Microsoft.AspNetCore.Mvc.ActionResult<AlifeApi.BusinessRules.Infrastructure.PagedListOutput<AlifeApi.BusinessRules.DepartmentModels.DepartmentOutput>>> GetDepartmentListAsync(AlifeApi.BusinessRules.DepartmentModels.DepartmentListGetInput input);
```
#### Parameters

<a name='AlifeApi.WebApi.Controllers.DepartmentController.GetDepartmentListAsync(AlifeApi.BusinessRules.DepartmentModels.DepartmentListGetInput).input'></a>

`input` [AlifeApi.BusinessRules.DepartmentModels.DepartmentListGetInput](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.BusinessRules.DepartmentModels.DepartmentListGetInput 'AlifeApi.BusinessRules.DepartmentModels.DepartmentListGetInput')

查詢條件

#### Returns
[System.Threading.Tasks.Task&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')[Microsoft.AspNetCore.Mvc.ActionResult&lt;](https://docs.microsoft.com/en-us/dotnet/api/Microsoft.AspNetCore.Mvc.ActionResult-1 'Microsoft.AspNetCore.Mvc.ActionResult`1')[AlifeApi.BusinessRules.Infrastructure.PagedListOutput&lt;](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.BusinessRules.Infrastructure.PagedListOutput-1 'AlifeApi.BusinessRules.Infrastructure.PagedListOutput`1')[AlifeApi.BusinessRules.DepartmentModels.DepartmentOutput](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.BusinessRules.DepartmentModels.DepartmentOutput 'AlifeApi.BusinessRules.DepartmentModels.DepartmentOutput')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.BusinessRules.Infrastructure.PagedListOutput-1 'AlifeApi.BusinessRules.Infrastructure.PagedListOutput`1')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/Microsoft.AspNetCore.Mvc.ActionResult-1 'Microsoft.AspNetCore.Mvc.ActionResult`1')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')  
分頁的部門列表

<a name='AlifeApi.WebApi.Controllers.DepartmentController.UpdateDepartmentAsync(string,string,AlifeApi.BusinessRules.DepartmentModels.DepartmentUpdateInput)'></a>

## DepartmentController.UpdateDepartmentAsync(string, string, DepartmentUpdateInput) Method

更新部門資訊

```csharp
public System.Threading.Tasks.Task<Microsoft.AspNetCore.Mvc.ActionResult<AlifeApi.BusinessRules.DepartmentModels.DepartmentOutput>> UpdateDepartmentAsync(string companyId, string departmentId, AlifeApi.BusinessRules.DepartmentModels.DepartmentUpdateInput input);
```
#### Parameters

<a name='AlifeApi.WebApi.Controllers.DepartmentController.UpdateDepartmentAsync(string,string,AlifeApi.BusinessRules.DepartmentModels.DepartmentUpdateInput).companyId'></a>

`companyId` [System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

公司ID

<a name='AlifeApi.WebApi.Controllers.DepartmentController.UpdateDepartmentAsync(string,string,AlifeApi.BusinessRules.DepartmentModels.DepartmentUpdateInput).departmentId'></a>

`departmentId` [System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

部門ID

<a name='AlifeApi.WebApi.Controllers.DepartmentController.UpdateDepartmentAsync(string,string,AlifeApi.BusinessRules.DepartmentModels.DepartmentUpdateInput).input'></a>

`input` [AlifeApi.BusinessRules.DepartmentModels.DepartmentUpdateInput](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.BusinessRules.DepartmentModels.DepartmentUpdateInput 'AlifeApi.BusinessRules.DepartmentModels.DepartmentUpdateInput')

更新部門輸入資料

#### Returns
[System.Threading.Tasks.Task&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')[Microsoft.AspNetCore.Mvc.ActionResult&lt;](https://docs.microsoft.com/en-us/dotnet/api/Microsoft.AspNetCore.Mvc.ActionResult-1 'Microsoft.AspNetCore.Mvc.ActionResult`1')[AlifeApi.BusinessRules.DepartmentModels.DepartmentOutput](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.BusinessRules.DepartmentModels.DepartmentOutput 'AlifeApi.BusinessRules.DepartmentModels.DepartmentOutput')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/Microsoft.AspNetCore.Mvc.ActionResult-1 'Microsoft.AspNetCore.Mvc.ActionResult`1')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')  
更新後的部門資訊