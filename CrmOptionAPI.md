# CRM選項管理 API 文檔

本文檔詳細說明CRM選項管理的 API 使用方式，包括請求格式、回應格式和使用範例。

## API 路由說明

本系統使用 ASP.NET Core 的路由模式：`/api/[controller]/[action]`

- **基礎路徑**: `/api/`
- **控制器名稱**: `CrmOptions`
- **動作名稱**: 對應 Controller 中的方法名稱

因此完整的 API 端點格式為：`/api/CrmOptions/{ActionName}`

## 目錄

- [CRM選項 API](#crm選項-api)
- [錯誤處理](#錯誤處理)
- [使用範例](#使用範例)

---

## CRM選項 API

### 1. 查詢CRM選項列表 (分頁)

**端點**: `POST /api/CrmOptions`

**請求格式**:
```json
{
  "SiteCode": "string (可選)",           // 案場代碼 (篩選條件)
  "CrmOptionTypeId": 1,                 // CRM選項類型ID (篩選條件)
  "OptionValue": "string (可選)",        // 選項值 (模糊查詢)
  "IsActive": true/false/null,          // 是否啟用 (null表示不篩選)
  "PageIndex": 1,                       // 頁碼 (從1開始)
  "PageSize": 10                        // 每頁筆數
}
```

**回應格式**:
```json
{
  "TotalCount": 25,                     // 總筆數
  "PageIndex": 1,                       // 當前頁碼
  "PageSize": 10,                       // 每頁筆數
  "TotalPages": 3,                      // 總頁數
  "Details": [
    {
      "SiteCrmOptionId": 1,             // CRM選項ID
      "SiteCode": "A001",               // 案場代碼
      "SiteName": "信義帝寶",            // 案場名稱
      "CrmOptionTypeId": 1,             // CRM選項類型ID
      "CrmOptionTypeName": "需求坪數",   // CRM選項類型名稱
      "OptionValue": "20-30坪",         // 選項值
      "SortOrder": 1,                   // 排序順序
      "IsActive": true,                 // 是否啟用
      "CreateTime": "2024-01-01T10:00:00" // 建立時間
    }
  ]
}
```

### 2. 取得CRM選項詳細資料

**端點**: `GET /api/CrmOptions/{siteCrmOptionId}`

**路徑參數**:
- `siteCrmOptionId`: CRM選項ID (long)

**回應格式**:
```json
{
  "SiteCrmOptionId": 1,
  "SiteCode": "A001",
  "SiteName": "信義帝寶",
  "CrmOptionTypeId": 1,
  "CrmOptionTypeName": "需求坪數",
  "OptionValue": "20-30坪",
  "SortOrder": 1,
  "IsActive": true,
  "CreateTime": "2024-01-01T10:00:00",
  "UpdateTime": "2024-01-01T10:00:00",
  "CreatedUserInfoId": "user001",
  "CreatedUserName": "張三",
  "UpdatedUserInfoId": "user001",
  "UpdatedUserName": "張三"
}
```

### 3. 新增CRM選項

**端點**: `POST /api/CrmOptions/create`

**請求格式**:
```json
{
  "SiteCode": "A001",                   // 必填，案場代碼
  "CrmOptionTypeId": 1,                 // 必填，CRM選項類型ID
  "OptionValue": "20-30坪",             // 必填，選項值，最大200字元
  "SortOrder": 1,                       // 必填，排序順序，非負整數
  "IsActive": true                      // 可選，是否啟用，預設true
}
```

**回應格式**:
```json
{
  "SiteCrmOptionId": 1                  // 新建的CRM選項ID
}
```

### 4. 更新CRM選項

**端點**: `PUT /api/CrmOptions/{siteCrmOptionId}`

**路徑參數**:
- `siteCrmOptionId`: CRM選項ID (long)

**請求格式**:
```json
{
  "OptionValue": "25-35坪",             // 必填，選項值，最大200字元
  "SortOrder": 2,                       // 必填，排序順序，非負整數
  "IsActive": true                      // 必填，是否啟用
}
```

**回應**: HTTP 204 No Content (成功) 或錯誤訊息

### 5. 刪除CRM選項

**端點**: `DELETE /api/CrmOptions/{siteCrmOptionId}`

**路徑參數**:
- `siteCrmOptionId`: CRM選項ID (long)

**回應**: HTTP 204 No Content (成功) 或錯誤訊息

### 6. 取得CRM選項下拉選單

**端點**: `POST /api/CrmOptions/dropdown`

**說明**: 根據案場代碼和CRM選項類型ID取得對應的下拉選單選項

**請求格式**:
```json
{
  "SiteCode": "A001",                   // 必填，案場代碼
  "CrmOptionTypeId": 1,                 // 必填，CRM選項類型ID
  "OnlyActive": true                    // 可選，是否只取得啟用的選項，預設true
}
```

**回應格式**:
```json
[
  {
    "Name": "20-30坪",                  // 選項值 (顯示文字)
    "Value": 1,                         // 選項ID (選項值)
    "SortOrder": 1                      // 排序順序
  },
  {
    "Name": "30-40坪",
    "Value": 2,
    "SortOrder": 2
  }
]
```

### 7. 取得CRM選項類型下拉選單

**端點**: `GET /api/CrmOptions/types`

**說明**: 取得所有可用的CRM選項類型，用於建立新的CRM選項時選擇類型

**回應格式**:
```json
[
  {
    "Name": "需求坪數",                      // 選項類型名稱 (顯示文字)
    "Value": 1,                            // 選項類型ID (選項值)
    "Description": "客戶需求的房屋坪數範圍"    // 選項類型描述
  },
  {
    "Name": "需求格局",
    "Value": 2,
    "Description": "客戶需求的房屋格局類型"
  },
  {
    "Name": "預算範圍",
    "Value": 3,
    "Description": "客戶的購屋預算範圍"
  }
]
```

---

## 錯誤處理

### 常見錯誤回應格式

**400 Bad Request** - 請求參數錯誤或業務邏輯錯誤:
```json
{
  "message": "案場 'A001' 在此選項類型下已存在選項值 '20-30坪'。"
}
```

**404 Not Found** - 資源不存在:
```json
{
  "message": "找不到指定的CRM選項 (ID: 999)"
}
```

**500 Internal Server Error** - 伺服器內部錯誤:
```json
{
  "message": "更新CRM選項時發生內部錯誤"
}
```

### 驗證錯誤

當輸入資料不符合驗證規則時，會返回 400 錯誤，常見的驗證錯誤包括：

- 必填欄位為空
- 字串長度超過限制
- 數值超出範圍
- 案場代碼不存在
- CRM選項類型ID不存在
- 同一案場、同一選項類型下選項值重複

---

## 使用範例

### 範例 1: 為案場建立需求坪數選項

```javascript
// 1. 新增第一個坪數選項
const option1 = await fetch('/api/CrmOptions/CreateCrmOption', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    SiteCode: 'A001',
    CrmOptionTypeId: 1,  // 假設1是"需求坪數"類型
    OptionValue: '20-30坪',
    SortOrder: 1,
    IsActive: true
  })
});

// 2. 新增第二個坪數選項
const option2 = await fetch('/api/CrmOptions/CreateCrmOption', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    SiteCode: 'A001',
    CrmOptionTypeId: 1,
    OptionValue: '30-40坪',
    SortOrder: 2,
    IsActive: true
  })
});
```

### 範例 2: 取得案場的需求坪數下拉選單

```javascript
const dropdown = await fetch('/api/CrmOptions/GetCrmOptionDropdown', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    SiteCode: 'A001',
    CrmOptionTypeId: 1,  // 需求坪數類型
    OnlyActive: true
  })
});

const options = await dropdown.json();
// 結果: [{"Name": "20-30坪", "Value": 1, "SortOrder": 1}, ...]
```

### 範例 3: 查詢特定案場的所有CRM選項

```javascript
const result = await fetch('/api/CrmOptions/GetCrmOptions', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    SiteCode: 'A001',
    PageIndex: 1,
    PageSize: 20
  })
});

const crmOptions = await result.json();
// 取得該案場所有CRM選項的分頁列表
```

### 範例 4: 更新選項排序

```javascript
// 更新選項的排序順序
await fetch('/api/CrmOptions/UpdateCrmOption/1', {
  method: 'PUT',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    OptionValue: '20-30坪',
    SortOrder: 5,  // 調整排序
    IsActive: true
  })
});
```

### 範例 5: 取得CRM選項類型下拉選單

```javascript
// 取得所有可用的CRM選項類型
const typeDropdown = await fetch('/api/CrmOptions/GetCrmOptionTypeDropdown', {
  method: 'GET'
});

const optionTypes = await typeDropdown.json();
// 結果: [{"Name": "需求坪數", "Value": 1, "Description": "客戶需求的房屋坪數範圍"}, ...]

// 可用於前端建立選項時的類型選擇
optionTypes.forEach(type => {
  console.log(`類型: ${type.Name} (ID: ${type.Value}) - ${type.Description}`);
});
```

## 業務邏輯說明

1. **案場隔離**: 每個案場可以有自己的CRM選項設定，不同案場間的選項互不影響
2. **選項類型關聯**: 選項必須關聯到有效的CRM選項類型
3. **唯一性約束**: 同一案場、同一選項類型下，選項值必須唯一
4. **排序控制**: 透過 SortOrder 控制下拉選單中選項的顯示順序
5. **啟用狀態**: 可以停用選項而不刪除，方便管理歷史資料
