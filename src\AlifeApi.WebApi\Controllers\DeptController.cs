﻿using AlifeApi.BusinessRules.DeptModels;
using AlifeApi.BusinessRules.Infrastructure;
using Microsoft.AspNetCore.Mvc;

namespace AlifeApi.WebApi.Controllers
{
    /// <summary>
    /// 部門作業
    /// </summary>
    public class DeptController : AuthenticatedController
    {
        //private readonly DeptService _deptService;


        ///// <summary>
        ///// Initializes a new instance of the <see cref="DeptController"/> class.
        ///// </summary>
        ///// <param name="deptService">The role group service.</param>
        ///// <exception cref="ArgumentNullException">roleGroupService</exception>
        //public DeptController(DeptService deptService)
        //{
        //    this._deptService = deptService ?? throw new ArgumentNullException(nameof(deptService));
        //}

        ///// <summary>
        ///// 取得所有部門
        ///// </summary>
        ///// <param name="input">The input.</param>
        ///// <returns>部門作業</returns>
        //[HttpPost]
        //public async Task<PagedListOutput<DeptListResult>> GetDeptListAsync(DeptListCondition input)
        //    => await _deptService.GetDeptList(input);

        ///// <summary>
        ///// 新增部門
        ///// </summary>
        ///// <param name="input">The input.</param>
        ///// <returns>部門作業</returns>
        //[HttpPost]
        //public async Task CreateDeptAsync(CreateDeptCondition input)
        //    => await _deptService.CreateDept(input);

        ///// <summary>
        ///// 變更部門
        ///// </summary>
        ///// <param name="input">The input.</param>
        ///// <returns>部門作業</returns>
        //[HttpPost]
        //public async Task UpdateDeptAsync(UpdateDeptCondition input)
        //    => await _deptService.UpdateDept(input);

        ///// <summary>
        ///// 修改部門權限功能
        ///// </summary>
        ///// <param name="input">The input.</param>
        //[HttpPost]
        //public async Task UpdateDeptPermissionAsync(UpdateDeptPermissionCondition input)
        //    => await _deptService.UpdateDeptPermission(input);

        ///// <summary>
        ///// 刪除部門(如果原本是刪除，打了會復原)
        ///// </summary>
        ///// <param name="input">The input.</param>
        ///// <returns>部門作業</returns>
        //[HttpPost]
        //public async Task DeleteDeptAsync(DeleteDeptCondition input)
        //    => await _deptService.DeleteDept(input);


    }
}
