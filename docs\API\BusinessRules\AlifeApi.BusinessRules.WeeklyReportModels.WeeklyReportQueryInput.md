#### [AlifeApi.BusinessRules](index.md 'index')
### [AlifeApi.BusinessRules.WeeklyReportModels](AlifeApi.BusinessRules.WeeklyReportModels.md 'AlifeApi.BusinessRules.WeeklyReportModels')

## WeeklyReportQueryInput Class

週報查詢輸入DTO

```csharp
public class WeeklyReportQueryInput
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; WeeklyReportQueryInput
### Properties

<a name='AlifeApi.BusinessRules.WeeklyReportModels.WeeklyReportQueryInput.SiteCode'></a>

## WeeklyReportQueryInput.SiteCode Property

案場編號

```csharp
public string SiteCode { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='<PERSON>feA<PERSON>.BusinessRules.WeeklyReportModels.WeeklyReportQueryInput.WeekNumber'></a>

## WeeklyReportQueryInput.WeekNumber Property

週次

```csharp
public int WeekNumber { get; set; }
```

#### Property Value
[System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')

<a name='AlifeApi.BusinessRules.WeeklyReportModels.WeeklyReportQueryInput.Year'></a>

## WeeklyReportQueryInput.Year Property

年份

```csharp
public int Year { get; set; }
```

#### Property Value
[System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')