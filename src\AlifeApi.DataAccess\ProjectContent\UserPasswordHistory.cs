﻿using System;
using System.Collections.Generic;

namespace AlifeApi.DataAccess.ProjectContent
{
    /// <summary>
    /// 密碼歷史記錄表，用於儲存員工的歷史密碼資訊。
    /// </summary>
    public partial class UserPasswordHistory
    {
        /// <summary>
        /// 流水號，主鍵，用於唯一識別密碼記錄。
        /// </summary>
        public long UserPasswordHistoryId { get; set; }
        /// <summary>
        /// 員工編號，對應 UserInfo 表的 UserInfoId。
        /// </summary>
        public string UserInfoId { get; set; }
        /// <summary>
        /// 歷史密碼，儲存加密後的密碼內容。
        /// </summary>
        public string Password { get; set; }
        /// <summary>
        /// 建立者，記錄創建此記錄的員工編號。
        /// </summary>
        public string CreatedUserInfoId { get; set; }
        /// <summary>
        /// 創建時間，記錄密碼記錄創建的時間。
        /// </summary>
        public DateTime CreatedTime { get; set; }
    }
}
