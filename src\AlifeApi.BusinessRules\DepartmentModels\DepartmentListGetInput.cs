using AlifeApi.BusinessRules.Infrastructure;
using System.Text.Json.Serialization;

namespace AlifeApi.BusinessRules.DepartmentModels
{
    /// <summary>
    /// 取得部門列表查詢條件
    /// </summary>
    public class DepartmentListGetInput : PagedListInput // 繼承 PagedListInput 以支援分頁和排序
    {
        /// <summary>
        /// 公司ID (可選篩選條件)
        /// </summary>
        [JsonPropertyName("CompanyId")]
        public string CompanyId { get; set; }

        /// <summary>
        /// 部門名稱 (可選篩選條件)
        /// </summary>
        [JsonPropertyName("Name")]
        public string Name { get; set; }
    }
} 
