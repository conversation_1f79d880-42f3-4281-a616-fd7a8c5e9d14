﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AlifeApi.BusinessRules.ProjectVersionModels
{
    public class SyncProjectVersion : ProjectVersionBase
    {
        /// <summary>
        /// 系統專案版號
        /// </summary>
        [Required]
        public string VersionHash { get; set; }

        /// <summary>
        /// 版本發佈人
        /// </summary>
        [Required]
        public string LastUpdateUser { get; set; }

        /// <summary>
        /// 更新內容
        /// </summary>
        [Required]
        public string Content { get; set; }
    }
}
