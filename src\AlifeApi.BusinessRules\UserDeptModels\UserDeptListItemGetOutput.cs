﻿using System.Text.Json.Serialization;

namespace AlifeApi.BusinessRules.UserDeptModels
{
    public class UserDeptListItemGetOutput
    {
        /// <summary>
        /// 部門代號
        /// </summary>
        [JsonPropertyName("Dept")]
        public string DeptId { get; set; }

        /// <summary>
        /// 部門代號名稱
        /// </summary>
        public string DeptName { get; set; }

        /// <summary>
        /// 上層部門代號
        /// </summary>
        [JsonPropertyName("ParentDept")]
        public string ParentDeptId { get; set; }

        /// <summary>
        /// 上層部門代號名稱
        /// </summary>
        public string ParentDeptName { get; set; }

        /// <summary>
        /// 部門主管員工編號
        /// </summary>
        public string LeaderUserId { get; set; }

        /// <summary>
        /// 部門主管員工姓名
        /// </summary>
        public string LeaderUserName { get; set; }

        /// <summary>
        /// 是否啟用
        /// </summary>
        public bool IsDisabled { get; set; }
    }
}
