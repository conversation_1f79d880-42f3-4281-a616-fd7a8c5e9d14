#### [AlifeApi.BusinessRules](index.md 'index')
### [AlifeApi.BusinessRules.DailyReportModels](AlifeApi.BusinessRules.DailyReportModels.md 'AlifeApi.BusinessRules.DailyReportModels')

## DailyReportSummary Class

日報摘要資訊

```csharp
public class DailyReportSummary
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; DailyReportSummary
### Properties

<a name='AlifeApi.BusinessRules.DailyReportModels.DailyReportSummary.CallConversionRate'></a>

## DailyReportSummary.CallConversionRate Property

來電轉換率

```csharp
public decimal CallConversionRate { get; set; }
```

#### Property Value
[System.Decimal](https://docs.microsoft.com/en-us/dotnet/api/System.Decimal 'System.Decimal')

<a name='AlifeApi.BusinessRules.DailyReportModels.DailyReportSummary.CallCount'></a>

## DailyReportSummary.CallCount Property

來電數

```csharp
public int CallCount { get; set; }
```

#### Property Value
[System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')

<a name='AlifeApi.BusinessRules.DailyReportModels.DailyReportSummary.DayOfWeek'></a>

## DailyReportSummary.DayOfWeek Property

星期幾

```csharp
public string DayOfWeek { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.DailyReportModels.DailyReportSummary.LeadCount'></a>

## DailyReportSummary.LeadCount Property

留單數

```csharp
public int LeadCount { get; set; }
```

#### Property Value
[System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')

<a name='AlifeApi.BusinessRules.DailyReportModels.DailyReportSummary.ReportDate'></a>

## DailyReportSummary.ReportDate Property

日報日期

```csharp
public System.DateTime ReportDate { get; set; }
```

#### Property Value
[System.DateTime](https://docs.microsoft.com/en-us/dotnet/api/System.DateTime 'System.DateTime')

<a name='AlifeApi.BusinessRules.DailyReportModels.DailyReportSummary.TransactionConversionRate'></a>

## DailyReportSummary.TransactionConversionRate Property

成交轉換率

```csharp
public decimal TransactionConversionRate { get; set; }
```

#### Property Value
[System.Decimal](https://docs.microsoft.com/en-us/dotnet/api/System.Decimal 'System.Decimal')

<a name='AlifeApi.BusinessRules.DailyReportModels.DailyReportSummary.TransactionCount'></a>

## DailyReportSummary.TransactionCount Property

成交數

```csharp
public int TransactionCount { get; set; }
```

#### Property Value
[System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')

<a name='AlifeApi.BusinessRules.DailyReportModels.DailyReportSummary.VisitorConversionRate'></a>

## DailyReportSummary.VisitorConversionRate Property

來客轉換率

```csharp
public decimal VisitorConversionRate { get; set; }
```

#### Property Value
[System.Decimal](https://docs.microsoft.com/en-us/dotnet/api/System.Decimal 'System.Decimal')

<a name='AlifeApi.BusinessRules.DailyReportModels.DailyReportSummary.VisitorCount'></a>

## DailyReportSummary.VisitorCount Property

來客數

```csharp
public int VisitorCount { get; set; }
```

#### Property Value
[System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')