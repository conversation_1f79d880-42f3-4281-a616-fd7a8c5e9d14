﻿CREATE TABLE [dbo].[SYS_RoleGroupPermission] (
    [System]      VARCHAR (30) NOT NULL,
    [RoleGroupId] VARCHAR (50) NOT NULL,
    [FuncId]      VARCHAR (50) NOT NULL,
    CONSTRAINT [PK_SYS_RoleGroupPermission_1] PRIMARY KEY CLUSTERED ([System] ASC, [RoleGroupId] ASC, [FuncId] ASC),
    CONSTRAINT [FK_SYS_RoleGroupPermission_SYS_RoleGroup] FOREIGN KEY ([System], [RoleGroupId]) REFERENCES [dbo].[SYS_RoleGroup] ([System], [RoleGroupId]) ON DELETE CASCADE,
    CONSTRAINT [FK_SYS_RolePermission_SYS_MenuFunc] FOREIGN KEY ([System], [FuncId]) REFERENCES [dbo].[SYS_MenuFunc] ([System], [FuncId]) ON DELETE CASCADE
);


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'系統名稱', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'SYS_RoleGroupPermission', @level2type = N'COLUMN', @level2name = N'System';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'角色群組識別編號', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'SYS_RoleGroupPermission', @level2type = N'COLUMN', @level2name = N'RoleGroupId';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'系統項目識別編號', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'SYS_RoleGroupPermission', @level2type = N'COLUMN', @level2name = N'FuncId';

