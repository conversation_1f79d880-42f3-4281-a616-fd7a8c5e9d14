using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using AlifeApi.BusinessRules.Infrastructure;
using AlifeApi.DataAccess.ProjectContent;
using Microsoft.EntityFrameworkCore;

namespace AlifeApi.BusinessRules.CategoryModels
{
    /// <summary>
    /// 中分類服務
    /// </summary>
    public class MediumCategoryService : ServiceBase<alifeContext>
    {
        public MediumCategoryService(IServiceProvider serviceProvider, alifeContext dbContext)
            : base(serviceProvider, dbContext)
        {
        }

        /// <summary>
        /// 建立中分類
        /// </summary>
        /// <param name="input">中分類建立輸入資料</param>
        /// <returns>新建中分類的ID</returns>
        public async Task<long> CreateMediumCategoryAsync(MediumCategoryInput input)
        {
            ArgumentNullException.ThrowIfNull(input);

            // 檢查大分類是否存在
            if (!await Db.LargeCategories.AnyAsync(c => c.LargeCategoryId == input.LargeCategoryId))
            {
                throw new Exception($"指定的大分類 (ID: {input.LargeCategoryId}) 不存在。");
            }

            // 檢查在同一大分類下名稱是否重複
            if (await Db.MediumCategories.AnyAsync(c => c.LargeCategoryId == input.LargeCategoryId && c.Name == input.Name))
            {
                throw new Exception($"在指定大分類下，中分類名稱 '{input.Name}' 已存在。");
            }

            var mediumCategory = new MediumCategory
            {
                LargeCategoryId = input.LargeCategoryId,
                Name = input.Name,
                Description = input.Description,
                SortOrder = input.SortOrder,
                IsActive = input.IsActive,
                CreateTime = DateTime.Now,
                UpdateTime = DateTime.Now,
                CreatedUserInfoId = CurrentUser.UserId,
                UpdatedUserInfoId = CurrentUser.UserId
            };

            Db.MediumCategories.Add(mediumCategory);
            await Db.SaveChangesAsync();

            return mediumCategory.MediumCategoryId;
        }

        /// <summary>
        /// 取得中分類列表 (分頁)
        /// </summary>
        /// <param name="input">查詢條件</param>
        /// <returns>分頁的中分類列表</returns>
        public async Task<PagedListOutput<MediumCategoryListOutput>> GetMediumCategoryListAsync(MediumCategoryQueryInput input)
        {
            ArgumentNullException.ThrowIfNull(input);

            var query = from m in Db.MediumCategories
                        join l in Db.LargeCategories on m.LargeCategoryId equals l.LargeCategoryId
                        select new { MediumCategory = m, LargeCategory = l };

            // 篩選大分類ID
            if (input.LargeCategoryId.HasValue)
            {
                query = query.Where(x => x.MediumCategory.LargeCategoryId == input.LargeCategoryId.Value);
            }

            // 篩選名稱 (模糊查詢)
            if (!string.IsNullOrEmpty(input.Name))
            {
                query = query.Where(x => x.MediumCategory.Name.Contains(input.Name));
            }

            // 篩選是否啟用
            if (input.IsActive.HasValue)
            {
                query = query.Where(x => x.MediumCategory.IsActive == input.IsActive.Value);
            }

            var pagedResult = await query
                .OrderBy(x => x.LargeCategory.SortOrder)
                .ThenBy(x => x.MediumCategory.SortOrder)
                .ThenBy(x => x.MediumCategory.CreateTime)
                .Select(x => new MediumCategoryListOutput
                {
                    MediumCategoryId = x.MediumCategory.MediumCategoryId,
                    LargeCategoryId = x.MediumCategory.LargeCategoryId,
                    LargeCategoryName = x.LargeCategory.Name,
                    Name = x.MediumCategory.Name,
                    Description = x.MediumCategory.Description,
                    SortOrder = x.MediumCategory.SortOrder,
                    IsActive = x.MediumCategory.IsActive,
                    CreateTime = x.MediumCategory.CreateTime
                })
                .ToPagedListOutputAsync(input);

            return pagedResult;
        }

        /// <summary>
        /// 根據ID取得中分類詳細資料
        /// </summary>
        /// <param name="mediumCategoryId">中分類ID</param>
        /// <returns>中分類詳細資料</returns>
        public async Task<MediumCategoryOutput?> GetMediumCategoryByIdAsync(long mediumCategoryId)
        {
            var mediumCategory = await (from m in Db.MediumCategories
                                        join l in Db.LargeCategories on m.LargeCategoryId equals l.LargeCategoryId
                                        where m.MediumCategoryId == mediumCategoryId
                                        select new MediumCategoryOutput
                                        {
                                            MediumCategoryId = m.MediumCategoryId,
                                            LargeCategoryId = m.LargeCategoryId,
                                            LargeCategoryName = l.Name,
                                            Name = m.Name,
                                            Description = m.Description,
                                            SortOrder = m.SortOrder,
                                            IsActive = m.IsActive,
                                            CreateTime = m.CreateTime,
                                            CreatedUserInfoId = m.CreatedUserInfoId,
                                            UpdateTime = m.UpdateTime,
                                            UpdatedUserInfoId = m.UpdatedUserInfoId
                                        })
                                        .FirstOrDefaultAsync();

            if (mediumCategory == null)
            {
                return null;
            }

            // 查詢並填入建立者和更新者名稱
            var userIds = new[] { mediumCategory.CreatedUserInfoId, mediumCategory.UpdatedUserInfoId }.Distinct().ToList();
            var users = await Db.UserInfos
                .Where(u => userIds.Contains(u.UserInfoId))
                .Select(u => new { u.UserInfoId, u.Name })
                .ToListAsync();
            var userMap = users.ToDictionary(u => u.UserInfoId, u => u.Name);

            if (userMap.TryGetValue(mediumCategory.CreatedUserInfoId, out var createdName))
            {
                mediumCategory.CreatedUserName = createdName;
            }
            if (userMap.TryGetValue(mediumCategory.UpdatedUserInfoId, out var updatedName))
            {
                mediumCategory.UpdatedUserName = updatedName;
            }

            return mediumCategory;
        }

        /// <summary>
        /// 更新中分類資訊
        /// </summary>
        /// <param name="mediumCategoryId">中分類ID</param>
        /// <param name="input">中分類更新輸入資料</param>
        public async Task UpdateMediumCategoryAsync(long mediumCategoryId, MediumCategoryInput input)
        {
            ArgumentNullException.ThrowIfNull(input);

            var mediumCategory = await Db.MediumCategories.FindAsync(mediumCategoryId);

            if (mediumCategory == null)
            {
                throw new Exception($"找不到指定的中分類 (ID: {mediumCategoryId})");
            }

            // 檢查大分類是否存在
            if (!await Db.LargeCategories.AnyAsync(c => c.LargeCategoryId == input.LargeCategoryId))
            {
                throw new Exception($"指定的大分類 (ID: {input.LargeCategoryId}) 不存在。");
            }

            // 檢查在同一大分類下名稱是否重複 (排除自己)
            if (await Db.MediumCategories.AnyAsync(c => c.LargeCategoryId == input.LargeCategoryId && c.Name == input.Name && c.MediumCategoryId != mediumCategoryId))
            {
                throw new Exception($"在指定大分類下，中分類名稱 '{input.Name}' 已存在。");
            }

            mediumCategory.LargeCategoryId = input.LargeCategoryId;
            mediumCategory.Name = input.Name;
            mediumCategory.Description = input.Description;
            mediumCategory.SortOrder = input.SortOrder;
            mediumCategory.IsActive = input.IsActive;
            mediumCategory.UpdateTime = DateTime.Now;
            mediumCategory.UpdatedUserInfoId = CurrentUser.UserId;

            await Db.SaveChangesAsync();
        }

        /// <summary>
        /// 刪除中分類
        /// </summary>
        /// <param name="mediumCategoryId">中分類ID</param>
        public async Task DeleteMediumCategoryAsync(long mediumCategoryId)
        {
            var mediumCategory = await Db.MediumCategories.FindAsync(mediumCategoryId);

            if (mediumCategory == null)
            {
                throw new Exception($"找不到指定的中分類 (ID: {mediumCategoryId})");
            }

            // 檢查是否有關聯的小分類
            var hasSmallCategories = await Db.SmallCategories.AnyAsync(s => s.MediumCategoryId == mediumCategoryId);
            if (hasSmallCategories)
            {
                throw new Exception("無法刪除中分類，因為存在關聯的小分類。");
            }

            Db.MediumCategories.Remove(mediumCategory);
            await Db.SaveChangesAsync();
        }

        /// <summary>
        /// 取得中分類下拉選單列表
        /// </summary>
        /// <param name="largeCategoryId">可選的大分類ID，用於篩選特定大分類下的中分類</param>
        /// <returns>啟用的中分類下拉選單列表</returns>
        public async Task<List<MediumCategoryDropdownOutput>> GetMediumCategoryDropdownAsync(long? largeCategoryId = null)
        {
            var query = from m in Db.MediumCategories
                        join l in Db.LargeCategories on m.LargeCategoryId equals l.LargeCategoryId
                        where m.IsActive == true && l.IsActive == true
                        select new { MediumCategory = m, LargeCategory = l };

            // 如果指定了大分類ID，則篩選該大分類下的中分類
            if (largeCategoryId.HasValue)
            {
                query = query.Where(x => x.MediumCategory.LargeCategoryId == largeCategoryId.Value);
            }

            var result = await query
                .OrderBy(x => x.LargeCategory.SortOrder)
                .ThenBy(x => x.MediumCategory.SortOrder)
                .ThenBy(x => x.MediumCategory.CreateTime)
                .Select(x => new MediumCategoryDropdownOutput
                {
                    MediumCategoryId = x.MediumCategory.MediumCategoryId,
                    LargeCategoryId = x.MediumCategory.LargeCategoryId,
                    LargeCategoryName = x.LargeCategory.Name,
                    Name = x.MediumCategory.Name,
                    SortOrder = x.MediumCategory.SortOrder
                })
                .ToListAsync();

            return result;
        }
    }
} 
