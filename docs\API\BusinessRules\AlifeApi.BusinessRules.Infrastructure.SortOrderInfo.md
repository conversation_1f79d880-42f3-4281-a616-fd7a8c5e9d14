#### [AlifeApi.BusinessRules](index.md 'index')
### [AlifeApi.BusinessRules.Infrastructure](AlifeApi.BusinessRules.Infrastructure.md 'AlifeApi.BusinessRules.Infrastructure')

## SortOrderInfo Class

排序資訊

```csharp
public class SortOrderInfo
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; SortOrderInfo
### Properties

<a name='AlifeApi.BusinessRules.Infrastructure.SortOrderInfo.SortField'></a>

## SortOrderInfo.SortField Property

排序欄位

```csharp
public string SortField { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.Infrastructure.SortOrderInfo.SortOrder'></a>

## SortOrderInfo.SortOrder Property

排序方式(asc、desc)

```csharp
public string SortOrder { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')