﻿using System;
using System.Text.Json.Serialization;

namespace AlifeApi.BusinessRules.CustomerModels
{
    /// <summary>
    /// 客戶訪談紀錄輸出 DTO
    /// </summary>
    public class CustomerRecordOutput
    {
        /// <summary>
        /// 記錄唯一識別碼
        /// </summary>
        [JsonPropertyName("CustomerRecordId")]
        public int CustomerRecordId { get; set; }

        /// <summary>
        /// 互動或記錄的類型
        /// </summary>
        [JsonPropertyName("RecordType")]
        public string RecordType { get; set; }

        /// <summary>
        /// 詳細的記錄內容
        /// </summary>
        [JsonPropertyName("Notes")]
        public string Notes { get; set; }

        /// <summary>
        /// 互動發生的實際時間或記錄時間
        /// </summary>
        [JsonPropertyName("RecordedAt")]
        public DateTime RecordedAt { get; set; }

        /// <summary>
        /// 客戶分級或標籤
        /// </summary>
        [JsonPropertyName("CustomerLevel")]
        public string CustomerLevel { get; set; }

        /// <summary>
        /// 負責此次互動的人員資訊
        /// </summary>
        [JsonPropertyName("HandledBy")]
        public string HandledBy { get; set; }

        /// <summary>
        /// 此記錄創建人員的 UserInfo ID
        /// </summary>
        [JsonPropertyName("CreateUserInfoId")]
        public string CreateUserInfoId { get; set; }

        /// <summary>
        /// 此記錄的創建時間
        /// </summary>
        [JsonPropertyName("CreatedTime")]
        public DateTime CreatedTime { get; set; }

        // 可以根據需要選擇性地加入更新資訊
        // /// <summary>
        // /// 此記錄最後更新人員的 UserInfo ID
        // /// </summary>
        // [JsonPropertyName("UpdateUserInfoId")]
        // public string UpdateUserInfoId { get; set; }

        // /// <summary>
        // /// 此記錄的最後更新時間
        // /// </summary>
        // [JsonPropertyName("UpdateTime")]
        // public DateTime UpdateTime { get; set; }
    }
} 
