#### [<PERSON>feApi.WebApi](index.md 'index')
### [AlifeApi.WebApi.Controllers](AlifeApi.WebApi.Controllers.md 'AlifeApi.WebApi.Controllers')

## CompanyController Class

公司管理控制器

```csharp
public class CompanyController : AlifeApi.WebApi.Controllers.AuthenticatedController
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; [Microsoft.AspNetCore.Mvc.ControllerBase](https://docs.microsoft.com/en-us/dotnet/api/Microsoft.AspNetCore.Mvc.ControllerBase 'Microsoft.AspNetCore.Mvc.ControllerBase') &#129106; [Microsoft.AspNetCore.Mvc.Controller](https://docs.microsoft.com/en-us/dotnet/api/Microsoft.AspNetCore.Mvc.Controller 'Microsoft.AspNetCore.Mvc.Controller') &#129106; [AlifeApi.WebApi.Controllers.BasicController](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.WebApi.Controllers.BasicController 'AlifeApi.WebApi.Controllers.BasicController') &#129106; [AlifeApi.WebApi.Controllers.AuthenticatedController](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.WebApi.Controllers.AuthenticatedController 'AlifeApi.WebApi.Controllers.AuthenticatedController') &#129106; CompanyController
### Constructors

<a name='AlifeApi.WebApi.Controllers.CompanyController.CompanyController(AlifeApi.BusinessRules.CompanyModels.CompanyService)'></a>

## CompanyController(CompanyService) Constructor

建構函數

```csharp
public CompanyController(AlifeApi.BusinessRules.CompanyModels.CompanyService companyService);
```
#### Parameters

<a name='AlifeApi.WebApi.Controllers.CompanyController.CompanyController(AlifeApi.BusinessRules.CompanyModels.CompanyService).companyService'></a>

`companyService` [AlifeApi.BusinessRules.CompanyModels.CompanyService](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.BusinessRules.CompanyModels.CompanyService 'AlifeApi.BusinessRules.CompanyModels.CompanyService')
### Methods

<a name='AlifeApi.WebApi.Controllers.CompanyController.GetCompanyDropdownListAsync()'></a>

## CompanyController.GetCompanyDropdownListAsync() Method

取得公司下拉選單

```csharp
public System.Threading.Tasks.Task<Microsoft.AspNetCore.Mvc.ActionResult<System.Collections.Generic.List<AlifeApi.BusinessRules.CompanyModels.CompanyDropdownOutput>>> GetCompanyDropdownListAsync();
```

#### Returns
[System.Threading.Tasks.Task&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')[Microsoft.AspNetCore.Mvc.ActionResult&lt;](https://docs.microsoft.com/en-us/dotnet/api/Microsoft.AspNetCore.Mvc.ActionResult-1 'Microsoft.AspNetCore.Mvc.ActionResult`1')[System.Collections.Generic.List&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Collections.Generic.List-1 'System.Collections.Generic.List`1')[AlifeApi.BusinessRules.CompanyModels.CompanyDropdownOutput](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.BusinessRules.CompanyModels.CompanyDropdownOutput 'AlifeApi.BusinessRules.CompanyModels.CompanyDropdownOutput')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Collections.Generic.List-1 'System.Collections.Generic.List`1')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/Microsoft.AspNetCore.Mvc.ActionResult-1 'Microsoft.AspNetCore.Mvc.ActionResult`1')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')  
公司下拉選單列表