﻿using AlifeApi.BusinessRules.UnitModels;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Linq;
using System.Threading.Tasks;
using AlifeApi.Common.DependencyInjection;
using AlifeApi.BusinessRules.Infrastructure;
using System.Collections.Generic;

namespace AlifeApi.WebApi.Controllers
{
    /// <summary>
    /// 房屋與車位銷售管理控制器
    /// </summary>
    public class UnitsController : AuthenticatedController
    {
        private readonly UnitService _unitService;

        public UnitsController(UnitService unitService)
        {
            _unitService = unitService ?? throw new ArgumentNullException(nameof(unitService));
        }

        /// <summary>
        /// 取得房屋單位列表 (分頁)
        /// </summary>
        [HttpPost]
        public async Task<ActionResult<PagedListOutput<UnitOutput>>> GetUnits([FromBody] UnitListInput input)
        {
            try
            {
                var result = await _unitService.GetListAsync(input);
                return Ok(result);
            }
            catch (Exception ex)
            {
                return StatusCode(500, $"取得列表時發生錯誤: {ex.Message}");
            }
        }

        /// <summary>
        /// 根據 ID 取得單一房屋單位
        /// </summary>
        [HttpGet("{id}")]
        public async Task<ActionResult<UnitOutput>> Get(int id)
        {
            try
            {
                var unit = await _unitService.GetAsync(id);
                if (unit == null)
                {
                    return NotFound(new { message = $"找不到 ID 為 {id} 的房屋單位。" });
                }
                return Ok(unit);
            }
            catch (Exception ex)
            {
                return StatusCode(500, $"取得資料時發生錯誤: {ex.Message}");
            }
        }

        /// <summary>
        /// 建立新的房屋單位
        /// </summary>
        [HttpPost]
        public async Task<IActionResult> Create([FromBody] UnitCreateInput input)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            try
            {
                var createdUnit = await _unitService.CreateAsync(input);
                return CreatedAtAction(nameof(Get), new { id = createdUnit.UnitId }, createdUnit);
            }
            catch (Exception ex)
            {
                return BadRequest(new { message = ex.Message });
            }
        }

        /// <summary>
        /// 更新現有的房屋單位
        /// </summary>
        [HttpPut("{id}")]
        public async Task<IActionResult> Update(int id, [FromBody] UnitUpdateInput input)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            try
            {
                await _unitService.UpdateAsync(id, input);
                return NoContent();
            }
            catch (KeyNotFoundException ex)
            {
                return NotFound(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                return BadRequest(new { message = ex.Message });
            }
        }

        /// <summary>
        /// 刪除房屋單位
        /// </summary>
        [HttpDelete("{id}")]
        public async Task<IActionResult> Delete(int id)
        {
            try
            {
                await _unitService.DeleteAsync(id);
                return NoContent();
            }
            catch (KeyNotFoundException ex)
            {
                return NotFound(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                return BadRequest(new { message = ex.Message });
            }
        }

        [HttpPost("import-parking-spaces")]
        public async Task<IActionResult> ImportParkingSpaces([FromBody] ImportDataInput input)
        {
            if (input == null || !input.Data.Any())
            {
                return BadRequest("匯入資料不能為空。");
            }

            try
            {
                await _unitService.ImportParkingSpacesAsync(input);
                return Ok(new { message = "車位資料匯入成功。" });
            }
            catch (Exception ex)
            {
                return StatusCode(500, $"匯入過程中發生錯誤: {ex.Message}");
            }
        }
    }
} 
