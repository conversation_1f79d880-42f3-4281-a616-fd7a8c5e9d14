using System;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using AlifeApi.BusinessRules.UnitModels;
using AlifeApi.BusinessRules.Infrastructure;

namespace AlifeApi.WebApi.Controllers
{
    /// <summary>
    /// 房屋單位管理
    /// </summary>
    public class UnitsController : AuthenticatedController
    {
        private readonly UnitService _unitService;

        public UnitsController(UnitService unitService)
        {
            _unitService = unitService ?? throw new ArgumentNullException(nameof(unitService));
        }

        /// <summary>
        /// 取得房屋單位列表 (分頁)
        /// </summary>
        /// <param name="input">查詢條件</param>
        /// <returns>分頁的房屋單位列表</returns>
        [HttpPost]
        public async Task<ActionResult<PagedListOutput<UnitListOutput>>> GetUnits([FromBody] UnitQueryInput input)
        {
            var result = await _unitService.GetUnitListAsync(input);
            return Ok(result);
        }

        /// <summary>
        /// 根據房屋單位ID取得詳細資訊
        /// </summary>
        /// <param name="unitId">房屋單位ID</param>
        /// <returns>房屋單位詳細資訊</returns>
        [HttpGet("{unitId}")]
        public async Task<ActionResult<UnitOutput>> GetUnit(int unitId)
        {
            var result = await _unitService.GetUnitByIdAsync(unitId);
            if (result == null)
                return NotFound();
            return Ok(result);
        }

        /// <summary>
        /// 新增房屋單位
        /// </summary>
        /// <param name="input">房屋單位建立輸入資料</param>
        /// <returns>新增的房屋單位ID</returns>
        [HttpPost]
        public async Task<ActionResult<int>> CreateUnit([FromBody] UnitCreateInput input)
        {
            try
            {
                var id = await _unitService.CreateUnitAsync(input);
                return CreatedAtAction(nameof(GetUnit), new { unitId = id }, new { UnitId = id });
            }
            catch (Exception ex)
            {
                return BadRequest(new { message = ex.Message });
            }
        }

        /// <summary>
        /// 更新房屋單位
        /// </summary>
        /// <param name="unitId">房屋單位ID</param>
        /// <param name="input">房屋單位更新輸入資料</param>
        /// <returns>NoContent</returns>
        [HttpPut("{unitId}")]
        public async Task<ActionResult> UpdateUnit(int unitId, [FromBody] UnitUpdateInput input)
        {
            try
            {
                await _unitService.UpdateUnitAsync(unitId, input);
                return NoContent();
            }
            catch (Exception ex)
            {
                return BadRequest(new { message = ex.Message });
            }
        }

        /// <summary>
        /// 刪除房屋單位
        /// </summary>
        /// <param name="unitId">房屋單位ID</param>
        /// <returns>NoContent</returns>
        [HttpDelete("{unitId}")]
        public async Task<ActionResult> DeleteUnit(int unitId)
        {
            try
            {
                await _unitService.DeleteUnitAsync(unitId);
                return NoContent();
            }
            catch (Exception ex)
            {
                return BadRequest(new { message = ex.Message });
            }
        }
    }
} 
