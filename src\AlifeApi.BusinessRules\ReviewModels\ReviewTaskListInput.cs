﻿using AlifeApi.BusinessRules.Infrastructure;
using System;
using System.Text.Json.Serialization;

namespace AlifeApi.BusinessRules.ReviewModels
{
    /// <summary>
    /// 審核流程清單查詢條件
    /// </summary>
    public class ReviewTaskListInput : PagedListInput
    {
        /// <summary>
        /// 流程名稱
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// 資料創建者 UserInfo ID
        /// </summary>
        [JsonPropertyName("CreatedUserInfoId")]
        public string CreatedUserInfoId { get; set; }

        /// <summary>
        /// 狀態
        /// </summary>
        public string Status { get; set; }

        /// <summary>
        /// 開始日期
        /// </summary>
        public DateTime? StartDate { get; set; }

        /// <summary>
        /// 結束日期
        /// </summary>
        public DateTime? EndDate { get; set; }
    }
} 
