#### [AlifeApi.BusinessRules](index.md 'index')
### [AlifeApi.BusinessRules.OwnerModels](AlifeApi.BusinessRules.OwnerModels.md 'AlifeApi.BusinessRules.OwnerModels')

## OwnerUpdateOutput Class

業主更新操作輸出模型

```csharp
public class OwnerUpdateOutput
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; OwnerUpdateOutput
### Properties

<a name='AlifeApi.BusinessRules.OwnerModels.OwnerUpdateOutput.Response'></a>

## OwnerUpdateOutput.Response Property

回應訊息

```csharp
public AlifeApi.BusinessRules.Infrastructure.Message Response { get; set; }
```

#### Property Value
[Message](AlifeApi.BusinessRules.Infrastructure.Message.md 'AlifeApi.BusinessRules.Infrastructure.Message')