#### [AlifeApi.BusinessRules](index.md 'index')
### [AlifeApi.BusinessRules.DepartmentModels](AlifeApi.BusinessRules.DepartmentModels.md 'AlifeApi.BusinessRules.DepartmentModels')

## DepartmentService Class

部門服務

```csharp
public class DepartmentService : AlifeApi.BusinessRules.Infrastructure.ServiceBase<AlifeApi.DataAccess.ProjectContent.alifeContext>
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; [AlifeApi.Common.InfrastructureBase](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.Common.InfrastructureBase 'AlifeApi.Common.InfrastructureBase') &#129106; [AlifeApi.BusinessRules.Infrastructure.ServiceBase&lt;](AlifeApi.BusinessRules.Infrastructure.ServiceBase_TDbContext_.md 'AlifeApi.BusinessRules.Infrastructure.ServiceBase<TDbContext>')[AlifeApi.DataAccess.ProjectContent.alifeContext](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.DataAccess.ProjectContent.alifeContext 'AlifeApi.DataAccess.ProjectContent.alifeContext')[&gt;](AlifeApi.BusinessRules.Infrastructure.ServiceBase_TDbContext_.md 'AlifeApi.BusinessRules.Infrastructure.ServiceBase<TDbContext>') &#129106; DepartmentService
### Constructors

<a name='AlifeApi.BusinessRules.DepartmentModels.DepartmentService.DepartmentService(System.IServiceProvider,AlifeApi.DataAccess.ProjectContent.alifeContext)'></a>

## DepartmentService(IServiceProvider, alifeContext) Constructor

建構函數

```csharp
public DepartmentService(System.IServiceProvider serviceProvider, AlifeApi.DataAccess.ProjectContent.alifeContext dbContext);
```
#### Parameters

<a name='AlifeApi.BusinessRules.DepartmentModels.DepartmentService.DepartmentService(System.IServiceProvider,AlifeApi.DataAccess.ProjectContent.alifeContext).serviceProvider'></a>

`serviceProvider` [System.IServiceProvider](https://docs.microsoft.com/en-us/dotnet/api/System.IServiceProvider 'System.IServiceProvider')

<a name='AlifeApi.BusinessRules.DepartmentModels.DepartmentService.DepartmentService(System.IServiceProvider,AlifeApi.DataAccess.ProjectContent.alifeContext).dbContext'></a>

`dbContext` [AlifeApi.DataAccess.ProjectContent.alifeContext](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.DataAccess.ProjectContent.alifeContext 'AlifeApi.DataAccess.ProjectContent.alifeContext')
### Methods

<a name='AlifeApi.BusinessRules.DepartmentModels.DepartmentService.CreateDepartmentAsync(AlifeApi.BusinessRules.DepartmentModels.DepartmentCreateInput)'></a>

## DepartmentService.CreateDepartmentAsync(DepartmentCreateInput) Method

新增部門

```csharp
public System.Threading.Tasks.Task CreateDepartmentAsync(AlifeApi.BusinessRules.DepartmentModels.DepartmentCreateInput input);
```
#### Parameters

<a name='AlifeApi.BusinessRules.DepartmentModels.DepartmentService.CreateDepartmentAsync(AlifeApi.BusinessRules.DepartmentModels.DepartmentCreateInput).input'></a>

`input` [DepartmentCreateInput](AlifeApi.BusinessRules.DepartmentModels.DepartmentCreateInput.md 'AlifeApi.BusinessRules.DepartmentModels.DepartmentCreateInput')

新增部門輸入資料

#### Returns
[System.Threading.Tasks.Task](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task 'System.Threading.Tasks.Task')

#### Exceptions

[System.ArgumentNullException](https://docs.microsoft.com/en-us/dotnet/api/System.ArgumentNullException 'System.ArgumentNullException')  
input

[System.Exception](https://docs.microsoft.com/en-us/dotnet/api/System.Exception 'System.Exception')  
部門已存在或其他資料庫錯誤

<a name='AlifeApi.BusinessRules.DepartmentModels.DepartmentService.DeleteDepartmentAsync(string,string)'></a>

## DepartmentService.DeleteDepartmentAsync(string, string) Method

刪除部門

```csharp
public System.Threading.Tasks.Task<AlifeApi.BusinessRules.UserInfoModels.UserInfoUpdateOutput> DeleteDepartmentAsync(string companyId, string departmentId);
```
#### Parameters

<a name='AlifeApi.BusinessRules.DepartmentModels.DepartmentService.DeleteDepartmentAsync(string,string).companyId'></a>

`companyId` [System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

公司ID

<a name='AlifeApi.BusinessRules.DepartmentModels.DepartmentService.DeleteDepartmentAsync(string,string).departmentId'></a>

`departmentId` [System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

部門ID

#### Returns
[System.Threading.Tasks.Task&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')[UserInfoUpdateOutput](AlifeApi.BusinessRules.UserInfoModels.UserInfoUpdateOutput.md 'AlifeApi.BusinessRules.UserInfoModels.UserInfoUpdateOutput')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')  
執行結果訊息

#### Exceptions

[System.Exception](https://docs.microsoft.com/en-us/dotnet/api/System.Exception 'System.Exception')  
找不到部門或部門已被使用

<a name='AlifeApi.BusinessRules.DepartmentModels.DepartmentService.GetDepartmentByIdAsync(string,string)'></a>

## DepartmentService.GetDepartmentByIdAsync(string, string) Method

根據公司ID和部門ID取得單一部門資訊

```csharp
public System.Threading.Tasks.Task<AlifeApi.BusinessRules.DepartmentModels.DepartmentOutput> GetDepartmentByIdAsync(string companyId, string departmentId);
```
#### Parameters

<a name='AlifeApi.BusinessRules.DepartmentModels.DepartmentService.GetDepartmentByIdAsync(string,string).companyId'></a>

`companyId` [System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

公司ID

<a name='AlifeApi.BusinessRules.DepartmentModels.DepartmentService.GetDepartmentByIdAsync(string,string).departmentId'></a>

`departmentId` [System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

部門ID

#### Returns
[System.Threading.Tasks.Task&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')[DepartmentOutput](AlifeApi.BusinessRules.DepartmentModels.DepartmentOutput.md 'AlifeApi.BusinessRules.DepartmentModels.DepartmentOutput')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')  
部門詳細資訊

<a name='AlifeApi.BusinessRules.DepartmentModels.DepartmentService.GetDepartmentDropdownListAsync(AlifeApi.BusinessRules.DepartmentModels.DepartmentDropdownInput)'></a>

## DepartmentService.GetDepartmentDropdownListAsync(DepartmentDropdownInput) Method

取得部門下拉選單

```csharp
public System.Threading.Tasks.Task<System.Collections.Generic.List<AlifeApi.BusinessRules.DepartmentModels.DepartmentDropdownOutput>> GetDepartmentDropdownListAsync(AlifeApi.BusinessRules.DepartmentModels.DepartmentDropdownInput input);
```
#### Parameters

<a name='AlifeApi.BusinessRules.DepartmentModels.DepartmentService.GetDepartmentDropdownListAsync(AlifeApi.BusinessRules.DepartmentModels.DepartmentDropdownInput).input'></a>

`input` [DepartmentDropdownInput](AlifeApi.BusinessRules.DepartmentModels.DepartmentDropdownInput.md 'AlifeApi.BusinessRules.DepartmentModels.DepartmentDropdownInput')

查詢條件

#### Returns
[System.Threading.Tasks.Task&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')[System.Collections.Generic.List&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Collections.Generic.List-1 'System.Collections.Generic.List`1')[DepartmentDropdownOutput](AlifeApi.BusinessRules.DepartmentModels.DepartmentDropdownOutput.md 'AlifeApi.BusinessRules.DepartmentModels.DepartmentDropdownOutput')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Collections.Generic.List-1 'System.Collections.Generic.List`1')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')  
部門下拉選單列表

<a name='AlifeApi.BusinessRules.DepartmentModels.DepartmentService.GetDepartmentListAsync(AlifeApi.BusinessRules.DepartmentModels.DepartmentListGetInput)'></a>

## DepartmentService.GetDepartmentListAsync(DepartmentListGetInput) Method

取得部門列表 (分頁)

```csharp
public System.Threading.Tasks.Task<AlifeApi.BusinessRules.Infrastructure.PagedListOutput<AlifeApi.BusinessRules.DepartmentModels.DepartmentOutput>> GetDepartmentListAsync(AlifeApi.BusinessRules.DepartmentModels.DepartmentListGetInput input);
```
#### Parameters

<a name='AlifeApi.BusinessRules.DepartmentModels.DepartmentService.GetDepartmentListAsync(AlifeApi.BusinessRules.DepartmentModels.DepartmentListGetInput).input'></a>

`input` [DepartmentListGetInput](AlifeApi.BusinessRules.DepartmentModels.DepartmentListGetInput.md 'AlifeApi.BusinessRules.DepartmentModels.DepartmentListGetInput')

查詢條件

#### Returns
[System.Threading.Tasks.Task&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')[AlifeApi.BusinessRules.Infrastructure.PagedListOutput&lt;](AlifeApi.BusinessRules.Infrastructure.PagedListOutput_TDetail_.md 'AlifeApi.BusinessRules.Infrastructure.PagedListOutput<TDetail>')[DepartmentOutput](AlifeApi.BusinessRules.DepartmentModels.DepartmentOutput.md 'AlifeApi.BusinessRules.DepartmentModels.DepartmentOutput')[&gt;](AlifeApi.BusinessRules.Infrastructure.PagedListOutput_TDetail_.md 'AlifeApi.BusinessRules.Infrastructure.PagedListOutput<TDetail>')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')  
分頁的部門列表

<a name='AlifeApi.BusinessRules.DepartmentModels.DepartmentService.UpdateDepartmentAsync(AlifeApi.BusinessRules.DepartmentModels.DepartmentUpdateInput)'></a>

## DepartmentService.UpdateDepartmentAsync(DepartmentUpdateInput) Method

更新部門資訊

```csharp
public System.Threading.Tasks.Task<AlifeApi.BusinessRules.DepartmentModels.DepartmentOutput> UpdateDepartmentAsync(AlifeApi.BusinessRules.DepartmentModels.DepartmentUpdateInput input);
```
#### Parameters

<a name='AlifeApi.BusinessRules.DepartmentModels.DepartmentService.UpdateDepartmentAsync(AlifeApi.BusinessRules.DepartmentModels.DepartmentUpdateInput).input'></a>

`input` [DepartmentUpdateInput](AlifeApi.BusinessRules.DepartmentModels.DepartmentUpdateInput.md 'AlifeApi.BusinessRules.DepartmentModels.DepartmentUpdateInput')

更新部門輸入資料

#### Returns
[System.Threading.Tasks.Task&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')[DepartmentOutput](AlifeApi.BusinessRules.DepartmentModels.DepartmentOutput.md 'AlifeApi.BusinessRules.DepartmentModels.DepartmentOutput')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')  
更新後的部門資訊

#### Exceptions

[System.ArgumentNullException](https://docs.microsoft.com/en-us/dotnet/api/System.ArgumentNullException 'System.ArgumentNullException')  
input

[System.Exception](https://docs.microsoft.com/en-us/dotnet/api/System.Exception 'System.Exception')  
找不到部門或其他資料庫錯誤