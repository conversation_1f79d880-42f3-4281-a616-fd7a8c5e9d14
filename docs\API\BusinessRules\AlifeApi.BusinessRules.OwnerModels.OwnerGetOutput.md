#### [AlifeApi.BusinessRules](index.md 'index')
### [AlifeApi.BusinessRules.OwnerModels](AlifeApi.BusinessRules.OwnerModels.md 'AlifeApi.BusinessRules.OwnerModels')

## OwnerGetOutput Class

取得單一業主詳細資料輸出模型

```csharp
public class OwnerGetOutput
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; OwnerGetOutput
### Properties

<a name='AlifeApi.BusinessRules.OwnerModels.OwnerGetOutput.CompanyAddress'></a>

## OwnerGetOutput.CompanyAddress Property

公司登記地址

```csharp
public string? CompanyAddress { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.OwnerModels.OwnerGetOutput.CompanyName'></a>

## OwnerGetOutput.CompanyName Property

公司名稱

```csharp
public string CompanyName { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.OwnerModels.OwnerGetOutput.CompanyPhone'></a>

## OwnerGetOutput.CompanyPhone Property

公司登記電話

```csharp
public string? CompanyPhone { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.OwnerModels.OwnerGetOutput.ContactPerson'></a>

## OwnerGetOutput.ContactPerson Property

主要聯絡窗口人員姓名

```csharp
public string? ContactPerson { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.OwnerModels.OwnerGetOutput.ContactPhone1'></a>

## OwnerGetOutput.ContactPhone1 Property

主要聯絡電話

```csharp
public string? ContactPhone1 { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.OwnerModels.OwnerGetOutput.ContactPhone2'></a>

## OwnerGetOutput.ContactPhone2 Property

次要聯絡電話 (備用)

```csharp
public string? ContactPhone2 { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.OwnerModels.OwnerGetOutput.Email'></a>

## OwnerGetOutput.Email Property

聯絡電子郵件地址

```csharp
public string? Email { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.OwnerModels.OwnerGetOutput.IdentificationNumber'></a>

## OwnerGetOutput.IdentificationNumber Property

證號 (依人別決定是 統一編號 或 身分證ID)

```csharp
public string IdentificationNumber { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.OwnerModels.OwnerGetOutput.MailingAddress'></a>

## OwnerGetOutput.MailingAddress Property

郵件通訊地址 (若與公司地址不同)

```csharp
public string? MailingAddress { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.OwnerModels.OwnerGetOutput.OwnerId'></a>

## OwnerGetOutput.OwnerId Property

業主唯一識別碼

```csharp
public int OwnerId { get; set; }
```

#### Property Value
[System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')

<a name='AlifeApi.BusinessRules.OwnerModels.OwnerGetOutput.PersonType'></a>

## OwnerGetOutput.PersonType Property

人別 (值為 法人 或 自然人)

```csharp
public string PersonType { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.OwnerModels.OwnerGetOutput.ResponsiblePerson'></a>

## OwnerGetOutput.ResponsiblePerson Property

負責人姓名

```csharp
public string? ResponsiblePerson { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')