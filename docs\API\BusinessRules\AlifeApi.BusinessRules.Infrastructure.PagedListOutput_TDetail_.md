#### [AlifeApi.BusinessRules](index.md 'index')
### [AlifeApi.BusinessRules.Infrastructure](AlifeApi.BusinessRules.Infrastructure.md 'AlifeApi.BusinessRules.Infrastructure')

## PagedListOutput<TDetail> Class

```csharp
public class PagedListOutput<TDetail> : AlifeApi.BusinessRules.Infrastructure.PagedListInput
```
#### Type parameters

<a name='AlifeApi.BusinessRules.Infrastructure.PagedListOutput_TDetail_.TDetail'></a>

`TDetail`

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; [PagedListInput](AlifeApi.BusinessRules.Infrastructure.PagedListInput.md 'AlifeApi.BusinessRules.Infrastructure.PagedListInput') &#129106; PagedListOutput<TDetail>
### Constructors

<a name='AlifeApi.BusinessRules.Infrastructure.PagedListOutput_TDetail_.PagedListOutput(AlifeApi.BusinessRules.Infrastructure.PagedListInput)'></a>

## PagedListOutput(PagedListInput) Constructor

Initializes a new instance of the [PagedListOutput&lt;TDetail&gt;](AlifeApi.BusinessRules.Infrastructure.PagedListOutput_TDetail_.md 'AlifeApi.BusinessRules.Infrastructure.PagedListOutput<TDetail>') class.

```csharp
public PagedListOutput(AlifeApi.BusinessRules.Infrastructure.PagedListInput input);
```
#### Parameters

<a name='AlifeApi.BusinessRules.Infrastructure.PagedListOutput_TDetail_.PagedListOutput(AlifeApi.BusinessRules.Infrastructure.PagedListInput).input'></a>

`input` [PagedListInput](AlifeApi.BusinessRules.Infrastructure.PagedListInput.md 'AlifeApi.BusinessRules.Infrastructure.PagedListInput')

The input.

#### Exceptions

[System.ArgumentNullException](https://docs.microsoft.com/en-us/dotnet/api/System.ArgumentNullException 'System.ArgumentNullException')  
input
### Properties

<a name='AlifeApi.BusinessRules.Infrastructure.PagedListOutput_TDetail_.Details'></a>

## PagedListOutput<TDetail>.Details Property

資料明細

```csharp
public System.Collections.Generic.IEnumerable<TDetail> Details { get; set; }
```

#### Property Value
[System.Collections.Generic.IEnumerable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Collections.Generic.IEnumerable-1 'System.Collections.Generic.IEnumerable`1')[TDetail](AlifeApi.BusinessRules.Infrastructure.PagedListOutput_TDetail_.md#AlifeApi.BusinessRules.Infrastructure.PagedListOutput_TDetail_.TDetail 'AlifeApi.BusinessRules.Infrastructure.PagedListOutput<TDetail>.TDetail')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Collections.Generic.IEnumerable-1 'System.Collections.Generic.IEnumerable`1')

<a name='AlifeApi.BusinessRules.Infrastructure.PagedListOutput_TDetail_.RecordCount'></a>

## PagedListOutput<TDetail>.RecordCount Property

記錄總數

```csharp
public int RecordCount { get; set; }
```

#### Property Value
[System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')

<a name='AlifeApi.BusinessRules.Infrastructure.PagedListOutput_TDetail_.TotalPages'></a>

## PagedListOutput<TDetail>.TotalPages Property

總頁數

```csharp
public int TotalPages { get; }
```

#### Property Value
[System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')