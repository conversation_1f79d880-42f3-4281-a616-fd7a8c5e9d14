#### [AlifeApi.BusinessRules](index.md 'index')
### [AlifeApi.BusinessRules.DepartmentModels](AlifeApi.BusinessRules.DepartmentModels.md 'AlifeApi.BusinessRules.DepartmentModels')

## DepartmentDropdownOutput Class

部門下拉選單輸出資料

```csharp
public class DepartmentDropdownOutput
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; DepartmentDropdownOutput
### Properties

<a name='AlifeApi.BusinessRules.DepartmentModels.DepartmentDropdownOutput.Name'></a>

## DepartmentDropdownOutput.Name Property

名稱

```csharp
public string Name { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.DepartmentModels.DepartmentDropdownOutput.Value'></a>

## DepartmentDropdownOutput.Value Property

值

```csharp
public string Value { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')