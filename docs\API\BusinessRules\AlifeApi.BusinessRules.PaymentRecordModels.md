#### [AlifeApi.BusinessRules](index.md 'index')

## AlifeApi.BusinessRules.PaymentRecordModels Namespace

| Classes | |
| :--- | :--- |
| [PaymentRecordCreateInput](AlifeApi.BusinessRules.PaymentRecordModels.PaymentRecordCreateInput.md 'AlifeApi.BusinessRules.PaymentRecordModels.PaymentRecordCreateInput') | 收款記錄建立輸入模型 |
| [PaymentRecordListOutput](AlifeApi.BusinessRules.PaymentRecordModels.PaymentRecordListOutput.md 'AlifeApi.BusinessRules.PaymentRecordModels.PaymentRecordListOutput') | 收款記錄列表輸出項目 |
| [PaymentRecordOutput](AlifeApi.BusinessRules.PaymentRecordModels.PaymentRecordOutput.md 'AlifeApi.BusinessRules.PaymentRecordModels.PaymentRecordOutput') | 收款記錄詳細輸出模型 |
| [PaymentRecordQueryInput](AlifeApi.BusinessRules.PaymentRecordModels.PaymentRecordQueryInput.md 'AlifeApi.BusinessRules.PaymentRecordModels.PaymentRecordQueryInput') | 收款記錄列表查詢輸入模型 |
| [PaymentRecordService](AlifeApi.BusinessRules.PaymentRecordModels.PaymentRecordService.md 'AlifeApi.BusinessRules.PaymentRecordModels.PaymentRecordService') | 收款記錄服務 |
| [PaymentRecordUpdateInput](AlifeApi.BusinessRules.PaymentRecordModels.PaymentRecordUpdateInput.md 'AlifeApi.BusinessRules.PaymentRecordModels.PaymentRecordUpdateInput') | 收款記錄更新輸入模型 |
