﻿using System;
using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;

namespace AlifeApi.BusinessRules.CustomerModels;

/// <summary>
/// 客戶資料輸入 DTO
/// </summary>
public class CustomerInput
{
    /// <summary>
    /// 客戶姓名
    /// </summary>
    [JsonPropertyName("Name")]
    public string Name { get; set; }

    /// <summary>
    /// 性別
    /// </summary>
    [JsonPropertyName("Gender")]
    public string Gender { get; set; }

    /// <summary>
    /// 生日
    /// </summary>
    [JsonPropertyName("Birthday")]
    public DateOnly? Birthday { get; set; } // 使用 DateOnly? 允許空值

    /// <summary>
    /// 縣市
    /// </summary>
    [JsonPropertyName("City")]
    public string City { get; set; }

    /// <summary>
    /// 區域
    /// </summary>
    [JsonPropertyName("District")]
    public string District { get; set; }

    /// <summary>
    /// 地址
    /// </summary>
    [JsonPropertyName("Address")]
    public string Address { get; set; }

    /// <summary>
    /// 電話
    /// </summary>
    [JsonPropertyName("PhoneNumber")]
    public string PhoneNumber { get; set; }

    /// <summary>
    /// 電子信箱
    /// </summary>
    [JsonPropertyName("Email")]
    [EmailAddress(ErrorMessage = "電子信箱格式不正確")]
    public string Email { get; set; }

    /// <summary>
    /// 職業
    /// </summary>
    [JsonPropertyName("Occupation")]
    public string Occupation { get; set; }

    /// <summary>
    /// 得知管道 (客戶來源)
    /// </summary>
    [JsonPropertyName("LeadSource")]
    public string LeadSource { get; set; }

    /// <summary>
    /// 需求坪數
    /// </summary>
    [JsonPropertyName("RequiredPingArea")]
    public string RequiredPingArea { get; set; }

    /// <summary>
    /// 需求格局
    /// </summary>
    [JsonPropertyName("RequiredLayout")]
    public string RequiredLayout { get; set; }

    /// <summary>
    /// 預算範圍
    /// </summary>
    [JsonPropertyName("Budget")]
    public string Budget { get; set; }

    /// <summary>
    /// 備註 (對應 Customer.PurchaseConditions)
    /// </summary>
    [JsonPropertyName("PurchaseConditions")]
    public string PurchaseConditions { get; set; }

    /// <summary>
    /// 客戶圖片 (Base64 編碼字串)
    /// </summary>
    [JsonPropertyName("ImageBase64")]
    public string ImageBase64 { get; set; }

    /// <summary>
    /// 案場代碼
    /// </summary>
    [JsonPropertyName("SiteCode")]
    public string SiteCode { get; set; }

    /// <summary>
    /// 要新增或更新的客戶訪談紀錄列表
    /// </summary>
    [JsonPropertyName("CustomerRecords")]
    public List<CustomerRecordInput> CustomerRecords { get; set; } = new List<CustomerRecordInput>(); // 初始化以避免 null
} 
