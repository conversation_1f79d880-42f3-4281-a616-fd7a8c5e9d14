﻿using System;
using System.Collections.Generic;

namespace AlifeApi.DataAccess.ProjectContent
{
    /// <summary>
    /// 職位資料表，用於儲存每個公司的職位資訊。
    /// </summary>
    public partial class JobTitle
    {
        /// <summary>
        /// 部門編號，主鍵的一部分，對應 Departments 表的 DepartmentId。
        /// </summary>
        public string DepartmentId { get; set; }
        /// <summary>
        /// 職位編號，主鍵的一部分，用於唯一識別職位。
        /// </summary>
        public string JobTitleId { get; set; }
        /// <summary>
        /// 職位名稱。
        /// </summary>
        public string Name { get; set; }
        /// <summary>
        /// 創建時間，記錄資料創建的時間。
        /// </summary>
        public DateTime CreatedTime { get; set; }
        /// <summary>
        /// 更新時間，記錄資料最後更新的時間。
        /// </summary>
        public DateTime UpdatedTime { get; set; }
        /// <summary>
        /// 建立者，記錄創建此資料的員工編號。
        /// </summary>
        public string CreatedUserInfoId { get; set; }
        /// <summary>
        /// 更新者，記錄最後更新此資料的員工編號。
        /// </summary>
        public string UpdatedUserInfoId { get; set; }
    }
}
