using System;
using System.ComponentModel.DataAnnotations;

namespace AlifeApi.BusinessRules.WeeklyReportModels
{
    /// <summary>
    /// 週報查詢輸入DTO
    /// </summary>
    public class WeeklyReportQueryInput
    {
        /// <summary>
        /// 案場編號
        /// </summary>
        [Required(ErrorMessage = "案場編號為必填")]
        [StringLength(20, ErrorMessage = "案場編號長度不可超過20字元")]
        public string SiteCode { get; set; } = null!;

        /// <summary>
        /// 年份
        /// </summary>
        [Required(ErrorMessage = "年份為必填")]
        [Range(2020, 2099, ErrorMessage = "年份必須在2020-2099之間")]
        public int Year { get; set; }

        /// <summary>
        /// 週次
        /// </summary>
        [Required(ErrorMessage = "週次為必填")]
        [Range(1, 53, ErrorMessage = "週次必須在1-53之間")]
        public int WeekNumber { get; set; }
    }

    /// <summary>
    /// 週報統計結果輸出DTO
    /// </summary>
    public class WeeklyReportOutput
    {
        /// <summary>
        /// 案場編號
        /// </summary>
        public string SiteCode { get; set; } = null!;

        /// <summary>
        /// 案場名稱
        /// </summary>
        public string? SiteName { get; set; }

        /// <summary>
        /// 年份
        /// </summary>
        public int Year { get; set; }

        /// <summary>
        /// 週次
        /// </summary>
        public int WeekNumber { get; set; }

        /// <summary>
        /// 週報開始日期 (週一)
        /// </summary>
        public DateTime StartDate { get; set; }

        /// <summary>
        /// 週報結束日期 (週日)
        /// </summary>
        public DateTime EndDate { get; set; }

        /// <summary>
        /// 週報標題
        /// </summary>
        public string Title { get; set; } = null!;

        /// <summary>
        /// 本週經營成果統計
        /// </summary>
        public WeeklyBusinessResultStatistics BusinessResults { get; set; } = new();

        /// <summary>
        /// 客戶背景分析統計
        /// </summary>
        public WeeklyCustomerAnalysisStatistics CustomerAnalysis { get; set; } = new();

        /// <summary>
        /// 銷售統計
        /// </summary>
        public WeeklySalesStatistics SalesStatistics { get; set; } = new();
    }

    /// <summary>
    /// 週經營成果統計
    /// </summary>
    public class WeeklyBusinessResultStatistics
    {
        /// <summary>
        /// 來客統計
        /// </summary>
        public WeeklyStatisticItem VisitorStats { get; set; } = new();

        /// <summary>
        /// 來電統計
        /// </summary>
        public WeeklyStatisticItem CallStats { get; set; } = new();

        /// <summary>
        /// 留單統計
        /// </summary>
        public WeeklyStatisticItem LeadStats { get; set; } = new();

        /// <summary>
        /// 成交統計
        /// </summary>
        public WeeklyStatisticItem TransactionStats { get; set; } = new();
    }

    /// <summary>
    /// 週統計項目
    /// </summary>
    public class WeeklyStatisticItem
    {
        /// <summary>
        /// 前期累計 (本週之前的總計)
        /// </summary>
        public int PreviousPeriodTotal { get; set; }

        /// <summary>
        /// 本週總計
        /// </summary>
        public int CurrentWeekTotal { get; set; }

        /// <summary>
        /// 總計 (前期累計 + 本週總計)
        /// </summary>
        public int Total { get; set; }

        /// <summary>
        /// 每日明細
        /// </summary>
        public List<DailyStatistic> DailyDetails { get; set; } = new();
    }

    /// <summary>
    /// 每日統計
    /// </summary>
    public class DailyStatistic
    {
        /// <summary>
        /// 日期
        /// </summary>
        public DateTime Date { get; set; }

        /// <summary>
        /// 星期幾
        /// </summary>
        public string DayOfWeek { get; set; } = null!;

        /// <summary>
        /// 數量
        /// </summary>
        public int Count { get; set; }
    }

    /// <summary>
    /// 客戶背景分析統計
    /// </summary>
    public class WeeklyCustomerAnalysisStatistics
    {
        /// <summary>
        /// 年齡分析
        /// </summary>
        public List<CustomerAgeAnalysis> AgeAnalysis { get; set; } = new();

        /// <summary>
        /// 行業分析
        /// </summary>
        public List<CustomerIndustryAnalysis> IndustryAnalysis { get; set; } = new();

        /// <summary>
        /// 區域分析
        /// </summary>
        public List<CustomerRegionAnalysis> RegionAnalysis { get; set; } = new();
    }

    /// <summary>
    /// 客戶年齡分析
    /// </summary>
    public class CustomerAgeAnalysis
    {
        /// <summary>
        /// 年齡區間
        /// </summary>
        public string AgeRange { get; set; } = null!;

        /// <summary>
        /// 來客數
        /// </summary>
        public int VisitorCount { get; set; }

        /// <summary>
        /// 來客佔比
        /// </summary>
        public decimal VisitorPercentage { get; set; }

        /// <summary>
        /// 回訪數
        /// </summary>
        public int ReturnVisitCount { get; set; }

        /// <summary>
        /// 回訪佔比
        /// </summary>
        public decimal ReturnVisitPercentage { get; set; }

        /// <summary>
        /// 成交數
        /// </summary>
        public int TransactionCount { get; set; }

        /// <summary>
        /// 成交佔比
        /// </summary>
        public decimal TransactionPercentage { get; set; }
    }

    /// <summary>
    /// 客戶行業分析
    /// </summary>
    public class CustomerIndustryAnalysis
    {
        /// <summary>
        /// 行業
        /// </summary>
        public string Industry { get; set; } = null!;

        /// <summary>
        /// 來客數
        /// </summary>
        public int VisitorCount { get; set; }

        /// <summary>
        /// 來客佔比
        /// </summary>
        public decimal VisitorPercentage { get; set; }

        /// <summary>
        /// 回訪數
        /// </summary>
        public int ReturnVisitCount { get; set; }

        /// <summary>
        /// 回訪佔比
        /// </summary>
        public decimal ReturnVisitPercentage { get; set; }

        /// <summary>
        /// 成交數
        /// </summary>
        public int TransactionCount { get; set; }

        /// <summary>
        /// 成交佔比
        /// </summary>
        public decimal TransactionPercentage { get; set; }
    }

    /// <summary>
    /// 客戶區域分析
    /// </summary>
    public class CustomerRegionAnalysis
    {
        /// <summary>
        /// 區域 (縣市+區域)
        /// </summary>
        public string Region { get; set; } = null!;

        /// <summary>
        /// 來客數
        /// </summary>
        public int VisitorCount { get; set; }

        /// <summary>
        /// 來客佔比
        /// </summary>
        public decimal VisitorPercentage { get; set; }

        /// <summary>
        /// 來電數
        /// </summary>
        public int CallCount { get; set; }

        /// <summary>
        /// 來電佔比
        /// </summary>
        public decimal CallPercentage { get; set; }
    }

    /// <summary>
    /// 週銷售統計
    /// </summary>
    public class WeeklySalesStatistics
    {
        /// <summary>
        /// 房屋銷售統計
        /// </summary>
        public UnitSalesStatistics UnitStats { get; set; } = new();

        /// <summary>
        /// 車位銷售統計
        /// </summary>
        public ParkingSalesStatistics ParkingStats { get; set; } = new();
    }

    /// <summary>
    /// 房屋銷售統計
    /// </summary>
    public class UnitSalesStatistics
    {
        /// <summary>
        /// 可售戶數
        /// </summary>
        public int AvailableUnits { get; set; }

        /// <summary>
        /// 可售金額
        /// </summary>
        public decimal AvailableAmount { get; set; }

        /// <summary>
        /// 已售戶數
        /// </summary>
        public int SoldUnits { get; set; }

        /// <summary>
        /// 已售底價金額
        /// </summary>
        public decimal SoldMinimumAmount { get; set; }

        /// <summary>
        /// 已簽約總數
        /// </summary>
        public int SignedTotal { get; set; }

        /// <summary>
        /// 已簽約總成交金額
        /// </summary>
        public decimal SignedTotalTransactionAmount { get; set; }

        /// <summary>
        /// 銷售率
        /// </summary>
        public decimal SalesRate { get; set; }

        /// <summary>
        /// 未售戶數
        /// </summary>
        public int UnsoldUnits { get; set; }

        /// <summary>
        /// 未售金額
        /// </summary>
        public decimal UnsoldAmount { get; set; }

        /// <summary>
        /// 未售率
        /// </summary>
        public decimal UnsoldRate { get; set; }
    }

    /// <summary>
    /// 車位銷售統計
    /// </summary>
    public class ParkingSalesStatistics
    {
        /// <summary>
        /// 可售車位數
        /// </summary>
        public int AvailableSpaces { get; set; }

        /// <summary>
        /// 可售金額
        /// </summary>
        public decimal AvailableAmount { get; set; }

        /// <summary>
        /// 已售車位數
        /// </summary>
        public int SoldSpaces { get; set; }

        /// <summary>
        /// 已售金額
        /// </summary>
        public decimal SoldAmount { get; set; }

        /// <summary>
        /// 已簽約總數
        /// </summary>
        public int SignedTotal { get; set; }

        /// <summary>
        /// 已簽約總成交金額
        /// </summary>
        public decimal SignedTotalTransactionAmount { get; set; }

        /// <summary>
        /// 銷售率
        /// </summary>
        public decimal SalesRate { get; set; }

        /// <summary>
        /// 未售車位數
        /// </summary>
        public int UnsoldSpaces { get; set; }

        /// <summary>
        /// 未售金額
        /// </summary>
        public decimal UnsoldAmount { get; set; }

        /// <summary>
        /// 未售率
        /// </summary>
        public decimal UnsoldRate { get; set; }
    }
} 
