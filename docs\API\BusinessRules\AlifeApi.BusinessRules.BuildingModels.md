#### [AlifeApi.BusinessRules](index.md 'index')

## AlifeApi.BusinessRules.BuildingModels Namespace

| Classes | |
| :--- | :--- |
| [BuildingCreateInput](AlifeApi.BusinessRules.BuildingModels.BuildingCreateInput.md 'AlifeApi.BusinessRules.BuildingModels.BuildingCreateInput') | 建築物建立輸入模型 |
| [BuildingDropdownInput](AlifeApi.BusinessRules.BuildingModels.BuildingDropdownInput.md 'AlifeApi.BusinessRules.BuildingModels.BuildingDropdownInput') | 建築物下拉選單輸入資料 |
| [BuildingDropdownOutput](AlifeApi.BusinessRules.BuildingModels.BuildingDropdownOutput.md 'AlifeApi.BusinessRules.BuildingModels.BuildingDropdownOutput') | 建築物下拉選單輸出資料 |
| [BuildingListOutput](AlifeApi.BusinessRules.BuildingModels.BuildingListOutput.md 'AlifeApi.BusinessRules.BuildingModels.BuildingListOutput') | 建築物列表輸出項目 |
| [BuildingOutput](AlifeApi.BusinessRules.BuildingModels.BuildingOutput.md 'AlifeApi.BusinessRules.BuildingModels.BuildingOutput') | 建築物詳細輸出模型 |
| [BuildingQueryInput](AlifeApi.BusinessRules.BuildingModels.BuildingQueryInput.md 'AlifeApi.BusinessRules.BuildingModels.BuildingQueryInput') | 建築物列表查詢輸入模型 |
| [BuildingService](AlifeApi.BusinessRules.BuildingModels.BuildingService.md 'AlifeApi.BusinessRules.BuildingModels.BuildingService') | 建築物服務 |
| [BuildingUpdateInput](AlifeApi.BusinessRules.BuildingModels.BuildingUpdateInput.md 'AlifeApi.BusinessRules.BuildingModels.BuildingUpdateInput') | 建築物更新輸入模型 |
