#### [AlifeApi.DataAccess](index.md 'index')
### [AlifeApi.DataAccess.ProjectContent](AlifeApi.DataAccess.ProjectContent.md 'AlifeApi.DataAccess.ProjectContent')

## SupplierFile Class

供應商檔案資料表，用於儲存與供應商相關的檔案資訊

```csharp
public class SupplierFile
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; SupplierFile
### Properties

<a name='AlifeApi.DataAccess.ProjectContent.SupplierFile.Agent'></a>

## SupplierFile.Agent Property

承辦人姓名

```csharp
public string Agent { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContent.SupplierFile.CreatedTime'></a>

## SupplierFile.CreatedTime Property

資料創建時間

```csharp
public System.DateTime CreatedTime { get; set; }
```

#### Property Value
[System.DateTime](https://docs.microsoft.com/en-us/dotnet/api/System.DateTime 'System.DateTime')

<a name='AlifeApi.DataAccess.ProjectContent.SupplierFile.CreatedUserInfoId'></a>

## SupplierFile.CreatedUserInfoId Property

資料創建人識別碼（參考 UserInfo 表的 UserInfoId）

```csharp
public string CreatedUserInfoId { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContent.SupplierFile.FileName'></a>

## SupplierFile.FileName Property

檔案名稱

```csharp
public string FileName { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContent.SupplierFile.FilePath'></a>

## SupplierFile.FilePath Property

檔案儲存路徑

```csharp
public string FilePath { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContent.SupplierFile.FormType'></a>

## SupplierFile.FormType Property

表單類別（例如：合約、證明文件）

```csharp
public string FormType { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContent.SupplierFile.Remark'></a>

## SupplierFile.Remark Property

備註

```csharp
public string Remark { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContent.SupplierFile.SupplierFileId'></a>

## SupplierFile.SupplierFileId Property

檔案唯一識別碼，自動遞增

```csharp
public int SupplierFileId { get; set; }
```

#### Property Value
[System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')

<a name='AlifeApi.DataAccess.ProjectContent.SupplierFile.SupplierId'></a>

## SupplierFile.SupplierId Property

關聯的供應商識別碼，外鍵

```csharp
public int SupplierId { get; set; }
```

#### Property Value
[System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')

<a name='AlifeApi.DataAccess.ProjectContent.SupplierFile.UpdatedTime'></a>

## SupplierFile.UpdatedTime Property

資料更新時間

```csharp
public System.DateTime UpdatedTime { get; set; }
```

#### Property Value
[System.DateTime](https://docs.microsoft.com/en-us/dotnet/api/System.DateTime 'System.DateTime')

<a name='AlifeApi.DataAccess.ProjectContent.SupplierFile.UpdatedUserInfoId'></a>

## SupplierFile.UpdatedUserInfoId Property

資料更新人識別碼（參考 UserInfo 表的 UserInfoId）

```csharp
public string UpdatedUserInfoId { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')