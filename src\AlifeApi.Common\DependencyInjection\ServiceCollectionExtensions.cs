﻿using System.Reflection;
using AlifeApi.Common.DependencyInjection;

namespace Microsoft.Extensions.DependencyInjection
{
    /// <summary>
    /// IServiceCollection 的擴充方法
    /// </summary>
    public static class ServiceCollectionExtensions
    {
        /// <summary>
        /// 在指定的 Assembly 集合中，尋找符合指定 Assembly 名稱模式的 Assembly，並將其中實作 <c>IScopedDependency</c>、<c>ITransientDependency</c> 和 <c>ISingletonDependency</c> 的類別進行相依性注入設定
        /// </summary>
        /// <param name="services">相依性注入的服務集合</param>
        /// <param name="dllFileNamePattern">The assembly name pattern.</param>
        /// <returns>已註冊服務的服務集合</returns>
        public static IServiceCollection AddDependencies(this IServiceCollection services, string dllFileNamePattern)
        {
            string path = AppDomain.CurrentDomain.RelativeSearchPath ?? AppDomain.CurrentDomain.BaseDirectory;
            IEnumerable<Assembly> assemblies = Directory.GetFiles(path, dllFileNamePattern).Select(Assembly.LoadFrom);

            foreach (Type type in assemblies.SelectMany(x => x.GetTypes()))
            {
                if (!type.IsAbstract && !type.IsInterface)
                {
                    if (type.IsAssignableTo(typeof(IScopedDependency)))
                    {
                        RegisterServices(services, type, ServiceLifetime.Scoped);
                    }

                    if (type.IsAssignableTo(typeof(ITransientDependency)))
                    {
                        RegisterServices(services, type, ServiceLifetime.Transient);
                    }

                    if (type.IsAssignableTo(typeof(ISingletonDependency)))
                    {
                        RegisterServices(services, type, ServiceLifetime.Singleton);
                    }
                }
            }

            return services;
        }

        private static void RegisterServices(IServiceCollection services, Type concreteType, ServiceLifetime lifetime)
        {
            services.Add(new ServiceDescriptor(concreteType, concreteType, lifetime));

            Type baseType = concreteType.BaseType;
            while (baseType is not null)
            {
                services.Add(new ServiceDescriptor(baseType, concreteType, lifetime));
                baseType = baseType.BaseType;
            }

            Type[] interfaceTypes = concreteType.GetInterfaces();
            foreach (Type interfaceType in interfaceTypes)
            {
                services.Add(new ServiceDescriptor(interfaceType, concreteType, lifetime));
            }
        }
    }
}
