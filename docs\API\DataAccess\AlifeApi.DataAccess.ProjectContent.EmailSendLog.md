#### [AlifeApi.DataAccess](index.md 'index')
### [AlifeApi.DataAccess.ProjectContent](AlifeApi.DataAccess.ProjectContent.md 'AlifeApi.DataAccess.ProjectContent')

## EmailSendLog Class

電子郵件發送記錄表，用於儲存系統發送電子郵件的記錄資訊。

```csharp
public class EmailSendLog
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; EmailSendLog
### Properties

<a name='AlifeApi.DataAccess.ProjectContent.EmailSendLog.AttachmentFileName'></a>

## EmailSendLog.AttachmentFileName Property

附件的檔案名稱。

```csharp
public string AttachmentFileName { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContent.EmailSendLog.AttachmentFileSize'></a>

## EmailSendLog.AttachmentFileSize Property

附件檔案大小，單位為位元組。

```csharp
public System.Nullable<int> AttachmentFileSize { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

<a name='AlifeApi.DataAccess.ProjectContent.EmailSendLog.Body'></a>

## EmailSendLog.Body Property

郵件內容。

```csharp
public string Body { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContent.EmailSendLog.CreatedTime'></a>

## EmailSendLog.CreatedTime Property

創建時間，記錄郵件記錄創建的時間。

```csharp
public System.DateTime CreatedTime { get; set; }
```

#### Property Value
[System.DateTime](https://docs.microsoft.com/en-us/dotnet/api/System.DateTime 'System.DateTime')

<a name='AlifeApi.DataAccess.ProjectContent.EmailSendLog.CreatedUserInfoId'></a>

## EmailSendLog.CreatedUserInfoId Property

建立者，記錄創建此記錄的員工編號或系統帳號。

```csharp
public string CreatedUserInfoId { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContent.EmailSendLog.EmailSendLogId'></a>

## EmailSendLog.EmailSendLogId Property

流水號，主鍵，用於唯一識別郵件發送記錄。

```csharp
public int EmailSendLogId { get; set; }
```

#### Property Value
[System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')

<a name='AlifeApi.DataAccess.ProjectContent.EmailSendLog.ErrorMessage'></a>

## EmailSendLog.ErrorMessage Property

若發送失敗，記錄錯誤訊息。

```csharp
public string ErrorMessage { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContent.EmailSendLog.IsSuccess'></a>

## EmailSendLog.IsSuccess Property

是否發送成功，true 表示成功，false 表示失敗。

```csharp
public bool IsSuccess { get; set; }
```

#### Property Value
[System.Boolean](https://docs.microsoft.com/en-us/dotnet/api/System.Boolean 'System.Boolean')

<a name='AlifeApi.DataAccess.ProjectContent.EmailSendLog.RecipientEmail'></a>

## EmailSendLog.RecipientEmail Property

收件者電子郵件地址。

```csharp
public string RecipientEmail { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContent.EmailSendLog.RecipientName'></a>

## EmailSendLog.RecipientName Property

收件者姓名。

```csharp
public string RecipientName { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContent.EmailSendLog.ReportEndDate'></a>

## EmailSendLog.ReportEndDate Property

報表統計的結束日期。

```csharp
public System.DateTime ReportEndDate { get; set; }
```

#### Property Value
[System.DateTime](https://docs.microsoft.com/en-us/dotnet/api/System.DateTime 'System.DateTime')

<a name='AlifeApi.DataAccess.ProjectContent.EmailSendLog.ReportStartDate'></a>

## EmailSendLog.ReportStartDate Property

報表統計的開始日期。

```csharp
public System.DateTime ReportStartDate { get; set; }
```

#### Property Value
[System.DateTime](https://docs.microsoft.com/en-us/dotnet/api/System.DateTime 'System.DateTime')

<a name='AlifeApi.DataAccess.ProjectContent.EmailSendLog.SentDate'></a>

## EmailSendLog.SentDate Property

郵件發送時間。

```csharp
public System.DateTime SentDate { get; set; }
```

#### Property Value
[System.DateTime](https://docs.microsoft.com/en-us/dotnet/api/System.DateTime 'System.DateTime')

<a name='AlifeApi.DataAccess.ProjectContent.EmailSendLog.Subject'></a>

## EmailSendLog.Subject Property

郵件主題。

```csharp
public string Subject { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')