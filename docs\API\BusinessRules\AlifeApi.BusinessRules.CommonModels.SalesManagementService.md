#### [AlifeApi.BusinessRules](index.md 'index')
### [AlifeApi.BusinessRules.CommonModels](AlifeApi.BusinessRules.CommonModels.md 'AlifeApi.BusinessRules.CommonModels')

## SalesManagementService Class

銷售管理統一服務

```csharp
public class SalesManagementService : AlifeApi.BusinessRules.Infrastructure.ServiceBase<AlifeApi.DataAccess.ProjectContent.alifeContext>
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; [AlifeApi.Common.InfrastructureBase](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.Common.InfrastructureBase 'AlifeApi.Common.InfrastructureBase') &#129106; [AlifeApi.BusinessRules.Infrastructure.ServiceBase&lt;](AlifeApi.BusinessRules.Infrastructure.ServiceBase_TDbContext_.md 'AlifeApi.BusinessRules.Infrastructure.ServiceBase<TDbContext>')[AlifeApi.DataAccess.ProjectContent.alifeContext](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.DataAccess.ProjectContent.alifeContext 'AlifeApi.DataAccess.ProjectContent.alifeContext')[&gt;](AlifeApi.BusinessRules.Infrastructure.ServiceBase_TDbContext_.md 'AlifeApi.BusinessRules.Infrastructure.ServiceBase<TDbContext>') &#129106; SalesManagementService
### Methods

<a name='AlifeApi.BusinessRules.CommonModels.SalesManagementService.BatchChangeParkingSpaceStatusAsync(System.Collections.Generic.List_int_,string,System.Nullable_int_)'></a>

## SalesManagementService.BatchChangeParkingSpaceStatusAsync(List<int>, string, Nullable<int>) Method

直接變更車位銷售狀態

```csharp
private System.Threading.Tasks.Task BatchChangeParkingSpaceStatusAsync(System.Collections.Generic.List<int> parkingSpaceIds, string status, System.Nullable<int> orderId=null);
```
#### Parameters

<a name='AlifeApi.BusinessRules.CommonModels.SalesManagementService.BatchChangeParkingSpaceStatusAsync(System.Collections.Generic.List_int_,string,System.Nullable_int_).parkingSpaceIds'></a>

`parkingSpaceIds` [System.Collections.Generic.List&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Collections.Generic.List-1 'System.Collections.Generic.List`1')[System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Collections.Generic.List-1 'System.Collections.Generic.List`1')

停車位ID列表

<a name='AlifeApi.BusinessRules.CommonModels.SalesManagementService.BatchChangeParkingSpaceStatusAsync(System.Collections.Generic.List_int_,string,System.Nullable_int_).status'></a>

`status` [System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

新狀態

<a name='AlifeApi.BusinessRules.CommonModels.SalesManagementService.BatchChangeParkingSpaceStatusAsync(System.Collections.Generic.List_int_,string,System.Nullable_int_).orderId'></a>

`orderId` [System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

訂單ID (可選)

#### Returns
[System.Threading.Tasks.Task](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task 'System.Threading.Tasks.Task')

<a name='AlifeApi.BusinessRules.CommonModels.SalesManagementService.BatchOwnerReserveParkingSpacesAsync(System.Collections.Generic.List_int_,string)'></a>

## SalesManagementService.BatchOwnerReserveParkingSpacesAsync(List<int>, string) Method

批次設定停車位為地主保留狀態

```csharp
public System.Threading.Tasks.Task BatchOwnerReserveParkingSpacesAsync(System.Collections.Generic.List<int> parkingSpaceIds, string? reason=null);
```
#### Parameters

<a name='AlifeApi.BusinessRules.CommonModels.SalesManagementService.BatchOwnerReserveParkingSpacesAsync(System.Collections.Generic.List_int_,string).parkingSpaceIds'></a>

`parkingSpaceIds` [System.Collections.Generic.List&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Collections.Generic.List-1 'System.Collections.Generic.List`1')[System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Collections.Generic.List-1 'System.Collections.Generic.List`1')

停車位ID列表

<a name='AlifeApi.BusinessRules.CommonModels.SalesManagementService.BatchOwnerReserveParkingSpacesAsync(System.Collections.Generic.List_int_,string).reason'></a>

`reason` [System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

保留原因

#### Returns
[System.Threading.Tasks.Task](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task 'System.Threading.Tasks.Task')

<a name='AlifeApi.BusinessRules.CommonModels.SalesManagementService.BatchReserveParkingSpacesAsync(System.Collections.Generic.List_int_,string)'></a>

## SalesManagementService.BatchReserveParkingSpacesAsync(List<int>, string) Method

批次設定停車位為保留狀態

```csharp
public System.Threading.Tasks.Task BatchReserveParkingSpacesAsync(System.Collections.Generic.List<int> parkingSpaceIds, string? reason=null);
```
#### Parameters

<a name='AlifeApi.BusinessRules.CommonModels.SalesManagementService.BatchReserveParkingSpacesAsync(System.Collections.Generic.List_int_,string).parkingSpaceIds'></a>

`parkingSpaceIds` [System.Collections.Generic.List&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Collections.Generic.List-1 'System.Collections.Generic.List`1')[System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Collections.Generic.List-1 'System.Collections.Generic.List`1')

停車位ID列表

<a name='AlifeApi.BusinessRules.CommonModels.SalesManagementService.BatchReserveParkingSpacesAsync(System.Collections.Generic.List_int_,string).reason'></a>

`reason` [System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

保留原因

#### Returns
[System.Threading.Tasks.Task](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task 'System.Threading.Tasks.Task')

<a name='AlifeApi.BusinessRules.CommonModels.SalesManagementService.BatchReserveUnitsAsync(System.Collections.Generic.List_int_,string)'></a>

## SalesManagementService.BatchReserveUnitsAsync(List<int>, string) Method

批次設定房屋為保留狀態

```csharp
public System.Threading.Tasks.Task BatchReserveUnitsAsync(System.Collections.Generic.List<int> unitIds, string? reason=null);
```
#### Parameters

<a name='AlifeApi.BusinessRules.CommonModels.SalesManagementService.BatchReserveUnitsAsync(System.Collections.Generic.List_int_,string).unitIds'></a>

`unitIds` [System.Collections.Generic.List&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Collections.Generic.List-1 'System.Collections.Generic.List`1')[System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Collections.Generic.List-1 'System.Collections.Generic.List`1')

房屋單位ID列表

<a name='AlifeApi.BusinessRules.CommonModels.SalesManagementService.BatchReserveUnitsAsync(System.Collections.Generic.List_int_,string).reason'></a>

`reason` [System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

保留原因

#### Returns
[System.Threading.Tasks.Task](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task 'System.Threading.Tasks.Task')

<a name='AlifeApi.BusinessRules.CommonModels.SalesManagementService.GetSalesStatisticsAsync(string)'></a>

## SalesManagementService.GetSalesStatisticsAsync(string) Method

獲取綜合銷售統計

```csharp
public System.Threading.Tasks.Task<AlifeApi.BusinessRules.CommonModels.ComprehensiveSalesStatistics> GetSalesStatisticsAsync(string siteCode);
```
#### Parameters

<a name='AlifeApi.BusinessRules.CommonModels.SalesManagementService.GetSalesStatisticsAsync(string).siteCode'></a>

`siteCode` [System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

案場編號

#### Returns
[System.Threading.Tasks.Task&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')[ComprehensiveSalesStatistics](AlifeApi.BusinessRules.CommonModels.ComprehensiveSalesStatistics.md 'AlifeApi.BusinessRules.CommonModels.ComprehensiveSalesStatistics')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')  
綜合銷售統計

<a name='AlifeApi.BusinessRules.CommonModels.SalesManagementService.HandleOrderCancelledAsync(int,System.Nullable_int_,string)'></a>

## SalesManagementService.HandleOrderCancelledAsync(int, Nullable<int>, string) Method

處理訂單取消時的狀態回滾

```csharp
public System.Threading.Tasks.Task HandleOrderCancelledAsync(int orderId, System.Nullable<int> unitId, string? parkingSpaceIdsString);
```
#### Parameters

<a name='AlifeApi.BusinessRules.CommonModels.SalesManagementService.HandleOrderCancelledAsync(int,System.Nullable_int_,string).orderId'></a>

`orderId` [System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')

訂單ID

<a name='AlifeApi.BusinessRules.CommonModels.SalesManagementService.HandleOrderCancelledAsync(int,System.Nullable_int_,string).unitId'></a>

`unitId` [System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

房屋單位ID (可選)

<a name='AlifeApi.BusinessRules.CommonModels.SalesManagementService.HandleOrderCancelledAsync(int,System.Nullable_int_,string).parkingSpaceIdsString'></a>

`parkingSpaceIdsString` [System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

停車位ID字串 (可選)

#### Returns
[System.Threading.Tasks.Task](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task 'System.Threading.Tasks.Task')

<a name='AlifeApi.BusinessRules.CommonModels.SalesManagementService.HandleOrderCompletedAsync(int,System.Nullable_int_,string)'></a>

## SalesManagementService.HandleOrderCompletedAsync(int, Nullable<int>, string) Method

處理訂單完成簽約時的狀態變更

```csharp
public System.Threading.Tasks.Task HandleOrderCompletedAsync(int orderId, System.Nullable<int> unitId, string? parkingSpaceIdsString);
```
#### Parameters

<a name='AlifeApi.BusinessRules.CommonModels.SalesManagementService.HandleOrderCompletedAsync(int,System.Nullable_int_,string).orderId'></a>

`orderId` [System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')

訂單ID

<a name='AlifeApi.BusinessRules.CommonModels.SalesManagementService.HandleOrderCompletedAsync(int,System.Nullable_int_,string).unitId'></a>

`unitId` [System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

房屋單位ID (可選)

<a name='AlifeApi.BusinessRules.CommonModels.SalesManagementService.HandleOrderCompletedAsync(int,System.Nullable_int_,string).parkingSpaceIdsString'></a>

`parkingSpaceIdsString` [System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

停車位ID字串 (可選)

#### Returns
[System.Threading.Tasks.Task](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task 'System.Threading.Tasks.Task')

<a name='AlifeApi.BusinessRules.CommonModels.SalesManagementService.HandleOrderCreatedAsync(int,System.Nullable_int_,string)'></a>

## SalesManagementService.HandleOrderCreatedAsync(int, Nullable<int>, string) Method

處理訂單建立時的狀態變更

```csharp
public System.Threading.Tasks.Task HandleOrderCreatedAsync(int orderId, System.Nullable<int> unitId, string? parkingSpaceIdsString);
```
#### Parameters

<a name='AlifeApi.BusinessRules.CommonModels.SalesManagementService.HandleOrderCreatedAsync(int,System.Nullable_int_,string).orderId'></a>

`orderId` [System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')

訂單ID

<a name='AlifeApi.BusinessRules.CommonModels.SalesManagementService.HandleOrderCreatedAsync(int,System.Nullable_int_,string).unitId'></a>

`unitId` [System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

房屋單位ID (可選)

<a name='AlifeApi.BusinessRules.CommonModels.SalesManagementService.HandleOrderCreatedAsync(int,System.Nullable_int_,string).parkingSpaceIdsString'></a>

`parkingSpaceIdsString` [System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

停車位ID字串 (可選)

#### Returns
[System.Threading.Tasks.Task](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task 'System.Threading.Tasks.Task')

<a name='AlifeApi.BusinessRules.CommonModels.SalesManagementService.ValidateAvailabilityAsync(System.Nullable_int_,System.Collections.Generic.List_int_)'></a>

## SalesManagementService.ValidateAvailabilityAsync(Nullable<int>, List<int>) Method

檢查房屋和車位的可售狀態

```csharp
public System.Threading.Tasks.Task<AlifeApi.BusinessRules.CommonModels.SalesValidationResult> ValidateAvailabilityAsync(System.Nullable<int> unitId, System.Collections.Generic.List<int>? parkingSpaceIds);
```
#### Parameters

<a name='AlifeApi.BusinessRules.CommonModels.SalesManagementService.ValidateAvailabilityAsync(System.Nullable_int_,System.Collections.Generic.List_int_).unitId'></a>

`unitId` [System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

房屋單位ID (可選)

<a name='AlifeApi.BusinessRules.CommonModels.SalesManagementService.ValidateAvailabilityAsync(System.Nullable_int_,System.Collections.Generic.List_int_).parkingSpaceIds'></a>

`parkingSpaceIds` [System.Collections.Generic.List&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Collections.Generic.List-1 'System.Collections.Generic.List`1')[System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Collections.Generic.List-1 'System.Collections.Generic.List`1')

停車位ID列表 (可選)

#### Returns
[System.Threading.Tasks.Task&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')[SalesValidationResult](AlifeApi.BusinessRules.CommonModels.SalesValidationResult.md 'AlifeApi.BusinessRules.CommonModels.SalesValidationResult')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')  
驗證結果