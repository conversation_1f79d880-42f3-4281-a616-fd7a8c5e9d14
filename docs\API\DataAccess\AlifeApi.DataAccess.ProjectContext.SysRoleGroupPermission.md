#### [AlifeApi.DataAccess](index.md 'index')
### [AlifeApi.DataAccess.ProjectContext](AlifeApi.DataAccess.ProjectContext.md 'AlifeApi.DataAccess.ProjectContext')

## SysRoleGroupPermission Class

```csharp
public class SysRoleGroupPermission
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; SysRoleGroupPermission
### Properties

<a name='AlifeApi.DataAccess.ProjectContext.SysRoleGroupPermission.FuncId'></a>

## SysRoleGroupPermission.FuncId Property

系統項目識別編號

```csharp
public string FuncId { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContext.SysRoleGroupPermission.RoleGroupId'></a>

## SysRoleGroupPermission.RoleGroupId Property

角色群組識別編號

```csharp
public string RoleGroupId { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContext.SysRoleGroupPermission.System'></a>

## SysRoleGroupPermission.System Property

系統名稱

```csharp
public string System { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')