#### [AlifeApi.BusinessRules](index.md 'index')
### [AlifeApi.BusinessRules.DeptModels](AlifeApi.BusinessRules.DeptModels.md 'AlifeApi.BusinessRules.DeptModels')

## DeptListResult Class

```csharp
public class DeptListResult
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; DeptListResult
### Properties

<a name='AlifeApi.BusinessRules.DeptModels.DeptListResult.CreatedTime'></a>

## DeptListResult.CreatedTime Property

建立時間

```csharp
public System.DateTime CreatedTime { get; set; }
```

#### Property Value
[System.DateTime](https://docs.microsoft.com/en-us/dotnet/api/System.DateTime 'System.DateTime')

<a name='AlifeApi.BusinessRules.DeptModels.DeptListResult.CreatedUserId'></a>

## DeptListResult.CreatedUserId Property

建立人帳號

```csharp
public string CreatedUserId { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.DeptModels.DeptListResult.DeptId'></a>

## DeptListResult.DeptId Property

使用者單位

```csharp
public string DeptId { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.DeptModels.DeptListResult.DeptName'></a>

## DeptListResult.DeptName Property

使用者單位

```csharp
public string DeptName { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.DeptModels.DeptListResult.FuncIds'></a>

## DeptListResult.FuncIds Property

部門權限

```csharp
public System.Collections.Generic.IEnumerable<string> FuncIds { get; set; }
```

#### Property Value
[System.Collections.Generic.IEnumerable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Collections.Generic.IEnumerable-1 'System.Collections.Generic.IEnumerable`1')[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Collections.Generic.IEnumerable-1 'System.Collections.Generic.IEnumerable`1')

<a name='AlifeApi.BusinessRules.DeptModels.DeptListResult.IsDisabled'></a>

## DeptListResult.IsDisabled Property

是否刪除

```csharp
public bool IsDisabled { get; set; }
```

#### Property Value
[System.Boolean](https://docs.microsoft.com/en-us/dotnet/api/System.Boolean 'System.Boolean')

<a name='AlifeApi.BusinessRules.DeptModels.DeptListResult.LeaderUserId'></a>

## DeptListResult.LeaderUserId Property

部門主管

```csharp
public string LeaderUserId { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.DeptModels.DeptListResult.MenuTrees'></a>

## DeptListResult.MenuTrees Property

權限樹

```csharp
public System.Collections.Generic.IEnumerable<AlifeApi.BusinessRules.RoleGroupModels.MenuTreeOutput> MenuTrees { get; set; }
```

#### Property Value
[System.Collections.Generic.IEnumerable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Collections.Generic.IEnumerable-1 'System.Collections.Generic.IEnumerable`1')[MenuTreeOutput](AlifeApi.BusinessRules.RoleGroupModels.MenuTreeOutput.md 'AlifeApi.BusinessRules.RoleGroupModels.MenuTreeOutput')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Collections.Generic.IEnumerable-1 'System.Collections.Generic.IEnumerable`1')

<a name='AlifeApi.BusinessRules.DeptModels.DeptListResult.ParentDeptId'></a>

## DeptListResult.ParentDeptId Property

上級使用者單位

```csharp
public string ParentDeptId { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.DeptModels.DeptListResult.ParentDeptName'></a>

## DeptListResult.ParentDeptName Property

上級使用者單位

```csharp
public string ParentDeptName { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.DeptModels.DeptListResult.Permission'></a>

## DeptListResult.Permission Property

部門權限

```csharp
public string Permission { get; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.DeptModels.DeptListResult.UpdatedTime'></a>

## DeptListResult.UpdatedTime Property

更新時間

```csharp
public System.DateTime UpdatedTime { get; set; }
```

#### Property Value
[System.DateTime](https://docs.microsoft.com/en-us/dotnet/api/System.DateTime 'System.DateTime')

<a name='AlifeApi.BusinessRules.DeptModels.DeptListResult.UpdatedUserId'></a>

## DeptListResult.UpdatedUserId Property

更新者帳號

```csharp
public string UpdatedUserId { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.DeptModels.DeptListResult.UserIds'></a>

## DeptListResult.UserIds Property

部門成員清單

```csharp
public System.Collections.Generic.IEnumerable<string> UserIds { get; set; }
```

#### Property Value
[System.Collections.Generic.IEnumerable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Collections.Generic.IEnumerable-1 'System.Collections.Generic.IEnumerable`1')[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Collections.Generic.IEnumerable-1 'System.Collections.Generic.IEnumerable`1')