#### [AlifeApi.WebApi](index.md 'index')
### [AlifeApi.WebApi.Controllers](AlifeApi.WebApi.Controllers.md 'AlifeApi.WebApi.Controllers')

## UserInfoController Class

使用者操作相關(UserInfo)

```csharp
public class UserInfoController : AlifeApi.WebApi.Controllers.AuthenticatedController
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; [Microsoft.AspNetCore.Mvc.ControllerBase](https://docs.microsoft.com/en-us/dotnet/api/Microsoft.AspNetCore.Mvc.ControllerBase 'Microsoft.AspNetCore.Mvc.ControllerBase') &#129106; [Microsoft.AspNetCore.Mvc.Controller](https://docs.microsoft.com/en-us/dotnet/api/Microsoft.AspNetCore.Mvc.Controller 'Microsoft.AspNetCore.Mvc.Controller') &#129106; [AlifeApi.WebApi.Controllers.BasicController](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.WebApi.Controllers.BasicController 'AlifeApi.WebApi.Controllers.BasicController') &#129106; [AlifeApi.WebApi.Controllers.AuthenticatedController](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.WebApi.Controllers.AuthenticatedController 'AlifeApi.WebApi.Controllers.AuthenticatedController') &#129106; UserInfoController
### Constructors

<a name='AlifeApi.WebApi.Controllers.UserInfoController.UserInfoController(AlifeApi.BusinessRules.UserInfoModels.UserInfoServicePg,AlifeApi.BusinessRules.LoginModels.LoginServicePg,AlifeApi.WebApi.Util.JwtHelper,Microsoft.AspNetCore.SignalR.IHubContext_AlifeApi.WebApi.SignalR.ConnectHub_)'></a>

## UserInfoController(UserInfoServicePg, LoginServicePg, JwtHelper, IHubContext<ConnectHub>) Constructor

建構子

```csharp
public UserInfoController(AlifeApi.BusinessRules.UserInfoModels.UserInfoServicePg userInfoService, AlifeApi.BusinessRules.LoginModels.LoginServicePg loginService, AlifeApi.WebApi.Util.JwtHelper jwtHelper, Microsoft.AspNetCore.SignalR.IHubContext<AlifeApi.WebApi.SignalR.ConnectHub> hubcontext);
```
#### Parameters

<a name='AlifeApi.WebApi.Controllers.UserInfoController.UserInfoController(AlifeApi.BusinessRules.UserInfoModels.UserInfoServicePg,AlifeApi.BusinessRules.LoginModels.LoginServicePg,AlifeApi.WebApi.Util.JwtHelper,Microsoft.AspNetCore.SignalR.IHubContext_AlifeApi.WebApi.SignalR.ConnectHub_).userInfoService'></a>

`userInfoService` [AlifeApi.BusinessRules.UserInfoModels.UserInfoServicePg](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.BusinessRules.UserInfoModels.UserInfoServicePg 'AlifeApi.BusinessRules.UserInfoModels.UserInfoServicePg')

<a name='AlifeApi.WebApi.Controllers.UserInfoController.UserInfoController(AlifeApi.BusinessRules.UserInfoModels.UserInfoServicePg,AlifeApi.BusinessRules.LoginModels.LoginServicePg,AlifeApi.WebApi.Util.JwtHelper,Microsoft.AspNetCore.SignalR.IHubContext_AlifeApi.WebApi.SignalR.ConnectHub_).loginService'></a>

`loginService` [AlifeApi.BusinessRules.LoginModels.LoginServicePg](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.BusinessRules.LoginModels.LoginServicePg 'AlifeApi.BusinessRules.LoginModels.LoginServicePg')

<a name='AlifeApi.WebApi.Controllers.UserInfoController.UserInfoController(AlifeApi.BusinessRules.UserInfoModels.UserInfoServicePg,AlifeApi.BusinessRules.LoginModels.LoginServicePg,AlifeApi.WebApi.Util.JwtHelper,Microsoft.AspNetCore.SignalR.IHubContext_AlifeApi.WebApi.SignalR.ConnectHub_).jwtHelper'></a>

`jwtHelper` [JwtHelper](AlifeApi.WebApi.Util.JwtHelper.md 'AlifeApi.WebApi.Util.JwtHelper')

<a name='AlifeApi.WebApi.Controllers.UserInfoController.UserInfoController(AlifeApi.BusinessRules.UserInfoModels.UserInfoServicePg,AlifeApi.BusinessRules.LoginModels.LoginServicePg,AlifeApi.WebApi.Util.JwtHelper,Microsoft.AspNetCore.SignalR.IHubContext_AlifeApi.WebApi.SignalR.ConnectHub_).hubcontext'></a>

`hubcontext` [Microsoft.AspNetCore.SignalR.IHubContext&lt;](https://docs.microsoft.com/en-us/dotnet/api/Microsoft.AspNetCore.SignalR.IHubContext-1 'Microsoft.AspNetCore.SignalR.IHubContext`1')[AlifeApi.WebApi.SignalR.ConnectHub](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.WebApi.SignalR.ConnectHub 'AlifeApi.WebApi.SignalR.ConnectHub')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/Microsoft.AspNetCore.SignalR.IHubContext-1 'Microsoft.AspNetCore.SignalR.IHubContext`1')
### Methods

<a name='AlifeApi.WebApi.Controllers.UserInfoController.AutoLogoutAsync()'></a>

## UserInfoController.AutoLogoutAsync() Method

使用者-自動登出

```csharp
public System.Threading.Tasks.Task<Microsoft.AspNetCore.Mvc.ActionResult<string>> AutoLogoutAsync();
```

#### Returns
[System.Threading.Tasks.Task&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')[Microsoft.AspNetCore.Mvc.ActionResult&lt;](https://docs.microsoft.com/en-us/dotnet/api/Microsoft.AspNetCore.Mvc.ActionResult-1 'Microsoft.AspNetCore.Mvc.ActionResult`1')[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/Microsoft.AspNetCore.Mvc.ActionResult-1 'Microsoft.AspNetCore.Mvc.ActionResult`1')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')  
ClientId

<a name='AlifeApi.WebApi.Controllers.UserInfoController.ChangePasswordAsync(AlifeApi.BusinessRules.UserInfoModels.PasswordChangeInput)'></a>

## UserInfoController.ChangePasswordAsync(PasswordChangeInput) Method

帳號權限管理-修改密碼

```csharp
public System.Threading.Tasks.Task<Microsoft.AspNetCore.Mvc.ActionResult<AlifeApi.BusinessRules.UserInfoModels.UserInfoUpdateOutput>> ChangePasswordAsync(AlifeApi.BusinessRules.UserInfoModels.PasswordChangeInput input);
```
#### Parameters

<a name='AlifeApi.WebApi.Controllers.UserInfoController.ChangePasswordAsync(AlifeApi.BusinessRules.UserInfoModels.PasswordChangeInput).input'></a>

`input` [AlifeApi.BusinessRules.UserInfoModels.PasswordChangeInput](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.BusinessRules.UserInfoModels.PasswordChangeInput 'AlifeApi.BusinessRules.UserInfoModels.PasswordChangeInput')

密碼修改輸入資料

#### Returns
[System.Threading.Tasks.Task&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')[Microsoft.AspNetCore.Mvc.ActionResult&lt;](https://docs.microsoft.com/en-us/dotnet/api/Microsoft.AspNetCore.Mvc.ActionResult-1 'Microsoft.AspNetCore.Mvc.ActionResult`1')[AlifeApi.BusinessRules.UserInfoModels.UserInfoUpdateOutput](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.BusinessRules.UserInfoModels.UserInfoUpdateOutput 'AlifeApi.BusinessRules.UserInfoModels.UserInfoUpdateOutput')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/Microsoft.AspNetCore.Mvc.ActionResult-1 'Microsoft.AspNetCore.Mvc.ActionResult`1')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')  
修改結果

<a name='AlifeApi.WebApi.Controllers.UserInfoController.CreateUserInfoAsync(AlifeApi.BusinessRules.UserInfoModels.UserInfoCreateInputPg)'></a>

## UserInfoController.CreateUserInfoAsync(UserInfoCreateInputPg) Method

帳號權限管理-帳號新增

```csharp
public System.Threading.Tasks.Task<Microsoft.AspNetCore.Mvc.ActionResult<AlifeApi.BusinessRules.UserInfoModels.UserInfoUpdateOutput>> CreateUserInfoAsync(AlifeApi.BusinessRules.UserInfoModels.UserInfoCreateInputPg input);
```
#### Parameters

<a name='AlifeApi.WebApi.Controllers.UserInfoController.CreateUserInfoAsync(AlifeApi.BusinessRules.UserInfoModels.UserInfoCreateInputPg).input'></a>

`input` [AlifeApi.BusinessRules.UserInfoModels.UserInfoCreateInputPg](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.BusinessRules.UserInfoModels.UserInfoCreateInputPg 'AlifeApi.BusinessRules.UserInfoModels.UserInfoCreateInputPg')

使用者新增輸入資料

#### Returns
[System.Threading.Tasks.Task&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')[Microsoft.AspNetCore.Mvc.ActionResult&lt;](https://docs.microsoft.com/en-us/dotnet/api/Microsoft.AspNetCore.Mvc.ActionResult-1 'Microsoft.AspNetCore.Mvc.ActionResult`1')[AlifeApi.BusinessRules.UserInfoModels.UserInfoUpdateOutput](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.BusinessRules.UserInfoModels.UserInfoUpdateOutput 'AlifeApi.BusinessRules.UserInfoModels.UserInfoUpdateOutput')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/Microsoft.AspNetCore.Mvc.ActionResult-1 'Microsoft.AspNetCore.Mvc.ActionResult`1')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')  
新增結果

<a name='AlifeApi.WebApi.Controllers.UserInfoController.GetUserInfoDropdownListAsync(AlifeApi.BusinessRules.UserInfoModels.UserInfoDropdownInput)'></a>

## UserInfoController.GetUserInfoDropdownListAsync(UserInfoDropdownInput) Method

取得使用者選單

```csharp
public System.Threading.Tasks.Task<Microsoft.AspNetCore.Mvc.ActionResult<System.Collections.Generic.List<AlifeApi.BusinessRules.UserInfoModels.UserInfoDropdownOutput>>> GetUserInfoDropdownListAsync(AlifeApi.BusinessRules.UserInfoModels.UserInfoDropdownInput input);
```
#### Parameters

<a name='AlifeApi.WebApi.Controllers.UserInfoController.GetUserInfoDropdownListAsync(AlifeApi.BusinessRules.UserInfoModels.UserInfoDropdownInput).input'></a>

`input` [AlifeApi.BusinessRules.UserInfoModels.UserInfoDropdownInput](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.BusinessRules.UserInfoModels.UserInfoDropdownInput 'AlifeApi.BusinessRules.UserInfoModels.UserInfoDropdownInput')

#### Returns
[System.Threading.Tasks.Task&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')[Microsoft.AspNetCore.Mvc.ActionResult&lt;](https://docs.microsoft.com/en-us/dotnet/api/Microsoft.AspNetCore.Mvc.ActionResult-1 'Microsoft.AspNetCore.Mvc.ActionResult`1')[System.Collections.Generic.List&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Collections.Generic.List-1 'System.Collections.Generic.List`1')[AlifeApi.BusinessRules.UserInfoModels.UserInfoDropdownOutput](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.BusinessRules.UserInfoModels.UserInfoDropdownOutput 'AlifeApi.BusinessRules.UserInfoModels.UserInfoDropdownOutput')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Collections.Generic.List-1 'System.Collections.Generic.List`1')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/Microsoft.AspNetCore.Mvc.ActionResult-1 'Microsoft.AspNetCore.Mvc.ActionResult`1')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')  
使用者下拉選單列表

<a name='AlifeApi.WebApi.Controllers.UserInfoController.GetUserInfoListAsync(AlifeApi.BusinessRules.UserInfoModels.UserInfoListGetInput)'></a>

## UserInfoController.GetUserInfoListAsync(UserInfoListGetInput) Method

帳號權限管理-使用者清單

```csharp
public System.Threading.Tasks.Task<Microsoft.AspNetCore.Mvc.ActionResult<AlifeApi.BusinessRules.Infrastructure.PagedListOutput<AlifeApi.BusinessRules.UserInfoModels.UserInfoListItemGetOutputPg>>> GetUserInfoListAsync(AlifeApi.BusinessRules.UserInfoModels.UserInfoListGetInput input);
```
#### Parameters

<a name='AlifeApi.WebApi.Controllers.UserInfoController.GetUserInfoListAsync(AlifeApi.BusinessRules.UserInfoModels.UserInfoListGetInput).input'></a>

`input` [AlifeApi.BusinessRules.UserInfoModels.UserInfoListGetInput](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.BusinessRules.UserInfoModels.UserInfoListGetInput 'AlifeApi.BusinessRules.UserInfoModels.UserInfoListGetInput')

#### Returns
[System.Threading.Tasks.Task&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')[Microsoft.AspNetCore.Mvc.ActionResult&lt;](https://docs.microsoft.com/en-us/dotnet/api/Microsoft.AspNetCore.Mvc.ActionResult-1 'Microsoft.AspNetCore.Mvc.ActionResult`1')[AlifeApi.BusinessRules.Infrastructure.PagedListOutput&lt;](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.BusinessRules.Infrastructure.PagedListOutput-1 'AlifeApi.BusinessRules.Infrastructure.PagedListOutput`1')[AlifeApi.BusinessRules.UserInfoModels.UserInfoListItemGetOutputPg](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.BusinessRules.UserInfoModels.UserInfoListItemGetOutputPg 'AlifeApi.BusinessRules.UserInfoModels.UserInfoListItemGetOutputPg')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.BusinessRules.Infrastructure.PagedListOutput-1 'AlifeApi.BusinessRules.Infrastructure.PagedListOutput`1')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/Microsoft.AspNetCore.Mvc.ActionResult-1 'Microsoft.AspNetCore.Mvc.ActionResult`1')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')  
使用者清單

<a name='AlifeApi.WebApi.Controllers.UserInfoController.UpdateUserInfoAsync(AlifeApi.BusinessRules.UserInfoModels.UserInfoUpdateInputPg)'></a>

## UserInfoController.UpdateUserInfoAsync(UserInfoUpdateInputPg) Method

修改使用者資料

```csharp
public System.Threading.Tasks.Task<Microsoft.AspNetCore.Mvc.ActionResult<AlifeApi.BusinessRules.UserInfoModels.UserInfoUpdateOutput>> UpdateUserInfoAsync(AlifeApi.BusinessRules.UserInfoModels.UserInfoUpdateInputPg input);
```
#### Parameters

<a name='AlifeApi.WebApi.Controllers.UserInfoController.UpdateUserInfoAsync(AlifeApi.BusinessRules.UserInfoModels.UserInfoUpdateInputPg).input'></a>

`input` [AlifeApi.BusinessRules.UserInfoModels.UserInfoUpdateInputPg](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.BusinessRules.UserInfoModels.UserInfoUpdateInputPg 'AlifeApi.BusinessRules.UserInfoModels.UserInfoUpdateInputPg')

使用者更新輸入資料

#### Returns
[System.Threading.Tasks.Task&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')[Microsoft.AspNetCore.Mvc.ActionResult&lt;](https://docs.microsoft.com/en-us/dotnet/api/Microsoft.AspNetCore.Mvc.ActionResult-1 'Microsoft.AspNetCore.Mvc.ActionResult`1')[AlifeApi.BusinessRules.UserInfoModels.UserInfoUpdateOutput](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.BusinessRules.UserInfoModels.UserInfoUpdateOutput 'AlifeApi.BusinessRules.UserInfoModels.UserInfoUpdateOutput')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/Microsoft.AspNetCore.Mvc.ActionResult-1 'Microsoft.AspNetCore.Mvc.ActionResult`1')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')  
修改結果

<a name='AlifeApi.WebApi.Controllers.UserInfoController.UpdateUserInfoStatusAsync(AlifeApi.BusinessRules.UserInfoModels.UserInfoStatusUpdateInputPg)'></a>

## UserInfoController.UpdateUserInfoStatusAsync(UserInfoStatusUpdateInputPg) Method

更新使用者帳號狀態

```csharp
public System.Threading.Tasks.Task<Microsoft.AspNetCore.Mvc.ActionResult<AlifeApi.BusinessRules.UserInfoModels.UserInfoUpdateOutput>> UpdateUserInfoStatusAsync(AlifeApi.BusinessRules.UserInfoModels.UserInfoStatusUpdateInputPg input);
```
#### Parameters

<a name='AlifeApi.WebApi.Controllers.UserInfoController.UpdateUserInfoStatusAsync(AlifeApi.BusinessRules.UserInfoModels.UserInfoStatusUpdateInputPg).input'></a>

`input` [AlifeApi.BusinessRules.UserInfoModels.UserInfoStatusUpdateInputPg](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.BusinessRules.UserInfoModels.UserInfoStatusUpdateInputPg 'AlifeApi.BusinessRules.UserInfoModels.UserInfoStatusUpdateInputPg')

使用者狀態更新輸入資料

#### Returns
[System.Threading.Tasks.Task&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')[Microsoft.AspNetCore.Mvc.ActionResult&lt;](https://docs.microsoft.com/en-us/dotnet/api/Microsoft.AspNetCore.Mvc.ActionResult-1 'Microsoft.AspNetCore.Mvc.ActionResult`1')[AlifeApi.BusinessRules.UserInfoModels.UserInfoUpdateOutput](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.BusinessRules.UserInfoModels.UserInfoUpdateOutput 'AlifeApi.BusinessRules.UserInfoModels.UserInfoUpdateOutput')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/Microsoft.AspNetCore.Mvc.ActionResult-1 'Microsoft.AspNetCore.Mvc.ActionResult`1')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')  
更新結果

<a name='AlifeApi.WebApi.Controllers.UserInfoController.UserLoginAsync(AlifeApi.Common.Util.IEncryptionHelper,AlifeApi.BusinessRules.LoginModels.UserLoginInput)'></a>

## UserInfoController.UserLoginAsync(IEncryptionHelper, UserLoginInput) Method

使用者登入

```csharp
public System.Threading.Tasks.Task<Microsoft.AspNetCore.Mvc.ActionResult<AlifeApi.BusinessRules.LoginModels.UserLoginPgOutput>> UserLoginAsync(AlifeApi.Common.Util.IEncryptionHelper encryptionHelper, AlifeApi.BusinessRules.LoginModels.UserLoginInput input);
```
#### Parameters

<a name='AlifeApi.WebApi.Controllers.UserInfoController.UserLoginAsync(AlifeApi.Common.Util.IEncryptionHelper,AlifeApi.BusinessRules.LoginModels.UserLoginInput).encryptionHelper'></a>

`encryptionHelper` [AlifeApi.Common.Util.IEncryptionHelper](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.Common.Util.IEncryptionHelper 'AlifeApi.Common.Util.IEncryptionHelper')

The encryption helper.

<a name='AlifeApi.WebApi.Controllers.UserInfoController.UserLoginAsync(AlifeApi.Common.Util.IEncryptionHelper,AlifeApi.BusinessRules.LoginModels.UserLoginInput).input'></a>

`input` [AlifeApi.BusinessRules.LoginModels.UserLoginInput](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.BusinessRules.LoginModels.UserLoginInput 'AlifeApi.BusinessRules.LoginModels.UserLoginInput')

The input.

#### Returns
[System.Threading.Tasks.Task&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')[Microsoft.AspNetCore.Mvc.ActionResult&lt;](https://docs.microsoft.com/en-us/dotnet/api/Microsoft.AspNetCore.Mvc.ActionResult-1 'Microsoft.AspNetCore.Mvc.ActionResult`1')[AlifeApi.BusinessRules.LoginModels.UserLoginPgOutput](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.BusinessRules.LoginModels.UserLoginPgOutput 'AlifeApi.BusinessRules.LoginModels.UserLoginPgOutput')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/Microsoft.AspNetCore.Mvc.ActionResult-1 'Microsoft.AspNetCore.Mvc.ActionResult`1')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')  
登入結果

<a name='AlifeApi.WebApi.Controllers.UserInfoController.UserLogoutAsync()'></a>

## UserInfoController.UserLogoutAsync() Method

使用者-使用者登出

```csharp
public System.Threading.Tasks.Task<Microsoft.AspNetCore.Mvc.ActionResult<string>> UserLogoutAsync();
```

#### Returns
[System.Threading.Tasks.Task&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')[Microsoft.AspNetCore.Mvc.ActionResult&lt;](https://docs.microsoft.com/en-us/dotnet/api/Microsoft.AspNetCore.Mvc.ActionResult-1 'Microsoft.AspNetCore.Mvc.ActionResult`1')[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/Microsoft.AspNetCore.Mvc.ActionResult-1 'Microsoft.AspNetCore.Mvc.ActionResult`1')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')  
ClientId