#### [AlifeApi.BusinessRules](index.md 'index')
### [AlifeApi.BusinessRules.CommonModels](AlifeApi.BusinessRules.CommonModels.md 'AlifeApi.BusinessRules.CommonModels')

## UnifiedSalesQueryInput Class

統一銷售查詢輸入

```csharp
public class UnifiedSalesQueryInput : AlifeApi.BusinessRules.Infrastructure.PagedListInput
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; [PagedListInput](AlifeApi.BusinessRules.Infrastructure.PagedListInput.md 'AlifeApi.BusinessRules.Infrastructure.PagedListInput') &#129106; UnifiedSalesQueryInput
### Properties

<a name='AlifeApi.BusinessRules.CommonModels.UnifiedSalesQueryInput.BuildingId'></a>

## UnifiedSalesQueryInput.BuildingId Property

建築物ID

```csharp
public System.Nullable<int> BuildingId { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

<a name='AlifeApi.BusinessRules.CommonModels.UnifiedSalesQueryInput.Keyword'></a>

## UnifiedSalesQueryInput.Keyword Property

關鍵字查詢

```csharp
public string? Keyword { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.CommonModels.UnifiedSalesQueryInput.SiteCode'></a>

## UnifiedSalesQueryInput.SiteCode Property

案場編號

```csharp
public string SiteCode { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.CommonModels.UnifiedSalesQueryInput.SortBy'></a>

## UnifiedSalesQueryInput.SortBy Property

排序欄位

```csharp
public string? SortBy { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.CommonModels.UnifiedSalesQueryInput.SortDirection'></a>

## UnifiedSalesQueryInput.SortDirection Property

排序方向 (asc/desc)

```csharp
public string? SortDirection { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.CommonModels.UnifiedSalesQueryInput.Status'></a>

## UnifiedSalesQueryInput.Status Property

狀態列表

```csharp
public System.Collections.Generic.List<string>? Status { get; set; }
```

#### Property Value
[System.Collections.Generic.List&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Collections.Generic.List-1 'System.Collections.Generic.List`1')[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Collections.Generic.List-1 'System.Collections.Generic.List`1')