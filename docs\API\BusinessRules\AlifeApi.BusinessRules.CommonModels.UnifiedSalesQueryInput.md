#### [AlifeApi.BusinessRules](index.md 'index')
### [AlifeApi.BusinessRules.CommonModels](AlifeApi.BusinessRules.CommonModels.md 'AlifeApi.BusinessRules.CommonModels')

## UnifiedSalesQueryInput Class

統一銷售查詢輸入

```csharp
public class UnifiedSalesQueryInput : AlifeApi.BusinessRules.Infrastructure.PagedListInput
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; [PagedListInput](AlifeApi.BusinessRules.Infrastructure.PagedListInput.md 'AlifeApi.BusinessRules.Infrastructure.PagedListInput') &#129106; UnifiedSalesQueryInput
### Properties

<a name='AlifeApi.BusinessRules.CommonModels.UnifiedSalesQueryInput.BuildingId'></a>

## UnifiedSalesQueryInput.BuildingId Property

建築物ID

```csharp
public System.Nullable<int> BuildingId { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

<a name='AlifeApi.BusinessRules.CommonModels.UnifiedSalesQueryInput.FloorId'></a>

## UnifiedSalesQueryInput.FloorId Property

樓層ID

```csharp
public System.Nullable<int> FloorId { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

<a name='AlifeApi.BusinessRules.CommonModels.UnifiedSalesQueryInput.ItemType'></a>

## UnifiedSalesQueryInput.ItemType Property

物件類別 ("房屋"、"車位"，空值表示全部)

```csharp
public string? ItemType { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.CommonModels.UnifiedSalesQueryInput.Number'></a>

## UnifiedSalesQueryInput.Number Property

編號/戶號（模糊查詢）

```csharp
public string? Number { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.CommonModels.UnifiedSalesQueryInput.SiteCode'></a>

## UnifiedSalesQueryInput.SiteCode Property

案場編號

```csharp
public string? SiteCode { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.CommonModels.UnifiedSalesQueryInput.Status'></a>

## UnifiedSalesQueryInput.Status Property

狀態

```csharp
public string? Status { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')