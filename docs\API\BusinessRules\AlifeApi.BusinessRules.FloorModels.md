#### [AlifeApi.BusinessRules](index.md 'index')

## AlifeApi.BusinessRules.FloorModels Namespace

| Classes | |
| :--- | :--- |
| [FloorCreateInput](AlifeApi.BusinessRules.FloorModels.FloorCreateInput.md 'AlifeApi.BusinessRules.FloorModels.FloorCreateInput') | 樓層建立輸入模型 |
| [FloorDropdownInput](AlifeApi.BusinessRules.FloorModels.FloorDropdownInput.md 'AlifeApi.BusinessRules.FloorModels.FloorDropdownInput') | 樓層下拉選單輸入資料 |
| [FloorDropdownOutput](AlifeApi.BusinessRules.FloorModels.FloorDropdownOutput.md 'AlifeApi.BusinessRules.FloorModels.FloorDropdownOutput') | 樓層下拉選單輸出資料 |
| [FloorListOutput](AlifeApi.BusinessRules.FloorModels.FloorListOutput.md 'AlifeApi.BusinessRules.FloorModels.FloorListOutput') | 樓層列表輸出項目 |
| [FloorOutput](AlifeApi.BusinessRules.FloorModels.FloorOutput.md 'AlifeApi.BusinessRules.FloorModels.FloorOutput') | 樓層詳細輸出模型 |
| [FloorQueryInput](AlifeApi.BusinessRules.FloorModels.FloorQueryInput.md 'AlifeApi.BusinessRules.FloorModels.FloorQueryInput') | 樓層列表查詢輸入模型 |
| [FloorService](AlifeApi.BusinessRules.FloorModels.FloorService.md 'AlifeApi.BusinessRules.FloorModels.FloorService') | 樓層服務 |
| [FloorUpdateInput](AlifeApi.BusinessRules.FloorModels.FloorUpdateInput.md 'AlifeApi.BusinessRules.FloorModels.FloorUpdateInput') | 樓層更新輸入模型 |
