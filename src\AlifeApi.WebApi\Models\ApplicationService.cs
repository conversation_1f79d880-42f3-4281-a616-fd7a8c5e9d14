﻿using AlifeApi.BusinessRules.Infrastructure;
using AlifeApi.Common;
using AlifeApi.Common.DependencyInjection;

namespace AlifeApi.WebApi.Models
{
    public abstract class ApplicationService : InfrastructureBase, IScopedDependency
    {
        public ApplicationService(IServiceProvider serviceProvider) : base(serviceProvider)
        {
        }

        public ICurrentUser CurrentUser => LazyServiceProvider.GetService<ICurrentUser>();
    }
}
