namespace AlifeApi.BusinessRules.ReviewModels
{
    /// <summary>
    /// 部門分組輸出資料
    /// </summary>
    public class ReviewDeptGroupOutput
    {
        /// <summary>
        /// 部門資料
        /// </summary>
        public ReviewDeptOutput Dept { get; set; }

        /// <summary>
        /// 部門下的用戶列表
        /// </summary>
        public List<ReviewUserOutput> Users { get; set; }
    }

    /// <summary>
    /// 部門輸出資料
    /// </summary>
    public class ReviewDeptOutput
    {
        /// <summary>
        /// 部門名稱
        /// </summary>
        public string DeptName { get; set; }
    }

    /// <summary>
    /// 審核人員輸出資料
    /// </summary>
    public class ReviewUserOutput
    {
        /// <summary>
        /// 用戶ID
        /// </summary>
        public string UserInfoId { get; set; }

        /// <summary>
        /// 用戶名稱
        /// </summary>
        public string UserName { get; set; }

        /// <summary>
        /// 部門ID
        /// </summary>
        public string DeptId { get; set; }

        /// <summary>
        /// 部門名稱
        /// </summary>
        public string DeptName { get; set; }

        /// <summary>
        /// 職稱代碼
        /// </summary>
        public string GradeCode { get; set; }

        /// <summary>
        /// 職稱名稱
        /// </summary>
        public string GradeName { get; set; }
    }
} 
