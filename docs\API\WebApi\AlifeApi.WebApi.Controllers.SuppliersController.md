#### [AlifeApi.WebApi](index.md 'index')
### [AlifeApi.WebApi.Controllers](AlifeApi.WebApi.Controllers.md 'AlifeApi.WebApi.Controllers')

## SuppliersController Class

供應商管理

```csharp
public class SuppliersController : AlifeApi.WebApi.Controllers.AuthenticatedController
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; [Microsoft.AspNetCore.Mvc.ControllerBase](https://docs.microsoft.com/en-us/dotnet/api/Microsoft.AspNetCore.Mvc.ControllerBase 'Microsoft.AspNetCore.Mvc.ControllerBase') &#129106; [Microsoft.AspNetCore.Mvc.Controller](https://docs.microsoft.com/en-us/dotnet/api/Microsoft.AspNetCore.Mvc.Controller 'Microsoft.AspNetCore.Mvc.Controller') &#129106; [AlifeApi.WebApi.Controllers.BasicController](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.WebApi.Controllers.BasicController 'AlifeApi.WebApi.Controllers.BasicController') &#129106; [AlifeApi.WebApi.Controllers.AuthenticatedController](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.WebApi.Controllers.AuthenticatedController 'AlifeApi.WebApi.Controllers.AuthenticatedController') &#129106; SuppliersController
### Methods

<a name='AlifeApi.WebApi.Controllers.SuppliersController.CreateSupplier(AlifeApi.BusinessRules.SupplierModels.SupplierCreateInput)'></a>

## SuppliersController.CreateSupplier(SupplierCreateInput) Method

新增供應商

```csharp
public System.Threading.Tasks.Task<Microsoft.AspNetCore.Mvc.ActionResult<object>> CreateSupplier(AlifeApi.BusinessRules.SupplierModels.SupplierCreateInput input);
```
#### Parameters

<a name='AlifeApi.WebApi.Controllers.SuppliersController.CreateSupplier(AlifeApi.BusinessRules.SupplierModels.SupplierCreateInput).input'></a>

`input` [AlifeApi.BusinessRules.SupplierModels.SupplierCreateInput](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.BusinessRules.SupplierModels.SupplierCreateInput 'AlifeApi.BusinessRules.SupplierModels.SupplierCreateInput')

供應商建立輸入資料

#### Returns
[System.Threading.Tasks.Task&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')[Microsoft.AspNetCore.Mvc.ActionResult&lt;](https://docs.microsoft.com/en-us/dotnet/api/Microsoft.AspNetCore.Mvc.ActionResult-1 'Microsoft.AspNetCore.Mvc.ActionResult`1')[System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/Microsoft.AspNetCore.Mvc.ActionResult-1 'Microsoft.AspNetCore.Mvc.ActionResult`1')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')  
新增的供應商ID

<a name='AlifeApi.WebApi.Controllers.SuppliersController.DeleteSupplier(int)'></a>

## SuppliersController.DeleteSupplier(int) Method

刪除供應商

```csharp
public System.Threading.Tasks.Task<Microsoft.AspNetCore.Mvc.ActionResult> DeleteSupplier(int supplierId);
```
#### Parameters

<a name='AlifeApi.WebApi.Controllers.SuppliersController.DeleteSupplier(int).supplierId'></a>

`supplierId` [System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')

供應商ID

#### Returns
[System.Threading.Tasks.Task&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')[Microsoft.AspNetCore.Mvc.ActionResult](https://docs.microsoft.com/en-us/dotnet/api/Microsoft.AspNetCore.Mvc.ActionResult 'Microsoft.AspNetCore.Mvc.ActionResult')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')  
NoContent

<a name='AlifeApi.WebApi.Controllers.SuppliersController.GetSupplier(int)'></a>

## SuppliersController.GetSupplier(int) Method

根據供應商ID取得詳細資訊

```csharp
public System.Threading.Tasks.Task<Microsoft.AspNetCore.Mvc.ActionResult<AlifeApi.BusinessRules.SupplierModels.SupplierOutput>> GetSupplier(int supplierId);
```
#### Parameters

<a name='AlifeApi.WebApi.Controllers.SuppliersController.GetSupplier(int).supplierId'></a>

`supplierId` [System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')

供應商ID

#### Returns
[System.Threading.Tasks.Task&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')[Microsoft.AspNetCore.Mvc.ActionResult&lt;](https://docs.microsoft.com/en-us/dotnet/api/Microsoft.AspNetCore.Mvc.ActionResult-1 'Microsoft.AspNetCore.Mvc.ActionResult`1')[AlifeApi.BusinessRules.SupplierModels.SupplierOutput](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.BusinessRules.SupplierModels.SupplierOutput 'AlifeApi.BusinessRules.SupplierModels.SupplierOutput')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/Microsoft.AspNetCore.Mvc.ActionResult-1 'Microsoft.AspNetCore.Mvc.ActionResult`1')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')  
供應商詳細資訊

<a name='AlifeApi.WebApi.Controllers.SuppliersController.GetSuppliers(AlifeApi.BusinessRules.SupplierModels.SupplierQueryInput)'></a>

## SuppliersController.GetSuppliers(SupplierQueryInput) Method

取得供應商列表 (分頁)

```csharp
public System.Threading.Tasks.Task<Microsoft.AspNetCore.Mvc.ActionResult<AlifeApi.BusinessRules.Infrastructure.PagedListOutput<AlifeApi.BusinessRules.SupplierModels.SupplierListOutput>>> GetSuppliers(AlifeApi.BusinessRules.SupplierModels.SupplierQueryInput input);
```
#### Parameters

<a name='AlifeApi.WebApi.Controllers.SuppliersController.GetSuppliers(AlifeApi.BusinessRules.SupplierModels.SupplierQueryInput).input'></a>

`input` [AlifeApi.BusinessRules.SupplierModels.SupplierQueryInput](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.BusinessRules.SupplierModels.SupplierQueryInput 'AlifeApi.BusinessRules.SupplierModels.SupplierQueryInput')

查詢條件

#### Returns
[System.Threading.Tasks.Task&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')[Microsoft.AspNetCore.Mvc.ActionResult&lt;](https://docs.microsoft.com/en-us/dotnet/api/Microsoft.AspNetCore.Mvc.ActionResult-1 'Microsoft.AspNetCore.Mvc.ActionResult`1')[AlifeApi.BusinessRules.Infrastructure.PagedListOutput&lt;](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.BusinessRules.Infrastructure.PagedListOutput-1 'AlifeApi.BusinessRules.Infrastructure.PagedListOutput`1')[AlifeApi.BusinessRules.SupplierModels.SupplierListOutput](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.BusinessRules.SupplierModels.SupplierListOutput 'AlifeApi.BusinessRules.SupplierModels.SupplierListOutput')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.BusinessRules.Infrastructure.PagedListOutput-1 'AlifeApi.BusinessRules.Infrastructure.PagedListOutput`1')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/Microsoft.AspNetCore.Mvc.ActionResult-1 'Microsoft.AspNetCore.Mvc.ActionResult`1')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')  
分頁的供應商列表

<a name='AlifeApi.WebApi.Controllers.SuppliersController.UpdateSupplier(int,AlifeApi.BusinessRules.SupplierModels.SupplierUpdateInput)'></a>

## SuppliersController.UpdateSupplier(int, SupplierUpdateInput) Method

更新供應商

```csharp
public System.Threading.Tasks.Task<Microsoft.AspNetCore.Mvc.ActionResult> UpdateSupplier(int supplierId, AlifeApi.BusinessRules.SupplierModels.SupplierUpdateInput input);
```
#### Parameters

<a name='AlifeApi.WebApi.Controllers.SuppliersController.UpdateSupplier(int,AlifeApi.BusinessRules.SupplierModels.SupplierUpdateInput).supplierId'></a>

`supplierId` [System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')

供應商ID

<a name='AlifeApi.WebApi.Controllers.SuppliersController.UpdateSupplier(int,AlifeApi.BusinessRules.SupplierModels.SupplierUpdateInput).input'></a>

`input` [AlifeApi.BusinessRules.SupplierModels.SupplierUpdateInput](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.BusinessRules.SupplierModels.SupplierUpdateInput 'AlifeApi.BusinessRules.SupplierModels.SupplierUpdateInput')

供應商更新輸入資料

#### Returns
[System.Threading.Tasks.Task&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')[Microsoft.AspNetCore.Mvc.ActionResult](https://docs.microsoft.com/en-us/dotnet/api/Microsoft.AspNetCore.Mvc.ActionResult 'Microsoft.AspNetCore.Mvc.ActionResult')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')  
NoContent