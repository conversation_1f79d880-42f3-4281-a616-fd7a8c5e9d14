﻿//using System.Data;
//using AlifeApi.BusinessRules.Infrastructure;
//using AlifeApi.Common.DataAnnotations;
//using AlifeApi.DataAccess.ProjectContext;
//using Microsoft.EntityFrameworkCore;

//namespace AlifeApi.BusinessRules.DeptModels
//{
//    public class DeptService : ServiceBase<ProjectContext>
//    {
//        public DeptService(IServiceProvider serviceProvider, ProjectContext dbContext) : base(serviceProvider, dbContext)
//        {
//        }

//        public async Task CreateDept(CreateDeptCondition input)
//        {
//            ArgumentNullException.ThrowIfNull(input, nameof(input));



//            if (await Db.UserDept.AnyAsync(x => x.Id == input.DeptId))
//            {
//                throw new KyException("部門代碼已存在");
//            }
//            var parentDept = await Db.UserDept.Include(x => x.UserDeptPermission).FirstOrDefaultAsync(x => x.Id == input.ParentDeptId);

//            if (parentDept != null)
//            {
//                var errorPermission = input.FuncIds.Except(parentDept.UserDeptPermission.Select(x => x.FuncId));
//                if (errorPermission.Any())
//                {
//                    throw new KyException($"{string.Join("、", errorPermission)},不存在於上層部門權限中");
//                }
//            }
//            var leaderUser = await Db.UserInfo.FirstOrDefaultAsync(x => x.Id == input.LeaderUserId);

//            Db.UserDept.Add(new UserDept()
//            {
//                Id = input.DeptId,
//                DeptName = input.DeptName,
//                ParentDeptId = parentDept?.Id,
//                LeaderUserId = leaderUser?.Id,
//                CreatedUserId = CurrentUser.UserId,
//                CreatedTime = DateTime.Now,
//                UpdatedUserId = CurrentUser.UserId,
//                UpdatedTime = DateTime.Now,
//                UserDeptPermission = input.FuncIds.Select(x => new UserDeptPermission()
//                {
//                    System = CurrentUser.System,
//                    FuncId = x
//                }).ToList()
//            });

//            await Db.SaveChangesAsync();
//        }

//        public async Task DeleteDept(DeleteDeptCondition input)
//        {
//            ArgumentNullException.ThrowIfNull(input, nameof(input));

//            var deptList = Db.UserDept.Include(x => x.UserInfo).Where(d => d.Id == input.DeptId).ToList();

//            if (!deptList.Any())
//                throw new KyException($"找不到部門");

//            // 递归查询子部门
//            void GetSubDepartments(string parentId)
//            {
//                var subDepartments = Db.UserDept.Include(x => x.UserInfo).Where(d => d.ParentDeptId == parentId).ToList();
//                foreach (var department in subDepartments)
//                {
//                    deptList.Add(department);
//                    GetSubDepartments(department.Id); // 遞迴查詢子部門
//                }
//            }

//            // 执行递归查询
//            GetSubDepartments(input.DeptId);

//            if (deptList.Where(x => x.UserInfo.Any()).Any())
//                throw new KyException($"尚有使用者存在於此部門或其子部門，請先將移轉使用者部門");

//            if (deptList.FirstOrDefault().IsDisabled)
//                deptList.FirstOrDefault().IsDisabled = false;
//            else
//                deptList.ForEach((dept) =>
//                {
//                    dept.IsDisabled = true;
//                });

//            await Db.SaveChangesAsync();
//        }

//        public async Task<PagedListOutput<DeptListResult>> GetDeptList(DeptListCondition input)
//        {
//            ArgumentNullException.ThrowIfNull(input, nameof(input));
//            PagedListOutput<DeptListResult> output = await Db.UserDept.AsNoTracking()
//                .Where(x => !x.IsDisabled)
//                .Select(s => new DeptListResult
//                {
//                    DeptId = s.Id,
//                    DeptName = s.DeptName,
//                    ParentDeptId = s.ParentDeptId,
//                    ParentDeptName = s.ParentDept.DeptName,
//                    LeaderUserId = s.LeaderUserId,
//                    CreatedUserId = s.CreatedUserId,
//                    CreatedTime = s.CreatedTime,
//                    UpdatedUserId = s.UpdatedUserId,
//                    UpdatedTime = s.UpdatedTime,
//                    IsDisabled = s.IsDisabled,
//                    FuncIds = s.UserDeptPermission.Select(x => x.FuncId),
//                    UserIds = s.UserInfo.Select(x => x.Id)
//                })
//                .ToPagedListOutputAsync(input);

//            return output;
//        }

//        public async Task UpdateDept(UpdateDeptCondition input)
//        {
//            ArgumentNullException.ThrowIfNull(input, nameof(input));

//            UserDept dept = await Db.UserDept
//                .Include(x => x.UserDeptPermission)
//                .SingleAsync(x => x.Id == input.DeptId);

//            var parentDept = await Db.UserDept.Include(x => x.UserDeptPermission).FirstOrDefaultAsync(x => x.Id == input.ParentDeptId);

//            if (parentDept != null)
//            {
//                var errorPermission = input.FuncIds.Except(parentDept.UserDeptPermission.Select(x => x.FuncId));
//                if (errorPermission.Any())
//                {
//                    throw new KyException($"{string.Join("、", errorPermission)},不存在於上層部門權限中");
//                }
//            }
//            var leaderUser = await Db.UserInfo.FirstOrDefaultAsync(x => x.Id == input.LeaderUserId);

//            dept.DeptName = input.DeptName;
//            dept.ParentDeptId = input.ParentDeptId;
//            dept.LeaderUserId = leaderUser?.Id;
//            dept.UpdatedUserId = CurrentUser.UserId;
//            dept.UpdatedTime = DateTime.Now;
//            dept.UserDeptPermission = input.FuncIds.Select(x => new UserDeptPermission()
//            {
//                System = CurrentUser.System,
//                FuncId = x
//            }).ToList();

//            await Db.SaveChangesAsync();
//        }

//        public async Task UpdateDeptPermission(UpdateDeptPermissionCondition input)
//        {
//            ArgumentNullException.ThrowIfNull(input, nameof(input));

//            UserDept dept = await Db.UserDept
//                .Include(x => x.UserDeptPermission)
//                .SingleAsync(x => x.Id == input.DeptId);

//            var parentDept = await Db.UserDept.Include(x => x.UserDeptPermission).FirstOrDefaultAsync(x => x.Id == dept.ParentDeptId);

//            if (parentDept != null)
//            {
//                var errorPermission = input.FuncIds.Except(parentDept.UserDeptPermission.Select(x => x.FuncId));
//                if (errorPermission.Any())
//                {
//                    throw new KyException($"{string.Join("、", errorPermission)},不存在於上層部門權限中");
//                }
//            }
//            dept.UpdatedUserId = CurrentUser.UserId;
//            dept.UpdatedTime = DateTime.Now;
//            dept.UserDeptPermission = input.FuncIds.Select(x => new UserDeptPermission()
//            {
//                System = CurrentUser.System,
//                FuncId = x
//            }).ToList();

//            await Db.SaveChangesAsync();
//        }
//    }
//}
