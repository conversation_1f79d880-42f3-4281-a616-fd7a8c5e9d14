#### [AlifeApi.DataAccess](index.md 'index')
### [AlifeApi.DataAccess.ProjectContent](AlifeApi.DataAccess.ProjectContent.md 'AlifeApi.DataAccess.ProjectContent')

## SiteCrmOption Class

儲存每個案場在不同CRM選項類型下的具體選項值

```csharp
public class SiteCrmOption
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; SiteCrmOption
### Properties

<a name='AlifeApi.DataAccess.ProjectContent.SiteCrmOption.CreatedUserInfoId'></a>

## SiteCrmOption.CreatedUserInfoId Property

建立者，記錄創建此資料的員工編號。

```csharp
public string CreatedUserInfoId { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContent.SiteCrmOption.CreateTime'></a>

## SiteCrmOption.CreateTime Property

創建時間，記錄資料創建的時間。

```csharp
public System.DateTime CreateTime { get; set; }
```

#### Property Value
[System.DateTime](https://docs.microsoft.com/en-us/dotnet/api/System.DateTime 'System.DateTime')

<a name='AlifeApi.DataAccess.ProjectContent.SiteCrmOption.CrmOptionTypeId'></a>

## SiteCrmOption.CrmOptionTypeId Property

關聯的選項類型ID (參考 CrmOptionTypes 表)

```csharp
public long CrmOptionTypeId { get; set; }
```

#### Property Value
[System.Int64](https://docs.microsoft.com/en-us/dotnet/api/System.Int64 'System.Int64')

<a name='AlifeApi.DataAccess.ProjectContent.SiteCrmOption.IsActive'></a>

## SiteCrmOption.IsActive Property

此選項是否啟用

```csharp
public System.Nullable<bool> IsActive { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.Boolean](https://docs.microsoft.com/en-us/dotnet/api/System.Boolean 'System.Boolean')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

<a name='AlifeApi.DataAccess.ProjectContent.SiteCrmOption.OptionValue'></a>

## SiteCrmOption.OptionValue Property

實際顯示在下拉選單中的文字 (例如: 20-30坪, 3房2廳)

```csharp
public string OptionValue { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContent.SiteCrmOption.SiteCode'></a>

## SiteCrmOption.SiteCode Property

關聯的案場代碼 (參考 Sites 表的 SiteCode)

```csharp
public string SiteCode { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContent.SiteCrmOption.SiteCrmOptionId'></a>

## SiteCrmOption.SiteCrmOptionId Property

主鍵，選項值唯一識別碼

```csharp
public long SiteCrmOptionId { get; set; }
```

#### Property Value
[System.Int64](https://docs.microsoft.com/en-us/dotnet/api/System.Int64 'System.Int64')

<a name='AlifeApi.DataAccess.ProjectContent.SiteCrmOption.SortOrder'></a>

## SiteCrmOption.SortOrder Property

在同一個下拉選單中的顯示順序，數字越小越前面

```csharp
public int SortOrder { get; set; }
```

#### Property Value
[System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')

<a name='AlifeApi.DataAccess.ProjectContent.SiteCrmOption.UpdatedUserInfoId'></a>

## SiteCrmOption.UpdatedUserInfoId Property

更新者，記錄最後更新此資料的員工編號。

```csharp
public string UpdatedUserInfoId { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContent.SiteCrmOption.UpdateTime'></a>

## SiteCrmOption.UpdateTime Property

更新時間，記錄資料最後更新的時間。

```csharp
public System.DateTime UpdateTime { get; set; }
```

#### Property Value
[System.DateTime](https://docs.microsoft.com/en-us/dotnet/api/System.DateTime 'System.DateTime')