#### [AlifeApi.BusinessRules](index.md 'index')
### [AlifeApi.BusinessRules.ReviewModels](AlifeApi.BusinessRules.ReviewModels.md 'AlifeApi.BusinessRules.ReviewModels')

## ReviewDeptOutput Class

部門輸出資料

```csharp
public class ReviewDeptOutput
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; ReviewDeptOutput
### Properties

<a name='AlifeApi.BusinessRules.ReviewModels.ReviewDeptOutput.DeptName'></a>

## ReviewDeptOutput.DeptName Property

部門名稱

```csharp
public string DeptName { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')