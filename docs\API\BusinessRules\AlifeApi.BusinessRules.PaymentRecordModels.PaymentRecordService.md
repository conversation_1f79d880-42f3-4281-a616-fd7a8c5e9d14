#### [AlifeApi.BusinessRules](index.md 'index')
### [AlifeApi.BusinessRules.PaymentRecordModels](AlifeApi.BusinessRules.PaymentRecordModels.md 'AlifeApi.BusinessRules.PaymentRecordModels')

## PaymentRecordService Class

收款記錄服務

```csharp
public class PaymentRecordService : AlifeApi.BusinessRules.Infrastructure.ServiceBase<AlifeApi.DataAccess.ProjectContent.alifeContext>
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; [AlifeApi.Common.InfrastructureBase](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.Common.InfrastructureBase 'AlifeApi.Common.InfrastructureBase') &#129106; [AlifeApi.BusinessRules.Infrastructure.ServiceBase&lt;](AlifeApi.BusinessRules.Infrastructure.ServiceBase_TDbContext_.md 'AlifeApi.BusinessRules.Infrastructure.ServiceBase<TDbContext>')[AlifeApi.DataAccess.ProjectContent.alifeContext](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.DataAccess.ProjectContent.alifeContext 'AlifeApi.DataAccess.ProjectContent.alifeContext')[&gt;](AlifeApi.BusinessRules.Infrastructure.ServiceBase_TDbContext_.md 'AlifeApi.BusinessRules.Infrastructure.ServiceBase<TDbContext>') &#129106; PaymentRecordService
### Methods

<a name='AlifeApi.BusinessRules.PaymentRecordModels.PaymentRecordService.CreatePaymentRecordAsync(AlifeApi.BusinessRules.PaymentRecordModels.PaymentRecordCreateInput)'></a>

## PaymentRecordService.CreatePaymentRecordAsync(PaymentRecordCreateInput) Method

建立收款記錄

```csharp
public System.Threading.Tasks.Task<int> CreatePaymentRecordAsync(AlifeApi.BusinessRules.PaymentRecordModels.PaymentRecordCreateInput input);
```
#### Parameters

<a name='AlifeApi.BusinessRules.PaymentRecordModels.PaymentRecordService.CreatePaymentRecordAsync(AlifeApi.BusinessRules.PaymentRecordModels.PaymentRecordCreateInput).input'></a>

`input` [PaymentRecordCreateInput](AlifeApi.BusinessRules.PaymentRecordModels.PaymentRecordCreateInput.md 'AlifeApi.BusinessRules.PaymentRecordModels.PaymentRecordCreateInput')

收款記錄建立輸入資料

#### Returns
[System.Threading.Tasks.Task&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')[System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')  
新建收款記錄的ID

<a name='AlifeApi.BusinessRules.PaymentRecordModels.PaymentRecordService.DeletePaymentRecordAsync(int)'></a>

## PaymentRecordService.DeletePaymentRecordAsync(int) Method

刪除收款記錄

```csharp
public System.Threading.Tasks.Task DeletePaymentRecordAsync(int paymentRecordId);
```
#### Parameters

<a name='AlifeApi.BusinessRules.PaymentRecordModels.PaymentRecordService.DeletePaymentRecordAsync(int).paymentRecordId'></a>

`paymentRecordId` [System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')

收款記錄ID

#### Returns
[System.Threading.Tasks.Task](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task 'System.Threading.Tasks.Task')

<a name='AlifeApi.BusinessRules.PaymentRecordModels.PaymentRecordService.GetPaymentRecordByIdAsync(int)'></a>

## PaymentRecordService.GetPaymentRecordByIdAsync(int) Method

根據ID取得收款記錄詳細資料

```csharp
public System.Threading.Tasks.Task<AlifeApi.BusinessRules.PaymentRecordModels.PaymentRecordOutput?> GetPaymentRecordByIdAsync(int paymentRecordId);
```
#### Parameters

<a name='AlifeApi.BusinessRules.PaymentRecordModels.PaymentRecordService.GetPaymentRecordByIdAsync(int).paymentRecordId'></a>

`paymentRecordId` [System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')

收款記錄ID

#### Returns
[System.Threading.Tasks.Task&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')[PaymentRecordOutput](AlifeApi.BusinessRules.PaymentRecordModels.PaymentRecordOutput.md 'AlifeApi.BusinessRules.PaymentRecordModels.PaymentRecordOutput')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')  
收款記錄詳細資料

<a name='AlifeApi.BusinessRules.PaymentRecordModels.PaymentRecordService.GetPaymentRecordListAsync(AlifeApi.BusinessRules.PaymentRecordModels.PaymentRecordQueryInput)'></a>

## PaymentRecordService.GetPaymentRecordListAsync(PaymentRecordQueryInput) Method

取得指定訂單的收款記錄列表 (分頁)

```csharp
public System.Threading.Tasks.Task<AlifeApi.BusinessRules.Infrastructure.PagedListOutput<AlifeApi.BusinessRules.PaymentRecordModels.PaymentRecordListOutput>> GetPaymentRecordListAsync(AlifeApi.BusinessRules.PaymentRecordModels.PaymentRecordQueryInput input);
```
#### Parameters

<a name='AlifeApi.BusinessRules.PaymentRecordModels.PaymentRecordService.GetPaymentRecordListAsync(AlifeApi.BusinessRules.PaymentRecordModels.PaymentRecordQueryInput).input'></a>

`input` [PaymentRecordQueryInput](AlifeApi.BusinessRules.PaymentRecordModels.PaymentRecordQueryInput.md 'AlifeApi.BusinessRules.PaymentRecordModels.PaymentRecordQueryInput')

查詢條件 (必須包含 OrderId)

#### Returns
[System.Threading.Tasks.Task&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')[AlifeApi.BusinessRules.Infrastructure.PagedListOutput&lt;](AlifeApi.BusinessRules.Infrastructure.PagedListOutput_TDetail_.md 'AlifeApi.BusinessRules.Infrastructure.PagedListOutput<TDetail>')[PaymentRecordListOutput](AlifeApi.BusinessRules.PaymentRecordModels.PaymentRecordListOutput.md 'AlifeApi.BusinessRules.PaymentRecordModels.PaymentRecordListOutput')[&gt;](AlifeApi.BusinessRules.Infrastructure.PagedListOutput_TDetail_.md 'AlifeApi.BusinessRules.Infrastructure.PagedListOutput<TDetail>')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')  
分頁的收款記錄列表

<a name='AlifeApi.BusinessRules.PaymentRecordModels.PaymentRecordService.UpdatePaymentRecordAsync(int,AlifeApi.BusinessRules.PaymentRecordModels.PaymentRecordUpdateInput)'></a>

## PaymentRecordService.UpdatePaymentRecordAsync(int, PaymentRecordUpdateInput) Method

更新收款記錄資訊

```csharp
public System.Threading.Tasks.Task UpdatePaymentRecordAsync(int paymentRecordId, AlifeApi.BusinessRules.PaymentRecordModels.PaymentRecordUpdateInput input);
```
#### Parameters

<a name='AlifeApi.BusinessRules.PaymentRecordModels.PaymentRecordService.UpdatePaymentRecordAsync(int,AlifeApi.BusinessRules.PaymentRecordModels.PaymentRecordUpdateInput).paymentRecordId'></a>

`paymentRecordId` [System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')

收款記錄ID

<a name='AlifeApi.BusinessRules.PaymentRecordModels.PaymentRecordService.UpdatePaymentRecordAsync(int,AlifeApi.BusinessRules.PaymentRecordModels.PaymentRecordUpdateInput).input'></a>

`input` [PaymentRecordUpdateInput](AlifeApi.BusinessRules.PaymentRecordModels.PaymentRecordUpdateInput.md 'AlifeApi.BusinessRules.PaymentRecordModels.PaymentRecordUpdateInput')

收款記錄更新輸入資料

#### Returns
[System.Threading.Tasks.Task](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task 'System.Threading.Tasks.Task')