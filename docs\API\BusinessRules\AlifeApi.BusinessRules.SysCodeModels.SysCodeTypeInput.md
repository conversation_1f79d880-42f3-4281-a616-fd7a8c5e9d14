#### [AlifeApi.BusinessRules](index.md 'index')
### [AlifeApi.BusinessRules.SysCodeModels](AlifeApi.BusinessRules.SysCodeModels.md 'AlifeApi.BusinessRules.SysCodeModels')

## SysCodeTypeInput Class

查詢特定代碼類別的輸入 DTO

```csharp
public class SysCodeTypeInput
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; SysCodeTypeInput
### Properties

<a name='AlifeApi.BusinessRules.SysCodeModels.SysCodeTypeInput.Type'></a>

## SysCodeTypeInput.Type Property

代碼類別

```csharp
public string Type { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')