#### [AlifeApi.BusinessRules](index.md 'index')
### [AlifeApi.BusinessRules.UserInfoModels](AlifeApi.BusinessRules.UserInfoModels.md 'AlifeApi.BusinessRules.UserInfoModels')

## UserInfoStatusUpdateInputPg Class

更新使用者狀態輸入資料 (PostgreSQL版本)

```csharp
public class UserInfoStatusUpdateInputPg
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; UserInfoStatusUpdateInputPg
### Properties

<a name='AlifeApi.BusinessRules.UserInfoModels.UserInfoStatusUpdateInputPg.Status'></a>

## UserInfoStatusUpdateInputPg.Status Property

帳號狀態 (true: 啟用, false: 停用)

```csharp
public bool Status { get; set; }
```

#### Property Value
[System.Boolean](https://docs.microsoft.com/en-us/dotnet/api/System.Boolean 'System.Boolean')

<a name='AlifeApi.BusinessRules.UserInfoModels.UserInfoStatusUpdateInputPg.UserInfoId'></a>

## UserInfoStatusUpdateInputPg.UserInfoId Property

使用者ID

```csharp
public string UserInfoId { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')