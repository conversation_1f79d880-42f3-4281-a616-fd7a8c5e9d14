#### [AlifeApi.BusinessRules](index.md 'index')
### [AlifeApi.BusinessRules.Infrastructure](AlifeApi.BusinessRules.Infrastructure.md 'AlifeApi.BusinessRules.Infrastructure')

## IImageStorageService Interface

圖片儲存服務介面

```csharp
public interface IImageStorageService
```

Derived  
&#8627; [ImageStorageService](AlifeApi.BusinessRules.Infrastructure.ImageStorageService.md 'AlifeApi.BusinessRules.Infrastructure.ImageStorageService')
### Methods

<a name='AlifeApi.BusinessRules.Infrastructure.IImageStorageService.DeleteImageAsync(string)'></a>

## IImageStorageService.DeleteImageAsync(string) Method

刪除圖片

```csharp
System.Threading.Tasks.Task DeleteImageAsync(string imagePath);
```
#### Parameters

<a name='AlifeApi.BusinessRules.Infrastructure.IImageStorageService.DeleteImageAsync(string).imagePath'></a>

`imagePath` [System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

圖片路徑 (檔案名稱)

#### Returns
[System.Threading.Tasks.Task](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task 'System.Threading.Tasks.Task')

<a name='AlifeApi.BusinessRules.Infrastructure.IImageStorageService.SaveImageAsync(string,string)'></a>

## IImageStorageService.SaveImageAsync(string, string) Method

儲存 Base64 編碼的圖片

```csharp
System.Threading.Tasks.Task<string> SaveImageAsync(string imageBase64, string customerId);
```
#### Parameters

<a name='AlifeApi.BusinessRules.Infrastructure.IImageStorageService.SaveImageAsync(string,string).imageBase64'></a>

`imageBase64` [System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

Base64 編碼的圖片字串

<a name='AlifeApi.BusinessRules.Infrastructure.IImageStorageService.SaveImageAsync(string,string).customerId'></a>

`customerId` [System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

客戶 ID (用於檔案命名)

#### Returns
[System.Threading.Tasks.Task&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')  
儲存路徑 (檔案名稱)