﻿using AlifeApi.BusinessRules.Infrastructure;
using AlifeApi.BusinessRules.SysCodeModels;
using Microsoft.AspNetCore.Mvc;
using AlifeApi.BusinessRules.SysCodeModels;

namespace AlifeApi.WebApi.Controllers
{
    /// <summary>
    /// 系統代碼(SysCode)
    /// </summary>
    public class SysCodeController : AuthenticatedController
    {
        private readonly SysCodeService _sysCodeService;

        /// <summary>
        /// Initializes a new instance of the <see cref="SysCodeController"/> class.
        /// </summary>
        /// <param name="sysCodeService">The system code service.</param>
        /// <exception cref="ArgumentNullException">sysCodeService</exception>
        public SysCodeController(SysCodeService sysCodeService)
        {
            _sysCodeService = sysCodeService ?? throw new ArgumentNullException(nameof(sysCodeService));
        }

        /// <summary>
        /// 取得所有 SysCode 類別 (Type)
        /// </summary>
        [HttpGet("types")]
        public async Task<IActionResult> GetAllSysCodeTypes()
        {
            var types = await _sysCodeService.GetAllSysCodeTypesAsync();
            return Ok(types);
        }

        /// <summary>
        /// 依類別取得所有 SysCode 條目
        /// </summary>
        [HttpGet("codes/{type}")]
        public async Task<IActionResult> GetSysCodesByType(string type)
        {
            var codes = await _sysCodeService.GetSysCodesByTypeAsync(type);
            return Ok(codes);
        }

        /// <summary>
        /// 新增代碼
        /// </summary>
        /// <param name="input">The input.</param>
        [HttpPost]
        public async Task CreateSysCodeAsync(SysCodeCreateInput input)
            => await _sysCodeService.CreateSysCodeAsync(input);

        /// <summary>
        /// 編輯、作廢代碼
        /// </summary>
        /// <param name="input">The input.</param>
        [HttpPost]
        public async Task EditSysCodeAsync(SysCodeUpdateInput input)
            => await _sysCodeService.UpdateSysCodeAsync(input);
    }
}
