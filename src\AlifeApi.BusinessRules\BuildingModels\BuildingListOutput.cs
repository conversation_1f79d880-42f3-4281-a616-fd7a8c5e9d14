using System;

namespace AlifeApi.BusinessRules.BuildingModels
{
    /// <summary>
    /// 建築物列表輸出項目
    /// </summary>
    public class BuildingListOutput
    {
        /// <summary>
        /// 建築物唯一識別碼
        /// </summary>
        public int BuildingId { get; set; }

        /// <summary>
        /// 案場代碼
        /// </summary>
        public string SiteCode { get; set; } = null!;

        /// <summary>
        /// 案場名稱
        /// </summary>
        public string? SiteName { get; set; }

        /// <summary>
        /// 建築物名稱
        /// </summary>
        public string BuildingName { get; set; } = null!;

        /// <summary>
        /// 地上樓層總數
        /// </summary>
        public int? TotalAboveGroundFloors { get; set; }

        /// <summary>
        /// 地下樓層總數
        /// </summary>
        public int? TotalBelowGroundFloors { get; set; }

        /// <summary>
        /// 建築物類型
        /// </summary>
        public string? BuildingType { get; set; }

        /// <summary>
        /// 完工日期
        /// </summary>
        public DateOnly? CompletionDate { get; set; }

        /// <summary>
        /// 建立時間
        /// </summary>
        public DateTime CreatedTime { get; set; }
    }
} 
