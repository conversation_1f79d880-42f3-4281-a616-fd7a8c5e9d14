using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Threading.Tasks;
using AlifeApi.BusinessRules.Infrastructure;
using AlifeApi.DataAccess.ProjectContent;
using Microsoft.EntityFrameworkCore;

namespace AlifeApi.BusinessRules.DailyReportModels
{
    /// <summary>
    /// 日報統計服務
    /// </summary>
    public class DailyReportService : ServiceBase<alifeContext>
    {
        /// <summary>
        /// 建構函數
        /// </summary>
        public DailyReportService(
            IServiceProvider serviceProvider,
            alifeContext dbContext)
            : base(serviceProvider, dbContext)
        {
        }

        /// <summary>
        /// 取得單日日報統計
        /// </summary>
        /// <param name="input">查詢條件</param>
        /// <returns>日報統計結果</returns>
        public async Task<DailyReportOutput> GetDailyReportAsync(DailyReportQueryInput input)
        {
            ArgumentNullException.ThrowIfNull(input, nameof(input));

            var reportDate = input.ReportDate.Date;
            var nextDate = reportDate.AddDays(1);

            // 取得案場資訊
            var site = await Db.Sites
                .Where(s => s.SiteCode == input.SiteCode)
                .Select(s => new { s.SiteCode, s.SiteName })
                .FirstOrDefaultAsync();

            if (site == null)
            {
                throw new ArgumentException($"找不到案場編號: {input.SiteCode}");
            }

            // 統計客戶記錄數據 - 分類統計來客、來電、留單
            var customerRecordStats = await Db.CustomerRecords
                .Where(cr => cr.RecordedAt >= reportDate && cr.RecordedAt < nextDate)
                .Join(Db.Customers, cr => cr.CustomerId, c => c.CustomerId, (cr, c) => new { cr, c })
                .Where(x => x.c.SiteCode == input.SiteCode)
                .GroupBy(x => x.cr.RecordType)
                .Select(g => new
                {
                    RecordType = g.Key,
                    Count = g.Count()
                })
                .ToListAsync();

            // 統計成交數據
            var transactionCount = await Db.PurchaseOrders
                .Where(po => po.OrderDate >= DateOnly.FromDateTime(reportDate) && po.OrderDate < DateOnly.FromDateTime(nextDate))
                .Where(po => po.SiteCode == input.SiteCode)
                .CountAsync();

            // 整理統計數據
            var visitorCount = customerRecordStats.FirstOrDefault(s => s.RecordType == "來客")?.Count ?? 0;
            var callCount = customerRecordStats.FirstOrDefault(s => s.RecordType == "來電")?.Count ?? 0;
            var leadCount = customerRecordStats.FirstOrDefault(s => s.RecordType == "留單")?.Count ?? 0;

            // 計算轉換率
            var visitorConversionRate = visitorCount > 0 ? Math.Round((decimal)leadCount / visitorCount * 100, 2) : 0;
            var callConversionRate = callCount > 0 ? Math.Round((decimal)leadCount / callCount * 100, 2) : 0;
            var transactionConversionRate = leadCount > 0 ? Math.Round((decimal)transactionCount / leadCount * 100, 2) : 0;

            // 取得客戶明細
            var customerDetails = await GetDailyCustomerDetailsAsync(input.SiteCode, reportDate, nextDate);

            var result = new DailyReportOutput
            {
                SiteCode = input.SiteCode,
                SiteName = site.SiteName,
                ReportDate = reportDate,
                DayOfWeek = GetChineseDayOfWeek(reportDate),
                VisitorCount = visitorCount,
                CallCount = callCount,
                LeadCount = leadCount,
                TransactionCount = transactionCount,
                VisitorConversionRate = visitorConversionRate,
                CallConversionRate = callConversionRate,
                TransactionConversionRate = transactionConversionRate,
                CustomerDetails = customerDetails
            };

            return result;
        }

        /// <summary>
        /// 取得期間日報統計列表
        /// </summary>
        /// <param name="input">查詢條件</param>
        /// <returns>期間日報統計</returns>
        public async Task<DailyReportListOutput> GetDailyReportListAsync(DailyReportPeriodQueryInput input)
        {
            ArgumentNullException.ThrowIfNull(input, nameof(input));

            // 驗證案場是否存在
            var siteExists = await Db.Sites.AnyAsync(s => s.SiteCode == input.SiteCode);
            if (!siteExists)
            {
                throw new ArgumentException($"找不到案場編號: {input.SiteCode}");
            }

            // 產生日期範圍
            var dateRange = GetDateRange(input.StartDate, input.EndDate);
            var reports = new List<DailyReportSummary>();

            // 批次查詢客戶記錄統計
            var customerRecordStats = await Db.CustomerRecords
                .Where(cr => cr.RecordedAt >= input.StartDate && cr.RecordedAt <= input.EndDate.AddDays(1).AddSeconds(-1))
                .Join(Db.Customers, cr => cr.CustomerId, c => c.CustomerId, (cr, c) => new { cr, c })
                .Where(x => x.c.SiteCode == input.SiteCode)
                .GroupBy(x => new 
                { 
                    Date = x.cr.RecordedAt.Date, 
                    RecordType = x.cr.RecordType 
                })
                .Select(g => new
                {
                    Date = g.Key.Date,
                    RecordType = g.Key.RecordType,
                    Count = g.Count()
                })
                .ToListAsync();

            // 批次查詢成交統計
            var transactionStats = await Db.PurchaseOrders
                .Where(po => po.OrderDate >= DateOnly.FromDateTime(input.StartDate) && po.OrderDate <= DateOnly.FromDateTime(input.EndDate))
                .Where(po => po.SiteCode == input.SiteCode)
                .GroupBy(po => po.OrderDate.ToDateTime(TimeOnly.MinValue).Date)
                .Select(g => new
                {
                    Date = g.Key,
                    Count = g.Count()
                })
                .ToListAsync();

            // 組織每日統計
            foreach (var date in dateRange)
            {
                var visitorCount = customerRecordStats.FirstOrDefault(s => s.Date == date && s.RecordType == "來客")?.Count ?? 0;
                var callCount = customerRecordStats.FirstOrDefault(s => s.Date == date && s.RecordType == "來電")?.Count ?? 0;
                var leadCount = customerRecordStats.FirstOrDefault(s => s.Date == date && s.RecordType == "留單")?.Count ?? 0;
                var transactionCount = transactionStats.FirstOrDefault(s => s.Date == date)?.Count ?? 0;

                var visitorConversionRate = visitorCount > 0 ? Math.Round((decimal)leadCount / visitorCount * 100, 2) : 0;
                var callConversionRate = callCount > 0 ? Math.Round((decimal)leadCount / callCount * 100, 2) : 0;
                var transactionConversionRate = leadCount > 0 ? Math.Round((decimal)transactionCount / leadCount * 100, 2) : 0;

                reports.Add(new DailyReportSummary
                {
                    ReportDate = date,
                    DayOfWeek = GetChineseDayOfWeek(date),
                    VisitorCount = visitorCount,
                    CallCount = callCount,
                    LeadCount = leadCount,
                    TransactionCount = transactionCount,
                    VisitorConversionRate = visitorConversionRate,
                    CallConversionRate = callConversionRate,
                    TransactionConversionRate = transactionConversionRate
                });
            }

            // 計算期間總計
            var periodSummary = CalculatePeriodSummary(input.StartDate, input.EndDate, reports);

            // 分頁處理
            var totalCount = reports.Count;
            var pagedReports = reports
                .Skip((input.Page - 1) * input.PageSize)
                .Take(input.PageSize)
                .ToList();

            return new DailyReportListOutput
            {
                TotalCount = totalCount,
                Reports = pagedReports,
                PeriodSummary = periodSummary
            };
        }

        /// <summary>
        /// 取得當日客戶明細
        /// </summary>
        private async Task<List<DailyCustomerDetail>> GetDailyCustomerDetailsAsync(string siteCode, DateTime reportDate, DateTime nextDate)
        {
            // 取得客戶記錄明細
            var customerRecords = await Db.CustomerRecords
                .Where(cr => cr.RecordedAt >= reportDate && cr.RecordedAt < nextDate)
                .Join(Db.Customers, cr => cr.CustomerId, c => c.CustomerId, (cr, c) => new { cr, c })
                .Where(x => x.c.SiteCode == siteCode)
                .OrderBy(x => x.cr.RecordedAt)
                .Select(x => new DailyCustomerDetail
                {
                    CustomerId = x.c.CustomerId,
                    CustomerName = x.c.Name,
                    PhoneNumber = x.c.PhoneNumber,
                    LeadSource = x.c.LeadSource,
                    RecordType = x.cr.RecordType,
                    RecordTime = x.cr.RecordedAt,
                    HandledBy = x.cr.HandledBy,
                    Notes = x.cr.Notes
                })
                .ToListAsync();

            // 取得成交記錄
            var transactionRecords = await Db.PurchaseOrders
                .Where(po => po.OrderDate >= DateOnly.FromDateTime(reportDate) && po.OrderDate < DateOnly.FromDateTime(nextDate))
                .Where(po => po.SiteCode == siteCode)
                .Join(Db.Customers, po => po.CustomerId, c => c.CustomerId, (po, c) => new { po, c })
                .OrderBy(x => x.po.OrderDate)
                .Select(x => new DailyCustomerDetail
                {
                    CustomerId = x.c.CustomerId,
                    CustomerName = x.c.Name,
                    PhoneNumber = x.c.PhoneNumber,
                    LeadSource = x.c.LeadSource,
                    RecordType = "成交",
                    RecordTime = x.po.OrderDate.ToDateTime(TimeOnly.MinValue),
                    HandledBy = x.po.SalespersonUserInfoId,
                    Notes = $"訂單號：{x.po.OrderNumber}，總價：{x.po.TotalPrice:N0}"
                })
                .ToListAsync();

            // 合併並排序
            var allRecords = customerRecords.Concat(transactionRecords)
                .OrderBy(r => r.RecordTime)
                .ToList();

            return allRecords;
        }

        /// <summary>
        /// 產生日期範圍
        /// </summary>
        private static List<DateTime> GetDateRange(DateTime startDate, DateTime endDate)
        {
            var dates = new List<DateTime>();
            var current = startDate.Date;
            var end = endDate.Date;

            while (current <= end)
            {
                dates.Add(current);
                current = current.AddDays(1);
            }

            return dates;
        }

        /// <summary>
        /// 計算期間總計統計
        /// </summary>
        private static DailyReportPeriodSummary CalculatePeriodSummary(DateTime startDate, DateTime endDate, List<DailyReportSummary> reports)
        {
            var totalVisitorCount = reports.Sum(r => r.VisitorCount);
            var totalCallCount = reports.Sum(r => r.CallCount);
            var totalLeadCount = reports.Sum(r => r.LeadCount);
            var totalTransactionCount = reports.Sum(r => r.TransactionCount);

            var averageVisitorConversionRate = reports.Count > 0 ? Math.Round(reports.Average(r => r.VisitorConversionRate), 2) : 0;
            var averageCallConversionRate = reports.Count > 0 ? Math.Round(reports.Average(r => r.CallConversionRate), 2) : 0;
            var averageTransactionConversionRate = reports.Count > 0 ? Math.Round(reports.Average(r => r.TransactionConversionRate), 2) : 0;

            return new DailyReportPeriodSummary
            {
                StartDate = startDate,
                EndDate = endDate,
                TotalVisitorCount = totalVisitorCount,
                TotalCallCount = totalCallCount,
                TotalLeadCount = totalLeadCount,
                TotalTransactionCount = totalTransactionCount,
                AverageVisitorConversionRate = averageVisitorConversionRate,
                AverageCallConversionRate = averageCallConversionRate,
                AverageTransactionConversionRate = averageTransactionConversionRate
            };
        }

        /// <summary>
        /// 取得中文星期顯示
        /// </summary>
        private static string GetChineseDayOfWeek(DateTime date)
        {
            return date.DayOfWeek switch
            {
                DayOfWeek.Sunday => "星期日",
                DayOfWeek.Monday => "星期一",
                DayOfWeek.Tuesday => "星期二",
                DayOfWeek.Wednesday => "星期三",
                DayOfWeek.Thursday => "星期四",
                DayOfWeek.Friday => "星期五",
                DayOfWeek.Saturday => "星期六",
                _ => date.DayOfWeek.ToString()
            };
        }
    }
} 
