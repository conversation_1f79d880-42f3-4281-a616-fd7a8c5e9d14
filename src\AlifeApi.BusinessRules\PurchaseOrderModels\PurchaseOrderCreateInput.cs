using System;
using System.ComponentModel.DataAnnotations;

namespace AlifeApi.BusinessRules.PurchaseOrderModels
{
    /// <summary>
    /// 買賣預定單建立輸入模型
    /// </summary>
    public class PurchaseOrderCreateInput
    {
        [Required(ErrorMessage = "缺少訂單日期")]
        public DateOnly OrderDate { get; set; }

        [Required(ErrorMessage = "缺少案場代碼")]
        public string SiteCode { get; set; } = null!;

        [Required(ErrorMessage = "缺少客戶ID")]
        public int CustomerId { get; set; }

        public string? SalesAgencyName { get; set; }
        public string? SalesAgencyMobile { get; set; }
        public string? SalesAgencyLandline { get; set; }
        public string? SalesAgencyEmail { get; set; }

        public int? UnitId { get; set; }
        public decimal? LandShareArea { get; set; }
        public string? PurchasedParkingSpaceIds { get; set; }
        public decimal? PropertyPrice { get; set; }
        public decimal? ParkingSpacePrice { get; set; }

        [Required(ErrorMessage = "缺少交易總價")]
        public decimal TotalPrice { get; set; }

        public decimal? DepositAmount { get; set; }
        public decimal? DepositPaidAmount { get; set; }
        public string? DepositPaymentMethod { get; set; }
        public string? DepositPayee { get; set; }
        public DateTime? DepositDueDate { get; set; }
        public decimal? DepositBalanceAmount { get; set; }
        public DateTime? ContractSigningAppointment { get; set; }
        public decimal? ContractSigningAmount { get; set; }
        public bool? ConsentToDataUsage { get; set; }
        public string? OrderRemarks { get; set; }

        [Required(ErrorMessage = "缺少銷售人員ID")]
        public string SalespersonUserInfoId { get; set; } = null!;

        public string? SaleType { get; set; }

        [Required(ErrorMessage = "缺少訂單狀態")]
        public string Status { get; set; } = null!;
    }
} 
