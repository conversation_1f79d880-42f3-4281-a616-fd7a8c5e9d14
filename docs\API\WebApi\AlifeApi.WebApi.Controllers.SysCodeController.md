#### [AlifeApi.WebApi](index.md 'index')
### [AlifeApi.WebApi.Controllers](AlifeApi.WebApi.Controllers.md 'AlifeApi.WebApi.Controllers')

## SysCodeController Class

系統代碼(SysCode)

```csharp
public class SysCodeController : AlifeApi.WebApi.Controllers.AuthenticatedController
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; [Microsoft.AspNetCore.Mvc.ControllerBase](https://docs.microsoft.com/en-us/dotnet/api/Microsoft.AspNetCore.Mvc.ControllerBase 'Microsoft.AspNetCore.Mvc.ControllerBase') &#129106; [Microsoft.AspNetCore.Mvc.Controller](https://docs.microsoft.com/en-us/dotnet/api/Microsoft.AspNetCore.Mvc.Controller 'Microsoft.AspNetCore.Mvc.Controller') &#129106; [AlifeApi.WebApi.Controllers.BasicController](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.WebApi.Controllers.BasicController 'AlifeApi.WebApi.Controllers.BasicController') &#129106; [AlifeApi.WebApi.Controllers.AuthenticatedController](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.WebApi.Controllers.AuthenticatedController 'AlifeApi.WebApi.Controllers.AuthenticatedController') &#129106; SysCodeController
### Constructors

<a name='AlifeApi.WebApi.Controllers.SysCodeController.SysCodeController(AlifeApi.BusinessRules.SysCodeModels.SysCodeService)'></a>

## SysCodeController(SysCodeService) Constructor

Initializes a new instance of the [SysCodeController](AlifeApi.WebApi.Controllers.SysCodeController.md 'AlifeApi.WebApi.Controllers.SysCodeController') class.

```csharp
public SysCodeController(AlifeApi.BusinessRules.SysCodeModels.SysCodeService sysCodeService);
```
#### Parameters

<a name='AlifeApi.WebApi.Controllers.SysCodeController.SysCodeController(AlifeApi.BusinessRules.SysCodeModels.SysCodeService).sysCodeService'></a>

`sysCodeService` [AlifeApi.BusinessRules.SysCodeModels.SysCodeService](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.BusinessRules.SysCodeModels.SysCodeService 'AlifeApi.BusinessRules.SysCodeModels.SysCodeService')

The system code service.

#### Exceptions

[System.ArgumentNullException](https://docs.microsoft.com/en-us/dotnet/api/System.ArgumentNullException 'System.ArgumentNullException')  
sysCodeService
### Methods

<a name='AlifeApi.WebApi.Controllers.SysCodeController.CreateSysCodeAsync(AlifeApi.BusinessRules.SysCodeModels.SysCodeCreateInput)'></a>

## SysCodeController.CreateSysCodeAsync(SysCodeCreateInput) Method

新增代碼

```csharp
public System.Threading.Tasks.Task CreateSysCodeAsync(AlifeApi.BusinessRules.SysCodeModels.SysCodeCreateInput input);
```
#### Parameters

<a name='AlifeApi.WebApi.Controllers.SysCodeController.CreateSysCodeAsync(AlifeApi.BusinessRules.SysCodeModels.SysCodeCreateInput).input'></a>

`input` [AlifeApi.BusinessRules.SysCodeModels.SysCodeCreateInput](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.BusinessRules.SysCodeModels.SysCodeCreateInput 'AlifeApi.BusinessRules.SysCodeModels.SysCodeCreateInput')

The input.

#### Returns
[System.Threading.Tasks.Task](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task 'System.Threading.Tasks.Task')

<a name='AlifeApi.WebApi.Controllers.SysCodeController.EditSysCodeAsync(AlifeApi.BusinessRules.SysCodeModels.SysCodeUpdateInput)'></a>

## SysCodeController.EditSysCodeAsync(SysCodeUpdateInput) Method

編輯、作廢代碼

```csharp
public System.Threading.Tasks.Task EditSysCodeAsync(AlifeApi.BusinessRules.SysCodeModels.SysCodeUpdateInput input);
```
#### Parameters

<a name='AlifeApi.WebApi.Controllers.SysCodeController.EditSysCodeAsync(AlifeApi.BusinessRules.SysCodeModels.SysCodeUpdateInput).input'></a>

`input` [AlifeApi.BusinessRules.SysCodeModels.SysCodeUpdateInput](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.BusinessRules.SysCodeModels.SysCodeUpdateInput 'AlifeApi.BusinessRules.SysCodeModels.SysCodeUpdateInput')

The input.

#### Returns
[System.Threading.Tasks.Task](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task 'System.Threading.Tasks.Task')

<a name='AlifeApi.WebApi.Controllers.SysCodeController.GetAllSysCodeTypes()'></a>

## SysCodeController.GetAllSysCodeTypes() Method

取得所有 SysCode 類別 (Type)

```csharp
public System.Threading.Tasks.Task<Microsoft.AspNetCore.Mvc.IActionResult> GetAllSysCodeTypes();
```

#### Returns
[System.Threading.Tasks.Task&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')[Microsoft.AspNetCore.Mvc.IActionResult](https://docs.microsoft.com/en-us/dotnet/api/Microsoft.AspNetCore.Mvc.IActionResult 'Microsoft.AspNetCore.Mvc.IActionResult')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')

<a name='AlifeApi.WebApi.Controllers.SysCodeController.GetSysCodesByType(string)'></a>

## SysCodeController.GetSysCodesByType(string) Method

依類別取得所有 SysCode 條目

```csharp
public System.Threading.Tasks.Task<Microsoft.AspNetCore.Mvc.IActionResult> GetSysCodesByType(string type);
```
#### Parameters

<a name='AlifeApi.WebApi.Controllers.SysCodeController.GetSysCodesByType(string).type'></a>

`type` [System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

#### Returns
[System.Threading.Tasks.Task&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')[Microsoft.AspNetCore.Mvc.IActionResult](https://docs.microsoft.com/en-us/dotnet/api/Microsoft.AspNetCore.Mvc.IActionResult 'Microsoft.AspNetCore.Mvc.IActionResult')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')