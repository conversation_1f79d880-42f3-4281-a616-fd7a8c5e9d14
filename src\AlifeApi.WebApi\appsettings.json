{"ConnectionStrings": {"DefaultConnection": "data source=localhost;initial catalog=PanAsia;TrustServerCertificate=true;persist security info=True;user id=ky-rd;password=************", "LogConnection": "data source=localhost;initial catalog=PanAsiaLog;TrustServerCertificate=true;persist security info=True;user id=ky-rd;password=************", "PostgresqlConnection": "Host=*************;Database=alife;Username=alife;Password=********;Trust Server Certificate=true", "PostgresqlLogConnection": "Host=*************;Database=alifeLog;Username=alife;Password=********;Trust Server Certificate=true"}, "System": {"SystemName": "AlifeA<PERSON>"}, "Jwt": {"Issuer": "LEX", "SignKey": "2023KunYouTechSetTheLexToTheKeyForTheAPBs"}, "ScheduleWork": {"Schedules": [{"ClassType": "AlifeApi.WebApi.Schedule.AutoLogOutSchedule", "CycleSeconds": "60", "Purpose": "自動登出", "Enabld": "true"}, {"ClassType": "AlifeApi.WebApi.Schedule.IdleAccountSchedule", "CycleSeconds": "86400", "Purpose": "帳號閒置", "Enabld": "true"}]}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "AllowedHosts": "*", "FilePath": "C:\\alifeFile", "ImageStorageSettings": {"LocalPath": "C:\\alifeImg", "ImageBaseUrl": "http://localhost:7169/alife/Img/", "NasPath": "\\\\*************\\home\\TestUpload", "NasCredentials": {"Username": "a-life", "Password": "(OL>1qaz5tgb"}}}