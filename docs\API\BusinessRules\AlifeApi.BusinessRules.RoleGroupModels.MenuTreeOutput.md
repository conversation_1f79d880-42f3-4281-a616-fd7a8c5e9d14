#### [AlifeApi.BusinessRules](index.md 'index')
### [AlifeApi.BusinessRules.RoleGroupModels](AlifeApi.BusinessRules.RoleGroupModels.md 'AlifeApi.BusinessRules.RoleGroupModels')

## MenuTreeOutput Class

權限

```csharp
public class MenuTreeOutput
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; MenuTreeOutput
### Properties

<a name='AlifeApi.BusinessRules.RoleGroupModels.MenuTreeOutput.Childrens'></a>

## MenuTreeOutput.Childrens Property

Children

```csharp
public System.Collections.Generic.IEnumerable<AlifeApi.BusinessRules.RoleGroupModels.MenuTreeOutput> Childrens { get; set; }
```

#### Property Value
[System.Collections.Generic.IEnumerable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Collections.Generic.IEnumerable-1 'System.Collections.Generic.IEnumerable`1')[MenuTreeOutput](AlifeApi.BusinessRules.RoleGroupModels.MenuTreeOutput.md 'AlifeApi.BusinessRules.RoleGroupModels.MenuTreeOutput')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Collections.Generic.IEnumerable-1 'System.Collections.Generic.IEnumerable`1')

<a name='AlifeApi.BusinessRules.RoleGroupModels.MenuTreeOutput.Id'></a>

## MenuTreeOutput.Id Property

ID

```csharp
public string Id { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.RoleGroupModels.MenuTreeOutput.Name'></a>

## MenuTreeOutput.Name Property

Name

```csharp
public string Name { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.RoleGroupModels.MenuTreeOutput.Selected'></a>

## MenuTreeOutput.Selected Property

Selected

```csharp
public bool Selected { get; set; }
```

#### Property Value
[System.Boolean](https://docs.microsoft.com/en-us/dotnet/api/System.Boolean 'System.Boolean')