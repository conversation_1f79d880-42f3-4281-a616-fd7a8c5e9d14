#### [AlifeApi.DataAccess](index.md 'index')
### [AlifeApi.DataAccess.ProjectContent](AlifeApi.DataAccess.ProjectContent.md 'AlifeApi.DataAccess.ProjectContent')

## Site Class

案場資料表，用於儲存案場的基本資訊、區域資料和合約相關資訊。

```csharp
public class Site
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; Site
### Properties

<a name='AlifeApi.DataAccess.ProjectContent.Site.AboveGroundFloors'></a>

## Site.AboveGroundFloors Property

規劃樓層(上層)，地上樓層數。

```csharp
public string AboveGroundFloors { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContent.Site.AdvertisingBudget'></a>

## Site.AdvertisingBudget Property

應編廣告預算，案場應編列的廣告預算金額。

```csharp
public System.Nullable<decimal> AdvertisingBudget { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.Decimal](https://docs.microsoft.com/en-us/dotnet/api/System.Decimal 'System.Decimal')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

<a name='AlifeApi.DataAccess.ProjectContent.Site.AdvertisingBudgetRate'></a>

## Site.AdvertisingBudgetRate Property

廣告預算率，廣告預算的比率（百分比）。

```csharp
public System.Nullable<decimal> AdvertisingBudgetRate { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.Decimal](https://docs.microsoft.com/en-us/dotnet/api/System.Decimal 'System.Decimal')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

<a name='AlifeApi.DataAccess.ProjectContent.Site.BelowGroundFloors'></a>

## Site.BelowGroundFloors Property

規劃樓層(下層)，地下樓層數。

```csharp
public string BelowGroundFloors { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContent.Site.Broker'></a>

## Site.Broker Property

經紀人，負責案場的經紀人名稱或編號。

```csharp
public string Broker { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContent.Site.BusinessIds'></a>

## Site.BusinessIds Property

業務，儲存以逗號分隔的字串（如 "pm8327,bnv783,oia198027,..."），對應多個 UserInfo 表的 UserInfoId。

```csharp
public string BusinessIds { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContent.Site.Chairman'></a>

## Site.Chairman Property

主委，儲存單一人員 ID，對應 UserInfo 表的 UserInfoId。

```csharp
public string Chairman { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContent.Site.City'></a>

## Site.City Property

所在縣市，案場所在的縣市。

```csharp
public string City { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContent.Site.CompanyId'></a>

## Site.CompanyId Property

執行公司別，對應 Company 表的 CompanyId，記錄案場所屬公司。

```csharp
public string CompanyId { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContent.Site.ContractedAmount'></a>

## Site.ContractedAmount Property

已發包金額，已發包的金額。

```csharp
public System.Nullable<decimal> ContractedAmount { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.Decimal](https://docs.microsoft.com/en-us/dotnet/api/System.Decimal 'System.Decimal')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

<a name='AlifeApi.DataAccess.ProjectContent.Site.ContractPeriod'></a>

## Site.ContractPeriod Property

合約期間，案場合約的期間（天數或月數）。

```csharp
public System.Nullable<System.DateOnly> ContractPeriod { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.DateOnly](https://docs.microsoft.com/en-us/dotnet/api/System.DateOnly 'System.DateOnly')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

<a name='AlifeApi.DataAccess.ProjectContent.Site.ControlReserveRate'></a>

## Site.ControlReserveRate Property

控存率，控制儲備的比率（百分比）。

```csharp
public System.Nullable<decimal> ControlReserveRate { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.Decimal](https://docs.microsoft.com/en-us/dotnet/api/System.Decimal 'System.Decimal')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

<a name='AlifeApi.DataAccess.ProjectContent.Site.CreatedTime'></a>

## Site.CreatedTime Property

創建時間，記錄資料創建的時間。

```csharp
public System.DateTime CreatedTime { get; set; }
```

#### Property Value
[System.DateTime](https://docs.microsoft.com/en-us/dotnet/api/System.DateTime 'System.DateTime')

<a name='AlifeApi.DataAccess.ProjectContent.Site.CreatedUserInfoId'></a>

## Site.CreatedUserInfoId Property

建立者，記錄創建此資料的員工編號，對應 UserInfo 表的 UserInfoId。

```csharp
public string CreatedUserInfoId { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContent.Site.DeputyProjectManager'></a>

## Site.DeputyProjectManager Property

副專，儲存單一人員 ID，對應 UserInfo 表的 UserInfoId。

```csharp
public string DeputyProjectManager { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContent.Site.Developer'></a>

## Site.Developer Property

投資興建，開發商或投資方的名稱。

```csharp
public string Developer { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContent.Site.District'></a>

## Site.District Property

所在區域，案場所在的區域或鄉鎮區。

```csharp
public string District { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContent.Site.ExcessPriceAllocation'></a>

## Site.ExcessPriceAllocation Property

超價款分配，超價款的分配方式。

```csharp
public string ExcessPriceAllocation { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContent.Site.ExtensionPeriod'></a>

## Site.ExtensionPeriod Property

展延期間，合約展延的期間（天數或月數）。

```csharp
public System.Nullable<System.DateOnly> ExtensionPeriod { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.DateOnly](https://docs.microsoft.com/en-us/dotnet/api/System.DateOnly 'System.DateOnly')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

<a name='AlifeApi.DataAccess.ProjectContent.Site.LandArea'></a>

## Site.LandArea Property

基地面積，案場用地的面積，單位為平方公尺。

```csharp
public System.Nullable<decimal> LandArea { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.Decimal](https://docs.microsoft.com/en-us/dotnet/api/System.Decimal 'System.Decimal')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

<a name='AlifeApi.DataAccess.ProjectContent.Site.PaidAmount'></a>

## Site.PaidAmount Property

已請款金額，已請款的金額，預設為 0。

```csharp
public System.Nullable<decimal> PaidAmount { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.Decimal](https://docs.microsoft.com/en-us/dotnet/api/System.Decimal 'System.Decimal')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

<a name='AlifeApi.DataAccess.ProjectContent.Site.ParkingType'></a>

## Site.ParkingType Property

車位類別，案場的車位類型（如機械車位、平面車位）。

```csharp
public string ParkingType { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContent.Site.PlannedParkingSpaces'></a>

## Site.PlannedParkingSpaces Property

規劃戶車(車位)，規劃的車位數。

```csharp
public System.Nullable<int> PlannedParkingSpaces { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

<a name='AlifeApi.DataAccess.ProjectContent.Site.PlannedResidentialUnits'></a>

## Site.PlannedResidentialUnits Property

規劃戶車(住家)，規劃的住宅戶數。

```csharp
public System.Nullable<int> PlannedResidentialUnits { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

<a name='AlifeApi.DataAccess.ProjectContent.Site.PlannedStoreUnits'></a>

## Site.PlannedStoreUnits Property

規劃戶車(店面)，規劃的店面戶數。

```csharp
public System.Nullable<int> PlannedStoreUnits { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

<a name='AlifeApi.DataAccess.ProjectContent.Site.ProjectManager'></a>

## Site.ProjectManager Property

專案，儲存單一人員 ID，對應 UserInfo 表的 UserInfoId。

```csharp
public string ProjectManager { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContent.Site.PromotionType'></a>

## Site.PromotionType Property

推案型態，描述案場的推案方式或類型。

```csharp
public string PromotionType { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContent.Site.PublicFacilityRatio'></a>

## Site.PublicFacilityRatio Property

公設比，公共設施比例（百分比）。

```csharp
public System.Nullable<decimal> PublicFacilityRatio { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.Decimal](https://docs.microsoft.com/en-us/dotnet/api/System.Decimal 'System.Decimal')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

<a name='AlifeApi.DataAccess.ProjectContent.Site.ReceptionCenter'></a>

## Site.ReceptionCenter Property

接待中心，案場的接待中心名稱或地址。

```csharp
public string ReceptionCenter { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContent.Site.ReserveAmount'></a>

## Site.ReserveAmount Property

保留款，案場的保留款金額。

```csharp
public System.Nullable<decimal> ReserveAmount { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.Decimal](https://docs.microsoft.com/en-us/dotnet/api/System.Decimal 'System.Decimal')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

<a name='AlifeApi.DataAccess.ProjectContent.Site.RunnerIds'></a>

## Site.RunnerIds Property

跑單，儲存以逗號分隔的字串（如 "pm8327,bnv783,oia198027,..."），對應多個 UserInfo 表的 UserInfoId。

```csharp
public string RunnerIds { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContent.Site.SellableTotalPrice'></a>

## Site.SellableTotalPrice Property

可售總銷，可供銷售的總金額。

```csharp
public System.Nullable<decimal> SellableTotalPrice { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.Decimal](https://docs.microsoft.com/en-us/dotnet/api/System.Decimal 'System.Decimal')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

<a name='AlifeApi.DataAccess.ProjectContent.Site.ServiceFeeCalculation'></a>

## Site.ServiceFeeCalculation Property

服務費計算，服務費的計算方式。

```csharp
public string ServiceFeeCalculation { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContent.Site.ServiceFeeRate'></a>

## Site.ServiceFeeRate Property

服務費率，服務費的比率（百分比）。

```csharp
public System.Nullable<decimal> ServiceFeeRate { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.Decimal](https://docs.microsoft.com/en-us/dotnet/api/System.Decimal 'System.Decimal')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

<a name='AlifeApi.DataAccess.ProjectContent.Site.SiteCode'></a>

## Site.SiteCode Property

案場號碼，主鍵，用於唯一識別案場。

```csharp
public string SiteCode { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContent.Site.SiteLocation'></a>

## Site.SiteLocation Property

基地位置，案場的具體地址。

```csharp
public string SiteLocation { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContent.Site.SiteName'></a>

## Site.SiteName Property

案名，案場名稱。

```csharp
public string SiteName { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContent.Site.SitePhone'></a>

## Site.SitePhone Property

案場電話，案場的聯絡電話。

```csharp
public string SitePhone { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContent.Site.Structure'></a>

## Site.Structure Property

結構，案場的建築結構類型。

```csharp
public string Structure { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContent.Site.TotalSalePrice'></a>

## Site.TotalSalePrice Property

全案總銷，案場的總銷售金額。

```csharp
public System.Nullable<decimal> TotalSalePrice { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.Decimal](https://docs.microsoft.com/en-us/dotnet/api/System.Decimal 'System.Decimal')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

<a name='AlifeApi.DataAccess.ProjectContent.Site.UnitSize'></a>

## Site.UnitSize Property

坪數，案場的單位面積（坪數）。

```csharp
public string UnitSize { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContent.Site.UpdatedTime'></a>

## Site.UpdatedTime Property

更新時間，記錄資料最後更新的時間。

```csharp
public System.DateTime UpdatedTime { get; set; }
```

#### Property Value
[System.DateTime](https://docs.microsoft.com/en-us/dotnet/api/System.DateTime 'System.DateTime')

<a name='AlifeApi.DataAccess.ProjectContent.Site.UpdatedUserInfoId'></a>

## Site.UpdatedUserInfoId Property

更新者，記錄最後更新此資料的員工編號，對應 UserInfo 表的 UserInfoId。

```csharp
public string UpdatedUserInfoId { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContent.Site.ViceChairman'></a>

## Site.ViceChairman Property

副主委，儲存單一人員 ID，對應 UserInfo 表的 UserInfoId。

```csharp
public string ViceChairman { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContent.Site.Zoning'></a>

## Site.Zoning Property

使用分區，案場所在的使用分區類型。

```csharp
public string Zoning { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')