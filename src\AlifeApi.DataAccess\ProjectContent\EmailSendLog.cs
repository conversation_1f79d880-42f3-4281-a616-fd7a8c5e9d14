﻿using System;
using System.Collections.Generic;

namespace AlifeApi.DataAccess.ProjectContent
{
    /// <summary>
    /// 電子郵件發送記錄表，用於儲存系統發送電子郵件的記錄資訊。
    /// </summary>
    public partial class EmailSendLog
    {
        /// <summary>
        /// 流水號，主鍵，用於唯一識別郵件發送記錄。
        /// </summary>
        public int EmailSendLogId { get; set; }
        /// <summary>
        /// 收件者電子郵件地址。
        /// </summary>
        public string RecipientEmail { get; set; }
        /// <summary>
        /// 收件者姓名。
        /// </summary>
        public string RecipientName { get; set; }
        /// <summary>
        /// 郵件主題。
        /// </summary>
        public string Subject { get; set; }
        /// <summary>
        /// 郵件內容。
        /// </summary>
        public string Body { get; set; }
        /// <summary>
        /// 郵件發送時間。
        /// </summary>
        public DateTime SentDate { get; set; }
        /// <summary>
        /// 是否發送成功，true 表示成功，false 表示失敗。
        /// </summary>
        public bool IsSuccess { get; set; }
        /// <summary>
        /// 若發送失敗，記錄錯誤訊息。
        /// </summary>
        public string ErrorMessage { get; set; }
        /// <summary>
        /// 報表統計的開始日期。
        /// </summary>
        public DateTime ReportStartDate { get; set; }
        /// <summary>
        /// 報表統計的結束日期。
        /// </summary>
        public DateTime ReportEndDate { get; set; }
        /// <summary>
        /// 附件的檔案名稱。
        /// </summary>
        public string AttachmentFileName { get; set; }
        /// <summary>
        /// 附件檔案大小，單位為位元組。
        /// </summary>
        public int? AttachmentFileSize { get; set; }
        /// <summary>
        /// 建立者，記錄創建此記錄的員工編號或系統帳號。
        /// </summary>
        public string CreatedUserInfoId { get; set; }
        /// <summary>
        /// 創建時間，記錄郵件記錄創建的時間。
        /// </summary>
        public DateTime CreatedTime { get; set; }
    }
}
