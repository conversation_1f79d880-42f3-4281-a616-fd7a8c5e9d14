#### [AlifeApi.DataAccess](index.md 'index')
### [AlifeApi.DataAccess.ProjectContent](AlifeApi.DataAccess.ProjectContent.md 'AlifeApi.DataAccess.ProjectContent')

## Building Class

儲存案場內每一棟獨立建築的基本資訊。

```csharp
public class Building
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; Building
### Properties

<a name='AlifeApi.DataAccess.ProjectContent.Building.BuildingId'></a>

## Building.BuildingId Property

建築物唯一識別碼 (主鍵, 自動遞增)。

```csharp
public int BuildingId { get; set; }
```

#### Property Value
[System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')

<a name='AlifeApi.DataAccess.ProjectContent.Building.BuildingName'></a>

## Building.BuildingName Property

建築物名稱或編號 (例如: "A棟", "帝寶一期", "Building 1")。

```csharp
public string BuildingName { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContent.Building.BuildingType'></a>

## Building.BuildingType Property

建築類型 (例如: "住宅", "商辦", "住商混合" - 建議關聯 SYS_Code)。

```csharp
public string BuildingType { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContent.Building.CompletionDate'></a>

## Building.CompletionDate Property

預計或實際完工日期。

```csharp
public System.Nullable<System.DateOnly> CompletionDate { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.DateOnly](https://docs.microsoft.com/en-us/dotnet/api/System.DateOnly 'System.DateOnly')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

<a name='AlifeApi.DataAccess.ProjectContent.Building.CreatedTime'></a>

## Building.CreatedTime Property

資料創建時間 (TIMESTAMP WITHOUT TIME ZONE)。

```csharp
public System.DateTime CreatedTime { get; set; }
```

#### Property Value
[System.DateTime](https://docs.microsoft.com/en-us/dotnet/api/System.DateTime 'System.DateTime')

<a name='AlifeApi.DataAccess.ProjectContent.Building.CreatedUserInfoId'></a>

## Building.CreatedUserInfoId Property

建立者 (參考 UserInfo 表)。

```csharp
public string CreatedUserInfoId { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContent.Building.Remarks'></a>

## Building.Remarks Property

備註。

```csharp
public string Remarks { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContent.Building.SiteCode'></a>

## Building.SiteCode Property

所屬案場編號 (參考 Sites 表)。

```csharp
public string SiteCode { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContent.Building.TotalAboveGroundFloors'></a>

## Building.TotalAboveGroundFloors Property

該棟地上總樓層數。

```csharp
public System.Nullable<int> TotalAboveGroundFloors { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

<a name='AlifeApi.DataAccess.ProjectContent.Building.TotalBelowGroundFloors'></a>

## Building.TotalBelowGroundFloors Property

該棟地下總樓層數。

```csharp
public System.Nullable<int> TotalBelowGroundFloors { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

<a name='AlifeApi.DataAccess.ProjectContent.Building.UpdatedTime'></a>

## Building.UpdatedTime Property

資料最後更新時間 (TIMESTAMP WITHOUT TIME ZONE)。

```csharp
public System.DateTime UpdatedTime { get; set; }
```

#### Property Value
[System.DateTime](https://docs.microsoft.com/en-us/dotnet/api/System.DateTime 'System.DateTime')

<a name='AlifeApi.DataAccess.ProjectContent.Building.UpdatedUserInfoId'></a>

## Building.UpdatedUserInfoId Property

更新者 (參考 UserInfo 表)。

```csharp
public string UpdatedUserInfoId { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')