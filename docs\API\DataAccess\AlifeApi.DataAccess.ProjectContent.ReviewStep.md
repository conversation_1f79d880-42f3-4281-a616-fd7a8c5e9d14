#### [AlifeApi.DataAccess](index.md 'index')
### [AlifeApi.DataAccess.ProjectContent](AlifeApi.DataAccess.ProjectContent.md 'AlifeApi.DataAccess.ProjectContent')

## ReviewStep Class

審核步驟表，用於儲存每個審核任務的具體步驟資訊。

```csharp
public class ReviewStep
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; ReviewStep
### Properties

<a name='AlifeApi.DataAccess.ProjectContent.ReviewStep.Name'></a>

## ReviewStep.Name Property

步驟名稱，描述步驟的具體內容。

```csharp
public string Name { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContent.ReviewStep.Sequence'></a>

## ReviewStep.Sequence Property

步驟順序，用於定義步驟的執行順序。

```csharp
public int Sequence { get; set; }
```

#### Property Value
[System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')

<a name='AlifeApi.DataAccess.ProjectContent.ReviewStep.Status'></a>

## ReviewStep.Status Property

步驟狀態，可選值為 waiting（等待中）、active（進行中）、completed（已完成）、skipped（已跳過）、rejected（已拒絕）。

```csharp
public string Status { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContent.ReviewStep.StepId'></a>

## ReviewStep.StepId Property

步驟流水號，主鍵，自動遞增，用於唯一識別審核步驟。

```csharp
public int StepId { get; set; }
```

#### Property Value
[System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')

<a name='AlifeApi.DataAccess.ProjectContent.ReviewStep.TaskId'></a>

## ReviewStep.TaskId Property

任務流水號，對應 ReviewTasks 表的 TaskId。

```csharp
public System.Nullable<int> TaskId { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

<a name='AlifeApi.DataAccess.ProjectContent.ReviewStep.TimeLimit'></a>

## ReviewStep.TimeLimit Property

時間限制，以小時為單位，指定步驟必須完成的時間。

```csharp
public System.Nullable<int> TimeLimit { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')