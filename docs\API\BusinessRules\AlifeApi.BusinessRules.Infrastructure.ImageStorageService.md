#### [AlifeApi.BusinessRules](index.md 'index')
### [AlifeApi.BusinessRules.Infrastructure](AlifeApi.BusinessRules.Infrastructure.md 'AlifeApi.BusinessRules.Infrastructure')

## ImageStorageService Class

圖片儲存服務實作

```csharp
public class ImageStorageService :
AlifeApi.BusinessRules.Infrastructure.IImageStorageService
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; ImageStorageService

Implements [IImageStorageService](AlifeApi.BusinessRules.Infrastructure.IImageStorageService.md 'AlifeApi.BusinessRules.Infrastructure.IImageStorageService')
### Constructors

<a name='AlifeApi.BusinessRules.Infrastructure.ImageStorageService.ImageStorageService(Microsoft.Extensions.Configuration.IConfiguration)'></a>

## ImageStorageService(IConfiguration) Constructor

建構函數

```csharp
public ImageStorageService(Microsoft.Extensions.Configuration.IConfiguration configuration);
```
#### Parameters

<a name='AlifeApi.BusinessRules.Infrastructure.ImageStorageService.ImageStorageService(Microsoft.Extensions.Configuration.IConfiguration).configuration'></a>

`configuration` [Microsoft.Extensions.Configuration.IConfiguration](https://docs.microsoft.com/en-us/dotnet/api/Microsoft.Extensions.Configuration.IConfiguration 'Microsoft.Extensions.Configuration.IConfiguration')
### Methods

<a name='AlifeApi.BusinessRules.Infrastructure.ImageStorageService.DeleteImageAsync(string)'></a>

## ImageStorageService.DeleteImageAsync(string) Method

刪除圖片

```csharp
public System.Threading.Tasks.Task DeleteImageAsync(string imagePath);
```
#### Parameters

<a name='AlifeApi.BusinessRules.Infrastructure.ImageStorageService.DeleteImageAsync(string).imagePath'></a>

`imagePath` [System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

Implements [DeleteImageAsync(string)](AlifeApi.BusinessRules.Infrastructure.IImageStorageService.md#AlifeApi.BusinessRules.Infrastructure.IImageStorageService.DeleteImageAsync(string) 'AlifeApi.BusinessRules.Infrastructure.IImageStorageService.DeleteImageAsync(string)')

#### Returns
[System.Threading.Tasks.Task](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task 'System.Threading.Tasks.Task')

<a name='AlifeApi.BusinessRules.Infrastructure.ImageStorageService.SaveImageAsync(string,string)'></a>

## ImageStorageService.SaveImageAsync(string, string) Method

儲存 Base64 編碼的圖片

```csharp
public System.Threading.Tasks.Task<string> SaveImageAsync(string imageBase64, string customerId);
```
#### Parameters

<a name='AlifeApi.BusinessRules.Infrastructure.ImageStorageService.SaveImageAsync(string,string).imageBase64'></a>

`imageBase64` [System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.Infrastructure.ImageStorageService.SaveImageAsync(string,string).customerId'></a>

`customerId` [System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

Implements [SaveImageAsync(string, string)](AlifeApi.BusinessRules.Infrastructure.IImageStorageService.md#AlifeApi.BusinessRules.Infrastructure.IImageStorageService.SaveImageAsync(string,string) 'AlifeApi.BusinessRules.Infrastructure.IImageStorageService.SaveImageAsync(string, string)')

#### Returns
[System.Threading.Tasks.Task&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')