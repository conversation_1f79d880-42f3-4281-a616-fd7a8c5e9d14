using AlifeApi.BusinessRules.Infrastructure;
using AlifeApi.DataAccess.ProjectContent;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace AlifeApi.BusinessRules.CrmOptionModels
{
    /// <summary>
    /// CRM選項服務
    /// </summary>
    public class CrmOptionService : ServiceBase<alifeContext>
    {
        public CrmOptionService(IServiceProvider serviceProvider, alifeContext dbContext)
            : base(serviceProvider, dbContext)
        {
        }

        /// <summary>
        /// 建立CRM選項
        /// </summary>
        /// <param name="input">CRM選項建立輸入資料</param>
        /// <returns>新建CRM選項的ID</returns>
        public async Task<long> CreateCrmOptionAsync(CrmOptionCreateInput input)
        {
            ArgumentNullException.ThrowIfNull(input);

            // 檢查 SiteCode 是否存在
            if (!await Db.Sites.AnyAsync(s => s.SiteCode == input.SiteCode))
            {
                throw new Exception($"指定的案場代碼 '{input.SiteCode}' 不存在。");
            }

            // 檢查 CrmOptionTypeId 是否存在
            if (!await Db.CrmOptionTypes.AnyAsync(c => c.CrmOptionTypeId == input.CrmOptionTypeId))
            {
                throw new Exception($"指定的CRM選項類型ID '{input.CrmOptionTypeId}' 不存在。");
            }

            // 檢查同一案場、同一選項類型下是否已有相同的選項值
            var existingOption = await Db.SiteCrmOptions
                .AnyAsync(s => s.SiteCode == input.SiteCode 
                            && s.CrmOptionTypeId == input.CrmOptionTypeId 
                            && s.OptionValue == input.OptionValue);

            if (existingOption)
            {
                throw new Exception($"案場 '{input.SiteCode}' 在此選項類型下已存在選項值 '{input.OptionValue}'。");
            }

            var crmOption = new SiteCrmOption
            {
                SiteCode = input.SiteCode,
                CrmOptionTypeId = input.CrmOptionTypeId,
                OptionValue = input.OptionValue,
                SortOrder = input.SortOrder,
                IsActive = input.IsActive,
                CreateTime = DateTime.Now,
                UpdateTime = DateTime.Now,
                CreatedUserInfoId = CurrentUser.UserId,
                UpdatedUserInfoId = CurrentUser.UserId
            };

            Db.SiteCrmOptions.Add(crmOption);
            await Db.SaveChangesAsync();

            return crmOption.SiteCrmOptionId;
        }

        /// <summary>
        /// 取得CRM選項列表 (分頁)
        /// </summary>
        /// <param name="input">查詢條件</param>
        /// <returns>分頁的CRM選項列表</returns>
        public async Task<PagedListOutput<CrmOptionListOutput>> GetCrmOptionListAsync(CrmOptionQueryInput input)
        {
            ArgumentNullException.ThrowIfNull(input);

            var query = Db.SiteCrmOptions.AsQueryable();

            // 篩選 SiteCode
            if (!string.IsNullOrEmpty(input.SiteCode))
            {
                query = query.Where(c => c.SiteCode == input.SiteCode);
            }

            // 篩選 CrmOptionTypeId
            if (input.CrmOptionTypeId.HasValue)
            {
                query = query.Where(c => c.CrmOptionTypeId == input.CrmOptionTypeId.Value);
            }

            // 篩選 OptionValue (模糊查詢)
            if (!string.IsNullOrEmpty(input.OptionValue))
            {
                query = query.Where(c => c.OptionValue.Contains(input.OptionValue));
            }

            // 篩選 IsActive
            if (input.IsActive.HasValue)
            {
                query = query.Where(c => c.IsActive == input.IsActive.Value);
            }

            var pagedResult = await query
                .OrderBy(c => c.SiteCode)
                .ThenBy(c => c.CrmOptionTypeId)
                .ThenBy(c => c.SortOrder)
                .Select(c => new CrmOptionListOutput
                {
                    SiteCrmOptionId = c.SiteCrmOptionId,
                    SiteCode = c.SiteCode,
                    CrmOptionTypeId = c.CrmOptionTypeId,
                    OptionValue = c.OptionValue,
                    SortOrder = c.SortOrder,
                    IsActive = c.IsActive,
                    CreateTime = c.CreateTime
                })
                .ToPagedListOutputAsync(input);

            // 填充案場名稱和選項類型名稱
            if (pagedResult.Details.Any())
            {
                await FillSiteAndTypeNamesAsync(pagedResult.Details);
            }

            return pagedResult;
        }

        /// <summary>
        /// 根據ID取得CRM選項詳細資料
        /// </summary>
        /// <param name="siteCrmOptionId">CRM選項ID</param>
        /// <returns>CRM選項詳細資料</returns>
        public async Task<CrmOptionOutput?> GetCrmOptionByIdAsync(long siteCrmOptionId)
        {
            var crmOption = await Db.SiteCrmOptions
                .Where(c => c.SiteCrmOptionId == siteCrmOptionId)
                .Select(c => new CrmOptionOutput
                {
                    SiteCrmOptionId = c.SiteCrmOptionId,
                    SiteCode = c.SiteCode,
                    CrmOptionTypeId = c.CrmOptionTypeId,
                    OptionValue = c.OptionValue,
                    SortOrder = c.SortOrder,
                    IsActive = c.IsActive,
                    CreateTime = c.CreateTime,
                    UpdateTime = c.UpdateTime,
                    CreatedUserInfoId = c.CreatedUserInfoId,
                    UpdatedUserInfoId = c.UpdatedUserInfoId
                })
                .FirstOrDefaultAsync();

            if (crmOption == null)
            {
                return null;
            }

            // 填充案場名稱和選項類型名稱
            await FillSiteAndTypeNamesAsync(new[] { crmOption });

            // 查詢並填入建立者和更新者名稱
            var userIds = new[] { crmOption.CreatedUserInfoId, crmOption.UpdatedUserInfoId }.Distinct().ToList();
            var users = await Db.UserInfos
                .Where(u => userIds.Contains(u.UserInfoId))
                .Select(u => new { u.UserInfoId, u.Name })
                .ToListAsync();
            var userMap = users.ToDictionary(u => u.UserInfoId, u => u.Name);

            if (userMap.TryGetValue(crmOption.CreatedUserInfoId, out var createdName))
            {
                crmOption.CreatedUserName = createdName;
            }
            if (userMap.TryGetValue(crmOption.UpdatedUserInfoId, out var updatedName))
            {
                crmOption.UpdatedUserName = updatedName;
            }

            return crmOption;
        }

        /// <summary>
        /// 更新CRM選項資訊
        /// </summary>
        /// <param name="siteCrmOptionId">CRM選項ID</param>
        /// <param name="input">CRM選項更新輸入資料</param>
        public async Task UpdateCrmOptionAsync(long siteCrmOptionId, CrmOptionUpdateInput input)
        {
            ArgumentNullException.ThrowIfNull(input);

            var crmOption = await Db.SiteCrmOptions.FindAsync(siteCrmOptionId);

            if (crmOption == null)
            {
                throw new Exception($"找不到指定的CRM選項 (ID: {siteCrmOptionId})");
            }

            // 檢查同一案場、同一選項類型下是否已有相同的選項值 (排除自己)
            var existingOption = await Db.SiteCrmOptions
                .AnyAsync(s => s.SiteCode == crmOption.SiteCode
                            && s.CrmOptionTypeId == crmOption.CrmOptionTypeId
                            && s.OptionValue == input.OptionValue
                            && s.SiteCrmOptionId != siteCrmOptionId);

            if (existingOption)
            {
                throw new Exception($"案場 '{crmOption.SiteCode}' 在此選項類型下已存在選項值 '{input.OptionValue}'。");
            }

            crmOption.OptionValue = input.OptionValue;
            crmOption.SortOrder = input.SortOrder;
            crmOption.IsActive = input.IsActive;
            crmOption.UpdateTime = DateTime.Now;
            crmOption.UpdatedUserInfoId = CurrentUser.UserId;

            await Db.SaveChangesAsync();
        }

        /// <summary>
        /// 刪除CRM選項
        /// </summary>
        /// <param name="siteCrmOptionId">CRM選項ID</param>
        public async Task DeleteCrmOptionAsync(long siteCrmOptionId)
        {
            var crmOption = await Db.SiteCrmOptions.FindAsync(siteCrmOptionId);

            if (crmOption == null)
            {
                // 允許刪除不存在的紀錄，使其具有幂等性
                return;
            }

            Db.SiteCrmOptions.Remove(crmOption);
            await Db.SaveChangesAsync();
        }

        /// <summary>
        /// 取得CRM選項下拉選單
        /// </summary>
        /// <param name="input">查詢條件</param>
        /// <returns>CRM選項下拉選單列表</returns>
        public async Task<List<CrmOptionDropdownOutput>> GetCrmOptionDropdownListAsync(CrmOptionDropdownInput input)
        {
            ArgumentNullException.ThrowIfNull(input, nameof(input));

            var query = Db.SiteCrmOptions
                .Where(c => c.SiteCode == input.SiteCode && c.CrmOptionTypeId == input.CrmOptionTypeId);

            if (input.OnlyActive)
            {
                query = query.Where(c => c.IsActive == true);
            }

            return await query
                .OrderBy(c => c.SortOrder)
                .ThenBy(c => c.OptionValue)
                .Select(c => new CrmOptionDropdownOutput
                {
                    Name = c.OptionValue,
                    Value = c.SiteCrmOptionId,
                    SortOrder = c.SortOrder
                })
                .ToListAsync();
        }

        /// <summary>
        /// 取得CRM選項類型下拉選單
        /// </summary>
        /// <returns>CRM選項類型下拉選單列表</returns>
        public async Task<List<CrmOptionTypeDropdownOutput>> GetCrmOptionTypeDropdownListAsync()
        {
            return await Db.CrmOptionTypes
                .OrderBy(c => c.TypeName)
                .Select(c => new CrmOptionTypeDropdownOutput
                {
                    Name = c.TypeName,
                    Value = c.CrmOptionTypeId,
                    Description = c.Description
                })
                .ToListAsync();
        }

        /// <summary>
        /// 填充案場名稱和選項類型名稱
        /// </summary>
        /// <param name="items">需要填充的項目列表</param>
        private async Task FillSiteAndTypeNamesAsync<T>(IEnumerable<T> items) where T : class
        {
            if (!items.Any()) return;

            // 取得所有需要的案場代碼和選項類型ID
            var siteCodes = new HashSet<string>();
            var crmOptionTypeIds = new HashSet<long>();

            foreach (var item in items)
            {
                var siteCodeProp = item.GetType().GetProperty("SiteCode");
                var crmOptionTypeIdProp = item.GetType().GetProperty("CrmOptionTypeId");

                if (siteCodeProp?.GetValue(item) is string siteCode)
                    siteCodes.Add(siteCode);

                if (crmOptionTypeIdProp?.GetValue(item) is long crmOptionTypeId)
                    crmOptionTypeIds.Add(crmOptionTypeId);
            }

            // 查詢案場名稱
            var siteNames = await Db.Sites
                .Where(s => siteCodes.Contains(s.SiteCode))
                .ToDictionaryAsync(s => s.SiteCode, s => s.SiteName);

            // 查詢選項類型名稱
            var crmOptionTypeNames = await Db.CrmOptionTypes
                .Where(c => crmOptionTypeIds.Contains(c.CrmOptionTypeId))
                .ToDictionaryAsync(c => c.CrmOptionTypeId, c => c.TypeName);

            // 填充名稱
            foreach (var item in items)
            {
                var siteCodeProp = item.GetType().GetProperty("SiteCode");
                var siteNameProp = item.GetType().GetProperty("SiteName");
                var crmOptionTypeIdProp = item.GetType().GetProperty("CrmOptionTypeId");
                var crmOptionTypeNameProp = item.GetType().GetProperty("CrmOptionTypeName");

                if (siteCodeProp?.GetValue(item) is string siteCode && siteNameProp != null)
                {
                    if (siteNames.TryGetValue(siteCode, out var siteName))
                        siteNameProp.SetValue(item, siteName);
                }

                if (crmOptionTypeIdProp?.GetValue(item) is long crmOptionTypeId && crmOptionTypeNameProp != null)
                {
                    if (crmOptionTypeNames.TryGetValue(crmOptionTypeId, out var typeName))
                        crmOptionTypeNameProp.SetValue(item, typeName);
                }
            }
        }
    }
}
