#### [AlifeApi.BusinessRules](index.md 'index')
### [AlifeApi.BusinessRules.ProblemReportModels](AlifeApi.BusinessRules.ProblemReportModels.md 'AlifeApi.BusinessRules.ProblemReportModels')

## ReportReplyInput Class

```csharp
public class ReportReplyInput
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; ReportReplyInput
### Properties

<a name='AlifeApi.BusinessRules.ProblemReportModels.ReportReplyInput.Id'></a>

## ReportReplyInput.Id Property

問題回報處理唯一識別值

```csharp
public long Id { get; set; }
```

#### Property Value
[System.Int64](https://docs.microsoft.com/en-us/dotnet/api/System.Int64 'System.Int64')

<a name='AlifeApi.BusinessRules.ProblemReportModels.ReportReplyInput.ProblemType'></a>

## ReportReplyInput.ProblemType Property

類型

```csharp
public string ProblemType { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.ProblemReportModels.ReportReplyInput.ProcessStatus'></a>

## ReportReplyInput.ProcessStatus Property

處理狀態

```csharp
public string ProcessStatus { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.ProblemReportModels.ReportReplyInput.ReplyContent'></a>

## ReportReplyInput.ReplyContent Property

回覆問題內容

```csharp
public string ReplyContent { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.ProblemReportModels.ReportReplyInput.ReplyTime'></a>

## ReportReplyInput.ReplyTime Property

處理時間

```csharp
public System.Nullable<System.DateTime> ReplyTime { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.DateTime](https://docs.microsoft.com/en-us/dotnet/api/System.DateTime 'System.DateTime')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

<a name='AlifeApi.BusinessRules.ProblemReportModels.ReportReplyInput.ReplyUserId'></a>

## ReportReplyInput.ReplyUserId Property

回覆人員

```csharp
public string ReplyUserId { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')