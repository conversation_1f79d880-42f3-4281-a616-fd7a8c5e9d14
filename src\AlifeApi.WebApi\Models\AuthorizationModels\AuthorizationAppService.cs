﻿using AlifeApi.WebApi.Util;

namespace AlifeApi.WebApi.Models.AuthorizationModels
{
    public class AuthorizationAppService : ApplicationService
    {
        private readonly JwtHelper jwtHelper;

        public AuthorizationAppService(IServiceProvider serviceProvider, JwtHelper jwtHelper) : base(serviceProvider)
        {
            this.jwtHelper = jwtHelper ?? throw new ArgumentNullException(nameof(jwtHelper));
        }

        /// <summary>
        /// Refreshes the token.
        /// </summary>
        /// <param name="token">The token.</param>
        /// <returns>Token info.</returns>
        public RefreshTokenOutput RefreshToken(string token)
        {
            RefreshTokenOutput output = new();
            if (string.IsNullOrEmpty(token))
            {
                return output;
            }

            DateTime? oldTime = jwtHelper.GetTokenExpirationTime(token);
            if (DateTime.Now > oldTime)
            {
                output.Message = "It is too late.";
                return output;
            }

            string newToekn = "Bearer " + jwtHelper.GenerateToken(
                CurrentUser.UserId, CurrentUser.DeptId, CurrentUser.GradeCode, CurrentUser.RoleIds);
            DateTime? expireDate = jwtHelper.GetTokenExpirationTime(newToekn);
            output.ExpireDate = expireDate;
            output.Token = newToekn;

            return output;
        }
    }
}
