﻿using System;
using System.Collections.Generic;

namespace AlifeApi.DataAccess.ProjectContent
{
    /// <summary>
    /// 部門資料表，用於儲存每個公司的部門資訊。
    /// </summary>
    public partial class Department
    {
        /// <summary>
        /// 公司編號，主鍵的一部分，對應 Company 表的 CompanyId。
        /// </summary>
        public string CompanyId { get; set; }
        /// <summary>
        /// 部門編號，主鍵的一部分，用於唯一識別部門。
        /// </summary>
        public string DepartmentId { get; set; }
        /// <summary>
        /// 部門名稱。
        /// </summary>
        public string Name { get; set; }
        /// <summary>
        /// 創建時間，記錄資料創建的時間。
        /// </summary>
        public DateTime CreatedTime { get; set; }
        /// <summary>
        /// 更新時間，記錄資料最後更新的時間。
        /// </summary>
        public DateTime UpdatedTime { get; set; }
        /// <summary>
        /// 建立者，記錄創建此資料的員工編號。
        /// </summary>
        public string CreatedUserInfoId { get; set; }
        /// <summary>
        /// 更新者，記錄最後更新此資料的員工編號。
        /// </summary>
        public string UpdatedUserInfoId { get; set; }

        public virtual Company Company { get; set; }
    }
}
