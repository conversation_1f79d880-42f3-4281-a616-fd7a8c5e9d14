#### [AlifeApi.WebApi](index.md 'index')
### [AlifeApi.WebApi.Controllers](AlifeApi.WebApi.Controllers.md 'AlifeApi.WebApi.Controllers')

## RoleGroupControllerPg Class

角色權限管理（PostgreSQL 版本）

```csharp
public class RoleGroupControllerPg : AlifeApi.WebApi.Controllers.AuthenticatedController
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; [Microsoft.AspNetCore.Mvc.ControllerBase](https://docs.microsoft.com/en-us/dotnet/api/Microsoft.AspNetCore.Mvc.ControllerBase 'Microsoft.AspNetCore.Mvc.ControllerBase') &#129106; [Microsoft.AspNetCore.Mvc.Controller](https://docs.microsoft.com/en-us/dotnet/api/Microsoft.AspNetCore.Mvc.Controller 'Microsoft.AspNetCore.Mvc.Controller') &#129106; [AlifeApi.WebApi.Controllers.BasicController](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.WebApi.Controllers.BasicController 'AlifeApi.WebApi.Controllers.BasicController') &#129106; [AlifeApi.WebApi.Controllers.AuthenticatedController](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.WebApi.Controllers.AuthenticatedController 'AlifeApi.WebApi.Controllers.AuthenticatedController') &#129106; RoleGroupControllerPg
### Constructors

<a name='AlifeApi.WebApi.Controllers.RoleGroupControllerPg.RoleGroupControllerPg(AlifeApi.BusinessRules.RoleGroupModels.RoleGroupServicePg)'></a>

## RoleGroupControllerPg(RoleGroupServicePg) Constructor

建構函數

```csharp
public RoleGroupControllerPg(AlifeApi.BusinessRules.RoleGroupModels.RoleGroupServicePg roleGroupService);
```
#### Parameters

<a name='AlifeApi.WebApi.Controllers.RoleGroupControllerPg.RoleGroupControllerPg(AlifeApi.BusinessRules.RoleGroupModels.RoleGroupServicePg).roleGroupService'></a>

`roleGroupService` [AlifeApi.BusinessRules.RoleGroupModels.RoleGroupServicePg](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.BusinessRules.RoleGroupModels.RoleGroupServicePg 'AlifeApi.BusinessRules.RoleGroupModels.RoleGroupServicePg')

角色權限服務

#### Exceptions

[System.ArgumentNullException](https://docs.microsoft.com/en-us/dotnet/api/System.ArgumentNullException 'System.ArgumentNullException')  
roleGroupService
### Fields

<a name='AlifeApi.WebApi.Controllers.RoleGroupControllerPg._roleGroupService'></a>

## RoleGroupControllerPg._roleGroupService Field

角色權限服務

```csharp
private readonly RoleGroupServicePg _roleGroupService;
```

#### Field Value
[AlifeApi.BusinessRules.RoleGroupModels.RoleGroupServicePg](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.BusinessRules.RoleGroupModels.RoleGroupServicePg 'AlifeApi.BusinessRules.RoleGroupModels.RoleGroupServicePg')
### Methods

<a name='AlifeApi.WebApi.Controllers.RoleGroupControllerPg.CreateRoleGroupAsync(AlifeApi.BusinessRules.RoleGroupModels.RoleGroupCreateInputPg)'></a>

## RoleGroupControllerPg.CreateRoleGroupAsync(RoleGroupCreateInputPg) Method

新增角色權限

```csharp
public System.Threading.Tasks.Task<Microsoft.AspNetCore.Mvc.ActionResult> CreateRoleGroupAsync(AlifeApi.BusinessRules.RoleGroupModels.RoleGroupCreateInputPg input);
```
#### Parameters

<a name='AlifeApi.WebApi.Controllers.RoleGroupControllerPg.CreateRoleGroupAsync(AlifeApi.BusinessRules.RoleGroupModels.RoleGroupCreateInputPg).input'></a>

`input` [AlifeApi.BusinessRules.RoleGroupModels.RoleGroupCreateInputPg](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.BusinessRules.RoleGroupModels.RoleGroupCreateInputPg 'AlifeApi.BusinessRules.RoleGroupModels.RoleGroupCreateInputPg')

角色權限輸入資料

#### Returns
[System.Threading.Tasks.Task&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')[Microsoft.AspNetCore.Mvc.ActionResult](https://docs.microsoft.com/en-us/dotnet/api/Microsoft.AspNetCore.Mvc.ActionResult 'Microsoft.AspNetCore.Mvc.ActionResult')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')  
執行結果

<a name='AlifeApi.WebApi.Controllers.RoleGroupControllerPg.DeleteRoleGroupAsync(AlifeApi.BusinessRules.RoleGroupModels.RoleGroupDeleteInputPg)'></a>

## RoleGroupControllerPg.DeleteRoleGroupAsync(RoleGroupDeleteInputPg) Method

刪除角色

```csharp
public System.Threading.Tasks.Task<Microsoft.AspNetCore.Mvc.ActionResult> DeleteRoleGroupAsync(AlifeApi.BusinessRules.RoleGroupModels.RoleGroupDeleteInputPg input);
```
#### Parameters

<a name='AlifeApi.WebApi.Controllers.RoleGroupControllerPg.DeleteRoleGroupAsync(AlifeApi.BusinessRules.RoleGroupModels.RoleGroupDeleteInputPg).input'></a>

`input` [AlifeApi.BusinessRules.RoleGroupModels.RoleGroupDeleteInputPg](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.BusinessRules.RoleGroupModels.RoleGroupDeleteInputPg 'AlifeApi.BusinessRules.RoleGroupModels.RoleGroupDeleteInputPg')

角色刪除輸入資料

#### Returns
[System.Threading.Tasks.Task&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')[Microsoft.AspNetCore.Mvc.ActionResult](https://docs.microsoft.com/en-us/dotnet/api/Microsoft.AspNetCore.Mvc.ActionResult 'Microsoft.AspNetCore.Mvc.ActionResult')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')  
執行結果

<a name='AlifeApi.WebApi.Controllers.RoleGroupControllerPg.GetMenuTreeAsync()'></a>

## RoleGroupControllerPg.GetMenuTreeAsync() Method

取得權限樹

```csharp
public System.Threading.Tasks.Task<Microsoft.AspNetCore.Mvc.ActionResult<System.Collections.Generic.IEnumerable<AlifeApi.BusinessRules.RoleGroupModels.MenuTreeOutput>>> GetMenuTreeAsync();
```

#### Returns
[System.Threading.Tasks.Task&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')[Microsoft.AspNetCore.Mvc.ActionResult&lt;](https://docs.microsoft.com/en-us/dotnet/api/Microsoft.AspNetCore.Mvc.ActionResult-1 'Microsoft.AspNetCore.Mvc.ActionResult`1')[System.Collections.Generic.IEnumerable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Collections.Generic.IEnumerable-1 'System.Collections.Generic.IEnumerable`1')[AlifeApi.BusinessRules.RoleGroupModels.MenuTreeOutput](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.BusinessRules.RoleGroupModels.MenuTreeOutput 'AlifeApi.BusinessRules.RoleGroupModels.MenuTreeOutput')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Collections.Generic.IEnumerable-1 'System.Collections.Generic.IEnumerable`1')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/Microsoft.AspNetCore.Mvc.ActionResult-1 'Microsoft.AspNetCore.Mvc.ActionResult`1')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')  
權限樹

<a name='AlifeApi.WebApi.Controllers.RoleGroupControllerPg.GetRoleGroupByIdAsync(string,string)'></a>

## RoleGroupControllerPg.GetRoleGroupByIdAsync(string, string) Method

根據ID獲取角色權限詳細資訊

```csharp
public System.Threading.Tasks.Task<Microsoft.AspNetCore.Mvc.ActionResult<AlifeApi.BusinessRules.RoleGroupModels.RoleGroupOutputPg>> GetRoleGroupByIdAsync(string id, string siteCode=null);
```
#### Parameters

<a name='AlifeApi.WebApi.Controllers.RoleGroupControllerPg.GetRoleGroupByIdAsync(string,string).id'></a>

`id` [System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

角色ID

<a name='AlifeApi.WebApi.Controllers.RoleGroupControllerPg.GetRoleGroupByIdAsync(string,string).siteCode'></a>

`siteCode` [System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

案場代碼

#### Returns
[System.Threading.Tasks.Task&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')[Microsoft.AspNetCore.Mvc.ActionResult&lt;](https://docs.microsoft.com/en-us/dotnet/api/Microsoft.AspNetCore.Mvc.ActionResult-1 'Microsoft.AspNetCore.Mvc.ActionResult`1')[AlifeApi.BusinessRules.RoleGroupModels.RoleGroupOutputPg](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.BusinessRules.RoleGroupModels.RoleGroupOutputPg 'AlifeApi.BusinessRules.RoleGroupModels.RoleGroupOutputPg')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/Microsoft.AspNetCore.Mvc.ActionResult-1 'Microsoft.AspNetCore.Mvc.ActionResult`1')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')  
角色權限詳細資訊

<a name='AlifeApi.WebApi.Controllers.RoleGroupControllerPg.GetRoleGroupDropdownListAsync(AlifeApi.BusinessRules.RoleGroupModels.RoleGroupDropdownInputPg)'></a>

## RoleGroupControllerPg.GetRoleGroupDropdownListAsync(RoleGroupDropdownInputPg) Method

取得角色群組下拉選單

```csharp
public System.Threading.Tasks.Task<Microsoft.AspNetCore.Mvc.ActionResult<System.Collections.Generic.List<AlifeApi.BusinessRules.RoleGroupModels.RoleGroupDropdownOutput>>> GetRoleGroupDropdownListAsync(AlifeApi.BusinessRules.RoleGroupModels.RoleGroupDropdownInputPg input);
```
#### Parameters

<a name='AlifeApi.WebApi.Controllers.RoleGroupControllerPg.GetRoleGroupDropdownListAsync(AlifeApi.BusinessRules.RoleGroupModels.RoleGroupDropdownInputPg).input'></a>

`input` [AlifeApi.BusinessRules.RoleGroupModels.RoleGroupDropdownInputPg](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.BusinessRules.RoleGroupModels.RoleGroupDropdownInputPg 'AlifeApi.BusinessRules.RoleGroupModels.RoleGroupDropdownInputPg')

查詢條件

#### Returns
[System.Threading.Tasks.Task&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')[Microsoft.AspNetCore.Mvc.ActionResult&lt;](https://docs.microsoft.com/en-us/dotnet/api/Microsoft.AspNetCore.Mvc.ActionResult-1 'Microsoft.AspNetCore.Mvc.ActionResult`1')[System.Collections.Generic.List&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Collections.Generic.List-1 'System.Collections.Generic.List`1')[AlifeApi.BusinessRules.RoleGroupModels.RoleGroupDropdownOutput](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.BusinessRules.RoleGroupModels.RoleGroupDropdownOutput 'AlifeApi.BusinessRules.RoleGroupModels.RoleGroupDropdownOutput')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Collections.Generic.List-1 'System.Collections.Generic.List`1')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/Microsoft.AspNetCore.Mvc.ActionResult-1 'Microsoft.AspNetCore.Mvc.ActionResult`1')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')  
角色群組下拉選單列表

<a name='AlifeApi.WebApi.Controllers.RoleGroupControllerPg.GetRoleGroupListAsync(AlifeApi.BusinessRules.RoleGroupModels.RoleGroupListGetInputPg)'></a>

## RoleGroupControllerPg.GetRoleGroupListAsync(RoleGroupListGetInputPg) Method

取得角色權限列表

```csharp
public System.Threading.Tasks.Task<AlifeApi.BusinessRules.Infrastructure.PagedListOutput<AlifeApi.BusinessRules.RoleGroupModels.RoleGroupOutputPg>> GetRoleGroupListAsync(AlifeApi.BusinessRules.RoleGroupModels.RoleGroupListGetInputPg input);
```
#### Parameters

<a name='AlifeApi.WebApi.Controllers.RoleGroupControllerPg.GetRoleGroupListAsync(AlifeApi.BusinessRules.RoleGroupModels.RoleGroupListGetInputPg).input'></a>

`input` [AlifeApi.BusinessRules.RoleGroupModels.RoleGroupListGetInputPg](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.BusinessRules.RoleGroupModels.RoleGroupListGetInputPg 'AlifeApi.BusinessRules.RoleGroupModels.RoleGroupListGetInputPg')

查詢條件

#### Returns
[System.Threading.Tasks.Task&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')[AlifeApi.BusinessRules.Infrastructure.PagedListOutput&lt;](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.BusinessRules.Infrastructure.PagedListOutput-1 'AlifeApi.BusinessRules.Infrastructure.PagedListOutput`1')[AlifeApi.BusinessRules.RoleGroupModels.RoleGroupOutputPg](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.BusinessRules.RoleGroupModels.RoleGroupOutputPg 'AlifeApi.BusinessRules.RoleGroupModels.RoleGroupOutputPg')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.BusinessRules.Infrastructure.PagedListOutput-1 'AlifeApi.BusinessRules.Infrastructure.PagedListOutput`1')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')  
角色權限列表

<a name='AlifeApi.WebApi.Controllers.RoleGroupControllerPg.UpdateRoleGroupPermissionAsync(AlifeApi.BusinessRules.RoleGroupModels.RoleGroupPermissionUpdateInputPg)'></a>

## RoleGroupControllerPg.UpdateRoleGroupPermissionAsync(RoleGroupPermissionUpdateInputPg) Method

修改角色權限功能

```csharp
public System.Threading.Tasks.Task<Microsoft.AspNetCore.Mvc.ActionResult> UpdateRoleGroupPermissionAsync(AlifeApi.BusinessRules.RoleGroupModels.RoleGroupPermissionUpdateInputPg input);
```
#### Parameters

<a name='AlifeApi.WebApi.Controllers.RoleGroupControllerPg.UpdateRoleGroupPermissionAsync(AlifeApi.BusinessRules.RoleGroupModels.RoleGroupPermissionUpdateInputPg).input'></a>

`input` [AlifeApi.BusinessRules.RoleGroupModels.RoleGroupPermissionUpdateInputPg](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.BusinessRules.RoleGroupModels.RoleGroupPermissionUpdateInputPg 'AlifeApi.BusinessRules.RoleGroupModels.RoleGroupPermissionUpdateInputPg')

角色權限更新輸入資料

#### Returns
[System.Threading.Tasks.Task&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')[Microsoft.AspNetCore.Mvc.ActionResult](https://docs.microsoft.com/en-us/dotnet/api/Microsoft.AspNetCore.Mvc.ActionResult 'Microsoft.AspNetCore.Mvc.ActionResult')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')  
執行結果

<a name='AlifeApi.WebApi.Controllers.RoleGroupControllerPg.UpdateRoleGroupUserAsync(AlifeApi.BusinessRules.RoleGroupModels.RoleGroupUserUpdateInputPg)'></a>

## RoleGroupControllerPg.UpdateRoleGroupUserAsync(RoleGroupUserUpdateInputPg) Method

修改角色權限使用者

```csharp
public System.Threading.Tasks.Task<Microsoft.AspNetCore.Mvc.ActionResult> UpdateRoleGroupUserAsync(AlifeApi.BusinessRules.RoleGroupModels.RoleGroupUserUpdateInputPg input);
```
#### Parameters

<a name='AlifeApi.WebApi.Controllers.RoleGroupControllerPg.UpdateRoleGroupUserAsync(AlifeApi.BusinessRules.RoleGroupModels.RoleGroupUserUpdateInputPg).input'></a>

`input` [AlifeApi.BusinessRules.RoleGroupModels.RoleGroupUserUpdateInputPg](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.BusinessRules.RoleGroupModels.RoleGroupUserUpdateInputPg 'AlifeApi.BusinessRules.RoleGroupModels.RoleGroupUserUpdateInputPg')

角色權限使用者更新輸入資料

#### Returns
[System.Threading.Tasks.Task&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')[Microsoft.AspNetCore.Mvc.ActionResult](https://docs.microsoft.com/en-us/dotnet/api/Microsoft.AspNetCore.Mvc.ActionResult 'Microsoft.AspNetCore.Mvc.ActionResult')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')  
執行結果