#### [AlifeApi.WebApi](index.md 'index')
### [AlifeApi.WebApi.Schedule](AlifeApi.WebApi.Schedule.md 'AlifeApi.WebApi.Schedule')

## QuartzJobRunner Class

```csharp
public class QuartzJobRunner
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; QuartzJobRunner
### Fields

<a name='AlifeApi.WebApi.Schedule.QuartzJobRunner._configuration'></a>

## QuartzJobRunner._configuration Field

系統設定

```csharp
private IConfiguration _configuration;
```

#### Field Value
[Microsoft.Extensions.Configuration.IConfiguration](https://docs.microsoft.com/en-us/dotnet/api/Microsoft.Extensions.Configuration.IConfiguration 'Microsoft.Extensions.Configuration.IConfiguration')