﻿using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Configuration;
using System;
using System.IO;
using System.Threading.Tasks;

namespace AlifeApi.BusinessRules.Infrastructure
{
    /// <summary>
    /// 圖片儲存服務實作
    /// </summary>
    public class ImageStorageService : IImageStorageService
    {
        private readonly string _storagePath; // 移除預設值

        /// <summary>
        /// 建構函數
        /// </summary>
        public ImageStorageService(IConfiguration configuration) // 注入 IConfiguration
        {
            // 從設定檔讀取儲存路徑，如果未設定則使用預設值
            _storagePath = configuration["ImageStorageSettings:LocalPath"] ?? "C:\\alifeImg";

            // 確保儲存路徑存在
            if (!Directory.Exists(_storagePath))
            {
                Directory.CreateDirectory(_storagePath);
            }
        }

        /// <summary>
        /// 儲存 Base64 編碼的圖片
        /// </summary>
        public async Task<string> SaveImageAsync(string imageBase64, string customerId)
        {
            if (string.IsNullOrEmpty(imageBase64))
            {
                throw new ArgumentException("Base64 圖片字串無效", nameof(imageBase64));
            }

            // 移除 Base64 前綴 (如果有的話)
            if (imageBase64.Contains("base64,"))
            {
                imageBase64 = imageBase64.Substring(imageBase64.IndexOf("base64,") + 7);
            }

            byte[] imageBytes;
            try
            {
                imageBytes = Convert.FromBase64String(imageBase64);
            }
            catch (FormatException ex)
            {
                throw new ArgumentException("Base64 圖片字串格式錯誤", nameof(imageBase64), ex);
            }

            // 生成檔名
            string utcNowString = DateTime.Now.ToString("yyyyMMddHHmmss");
            string fileName = $"{customerId}_{utcNowString}.jpg"; // 假設圖片格式為 jpg
            string filePath = Path.Combine(_storagePath, fileName);

            try
            {
                // 儲存圖片
                await File.WriteAllBytesAsync(filePath, imageBytes);
                return filePath; // 改為返回完整路徑
            }
            catch (Exception ex)
            {
                throw new Exception($"儲存圖片到 {filePath} 時發生錯誤: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 刪除圖片
        /// </summary>
        public async Task DeleteImageAsync(string imagePath)
        {
            if (string.IsNullOrEmpty(imagePath))
            {
                return;
            }

            var filePath = Path.Combine(_storagePath, imagePath);

            try
            {
                // 刪除檔案
                if (File.Exists(filePath))
                {
                    File.Delete(filePath);
                }

                await Task.CompletedTask; // 為了符合非同步方法的要求
            }
            catch (Exception ex)
            {
                // Log the exception or handle it as needed
                Console.WriteLine($"刪除圖片 {filePath} 時發生錯誤: {ex.Message}");
                // 根據您的錯誤處理策略，決定是否重新拋出異常
                // throw new Exception($"刪除圖片時發生錯誤: {ex.Message}", ex);
            }
        }
    }
} 
