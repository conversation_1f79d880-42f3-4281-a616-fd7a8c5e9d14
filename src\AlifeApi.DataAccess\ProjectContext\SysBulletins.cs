﻿using System;
using System.Collections.Generic;

namespace AlifeApi.DataAccess.ProjectContext
{
    public partial class SysBulletins
    {
        public SysBulletins()
        {
            SysBulletinsAttach = new HashSet<SysBulletinsAttach>();
            SysBulletinsClickRecord = new HashSet<SysBulletinsClickRecord>();
        }

        /// <summary>
        /// 公告流水號
        /// </summary>
        public int BlId { get; set; }

        /// <summary>
        /// 公告標題
        /// </summary>
        public string BlTitle { get; set; }

        /// <summary>
        /// 公告內容
        /// </summary>
        public string BlContent { get; set; }

        /// <summary>
        /// 啟用日期
        /// </summary>
        public DateTime? BlPostDateFrom { get; set; }

        /// <summary>
        /// 結束日期
        /// </summary>
        public DateTime? BlPostDateToxx { get; set; }

        /// <summary>
        /// 是否置頂
        /// </summary>
        public bool BlIsTop { get; set; }

        /// <summary>
        /// 是否上架
        /// </summary>
        public bool BlIsEnable { get; set; }

        /// <summary>
        /// 建立者
        /// </summary>
        public string BlCrUser { get; set; }

        /// <summary>
        /// 建立時間
        /// </summary>
        public DateTime? BlCrDatetime { get; set; }

        /// <summary>
        /// 更新者
        /// </summary>
        public string BlUpUser { get; set; }

        /// <summary>
        /// 更新時間
        /// </summary>
        public DateTime? BlUpDatetime { get; set; }

        public virtual ICollection<SysBulletinsAttach> SysBulletinsAttach { get; set; }

        public virtual ICollection<SysBulletinsClickRecord> SysBulletinsClickRecord { get; set; }

    }
}
