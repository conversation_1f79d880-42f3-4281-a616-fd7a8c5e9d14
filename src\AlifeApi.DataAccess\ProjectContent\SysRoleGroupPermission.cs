﻿using System;
using System.Collections.Generic;

namespace AlifeApi.DataAccess.ProjectContent
{
    /// <summary>
    /// 角色群組權限表，用於儲存角色群組對系統功能的權限設置。
    /// </summary>
    public partial class SysRoleGroupPermission
    {
        /// <summary>
        /// 系統名稱，主鍵的一部分，用於區分不同系統。
        /// </summary>
        public string System { get; set; }
        /// <summary>
        /// 角色群組識別編號，主鍵的一部分，對應 SYS_RoleGroup 表的 RoleGroupId。
        /// </summary>
        public string RoleGroupId { get; set; }
        /// <summary>
        /// 功能項目識別編號，主鍵的一部分，對應 SYS_MenuFunc 表的 FuncId。
        /// </summary>
        public string FuncId { get; set; }
        /// <summary>
        /// CRUD 權限，儲存對功能的創建(C)、讀取(R)、更新(U)、刪除(D)權限設定。
        /// </summary>
        public string Crud { get; set; }
        /// <summary>
        /// 是否啟用，true 表示啟用，false 表示停用，預設為 false。
        /// </summary>
        public bool IsActive { get; set; }
    }
}
