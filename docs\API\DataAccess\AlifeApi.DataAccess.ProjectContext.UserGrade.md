#### [AlifeApi.DataAccess](index.md 'index')
### [AlifeApi.DataAccess.ProjectContext](AlifeApi.DataAccess.ProjectContext.md 'AlifeApi.DataAccess.ProjectContext')

## UserGrade Class

```csharp
public class UserGrade
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; UserGrade
### Properties

<a name='AlifeApi.DataAccess.ProjectContext.UserGrade.CreatedTime'></a>

## UserGrade.CreatedTime Property

建立時間

```csharp
public System.DateTime CreatedTime { get; set; }
```

#### Property Value
[System.DateTime](https://docs.microsoft.com/en-us/dotnet/api/System.DateTime 'System.DateTime')

<a name='AlifeApi.DataAccess.ProjectContext.UserGrade.CreatedUserId'></a>

## UserGrade.CreatedUserId Property

建立者

```csharp
public string CreatedUserId { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContext.UserGrade.GradeName'></a>

## UserGrade.GradeName Property

職稱

```csharp
public string GradeName { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContext.UserGrade.Id'></a>

## UserGrade.Id Property

職稱代碼

```csharp
public string Id { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContext.UserGrade.IsDisabled'></a>

## UserGrade.IsDisabled Property

是否刪除

```csharp
public bool IsDisabled { get; set; }
```

#### Property Value
[System.Boolean](https://docs.microsoft.com/en-us/dotnet/api/System.Boolean 'System.Boolean')

<a name='AlifeApi.DataAccess.ProjectContext.UserGrade.ParentGradeId'></a>

## UserGrade.ParentGradeId Property

上層職稱代碼

```csharp
public string ParentGradeId { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContext.UserGrade.UpdatedTime'></a>

## UserGrade.UpdatedTime Property

更新時間

```csharp
public System.DateTime UpdatedTime { get; set; }
```

#### Property Value
[System.DateTime](https://docs.microsoft.com/en-us/dotnet/api/System.DateTime 'System.DateTime')

<a name='AlifeApi.DataAccess.ProjectContext.UserGrade.UpdatedUserId'></a>

## UserGrade.UpdatedUserId Property

更新者

```csharp
public string UpdatedUserId { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')