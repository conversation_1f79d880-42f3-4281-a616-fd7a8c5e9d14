#### [AlifeApi.BusinessRules](index.md 'index')
### [AlifeApi.BusinessRules.ProjectVersionModels](AlifeApi.BusinessRules.ProjectVersionModels.md 'AlifeApi.BusinessRules.ProjectVersionModels')

## ProjectVersionOutput Class

```csharp
public class ProjectVersionOutput : AlifeApi.BusinessRules.ProjectVersionModels.ProjectVersionBase,
AlifeApi.BusinessRules.Infrastructure.IApiMessage
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; [ProjectVersionBase](AlifeApi.BusinessRules.ProjectVersionModels.ProjectVersionBase.md 'AlifeApi.BusinessRules.ProjectVersionModels.ProjectVersionBase') &#129106; ProjectVersionOutput

Implements [IApiMessage](AlifeApi.BusinessRules.Infrastructure.IApiMessage.md 'AlifeApi.BusinessRules.Infrastructure.IApiMessage')
### Properties

<a name='AlifeApi.BusinessRules.ProjectVersionModels.ProjectVersionOutput.LastUpdateTime'></a>

## ProjectVersionOutput.LastUpdateTime Property

版本發布時間(DateTime)

```csharp
public System.Nullable<System.DateTime> LastUpdateTime { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.DateTime](https://docs.microsoft.com/en-us/dotnet/api/System.DateTime 'System.DateTime')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

<a name='AlifeApi.BusinessRules.ProjectVersionModels.ProjectVersionOutput.Response'></a>

## ProjectVersionOutput.Response Property

Gets the response.

```csharp
public System.Enum Response { get; set; }
```

Implements [Response](AlifeApi.BusinessRules.Infrastructure.IApiMessage.md#AlifeApi.BusinessRules.Infrastructure.IApiMessage.Response 'AlifeApi.BusinessRules.Infrastructure.IApiMessage.Response')

#### Property Value
[System.Enum](https://docs.microsoft.com/en-us/dotnet/api/System.Enum 'System.Enum')

<a name='AlifeApi.BusinessRules.ProjectVersionModels.ProjectVersionOutput.Version'></a>

## ProjectVersionOutput.Version Property

系統專案版號

```csharp
public string Version { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')