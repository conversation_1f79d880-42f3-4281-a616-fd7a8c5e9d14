#### [AlifeApi.BusinessRules](index.md 'index')
### [AlifeApi.BusinessRules.CommonModels](AlifeApi.BusinessRules.CommonModels.md 'AlifeApi.BusinessRules.CommonModels')

## UnifiedSalesStatistics Class

統一銷售統計

```csharp
public class UnifiedSalesStatistics
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; UnifiedSalesStatistics
### Properties

<a name='AlifeApi.BusinessRules.CommonModels.UnifiedSalesStatistics.GeneratedTime'></a>

## UnifiedSalesStatistics.GeneratedTime Property

統計產生時間

```csharp
public System.DateTime GeneratedTime { get; set; }
```

#### Property Value
[System.DateTime](https://docs.microsoft.com/en-us/dotnet/api/System.DateTime 'System.DateTime')

<a name='AlifeApi.BusinessRules.CommonModels.UnifiedSalesStatistics.ParkingSpaceStatistics'></a>

## UnifiedSalesStatistics.ParkingSpaceStatistics Property

車位統計

```csharp
public global::ParkingSpaceSalesStatistics ParkingSpaceStatistics { get; set; }
```

#### Property Value
[ParkingSpaceSalesStatistics](https://docs.microsoft.com/en-us/dotnet/api/ParkingSpaceSalesStatistics 'ParkingSpaceSalesStatistics')

<a name='AlifeApi.BusinessRules.CommonModels.UnifiedSalesStatistics.SiteCode'></a>

## UnifiedSalesStatistics.SiteCode Property

案場編號

```csharp
public string SiteCode { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.CommonModels.UnifiedSalesStatistics.TotalAvailableCount'></a>

## UnifiedSalesStatistics.TotalAvailableCount Property

總可售數量

```csharp
public int TotalAvailableCount { get; set; }
```

#### Property Value
[System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')

<a name='AlifeApi.BusinessRules.CommonModels.UnifiedSalesStatistics.TotalReservedCount'></a>

## UnifiedSalesStatistics.TotalReservedCount Property

總保留數量

```csharp
public int TotalReservedCount { get; set; }
```

#### Property Value
[System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')

<a name='AlifeApi.BusinessRules.CommonModels.UnifiedSalesStatistics.TotalSoldCount'></a>

## UnifiedSalesStatistics.TotalSoldCount Property

總已售數量

```csharp
public int TotalSoldCount { get; set; }
```

#### Property Value
[System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')

<a name='AlifeApi.BusinessRules.CommonModels.UnifiedSalesStatistics.UnitStatistics'></a>

## UnifiedSalesStatistics.UnitStatistics Property

房屋統計

```csharp
public global::UnitSalesStatistics UnitStatistics { get; set; }
```

#### Property Value
[UnitSalesStatistics](https://docs.microsoft.com/en-us/dotnet/api/UnitSalesStatistics 'UnitSalesStatistics')