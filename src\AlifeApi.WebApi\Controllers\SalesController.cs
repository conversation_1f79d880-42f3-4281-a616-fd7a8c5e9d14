using System;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using AlifeApi.BusinessRules.SalesModels;
using AlifeApi.BusinessRules.Infrastructure;
using AlifeApi.BusinessRules.PurchaseOrderModels;
using AlifeApi.BusinessRules.CommonModels;

namespace AlifeApi.WebApi.Controllers
{
    /// <summary>
    /// 銷控表管理
    /// </summary>
    public class SalesController : AuthenticatedController
    {
        private readonly SalesService _salesService;
        private readonly PurchaseOrderService _purchaseOrderService;
        private readonly ICurrentUser _currentUser;

        public SalesController(SalesService salesService, PurchaseOrderService purchaseOrderService, ICurrentUser currentUser)
        {
            _salesService = salesService ?? throw new ArgumentNullException(nameof(salesService));
            _purchaseOrderService = purchaseOrderService ?? throw new ArgumentNullException(nameof(purchaseOrderService));
            _currentUser = currentUser ?? throw new ArgumentNullException(nameof(currentUser));
        }

        /// <summary>
        /// 取得銷控表資料
        /// </summary>
        /// <param name="input">銷控表查詢條件</param>
        /// <returns>銷控表格式資料</returns>
        [HttpPost("sales-control")]
        public async Task<ActionResult<BuildingSalesData>> GetSalesControl([FromBody] SalesControlInput input)
        {
            try
            {
                var result = await _salesService.GetSalesControlAsync(input);
                return Ok(result);
            }
            catch (Exception ex)
            {
                return BadRequest(new { message = ex.Message });
            }
        }

        /// <summary>
        /// 取得案場銷售統計摘要
        /// </summary>
        /// <param name="siteCode">案場編號</param>
        /// <returns>銷售統計資料</returns>
        [HttpGet("statistics/{siteCode}")]
        public async Task<ActionResult<ComprehensiveSalesStatistics>> GetSalesStatistics(string siteCode)
        {
            try
            {
                var result = await _salesService.GetSalesStatisticsAsync(siteCode);
                return Ok(result);
            }
            catch (Exception ex)
            {
                return BadRequest(new { message = ex.Message });
            }
        }

        /// <summary>
        /// 取得銷控表列表格式資料（只返回有訂單的資料）
        /// </summary>
        /// <param name="input">銷控表列表查詢條件</param>
        /// <returns>銷控表列表格式資料</returns>
        [HttpPost("sales-control-list")]
        public async Task<ActionResult<SalesControlListOutput>> GetSalesControlList([FromBody] SalesControlListInput input)
        {
            try
            {
                var result = await _salesService.GetSalesControlListAsync(input);
                return Ok(result);
            }
            catch (Exception ex)
            {
                return BadRequest(new { message = ex.Message });
            }
        }

        /// <summary>
        /// 銷控表單位狀態完整更新 (同時處理訂單和Units狀態)
        /// </summary>
        /// <param name="unitId">房屋單位ID</param>
        /// <param name="input">銷控表單位更新輸入</param>
        /// <returns>NoContent</returns>
        [HttpPut("sales-control-unit/{unitId}")]
        public async Task<ActionResult> UpdateSalesControlUnit(int unitId, [FromBody] SalesControlUnitUpdateInput input)
        {
            try
            {
                await _salesService.UpdateSalesControlUnitAsync(unitId, input, _currentUser.UserId);
                return NoContent();
            }
            catch (Exception ex)
            {
                return BadRequest(new { message = ex.Message });
            }
        }

        /// <summary>
        /// 更新房屋單位銷售狀態 (舊版API，建議使用 sales-control-unit)
        /// </summary>
        /// <param name="unitId">房屋單位ID</param>
        /// <param name="input">狀態更新輸入</param>
        /// <returns>NoContent</returns>
        [HttpPut("unit-status/{unitId}")]
        public async Task<ActionResult> UpdateUnitSalesStatus(int unitId, [FromBody] UnitStatusUpdateInput input)
        {
            try
            {
                await _salesService.UpdateUnitSalesStatusAsync(unitId, input.Status, _currentUser.UserId);
                return NoContent();
            }
            catch (Exception ex)
            {
                return BadRequest(new { message = ex.Message });
            }
        }
    }

    /// <summary>
    /// 房屋單位狀態更新輸入
    /// </summary>
    public class UnitStatusUpdateInput
    {
        /// <summary>
        /// 新的銷售狀態
        /// </summary>
        public string Status { get; set; } = null!;
    }
}
