#### [AlifeApi.BusinessRules](index.md 'index')
### [AlifeApi.BusinessRules.DailyReportModels](AlifeApi.BusinessRules.DailyReportModels.md 'AlifeApi.BusinessRules.DailyReportModels')

## DailyReportListOutput Class

日報期間統計列表輸出DTO

```csharp
public class DailyReportListOutput
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; DailyReportListOutput
### Properties

<a name='AlifeApi.BusinessRules.DailyReportModels.DailyReportListOutput.PeriodSummary'></a>

## DailyReportListOutput.PeriodSummary Property

期間總計統計

```csharp
public AlifeApi.BusinessRules.DailyReportModels.DailyReportPeriodSummary PeriodSummary { get; set; }
```

#### Property Value
[DailyReportPeriodSummary](AlifeApi.BusinessRules.DailyReportModels.DailyReportPeriodSummary.md 'AlifeApi.BusinessRules.DailyReportModels.DailyReportPeriodSummary')

<a name='AlifeApi.BusinessRules.DailyReportModels.DailyReportListOutput.Reports'></a>

## DailyReportListOutput.Reports Property

日報統計列表

```csharp
public System.Collections.Generic.List<AlifeApi.BusinessRules.DailyReportModels.DailyReportSummary> Reports { get; set; }
```

#### Property Value
[System.Collections.Generic.List&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Collections.Generic.List-1 'System.Collections.Generic.List`1')[DailyReportSummary](AlifeApi.BusinessRules.DailyReportModels.DailyReportSummary.md 'AlifeApi.BusinessRules.DailyReportModels.DailyReportSummary')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Collections.Generic.List-1 'System.Collections.Generic.List`1')

<a name='AlifeApi.BusinessRules.DailyReportModels.DailyReportListOutput.TotalCount'></a>

## DailyReportListOutput.TotalCount Property

總筆數

```csharp
public int TotalCount { get; set; }
```

#### Property Value
[System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')