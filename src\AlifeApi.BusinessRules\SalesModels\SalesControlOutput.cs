using System.Collections.Generic;
using System.Text.Json.Serialization;

namespace AlifeApi.BusinessRules.SalesModels
{
    /// <summary>
    /// 房屋單位銷售狀態
    /// 業務流程：可售 -> 售(訂金未付足) -> 足(補足訂金) -> 簽(已簽合約)
    /// 請款和領款不再是狀態，而是透過 RequestDate 和 ReceiveDate 欄位記錄
    /// </summary>
    public static class UnitSalesStatus
    {
        public const string Available = "可售"; // 可售
        public const string Reserved = "保留";  // 保留 (包含地主保留、業主保留)
        public const string Sold = "售";        // 已售(訂金未付足)
        public const string DepositFull = "足"; // 補足訂金
        public const string Contracted = "簽";  // 已簽合約或者已付完簽約金
        
        // 以下常數保留用於舊資料相容性，但不再作為新的狀態使用
        [Obsolete("請款不再是狀態，請使用 RequestDate 欄位記錄請款日期")]
        public const string Requested = "請";   // 向業主請費用 (已廢棄)
        
        [Obsolete("領款不再是狀態，請使用 ReceiveDate 欄位記錄領款日期")]
        public const string Received = "領";    // 已向業主領到費用 (已廢棄)
        
        [Obsolete("樣品屋狀態已廢棄，請使用保留狀態")]
        public const string Model = "樣";       // 樣品屋 (已廢棄)
    }

    /// <summary>
    /// 房屋單位資訊
    /// </summary>
    public class UnitInfo
    {
        /// <summary>
        /// 房屋單位ID
        /// </summary>
        public int UnitId { get; set; }

        /// <summary>
        /// 戶號 (例如: "A", "B", "C")
        /// </summary>
        public string UnitNumber { get; set; } = null!;

        /// <summary>
        /// 銷售狀態
        /// </summary>
        public string Status { get; set; } = null!;

        /// <summary>
        /// 客戶姓名
        /// </summary>
        [JsonIgnore(Condition = JsonIgnoreCondition.Never)]
        public string? CustomerName { get; set; }

        /// <summary>
        /// 售出日期 (實際銷售日期)
        /// </summary>
        [JsonIgnore(Condition = JsonIgnoreCondition.Never)]
        public DateOnly? SaleDate { get; set; }

        /// <summary>
        /// 請款日期 (向業主請費用的日期)
        /// </summary>
        [JsonIgnore(Condition = JsonIgnoreCondition.Never)]
        public DateOnly? RequestDate { get; set; }

        /// <summary>
        /// 領款日期 (已向業主領到費用的日期)
        /// </summary>
        [JsonIgnore(Condition = JsonIgnoreCondition.Never)]
        public DateOnly? ReceiveDate { get; set; }

        /// <summary>
        /// 實價登錄提交日期
        /// </summary>
        [JsonIgnore(Condition = JsonIgnoreCondition.Never)]
        public DateOnly? PriceRegistrationSubmissionDate { get; set; }

        /// <summary>
        /// 購買的車位列表（車位編號，以逗號分隔）
        /// </summary>
        public string? PurchasedParkingSpaces { get; set; }

        /// <summary>
        /// 成交價格
        /// </summary>
        public decimal? SalePrice { get; set; }

        /// <summary>
        /// 坪數
        /// </summary>
        public decimal? Area { get; set; }

        /// <summary>
        /// 房型 (格局)
        /// </summary>
        public string? UnitType { get; set; }

        /// <summary>
        /// 用於摘要行顯示的數值
        /// </summary>
        public object? DisplayValue { get; set; }

        /// <summary>
        /// 樓層名稱
        /// </summary>
        public string? FloorLabel { get; set; }

        /// <summary>
        /// 建築物名稱
        /// </summary>
        public string? BuildingName { get; set; }
    }

    /// <summary>
    /// 樓層資訊
    /// </summary>
    public class FloorInfo
    {
        /// <summary>
        /// 樓層編號 (例如: "15F", "14F", "店面")
        /// </summary>
        public string FloorNumber { get; set; } = null!;

        /// <summary>
        /// 樓層ID
        /// </summary>
        public int FloorId { get; set; }

        /// <summary>
        /// 樓層數值 (用於排序)
        /// </summary>
        public int FloorLevel { get; set; }

        /// <summary>
        /// 該樓層的房屋單位列表
        /// </summary>
        public List<UnitInfo> Units { get; set; } = new List<UnitInfo>();

        /// <summary>
        /// 樓層統計摘要
        /// </summary>
        public FloorSummary Summary { get; set; } = new FloorSummary();
    }


} 
 