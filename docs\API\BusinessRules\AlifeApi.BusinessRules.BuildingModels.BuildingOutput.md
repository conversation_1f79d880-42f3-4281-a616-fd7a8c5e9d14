#### [AlifeApi.BusinessRules](index.md 'index')
### [AlifeApi.BusinessRules.BuildingModels](AlifeApi.BusinessRules.BuildingModels.md 'AlifeApi.BusinessRules.BuildingModels')

## BuildingOutput Class

建築物詳細輸出模型

```csharp
public class BuildingOutput
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; BuildingOutput
### Properties

<a name='AlifeApi.BusinessRules.BuildingModels.BuildingOutput.BuildingId'></a>

## BuildingOutput.BuildingId Property

建築物唯一識別碼

```csharp
public int BuildingId { get; set; }
```

#### Property Value
[System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')

<a name='AlifeApi.BusinessRules.BuildingModels.BuildingOutput.BuildingName'></a>

## BuildingOutput.BuildingName Property

建築物名稱

```csharp
public string BuildingName { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.BuildingModels.BuildingOutput.BuildingType'></a>

## BuildingOutput.BuildingType Property

建築物類型

```csharp
public string? BuildingType { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.BuildingModels.BuildingOutput.CompletionDate'></a>

## BuildingOutput.CompletionDate Property

完工日期

```csharp
public System.Nullable<System.DateOnly> CompletionDate { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.DateOnly](https://docs.microsoft.com/en-us/dotnet/api/System.DateOnly 'System.DateOnly')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

<a name='AlifeApi.BusinessRules.BuildingModels.BuildingOutput.CreatedTime'></a>

## BuildingOutput.CreatedTime Property

建立時間

```csharp
public System.DateTime CreatedTime { get; set; }
```

#### Property Value
[System.DateTime](https://docs.microsoft.com/en-us/dotnet/api/System.DateTime 'System.DateTime')

<a name='AlifeApi.BusinessRules.BuildingModels.BuildingOutput.CreatedUserInfoId'></a>

## BuildingOutput.CreatedUserInfoId Property

建立者 UserInfoId

```csharp
public string CreatedUserInfoId { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.BuildingModels.BuildingOutput.CreatedUserName'></a>

## BuildingOutput.CreatedUserName Property

建立者名稱 (選填)

```csharp
public string? CreatedUserName { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.BuildingModels.BuildingOutput.Remarks'></a>

## BuildingOutput.Remarks Property

備註

```csharp
public string? Remarks { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.BuildingModels.BuildingOutput.SiteCode'></a>

## BuildingOutput.SiteCode Property

案場代碼

```csharp
public string SiteCode { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.BuildingModels.BuildingOutput.TotalAboveGroundFloors'></a>

## BuildingOutput.TotalAboveGroundFloors Property

地上樓層總數

```csharp
public System.Nullable<int> TotalAboveGroundFloors { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

<a name='AlifeApi.BusinessRules.BuildingModels.BuildingOutput.TotalBelowGroundFloors'></a>

## BuildingOutput.TotalBelowGroundFloors Property

地下樓層總數

```csharp
public System.Nullable<int> TotalBelowGroundFloors { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

<a name='AlifeApi.BusinessRules.BuildingModels.BuildingOutput.UpdatedTime'></a>

## BuildingOutput.UpdatedTime Property

更新時間

```csharp
public System.DateTime UpdatedTime { get; set; }
```

#### Property Value
[System.DateTime](https://docs.microsoft.com/en-us/dotnet/api/System.DateTime 'System.DateTime')

<a name='AlifeApi.BusinessRules.BuildingModels.BuildingOutput.UpdatedUserInfoId'></a>

## BuildingOutput.UpdatedUserInfoId Property

更新者 UserInfoId

```csharp
public string UpdatedUserInfoId { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.BuildingModels.BuildingOutput.UpdatedUserName'></a>

## BuildingOutput.UpdatedUserName Property

更新者名稱 (選填)

```csharp
public string? UpdatedUserName { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')