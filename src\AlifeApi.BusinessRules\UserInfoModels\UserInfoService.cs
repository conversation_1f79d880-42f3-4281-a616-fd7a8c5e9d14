﻿using AlifeApi.BusinessRules.Infrastructure;
using AlifeApi.BusinessRules.RoleGroupModels;
using AlifeApi.Common.DataAnnotations;
using AlifeApi.Common.Util;
using AlifeApi.DataAccess.ProjectContext;
using Microsoft.EntityFrameworkCore;

//namespace AlifeApi.BusinessRules.UserInfoModels
//{
//    /// <summary>
//    /// 使用者管理
//    /// </summary>
//    public class UserInfoService : ServiceBase<ProjectContext>
//    {
//        private readonly PasswordManager _passwordManager;

//        public UserInfoService(IServiceProvider serviceProvider, ProjectContext dbContext, PasswordManager passwordManager)
//            : base(serviceProvider, dbContext)
//        {
//            _passwordManager = passwordManager ?? throw new ArgumentNullException(nameof(passwordManager));
//        }


//        /// <summary>
//        /// 帳號權限管理-帳號新增
//        /// </summary>
//        /// <param name="input">The input.</param>
//        /// <returns>執行結果</returns>
//        /// <exception cref="ArgumentNullException">input</exception>
//        public async Task<UserInfoUpdateOutput> CreatUserInfoAsync(UserInfoCreateInput input)
//        {
//            ArgumentNullException.ThrowIfNull(input, nameof(input));

//            UserInfoUpdateOutput output = new();
//            bool isDefaultPasswordUsed = false;

//            if (string.IsNullOrEmpty(input.UserPw))
//            {
//                input.UserPw = (await Db.SysSystemSetting.SingleAsync(x => x.Type == "SecuritySetting" && x.Key == "DefaultPassword"))
//                    .Value;
//                isDefaultPasswordUsed = true;
//            }

//            if (await Db.UserInfo.AnyAsync(x => x.Id == input.UserId))
//            {
//                output.Response = Message.AccountAlreadyExist;
//                return output;
//            }

//            if (await Db.UserDept.AnyAsync(x => x.Id == input.DeptId && x.IsDisabled))
//            {
//                throw new KyException("該部門不存在或已刪除");
//            }

//            if (await Db.UserGrade.AnyAsync(x => x.Id == input.GradeCode && x.IsDisabled))
//            {
//                throw new KyException("該職稱不存在或已刪除");
//            }


//            UserInfo entity = new()
//            {
//                Id = input.UserId,
//                Pw = _passwordManager.HashPassword(input.UserPw),
//                DeptId = input.DeptId,
//                GradeCode = input.GradeCode,
//                Name = input.UserName ?? "",
//                IdNo = input.UserIdNo ?? "",
//                Email = input.UserEmail ?? "",
//                IsDisabled = false,
//                LastLoginIp = "",
//                LoginFailedCount = 0,
//                IsEnabled = true, // 感覺原本應該是提供審核帳號時用的，但現在只是用來存停用、啟用而已
//                EnabledTtime = DateTime.Now,
//                CreatedTime = DateTime.Now,
//                CreatedUserId = CurrentUser?.UserId ?? input.UserId,
//                UpdatedTime = DateTime.Now,
//                UpdatedUserId = CurrentUser?.UserId ?? input.UserId,
//            };

//            Db.UserInfo.Add(entity);

//            foreach (string roleId in input.Roles ?? Enumerable.Empty<string>())
//            {
//                // 檢核 RoleGroupId 是否存在
//                SysRoleGroup role = await Db.SysRoleGroup
//                    .SingleAsync(x => x.System == CurrentUser.System && x.RoleGroupId == roleId);

//                Db.SysRoleGroupUser.Add(new SysRoleGroupUser
//                {
//                    System = CurrentUser.System,
//                    RoleGroupId = role.RoleGroupId,
//                    UserId = input.UserId
//                });
//            }

//            if (!isDefaultPasswordUsed)
//            {
//                await _passwordManager.CreatePasswordHistoryAsync(input.UserId, input.UserPw);
//            }

//            await Db.SaveChangesAsync();

//            output.Response = Message.Success;
//            return output;
//        }

//        /// <summary>
//        /// 修改使用者資料
//        /// </summary>
//        /// <param name="input">The input.</param>
//        /// <returns>執行結果</returns>
//        /// <exception cref="ArgumentNullException">input</exception>
//        public async Task<UserInfoUpdateOutput> UpdateUserInfoAsync(UserInfoUpdateInput input)
//        {
//            ArgumentNullException.ThrowIfNull(input, nameof(input));

//            UserInfoUpdateOutput output = new();

//            UserInfo entity = await Db.UserInfo.SingleOrDefaultAsync(x => x.Id == input.UserId);
//            if (entity is null)
//            {
//                output.Response = Message.AccountFail;
//                return output;
//            }

//            if (input.UserPw is not null)
//            {
//                if (!await _passwordManager.IsPasswordUsedBeforeAsync(input.UserId, input.UserPw))
//                {
//                    output.Response = Message.PasswordUsedRecently;
//                    return output;
//                }
//                entity.Pw = _passwordManager.HashPassword(input.UserPw);
//            }

//            if (await Db.UserDept.AnyAsync(x => x.Id == input.DeptId && x.IsDisabled))
//            {
//                throw new KyException("該部門不存在或已刪除");
//            }

//            if (await Db.UserGrade.AnyAsync(x => x.Id == input.GradeCode && x.IsDisabled))
//            {
//                throw new KyException("該職稱不存在或已刪除");
//            }

//            ValueMapper<UserInfoUpdateInput, UserInfo> mapper = new(input, entity);
//            mapper.MapIfHasValue(x => x.UserName, x => x.Name);
//            mapper.MapIfHasValue(x => x.UserIdNo, x => x.IdNo);
//            mapper.MapIfHasValue(x => x.DeptId, x => x.DeptId);
//            mapper.MapIfHasValue(x => x.GradeCode, x => x.GradeCode);
//            mapper.MapIfHasValue(x => x.IsEnabled, x => x.IsEnabled);
//            mapper.MapIfHasValue(
//                x => x.IsEnabled, x => x.EnabledTtime,
//                x => input.IsEnabled.Value ? DateTime.Now : entity.EnabledTtime
//            );

//            // 正常這個參數應該是要用解鎖的單字，不過前面這樣設計就算了
//            if (!input.IsLocked)
//            {
//                entity.LoginFailedCount = 0;
//            }
//            entity.UpdatedUserId = CurrentUser?.UserId ?? input.UserId;
//            entity.UpdatedTime = DateTime.Now;

//            if (input.Roles is not null)
//            {
//                List<SysRoleGroupUser> roleUsers = await Db.SysRoleGroupUser
//                    .Where(x => x.UserId == input.UserId)
//                    .ToListAsync();

//                Db.SysRoleGroupUser.RemoveRange(
//                    roleUsers.Where(x => x.System == CurrentUser.System && !input.Roles.Any(y => y == x.RoleGroupId))
//                );

//                foreach (string roleId in input.Roles.Distinct()
//                    .Where(x => !roleUsers.Any(y => y.System == CurrentUser.System && y.RoleGroupId == x)))
//                {
//                    // 檢核 RoleGroupId 是否存在
//                    SysRoleGroup role = await Db.SysRoleGroup
//                        .SingleAsync(x => x.System == CurrentUser.System && x.RoleGroupId == roleId);

//                    Db.SysRoleGroupUser.Add(new SysRoleGroupUser
//                    {
//                        System = CurrentUser.System,
//                        RoleGroupId = role.RoleGroupId,
//                        UserId = input.UserId,
//                    });
//                }
//            }
//            await Db.SaveChangesAsync();

//            if (input.UserPw is not null)
//            {
//                await _passwordManager.CreatePasswordHistoryAsync(input.UserId, input.UserPw);
//            }

//            output.Response = Message.Success;
//            return output;
//        }

//        /// <summary>
//        /// 帳號權限管理-修改密碼
//        /// </summary>
//        /// <param name="input">The input.</param>
//        /// <returns>執行結果</returns>
//        /// <exception cref="ArgumentNullException">input</exception>
//        public async Task<UserInfoUpdateOutput> ChangePasswordAsync(PasswordChangeInput input)
//        {
//            ArgumentNullException.ThrowIfNull(input, nameof(input));

//            UserInfoUpdateOutput output = new();

//            UserInfo entity = await Db.UserInfo.SingleOrDefaultAsync(x => x.Id == input.UserId);

//            if (entity is null)
//            {
//                output.Response = Message.AccountFail;
//                return output;
//            }

//            if (await _passwordManager.IsPasswordUsedBeforeAsync(input.UserId, input.ChangePw))
//            {
//                output.Response = Message.PasswordUsedRecently;
//                return output;
//            }
//            entity.Pw = _passwordManager.HashPassword(input.ChangePw);

//            await Db.SaveChangesAsync();
//            await _passwordManager.CreatePasswordHistoryAsync(input.UserId, input.ChangePw);

//            output.Response = Message.Success;
//            return output;
//        }

//        /// <summary>
//        /// 帳號權限管理-人員列表
//        /// </summary>
//        /// <param name="input">The input.</param>
//        /// <returns>人員列表</returns>
//        /// <exception cref="ArgumentNullException">input</exception>
//        public async Task<PagedListOutput<UserInfoListItemGetOutput>> GetUserInfoListAsync(
//            UserInfoListGetInput input)
//        {
//            ArgumentNullException.ThrowIfNull(input, nameof(input));
//            var LoginFailedCount = Convert.ToInt32(Db.SysSystemSetting.AsNoTracking().FirstOrDefault(x => x.Type == "SecuritySetting" && x.Key == "LoginFailedCount")?.Value ?? int.MaxValue.ToString());

//            var output = await Db.UserInfo.AsNoTracking()
//                .Select(x => new UserInfoListItemGetOutput
//                {
//                    Id = x.Id,
//                    UserName = x.Name,
//                    UserPw = x.Pw,
//                    UserIdNo = x.IdNo,
//                    GradeCode = x.GradeCode,
//                    GradeName = x.GradeCodeNavigation.GradeName,
//                    DeptId = x.DeptId,
//                    DeptName = x.Dept.DeptName,
//                    Email = x.Email,
//                    IsEnabled = x.IsEnabled,
//                    IsAdmin = x.SysRoleGroupUser.Any(y => y.SysRoleGroup.IsAdmin),
//                    // 都二階層了，應該是不會用它排序 RoleOutput.Order 在 Output 內部處理
//                    Roles = x.SysRoleGroupUser.Select(y => new RoleOutput
//                    {
//                        RoleId = y.RoleGroupId,
//                        RoleName = y.SysRoleGroup.Name
//                    }),
//                    LastLoginIP = x.LastLoginIp,
//                    LastLoginTime = x.LastLoginTime,
//                    LoginFailedCount = x.LoginFailedCount,
//                    IsLock = x.LoginFailedCount > LoginFailedCount
//                })
//                .ToPagedListOutputAsync(input);

//            return output;
//        }


//        public async void ProcessIdleAccount()
//        {
//            //var ScheduleHistory = new SysScheduleHistory()
//            //{
//            //    SsName = "IdleAccount",
//            //    SsStartTime = DateTime.Now,
//            //};
//            //try
//            //{
//            //    var IdleSettings = Db.SysSystemSetting.AsNoTracking().Where(x => x.Type == "SecuritySetting").ToList();
//            //    var IdleNewAccountDays = Convert.ToInt32(IdleSettings.FirstOrDefault(x => x.Key == "IdleNewAccountDays")?.Value ?? TimeSpan.MaxValue.TotalDays.ToString());
//            //    var IdleAccountDays = Convert.ToInt32(IdleSettings.FirstOrDefault(x => x.Key == "IdleAccountDays")?.Value ?? TimeSpan.MaxValue.TotalDays.ToString());

//            //    var AllUsers = Db.UserInfo.AsNoTracking().Where(x => x.IsEnabled).ToList();

//            //    foreach (var user in AllUsers)
//            //    {
//            //        if (user.LastLoginTime == null)
//            //        {
//            //            if ((DateTime.Now - user.CreatedTime) > TimeSpan.FromDays(IdleNewAccountDays))
//            //            {
//            //                user.IsEnabled = false;
//            //                user.UpdatedUserId = "Administrator";
//            //                user.UpdatedTime = DateTime.Now;
//            //                Db.Entry(user).State = EntityState.Modified;
//            //            }
//            //        }
//            //        else
//            //        {
//            //            if ((DateTime.Now - user.LastLoginTime) > TimeSpan.FromDays(IdleNewAccountDays))
//            //            {
//            //                user.IsEnabled = false;
//            //                user.UpdatedUserId = "Administrator";
//            //                user.UpdatedTime = DateTime.Now;
//            //                Db.Entry(user).State = EntityState.Modified;
//            //            }
//            //        }
//            //    }
//            //}
//            //catch (Exception ex)
//            //{
//            //    ScheduleHistory.SsError = ex.Message;
//            //}
//            //ScheduleHistory.SsEndTime = DateTime.Now;
//            //Db.SysScheduleHistory.Add(ScheduleHistory);
//            //Db.SaveChanges();
//        }
//    }
//}
