﻿CREATE TABLE [dbo].[UserDeptPermission] (
    [System] VARCHAR (30) NOT NULL,
    [DeptId] VARCHAR (20) NOT NULL,
    [FuncId] VARCHAR (50) NOT NULL,
    CONSTRAINT [PK_UserDeptPermission] PRIMARY KEY CLUSTERED ([System] ASC, [DeptId] ASC, [FuncId] ASC),
    CONSTRAINT [FK_UserDeptPermission_SYS_MenuFunc] FOREIGN KEY ([System], [FuncId]) REFERENCES [dbo].[SYS_MenuFunc] ([System], [FuncId]),
    CONSTRAINT [FK_UserDeptPermission_UserDept] FOREIGN KEY ([DeptId]) REFERENCES [dbo].[UserDept] ([Id])
);


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'系統項目識別編號', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'UserDeptPermission', @level2type = N'COLUMN', @level2name = N'FuncId';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'部門識別編號', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'UserDeptPermission', @level2type = N'COLUMN', @level2name = N'DeptId';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'系統名稱', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'UserDeptPermission', @level2type = N'COLUMN', @level2name = N'System';

