using System;

namespace AlifeApi.BusinessRules.FloorModels
{
    /// <summary>
    /// 樓層列表輸出項目
    /// </summary>
    public class FloorListOutput
    {
        /// <summary>
        /// 樓層唯一識別碼
        /// </summary>
        public int FloorId { get; set; }

        /// <summary>
        /// 建築物ID
        /// </summary>
        public int BuildingId { get; set; }

        /// <summary>
        /// 案場代碼
        /// </summary>
        public string SiteCode { get; set; } = null!;

        /// <summary>
        /// 案場名稱
        /// </summary>
        public string? SiteName { get; set; }

        /// <summary>
        /// 樓層標示
        /// </summary>
        public string FloorLabel { get; set; } = null!;

        /// <summary>
        /// 樓層數值
        /// </summary>
        public int FloorLevel { get; set; }

        /// <summary>
        /// 樓層類型
        /// </summary>
        public string FloorType { get; set; } = null!;

        /// <summary>
        /// 樓層高度
        /// </summary>
        public decimal? FloorHeight { get; set; }

        /// <summary>
        /// 建立時間
        /// </summary>
        public DateTime CreatedTime { get; set; }
    }
} 
