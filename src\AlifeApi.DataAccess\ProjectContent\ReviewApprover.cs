﻿using System;
using System.Collections.Generic;

namespace AlifeApi.DataAccess.ProjectContent
{
    /// <summary>
    /// 審核人員表，用於儲存每個審核步驟的負責人員資訊。
    /// </summary>
    public partial class ReviewApprover
    {
        /// <summary>
        /// 審核人員流水號，主鍵，自動遞增，用於唯一識別審核人員記錄。
        /// </summary>
        public int ApproverId { get; set; }
        /// <summary>
        /// 步驟流水號，對應 ReviewSteps 表的 StepId。
        /// </summary>
        public int? StepId { get; set; }
        /// <summary>
        /// 使用者編號，對應 UserInfo 表的 UserInfoId。
        /// </summary>
        public string UserInfoId { get; set; }
        /// <summary>
        /// 是否為必要審核人員，true 表示必須審核，false 表示可選，預設為 true。
        /// </summary>
        public bool? IsRequired { get; set; }

        public virtual ReviewStep Step { get; set; }
    }
}
