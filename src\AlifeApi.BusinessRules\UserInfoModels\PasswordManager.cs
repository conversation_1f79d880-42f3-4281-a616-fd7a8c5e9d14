﻿using System.Security.Cryptography;
using System.Text;
using System.Text.RegularExpressions;
using AlifeApi.BusinessRules.Infrastructure;
using AlifeApi.DataAccess.ProjectContent;
using Microsoft.EntityFrameworkCore;

namespace AlifeApi.BusinessRules.UserInfoModels
{
    /// <summary>
    /// 密碼邏輯
    /// </summary>
    public class PasswordManager : ServiceBase<alifeContext>
    {
        private static readonly string _salt = "saltKey";

        public PasswordManager(IServiceProvider serviceProvider, alifeContext dbContext) : base(serviceProvider, dbContext)
        {
        }

        /// <summary>
        /// 取得預設密碼
        /// </summary>
        /// <returns>預設密碼</returns>
        public async Task<string> GetDefaultPasswordAsync()
        {
            return (await Db.SysSystemSettings.SingleAsync(x => x.Type == "SecuritySetting" && x.Key == "DefaultPassword"))
               .Value;
        }

        /// <summary>
        /// 增加使用者密碼使用紀錄
        /// </summary>
        /// <param name="userId">The user identifier.</param>
        /// <param name="password">The password.</param>
        public async Task CreatePasswordHistoryAsync(string userId, string password)
        {
            Db.UserPasswordHistories.Add(new UserPasswordHistory
            {
                UserInfoId = userId,
                Password = HashPassword(password),
                CreatedUserInfoId = CurrentUser?.UserId ?? userId,
                CreatedTime = DateTime.Now
            });

            await Db.SaveChangesAsync();
        }

        /// <summary>
        /// 使用 HMACSHA1 進行雜湊
        /// </summary>
        /// <param name="password">未雜湊密碼</param>
        /// <returns>雜湊後密碼</returns>
        public string HashPassword(string password)
        {
            ArgumentNullException.ThrowIfNull(password, nameof(password));

            byte[] signature = Encoding.UTF8.GetBytes(password);
            var apiKey = Encoding.UTF8.GetBytes(_salt);
            using HMACSHA1 hmac = new(apiKey);
            byte[] signatureBytes = hmac.ComputeHash(signature);

            return BitConverter.ToString(signatureBytes)
                .ToLowerInvariant()
                .Replace("-", "");
        }

        /// <summary>
        /// 將 <c>password</c> 雜湊後，與<c>hashedPassword</c>比對是否一致
        /// </summary>
        /// <param name="hashedPassword">The hashed password.</param>
        /// <param name="password">The password.</param>
        /// <returns>如果相符，則為 true;否則為 false</returns>
        /// <remarks>
        /// 額外拆分原因為當雜奏規則變更，但要相容舊雜湊規則，只需變更此方法
        /// </remarks>

        public bool VerifyHashedPassword(string hashedPassword, string password)
        {
            ArgumentNullException.ThrowIfNull(hashedPassword, nameof(hashedPassword));
            ArgumentNullException.ThrowIfNull(password, nameof(password));

            // 前端會直接傳加密的，所以兩個都判斷
            return hashedPassword == password
                || hashedPassword == HashPassword(password);
        }

        /// <summary>
        /// Validates the format of the password according to certain criteria.
        /// </summary>
        /// <param name="password">The password to be validated.</param>
        /// <returns>
        /// <c>true</c> if the password format is valid; otherwise, <c>false</c>.
        /// </returns>
        public bool ValidatePasswordFormat(string password)
        {
            ArgumentNullException.ThrowIfNull(password, nameof(password));


            if (password.Length < 8)
            {
                return false;
            }

            if (!Regex.IsMatch(password, @"[A-Z]+"))
            {
                return false;
            }

            if (!Regex.IsMatch(password, @"[a-z]+"))
            {
                return false;
            }

            if (!Regex.IsMatch(password, @"[0-9]+"))
            {
                return false;
            }

            if (!Regex.IsMatch(password, @"[@#$%^&+=]+"))
            {
                return false;
            }

            return true;
        }

        /// <summary>
        /// 檢查密碼是否之前用過(抓最新的3筆)
        /// </summary>
        /// <param name="userId">The user identifier.</param>
        /// <param name="newPassword">The new password.</param>
        /// <returns>
        ///   <c>true</c> if [is password used before asynchronous] [the specified user identifier]; otherwise, <c>false</c>.
        /// </returns>
        public async Task<bool> IsPasswordUsedBeforeAsync(string userId, string newPassword)
        {
            ArgumentNullException.ThrowIfNull(userId, nameof(userId));
            ArgumentNullException.ThrowIfNull(newPassword, nameof(newPassword));

            var SecuritySetting = Convert.ToInt32(Db.SysSystemSettings.FirstOrDefault(x => x.Key == "PasswordUsedBeforeCount")?.Value ?? int.MinValue.ToString());
            string hashedNewPassword = HashPassword(newPassword);
            return await Db.UserPasswordHistories
                .Where(x => x.UserInfoId == userId)
                .OrderByDescending(x => x.CreatedTime)
                .Take(SecuritySetting)
                .AnyAsync(x => x.Password == newPassword || x.Password == hashedNewPassword);
        }
    }
}
