{"profiles": {"AlifeApi.WebApi": {"commandName": "Project", "launchBrowser": true, "launchUrl": "swagger", "dotnetRunMessages": true, "applicationUrl": "http://localhost:8080", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development"}}, "IIS Express": {"commandName": "IISExpress", "launchBrowser": true, "launchUrl": "swagger", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development"}}}, "$schema": "https://json.schemastore.org/launchsettings.json", "iisSettings": {"windowsAuthentication": false, "anonymousAuthentication": true, "iisExpress": {"applicationUrl": "http://localhost:41287", "sslPort": 44306}}}