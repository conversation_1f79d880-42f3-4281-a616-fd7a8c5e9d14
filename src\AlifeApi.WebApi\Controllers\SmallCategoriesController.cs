using AlifeApi.BusinessRules.CategoryModels;
using AlifeApi.BusinessRules.Infrastructure;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Threading.Tasks;

namespace AlifeApi.WebApi.Controllers
{
    /// <summary>
    /// 小分類管理
    /// </summary>
    public class SmallCategoriesController : AuthenticatedController
    {
        private readonly SmallCategoryService _smallCategoryService;

        public SmallCategoriesController(SmallCategoryService smallCategoryService)
        {
            _smallCategoryService = smallCategoryService;
        }

        /// <summary>
        /// 取得小分類列表 (分頁)
        /// </summary>
        /// <param name="input">查詢條件</param>
        /// <returns>分頁的小分類列表</returns>
        [HttpPost]
        public async Task<IActionResult> GetSmallCategories([FromBody] SmallCategoryQueryInput input)
        {
            var result = await _smallCategoryService.GetSmallCategoryListAsync(input);
            return Ok(result);
        }

        /// <summary>
        /// 根據小分類ID取得單一小分類資訊
        /// </summary>
        /// <param name="smallCategoryId">小分類ID</param>
        /// <returns>小分類詳細資訊</returns>
        [HttpGet("{smallCategoryId}")]
        public async Task<IActionResult> GetSmallCategory(long smallCategoryId)
        {
            var result = await _smallCategoryService.GetSmallCategoryByIdAsync(smallCategoryId);
            if (result == null)
            {
                return NotFound();
            }
            return Ok(result);
        }

        /// <summary>
        /// 新增小分類
        /// </summary>
        /// <param name="input">新增小分類輸入資料</param>
        /// <returns>新增的小分類ID</returns>
        [HttpPost]
        public async Task<IActionResult> CreateSmallCategory([FromBody] SmallCategoryInput input)
        {
            try
            {
                var smallCategoryId = await _smallCategoryService.CreateSmallCategoryAsync(input);
                return CreatedAtAction(nameof(GetSmallCategory), new { smallCategoryId = smallCategoryId }, new { SmallCategoryId = smallCategoryId });
            }
            catch (Exception ex)
            {
                return BadRequest(new { message = ex.Message });
            }
        }

        /// <summary>
        /// 更新小分類資料
        /// </summary>
        /// <param name="smallCategoryId">要更新的小分類ID</param>
        /// <param name="input">更新的小分類資料</param>
        /// <returns>成功時返回 Ok，失敗時返回 NotFound 或 BadRequest</returns>
        [HttpPost("{smallCategoryId}")]
        public async Task<IActionResult> UpdateSmallCategory(long smallCategoryId, [FromBody] SmallCategoryInput input)
        {
            try
            {
                await _smallCategoryService.UpdateSmallCategoryAsync(smallCategoryId, input);
                return Ok();
            }
            catch (Exception ex) when (ex.Message.Contains("找不到"))
            {
                return NotFound(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                return BadRequest(new { message = ex.Message });
            }
        }

        /// <summary>
        /// 刪除小分類
        /// </summary>
        /// <param name="smallCategoryId">要刪除的小分類ID</param>
        /// <returns>成功時返回 Ok，失敗時返回 NotFound 或 BadRequest</returns>
        [HttpDelete("{smallCategoryId}")]
        public async Task<IActionResult> DeleteSmallCategory(long smallCategoryId)
        {
            try
            {
                await _smallCategoryService.DeleteSmallCategoryAsync(smallCategoryId);
                return Ok();
            }
            catch (Exception ex) when (ex.Message.Contains("找不到"))
            {
                return NotFound(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                return BadRequest(new { message = ex.Message });
            }
        }

        /// <summary>
        /// 取得小分類下拉選單列表
        /// </summary>
        /// <param name="largeCategoryId">可選的大分類ID，用於篩選特定大分類下的小分類</param>
        /// <param name="mediumCategoryId">可選的中分類ID，用於篩選特定中分類下的小分類</param>
        /// <returns>啟用的小分類下拉選單列表</returns>
        [HttpGet]
        public async Task<IActionResult> GetSmallCategoryDropdown([FromQuery] long? largeCategoryId = null, [FromQuery] long? mediumCategoryId = null)
        {
            var result = await _smallCategoryService.GetSmallCategoryDropdownAsync(largeCategoryId, mediumCategoryId);
            return Ok(result);
        }
    }
} 
