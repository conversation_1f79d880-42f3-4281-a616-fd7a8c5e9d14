﻿using System;
using System.Collections.Generic;

namespace AlifeApi.DataAccess.ProjectContext
{
    /// <summary>
    /// 系統代碼資料
    /// </summary>
    public partial class SysCode
    {
        public SysCode()
        {
            InverseSysCodeNavigation = new HashSet<SysCode>();
        }

        /// <summary>
        /// 類型
        /// </summary>
        public string Type { get; set; }

        /// <summary>
        /// 代碼
        /// </summary>
        public string Code { get; set; }

        /// <summary>
        /// 上層代碼
        /// </summary>
        public string ParentCode { get; set; }

        /// <summary>
        /// 內容
        /// </summary>
        public string CodeDesc { get; set; }

        /// <summary>
        /// 是否作廢
        /// </summary>
        public bool IsDisabled { get; set; }

        /// <summary>
        /// 排序
        /// </summary>
        public short CodeOrder { get; set; }

        /// <summary>
        /// 建立者
        /// </summary>
        public string CreatedUserId { get; set; }

        /// <summary>
        /// 建立時間
        /// </summary>
        public DateTime CreatedTime { get; set; }

        /// <summary>
        /// 新增者
        /// </summary>
        public string UpdatedUserId { get; set; }

        /// <summary>
        /// 更新時間
        /// </summary>
        public DateTime UpdatedTime { get; set; }

        public virtual SysCode SysCodeNavigation { get; set; }

        public virtual ICollection<SysCode> InverseSysCodeNavigation { get; set; }

    }
}
