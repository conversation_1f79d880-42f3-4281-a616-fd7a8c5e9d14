#### [AlifeApi.BusinessRules](index.md 'index')
### [AlifeApi.BusinessRules.WeeklyReportModels](AlifeApi.BusinessRules.WeeklyReportModels.md 'AlifeApi.BusinessRules.WeeklyReportModels')

## CustomerRegionAnalysis Class

客戶區域分析

```csharp
public class CustomerRegionAnalysis
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; CustomerRegionAnalysis
### Properties

<a name='AlifeApi.BusinessRules.WeeklyReportModels.CustomerRegionAnalysis.CallCount'></a>

## CustomerRegionAnalysis.CallCount Property

來電數

```csharp
public int CallCount { get; set; }
```

#### Property Value
[System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')

<a name='<PERSON>feApi.BusinessRules.WeeklyReportModels.CustomerRegionAnalysis.CallPercentage'></a>

## CustomerRegionAnalysis.CallPercentage Property

來電佔比

```csharp
public decimal CallPercentage { get; set; }
```

#### Property Value
[System.Decimal](https://docs.microsoft.com/en-us/dotnet/api/System.Decimal 'System.Decimal')

<a name='AlifeApi.BusinessRules.WeeklyReportModels.CustomerRegionAnalysis.Region'></a>

## CustomerRegionAnalysis.Region Property

區域 (縣市+區域)

```csharp
public string Region { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.WeeklyReportModels.CustomerRegionAnalysis.VisitorCount'></a>

## CustomerRegionAnalysis.VisitorCount Property

來客數

```csharp
public int VisitorCount { get; set; }
```

#### Property Value
[System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')

<a name='AlifeApi.BusinessRules.WeeklyReportModels.CustomerRegionAnalysis.VisitorPercentage'></a>

## CustomerRegionAnalysis.VisitorPercentage Property

來客佔比

```csharp
public decimal VisitorPercentage { get; set; }
```

#### Property Value
[System.Decimal](https://docs.microsoft.com/en-us/dotnet/api/System.Decimal 'System.Decimal')