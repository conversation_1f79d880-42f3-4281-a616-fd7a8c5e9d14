﻿using System.Text.Json.Serialization;

namespace AlifeApi.BusinessRules.LogAuditModels
{
    /// <summary>
    /// 日誌稽核列表
    /// </summary>
    public class LogAuditMonthStatisticsItemGetOutput
    {
        /// <summary>
        /// 月份
        /// </summary>
        public string Month { get; set; }

        /// <summary>
        /// 部門名稱
        /// </summary>
        public string DeptName { get; set; }

        /// <summary>
        /// 部門代碼
        /// </summary>
        [JsonPropertyName("Dept")]
        public string DeptId { get; set; }

        /// <summary>
        /// 紀錄數量
        /// </summary>
        public int RecordCount { get; set; }

        /// <summary>
        /// 使用者數量
        /// </summary>
        public int UserCount { get; set; }
    }
}
