#### [<PERSON>feApi.WebApi](index.md 'index')
### [AlifeApi.WebApi.Controllers](AlifeApi.WebApi.Controllers.md 'AlifeApi.WebApi.Controllers')

## SalesController Class

銷控表管理

```csharp
public class SalesController : AlifeApi.WebApi.Controllers.AuthenticatedController
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; [Microsoft.AspNetCore.Mvc.ControllerBase](https://docs.microsoft.com/en-us/dotnet/api/Microsoft.AspNetCore.Mvc.ControllerBase 'Microsoft.AspNetCore.Mvc.ControllerBase') &#129106; [Microsoft.AspNetCore.Mvc.Controller](https://docs.microsoft.com/en-us/dotnet/api/Microsoft.AspNetCore.Mvc.Controller 'Microsoft.AspNetCore.Mvc.Controller') &#129106; [AlifeApi.WebApi.Controllers.BasicController](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.WebApi.Controllers.BasicController 'AlifeApi.WebApi.Controllers.BasicController') &#129106; [AlifeApi.WebApi.Controllers.AuthenticatedController](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.WebApi.Controllers.AuthenticatedController 'AlifeApi.WebApi.Controllers.AuthenticatedController') &#129106; SalesController
### Methods

<a name='AlifeApi.WebApi.Controllers.SalesController.GetSalesControl(AlifeApi.BusinessRules.SalesModels.SalesControlInput)'></a>

## SalesController.GetSalesControl(SalesControlInput) Method

取得銷控表資料

```csharp
public System.Threading.Tasks.Task<Microsoft.AspNetCore.Mvc.ActionResult<AlifeApi.BusinessRules.SalesModels.BuildingSalesData>> GetSalesControl(AlifeApi.BusinessRules.SalesModels.SalesControlInput input);
```
#### Parameters

<a name='AlifeApi.WebApi.Controllers.SalesController.GetSalesControl(AlifeApi.BusinessRules.SalesModels.SalesControlInput).input'></a>

`input` [AlifeApi.BusinessRules.SalesModels.SalesControlInput](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.BusinessRules.SalesModels.SalesControlInput 'AlifeApi.BusinessRules.SalesModels.SalesControlInput')

銷控表查詢條件

#### Returns
[System.Threading.Tasks.Task&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')[Microsoft.AspNetCore.Mvc.ActionResult&lt;](https://docs.microsoft.com/en-us/dotnet/api/Microsoft.AspNetCore.Mvc.ActionResult-1 'Microsoft.AspNetCore.Mvc.ActionResult`1')[AlifeApi.BusinessRules.SalesModels.BuildingSalesData](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.BusinessRules.SalesModels.BuildingSalesData 'AlifeApi.BusinessRules.SalesModels.BuildingSalesData')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/Microsoft.AspNetCore.Mvc.ActionResult-1 'Microsoft.AspNetCore.Mvc.ActionResult`1')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')  
銷控表格式資料

<a name='AlifeApi.WebApi.Controllers.SalesController.GetSalesControlList(global__SalesControlListInput)'></a>

## SalesController.GetSalesControlList(SalesControlListInput) Method

取得銷控表列表格式資料（只返回有訂單的資料）

```csharp
public System.Threading.Tasks.Task<Microsoft.AspNetCore.Mvc.ActionResult<global::SalesControlListOutput>> GetSalesControlList(global::SalesControlListInput input);
```
#### Parameters

<a name='AlifeApi.WebApi.Controllers.SalesController.GetSalesControlList(global__SalesControlListInput).input'></a>

`input` [SalesControlListInput](https://docs.microsoft.com/en-us/dotnet/api/SalesControlListInput 'SalesControlListInput')

銷控表列表查詢條件

#### Returns
[System.Threading.Tasks.Task&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')[Microsoft.AspNetCore.Mvc.ActionResult&lt;](https://docs.microsoft.com/en-us/dotnet/api/Microsoft.AspNetCore.Mvc.ActionResult-1 'Microsoft.AspNetCore.Mvc.ActionResult`1')[SalesControlListOutput](https://docs.microsoft.com/en-us/dotnet/api/SalesControlListOutput 'SalesControlListOutput')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/Microsoft.AspNetCore.Mvc.ActionResult-1 'Microsoft.AspNetCore.Mvc.ActionResult`1')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')  
銷控表列表格式資料

<a name='AlifeApi.WebApi.Controllers.SalesController.GetSalesStatistics(string)'></a>

## SalesController.GetSalesStatistics(string) Method

取得案場銷售統計摘要

```csharp
public System.Threading.Tasks.Task<Microsoft.AspNetCore.Mvc.ActionResult<AlifeApi.BusinessRules.SalesModels.SalesSummaryData>> GetSalesStatistics(string siteCode);
```
#### Parameters

<a name='AlifeApi.WebApi.Controllers.SalesController.GetSalesStatistics(string).siteCode'></a>

`siteCode` [System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

案場編號

#### Returns
[System.Threading.Tasks.Task&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')[Microsoft.AspNetCore.Mvc.ActionResult&lt;](https://docs.microsoft.com/en-us/dotnet/api/Microsoft.AspNetCore.Mvc.ActionResult-1 'Microsoft.AspNetCore.Mvc.ActionResult`1')[AlifeApi.BusinessRules.SalesModels.SalesSummaryData](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.BusinessRules.SalesModels.SalesSummaryData 'AlifeApi.BusinessRules.SalesModels.SalesSummaryData')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/Microsoft.AspNetCore.Mvc.ActionResult-1 'Microsoft.AspNetCore.Mvc.ActionResult`1')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')  
銷售統計資料

<a name='AlifeApi.WebApi.Controllers.SalesController.UpdateSalesControlUnit(int,AlifeApi.WebApi.Controllers.SalesControlUnitUpdateInput)'></a>

## SalesController.UpdateSalesControlUnit(int, SalesControlUnitUpdateInput) Method

銷控表單位狀態完整更新 (同時處理訂單和Units狀態)

```csharp
public System.Threading.Tasks.Task<Microsoft.AspNetCore.Mvc.ActionResult> UpdateSalesControlUnit(int unitId, AlifeApi.WebApi.Controllers.SalesControlUnitUpdateInput input);
```
#### Parameters

<a name='AlifeApi.WebApi.Controllers.SalesController.UpdateSalesControlUnit(int,AlifeApi.WebApi.Controllers.SalesControlUnitUpdateInput).unitId'></a>

`unitId` [System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')

房屋單位ID

<a name='AlifeApi.WebApi.Controllers.SalesController.UpdateSalesControlUnit(int,AlifeApi.WebApi.Controllers.SalesControlUnitUpdateInput).input'></a>

`input` [SalesControlUnitUpdateInput](AlifeApi.WebApi.Controllers.SalesControlUnitUpdateInput.md 'AlifeApi.WebApi.Controllers.SalesControlUnitUpdateInput')

銷控表單位更新輸入

#### Returns
[System.Threading.Tasks.Task&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')[Microsoft.AspNetCore.Mvc.ActionResult](https://docs.microsoft.com/en-us/dotnet/api/Microsoft.AspNetCore.Mvc.ActionResult 'Microsoft.AspNetCore.Mvc.ActionResult')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')  
NoContent

<a name='AlifeApi.WebApi.Controllers.SalesController.UpdateUnitSalesStatus(int,AlifeApi.WebApi.Controllers.UnitStatusUpdateInput)'></a>

## SalesController.UpdateUnitSalesStatus(int, UnitStatusUpdateInput) Method

更新房屋單位銷售狀態 (舊版API，建議使用 sales-control-unit)

```csharp
public System.Threading.Tasks.Task<Microsoft.AspNetCore.Mvc.ActionResult> UpdateUnitSalesStatus(int unitId, AlifeApi.WebApi.Controllers.UnitStatusUpdateInput input);
```
#### Parameters

<a name='AlifeApi.WebApi.Controllers.SalesController.UpdateUnitSalesStatus(int,AlifeApi.WebApi.Controllers.UnitStatusUpdateInput).unitId'></a>

`unitId` [System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')

房屋單位ID

<a name='AlifeApi.WebApi.Controllers.SalesController.UpdateUnitSalesStatus(int,AlifeApi.WebApi.Controllers.UnitStatusUpdateInput).input'></a>

`input` [UnitStatusUpdateInput](AlifeApi.WebApi.Controllers.UnitStatusUpdateInput.md 'AlifeApi.WebApi.Controllers.UnitStatusUpdateInput')

狀態更新輸入

#### Returns
[System.Threading.Tasks.Task&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')[Microsoft.AspNetCore.Mvc.ActionResult](https://docs.microsoft.com/en-us/dotnet/api/Microsoft.AspNetCore.Mvc.ActionResult 'Microsoft.AspNetCore.Mvc.ActionResult')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')  
NoContent