﻿using AlifeApi.BusinessRules.Infrastructure;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AlifeApi.BusinessRules.ProjectVersionModels
{
    public class ProjectVersionListCondition : PagedListInput
    {
        /// <summary>
        /// 搜尋內容
        /// </summary>
        public string SearchContent { get; set; }

        /// <summary>
        /// 搜尋起始時間
        /// </summary>
        public DateTime? SearchStartDate { get; set; }

        /// <summary>
        /// 搜尋結束時間
        /// </summary>
        public DateTime? SearchEndDate { get; set; }
    }
}
