#### [AlifeApi.BusinessRules](index.md 'index')

## AlifeApi.BusinessRules.RoleGroupModels Namespace

| Classes | |
| :--- | :--- |
| [MenuTreeOutput](AlifeApi.BusinessRules.RoleGroupModels.MenuTreeOutput.md 'AlifeApi.BusinessRules.RoleGroupModels.MenuTreeOutput') | 權限 |
| [RoleGroupCreateInput](AlifeApi.BusinessRules.RoleGroupModels.RoleGroupCreateInput.md 'AlifeApi.BusinessRules.RoleGroupModels.RoleGroupCreateInput') | 角色權限 |
| [RoleGroupCreateInputPg](AlifeApi.BusinessRules.RoleGroupModels.RoleGroupCreateInputPg.md 'AlifeApi.BusinessRules.RoleGroupModels.RoleGroupCreateInputPg') | 角色權限建立輸入(PostgreSQL 版本) |
| [RoleGroupDeleteInput](AlifeApi.BusinessRules.RoleGroupModels.RoleGroupDeleteInput.md 'AlifeApi.BusinessRules.RoleGroupModels.RoleGroupDeleteInput') | 刪除角色權限 |
| [RoleGroupDeleteInputPg](AlifeApi.BusinessRules.RoleGroupModels.RoleGroupDeleteInputPg.md 'AlifeApi.BusinessRules.RoleGroupModels.RoleGroupDeleteInputPg') | 刪除角色權限（PostgreSQL 版本） |
| [RoleGroupDropdownInputPg](AlifeApi.BusinessRules.RoleGroupModels.RoleGroupDropdownInputPg.md 'AlifeApi.BusinessRules.RoleGroupModels.RoleGroupDropdownInputPg') | 角色群組下拉選單查詢輸入 |
| [RoleGroupDropdownOutput](AlifeApi.BusinessRules.RoleGroupModels.RoleGroupDropdownOutput.md 'AlifeApi.BusinessRules.RoleGroupModels.RoleGroupDropdownOutput') | 角色群組下拉選單輸出資料 |
| [RoleGroupListGetInput](AlifeApi.BusinessRules.RoleGroupModels.RoleGroupListGetInput.md 'AlifeApi.BusinessRules.RoleGroupModels.RoleGroupListGetInput') | 取得角色權限 |
| [RoleGroupListGetInputPg](AlifeApi.BusinessRules.RoleGroupModels.RoleGroupListGetInputPg.md 'AlifeApi.BusinessRules.RoleGroupModels.RoleGroupListGetInputPg') | 取得角色權限列表（PostgreSQL 版本） |
| [RoleGroupOutput](AlifeApi.BusinessRules.RoleGroupModels.RoleGroupOutput.md 'AlifeApi.BusinessRules.RoleGroupModels.RoleGroupOutput') | |
| [RoleGroupOutputPg](AlifeApi.BusinessRules.RoleGroupModels.RoleGroupOutputPg.md 'AlifeApi.BusinessRules.RoleGroupModels.RoleGroupOutputPg') | 角色權限輸出（PostgreSQL 版本） |
| [RoleGroupPermissionUpdateInput](AlifeApi.BusinessRules.RoleGroupModels.RoleGroupPermissionUpdateInput.md 'AlifeApi.BusinessRules.RoleGroupModels.RoleGroupPermissionUpdateInput') | 更新角色權限 |
| [RoleGroupPermissionUpdateInputPg](AlifeApi.BusinessRules.RoleGroupModels.RoleGroupPermissionUpdateInputPg.md 'AlifeApi.BusinessRules.RoleGroupModels.RoleGroupPermissionUpdateInputPg') | 更新角色權限（PostgreSQL 版本） |
| [RoleGroupServicePg](AlifeApi.BusinessRules.RoleGroupModels.RoleGroupServicePg.md 'AlifeApi.BusinessRules.RoleGroupModels.RoleGroupServicePg') | 角色權限商業邏輯（PostgreSQL 版本） |
| [RoleGroupUserUpdateInput](AlifeApi.BusinessRules.RoleGroupModels.RoleGroupUserUpdateInput.md 'AlifeApi.BusinessRules.RoleGroupModels.RoleGroupUserUpdateInput') | 修改角色權限使用者 |
| [RoleGroupUserUpdateInputPg](AlifeApi.BusinessRules.RoleGroupModels.RoleGroupUserUpdateInputPg.md 'AlifeApi.BusinessRules.RoleGroupModels.RoleGroupUserUpdateInputPg') | 修改角色權限使用者（PostgreSQL 版本） |
| [RoleOutput](AlifeApi.BusinessRules.RoleGroupModels.RoleOutput.md 'AlifeApi.BusinessRules.RoleGroupModels.RoleOutput') | 角色資料模型 |
