#### [AlifeApi.BusinessRules](index.md 'index')
### [AlifeApi.BusinessRules.SysCodeModels](AlifeApi.BusinessRules.SysCodeModels.md 'AlifeApi.BusinessRules.SysCodeModels')

## SysCodeUpdateInput Class

編輯、作廢 SysCode 代碼

```csharp
public class SysCodeUpdateInput
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; SysCodeUpdateInput
### Properties

<a name='AlifeApi.BusinessRules.SysCodeModels.SysCodeUpdateInput.Code'></a>

## SysCodeUpdateInput.Code Property

代碼（取得資料用)

```csharp
public string Code { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.SysCodeModels.SysCodeUpdateInput.Desc'></a>

## SysCodeUpdateInput.Desc Property

代碼名稱

```csharp
public string Desc { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.SysCodeModels.SysCodeUpdateInput.IsActive'></a>

## SysCodeUpdateInput.IsActive Property

代碼是否啟用

```csharp
public System.Nullable<bool> IsActive { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.Boolean](https://docs.microsoft.com/en-us/dotnet/api/System.Boolean 'System.Boolean')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

<a name='AlifeApi.BusinessRules.SysCodeModels.SysCodeUpdateInput.Type'></a>

## SysCodeUpdateInput.Type Property

代碼類型（取得資料用)

```csharp
public string Type { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')