#### [AlifeApi.DataAccess](index.md 'index')
### [AlifeApi.DataAccess.ProjectContent](AlifeApi.DataAccess.ProjectContent.md 'AlifeApi.DataAccess.ProjectContent')

## Owner Class

業主基本資料表

```csharp
public class Owner
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; Owner
### Properties

<a name='AlifeApi.DataAccess.ProjectContent.Owner.CompanyAddress'></a>

## Owner.CompanyAddress Property

公司登記地址

```csharp
public string CompanyAddress { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContent.Owner.CompanyName'></a>

## Owner.CompanyName Property

公司名稱

```csharp
public string CompanyName { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContent.Owner.CompanyPhone'></a>

## Owner.CompanyPhone Property

公司登記電話

```csharp
public string CompanyPhone { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContent.Owner.ContactPerson'></a>

## Owner.ContactPerson Property

主要聯絡窗口人員姓名

```csharp
public string ContactPerson { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContent.Owner.ContactPhone1'></a>

## Owner.ContactPhone1 Property

主要聯絡電話

```csharp
public string ContactPhone1 { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContent.Owner.ContactPhone2'></a>

## Owner.ContactPhone2 Property

次要聯絡電話 (備用)

```csharp
public string ContactPhone2 { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContent.Owner.Email'></a>

## Owner.Email Property

聯絡電子郵件地址

```csharp
public string Email { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContent.Owner.IdentificationNumber'></a>

## Owner.IdentificationNumber Property

證號 (依人別決定是 統一編號 或 身分證ID)

```csharp
public string IdentificationNumber { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContent.Owner.MailingAddress'></a>

## Owner.MailingAddress Property

郵件通訊地址 (若與公司地址不同)

```csharp
public string MailingAddress { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContent.Owner.OwnerId'></a>

## Owner.OwnerId Property

業主唯一識別碼 (自動增長)

```csharp
public int OwnerId { get; set; }
```

#### Property Value
[System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')

<a name='AlifeApi.DataAccess.ProjectContent.Owner.PersonType'></a>

## Owner.PersonType Property

人別 (值為 法人 或 自然人)

```csharp
public string PersonType { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContent.Owner.ResponsiblePerson'></a>

## Owner.ResponsiblePerson Property

負責人姓名

```csharp
public string ResponsiblePerson { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')