using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;

namespace AlifeApi.BusinessRules.CategoryModels
{
    /// <summary>
    /// 小分類資料輸入 DTO
    /// </summary>
    public class SmallCategoryInput
    {
        /// <summary>
        /// 所屬中分類ID
        /// </summary>
        [JsonPropertyName("MediumCategoryId")]
        [Required(ErrorMessage = "所屬中分類ID為必填")]
        public long MediumCategoryId { get; set; }

        /// <summary>
        /// 小分類名稱
        /// </summary>
        [JsonPropertyName("Name")]
        [Required(ErrorMessage = "小分類名稱為必填")]
        [StringLength(100, ErrorMessage = "小分類名稱長度不能超過100個字元")]
        public string Name { get; set; }

        /// <summary>
        /// 小分類描述
        /// </summary>
        [JsonPropertyName("Description")]
        [StringLength(500, ErrorMessage = "描述長度不能超過500個字元")]
        public string Description { get; set; }

        /// <summary>
        /// 排序順序
        /// </summary>
        [JsonPropertyName("SortOrder")]
        [Range(0, int.MaxValue, ErrorMessage = "排序順序必須為非負整數")]
        public int SortOrder { get; set; }

        /// <summary>
        /// 是否啟用
        /// </summary>
        [JsonPropertyName("IsActive")]
        public bool IsActive { get; set; } = true;
    }
} 
