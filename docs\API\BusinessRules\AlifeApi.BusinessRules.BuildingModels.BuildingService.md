#### [AlifeApi.BusinessRules](index.md 'index')
### [AlifeApi.BusinessRules.BuildingModels](AlifeApi.BusinessRules.BuildingModels.md 'AlifeApi.BusinessRules.BuildingModels')

## BuildingService Class

建築物服務

```csharp
public class BuildingService : AlifeApi.BusinessRules.Infrastructure.ServiceBase<AlifeApi.DataAccess.ProjectContent.alifeContext>
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; [AlifeApi.Common.InfrastructureBase](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.Common.InfrastructureBase 'AlifeApi.Common.InfrastructureBase') &#129106; [AlifeApi.BusinessRules.Infrastructure.ServiceBase&lt;](AlifeApi.BusinessRules.Infrastructure.ServiceBase_TDbContext_.md 'AlifeApi.BusinessRules.Infrastructure.ServiceBase<TDbContext>')[AlifeApi.DataAccess.ProjectContent.alifeContext](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.DataAccess.ProjectContent.alifeContext 'AlifeApi.DataAccess.ProjectContent.alifeContext')[&gt;](AlifeApi.BusinessRules.Infrastructure.ServiceBase_TDbContext_.md 'AlifeApi.BusinessRules.Infrastructure.ServiceBase<TDbContext>') &#129106; BuildingService
### Methods

<a name='AlifeApi.BusinessRules.BuildingModels.BuildingService.BulkImportFromExcelAsync(System.IO.Stream)'></a>

## BuildingService.BulkImportFromExcelAsync(Stream) Method

從 Excel 檔批次匯入建築物、樓層及車位

```csharp
public System.Threading.Tasks.Task BulkImportFromExcelAsync(System.IO.Stream excelStream);
```
#### Parameters

<a name='AlifeApi.BusinessRules.BuildingModels.BuildingService.BulkImportFromExcelAsync(System.IO.Stream).excelStream'></a>

`excelStream` [System.IO.Stream](https://docs.microsoft.com/en-us/dotnet/api/System.IO.Stream 'System.IO.Stream')

包含三個工作表的 Excel 檔："建築物","樓層","車位"，且各欄位須符合範本格式

#### Returns
[System.Threading.Tasks.Task](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task 'System.Threading.Tasks.Task')

<a name='AlifeApi.BusinessRules.BuildingModels.BuildingService.CreateBuildingAsync(AlifeApi.BusinessRules.BuildingModels.BuildingCreateInput)'></a>

## BuildingService.CreateBuildingAsync(BuildingCreateInput) Method

建立建築物

```csharp
public System.Threading.Tasks.Task<int> CreateBuildingAsync(AlifeApi.BusinessRules.BuildingModels.BuildingCreateInput input);
```
#### Parameters

<a name='AlifeApi.BusinessRules.BuildingModels.BuildingService.CreateBuildingAsync(AlifeApi.BusinessRules.BuildingModels.BuildingCreateInput).input'></a>

`input` [BuildingCreateInput](AlifeApi.BusinessRules.BuildingModels.BuildingCreateInput.md 'AlifeApi.BusinessRules.BuildingModels.BuildingCreateInput')

建築物建立輸入資料

#### Returns
[System.Threading.Tasks.Task&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')[System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')  
新建建築物的ID

<a name='AlifeApi.BusinessRules.BuildingModels.BuildingService.DeleteBuildingAsync(int)'></a>

## BuildingService.DeleteBuildingAsync(int) Method

刪除建築物

```csharp
public System.Threading.Tasks.Task DeleteBuildingAsync(int buildingId);
```
#### Parameters

<a name='AlifeApi.BusinessRules.BuildingModels.BuildingService.DeleteBuildingAsync(int).buildingId'></a>

`buildingId` [System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')

建築物ID

#### Returns
[System.Threading.Tasks.Task](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task 'System.Threading.Tasks.Task')

<a name='AlifeApi.BusinessRules.BuildingModels.BuildingService.GetBuildingByIdAsync(int)'></a>

## BuildingService.GetBuildingByIdAsync(int) Method

根據ID取得建築物詳細資料

```csharp
public System.Threading.Tasks.Task<AlifeApi.BusinessRules.BuildingModels.BuildingOutput?> GetBuildingByIdAsync(int buildingId);
```
#### Parameters

<a name='AlifeApi.BusinessRules.BuildingModels.BuildingService.GetBuildingByIdAsync(int).buildingId'></a>

`buildingId` [System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')

建築物ID

#### Returns
[System.Threading.Tasks.Task&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')[BuildingOutput](AlifeApi.BusinessRules.BuildingModels.BuildingOutput.md 'AlifeApi.BusinessRules.BuildingModels.BuildingOutput')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')  
建築物詳細資料

<a name='AlifeApi.BusinessRules.BuildingModels.BuildingService.GetBuildingDropdownListAsync(AlifeApi.BusinessRules.BuildingModels.BuildingDropdownInput)'></a>

## BuildingService.GetBuildingDropdownListAsync(BuildingDropdownInput) Method

取得建築物下拉選單

```csharp
public System.Threading.Tasks.Task<System.Collections.Generic.List<AlifeApi.BusinessRules.BuildingModels.BuildingDropdownOutput>> GetBuildingDropdownListAsync(AlifeApi.BusinessRules.BuildingModels.BuildingDropdownInput input);
```
#### Parameters

<a name='AlifeApi.BusinessRules.BuildingModels.BuildingService.GetBuildingDropdownListAsync(AlifeApi.BusinessRules.BuildingModels.BuildingDropdownInput).input'></a>

`input` [BuildingDropdownInput](AlifeApi.BusinessRules.BuildingModels.BuildingDropdownInput.md 'AlifeApi.BusinessRules.BuildingModels.BuildingDropdownInput')

查詢條件

#### Returns
[System.Threading.Tasks.Task&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')[System.Collections.Generic.List&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Collections.Generic.List-1 'System.Collections.Generic.List`1')[BuildingDropdownOutput](AlifeApi.BusinessRules.BuildingModels.BuildingDropdownOutput.md 'AlifeApi.BusinessRules.BuildingModels.BuildingDropdownOutput')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Collections.Generic.List-1 'System.Collections.Generic.List`1')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')  
建築物下拉選單列表

<a name='AlifeApi.BusinessRules.BuildingModels.BuildingService.GetBuildingListAsync(AlifeApi.BusinessRules.BuildingModels.BuildingQueryInput)'></a>

## BuildingService.GetBuildingListAsync(BuildingQueryInput) Method

取得建築物列表 (分頁)

```csharp
public System.Threading.Tasks.Task<AlifeApi.BusinessRules.Infrastructure.PagedListOutput<AlifeApi.BusinessRules.BuildingModels.BuildingListOutput>> GetBuildingListAsync(AlifeApi.BusinessRules.BuildingModels.BuildingQueryInput input);
```
#### Parameters

<a name='AlifeApi.BusinessRules.BuildingModels.BuildingService.GetBuildingListAsync(AlifeApi.BusinessRules.BuildingModels.BuildingQueryInput).input'></a>

`input` [BuildingQueryInput](AlifeApi.BusinessRules.BuildingModels.BuildingQueryInput.md 'AlifeApi.BusinessRules.BuildingModels.BuildingQueryInput')

查詢條件

#### Returns
[System.Threading.Tasks.Task&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')[AlifeApi.BusinessRules.Infrastructure.PagedListOutput&lt;](AlifeApi.BusinessRules.Infrastructure.PagedListOutput_TDetail_.md 'AlifeApi.BusinessRules.Infrastructure.PagedListOutput<TDetail>')[BuildingListOutput](AlifeApi.BusinessRules.BuildingModels.BuildingListOutput.md 'AlifeApi.BusinessRules.BuildingModels.BuildingListOutput')[&gt;](AlifeApi.BusinessRules.Infrastructure.PagedListOutput_TDetail_.md 'AlifeApi.BusinessRules.Infrastructure.PagedListOutput<TDetail>')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')  
分頁的建築物列表

<a name='AlifeApi.BusinessRules.BuildingModels.BuildingService.UpdateBuildingAsync(int,AlifeApi.BusinessRules.BuildingModels.BuildingUpdateInput)'></a>

## BuildingService.UpdateBuildingAsync(int, BuildingUpdateInput) Method

更新建築物資訊

```csharp
public System.Threading.Tasks.Task UpdateBuildingAsync(int buildingId, AlifeApi.BusinessRules.BuildingModels.BuildingUpdateInput input);
```
#### Parameters

<a name='AlifeApi.BusinessRules.BuildingModels.BuildingService.UpdateBuildingAsync(int,AlifeApi.BusinessRules.BuildingModels.BuildingUpdateInput).buildingId'></a>

`buildingId` [System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')

建築物ID

<a name='AlifeApi.BusinessRules.BuildingModels.BuildingService.UpdateBuildingAsync(int,AlifeApi.BusinessRules.BuildingModels.BuildingUpdateInput).input'></a>

`input` [BuildingUpdateInput](AlifeApi.BusinessRules.BuildingModels.BuildingUpdateInput.md 'AlifeApi.BusinessRules.BuildingModels.BuildingUpdateInput')

建築物更新輸入資料

#### Returns
[System.Threading.Tasks.Task](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task 'System.Threading.Tasks.Task')