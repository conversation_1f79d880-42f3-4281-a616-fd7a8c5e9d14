﻿using System.ComponentModel;

namespace AlifeApi.BusinessRules.Infrastructure
{
    /// <summary>
    /// 系統訊息
    /// </summary>
    public enum Message
    {
        /// <summary>
        /// 成功
        /// </summary>
        [Description("Success")]
        Success = 0,

        /// <summary>
        /// HTTP 400 Bad Request
        /// </summary>
        [Description("Bad Request")]
        Http_400_BadRequest = 400,

        /// <summary>
        /// HTTP 401 Http_401_Unauthorized
        /// </summary>
        [Description("Unauthorized")]
        Http_401_Unauthorized = 401,

        /// <summary>
        /// HTTP 403 Http_403_Forbidden
        /// </summary>
        [Description("Forbidden")]
        Http_403_Forbidden = 403,

        /// <summary>
        /// HTTP 404 Not Found
        /// </summary>
        [Description("Not Found")]
        Http_404_NotFound = 404,

        /// <summary>
        /// HTTP 500 Internal Server Error
        /// </summary>
        [Description("Internal Server Error")]
        Http_500_InternalServerError = 500,

        /// <summary>
        /// HTTP 501 Not Implemented
        /// </summary>
        [Description("Not Implemented")]
        Http_501_NotImplemented = 501,

        /// <summary>
        /// 非法輸入格式
        /// </summary>
        [Description("非法輸入格式")]
        InputDataFormatError = 410,

        #region 登入相關
        /// <summary>
        /// CaptchaFail
        /// </summary>
        [Description("驗證碼錯誤")]
        CaptchaFail = 117,

        /// <summary>
        /// 登入資訊無 Captcha
        /// </summary>
        [Description("登入資訊無驗證碼")]
        LoginInfoWithoutCaptcha = 120,

        /// <summary>
        /// 帳號已存在 AccountAlreadyExist
        /// </summary>
        [Description("帳號已存在")]
        AccountAlreadyExist = 122,

        /// <summary>
        /// 帳號輸入錯誤
        /// </summary>
        [Description("帳號輸入錯誤")]
        AccountFail = 131,

        /// <summary>
        /// 密碼輸入錯誤
        /// </summary>
        [Description("密碼輸入錯誤")]
        PasswordFail = 132,

        /// <summary>
        /// 請確認帳號是否停用或不符合使用效期
        /// </summary>
        [Description("請確認帳號是否停用或不符合使用效期")]
        CheckAccountIsValid = 133,

        /// <summary>
        /// 密碼長時間未變更
        /// </summary>
        [Description("密碼長時間未變更")]
        PasswordChange = 134,

        /// <summary>
        /// 密碼輸入錯誤太多次，請稍等
        /// </summary>
        [Description("密碼輸入錯誤太多次，請稍等")]
        AccountLoginFailTooManyTimeNeedToWait = 1001,

        /// <summary>
        /// 預設密碼
        /// </summary>
        [Description("預設密碼")]
        DefaultPassword = 1002,
        #endregion

        /// <summary>
        /// 系統異常
        /// </summary>
        [Description("系統異常")]
        SystemError = 999,

        /// <summary>
        /// 密碼與前幾次相同
        /// </summary>
        [Description("密碼與前幾次相同")]
        PasswordUsedRecently = 1000,
    }
}
