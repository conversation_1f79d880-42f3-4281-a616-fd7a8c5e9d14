## 專案說明
- 本專案為 Alife 房仲後端 RESTful API，使用 ASP.NET Core 6.0 + Entity Framework Core + PostgreSQL
- 提供客戶管理 (Customer)、審核流程 (ReviewTasks)、系統代碼 (SysCode)、角色權限 (RoleGroup) 等業務

## 環境需求
- .NET 6.0 SDK
- PostgreSQL 13 以上
- Visual Studio 2022 或 Visual Studio Code

## 安裝與設定
1. Clone 專案
   ```bash
   git clone <your-repo-url>
   cd a-life-api
   ```
2. 設定資料庫連線（appsettings.json）
   ```json
   "ConnectionStrings": {
     "DefaultConnection": "Host=localhost;Port=5432;Database=Alife;Username=user;Password=****"
   }
   ```
3. 初始化資料庫
   ```bash
   dotnet ef database update
   ```
4. 編譯並啟動
   ```bash
   dotnet build
   dotnet run --urls "https://localhost:5001"
   ```

## 資料表欄位變更記錄
- **Customer** 及 **Customer_Record** 表
  - `CreateUserInfoId` → `CreatedUserInfoId`
  - `CreateTime` → `CreatedTime`
  - `UpdateUserInfoId` → `UpdatedUserInfoId`
  - `UpdateTime` → `UpdatedTime`
- **ReviewTasks** 表
  - `CreatedAt` → `CreatedTime`
  - `CreatorUserInfoId` → `CreatedUserInfoId`

## 時間欄位與時區
- 採用 PostgreSQL `timestamp without time zone` (TIMESTAMP)
- 程式中所有時間皆以本地時間 (`DateTime.Now`) 或 `.ToLocalTime()` 處理，勿將 UTC (`DateTime.UtcNow`) 直接寫入

## 編碼風格與設定
- 已配置 `.editorconfig`，請遵循以下原則：
  - PascalCase 命名方法、屬性
  - JSON 回傳欄位名稱使用大駝峰 (PascalCase)
  - `using` directives 放置在 namespace 外部
  - 強制 `this.`、var 使用規則
  - 其他 C# & .NET 編碼慣例詳見 `.editorconfig`

## 主要資料夾結構
```
├─src/AlifeApi.BusinessRules    // Business logic 層
├─src/AlifeApi.DataAccess      // EF Core 實體與 DbContext
├─src/AlifeApi.WebApi          // ASP.NET Core 控制器與路由
├─docs/                        // API 文件 (Markdown)
```

## 版本管理與 Git Flow
- 使用 `main` 分支為主要開發分支
- Feature 分支以 `feature/xxx` 命名
- 完成功能後發 PR 並合併

## 其他注意事項
- 圖片上傳服務需設定 `ImageStorageSettings:ImageBaseUrl`
- 權限選單樹方法 `GetMenuTreesAsync` 已優化為：只有當所有子項都被選中或自身被授權時，父節點才顯示選中
- 已移除 MSSQL 連線與相關功能，程式中原有的 `UseSqlServer` 已全數註解

---
*歡迎參考並維護本後端範本*