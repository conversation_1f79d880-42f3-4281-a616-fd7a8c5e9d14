﻿using AlifeApi.BusinessRules.DepartmentModels;
using AlifeApi.BusinessRules.Infrastructure;
using AlifeApi.BusinessRules.UserInfoModels;
using Microsoft.AspNetCore.Mvc;

namespace AlifeApi.WebApi.Controllers
{
    /// <summary>
    /// 部門管理
    /// </summary>
    public class DepartmentController : AuthenticatedController
    {
        private readonly DepartmentService _departmentService;

        /// <summary>
        /// 建構函數
        /// </summary>
        public DepartmentController(DepartmentService departmentService)
        {
            _departmentService = departmentService ?? throw new ArgumentNullException(nameof(departmentService));
        }

        /// <summary>
        /// 取得部門列表 (分頁)
        /// </summary>
        /// <param name="input">查詢條件</param>
        /// <returns>分頁的部門列表</returns>
        [HttpPost("GetList")]
        public async Task<ActionResult<PagedListOutput<DepartmentOutput>>> GetDepartmentListAsync([FromBody] DepartmentListGetInput input)
        {
            try
            {
                var result = await _departmentService.GetDepartmentListAsync(input);
                return Ok(result);
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }

        /// <summary>
        /// 根據公司ID和部門ID取得單一部門資訊
        /// </summary>
        /// <param name="companyId">公司ID</param>
        /// <param name="departmentId">部門ID</param>
        /// <returns>部門詳細資訊</returns>
        [HttpGet("{companyId}/{departmentId}")]
        public async Task<ActionResult<DepartmentOutput>> GetDepartmentByIdAsync(string companyId, string departmentId)
        {
            try
            {
                var department = await _departmentService.GetDepartmentByIdAsync(companyId, departmentId);
                if (department == null)
                {
                    return NotFound("找不到指定的部門");
                }
                return Ok(department);
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }

        /// <summary>
        /// 新增部門
        /// </summary>
        /// <param name="input">新增部門輸入資料</param>
        /// <returns>執行結果</returns>
        [HttpPost("Create")]
        public async Task<ActionResult> CreateDepartmentAsync([FromBody] DepartmentCreateInput input)
        {
            try
            {
                await _departmentService.CreateDepartmentAsync(input);
                return Ok();
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }

        /// <summary>
        /// 更新部門資訊
        /// </summary>
        /// <param name="companyId">公司ID</param>
        /// <param name="departmentId">部門ID</param>
        /// <param name="input">更新部門輸入資料</param>
        /// <returns>更新後的部門資訊</returns>
        [HttpPut("{companyId}/{departmentId}")]
        public async Task<ActionResult<DepartmentOutput>> UpdateDepartmentAsync(string companyId, string departmentId, [FromBody] DepartmentUpdateInput input)
        {
            if (companyId != input.CompanyId || departmentId != input.DepartmentId)
            {
                return BadRequest("路由參數與請求內容中的ID不符");
            }

            try
            {
                var updatedDepartment = await _departmentService.UpdateDepartmentAsync(input);
                return Ok(updatedDepartment);
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }

        /// <summary>
        /// 刪除部門
        /// </summary>
        /// <param name="companyId">公司ID</param>
        /// <param name="departmentId">部門ID</param>
        /// <returns>執行結果訊息</returns>
        [HttpDelete("{companyId}/{departmentId}")]
        public async Task<ActionResult<UserInfoUpdateOutput>> DeleteDepartmentAsync(string companyId, string departmentId)
        {
            try
            {
                var result = await _departmentService.DeleteDepartmentAsync(companyId, departmentId);
                return Ok(result);
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }

        /// <summary>
        /// 取得部門下拉選單
        /// </summary>
        /// <param name="input">查詢條件</param>
        /// <returns>部門下拉選單列表</returns>
        [HttpPost]
        public async Task<ActionResult<List<DepartmentDropdownOutput>>> GetDepartmentDropdownListAsync([FromBody] DepartmentDropdownInput input)
        {
            try
            {
                var result = await _departmentService.GetDepartmentDropdownListAsync(input);
                return Ok(result);
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }
    }
} 
