#### [AlifeApi.Common](index.md 'index')
### [AlifeApi.Common.DataAnnotations](AlifeApi.Common.DataAnnotations.md 'AlifeApi.Common.DataAnnotations')

## RequiredForTypeAttribute Class

僅對指定的類型執行必填驗證的屬性

```csharp
public class RequiredForTypeAttribute : System.ComponentModel.DataAnnotations.RequiredAttribute
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; [System.Attribute](https://docs.microsoft.com/en-us/dotnet/api/System.Attribute 'System.Attribute') &#129106; [System.ComponentModel.DataAnnotations.ValidationAttribute](https://docs.microsoft.com/en-us/dotnet/api/System.ComponentModel.DataAnnotations.ValidationAttribute 'System.ComponentModel.DataAnnotations.ValidationAttribute') &#129106; [System.ComponentModel.DataAnnotations.RequiredAttribute](https://docs.microsoft.com/en-us/dotnet/api/System.ComponentModel.DataAnnotations.RequiredAttribute 'System.ComponentModel.DataAnnotations.RequiredAttribute') &#129106; RequiredForTypeAttribute

### See Also
- [System.ComponentModel.DataAnnotations.RequiredAttribute](https://docs.microsoft.com/en-us/dotnet/api/System.ComponentModel.DataAnnotations.RequiredAttribute 'System.ComponentModel.DataAnnotations.RequiredAttribute')
### Constructors

<a name='AlifeApi.Common.DataAnnotations.RequiredForTypeAttribute.RequiredForTypeAttribute(System.Type[])'></a>

## RequiredForTypeAttribute(Type[]) Constructor

初始化

```csharp
public RequiredForTypeAttribute(params System.Type[] targetTypes);
```
#### Parameters

<a name='AlifeApi.Common.DataAnnotations.RequiredForTypeAttribute.RequiredForTypeAttribute(System.Type[]).targetTypes'></a>

`targetTypes` [System.Type](https://docs.microsoft.com/en-us/dotnet/api/System.Type 'System.Type')[[]](https://docs.microsoft.com/en-us/dotnet/api/System.Array 'System.Array')

必填驗證應用的目標類型
### Methods

<a name='AlifeApi.Common.DataAnnotations.RequiredForTypeAttribute.IsValid(object)'></a>

## RequiredForTypeAttribute.IsValid(object) Method

確定指定的值對象是否有效

```csharp
public override bool IsValid(object value);
```
#### Parameters

<a name='AlifeApi.Common.DataAnnotations.RequiredForTypeAttribute.IsValid(object).value'></a>

`value` [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object')

要驗證的值對象

#### Returns
[System.Boolean](https://docs.microsoft.com/en-us/dotnet/api/System.Boolean 'System.Boolean')  
如果值對象有效，則為 true；否則為 false

<a name='AlifeApi.Common.DataAnnotations.RequiredForTypeAttribute.IsValid(object,System.ComponentModel.DataAnnotations.ValidationContext)'></a>

## RequiredForTypeAttribute.IsValid(object, ValidationContext) Method

驗證屬性值

```csharp
protected override System.ComponentModel.DataAnnotations.ValidationResult IsValid(object value, System.ComponentModel.DataAnnotations.ValidationContext validationContext);
```
#### Parameters

<a name='AlifeApi.Common.DataAnnotations.RequiredForTypeAttribute.IsValid(object,System.ComponentModel.DataAnnotations.ValidationContext).value'></a>

`value` [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object')

要驗證的屬性值

<a name='AlifeApi.Common.DataAnnotations.RequiredForTypeAttribute.IsValid(object,System.ComponentModel.DataAnnotations.ValidationContext).validationContext'></a>

`validationContext` [System.ComponentModel.DataAnnotations.ValidationContext](https://docs.microsoft.com/en-us/dotnet/api/System.ComponentModel.DataAnnotations.ValidationContext 'System.ComponentModel.DataAnnotations.ValidationContext')

表示要驗證的內容的上下文

#### Returns
[System.ComponentModel.DataAnnotations.ValidationResult](https://docs.microsoft.com/en-us/dotnet/api/System.ComponentModel.DataAnnotations.ValidationResult 'System.ComponentModel.DataAnnotations.ValidationResult')  
驗證結果。