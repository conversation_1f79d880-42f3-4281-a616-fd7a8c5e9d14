using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Threading.Tasks;
using AlifeApi.BusinessRules.Infrastructure;
using AlifeApi.DataAccess.ProjectContent;
using Microsoft.EntityFrameworkCore;

namespace AlifeApi.BusinessRules.WeeklyReportModels
{
    /// <summary>
    /// 週報統計服務
    /// </summary>
    public class WeeklyReportService : ServiceBase<alifeContext>
    {
        /// <summary>
        /// 建構函數
        /// </summary>
        public WeeklyReportService(
            IServiceProvider serviceProvider,
            alifeContext dbContext)
            : base(serviceProvider, dbContext)
        {
        }

        /// <summary>
        /// 取得週報統計
        /// </summary>
        /// <param name="input">查詢條件</param>
        /// <returns>週報統計結果</returns>
        public async Task<WeeklyReportOutput> GetWeeklyReportAsync(WeeklyReportQueryInput input)
        {
            ArgumentNullException.ThrowIfNull(input, nameof(input));

            // 取得案場資訊
            var site = await Db.Sites
                .Where(s => s.SiteCode == input.SiteCode)
                .Select(s => new { s.SiteCode, s.SiteName })
                .FirstOrDefaultAsync();

            if (site == null)
            {
                throw new ArgumentException($"找不到案場編號: {input.SiteCode}");
            }

            // 計算週的日期範圍
            var (startDate, endDate) = GetWeekDateRange(input.Year, input.WeekNumber);

            // 建立週報輸出物件
            var report = new WeeklyReportOutput
            {
                SiteCode = input.SiteCode,
                SiteName = site.SiteName,
                Year = input.Year,
                WeekNumber = input.WeekNumber,
                StartDate = startDate,
                EndDate = endDate,
                Title = $"第{input.WeekNumber}週 週報表 ({startDate:MM/dd}-{endDate:MM/dd})"
            };

            // 並行取得各項統計數據
            var businessResultsTask = GetBusinessResultStatisticsAsync(input.SiteCode, startDate, endDate);
            var customerAnalysisTask = GetCustomerAnalysisStatisticsAsync(input.SiteCode, startDate, endDate);
            var salesStatisticsTask = GetSalesStatisticsAsync(input.SiteCode);

            await Task.WhenAll(businessResultsTask, customerAnalysisTask, salesStatisticsTask);

            report.BusinessResults = await businessResultsTask;
            report.CustomerAnalysis = await customerAnalysisTask;
            report.SalesStatistics = await salesStatisticsTask;

            return report;
        }

        /// <summary>
        /// 取得經營成果統計
        /// </summary>
        private async Task<WeeklyBusinessResultStatistics> GetBusinessResultStatisticsAsync(string siteCode, DateTime startDate, DateTime endDate)
        {
            // 取得本週每日統計
            var currentWeekStats = await GetDailyStatsInPeriodAsync(siteCode, startDate, endDate);

            // 取得前期累計統計 (年初到本週開始前)
            var yearStart = new DateTime(startDate.Year, 1, 1);
            var previousPeriodStats = await GetPeriodStatsAsync(siteCode, yearStart, startDate.AddDays(-1));

            return new WeeklyBusinessResultStatistics
            {
                VisitorStats = CreateWeeklyStatisticItem("來客", currentWeekStats, previousPeriodStats),
                CallStats = CreateWeeklyStatisticItem("來電", currentWeekStats, previousPeriodStats),
                LeadStats = CreateWeeklyStatisticItem("留單", currentWeekStats, previousPeriodStats),
                TransactionStats = CreateWeeklyStatisticItem("成交", currentWeekStats, previousPeriodStats)
            };
        }

        /// <summary>
        /// 取得客戶分析統計
        /// </summary>
        private async Task<WeeklyCustomerAnalysisStatistics> GetCustomerAnalysisStatisticsAsync(string siteCode, DateTime startDate, DateTime endDate)
        {
            // 取得本週有記錄的客戶
            var weeklyCustomers = await Db.CustomerRecords
                .Where(cr => cr.RecordedAt >= startDate && cr.RecordedAt <= endDate)
                .Join(Db.Customers, cr => cr.CustomerId, c => c.CustomerId, (cr, c) => new { cr, c })
                .Where(x => x.c.SiteCode == siteCode)
                .Select(x => new
                {
                    x.c.CustomerId,
                    x.c.Birthday,
                    x.c.Occupation,
                    x.c.City,
                    x.c.District,
                    x.cr.RecordType
                })
                .ToListAsync();

            // 取得成交客戶
            var transactionCustomers = await Db.PurchaseOrders
                .Where(po => po.OrderDate >= DateOnly.FromDateTime(startDate) && po.OrderDate <= DateOnly.FromDateTime(endDate))
                .Where(po => po.SiteCode == siteCode)
                .Join(Db.Customers, po => po.CustomerId, c => c.CustomerId, (po, c) => new { po, c })
                .Select(x => new
                {
                    x.c.CustomerId,
                    x.c.Birthday,
                    x.c.Occupation,
                    x.c.City,
                    x.c.District,
                    RecordType = "成交"
                })
                .ToListAsync();

            var allCustomers = weeklyCustomers.Concat(transactionCustomers).Cast<dynamic>().ToList();

            // 年齡分析
            var ageAnalysis = AnalyzeCustomersByAge(allCustomers);

            // 行業分析
            var industryAnalysis = AnalyzeCustomersByIndustry(allCustomers);

            // 區域分析
            var regionAnalysis = AnalyzeCustomersByRegion(allCustomers);

            return new WeeklyCustomerAnalysisStatistics
            {
                AgeAnalysis = ageAnalysis,
                IndustryAnalysis = industryAnalysis,
                RegionAnalysis = regionAnalysis
            };
        }

        /// <summary>
        /// 取得銷售統計
        /// </summary>
        private async Task<WeeklySalesStatistics> GetSalesStatisticsAsync(string siteCode)
        {
            // 房屋統計
            var unitStats = await Db.Units
                .Where(u => u.SiteCode == siteCode)
                .GroupBy(u => u.Status)
                .Select(g => new
                {
                    Status = g.Key,
                    Count = g.Count(),
                    MinPriceSum = g.Sum(u => u.MinimumPrice ?? 0),
                    ListPriceSum = g.Sum(u => u.ListPrice ?? 0)
                })
                .ToListAsync();

            // 車位統計 - 現在從Units表查詢車位相關的統計
            var parkingStats = await Db.Units
                .Where(u => u.SiteCode == siteCode && u.UnitType == "車位")
                .GroupBy(u => u.Status)
                .Select(g => new
                {
                    Status = g.Key,
                    Count = g.Count(),
                    MinPriceSum = g.Sum(u => u.MinimumPrice ?? 0),
                    ListPriceSum = g.Sum(u => u.ListPrice ?? 0)
                })
                .ToListAsync();

            // 已簽約成交統計 (透過新的 PurchaseOrderItems 架構)
            var signedUnits = await (from poi in Db.PurchaseOrderItems
                                   join po in Db.PurchaseOrders on poi.OrderId equals po.OrderId
                                   where po.SiteCode == siteCode && poi.UnitId.HasValue
                                   select new { PropertyPrice = poi.ItemPrice })
                                   .ToListAsync();

            var signedParking = await (from poi in Db.PurchaseOrderItems
                                     join po in Db.PurchaseOrders on poi.OrderId equals po.OrderId
                                     where po.SiteCode == siteCode && poi.ParkingSpaceId.HasValue
                                     select new { ParkingSpacePrice = poi.ItemPrice })
                                     .ToListAsync();

            // 組織房屋統計
            var unitSalesStats = CreateUnitSalesStatistics(unitStats.Cast<dynamic>().ToList(), signedUnits.Cast<dynamic>().ToList());

            // 組織車位統計
            var parkingSalesStats = CreateParkingSalesStatistics(parkingStats.Cast<dynamic>().ToList(), signedParking.Cast<dynamic>().ToList());

            return new WeeklySalesStatistics
            {
                UnitStats = unitSalesStats,
                ParkingStats = parkingSalesStats
            };
        }

        /// <summary>
        /// 取得期間內每日統計
        /// </summary>
        private async Task<List<DailyStatistic>> GetDailyStatsInPeriodAsync(string siteCode, DateTime startDate, DateTime endDate)
        {
            var dateRange = Enumerable.Range(0, (endDate - startDate).Days + 1)
                .Select(i => startDate.AddDays(i))
                .ToList();

            var dailyStats = new List<DailyStatistic>();

            // 批次查詢客戶記錄統計
            var customerRecordStats = await Db.CustomerRecords
                .Where(cr => cr.RecordedAt >= startDate && cr.RecordedAt <= endDate)
                .Join(Db.Customers, cr => cr.CustomerId, c => c.CustomerId, (cr, c) => new { cr, c })
                .Where(x => x.c.SiteCode == siteCode)
                .GroupBy(x => new { Date = x.cr.RecordedAt.Date, RecordType = x.cr.RecordType })
                .Select(g => new
                {
                    Date = g.Key.Date,
                    RecordType = g.Key.RecordType,
                    Count = g.Count()
                })
                .ToListAsync();

            // 批次查詢成交統計
            var transactionStats = await Db.PurchaseOrders
                .Where(po => po.OrderDate >= DateOnly.FromDateTime(startDate) && po.OrderDate <= DateOnly.FromDateTime(endDate))
                .Where(po => po.SiteCode == siteCode)
                .GroupBy(po => po.OrderDate.ToDateTime(TimeOnly.MinValue).Date)
                .Select(g => new
                {
                    Date = g.Key,
                    Count = g.Count()
                })
                .ToListAsync();

            foreach (var date in dateRange)
            {
                var visitorCount = customerRecordStats.FirstOrDefault(s => s.Date == date && s.RecordType == "來客")?.Count ?? 0;
                var callCount = customerRecordStats.FirstOrDefault(s => s.Date == date && s.RecordType == "來電")?.Count ?? 0;
                var leadCount = customerRecordStats.FirstOrDefault(s => s.Date == date && s.RecordType == "留單")?.Count ?? 0;
                var transactionCount = transactionStats.FirstOrDefault(s => s.Date == date)?.Count ?? 0;

                dailyStats.AddRange(new[]
                {
                    new DailyStatistic { Date = date, DayOfWeek = GetChineseDayOfWeek(date), Count = visitorCount },
                    new DailyStatistic { Date = date, DayOfWeek = GetChineseDayOfWeek(date), Count = callCount },
                    new DailyStatistic { Date = date, DayOfWeek = GetChineseDayOfWeek(date), Count = leadCount },
                    new DailyStatistic { Date = date, DayOfWeek = GetChineseDayOfWeek(date), Count = transactionCount }
                });
            }

            return dailyStats;
        }

        /// <summary>
        /// 取得期間統計
        /// </summary>
        private async Task<Dictionary<string, int>> GetPeriodStatsAsync(string siteCode, DateTime startDate, DateTime endDate)
        {
            if (startDate > endDate) return new Dictionary<string, int>();

            var customerRecordStats = await Db.CustomerRecords
                .Where(cr => cr.RecordedAt >= startDate && cr.RecordedAt <= endDate)
                .Join(Db.Customers, cr => cr.CustomerId, c => c.CustomerId, (cr, c) => new { cr, c })
                .Where(x => x.c.SiteCode == siteCode)
                .GroupBy(x => x.cr.RecordType)
                .Select(g => new { RecordType = g.Key, Count = g.Count() })
                .ToListAsync();

            var transactionCount = await Db.PurchaseOrders
                .Where(po => po.OrderDate >= DateOnly.FromDateTime(startDate) && po.OrderDate <= DateOnly.FromDateTime(endDate))
                .Where(po => po.SiteCode == siteCode)
                .CountAsync();

            return new Dictionary<string, int>
            {
                ["來客"] = customerRecordStats.FirstOrDefault(s => s.RecordType == "來客")?.Count ?? 0,
                ["來電"] = customerRecordStats.FirstOrDefault(s => s.RecordType == "來電")?.Count ?? 0,
                ["留單"] = customerRecordStats.FirstOrDefault(s => s.RecordType == "留單")?.Count ?? 0,
                ["成交"] = transactionCount
            };
        }

        /// <summary>
        /// 建立週統計項目
        /// </summary>
        private WeeklyStatisticItem CreateWeeklyStatisticItem(string recordType, List<DailyStatistic> dailyStats, Dictionary<string, int> previousStats)
        {
            var currentWeekStats = dailyStats.Where(ds => ds.Count > 0).ToList(); // 簡化，實際需要根據recordType篩選
            var currentWeekTotal = currentWeekStats.Sum(ds => ds.Count);
            var previousTotal = previousStats.GetValueOrDefault(recordType, 0);

            return new WeeklyStatisticItem
            {
                PreviousPeriodTotal = previousTotal,
                CurrentWeekTotal = currentWeekTotal,
                Total = previousTotal + currentWeekTotal,
                DailyDetails = currentWeekStats
            };
        }

        /// <summary>
        /// 按年齡分析客戶
        /// </summary>
        private List<CustomerAgeAnalysis> AnalyzeCustomersByAge(List<dynamic> customers)
        {
            // 簡化的年齡分析實作
            var ageGroups = customers
                .Where(c => c.Birthday != null)
                .GroupBy(c => GetAgeRange(CalculateAge(c.Birthday)))
                .Select(g => new CustomerAgeAnalysis
                {
                    AgeRange = g.Key,
                    VisitorCount = g.Count(c => c.RecordType == "來客"),
                    ReturnVisitCount = g.Count(c => c.RecordType == "留單"),
                    TransactionCount = g.Count(c => c.RecordType == "成交")
                })
                .ToList();

            // 計算百分比
            var totalVisitors = ageGroups.Sum(a => a.VisitorCount);
            var totalReturnVisits = ageGroups.Sum(a => a.ReturnVisitCount);
            var totalTransactions = ageGroups.Sum(a => a.TransactionCount);

            foreach (var group in ageGroups)
            {
                group.VisitorPercentage = totalVisitors > 0 ? Math.Round((decimal)group.VisitorCount / totalVisitors * 100, 1) : 0;
                group.ReturnVisitPercentage = totalReturnVisits > 0 ? Math.Round((decimal)group.ReturnVisitCount / totalReturnVisits * 100, 1) : 0;
                group.TransactionPercentage = totalTransactions > 0 ? Math.Round((decimal)group.TransactionCount / totalTransactions * 100, 1) : 0;
            }

            return ageGroups;
        }

        /// <summary>
        /// 按行業分析客戶
        /// </summary>
        private List<CustomerIndustryAnalysis> AnalyzeCustomersByIndustry(List<dynamic> customers)
        {
            var industryGroups = customers
                .Where(c => !string.IsNullOrEmpty(c.Occupation))
                .GroupBy(c => c.Occupation)
                .Select(g => new CustomerIndustryAnalysis
                {
                    Industry = g.Key,
                    VisitorCount = g.Count(c => c.RecordType == "來客"),
                    ReturnVisitCount = g.Count(c => c.RecordType == "留單"),
                    TransactionCount = g.Count(c => c.RecordType == "成交")
                })
                .ToList();

            // 計算百分比
            var totalVisitors = industryGroups.Sum(i => i.VisitorCount);
            var totalReturnVisits = industryGroups.Sum(i => i.ReturnVisitCount);
            var totalTransactions = industryGroups.Sum(i => i.TransactionCount);

            foreach (var group in industryGroups)
            {
                group.VisitorPercentage = totalVisitors > 0 ? Math.Round((decimal)group.VisitorCount / totalVisitors * 100, 1) : 0;
                group.ReturnVisitPercentage = totalReturnVisits > 0 ? Math.Round((decimal)group.ReturnVisitCount / totalReturnVisits * 100, 1) : 0;
                group.TransactionPercentage = totalTransactions > 0 ? Math.Round((decimal)group.TransactionCount / totalTransactions * 100, 1) : 0;
            }

            return industryGroups;
        }

        /// <summary>
        /// 按區域分析客戶
        /// </summary>
        private List<CustomerRegionAnalysis> AnalyzeCustomersByRegion(List<dynamic> customers)
        {
            var regionGroups = customers
                .Where(c => !string.IsNullOrEmpty(c.City) || !string.IsNullOrEmpty(c.District))
                .GroupBy(c => $"{c.City}{c.District}")
                .Select(g => new CustomerRegionAnalysis
                {
                    Region = g.Key,
                    VisitorCount = g.Count(c => c.RecordType == "來客"),
                    CallCount = g.Count(c => c.RecordType == "來電")
                })
                .ToList();

            // 計算百分比
            var totalVisitors = regionGroups.Sum(r => r.VisitorCount);
            var totalCalls = regionGroups.Sum(r => r.CallCount);

            foreach (var group in regionGroups)
            {
                group.VisitorPercentage = totalVisitors > 0 ? Math.Round((decimal)group.VisitorCount / totalVisitors * 100, 1) : 0;
                group.CallPercentage = totalCalls > 0 ? Math.Round((decimal)group.CallCount / totalCalls * 100, 1) : 0;
            }

            return regionGroups;
        }

        /// <summary>
        /// 建立房屋銷售統計
        /// </summary>
        private UnitSalesStatistics CreateUnitSalesStatistics(List<dynamic> unitStats, List<dynamic> signedUnits)
        {
            var availableUnits = unitStats.FirstOrDefault(s => s.Status == "可售")?.Count ?? 0;
            var availableAmount = unitStats.FirstOrDefault(s => s.Status == "可售")?.ListPriceSum ?? 0;
            var soldUnits = unitStats.FirstOrDefault(s => s.Status == "已售")?.Count ?? 0;
            var soldMinimumAmount = unitStats.FirstOrDefault(s => s.Status == "已售")?.MinPriceSum ?? 0;

            var signedTotal = signedUnits.Count;
            var signedTotalAmount = signedUnits.Sum(s => s.PropertyPrice ?? 0);

            var totalUnits = availableUnits + soldUnits;
            var salesRate = totalUnits > 0 ? Math.Round((decimal)soldUnits / totalUnits * 100, 1) : 0;
            var unsoldRate = 100 - salesRate;

            return new UnitSalesStatistics
            {
                AvailableUnits = availableUnits,
                AvailableAmount = availableAmount,
                SoldUnits = soldUnits,
                SoldMinimumAmount = soldMinimumAmount,
                SignedTotal = signedTotal,
                SignedTotalTransactionAmount = signedTotalAmount,
                SalesRate = salesRate,
                UnsoldUnits = availableUnits,
                UnsoldAmount = availableAmount,
                UnsoldRate = unsoldRate
            };
        }

        /// <summary>
        /// 建立車位銷售統計
        /// </summary>
        private ParkingSalesStatistics CreateParkingSalesStatistics(List<dynamic> parkingStats, List<dynamic> signedParking)
        {
            var availableSpaces = parkingStats.FirstOrDefault(s => s.Status == "可售")?.Count ?? 0;
            var availableAmount = parkingStats.FirstOrDefault(s => s.Status == "可售")?.ListPriceSum ?? 0;
            var soldSpaces = parkingStats.FirstOrDefault(s => s.Status == "已售")?.Count ?? 0;
            var soldAmount = parkingStats.FirstOrDefault(s => s.Status == "已售")?.MinPriceSum ?? 0;

            var signedTotal = signedParking.Count;
            var signedTotalAmount = signedParking.Sum(s => s.ParkingSpacePrice ?? 0);

            var totalSpaces = availableSpaces + soldSpaces;
            var salesRate = totalSpaces > 0 ? Math.Round((decimal)soldSpaces / totalSpaces * 100, 1) : 0;
            var unsoldRate = 100 - salesRate;

            return new ParkingSalesStatistics
            {
                AvailableSpaces = availableSpaces,
                AvailableAmount = availableAmount,
                SoldSpaces = soldSpaces,
                SoldAmount = soldAmount,
                SignedTotal = signedTotal,
                SignedTotalTransactionAmount = signedTotalAmount,
                SalesRate = salesRate,
                UnsoldSpaces = availableSpaces,
                UnsoldAmount = availableAmount,
                UnsoldRate = unsoldRate
            };
        }

        /// <summary>
        /// 根據年份和週次計算日期範圍
        /// </summary>
        private static (DateTime startDate, DateTime endDate) GetWeekDateRange(int year, int weekNumber)
        {
            var jan1 = new DateTime(year, 1, 1);
            var daysToAdd = (weekNumber - 1) * 7;
            var startDate = jan1.AddDays(daysToAdd - (int)jan1.DayOfWeek + 1); // 週一開始
            var endDate = startDate.AddDays(6); // 週日結束

            return (startDate, endDate);
        }

        /// <summary>
        /// 計算年齡
        /// </summary>
        private static int CalculateAge(DateTime? birthDate)
        {
            if (!birthDate.HasValue) return 0;
            var today = DateTime.Today;
            var age = today.Year - birthDate.Value.Year;
            if (birthDate.Value.Date > today.AddYears(-age)) age--;
            return age;
        }

        /// <summary>
        /// 取得年齡區間
        /// </summary>
        private static string GetAgeRange(int age)
        {
            return age switch
            {
                < 30 => "30歲以下",
                >= 30 and < 40 => "30-39歲",
                >= 40 and < 50 => "40-49歲",
                >= 50 and < 60 => "50-59歲",
                _ => "60歲以上"
            };
        }

        /// <summary>
        /// 取得中文星期顯示
        /// </summary>
        private static string GetChineseDayOfWeek(DateTime date)
        {
            return date.DayOfWeek switch
            {
                DayOfWeek.Sunday => "星期日",
                DayOfWeek.Monday => "星期一",
                DayOfWeek.Tuesday => "星期二",
                DayOfWeek.Wednesday => "星期三",
                DayOfWeek.Thursday => "星期四",
                DayOfWeek.Friday => "星期五",
                DayOfWeek.Saturday => "星期六",
                _ => date.DayOfWeek.ToString()
            };
        }
    }
} 
