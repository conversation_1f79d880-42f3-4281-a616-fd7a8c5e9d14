﻿<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SignalR User Registration</title>
</head>
<body>
    <h1>SignalR User Registration</h1>

    <label for="jwtInput">Enter JWT:</label>
    <input type="text" id="jwtInput">
    <button onclick="registerUser()">Register</button>

    <ul id="onlineUsersList"></ul>
    <p id="onlineUsersCount"></p>

    <p id="onlineUsersCount2"></p>

    <script src="https://code.jquery.com/jquery-3.6.4.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/microsoft-signalr/3.1.9/signalr.min.js"></script>

    <script>
        const connection = new signalR.HubConnectionBuilder()
            .withUrl("/onlinecount")
            .configureLogging(signalR.LogLevel.Information)
            .build();

        // 接收在線user
        connection.on("OnlineUsersUpdated", (onlineUsers, onlineUsersCount) => {
            console.log(onlineUsers)
            updateOnlineUsers(onlineUsers, onlineUsersCount);
        });

        // 接收在線人數
        connection.on("OnlineUsersCountUpdated", (onlineUsersCount) => {
            console.log(onlineUsersCount)
            const onlineUsersCountElement = document.getElementById("onlineUsersCount2");
            // Update online users count
            onlineUsersCountElement.textContent = `Online Users Count: ${onlineUsersCount}`;
        });

        // 定義伺服器端可能呼叫的客戶端方法
        connection.on("ForceDisconnect", function () {
            // 處理強制斷線的邏輯
            connection.stop();
        });



        //註冊
        function registerUser() {
            const jwt = document.getElementById("jwtInput").value;

            connection.start()
                .then(() => {
                    console.log("SignalR connected");
                    // Call the RegisterUser method on the server
                    connection.invoke("RegisterUser", jwt)
                        .then(() => {
                            console.log("User registered successfully");
                        })
                        .catch((err) => {
                            console.error(err.toString());
                        });
                })
                .catch((err) => {
                    console.error(err.toString());
                });
        }

        function updateOnlineUsers(onlineUsers, onlineUsersCount) {
            const onlineUsersList = document.getElementById("onlineUsersList");
            const onlineUsersCountElement = document.getElementById("onlineUsersCount");

            // Clear previous data
            onlineUsersList.innerHTML = "";

            // Update online users list
            onlineUsers.forEach(user => {
                const listItem = document.createElement("li");
                listItem.textContent = `UserID: ${user.UserID}, LogoutTime: ${user.LogoutTime}`;
                onlineUsersList.appendChild(listItem);
            });

            // Update online users count
            onlineUsersCountElement.textContent = `Online Users Count: ${onlineUsersCount}`;
        }
    </script>
</body>
</html>
