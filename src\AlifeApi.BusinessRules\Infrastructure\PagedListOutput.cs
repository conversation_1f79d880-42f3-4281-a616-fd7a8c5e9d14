﻿using System.Text.Json.Serialization;

namespace AlifeApi.BusinessRules.Infrastructure
{
    public class PagedListOutput<TDetail> : PagedListInput
    {
        private int _totalPages;

        /// <summary>
        /// Initializes a new instance of the <see cref="PagedListOutput{TDetail}"/> class.
        /// </summary>
        /// <param name="input">The input.</param>
        /// <exception cref="ArgumentNullException">input</exception>
        public PagedListOutput(PagedListInput input)
        {
            ArgumentNullException.ThrowIfNull(input, nameof(input));

            UsingPaging = input.UsingPaging && input.NumberOfPerPage > 0;
            NumberOfPerPage = input.NumberOfPerPage;
            PageIndex = input.PageIndex;
            SortOrderInfos = input.SortOrderInfos;
            SearchTermInfos = input.SearchTermInfos;
        }

        /// <summary>
        /// 資料明細
        /// </summary>
        [JsonPropertyName("Detail")]
        public IEnumerable<TDetail> Details { get; set; } = Enumerable.Empty<TDetail>();

        /// <summary>
        /// 總頁數 
        /// </summary>
        public int TotalPages
        {
            get
            {
                _totalPages = 0;
                try
                {
                    if (UsingPaging && NumberOfPerPage > 0 && RecordCount > 0)
                    {
                        if (RecordCount <= NumberOfPerPage)
                        {
                            _totalPages = 1;
                        }
                        else
                        {
                            // 取回足頁的總頁數
                            _totalPages = RecordCount / NumberOfPerPage;
                            // 不足1頁數補1頁
                            if ((RecordCount % NumberOfPerPage) > 0)
                            {
                                _totalPages++;
                            }
                        }
                        PageIndex = PageIndex < 1 ? 0 : PageIndex;
                        PageIndex = (PageIndex > RecordCount) ? RecordCount : PageIndex;
                    }
                }
                catch
                {
                    _totalPages = 0;
                    UsingPaging = false;
                }
                return _totalPages;
            }
        }

        /// <summary>
        /// 記錄總數
        /// </summary>
        public int RecordCount { get; set; }
    }
}
