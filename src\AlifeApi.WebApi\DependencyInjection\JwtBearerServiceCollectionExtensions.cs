﻿using System.Text;
using AlifeApi.WebApi.DependencyInjection;
using AlifeApi.WebApi.Options;
using AlifeApi.WebApi.Util;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.IdentityModel.Tokens;

namespace AlifeApi.WebApi.DependencyInjection
{
    public static class JwtBearerServiceCollectionExtensions
    {
        public static void AddJwtBearer(this IServiceCollection services, IConfigurationSection section)
        {
            SymmetricSecurityKey issuerSigningKey = new(Encoding.UTF8.GetBytes(section.GetValue<string>("SignKey")));
            TokenValidationParameters tokenValidationParams = new()
            {
                ValidateIssuerSigningKey = true,
                IssuerSigningKey = issuerSigningKey,
                ValidateIssuer = false,
                ValidateAudience = false,
                ValidateLifetime = true,
                RequireExpirationTime = false,
                ClockSkew = TimeSpan.Zero
            };

            services.AddSingleton(tokenValidationParams);
            services.AddScoped<JwtHelper, JwtHelper>();
            services.AddOptions<JwtOptions>()
                .Bind(section)
                .ValidateDataAnnotations()
                .ValidateOnStart();

            services.AddAuthentication(JwtBearerDefaults.AuthenticationScheme)
                    .AddJwtBearer(options =>
                    {
                        // 當驗證失敗時，回應標頭會包含 WWW-Authenticate 標頭，這裡會顯示失敗的詳細錯誤原因
                        options.IncludeErrorDetails = true; // 預設值為 true，有時會特別關閉
                        options.TokenValidationParameters = new TokenValidationParameters
                        {
                            // 透過這項宣告，就可以從 "sub" 取值並設定給 User.Identity.Name
                            NameClaimType = "http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier",
                            // 透過這項宣告，就可以從 "roles" 取值，並可讓 [Authorize] 判斷角色
                            RoleClaimType = "http://schemas.microsoft.com/ws/2008/06/identity/claims/role",
                            // 一般都會驗證 Issuer
                            ValidateIssuer = true,
                            ValidIssuer = section.GetValue<string>("Issuer"),
                            // 通常不太需要驗證 Audience
                            ValidateAudience = false,
                            // ValidAudience = "JwtAuthDemo", // 不驗證就不需要填寫
                            // 一般都會驗證 Token 的有效期間
                            ValidateLifetime = true,
                            // 如果 Token 中包含 key 才需要驗證，一般都只有簽章而已
                            ValidateIssuerSigningKey = false,
                            IssuerSigningKey = issuerSigningKey
                        };
                    });
        }
    }
}
