#### [AlifeApi.BusinessRules](index.md 'index')
### [AlifeApi.BusinessRules.GradeModels](AlifeApi.BusinessRules.GradeModels.md 'AlifeApi.BusinessRules.GradeModels')

## UpdateGradePermissionCondition Class

```csharp
public class UpdateGradePermissionCondition
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; UpdateGradePermissionCondition
### Properties

<a name='AlifeApi.BusinessRules.GradeModels.UpdateGradePermissionCondition.FuncIds'></a>

## UpdateGradePermissionCondition.FuncIds Property

```csharp
public System.Collections.Generic.IEnumerable<string> FuncIds { get; }
```

#### Property Value
[System.Collections.Generic.IEnumerable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Collections.Generic.IEnumerable-1 'System.Collections.Generic.IEnumerable`1')[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Collections.Generic.IEnumerable-1 'System.Collections.Generic.IEnumerable`1')

### Remarks
前端會傳字串，只好這樣處理

<a name='AlifeApi.BusinessRules.GradeModels.UpdateGradePermissionCondition.GradeId'></a>

## UpdateGradePermissionCondition.GradeId Property

使用者職稱

```csharp
public string GradeId { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.GradeModels.UpdateGradePermissionCondition.Permission'></a>

## UpdateGradePermissionCondition.Permission Property

權限

```csharp
public string Permission { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')