﻿using System;
using System.Collections.Generic;

namespace AlifeApi.DataAccess.ProjectContent
{
    /// <summary>
    /// 客戶互動或訪談記錄表
    /// </summary>
    public partial class CustomerRecord
    {
        /// <summary>
        /// 記錄唯一識別碼 (主鍵)
        /// </summary>
        public int CustomerRecordId { get; set; }
        /// <summary>
        /// 關聯的客戶ID (外鍵)
        /// </summary>
        public int CustomerId { get; set; }
        /// <summary>
        /// 互動或記錄的類型
        /// </summary>
        public string RecordType { get; set; }
        /// <summary>
        /// 詳細的記錄內容
        /// </summary>
        public string Notes { get; set; }
        /// <summary>
        /// 互動發生的實際時間或記錄時間
        /// </summary>
        public DateTime RecordedAt { get; set; }
        /// <summary>
        /// 客戶分級或標籤
        /// </summary>
        public string CustomerLevel { get; set; }
        /// <summary>
        /// 負責此次互動的人員資訊
        /// </summary>
        public string HandledBy { get; set; }
        /// <summary>
        /// 此記錄創建人員的 UserInfo ID (VARCHAR(15) 外鍵)
        /// </summary>
        public string CreatedUserInfoId { get; set; }
        /// <summary>
        /// 此記錄的創建時間
        /// </summary>
        public DateTime CreatedTime { get; set; }
        /// <summary>
        /// 此記錄最後更新人員的 UserInfo ID (VARCHAR(15) 外鍵)
        /// </summary>
        public string UpdatedUserInfoId { get; set; }
        /// <summary>
        /// 此記錄的最後更新時間
        /// </summary>
        public DateTime UpdatedTime { get; set; }

        public virtual Customer Customer { get; set; }
    }
}
