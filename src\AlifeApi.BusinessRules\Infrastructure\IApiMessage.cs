﻿namespace AlifeApi.BusinessRules.Infrastructure
{
    /// <remarks>
    /// 現行處理會在 ApiResultFilter 統一回傳格式(ApiResult)，
    /// 當中的 Response 大部分是 SystemMessage，
    /// 但某些情況下，可能是使用別的 Enum，
    /// 或著是要回傳 SystemMessage 的其他 代碼
    /// 如果 Output 有實作 IApiMessage 的話，
    /// ApiResultFilter 會把 Response 的 Enum 替換為 IApiMessage.Response，
    /// 並且在 Enum 值非 0 的情況下，將 ApiResult.Body 設為 null，
    /// 目前我是傾向於統一一個 Enum，
    /// 因為前端有針對不同情境判斷不同的 Code，否則我怕有取到重覆 Code 的可能，
    /// 如果未來不會使用其他的 Enum 型別，那就可以把 Enum 改為 SystemMessage
    /// </remarks>
    public interface IApiMessage
    {
        /// <summary>
        /// Gets the response.
        /// </summary>
        Enum Response { get; }
    }
}
