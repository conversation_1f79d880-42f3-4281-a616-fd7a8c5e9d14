#### [AlifeApi.BusinessRules](index.md 'index')
### [AlifeApi.BusinessRules.ReviewModels](AlifeApi.BusinessRules.ReviewModels.md 'AlifeApi.BusinessRules.ReviewModels')

## ReviewDeptGroupOutput Class

部門分組輸出資料

```csharp
public class ReviewDeptGroupOutput
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; ReviewDeptGroupOutput
### Properties

<a name='AlifeApi.BusinessRules.ReviewModels.ReviewDeptGroupOutput.Dept'></a>

## ReviewDeptGroupOutput.Dept Property

部門資料

```csharp
public AlifeApi.BusinessRules.ReviewModels.ReviewDeptOutput Dept { get; set; }
```

#### Property Value
[ReviewDeptOutput](AlifeApi.BusinessRules.ReviewModels.ReviewDeptOutput.md 'AlifeApi.BusinessRules.ReviewModels.ReviewDeptOutput')

<a name='AlifeApi.BusinessRules.ReviewModels.ReviewDeptGroupOutput.Users'></a>

## ReviewDeptGroupOutput.Users Property

部門下的用戶列表

```csharp
public System.Collections.Generic.List<AlifeApi.BusinessRules.ReviewModels.ReviewUserOutput> Users { get; set; }
```

#### Property Value
[System.Collections.Generic.List&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Collections.Generic.List-1 'System.Collections.Generic.List`1')[ReviewUserOutput](AlifeApi.BusinessRules.ReviewModels.ReviewUserOutput.md 'AlifeApi.BusinessRules.ReviewModels.ReviewUserOutput')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Collections.Generic.List-1 'System.Collections.Generic.List`1')