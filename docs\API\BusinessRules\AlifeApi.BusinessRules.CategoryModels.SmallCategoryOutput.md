#### [AlifeApi.BusinessRules](index.md 'index')
### [AlifeApi.BusinessRules.CategoryModels](AlifeApi.BusinessRules.CategoryModels.md 'AlifeApi.BusinessRules.CategoryModels')

## SmallCategoryOutput Class

小分類資料輸出 DTO

```csharp
public class SmallCategoryOutput
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; SmallCategoryOutput
### Properties

<a name='AlifeApi.BusinessRules.CategoryModels.SmallCategoryOutput.CreatedUserInfoId'></a>

## SmallCategoryOutput.CreatedUserInfoId Property

建立人員ID

```csharp
public string CreatedUserInfoId { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.CategoryModels.SmallCategoryOutput.CreatedUserName'></a>

## SmallCategoryOutput.CreatedUserName Property

建立人員名稱

```csharp
public string CreatedUserName { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.CategoryModels.SmallCategoryOutput.CreateTime'></a>

## SmallCategoryOutput.CreateTime Property

建立時間

```csharp
public System.DateTime CreateTime { get; set; }
```

#### Property Value
[System.DateTime](https://docs.microsoft.com/en-us/dotnet/api/System.DateTime 'System.DateTime')

<a name='AlifeApi.BusinessRules.CategoryModels.SmallCategoryOutput.Description'></a>

## SmallCategoryOutput.Description Property

小分類描述

```csharp
public string Description { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.CategoryModels.SmallCategoryOutput.IsActive'></a>

## SmallCategoryOutput.IsActive Property

是否啟用

```csharp
public System.Nullable<bool> IsActive { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.Boolean](https://docs.microsoft.com/en-us/dotnet/api/System.Boolean 'System.Boolean')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

<a name='AlifeApi.BusinessRules.CategoryModels.SmallCategoryOutput.LargeCategoryId'></a>

## SmallCategoryOutput.LargeCategoryId Property

所屬大分類ID

```csharp
public long LargeCategoryId { get; set; }
```

#### Property Value
[System.Int64](https://docs.microsoft.com/en-us/dotnet/api/System.Int64 'System.Int64')

<a name='AlifeApi.BusinessRules.CategoryModels.SmallCategoryOutput.LargeCategoryName'></a>

## SmallCategoryOutput.LargeCategoryName Property

所屬大分類名稱

```csharp
public string LargeCategoryName { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.CategoryModels.SmallCategoryOutput.MediumCategoryId'></a>

## SmallCategoryOutput.MediumCategoryId Property

所屬中分類ID

```csharp
public long MediumCategoryId { get; set; }
```

#### Property Value
[System.Int64](https://docs.microsoft.com/en-us/dotnet/api/System.Int64 'System.Int64')

<a name='AlifeApi.BusinessRules.CategoryModels.SmallCategoryOutput.MediumCategoryName'></a>

## SmallCategoryOutput.MediumCategoryName Property

所屬中分類名稱

```csharp
public string MediumCategoryName { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.CategoryModels.SmallCategoryOutput.Name'></a>

## SmallCategoryOutput.Name Property

小分類名稱

```csharp
public string Name { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.CategoryModels.SmallCategoryOutput.SmallCategoryId'></a>

## SmallCategoryOutput.SmallCategoryId Property

小分類ID

```csharp
public long SmallCategoryId { get; set; }
```

#### Property Value
[System.Int64](https://docs.microsoft.com/en-us/dotnet/api/System.Int64 'System.Int64')

<a name='AlifeApi.BusinessRules.CategoryModels.SmallCategoryOutput.SortOrder'></a>

## SmallCategoryOutput.SortOrder Property

排序順序

```csharp
public int SortOrder { get; set; }
```

#### Property Value
[System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')

<a name='AlifeApi.BusinessRules.CategoryModels.SmallCategoryOutput.UpdatedUserInfoId'></a>

## SmallCategoryOutput.UpdatedUserInfoId Property

更新人員ID

```csharp
public string UpdatedUserInfoId { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.CategoryModels.SmallCategoryOutput.UpdatedUserName'></a>

## SmallCategoryOutput.UpdatedUserName Property

更新人員名稱

```csharp
public string UpdatedUserName { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.CategoryModels.SmallCategoryOutput.UpdateTime'></a>

## SmallCategoryOutput.UpdateTime Property

更新時間

```csharp
public System.DateTime UpdateTime { get; set; }
```

#### Property Value
[System.DateTime](https://docs.microsoft.com/en-us/dotnet/api/System.DateTime 'System.DateTime')