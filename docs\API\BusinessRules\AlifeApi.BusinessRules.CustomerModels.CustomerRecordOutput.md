#### [AlifeApi.BusinessRules](index.md 'index')
### [AlifeApi.BusinessRules.CustomerModels](AlifeApi.BusinessRules.CustomerModels.md 'AlifeApi.BusinessRules.CustomerModels')

## CustomerRecordOutput Class

客戶訪談紀錄輸出 DTO

```csharp
public class CustomerRecordOutput
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; CustomerRecordOutput
### Properties

<a name='AlifeApi.BusinessRules.CustomerModels.CustomerRecordOutput.CreatedTime'></a>

## CustomerRecordOutput.CreatedTime Property

此記錄的創建時間

```csharp
public System.DateTime CreatedTime { get; set; }
```

#### Property Value
[System.DateTime](https://docs.microsoft.com/en-us/dotnet/api/System.DateTime 'System.DateTime')

<a name='AlifeApi.BusinessRules.CustomerModels.CustomerRecordOutput.CreateUserInfoId'></a>

## CustomerRecordOutput.CreateUserInfoId Property

此記錄創建人員的 UserInfo ID

```csharp
public string CreateUserInfoId { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.CustomerModels.CustomerRecordOutput.CustomerLevel'></a>

## CustomerRecordOutput.CustomerLevel Property

客戶分級或標籤

```csharp
public string CustomerLevel { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.CustomerModels.CustomerRecordOutput.CustomerRecordId'></a>

## CustomerRecordOutput.CustomerRecordId Property

記錄唯一識別碼

```csharp
public int CustomerRecordId { get; set; }
```

#### Property Value
[System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')

<a name='AlifeApi.BusinessRules.CustomerModels.CustomerRecordOutput.HandledBy'></a>

## CustomerRecordOutput.HandledBy Property

負責此次互動的人員資訊

```csharp
public string HandledBy { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.CustomerModels.CustomerRecordOutput.Notes'></a>

## CustomerRecordOutput.Notes Property

詳細的記錄內容

```csharp
public string Notes { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.CustomerModels.CustomerRecordOutput.RecordedAt'></a>

## CustomerRecordOutput.RecordedAt Property

互動發生的實際時間或記錄時間

```csharp
public System.DateTime RecordedAt { get; set; }
```

#### Property Value
[System.DateTime](https://docs.microsoft.com/en-us/dotnet/api/System.DateTime 'System.DateTime')

<a name='AlifeApi.BusinessRules.CustomerModels.CustomerRecordOutput.RecordType'></a>

## CustomerRecordOutput.RecordType Property

互動或記錄的類型

```csharp
public string RecordType { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')