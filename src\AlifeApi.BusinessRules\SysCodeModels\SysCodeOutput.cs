﻿using System.Text.Json.Serialization;

namespace AlifeApi.BusinessRules.SysCodeModels
{
    /// <summary>
    /// 單層 SysCode
    /// </summary>
    /// <remarks>
    /// 這裡的回傳值又部分變成小駝峰...
    /// </remarks>
    public class SysCodeOutput
    {
        /// <summary>
        /// 值
        /// </summary>
        [JsonPropertyName("value")]
        public string Value { get; set; }


        /// <summary>
        /// 選項
        /// </summary>
        [JsonPropertyName("label")]
        public string Label { get; set; }

        /// <summary>
        /// 預設的排序
        /// </summary>
        [JsonPropertyName("Sort")]
        public short Order { get; set; }

        /// <summary>
        /// 是否啟用
        /// </summary>
        [JsonPropertyName("IsActive")]
        public bool IsActive { get; set; }
    }
}
