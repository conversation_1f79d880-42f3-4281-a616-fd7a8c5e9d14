#### [AlifeApi.DataAccess](index.md 'index')
### [AlifeApi.DataAccess.ProjectContext](AlifeApi.DataAccess.ProjectContext.md 'AlifeApi.DataAccess.ProjectContext')

## UserGradePermission Class

```csharp
public class UserGradePermission
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; UserGradePermission
### Properties

<a name='AlifeApi.DataAccess.ProjectContext.UserGradePermission.FuncId'></a>

## UserGradePermission.FuncId Property

系統項目識別編號

```csharp
public string FuncId { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContext.UserGradePermission.GradeId'></a>

## UserGradePermission.GradeId Property

職稱識別編號

```csharp
public string GradeId { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContext.UserGradePermission.System'></a>

## UserGradePermission.System Property

系統名稱

```csharp
public string System { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')