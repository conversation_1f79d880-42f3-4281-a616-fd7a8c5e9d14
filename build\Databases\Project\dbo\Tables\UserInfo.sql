﻿CREATE TABLE [dbo].[UserInfo] (
    [Id]               VARCHAR (15)  NOT NULL,
    [Name]             NVARCHAR (15) NOT NULL,
    [Pw]               VARCHAR (255) NOT NULL,
    [IdNo]             VARCHAR (15)  NOT NULL,
    [GradeCode]        VARCHAR (20)  NULL,
    [DeptId]           VARCHAR (20)  NULL,
    [Email]            VARCHAR (254) NOT NULL,
    [IsEnabled]        BIT           NOT NULL,
    [IsDisabled]       BIT           CONSTRAINT [DF_UserInfo_IsDisabled] DEFAULT ((0)) NOT NULL,
    [EnabledTtime]     DATETIME      NULL,
    [LastLoginIP]      VARCHAR (50)  NOT NULL,
    [LastLoginTime]    DATETIME      NULL,
    [LastLogoutTime]   DATETIME      NULL,
    [LoginFailedCount] SMALLINT      NOT NULL,
    [CreatedUserId]    VARCHAR (15)  NOT NULL,
    [CreatedTime]      DATETIME      NOT NULL,
    [UpdatedUserId]    VARCHAR (15)  NOT NULL,
    [UpdatedTime]      DATETIME      NOT NULL,
    CONSTRAINT [PK_UserInfo] PRIMARY KEY CLUSTERED ([Id] ASC),
    CONSTRAINT [FK_UserInfo_UserDept] FOREIGN KEY ([DeptId]) REFERENCES [dbo].[UserDept] ([Id]),
    CONSTRAINT [FK_UserInfo_UserGrade] FOREIGN KEY ([GradeCode]) REFERENCES [dbo].[UserGrade] ([Id])
);




GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'人員基本資料', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'UserInfo';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'員工編號', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'UserInfo', @level2type = N'COLUMN', @level2name = N'Id';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'人員姓名', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'UserInfo', @level2type = N'COLUMN', @level2name = N'Name';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'密碼', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'UserInfo', @level2type = N'COLUMN', @level2name = N'Pw';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'身分證字號', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'UserInfo', @level2type = N'COLUMN', @level2name = N'IdNo';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'職位', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'UserInfo', @level2type = N'COLUMN', @level2name = N'GradeCode';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'部門', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'UserInfo', @level2type = N'COLUMN', @level2name = N'DeptId';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'電子郵件', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'UserInfo', @level2type = N'COLUMN', @level2name = N'Email';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'是否啟用', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'UserInfo', @level2type = N'COLUMN', @level2name = N'IsEnabled';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'啟用時間', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'UserInfo', @level2type = N'COLUMN', @level2name = N'EnabledTtime';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'最後一次登入IP', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'UserInfo', @level2type = N'COLUMN', @level2name = N'LastLoginIP';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'最後登入時間', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'UserInfo', @level2type = N'COLUMN', @level2name = N'LastLoginTime';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'最後登出時間', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'UserInfo', @level2type = N'COLUMN', @level2name = N'LastLogoutTime';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'登入失敗次數加總', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'UserInfo', @level2type = N'COLUMN', @level2name = N'LoginFailedCount';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'建立者', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'UserInfo', @level2type = N'COLUMN', @level2name = N'CreatedUserId';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'建立時間', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'UserInfo', @level2type = N'COLUMN', @level2name = N'CreatedTime';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'更新者', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'UserInfo', @level2type = N'COLUMN', @level2name = N'UpdatedUserId';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'更新時間', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'UserInfo', @level2type = N'COLUMN', @level2name = N'UpdatedTime';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'是否刪除', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'UserInfo', @level2type = N'COLUMN', @level2name = N'IsDisabled';

