#### [AlifeApi.DataAccess](index.md 'index')
### [AlifeApi.DataAccess.ProjectContext](AlifeApi.DataAccess.ProjectContext.md 'AlifeApi.DataAccess.ProjectContext')

## SysProblemReport Class

平台問題回報資料

```csharp
public class SysProblemReport
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; SysProblemReport
### Properties

<a name='AlifeApi.DataAccess.ProjectContext.SysProblemReport.CreatedTime'></a>

## SysProblemReport.CreatedTime Property

建立時間

```csharp
public System.DateTime CreatedTime { get; set; }
```

#### Property Value
[System.DateTime](https://docs.microsoft.com/en-us/dotnet/api/System.DateTime 'System.DateTime')

<a name='AlifeApi.DataAccess.ProjectContext.SysProblemReport.CreatedUserId'></a>

## SysProblemReport.CreatedUserId Property

建立者

```csharp
public string CreatedUserId { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContext.SysProblemReport.DeptId'></a>

## SysProblemReport.DeptId Property

單位代碼

```csharp
public string DeptId { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContext.SysProblemReport.Id'></a>

## SysProblemReport.Id Property

流水號

```csharp
public long Id { get; set; }
```

#### Property Value
[System.Int64](https://docs.microsoft.com/en-us/dotnet/api/System.Int64 'System.Int64')

<a name='AlifeApi.DataAccess.ProjectContext.SysProblemReport.Menu'></a>

## SysProblemReport.Menu Property

功能頁籤

```csharp
public string Menu { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContext.SysProblemReport.ProblemContent'></a>

## SysProblemReport.ProblemContent Property

問題內容

```csharp
public string ProblemContent { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContext.SysProblemReport.ProblemType'></a>

## SysProblemReport.ProblemType Property

問題類型

```csharp
public string ProblemType { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContext.SysProblemReport.ProcessStatus'></a>

## SysProblemReport.ProcessStatus Property

處理狀態

```csharp
public string ProcessStatus { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContext.SysProblemReport.ReplyContent'></a>

## SysProblemReport.ReplyContent Property

處理回報

```csharp
public string ReplyContent { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContext.SysProblemReport.ReplyUserId'></a>

## SysProblemReport.ReplyUserId Property

處理人員

```csharp
public string ReplyUserId { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContext.SysProblemReport.Subject'></a>

## SysProblemReport.Subject Property

問題主旨

```csharp
public string Subject { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContext.SysProblemReport.UpdatedTime'></a>

## SysProblemReport.UpdatedTime Property

更新時間

```csharp
public System.DateTime UpdatedTime { get; set; }
```

#### Property Value
[System.DateTime](https://docs.microsoft.com/en-us/dotnet/api/System.DateTime 'System.DateTime')

<a name='AlifeApi.DataAccess.ProjectContext.SysProblemReport.UpdatedUserId'></a>

## SysProblemReport.UpdatedUserId Property

更新者

```csharp
public string UpdatedUserId { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')