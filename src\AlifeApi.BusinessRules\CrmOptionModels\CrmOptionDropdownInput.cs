using System.ComponentModel.DataAnnotations;

namespace AlifeApi.BusinessRules.CrmOptionModels
{
    /// <summary>
    /// CRM選項下拉選單查詢輸入模型
    /// </summary>
    public class CrmOptionDropdownInput
    {
        /// <summary>
        /// 案場代碼 (必填)
        /// </summary>
        [Required(ErrorMessage = "缺少案場代碼")]
        public string SiteCode { get; set; } = null!;

        /// <summary>
        /// CRM選項類型ID (必填)
        /// </summary>
        [Required(ErrorMessage = "缺少CRM選項類型ID")]
        public long CrmOptionTypeId { get; set; }

        /// <summary>
        /// 是否只取得啟用的選項 (預設為true)
        /// </summary>
        public bool OnlyActive { get; set; } = true;
    }
}
