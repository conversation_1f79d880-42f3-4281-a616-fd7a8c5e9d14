﻿using AlifeApi.BusinessRules.Infrastructure;
using AlifeApi.DataAccess.ProjectContext;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Internal;

//namespace AlifeApi.BusinessRules.RoleGroupModels
//{
//    /// <summary>
//    /// 角色權限商業邏輯
//    /// </summary>
//    public class RoleGroupService : ServiceBase<ProjectContext>
//    {
//        public RoleGroupService(IServiceProvider serviceProvider, ProjectContext dbContext) : base(serviceProvider, dbContext)
//        {
//        }

//        /// <summary>
//        /// 新增角色權限
//        /// </summary>
//        /// <param name="input">The input.</param>
//        /// <exception cref="ArgumentNullException">input</exception>
//        public async Task CreateRoleGroupAsync(RoleGroupCreateInput input)
//        {
//            ArgumentNullException.ThrowIfNull(input, nameof(input));

//            SysRoleGroup role = new()
//            {
//                System = CurrentUser.System,
//                RoleGroupId = Guid.NewGuid().ToString(),
//                Name = input.Name,
//                CreatedUserId = CurrentUser.UserId,
//                CreatedTime = DateTime.Now,
//                UpdatedUserId = CurrentUser.UserId,
//                UpdatedTime = DateTime.Now,
//            };

//            Db.SysRoleGroup.Add(role);

//            if (!string.IsNullOrEmpty(input.Permission))
//            {
//                foreach (string funcId in input.FuncIds)
//                {
//                    Db.SysRoleGroupPermission.Add(new SysRoleGroupPermission
//                    {
//                        System = CurrentUser.System,
//                        RoleGroupId = role.RoleGroupId,
//                        FuncId = funcId
//                    });
//                }
//            }

//            await Db.SaveChangesAsync();
//        }

//        /// <summary>
//        /// 修改角色權限功能
//        /// </summary>
//        /// <param name="input">The input.</param>
//        /// <exception cref="ArgumentNullException">input</exception>
//        public async Task UpdateRoleGroupPermissionAsync(RoleGroupPermissionUpdateInput input)
//        {
//            ArgumentNullException.ThrowIfNull(input, nameof(input));

//            if (input.FuncIds.Any())
//            {
//                List<SysRoleGroupPermission> permissions = await Db.SysRoleGroupPermission
//                    .Where(x => x.System == CurrentUser.System && x.RoleGroupId == input.Id)
//                    .ToListAsync();

//                foreach (string funcId in input.FuncIds.Distinct()
//                    .Where(x => !permissions.Any(y => y.FuncId == x)))
//                {
//                    Db.SysRoleGroupPermission.Add(new SysRoleGroupPermission
//                    {
//                        System = CurrentUser.System,
//                        RoleGroupId = input.Id,
//                        FuncId = funcId
//                    });
//                }

//                Db.SysRoleGroupPermission.RemoveRange(permissions.Where(x => input.FuncIds.Any(y => y == x.FuncId)));
//            }

//            SysRoleGroup role = await Db.SysRoleGroup
//                .SingleAsync(x => x.System == CurrentUser.System && x.RoleGroupId == input.Id);

//            role.Name = input.Name;
//            role.UpdatedUserId = CurrentUser.UserId;
//            role.UpdatedTime = DateTime.Now;

//            await Db.SaveChangesAsync();
//        }

//        /// <summary>
//        /// 修改角色權限使用者
//        /// </summary>
//        /// <param name="input">The input.</param>
//        /// <exception cref="ArgumentNullException">input</exception>
//        public async Task UpdateRoleGroupUserAsync(RoleGroupUserUpdateInput input)
//        {
//            ArgumentNullException.ThrowIfNull(input, nameof(input));

//            if (input.UserIds.Any())
//            {
//                List<SysRoleGroupUser> roleUsers = await Db.SysRoleGroupUser
//                    .Where(x => x.System == CurrentUser.System && x.RoleGroupId == input.Id)
//                    .ToListAsync();

//                foreach (string userId in input.UserIds.Distinct()
//                    .Where(x => !roleUsers.Any(y => y.UserId == x)))
//                {
//                    Db.SysRoleGroupUser.Add(new SysRoleGroupUser
//                    {
//                        System = CurrentUser.System,
//                        RoleGroupId = input.Id,
//                        UserId = userId
//                    });
//                }

//                Db.SysRoleGroupUser.RemoveRange(roleUsers.Where(x => input.UserIds.Any(y => y == x.UserId)));
//            }

//            SysRoleGroup role = await Db.SysRoleGroup
//                .SingleAsync(x => x.System == CurrentUser.System && x.RoleGroupId == input.Id);
//            role.UpdatedUserId = CurrentUser.UserId;
//            role.UpdatedTime = DateTime.Now;

//            await Db.SaveChangesAsync();
//        }

//        /// <summary>
//        /// 刪除角色
//        /// </summary>
//        /// <param name="input">The input.</param>
//        /// <exception cref="ArgumentNullException">input</exception>
//        public async Task DeleteRoleGroupAsync(RoleGroupDeleteInput input)
//        {
//            ArgumentNullException.ThrowIfNull(input, nameof(input));

//            SysRoleGroup role = await Db.SysRoleGroup
//                .SingleAsync(x => x.System == CurrentUser.System && x.RoleGroupId == input.Id);

//            Db.SysRoleGroup.Remove(role);

//            await Db.SaveChangesAsync();
//        }

//        /// <summary>
//        /// 取得選單樹
//        /// </summary>
//        /// <returns>選單樹</returns>
//        public async Task<IEnumerable<MenuTreeOutput>> GetMenuTreesAsync(IEnumerable<string> userFuncIds = null, string rootMenu = "")
//        {
//            List<SysMenuFunc> allFuncs = await Db.SysMenuFunc.ToListAsync();

//            return GetMenuTreesInternal(allFuncs, userFuncIds, rootMenu);
//        }

//        private static IEnumerable<MenuTreeOutput> GetMenuTreesInternal(
//            IEnumerable<SysMenuFunc> allFuncs, IEnumerable<string> userFuncIds = null, string rootMenu = "")
//        {
//            Dictionary<string, List<SysMenuFunc>> funcMap = allFuncs
//                .GroupBy(m => m.ParentFuncId)
//                .ToDictionary(g => g.Key, g => g.ToList());

//            return GetMenuTreesInternal(funcMap, userFuncIds, rootMenu);
//        }

//        private static IEnumerable<MenuTreeOutput> GetMenuTreesInternal(
//            Dictionary<string, List<SysMenuFunc>> funcMap, IEnumerable<string> userFuncIds, string currentFuncId)
//        {
//            if (!funcMap.TryGetValue(currentFuncId, out List<SysMenuFunc> currentLevelMenus))
//            {
//                return Enumerable.Empty<MenuTreeOutput>();
//            }

//            return currentLevelMenus.OrderBy(x => x.FuncOrder)
//                .Select(x =>
//                {
//                    IEnumerable<MenuTreeOutput> childrens =
//                        GetMenuTreesInternal(funcMap, userFuncIds, x.FuncId);
//                    return new MenuTreeOutput
//                    {
//                        Id = x.FuncId,
//                        Name = x.FuncName,
//                        Childrens = childrens,
//                        Selected = userFuncIds is null
//                            || userFuncIds.Any(y => y == x.FuncId) || childrens.Any(y => y.Selected),
//                    };
//                });
//        }

//        /// <summary>
//        /// 取得角色權限
//        /// </summary>
//        /// <param name="input">The input.</param>
//        /// <returns>角色權限</returns>
//        /// <exception cref="ArgumentNullException">input</exception>
//        public async Task<PagedListOutput<RoleGroupOutput>> GetRoleGroupListAsync(RoleGroupListGetInput input)
//        {
//            ArgumentNullException.ThrowIfNull(input, nameof(input));
//            List<SysMenuFunc> allFuncs = await Db.SysMenuFunc.ToListAsync();

//            PagedListOutput<RoleGroupOutput> pagedList = await Db.SysRoleGroup
//                .LeftJoin(Db.UserInfo, x => x.CreatedUserId, x => x.Id, (r, cu) => new { Role = r, CreatedUserName = cu.Name })
//                .LeftJoin(Db.UserInfo, x => x.Role.UpdatedUserId, x => x.Id, (r, uu) => new { r.Role, r.CreatedUserName, UpdatedUserName = uu.Name })
//                .Select(x => new RoleGroupOutput
//                {
//                    RoleGroupId = x.Role.RoleGroupId,
//                    Name = x.Role.Name,
//                    CreatedTime = x.Role.CreatedTime,
//                    CreatedUserId = x.Role.CreatedUserId,
//                    CreatedUserName = x.CreatedUserName,
//                    UpdatedTime = x.Role.UpdatedTime,
//                    UpdatedUserId = x.Role.UpdatedUserId,
//                    UpdatedUserName = x.CreatedUserName,
//                    FuncIds = x.Role.SysRoleGroupPermission.Select(x => x.FuncId),
//                    UserIds = x.Role.SysRoleGroupUser.Select(x => x.UserId)
//                })
//                .ToPagedListOutputAsync(input);

//            pagedList.Details.ToList().ForEach(x =>
//            {
//                x.MenuTrees = GetMenuTreesInternal(allFuncs, x.FuncIds, "");
//            });

//            return pagedList;
//        }

//        /// <summary>
//        /// 取得角色群組下拉選單
//        /// </summary>
//        /// <returns>角色群組下拉選單列表</returns>
//        public async Task<List<RoleGroupDropdownOutput>> GetRoleGroupDropdownListAsync()
//        {
//            return await Db.SysRoleGroup
//                .Where(x => x.System == "Alife")
//                .OrderBy(x => x.Name)
//                .Select(x => new RoleGroupDropdownOutput
//                {
//                    Name = x.Name,
//                    Value = x.RoleGroupId
//                })
//                .ToListAsync();
//        }
//    }
//}
