﻿CREATE TABLE [dbo].[SYS_RoleGroupUser] (
    [System]      VARCHAR (30) NOT NULL,
    [RoleGroupId] VARCHAR (50) NOT NULL,
    [UserId]      VARCHAR (15) NOT NULL,
    CONSTRAINT [PK_SYS_RoleGroupUser] PRIMARY KEY CLUSTERED ([System] ASC, [RoleGroupId] ASC, [UserId] ASC),
    CONSTRAINT [FK_SYS_RoleGroupUser_SYS_RoleGroup] FOREIGN KEY ([System], [RoleGroupId]) REFERENCES [dbo].[SYS_RoleGroup] ([System], [RoleGroupId]) ON DELETE CASCADE,
    CONSTRAINT [FK_SYS_RoleGroupUser_UserInfo] FOREIGN KEY ([UserId]) REFERENCES [dbo].[UserInfo] ([Id])
);


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'系統名稱', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'SYS_RoleGroupUser', @level2type = N'COLUMN', @level2name = N'System';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'角色群組', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'SYS_RoleGroupUser', @level2type = N'COLUMN', @level2name = N'RoleGroupId';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'權限群組人員', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'SYS_RoleGroupUser', @level2type = N'COLUMN', @level2name = N'UserId';

