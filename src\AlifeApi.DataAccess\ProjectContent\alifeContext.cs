﻿using System;
using System.Collections.Generic;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata;

namespace AlifeApi.DataAccess.ProjectContent
{
    public partial class alifeContext : DbContext
    {
        public alifeContext()
        {
        }

        public alifeContext(DbContextOptions<alifeContext> options)
            : base(options)
        {
        }

        public virtual DbSet<Building> Buildings { get; set; }
        public virtual DbSet<Company> Companies { get; set; }
        public virtual DbSet<CrmOptionType> CrmOptionTypes { get; set; }
        public virtual DbSet<Customer> Customers { get; set; }
        public virtual DbSet<CustomerRecord> CustomerRecords { get; set; }
        public virtual DbSet<Department> Departments { get; set; }
        public virtual DbSet<EmailSendLog> EmailSendLogs { get; set; }
        public virtual DbSet<Floor> Floors { get; set; }
        public virtual DbSet<JobTitle> JobTitles { get; set; }
        public virtual DbSet<LargeCategory> LargeCategories { get; set; }
        public virtual DbSet<MediumCategory> MediumCategories { get; set; }
        public virtual DbSet<NotificationSetting> NotificationSettings { get; set; }
        public virtual DbSet<Owner> Owners { get; set; }
        public virtual DbSet<ParkingSpace> ParkingSpaces { get; set; }
        public virtual DbSet<PaymentRecord> PaymentRecords { get; set; }
        public virtual DbSet<ProjectExpense> ProjectExpenses { get; set; }
        public virtual DbSet<PurchaseOrder> PurchaseOrders { get; set; }
        public virtual DbSet<PurchaseOrdersHistory> PurchaseOrdersHistories { get; set; }
        public virtual DbSet<ReviewApprover> ReviewApprovers { get; set; }
        public virtual DbSet<ReviewHistory> ReviewHistories { get; set; }
        public virtual DbSet<ReviewStep> ReviewSteps { get; set; }
        public virtual DbSet<ReviewTask> ReviewTasks { get; set; }
        public virtual DbSet<Site> Sites { get; set; }
        public virtual DbSet<SiteCrmOption> SiteCrmOptions { get; set; }
        public virtual DbSet<SmallCategory> SmallCategories { get; set; }
        public virtual DbSet<Supplier> Suppliers { get; set; }
        public virtual DbSet<SupplierFile> SupplierFiles { get; set; }
        public virtual DbSet<SysCode> SysCodes { get; set; }
        public virtual DbSet<SysMenuFunc> SysMenuFuncs { get; set; }
        public virtual DbSet<SysRoleGroup> SysRoleGroups { get; set; }
        public virtual DbSet<SysRoleGroupPermission> SysRoleGroupPermissions { get; set; }
        public virtual DbSet<SysRoleGroupUser> SysRoleGroupUsers { get; set; }
        public virtual DbSet<SysSystemSetting> SysSystemSettings { get; set; }
        public virtual DbSet<Unit> Units { get; set; }
        public virtual DbSet<UserDepartment> UserDepartments { get; set; }
        public virtual DbSet<UserInfo> UserInfos { get; set; }
        public virtual DbSet<UserJobTitle> UserJobTitles { get; set; }
        public virtual DbSet<UserPasswordHistory> UserPasswordHistories { get; set; }
        public virtual DbSet<UserPasswordResetCode> UserPasswordResetCodes { get; set; }

        protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
        {

        }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            modelBuilder.Entity<Building>(entity =>
            {
                entity.HasComment("儲存案場內每一棟獨立建築的基本資訊。");

                entity.Property(e => e.BuildingId).HasComment("建築物唯一識別碼 (主鍵, 自動遞增)。");

                entity.Property(e => e.BuildingName)
                    .IsRequired()
                    .HasColumnType("character varying")
                    .HasComment("建築物名稱或編號 (例如: \"A棟\", \"帝寶一期\", \"Building 1\")。");

                entity.Property(e => e.BuildingType)
                    .HasColumnType("character varying")
                    .HasComment("建築類型 (例如: \"住宅\", \"商辦\", \"住商混合\" - 建議關聯 SYS_Code)。");

                entity.Property(e => e.CompletionDate).HasComment("預計或實際完工日期。");

                entity.Property(e => e.CreatedTime)
                    .HasColumnType("timestamp without time zone")
                    .HasDefaultValueSql("CURRENT_TIMESTAMP")
                    .HasComment("資料創建時間 (TIMESTAMP WITHOUT TIME ZONE)。");

                entity.Property(e => e.CreatedUserInfoId)
                    .IsRequired()
                    .HasColumnType("character varying")
                    .HasComment("建立者 (參考 UserInfo 表)。");

                entity.Property(e => e.Remarks).HasComment("備註。");

                entity.Property(e => e.SiteCode)
                    .IsRequired()
                    .HasColumnType("character varying")
                    .HasComment("所屬案場編號 (參考 Sites 表)。");

                entity.Property(e => e.TotalAboveGroundFloors).HasComment("該棟地上總樓層數。");

                entity.Property(e => e.TotalBelowGroundFloors).HasComment("該棟地下總樓層數。");

                entity.Property(e => e.UpdatedTime)
                    .HasColumnType("timestamp without time zone")
                    .HasDefaultValueSql("CURRENT_TIMESTAMP")
                    .HasComment("資料最後更新時間 (TIMESTAMP WITHOUT TIME ZONE)。");

                entity.Property(e => e.UpdatedUserInfoId)
                    .IsRequired()
                    .HasColumnType("character varying")
                    .HasComment("更新者 (參考 UserInfo 表)。");
            });

            modelBuilder.Entity<Company>(entity =>
            {
                entity.ToTable("Company");

                entity.HasComment("公司資料表，用於儲存公司基本資訊。");

                entity.Property(e => e.CompanyId)
                    .HasMaxLength(10)
                    .HasComment("公司編號，主鍵，用於唯一識別公司。");

                entity.Property(e => e.CreatedTime)
                    .HasColumnType("timestamp without time zone")
                    .HasComment("創建時間，記錄資料創建的時間。");

                entity.Property(e => e.CreatedUserInfoId)
                    .IsRequired()
                    .HasMaxLength(15)
                    .HasComment("建立者，記錄創建此資料的員工編號。");

                entity.Property(e => e.Name)
                    .IsRequired()
                    .HasMaxLength(100)
                    .HasComment("公司名稱。");

                entity.Property(e => e.UpdatedTime)
                    .HasColumnType("timestamp without time zone")
                    .HasComment("更新時間，記錄資料最後更新的時間。");

                entity.Property(e => e.UpdatedUserInfoId)
                    .IsRequired()
                    .HasMaxLength(15)
                    .HasComment("更新者，記錄最後更新此資料的員工編號。");
            });

            modelBuilder.Entity<CrmOptionType>(entity =>
            {
                entity.HasComment("CRM中可由案場自訂的下拉選單類型");

                entity.HasIndex(e => e.TypeName, "CrmOptionTypes_TypeName_key")
                    .IsUnique();

                entity.Property(e => e.CrmOptionTypeId).HasComment("主鍵，選項類型唯一識別碼");

                entity.Property(e => e.CreateTime)
                    .HasColumnType("timestamp without time zone")
                    .HasDefaultValueSql("now()")
                    .HasComment("創建時間，記錄資料創建的時間。");

                entity.Property(e => e.CreatedUserInfoId)
                    .IsRequired()
                    .HasMaxLength(15)
                    .HasComment("建立者，記錄創建此資料的員工編號。");

                entity.Property(e => e.Description).HasComment("此選項類型的描述，方便後台管理人員理解");

                entity.Property(e => e.TypeName)
                    .IsRequired()
                    .HasMaxLength(100)
                    .HasComment("選項類型名稱 (例如: 需求坪數, 需求格局, 預算範圍)");

                entity.Property(e => e.UpdateTime)
                    .HasColumnType("timestamp without time zone")
                    .HasDefaultValueSql("now()")
                    .HasComment("更新時間，記錄資料最後更新的時間。");

                entity.Property(e => e.UpdatedUserInfoId)
                    .IsRequired()
                    .HasMaxLength(15)
                    .HasComment("更新者，記錄最後更新此資料的員工編號。");
            });

            modelBuilder.Entity<Customer>(entity =>
            {
                entity.ToTable("Customer");

                entity.HasComment("客戶基本資料表");

                entity.HasIndex(e => e.Email, "Customer_Email_key")
                    .IsUnique();

                entity.HasIndex(e => e.CreatedUserInfoId, "idx_customer_createuserid");

                entity.HasIndex(e => e.Email, "idx_customer_email");

                entity.HasIndex(e => e.PhoneNumber, "idx_customer_phonenumber");

                entity.HasIndex(e => e.UpdatedUserInfoId, "idx_customer_updateuserid");

                entity.Property(e => e.CustomerId).HasComment("客戶唯一識別碼 (主鍵)");

                entity.Property(e => e.Address).HasComment("詳細地址");

                entity.Property(e => e.ArchivePath).HasComment("簽名檔在NAS上的存檔路徑");

                entity.Property(e => e.Birthday).HasComment("生日");

                entity.Property(e => e.Budget).HasComment("購房預算範圍或數值 (文字描述或數值)");

                entity.Property(e => e.City).HasComment("居住或感興趣的縣市");

                entity.Property(e => e.CreatedTime)
                    .HasColumnType("timestamp without time zone")
                    .HasDefaultValueSql("CURRENT_TIMESTAMP")
                    .HasComment("資料創建時間");

                entity.Property(e => e.CreatedUserInfoId)
                    .HasMaxLength(15)
                    .HasComment("資料創建人員的 UserInfo ID (VARCHAR(15) 外鍵)");

                entity.Property(e => e.District).HasComment("居住或感興趣的區域");

                entity.Property(e => e.Email)
                    .HasMaxLength(255)
                    .HasComment("電子郵件地址 (唯一)");

                entity.Property(e => e.FloorPreference).HasComment("樓層偏好");

                entity.Property(e => e.Gender).HasComment("性別");

                entity.Property(e => e.HasConsent)
                    .HasDefaultValueSql("false")
                    .HasComment("是否同意個資使用或行銷 (True/False)");

                entity.Property(e => e.LeadSource).HasComment("客戶來源或得知管道");

                entity.Property(e => e.Name).HasComment("客戶姓名");

                entity.Property(e => e.Occupation).HasComment("職業");

                entity.Property(e => e.PhoneNumber)
                    .HasMaxLength(50)
                    .HasComment("聯絡電話");

                entity.Property(e => e.PurchaseConditions).HasComment("其他購屋條件");

                entity.Property(e => e.PurchasePurpose).HasComment("購屋主要用途 (例如: 自住, 投資)");

                entity.Property(e => e.PurchaseTimeline).HasComment("預計購屋時間範圍");

                entity.Property(e => e.RequiredLayout).HasComment("需求格局 (例如: 3房2廳)");

                entity.Property(e => e.RequiredPingArea).HasComment("需求坪數 (文字描述或數值)");

                entity.Property(e => e.RequiredPropertyType).HasComment("需求的房屋類型 (例如: 預售屋, 新成屋)");

                entity.Property(e => e.SiteCode)
                    .HasMaxLength(20)
                    .HasComment("客戶主要關聯的案場號碼");

                entity.Property(e => e.UpdatedTime)
                    .HasColumnType("timestamp without time zone")
                    .HasDefaultValueSql("CURRENT_TIMESTAMP")
                    .HasComment("資料最後更新時間");

                entity.Property(e => e.UpdatedUserInfoId)
                    .HasMaxLength(15)
                    .HasComment("資料最後更新人員的 UserInfo ID (VARCHAR(15) 外鍵)");

                entity.Property(e => e.WebPath).HasComment("簽名檔的IIS訪問路徑 (指向 C:\\alifeImg\\...)");
            });

            modelBuilder.Entity<CustomerRecord>(entity =>
            {
                entity.ToTable("Customer_Record");

                entity.HasComment("客戶互動或訪談記錄表");

                entity.HasIndex(e => e.CreatedUserInfoId, "idx_customer_record_createuserid");

                entity.HasIndex(e => e.CustomerId, "idx_customer_record_customerid");

                entity.HasIndex(e => e.RecordedAt, "idx_customer_record_recordedat");

                entity.HasIndex(e => e.UpdatedUserInfoId, "idx_customer_record_updateuserid");

                entity.Property(e => e.CustomerRecordId).HasComment("記錄唯一識別碼 (主鍵)");

                entity.Property(e => e.CreatedTime)
                    .HasColumnType("timestamp without time zone")
                    .HasDefaultValueSql("CURRENT_TIMESTAMP")
                    .HasComment("此記錄的創建時間");

                entity.Property(e => e.CreatedUserInfoId)
                    .HasMaxLength(15)
                    .HasComment("此記錄創建人員的 UserInfo ID (VARCHAR(15) 外鍵)");

                entity.Property(e => e.CustomerId).HasComment("關聯的客戶ID (外鍵)");

                entity.Property(e => e.CustomerLevel).HasComment("客戶分級或標籤");

                entity.Property(e => e.HandledBy).HasComment("負責此次互動的人員資訊");

                entity.Property(e => e.Notes)
                    .IsRequired()
                    .HasComment("詳細的記錄內容");

                entity.Property(e => e.RecordType).HasComment("互動或記錄的類型");

                entity.Property(e => e.RecordedAt)
                    .HasColumnType("timestamp without time zone")
                    .HasDefaultValueSql("CURRENT_TIMESTAMP")
                    .HasComment("互動發生的實際時間或記錄時間");

                entity.Property(e => e.UpdatedTime)
                    .HasColumnType("timestamp without time zone")
                    .HasDefaultValueSql("CURRENT_TIMESTAMP")
                    .HasComment("此記錄的最後更新時間");

                entity.Property(e => e.UpdatedUserInfoId)
                    .HasMaxLength(15)
                    .HasComment("此記錄最後更新人員的 UserInfo ID (VARCHAR(15) 外鍵)");

                entity.HasOne(d => d.Customer)
                    .WithMany(p => p.CustomerRecords)
                    .HasForeignKey(d => d.CustomerId)
                    .OnDelete(DeleteBehavior.Restrict)
                    .HasConstraintName("fk_customer");
            });

            modelBuilder.Entity<Department>(entity =>
            {
                entity.HasKey(e => new { e.CompanyId, e.DepartmentId })
                    .HasName("Departments_pkey");

                entity.HasComment("部門資料表，用於儲存每個公司的部門資訊。");

                entity.Property(e => e.CompanyId)
                    .HasMaxLength(10)
                    .HasComment("公司編號，主鍵的一部分，對應 Company 表的 CompanyId。");

                entity.Property(e => e.DepartmentId)
                    .HasMaxLength(50)
                    .HasComment("部門編號，主鍵的一部分，用於唯一識別部門。");

                entity.Property(e => e.CreatedTime)
                    .HasColumnType("timestamp without time zone")
                    .HasComment("創建時間，記錄資料創建的時間。");

                entity.Property(e => e.CreatedUserInfoId)
                    .IsRequired()
                    .HasMaxLength(15)
                    .HasComment("建立者，記錄創建此資料的員工編號。");

                entity.Property(e => e.Name)
                    .IsRequired()
                    .HasMaxLength(50)
                    .HasComment("部門名稱。");

                entity.Property(e => e.UpdatedTime)
                    .HasColumnType("timestamp without time zone")
                    .HasComment("更新時間，記錄資料最後更新的時間。");

                entity.Property(e => e.UpdatedUserInfoId)
                    .IsRequired()
                    .HasMaxLength(15)
                    .HasComment("更新者，記錄最後更新此資料的員工編號。");

                entity.HasOne(d => d.Company)
                    .WithMany(p => p.Departments)
                    .HasForeignKey(d => d.CompanyId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_Departments_Company");
            });

            modelBuilder.Entity<EmailSendLog>(entity =>
            {
                entity.ToTable("EmailSendLog");

                entity.HasComment("電子郵件發送記錄表，用於儲存系統發送電子郵件的記錄資訊。");

                entity.Property(e => e.EmailSendLogId)
                    .ValueGeneratedNever()
                    .HasComment("流水號，主鍵，用於唯一識別郵件發送記錄。");

                entity.Property(e => e.AttachmentFileName)
                    .HasMaxLength(200)
                    .HasComment("附件的檔案名稱。");

                entity.Property(e => e.AttachmentFileSize).HasComment("附件檔案大小，單位為位元組。");

                entity.Property(e => e.Body).HasComment("郵件內容。");

                entity.Property(e => e.CreatedTime)
                    .HasColumnType("timestamp without time zone")
                    .HasComment("創建時間，記錄郵件記錄創建的時間。");

                entity.Property(e => e.CreatedUserInfoId)
                    .IsRequired()
                    .HasMaxLength(15)
                    .HasComment("建立者，記錄創建此記錄的員工編號或系統帳號。");

                entity.Property(e => e.ErrorMessage).HasComment("若發送失敗，記錄錯誤訊息。");

                entity.Property(e => e.IsSuccess).HasComment("是否發送成功，true 表示成功，false 表示失敗。");

                entity.Property(e => e.RecipientEmail)
                    .IsRequired()
                    .HasMaxLength(100)
                    .HasComment("收件者電子郵件地址。");

                entity.Property(e => e.RecipientName)
                    .HasMaxLength(50)
                    .HasComment("收件者姓名。");

                entity.Property(e => e.ReportEndDate)
                    .HasColumnType("timestamp without time zone")
                    .HasComment("報表統計的結束日期。");

                entity.Property(e => e.ReportStartDate)
                    .HasColumnType("timestamp without time zone")
                    .HasComment("報表統計的開始日期。");

                entity.Property(e => e.SentDate)
                    .HasColumnType("timestamp without time zone")
                    .HasComment("郵件發送時間。");

                entity.Property(e => e.Subject)
                    .IsRequired()
                    .HasMaxLength(200)
                    .HasComment("郵件主題。");
            });

            modelBuilder.Entity<Floor>(entity =>
            {
                entity.HasComment("儲存建築物內每個樓層的資訊，包含住宅/商業樓層和停車場樓層。");

                entity.HasIndex(e => e.BuildingId, "IX_Floors_BuildingId");

                entity.HasIndex(e => e.FloorLevel, "IX_Floors_FloorLevel");

                entity.HasIndex(e => e.FloorType, "IX_Floors_FloorType");

                entity.HasIndex(e => e.SiteCode, "IX_Floors_SiteCode");

                entity.Property(e => e.FloorId).HasComment("樓層唯一識別碼 (主鍵, 自動遞增)。");

                entity.Property(e => e.BuildingId).HasComment("所屬建築物識別碼 (參考 Buildings 表)。");

                entity.Property(e => e.CreatedTime)
                    .HasColumnType("timestamp without time zone")
                    .HasDefaultValueSql("CURRENT_TIMESTAMP")
                    .HasComment("資料創建時間 (TIMESTAMP WITHOUT TIME ZONE)。");

                entity.Property(e => e.CreatedUserInfoId)
                    .IsRequired()
                    .HasColumnType("character varying")
                    .HasComment("建立者 (參考 UserInfo 表)。");

                entity.Property(e => e.FloorHeight)
                    .HasPrecision(5, 2)
                    .HasComment("樓層高度 (米)。");

                entity.Property(e => e.FloorLabel)
                    .IsRequired()
                    .HasColumnType("character varying")
                    .HasComment("樓層的顯示標示 (例如: \"15F\", \"1F\", \"店面\", \"P1F\", \"B1\")。");

                entity.Property(e => e.FloorLevel).HasComment("樓層的數值，用於排序 (例如: 15, 1, 0, -1, -2)。");

                entity.Property(e => e.FloorType)
                    .IsRequired()
                    .HasColumnType("character varying")
                    .HasComment("樓層主要用途，**用於區分住宅/商業樓層與停車場樓層** (例如: \"住宅\", \"商業\", \"停車場\" - 建議關聯 SYS_Code)。");

                entity.Property(e => e.Remarks).HasComment("備註。");

                entity.Property(e => e.SiteCode)
                    .IsRequired()
                    .HasColumnType("character varying")
                    .HasComment("所屬案場編號 (參考 Sites 表) - 方便查詢用。");

                entity.Property(e => e.UpdatedTime)
                    .HasColumnType("timestamp without time zone")
                    .HasDefaultValueSql("CURRENT_TIMESTAMP")
                    .HasComment("資料最後更新時間 (TIMESTAMP WITHOUT TIME ZONE)。");

                entity.Property(e => e.UpdatedUserInfoId)
                    .IsRequired()
                    .HasColumnType("character varying")
                    .HasComment("更新者 (參考 UserInfo 表)。");
            });

            modelBuilder.Entity<JobTitle>(entity =>
            {
                entity.HasKey(e => new { e.DepartmentId, e.JobTitleId })
                    .HasName("JobTitles_pkey");

                entity.HasComment("職位資料表，用於儲存每個公司的職位資訊。");

                entity.Property(e => e.DepartmentId)
                    .HasMaxLength(10)
                    .HasComment("部門編號，主鍵的一部分，對應 Departments 表的 DepartmentId。");

                entity.Property(e => e.JobTitleId)
                    .HasMaxLength(50)
                    .HasComment("職位編號，主鍵的一部分，用於唯一識別職位。");

                entity.Property(e => e.CreatedTime)
                    .HasColumnType("timestamp without time zone")
                    .HasComment("創建時間，記錄資料創建的時間。");

                entity.Property(e => e.CreatedUserInfoId)
                    .IsRequired()
                    .HasMaxLength(15)
                    .HasComment("建立者，記錄創建此資料的員工編號。");

                entity.Property(e => e.Name)
                    .IsRequired()
                    .HasMaxLength(50)
                    .HasComment("職位名稱。");

                entity.Property(e => e.UpdatedTime)
                    .HasColumnType("timestamp without time zone")
                    .HasComment("更新時間，記錄資料最後更新的時間。");

                entity.Property(e => e.UpdatedUserInfoId)
                    .IsRequired()
                    .HasMaxLength(15)
                    .HasComment("更新者，記錄最後更新此資料的員工編號。");
            });

            modelBuilder.Entity<LargeCategory>(entity =>
            {
                entity.HasComment("商品大分類資料表");

                entity.Property(e => e.LargeCategoryId).HasComment("主鍵，大分類唯一識別碼");

                entity.Property(e => e.CreateTime)
                    .HasColumnType("timestamp(6) without time zone")
                    .HasDefaultValueSql("now()")
                    .HasComment("創建時間，記錄資料創建的時間。");

                entity.Property(e => e.CreatedUserInfoId)
                    .IsRequired()
                    .HasMaxLength(15)
                    .HasComment("建立者，記錄創建此資料的員工編號。");

                entity.Property(e => e.Description).HasComment("大分類的詳細文字描述");

                entity.Property(e => e.IsActive)
                    .IsRequired()
                    .HasDefaultValueSql("true")
                    .HasComment("是否啟用此分類 (TRUE: 啟用, FALSE: 停用)");

                entity.Property(e => e.Name)
                    .IsRequired()
                    .HasMaxLength(255)
                    .HasComment("大分類的顯示名稱");

                entity.Property(e => e.SortOrder).HasComment("排序欄位，數字越小越前面");

                entity.Property(e => e.UpdateTime)
                    .HasColumnType("timestamp(6) without time zone")
                    .HasDefaultValueSql("now()")
                    .HasComment("更新時間，記錄資料最後更新的時間。");

                entity.Property(e => e.UpdatedUserInfoId)
                    .IsRequired()
                    .HasMaxLength(15)
                    .HasComment("更新者，記錄最後更新此資料的員工編號。");
            });

            modelBuilder.Entity<MediumCategory>(entity =>
            {
                entity.HasComment("商品中分類資料表");

                entity.HasIndex(e => e.LargeCategoryId, "IdxMediumCategoriesOnLargeCategoryId");

                entity.Property(e => e.MediumCategoryId).HasComment("主鍵，中分類唯一識別碼");

                entity.Property(e => e.CreateTime)
                    .HasColumnType("timestamp(6) without time zone")
                    .HasDefaultValueSql("now()")
                    .HasComment("創建時間，記錄資料創建的時間。");

                entity.Property(e => e.CreatedUserInfoId)
                    .IsRequired()
                    .HasMaxLength(15)
                    .HasComment("建立者，記錄創建此資料的員工編號。");

                entity.Property(e => e.Description).HasComment("中分類的詳細文字描述");

                entity.Property(e => e.IsActive)
                    .IsRequired()
                    .HasDefaultValueSql("true")
                    .HasComment("是否啟用此分類 (TRUE: 啟用, FALSE: 停用)");

                entity.Property(e => e.LargeCategoryId).HasComment("所屬大分類的ID，需由應用程式確保其有效性");

                entity.Property(e => e.Name)
                    .IsRequired()
                    .HasMaxLength(255)
                    .HasComment("中分類的顯示名稱");

                entity.Property(e => e.SortOrder).HasComment("在同一個大分類下的排序順序，數字越小越前面");

                entity.Property(e => e.UpdateTime)
                    .HasColumnType("timestamp(6) without time zone")
                    .HasDefaultValueSql("now()")
                    .HasComment("更新時間，記錄資料最後更新的時間。");

                entity.Property(e => e.UpdatedUserInfoId)
                    .IsRequired()
                    .HasMaxLength(15)
                    .HasComment("更新者，記錄最後更新此資料的員工編號。");
            });

            modelBuilder.Entity<NotificationSetting>(entity =>
            {
                entity.HasKey(e => e.SettingId)
                    .HasName("NotificationSettings_pkey");

                entity.HasComment("審核通知設定表，用於儲存每個審核步驟的通知設定資訊。");

                entity.Property(e => e.SettingId).HasComment("設定流水號，主鍵，自動遞增，用於唯一識別通知設定記錄。");

                entity.Property(e => e.Enabled)
                    .HasDefaultValueSql("true")
                    .HasComment("是否啟用，true 表示啟用，false 表示停用，預設為 true。");

                entity.Property(e => e.NotifyType)
                    .HasMaxLength(20)
                    .HasComment("通知類型，可選值為 system（系統通知）、email（電子郵件）、sms（簡訊）。");

                entity.Property(e => e.StepId).HasComment("步驟流水號，對應 ReviewSteps 表的 StepId。");

                entity.HasOne(d => d.Step)
                    .WithMany(p => p.NotificationSettings)
                    .HasForeignKey(d => d.StepId)
                    .HasConstraintName("FK_NotificationSettings_ReviewSteps");
            });

            modelBuilder.Entity<Owner>(entity =>
            {
                entity.ToTable("Owner");

                entity.HasComment("業主基本資料表");

                entity.Property(e => e.OwnerId).HasComment("業主唯一識別碼 (自動增長)");

                entity.Property(e => e.CompanyAddress).HasComment("公司登記地址");

                entity.Property(e => e.CompanyName).HasComment("公司名稱");

                entity.Property(e => e.CompanyPhone)
                    .HasMaxLength(50)
                    .HasComment("公司登記電話");

                entity.Property(e => e.ContactPerson).HasComment("主要聯絡窗口人員姓名");

                entity.Property(e => e.ContactPhone1)
                    .HasMaxLength(50)
                    .HasComment("主要聯絡電話");

                entity.Property(e => e.ContactPhone2)
                    .HasMaxLength(50)
                    .HasComment("次要聯絡電話 (備用)");

                entity.Property(e => e.Email).HasComment("聯絡電子郵件地址");

                entity.Property(e => e.IdentificationNumber)
                    .HasMaxLength(50)
                    .HasComment("證號 (依人別決定是 統一編號 或 身分證ID)");

                entity.Property(e => e.MailingAddress).HasComment("郵件通訊地址 (若與公司地址不同)");

                entity.Property(e => e.PersonType)
                    .HasMaxLength(10)
                    .HasComment("人別 (值為 法人 或 自然人)");

                entity.Property(e => e.ResponsiblePerson).HasComment("負責人姓名");
            });

            modelBuilder.Entity<ParkingSpace>(entity =>
            {
                entity.HasComment("停車位基本資訊表 - 僅儲存車位的物理特性和位置資訊，銷售相關資訊由 Units 表管理");

                entity.HasIndex(e => e.BuildingId, "IX_ParkingSpaces_BuildingId");

                entity.HasIndex(e => e.FloorId, "IX_ParkingSpaces_FloorId");

                entity.HasIndex(e => e.SiteCode, "IX_ParkingSpaces_SiteCode");

                entity.HasIndex(e => e.SpaceNumber, "IX_ParkingSpaces_SpaceNumber");

                entity.Property(e => e.ParkingSpaceId).HasComment("停車位唯一識別碼 (主鍵, 自動遞增)。");

                entity.Property(e => e.BuildingId).HasComment("所屬建築物識別碼 (參考 Buildings 表)。");

                entity.Property(e => e.CreatedTime)
                    .HasColumnType("timestamp without time zone")
                    .HasDefaultValueSql("CURRENT_TIMESTAMP")
                    .HasComment("資料創建時間 (TIMESTAMP WITHOUT TIME ZONE)。");

                entity.Property(e => e.CreatedUserInfoId)
                    .IsRequired()
                    .HasColumnType("character varying")
                    .HasComment("建立者 (參考 UserInfo 表)。");

                entity.Property(e => e.Dimensions)
                    .HasColumnType("character varying")
                    .HasComment("車位尺寸 (例如 \"250x550cm\")。");

                entity.Property(e => e.FloorId).HasComment("所在樓層識別碼 (**參考 Floors 表中 FloorType 為 '停車場' 的樓層ID**, e.g., P1F, B1)。");

                entity.Property(e => e.Location)
                    .HasColumnType("character varying")
                    .HasComment("車位詳細位置描述 (例如 \"靠近電梯\", \"角落位置\")");

                entity.Property(e => e.Remarks).HasComment("備註 (例如 \"柱子較多\", \"出入較便利\")");

                entity.Property(e => e.SiteCode)
                    .IsRequired()
                    .HasColumnType("character varying")
                    .HasComment("所屬案場編號 (參考 Sites 表)。");

                entity.Property(e => e.SpaceNumber)
                    .IsRequired()
                    .HasColumnType("character varying")
                    .HasComment("車位編號 (例如 \"A01\", \"B15\", \"50\")");

                entity.Property(e => e.SpaceType)
                    .IsRequired()
                    .HasColumnType("character varying")
                    .HasComment("車位類型 (例如 \"坡道平面\", \"機械\", \"大型\", \"小型\", \"殘障\" - 建議關聯 SYS_Code)");

                entity.Property(e => e.UpdatedTime)
                    .HasColumnType("timestamp without time zone")
                    .HasDefaultValueSql("CURRENT_TIMESTAMP")
                    .HasComment("資料最後更新時間 (TIMESTAMP WITHOUT TIME ZONE)。");

                entity.Property(e => e.UpdatedUserInfoId)
                    .IsRequired()
                    .HasColumnType("character varying")
                    .HasComment("更新者 (參考 UserInfo 表)。");
            });

            modelBuilder.Entity<PaymentRecord>(entity =>
            {
                entity.HasComment("記錄針對某一筆 PurchaseOrder 的分期收款明細。");

                entity.HasIndex(e => e.IsReceived, "IX_PaymentRecords_IsReceived");

                entity.HasIndex(e => e.OrderId, "IX_PaymentRecords_OrderId");

                entity.HasIndex(e => e.PaymentDate, "IX_PaymentRecords_PaymentDate");

                entity.HasIndex(e => e.PaymentType, "IX_PaymentRecords_PaymentType");

                entity.Property(e => e.PaymentRecordId).HasComment("收款記錄唯一識別碼 (主鍵, 自動遞增)。");

                entity.Property(e => e.Amount)
                    .HasPrecision(15)
                    .HasComment("本次收款的金額 (新台幣)。");

                entity.Property(e => e.AttachmentPath)
                    .HasColumnType("character varying")
                    .HasComment("相關檔案 (如收據掃描檔、匯款單) 的儲存路徑。");

                entity.Property(e => e.BankAccountNumber)
                    .HasColumnType("character varying")
                    .HasComment("匯款帳號末五碼或支票帳號。");

                entity.Property(e => e.BankName)
                    .HasColumnType("character varying")
                    .HasComment("匯款銀行或支票開立銀行。");

                entity.Property(e => e.CheckDueDate).HasComment("支票到期日 (如果付款方式是支票)。");

                entity.Property(e => e.CheckNumber)
                    .HasColumnType("character varying")
                    .HasComment("支票號碼 (如果付款方式是支票)。");

                entity.Property(e => e.CreatedTime)
                    .HasColumnType("timestamp without time zone")
                    .HasDefaultValueSql("CURRENT_TIMESTAMP")
                    .HasComment("記錄創建時間 (TIMESTAMP WITHOUT TIME ZONE)。");

                entity.Property(e => e.CreatedUserInfoId)
                    .IsRequired()
                    .HasColumnType("character varying")
                    .HasComment("建立此收款記錄的人員ID (參考 UserInfo 表)。");

                entity.Property(e => e.HandlingFee)
                    .HasPrecision(10)
                    .HasDefaultValueSql("0")
                    .HasComment("本次收款可能產生的手續費 (例如刷卡手續費，可選)。");

                entity.Property(e => e.IsReceived)
                    .HasDefaultValueSql("true")
                    .HasComment("標記此筆款項是否已確認入帳/兌現 (True/False)。");

                entity.Property(e => e.OrderId).HasComment("關聯的訂單 ID (參考 PurchaseOrders 表)。");

                entity.Property(e => e.PaymentDate).HasComment("實際收款的日期 (或預計收款日)。");

                entity.Property(e => e.PaymentMethod)
                    .IsRequired()
                    .HasColumnType("character varying")
                    .HasComment("本次收款的方式 (例如: 現金, 匯款, 支票 - 建議關lungen SYS_Code)。");

                entity.Property(e => e.PaymentType)
                    .IsRequired()
                    .HasColumnType("character varying")
                    .HasComment("本次收款的款項類別 (例如: 定金, 簽約金, 工程款 - 建議關聯 SYS_Code)。");

                entity.Property(e => e.Remarks).HasComment("收款備註 (例如：特定期數說明)。");

                entity.Property(e => e.TransferredToDeveloper)
                    .HasDefaultValueSql("false")
                    .HasComment("該筆款項是否已轉交給建設公司 (True/False，可選)。");

                entity.Property(e => e.UpdatedTime)
                    .HasColumnType("timestamp without time zone")
                    .HasDefaultValueSql("CURRENT_TIMESTAMP")
                    .HasComment("記錄最後更新時間 (TIMESTAMP WITHOUT TIME ZONE)。");

                entity.Property(e => e.UpdatedUserInfoId)
                    .IsRequired()
                    .HasColumnType("character varying")
                    .HasComment("最後更新此收款記錄的人員ID (參考 UserInfo 表)。");
            });

            modelBuilder.Entity<ProjectExpense>(entity =>
            {
                entity.HasComment("專案支出紀錄表，記錄每個案場的各項開銷");

                entity.HasIndex(e => e.ExpenseDate, "IdxProjectExpensesOnExpenseDate");

                entity.HasIndex(e => e.SiteCode, "IdxProjectExpensesOnSiteCode");

                entity.HasIndex(e => e.SmallCategoryId, "IdxProjectExpensesOnSmallCategoryId");

                entity.Property(e => e.ProjectExpenseId).HasComment("主鍵，支出紀錄唯一識別碼");

                entity.Property(e => e.Amount)
                    .HasPrecision(18, 2)
                    .HasComment("支出金額");

                entity.Property(e => e.CreateTime)
                    .HasColumnType("timestamp(6) without time zone")
                    .HasDefaultValueSql("now()")
                    .HasComment("創建時間，記錄資料創建的時間。");

                entity.Property(e => e.CreatedUserInfoId)
                    .IsRequired()
                    .HasMaxLength(15)
                    .HasComment("建立者，記錄創建此資料的員工編號。");

                entity.Property(e => e.Description).HasComment("關於此筆支出的額外文字描述或備註");

                entity.Property(e => e.ExpenseDate).HasComment("支出發生的日期");

                entity.Property(e => e.SiteCode)
                    .IsRequired()
                    .HasMaxLength(50)
                    .HasComment("案場代碼，指明此筆支出屬於哪個案場");

                entity.Property(e => e.SmallCategoryId).HasComment("關聯的小分類ID (需由應用程式確保其有效性)");

                entity.Property(e => e.UpdateTime)
                    .HasColumnType("timestamp(6) without time zone")
                    .HasDefaultValueSql("now()")
                    .HasComment("更新時間，記錄資料最後更新的時間。");

                entity.Property(e => e.UpdatedUserInfoId)
                    .IsRequired()
                    .HasMaxLength(15)
                    .HasComment("更新者，記錄最後更新此資料的員工編號。");
            });

            modelBuilder.Entity<PurchaseOrder>(entity =>
            {
                entity.HasKey(e => e.OrderId)
                    .HasName("PurchaseOrders_pkey");

                entity.HasComment("儲存客戶預定或購買房屋單位與停車位的訂單詳細資訊 (交易記錄)。");

                entity.HasIndex(e => e.CancellationDate, "IX_PurchaseOrders_CancellationDate")
                    .HasFilter("(\"CancellationDate\" IS NOT NULL)");

                entity.HasIndex(e => e.ContractSignedDate, "IX_PurchaseOrders_ContractSignedDate")
                    .HasFilter("(\"ContractSignedDate\" IS NOT NULL)");

                entity.HasIndex(e => e.CustomerId, "IX_PurchaseOrders_CustomerId");

                entity.HasIndex(e => e.DepositFullPaidDate, "IX_PurchaseOrders_DepositFullPaidDate")
                    .HasFilter("(\"DepositFullPaidDate\" IS NOT NULL)");

                entity.HasIndex(e => e.OrderDate, "IX_PurchaseOrders_OrderDate");

                entity.HasIndex(e => e.ReceiveDate, "IX_PurchaseOrders_ReceiveDate")
                    .HasFilter("(\"ReceiveDate\" IS NOT NULL)");

                entity.HasIndex(e => e.RequestDate, "IX_PurchaseOrders_RequestDate")
                    .HasFilter("(\"RequestDate\" IS NOT NULL)");

                entity.HasIndex(e => e.SaleDate, "IX_PurchaseOrders_SaleDate")
                    .HasFilter("(\"SaleDate\" IS NOT NULL)");

                entity.HasIndex(e => e.SalespersonUserInfoId, "IX_PurchaseOrders_SalespersonUserInfoId");

                entity.HasIndex(e => e.SiteCode, "IX_PurchaseOrders_SiteCode");

                entity.HasIndex(e => e.Status, "IX_PurchaseOrders_Status");

                entity.HasIndex(e => e.UnitId, "IX_PurchaseOrders_UnitId");

                entity.HasIndex(e => new { e.UnitId, e.Status }, "IX_PurchaseOrders_UnitId_Status_Dates")
                    .HasFilter("(\"UnitId\" IS NOT NULL)");

                entity.HasIndex(e => e.OrderNumber, "PurchaseOrders_OrderNumber_key")
                    .IsUnique();

                entity.Property(e => e.OrderId).HasComment("訂單唯一識別碼 (主鍵, 自動遞增)。");

                entity.Property(e => e.CancellationDate).HasComment("退訂日期 (客戶退訂或訂單取消的日期)");

                entity.Property(e => e.ConsentToDataUsage).HasComment("是否同意個人資料收集與利用 (True/False)。");

                entity.Property(e => e.ContractSignedDate).HasComment("實際簽約日期 (正式簽約完成的日期) - 狀態：簽");

                entity.Property(e => e.ContractSigningAmount)
                    .HasPrecision(15)
                    .HasComment("簽約金 (新台幣)。");

                entity.Property(e => e.ContractSigningAppointment)
                    .HasColumnType("timestamp without time zone")
                    .HasComment("約定的簽約日期與時間。");

                entity.Property(e => e.CreatedTime)
                    .HasColumnType("timestamp without time zone")
                    .HasDefaultValueSql("CURRENT_TIMESTAMP")
                    .HasComment("訂單記錄創建時間 (TIMESTAMP WITHOUT TIME ZONE)。");

                entity.Property(e => e.CreatedUserInfoId)
                    .IsRequired()
                    .HasColumnType("character varying")
                    .HasComment("建立者 (參考 UserInfo 表)。");

                entity.Property(e => e.CustomerId).HasComment("買受人ID (參考 Customer 表)。");

                entity.Property(e => e.DepositAmount)
                    .HasPrecision(15)
                    .HasComment("定金總額 (新台幣)。");

                entity.Property(e => e.DepositBalanceAmount)
                    .HasPrecision(15)
                    .HasComment("應補足的定金金額 (新台幣)。");

                entity.Property(e => e.DepositDueDate)
                    .HasColumnType("timestamp without time zone")
                    .HasComment("應補足定金的截止日期與時間。");

                entity.Property(e => e.DepositFullPaidDate).HasComment("足訂日期 (定金足額到位的實際日期) - 狀態：足");

                entity.Property(e => e.DepositPaidAmount)
                    .HasPrecision(15)
                    .HasComment("已付定金 (新台幣)。");

                entity.Property(e => e.DepositPayee)
                    .HasColumnType("character varying")
                    .HasComment("定金收款人。");

                entity.Property(e => e.DepositPaymentMethod)
                    .HasColumnType("character varying")
                    .HasComment("定金付款方式 (建議使用 SYS_Code)。");

                entity.Property(e => e.FinalPaymentDate).HasComment("尾款繳清日期 (最後一筆款項收款完成的日期)");

                entity.Property(e => e.HandoverDate).HasComment("交屋日期 (房屋正式交付給客戶的日期)");

                entity.Property(e => e.LandShareArea)
                    .HasPrecision(10, 2)
                    .HasComment("土地持分面積 (坪)。");

                entity.Property(e => e.OrderDate).HasComment("訂單建立日期。");

                entity.Property(e => e.OrderNumber)
                    .HasColumnType("character varying")
                    .HasComment("預定單編號 (可選, 應具備唯一性)。");

                entity.Property(e => e.OrderRemarks).HasComment("訂單備註 (例如：特殊協議)。");

                entity.Property(e => e.ParkingSpacePrice)
                    .HasPrecision(15)
                    .HasComment("本次交易的車位售價 (新台幣，可能是多個車位的總和)。");

                entity.Property(e => e.PriceRegistrationSubmissionDate).HasComment("實價登錄申報日");

                entity.Property(e => e.PropertyPrice)
                    .HasPrecision(15)
                    .HasComment("本次交易的房地售價 (新台幣，不含車位價)。");

                entity.Property(e => e.PurchasedParkingSpaceIds).HasComment("本次訂單購買的停車位 ID 列表，以逗號分隔 (參考 ParkingSpaces 表)。");

                entity.Property(e => e.ReceiveDate).HasComment("領款日期 (已向業主領到費用的日期) - 狀態：領");

                entity.Property(e => e.RequestDate).HasComment("請款日期 (向業主請費用的日期) - 狀態：請");

                entity.Property(e => e.SaleDate).HasComment("實際售出日期 (客戶確認購買意願的日期) - 狀態：售");

                entity.Property(e => e.SaleType)
                    .HasColumnType("character varying")
                    .HasComment("銷售類型 (例如: '委售', '自售' - 建議使用 SYS_Code)。");

                entity.Property(e => e.SalesAgencyEmail)
                    .HasColumnType("character varying")
                    .HasComment("代銷公司 E-mail。");

                entity.Property(e => e.SalesAgencyLandline)
                    .HasColumnType("character varying")
                    .HasComment("代銷公司市話。");

                entity.Property(e => e.SalesAgencyMobile)
                    .HasColumnType("character varying")
                    .HasComment("代銷公司行動電話。");

                entity.Property(e => e.SalesAgencyName)
                    .HasColumnType("character varying")
                    .HasComment("代銷公司名稱。");

                entity.Property(e => e.SalespersonUserInfoId)
                    .IsRequired()
                    .HasColumnType("character varying")
                    .HasComment("執行此訂單的銷售人員ID (參考 UserInfo 表)。");

                entity.Property(e => e.SiteCode)
                    .IsRequired()
                    .HasColumnType("character varying")
                    .HasComment("建案編號 (參考 Sites 表)。");

                entity.Property(e => e.Status)
                    .IsRequired()
                    .HasColumnType("character varying")
                    .HasComment("訂單本身的狀態 (例如: '預訂中', '已轉訂', '已簽約', '已取消', '已作廢' - 建議使用 SYS_Code)。");

                entity.Property(e => e.TotalPrice)
                    .HasPrecision(16)
                    .HasComment("本次交易的總價 (新台幣 = PropertyPrice + ParkingSpacePrice)。");

                entity.Property(e => e.UnitId).HasComment("購買的主要房屋單位 ID (參考 Units 表)，若僅購買車位則此欄位為 Null。");

                entity.Property(e => e.UpdatedTime)
                    .HasColumnType("timestamp without time zone")
                    .HasDefaultValueSql("CURRENT_TIMESTAMP")
                    .HasComment("訂單記錄最後更新時間 (TIMESTAMP WITHOUT TIME ZONE)。");

                entity.Property(e => e.UpdatedUserInfoId)
                    .IsRequired()
                    .HasColumnType("character varying")
                    .HasComment("更新者 (參考 UserInfo 表)。");
            });

            modelBuilder.Entity<PurchaseOrdersHistory>(entity =>
            {
                entity.HasKey(e => e.HistoryId)
                    .HasName("PurchaseOrdersHistory_pkey");

                entity.ToTable("PurchaseOrdersHistory");

                entity.HasComment("買賣預定單歷史記錄表，主要追蹤業務狀態變更（售、足、簽、請、領）");

                entity.HasIndex(e => e.ActionType, "IX_PurchaseOrdersHistory_ActionType");

                entity.HasIndex(e => e.CreatedTime, "IX_PurchaseOrdersHistory_CreatedTime");

                entity.HasIndex(e => e.OrderId, "IX_PurchaseOrdersHistory_OrderId");

                entity.HasIndex(e => new { e.OrderId, e.CreatedTime }, "IX_PurchaseOrdersHistory_OrderId_CreatedTime");

                entity.Property(e => e.HistoryId).HasComment("歷史記錄唯一識別碼 (主鍵, 自動遞增)");

                entity.Property(e => e.ActionType)
                    .IsRequired()
                    .HasMaxLength(50)
                    .HasComment("操作類型 (例如: STATUS_CHANGE, DATE_UPDATE, CREATE, MODIFY)");

                entity.Property(e => e.ContentRecord).HasComment("詳細記錄內容或備註說明");

                entity.Property(e => e.CreatedTime)
                    .HasColumnType("timestamp without time zone")
                    .HasDefaultValueSql("CURRENT_TIMESTAMP")
                    .HasComment("操作執行時間");

                entity.Property(e => e.CreatedUserInfoId)
                    .IsRequired()
                    .HasMaxLength(15)
                    .HasComment("執行此操作的使用者ID");

                entity.Property(e => e.NewStatus)
                    .HasMaxLength(100)
                    .HasComment("變更後的訂單狀態");

                entity.Property(e => e.OldStatus)
                    .HasMaxLength(100)
                    .HasComment("變更前的訂單狀態");

                entity.Property(e => e.OrderId).HasComment("關聯的訂單ID");
            });

            modelBuilder.Entity<ReviewApprover>(entity =>
            {
                entity.HasKey(e => e.ApproverId)
                    .HasName("ReviewApprovers_pkey");

                entity.HasComment("審核人員表，用於儲存每個審核步驟的負責人員資訊。");

                entity.Property(e => e.ApproverId).HasComment("審核人員流水號，主鍵，自動遞增，用於唯一識別審核人員記錄。");

                entity.Property(e => e.IsRequired)
                    .HasDefaultValueSql("true")
                    .HasComment("是否為必要審核人員，true 表示必須審核，false 表示可選，預設為 true。");

                entity.Property(e => e.StepId).HasComment("步驟流水號，對應 ReviewSteps 表的 StepId。");

                entity.Property(e => e.UserInfoId)
                    .IsRequired()
                    .HasMaxLength(15)
                    .HasComment("使用者編號，對應 UserInfo 表的 UserInfoId。");

                entity.HasOne(d => d.Step)
                    .WithMany(p => p.ReviewApprovers)
                    .HasForeignKey(d => d.StepId)
                    .HasConstraintName("FK_ReviewApprovers_ReviewSteps");
            });

            modelBuilder.Entity<ReviewHistory>(entity =>
            {
                entity.HasKey(e => e.HistoryId)
                    .HasName("ReviewHistory_pkey");

                entity.ToTable("ReviewHistory");

                entity.HasComment("審核歷史記錄表，用於儲存審核任務的歷史操作記錄。");

                entity.Property(e => e.HistoryId).HasComment("歷史記錄流水號，主鍵，自動遞增，用於唯一識別歷史記錄。");

                entity.Property(e => e.Action)
                    .HasMaxLength(20)
                    .HasComment("操作類型，可選值為 approve（批准）、reject（拒絕）、return（退回）、comment（評論）。");

                entity.Property(e => e.Comment).HasComment("操作評論，提供操作的詳細說明或意見。");

                entity.Property(e => e.StepId).HasComment("步驟流水號，對應 ReviewSteps 表的 StepId。");

                entity.Property(e => e.TaskId).HasComment("任務流水號，對應 ReviewTasks 表的 TaskId。");

                entity.Property(e => e.Timestamp)
                    .HasColumnType("timestamp without time zone")
                    .HasDefaultValueSql("CURRENT_TIMESTAMP")
                    .HasComment("操作時間，記錄操作發生的時間，預設為當前時間。");

                entity.Property(e => e.UserInfoId)
                    .IsRequired()
                    .HasMaxLength(15)
                    .HasComment("使用者編號，對應 UserInfo 表的 UserInfoId。");
            });

            modelBuilder.Entity<ReviewStep>(entity =>
            {
                entity.HasKey(e => e.StepId)
                    .HasName("ReviewSteps_pkey");

                entity.HasComment("審核步驟表，用於儲存每個審核任務的具體步驟資訊。");

                entity.Property(e => e.StepId).HasComment("步驟流水號，主鍵，自動遞增，用於唯一識別審核步驟。");

                entity.Property(e => e.Name)
                    .IsRequired()
                    .HasMaxLength(255)
                    .HasComment("步驟名稱，描述步驟的具體內容。");

                entity.Property(e => e.Sequence).HasComment("步驟順序，用於定義步驟的執行順序。");

                entity.Property(e => e.Status)
                    .HasMaxLength(20)
                    .HasComment("步驟狀態，可選值為 waiting（等待中）、active（進行中）、completed（已完成）、skipped（已跳過）、rejected（已拒絕）。");

                entity.Property(e => e.TaskId).HasComment("任務流水號，對應 ReviewTasks 表的 TaskId。");

                entity.Property(e => e.TimeLimit).HasComment("時間限制，以小時為單位，指定步驟必須完成的時間。");

                entity.HasOne(d => d.Task)
                    .WithMany(p => p.ReviewSteps)
                    .HasForeignKey(d => d.TaskId)
                    .HasConstraintName("FK_ReviewSteps_ReviewTasks");
            });

            modelBuilder.Entity<ReviewTask>(entity =>
            {
                entity.HasKey(e => e.TaskId)
                    .HasName("ReviewTasks_pkey");

                entity.HasComment("審核任務表，用於儲存審核任務的基本資訊，與特定案場關聯。");

                entity.Property(e => e.TaskId).HasComment("任務流水號，主鍵，自動遞增，用於唯一識別審核任務。");

                entity.Property(e => e.CreatedTime)
                    .HasColumnType("timestamp without time zone")
                    .HasDefaultValueSql("CURRENT_TIMESTAMP")
                    .HasComment("創建時間，記錄任務創建的時間，預設為當前時間。");

                entity.Property(e => e.CreatedUserInfoId)
                    .HasMaxLength(15)
                    .HasComment("創建者編號，對應 UserInfo 表的 UserInfoId。");

                entity.Property(e => e.Deadline)
                    .HasColumnType("timestamp without time zone")
                    .HasComment("截止日期，任務必須完成的時間。");

                entity.Property(e => e.Description).HasComment("任務描述，提供任務的詳細資訊。");

                entity.Property(e => e.SiteCode)
                    .HasMaxLength(50)
                    .HasComment("案場號碼，與 Sites 表的 SiteCode 關聯，指定任務的案場。");

                entity.Property(e => e.Status)
                    .HasMaxLength(20)
                    .HasComment("任務狀態，enable（啟用）、disable（停用）、deleted(刪除)");

                entity.Property(e => e.Title)
                    .IsRequired()
                    .HasMaxLength(255)
                    .HasComment("任務標題，描述任務的主要內容。");
            });

            modelBuilder.Entity<Site>(entity =>
            {
                entity.HasKey(e => e.SiteCode)
                    .HasName("Sites_pkey");

                entity.HasComment("案場資料表，用於儲存案場的基本資訊、區域資料和合約相關資訊。");

                entity.HasIndex(e => e.Chairman, "IX_Sites_Chairman");

                entity.HasIndex(e => e.CompanyId, "IX_Sites_CompanyId");

                entity.HasIndex(e => e.CreatedUserInfoId, "IX_Sites_CreatedUserInfoId");

                entity.HasIndex(e => e.DeputyProjectManager, "IX_Sites_DeputyProjectManager");

                entity.HasIndex(e => e.ProjectManager, "IX_Sites_ProjectManager");

                entity.HasIndex(e => e.UpdatedUserInfoId, "IX_Sites_UpdatedUserInfoId");

                entity.HasIndex(e => e.ViceChairman, "IX_Sites_ViceChairman");

                entity.Property(e => e.SiteCode)
                    .HasMaxLength(50)
                    .HasComment("案場號碼，主鍵，用於唯一識別案場。");

                entity.Property(e => e.AboveGroundFloors)
                    .HasMaxLength(50)
                    .HasComment("規劃樓層(上層)，地上樓層數。");

                entity.Property(e => e.AdvertisingBudget)
                    .HasPrecision(15, 2)
                    .HasComment("應編廣告預算，案場應編列的廣告預算金額。");

                entity.Property(e => e.AdvertisingBudgetRate)
                    .HasPrecision(5, 2)
                    .HasComment("廣告預算率，廣告預算的比率（百分比）。");

                entity.Property(e => e.BelowGroundFloors)
                    .HasMaxLength(50)
                    .HasComment("規劃樓層(下層)，地下樓層數。");

                entity.Property(e => e.Broker)
                    .HasMaxLength(50)
                    .HasComment("經紀人，負責案場的經紀人名稱或編號。");

                entity.Property(e => e.BusinessIds).HasComment("業務，儲存以逗號分隔的字串（如 \"pm8327,bnv783,oia198027,...\"），對應多個 UserInfo 表的 UserInfoId。");

                entity.Property(e => e.Chairman)
                    .HasMaxLength(15)
                    .HasComment("主委，儲存單一人員 ID，對應 UserInfo 表的 UserInfoId。");

                entity.Property(e => e.City)
                    .HasMaxLength(50)
                    .HasComment("所在縣市，案場所在的縣市。");

                entity.Property(e => e.CompanyId)
                    .IsRequired()
                    .HasMaxLength(10)
                    .HasComment("執行公司別，對應 Company 表的 CompanyId，記錄案場所屬公司。");

                entity.Property(e => e.ContractPeriod).HasComment("合約期間，案場合約的期間（天數或月數）。");

                entity.Property(e => e.ContractedAmount)
                    .HasPrecision(15, 2)
                    .HasComment("已發包金額，已發包的金額。");

                entity.Property(e => e.ControlReserveRate)
                    .HasPrecision(5, 2)
                    .HasComment("控存率，控制儲備的比率（百分比）。");

                entity.Property(e => e.CreatedTime)
                    .HasColumnType("timestamp without time zone")
                    .HasComment("創建時間，記錄資料創建的時間。");

                entity.Property(e => e.CreatedUserInfoId)
                    .IsRequired()
                    .HasMaxLength(15)
                    .HasComment("建立者，記錄創建此資料的員工編號，對應 UserInfo 表的 UserInfoId。");

                entity.Property(e => e.DeputyProjectManager)
                    .HasMaxLength(15)
                    .HasComment("副專，儲存單一人員 ID，對應 UserInfo 表的 UserInfoId。");

                entity.Property(e => e.Developer)
                    .HasMaxLength(100)
                    .HasComment("投資興建，開發商或投資方的名稱。");

                entity.Property(e => e.District)
                    .HasMaxLength(50)
                    .HasComment("所在區域，案場所在的區域或鄉鎮區。");

                entity.Property(e => e.ExcessPriceAllocation)
                    .HasMaxLength(50)
                    .HasComment("超價款分配，超價款的分配方式。");

                entity.Property(e => e.ExtensionPeriod).HasComment("展延期間，合約展延的期間（天數或月數）。");

                entity.Property(e => e.LandArea)
                    .HasPrecision(15, 2)
                    .HasComment("基地面積，案場用地的面積，單位為平方公尺。");

                entity.Property(e => e.PaidAmount)
                    .HasPrecision(15, 2)
                    .HasDefaultValueSql("0")
                    .HasComment("已請款金額，已請款的金額，預設為 0。");

                entity.Property(e => e.ParkingType)
                    .HasMaxLength(50)
                    .HasComment("車位類別，案場的車位類型（如機械車位、平面車位）。");

                entity.Property(e => e.PlannedParkingSpaces).HasComment("規劃戶車(車位)，規劃的車位數。");

                entity.Property(e => e.PlannedResidentialUnits).HasComment("規劃戶車(住家)，規劃的住宅戶數。");

                entity.Property(e => e.PlannedStoreUnits).HasComment("規劃戶車(店面)，規劃的店面戶數。");

                entity.Property(e => e.ProjectManager)
                    .HasMaxLength(15)
                    .HasComment("專案，儲存單一人員 ID，對應 UserInfo 表的 UserInfoId。");

                entity.Property(e => e.PromotionType)
                    .HasMaxLength(50)
                    .HasComment("推案型態，描述案場的推案方式或類型。");

                entity.Property(e => e.PublicFacilityRatio)
                    .HasPrecision(5, 2)
                    .HasComment("公設比，公共設施比例（百分比）。");

                entity.Property(e => e.ReceptionCenter)
                    .HasMaxLength(100)
                    .HasComment("接待中心，案場的接待中心名稱或地址。");

                entity.Property(e => e.ReserveAmount)
                    .HasPrecision(15, 2)
                    .HasComment("保留款，案場的保留款金額。");

                entity.Property(e => e.RunnerIds).HasComment("跑單，儲存以逗號分隔的字串（如 \"pm8327,bnv783,oia198027,...\"），對應多個 UserInfo 表的 UserInfoId。");

                entity.Property(e => e.SellableTotalPrice)
                    .HasPrecision(15, 2)
                    .HasComment("可售總銷，可供銷售的總金額。");

                entity.Property(e => e.ServiceFeeCalculation)
                    .HasMaxLength(50)
                    .HasComment("服務費計算，服務費的計算方式。");

                entity.Property(e => e.ServiceFeeRate)
                    .HasPrecision(5, 2)
                    .HasComment("服務費率，服務費的比率（百分比）。");

                entity.Property(e => e.SiteLocation).HasComment("基地位置，案場的具體地址。");

                entity.Property(e => e.SiteName)
                    .IsRequired()
                    .HasMaxLength(100)
                    .HasComment("案名，案場名稱。");

                entity.Property(e => e.SitePhone)
                    .HasMaxLength(50)
                    .HasComment("案場電話，案場的聯絡電話。");

                entity.Property(e => e.Structure)
                    .HasMaxLength(50)
                    .HasComment("結構，案場的建築結構類型。");

                entity.Property(e => e.TotalSalePrice)
                    .HasPrecision(15, 2)
                    .HasComment("全案總銷，案場的總銷售金額。");

                entity.Property(e => e.UnitSize)
                    .HasMaxLength(50)
                    .HasComment("坪數，案場的單位面積（坪數）。");

                entity.Property(e => e.UpdatedTime)
                    .HasColumnType("timestamp without time zone")
                    .HasComment("更新時間，記錄資料最後更新的時間。");

                entity.Property(e => e.UpdatedUserInfoId)
                    .IsRequired()
                    .HasMaxLength(15)
                    .HasComment("更新者，記錄最後更新此資料的員工編號，對應 UserInfo 表的 UserInfoId。");

                entity.Property(e => e.ViceChairman)
                    .HasMaxLength(15)
                    .HasComment("副主委，儲存單一人員 ID，對應 UserInfo 表的 UserInfoId。");

                entity.Property(e => e.Zoning)
                    .HasMaxLength(50)
                    .HasComment("使用分區，案場所在的使用分區類型。");

                entity.HasOne(d => d.ChairmanNavigation)
                    .WithMany(p => p.SiteChairmanNavigations)
                    .HasForeignKey(d => d.Chairman);

                entity.HasOne(d => d.CreatedUserInfo)
                    .WithMany(p => p.SiteCreatedUserInfos)
                    .HasForeignKey(d => d.CreatedUserInfoId)
                    .OnDelete(DeleteBehavior.ClientSetNull);

                entity.HasOne(d => d.DeputyProjectManagerNavigation)
                    .WithMany(p => p.SiteDeputyProjectManagerNavigations)
                    .HasForeignKey(d => d.DeputyProjectManager);

                entity.HasOne(d => d.ProjectManagerNavigation)
                    .WithMany(p => p.SiteProjectManagerNavigations)
                    .HasForeignKey(d => d.ProjectManager);

                entity.HasOne(d => d.UpdatedUserInfo)
                    .WithMany(p => p.SiteUpdatedUserInfos)
                    .HasForeignKey(d => d.UpdatedUserInfoId)
                    .OnDelete(DeleteBehavior.ClientSetNull);

                entity.HasOne(d => d.ViceChairmanNavigation)
                    .WithMany(p => p.SiteViceChairmanNavigations)
                    .HasForeignKey(d => d.ViceChairman);
            });

            modelBuilder.Entity<SiteCrmOption>(entity =>
            {
                entity.HasComment("儲存每個案場在不同CRM選項類型下的具體選項值");

                entity.HasIndex(e => e.CrmOptionTypeId, "IdxSiteCrmOptionsOnCrmOptionTypeId");

                entity.HasIndex(e => e.SiteCode, "IdxSiteCrmOptionsOnSiteCode");

                entity.Property(e => e.SiteCrmOptionId).HasComment("主鍵，選項值唯一識別碼");

                entity.Property(e => e.CreateTime)
                    .HasColumnType("timestamp without time zone")
                    .HasDefaultValueSql("now()")
                    .HasComment("創建時間，記錄資料創建的時間。");

                entity.Property(e => e.CreatedUserInfoId)
                    .IsRequired()
                    .HasMaxLength(15)
                    .HasComment("建立者，記錄創建此資料的員工編號。");

                entity.Property(e => e.CrmOptionTypeId).HasComment("關聯的選項類型ID (參考 CrmOptionTypes 表)");

                entity.Property(e => e.IsActive)
                    .IsRequired()
                    .HasDefaultValueSql("true")
                    .HasComment("此選項是否啟用");

                entity.Property(e => e.OptionValue)
                    .IsRequired()
                    .HasMaxLength(255)
                    .HasComment("實際顯示在下拉選單中的文字 (例如: 20-30坪, 3房2廳)");

                entity.Property(e => e.SiteCode)
                    .IsRequired()
                    .HasMaxLength(50)
                    .HasComment("關聯的案場代碼 (參考 Sites 表的 SiteCode)");

                entity.Property(e => e.SortOrder).HasComment("在同一個下拉選單中的顯示順序，數字越小越前面");

                entity.Property(e => e.UpdateTime)
                    .HasColumnType("timestamp without time zone")
                    .HasDefaultValueSql("now()")
                    .HasComment("更新時間，記錄資料最後更新的時間。");

                entity.Property(e => e.UpdatedUserInfoId)
                    .IsRequired()
                    .HasMaxLength(15)
                    .HasComment("更新者，記錄最後更新此資料的員工編號。");
            });

            modelBuilder.Entity<SmallCategory>(entity =>
            {
                entity.HasComment("商品小分類資料表");

                entity.HasIndex(e => e.MediumCategoryId, "IdxSmallCategoriesOnMediumCategoryId");

                entity.Property(e => e.SmallCategoryId).HasComment("主鍵，小分類唯一識別碼");

                entity.Property(e => e.CreateTime)
                    .HasColumnType("timestamp(6) without time zone")
                    .HasDefaultValueSql("now()")
                    .HasComment("創建時間，記錄資料創建的時間。");

                entity.Property(e => e.CreatedUserInfoId)
                    .IsRequired()
                    .HasMaxLength(15)
                    .HasComment("建立者，記錄創建此資料的員工編號。");

                entity.Property(e => e.Description).HasComment("小分類的詳細文字描述");

                entity.Property(e => e.IsActive)
                    .IsRequired()
                    .HasDefaultValueSql("true")
                    .HasComment("是否啟用此分類 (TRUE: 啟用, FALSE: 停用)");

                entity.Property(e => e.MediumCategoryId).HasComment("所屬中分類的ID，需由應用程式確保其有效性");

                entity.Property(e => e.Name)
                    .IsRequired()
                    .HasMaxLength(255)
                    .HasComment("小分類的顯示名稱");

                entity.Property(e => e.SortOrder).HasComment("在同一個中分類下的排序順序，數字越小越前面");

                entity.Property(e => e.UpdateTime)
                    .HasColumnType("timestamp(6) without time zone")
                    .HasDefaultValueSql("now()")
                    .HasComment("更新時間，記錄資料最後更新的時間。");

                entity.Property(e => e.UpdatedUserInfoId)
                    .IsRequired()
                    .HasMaxLength(15)
                    .HasComment("更新者，記錄最後更新此資料的員工編號。");
            });

            modelBuilder.Entity<Supplier>(entity =>
            {
                entity.ToTable("Supplier");

                entity.HasComment("供應商資料表，用於儲存供應商的基本資訊");

                entity.Property(e => e.SupplierId).HasComment("供應商唯一識別碼，自動遞增");

                entity.Property(e => e.AccountName)
                    .HasMaxLength(100)
                    .HasComment("收款戶名");

                entity.Property(e => e.Address).HasComment("通訊地址");

                entity.Property(e => e.BankBranch)
                    .HasMaxLength(100)
                    .HasComment("銀行分行名稱");

                entity.Property(e => e.BankName)
                    .HasMaxLength(100)
                    .HasComment("銀行名稱");

                entity.Property(e => e.CompanyPhone)
                    .HasMaxLength(20)
                    .HasComment("公司電話");

                entity.Property(e => e.ContactName)
                    .HasMaxLength(100)
                    .HasComment("聯絡窗口姓名");

                entity.Property(e => e.ContactPerson)
                    .HasMaxLength(100)
                    .HasComment("負責人姓名");

                entity.Property(e => e.ContactPhone1)
                    .HasMaxLength(20)
                    .HasComment("聯絡電話1");

                entity.Property(e => e.ContactPhone2)
                    .HasMaxLength(20)
                    .HasComment("聯絡電話2");

                entity.Property(e => e.CreatedTime)
                    .HasColumnType("timestamp without time zone")
                    .HasComment("資料創建時間");

                entity.Property(e => e.CreatedUserInfoId)
                    .HasMaxLength(15)
                    .HasComment("資料創建人識別碼（參考 UserInfo 表的 UserInfoId）");

                entity.Property(e => e.Email)
                    .HasMaxLength(255)
                    .HasComment("電子郵件");

                entity.Property(e => e.IdNumber)
                    .HasMaxLength(50)
                    .HasComment("證號（身分證號或統一編號）");

                entity.Property(e => e.IdType)
                    .HasMaxLength(50)
                    .HasComment("證號類型（例如：身分證、統一編號）");

                entity.Property(e => e.Name)
                    .IsRequired()
                    .HasMaxLength(255)
                    .HasComment("供應商名稱（個人或公司名稱）");

                entity.Property(e => e.PersonType)
                    .IsRequired()
                    .HasMaxLength(20)
                    .HasComment("人別（個人或法人，例如：個人、公司）");

                entity.Property(e => e.UpdatedTime)
                    .HasColumnType("timestamp without time zone")
                    .HasComment("資料更新時間");

                entity.Property(e => e.UpdatedUserInfoId)
                    .HasMaxLength(15)
                    .HasComment("資料更新人識別碼（參考 UserInfo 表的 UserInfoId）");
            });

            modelBuilder.Entity<SupplierFile>(entity =>
            {
                entity.ToTable("SupplierFile");

                entity.HasComment("供應商檔案資料表，用於儲存與供應商相關的檔案資訊");

                entity.Property(e => e.SupplierFileId).HasComment("檔案唯一識別碼，自動遞增");

                entity.Property(e => e.Agent)
                    .HasMaxLength(100)
                    .HasComment("承辦人姓名");

                entity.Property(e => e.CreatedTime)
                    .HasColumnType("timestamp without time zone")
                    .HasComment("資料創建時間");

                entity.Property(e => e.CreatedUserInfoId)
                    .HasMaxLength(15)
                    .HasComment("資料創建人識別碼（參考 UserInfo 表的 UserInfoId）");

                entity.Property(e => e.FileName)
                    .IsRequired()
                    .HasMaxLength(255)
                    .HasComment("檔案名稱");

                entity.Property(e => e.FilePath).HasComment("檔案儲存路徑");

                entity.Property(e => e.FormType)
                    .IsRequired()
                    .HasMaxLength(50)
                    .HasComment("表單類別（例如：合約、證明文件）");

                entity.Property(e => e.Remark).HasComment("備註");

                entity.Property(e => e.SupplierId).HasComment("關聯的供應商識別碼，外鍵");

                entity.Property(e => e.UpdatedTime)
                    .HasColumnType("timestamp without time zone")
                    .HasComment("資料更新時間");

                entity.Property(e => e.UpdatedUserInfoId)
                    .HasMaxLength(15)
                    .HasComment("資料更新人識別碼（參考 UserInfo 表的 UserInfoId）");

                entity.HasOne(d => d.Supplier)
                    .WithMany(p => p.SupplierFiles)
                    .HasForeignKey(d => d.SupplierId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("SupplierFile_SupplierId_fkey");
            });

            modelBuilder.Entity<SysCode>(entity =>
            {
                entity.HasKey(e => new { e.Type, e.Code })
                    .HasName("SYS_Code_pkey");

                entity.ToTable("SYS_Code");

                entity.HasComment("系統代碼表，用於儲存系統中的標準化代碼資訊，例如狀態碼、類型碼等。");

                entity.Property(e => e.Type)
                    .HasMaxLength(50)
                    .HasComment("代碼類型，主鍵的一部分，用於區分不同的代碼類別。");

                entity.Property(e => e.Code)
                    .HasMaxLength(30)
                    .HasComment("代碼值，主鍵的一部分，用於唯一識別具體代碼。");

                entity.Property(e => e.CodeDesc)
                    .HasMaxLength(300)
                    .HasComment("代碼描述，提供代碼的詳細解釋。");

                entity.Property(e => e.CodeOrder).HasComment("排序序號，用於控制代碼的顯示順序。");

                entity.Property(e => e.CreatedTime)
                    .HasColumnType("timestamp without time zone")
                    .HasComment("創建時間，記錄資料創建的時間。");

                entity.Property(e => e.CreatedUserInfoId)
                    .IsRequired()
                    .HasMaxLength(15)
                    .HasComment("建立者，記錄創建此資料的員工編號。");

                entity.Property(e => e.IsActive)
                    .IsRequired()
                    .HasDefaultValueSql("true")
                    .HasComment("是否啟用，true 表示啟用，false 表示停用，預設為 true。");

                entity.Property(e => e.ParentCode)
                    .HasMaxLength(30)
                    .HasComment("上層代碼，可為 NULL，表示代碼的層級關係。");

                entity.Property(e => e.UpdatedTime)
                    .HasColumnType("timestamp without time zone")
                    .HasComment("更新時間，記錄資料最後更新的時間。");

                entity.Property(e => e.UpdatedUserInfoId)
                    .IsRequired()
                    .HasMaxLength(15)
                    .HasComment("更新者，記錄最後更新此資料的員工編號。");
            });

            modelBuilder.Entity<SysMenuFunc>(entity =>
            {
                entity.HasKey(e => new { e.System, e.FuncId })
                    .HasName("SYS_MenuFunc_pkey");

                entity.ToTable("SYS_MenuFunc");

                entity.HasComment("系統功能選單表，用於儲存系統的功能項目及其層級結構。");

                entity.Property(e => e.System)
                    .HasMaxLength(30)
                    .HasComment("系統名稱，主鍵的一部分，用於區分不同系統。");

                entity.Property(e => e.FuncId)
                    .HasMaxLength(50)
                    .HasComment("功能項目識別編號，主鍵的一部分，用於唯一識別功能項目。");

                entity.Property(e => e.FuncName)
                    .IsRequired()
                    .HasMaxLength(50)
                    .HasComment("功能項目名稱。");

                entity.Property(e => e.FuncOrder).HasComment("功能項目排序，用於控制選單顯示順序。");

                entity.Property(e => e.ParentFuncId)
                    .HasMaxLength(50)
                    .HasComment("上層功能項目識別編號，用於表示功能項目的層級結構。");
            });

            modelBuilder.Entity<SysRoleGroup>(entity =>
            {
                entity.HasKey(e => new { e.System, e.RoleGroupId })
                    .HasName("SYS_RoleGroup_pkey");

                entity.ToTable("SYS_RoleGroup");

                entity.HasComment("角色群組表，用於儲存系統中的角色群組資訊，支援與特定案場關聯。");

                entity.Property(e => e.System)
                    .HasMaxLength(30)
                    .HasComment("系統名稱，主鍵的一部分，用於區分不同系統。");

                entity.Property(e => e.RoleGroupId)
                    .HasMaxLength(50)
                    .HasComment("角色群組識別編號，主鍵的一部分，用於唯一識別角色群組。");

                entity.Property(e => e.CreatedTime)
                    .HasColumnType("timestamp without time zone")
                    .HasComment("創建時間，記錄角色群組創建的時間。");

                entity.Property(e => e.CreatedUserInfoId)
                    .IsRequired()
                    .HasMaxLength(15)
                    .HasComment("建立者，記錄創建此角色群組的員工編號。");

                entity.Property(e => e.IsAdmin).HasComment("是否為管理者角色，true 表示是，false 表示否。");

                entity.Property(e => e.Name)
                    .IsRequired()
                    .HasMaxLength(50)
                    .HasComment("角色群組名稱。");

                entity.Property(e => e.SiteCode)
                    .HasMaxLength(50)
                    .HasComment("案場號碼，可為 NULL 表示全局角色，對應 Sites 表的 SiteCode。");

                entity.Property(e => e.UpdatedTime)
                    .HasColumnType("timestamp without time zone")
                    .HasComment("更新時間，記錄角色群組最後更新的時間。");

                entity.Property(e => e.UpdatedUserInfoId)
                    .IsRequired()
                    .HasMaxLength(15)
                    .HasComment("更新者，記錄最後更新此角色群組的員工編號。");
            });

            modelBuilder.Entity<SysRoleGroupPermission>(entity =>
            {
                entity.HasKey(e => new { e.System, e.RoleGroupId, e.FuncId })
                    .HasName("SYS_RoleGroupPermission_pkey");

                entity.ToTable("SYS_RoleGroupPermission");

                entity.HasComment("角色群組權限表，用於儲存角色群組對系統功能的權限設置。");

                entity.Property(e => e.System)
                    .HasMaxLength(30)
                    .HasComment("系統名稱，主鍵的一部分，用於區分不同系統。");

                entity.Property(e => e.RoleGroupId)
                    .HasMaxLength(50)
                    .HasComment("角色群組識別編號，主鍵的一部分，對應 SYS_RoleGroup 表的 RoleGroupId。");

                entity.Property(e => e.FuncId)
                    .HasMaxLength(50)
                    .HasComment("功能項目識別編號，主鍵的一部分，對應 SYS_MenuFunc 表的 FuncId。");

                entity.Property(e => e.Crud).HasComment("CRUD 權限，儲存對功能的創建(C)、讀取(R)、更新(U)、刪除(D)權限設定。");

                entity.Property(e => e.IsActive).HasComment("是否啟用，true 表示啟用，false 表示停用，預設為 false。");
            });

            modelBuilder.Entity<SysRoleGroupUser>(entity =>
            {
                entity.HasKey(e => new { e.System, e.RoleGroupId, e.UserInfoId })
                    .HasName("SYS_RoleGroupUser_pkey");

                entity.ToTable("SYS_RoleGroupUser");

                entity.HasComment("角色群組使用者關聯表，用於儲存角色群組與使用者的關聯關係。");

                entity.Property(e => e.System)
                    .HasMaxLength(30)
                    .HasComment("系統名稱，主鍵的一部分，用於區分不同系統。");

                entity.Property(e => e.RoleGroupId)
                    .HasMaxLength(50)
                    .HasComment("角色群組識別編號，主鍵的一部分，對應 SYS_RoleGroup 表的 RoleGroupId。");

                entity.Property(e => e.UserInfoId)
                    .HasMaxLength(15)
                    .HasComment("使用者編號，主鍵的一部分，對應 UserInfo 表的 UserInfoId。");

                entity.HasOne(d => d.UserInfo)
                    .WithMany(p => p.SysRoleGroupUsers)
                    .HasForeignKey(d => d.UserInfoId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_SYS_RoleGroupUser_UserInfo");
            });

            modelBuilder.Entity<SysSystemSetting>(entity =>
            {
                entity.HasKey(e => new { e.Type, e.Key })
                    .HasName("SYS_SystemSetting_pkey");

                entity.ToTable("SYS_SystemSetting");

                entity.HasComment("系統設定表，用於儲存系統的各種設定鍵值對資訊。");

                entity.Property(e => e.Type)
                    .HasMaxLength(50)
                    .HasComment("類別，用於區分不同的設定類型。");

                entity.Property(e => e.Key)
                    .HasMaxLength(50)
                    .HasComment("鍵，主鍵的一部分，用於唯一識別系統設定項目。");

                entity.Property(e => e.CreatedTime)
                    .HasColumnType("timestamp without time zone")
                    .HasComment("創建時間，記錄設定創建的時間。");

                entity.Property(e => e.CreatedUserInfoId)
                    .IsRequired()
                    .HasMaxLength(15)
                    .HasComment("建立者，記錄創建此設定的員工編號，對應 UserInfo 表的 UserInfoId。");

                entity.Property(e => e.IsEnabled)
                    .IsRequired()
                    .HasDefaultValueSql("true")
                    .HasComment("是否啟用，true 表示啟用，false 表示停用，預設為 true。");

                entity.Property(e => e.Name)
                    .IsRequired()
                    .HasMaxLength(50)
                    .HasComment("詳細名稱，描述設定的具體用途或含義。");

                entity.Property(e => e.UpdatedTime)
                    .HasColumnType("timestamp without time zone")
                    .HasComment("更新時間，記錄設定最後更新的時間。");

                entity.Property(e => e.UpdatedUserInfoId)
                    .IsRequired()
                    .HasMaxLength(15)
                    .HasComment("更新者，記錄最後更新此設定的員工編號，對應 UserInfo 表的 UserInfoId。");

                entity.Property(e => e.Value)
                    .HasMaxLength(100)
                    .HasComment("值，儲存設定的具體值，可為空。");
            });

            modelBuilder.Entity<Unit>(entity =>
            {
                entity.HasComment("儲存住宅/商業樓層上具體可銷售的獨立房屋單位(戶)的詳細資訊與狀態。**此表為必要**，對應銷控表主體格子。");

                entity.HasIndex(e => e.BuildingId, "IX_Units_BuildingId");

                entity.HasIndex(e => e.FloorId, "IX_Units_FloorId");

                entity.HasIndex(e => e.SiteCode, "IX_Units_SiteCode");

                entity.HasIndex(e => e.Status, "IX_Units_Status");

                entity.HasIndex(e => e.TotalArea, "IX_Units_TotalArea");

                entity.HasIndex(e => e.UnitNumber, "IX_Units_UnitNumber");

                entity.Property(e => e.UnitId).HasComment("房屋單位唯一識別碼 (主鍵, 自動遞增)。");

                entity.Property(e => e.AssociatedParkingSpaceIds).HasComment("關聯的停車位ID列表，以逗號分隔 (參考 ParkingSpaces 表)。住宅單位用於記錄建議搭配的車位；車位單位用於記錄對應的實體車位");

                entity.Property(e => e.AuxiliaryArea)
                    .HasPrecision(10, 2)
                    .HasComment("附屬建物坪數。");

                entity.Property(e => e.AwningArea).HasComment("雨遮面積 (坪)");

                entity.Property(e => e.BalconyArea).HasComment("陽台面積 (坪)");

                entity.Property(e => e.BuildingId).HasComment("所屬建築物識別碼 (參考 Buildings 表) - 方便查詢用。");

                entity.Property(e => e.CreatedTime)
                    .HasColumnType("timestamp without time zone")
                    .HasDefaultValueSql("CURRENT_TIMESTAMP")
                    .HasComment("資料創建時間 (TIMESTAMP WITHOUT TIME ZONE)。");

                entity.Property(e => e.CreatedUserInfoId)
                    .IsRequired()
                    .HasColumnType("character varying")
                    .HasComment("建立者 (參考 UserInfo 表)。");

                entity.Property(e => e.FloorId).HasComment("所屬樓層識別碼 (參考 Floors 表中 FloorType 為 '住宅'/'商業' 的樓層ID)。");

                entity.Property(e => e.IsPublicAreaIncluded).HasComment("權狀是否含公設 (True/False)。");

                entity.Property(e => e.LargePublicArea).HasComment("大公面積 (坪)");

                entity.Property(e => e.Layout)
                    .HasColumnType("character varying")
                    .HasComment("格局 (例如: \"3房2廳2衛\")。");

                entity.Property(e => e.ListPrice)
                    .HasPrecision(15)
                    .HasComment("表價 (牌價)。");

                entity.Property(e => e.MainArea)
                    .HasPrecision(10, 2)
                    .HasComment("主建物坪數。");

                entity.Property(e => e.MinimumPrice)
                    .HasPrecision(15)
                    .HasComment("底價。");

                entity.Property(e => e.Orientation)
                    .HasColumnType("character varying")
                    .HasComment("座向。");

                entity.Property(e => e.PublicAreaShare)
                    .HasPrecision(10, 2)
                    .HasComment("公設分攤坪數。");

                entity.Property(e => e.Remarks).HasComment("備註。");

                entity.Property(e => e.SiteCode)
                    .IsRequired()
                    .HasColumnType("character varying")
                    .HasComment("所屬案場編號 (參考 Sites 表) - 方便查詢用。");

                entity.Property(e => e.SmallPublicArea).HasComment("小公面積 (坪)");

                entity.Property(e => e.Status)
                    .IsRequired()
                    .HasColumnType("character varying")
                    .HasComment("單位目前的**獨立銷售狀態** (例如: '可售', '已預訂', '已售', '保留' - 建議關聯 SYS_Code)。");

                entity.Property(e => e.TotalArea)
                    .HasPrecision(10, 2)
                    .HasComment("權狀總坪數。");

                entity.Property(e => e.TransactionPrice)
                    .HasPrecision(15)
                    .HasComment("實際成交價 (成交後填入)。");

                entity.Property(e => e.UnitNumber)
                    .IsRequired()
                    .HasColumnType("character varying")
                    .HasComment("戶號 (銷控表橫軸，例如: \"A\", \"B\", \"C\")。");

                entity.Property(e => e.UnitType)
                    .IsRequired()
                    .HasColumnType("character varying")
                    .HasComment("單位類型 (例如: \"住宅\", \"店面\", \"車位\" - 建議關聯 SYS_Code)");

                entity.Property(e => e.UpdatedTime)
                    .HasColumnType("timestamp without time zone")
                    .HasDefaultValueSql("CURRENT_TIMESTAMP")
                    .HasComment("資料最後更新時間 (TIMESTAMP WITHOUT TIME ZONE)。");

                entity.Property(e => e.UpdatedUserInfoId)
                    .IsRequired()
                    .HasColumnType("character varying")
                    .HasComment("更新者 (參考 UserInfo 表)。");
            });

            modelBuilder.Entity<UserDepartment>(entity =>
            {
                entity.HasKey(e => new { e.UserInfoId, e.DepartmentId })
                    .HasName("UserDepartments_pkey");

                entity.HasComment("員工部門關聯表，用於儲存員工與部門的關聯關係，支持員工隸屬多個部門。");

                entity.Property(e => e.UserInfoId)
                    .HasMaxLength(15)
                    .HasComment("員工編號，主鍵的一部分，對應 UserInfo 表的 UserInfoId。");

                entity.Property(e => e.DepartmentId)
                    .HasMaxLength(50)
                    .HasComment("部門編號，主鍵的一部分，對應 Departments 表的 DepartmentId。");

                entity.Property(e => e.IsPrimary)
                    .HasDefaultValueSql("false")
                    .HasComment("是否為主要部門，true 表示是，false 表示否，預設為 false。");

                entity.HasOne(d => d.UserInfo)
                    .WithMany(p => p.UserDepartments)
                    .HasForeignKey(d => d.UserInfoId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_UserDepartments_UserInfo");
            });

            modelBuilder.Entity<UserInfo>(entity =>
            {
                entity.ToTable("UserInfo");

                entity.HasComment("員工基本資料表，用於儲存員工的個人資訊、聯絡方式等相關資料，角色、部門、職位資訊透過關聯表記錄。");

                entity.Property(e => e.UserInfoId)
                    .HasMaxLength(15)
                    .HasComment("員工編號，主鍵，用於唯一識別員工。");

                entity.Property(e => e.BirthDate).HasComment("出生日期。");

                entity.Property(e => e.CompanyId)
                    .IsRequired()
                    .HasMaxLength(10)
                    .HasComment("公司編號，對應 Company 表的 CompanyId，記錄員工所屬的主要公司。");

                entity.Property(e => e.CreatedTime)
                    .HasColumnType("timestamp without time zone")
                    .HasComment("創建時間，記錄資料創建的時間。");

                entity.Property(e => e.CreatedUserInfoId)
                    .IsRequired()
                    .HasMaxLength(15)
                    .HasComment("建立者，記錄創建此資料的員工編號。");

                entity.Property(e => e.Email)
                    .IsRequired()
                    .HasMaxLength(254)
                    .HasComment("電子郵件地址。");

                entity.Property(e => e.EmergencyContactName)
                    .HasMaxLength(50)
                    .HasComment("緊急聯絡人姓名。");

                entity.Property(e => e.EmergencyContactPhone)
                    .HasMaxLength(50)
                    .HasComment("緊急聯絡人電話。");

                entity.Property(e => e.EmergencyContactRelation)
                    .HasMaxLength(50)
                    .HasComment("與緊急聯絡人的關係，例如父母、配偶等。");

                entity.Property(e => e.Gender)
                    .HasMaxLength(10)
                    .HasComment("性別，例如 M（男）、F（女）。");

                entity.Property(e => e.HireDate).HasComment("到職日期。");

                entity.Property(e => e.Identity)
                    .HasMaxLength(10)
                    .HasComment("身分證字號");

                entity.Property(e => e.IsEmailNotificationEnabled).HasComment("是否需要發送電子郵件通知，true 表示是，false 表示否。");

                entity.Property(e => e.IsInside).HasComment("使否為內場/外場人員");

                entity.Property(e => e.IsM365).HasComment("是否為 M365 帳號，true 表示是，false 表示否。");

                entity.Property(e => e.LastLoginIp)
                    .HasMaxLength(50)
                    .HasColumnName("LastLoginIP")
                    .HasComment("最後一次登入的 IP 地址。");

                entity.Property(e => e.LastLoginTime)
                    .HasColumnType("timestamp without time zone")
                    .HasComment("最後一次登入的時間。");

                entity.Property(e => e.LastLogoutTime)
                    .HasColumnType("timestamp without time zone")
                    .HasComment("最後一次登出的時間。");

                entity.Property(e => e.LoginFailedCount).HasComment("登入失敗次數加總，用於追蹤密碼錯誤次數。");

                entity.Property(e => e.MailingAddress).HasComment("通訊地址。");

                entity.Property(e => e.MobileNumber)
                    .HasMaxLength(50)
                    .HasComment("手機號碼。");

                entity.Property(e => e.Name)
                    .IsRequired()
                    .HasMaxLength(15)
                    .HasComment("員工姓名。");

                entity.Property(e => e.Password)
                    .IsRequired()
                    .HasMaxLength(255)
                    .HasComment("密碼，儲存加密後的密碼內容。");

                entity.Property(e => e.RegisteredAddress).HasComment("戶籍地址。");

                entity.Property(e => e.ServiceUnit)
                    .HasMaxLength(50)
                    .HasComment("所屬案場。");

                entity.Property(e => e.Status).HasComment("狀態，true 表示啟用，false 表示停用。");

                entity.Property(e => e.TelephoneNumber)
                    .HasMaxLength(50)
                    .HasComment("電話號碼。");

                entity.Property(e => e.UpdatedTime)
                    .HasColumnType("timestamp without time zone")
                    .HasComment("更新時間，記錄資料最後更新的時間。");

                entity.Property(e => e.UpdatedUserInfoId)
                    .IsRequired()
                    .HasMaxLength(15)
                    .HasComment("更新者，記錄最後更新此資料的員工編號。");
            });

            modelBuilder.Entity<UserJobTitle>(entity =>
            {
                entity.HasKey(e => new { e.UserInfoId, e.JobTitleId })
                    .HasName("UserJobTitles_pkey");

                entity.HasComment("員工職位關聯表，用於儲存員工與職位的關聯關係，支持員工擁有複數職位。");

                entity.Property(e => e.UserInfoId)
                    .HasMaxLength(15)
                    .HasComment("員工編號，主鍵的一部分，對應 UserInfo 表的 UserInfoId。");

                entity.Property(e => e.JobTitleId)
                    .HasMaxLength(50)
                    .HasComment("職位編號，主鍵的一部分，對應 JobTitles 表的 JobTitleId。");

                entity.Property(e => e.IsPrimary)
                    .HasDefaultValueSql("false")
                    .HasComment("是否為主要職位，true 表示是，false 表示否，預設為 false。");

                entity.HasOne(d => d.UserInfo)
                    .WithMany(p => p.UserJobTitles)
                    .HasForeignKey(d => d.UserInfoId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_UserJobTitles_UserInfo");
            });

            modelBuilder.Entity<UserPasswordHistory>(entity =>
            {
                entity.ToTable("UserPasswordHistory");

                entity.HasComment("密碼歷史記錄表，用於儲存員工的歷史密碼資訊。");

                entity.Property(e => e.UserPasswordHistoryId)
                    .ValueGeneratedNever()
                    .HasComment("流水號，主鍵，用於唯一識別密碼記錄。");

                entity.Property(e => e.CreatedTime)
                    .HasColumnType("timestamp without time zone")
                    .HasComment("創建時間，記錄密碼記錄創建的時間。");

                entity.Property(e => e.CreatedUserInfoId)
                    .IsRequired()
                    .HasMaxLength(15)
                    .HasComment("建立者，記錄創建此記錄的員工編號。");

                entity.Property(e => e.Password)
                    .IsRequired()
                    .HasMaxLength(255)
                    .HasComment("歷史密碼，儲存加密後的密碼內容。");

                entity.Property(e => e.UserInfoId)
                    .IsRequired()
                    .HasMaxLength(15)
                    .HasComment("員工編號，對應 UserInfo 表的 UserInfoId。");
            });

            modelBuilder.Entity<UserPasswordResetCode>(entity =>
            {
                entity.HasKey(e => e.UserPasswordResetCodesId)
                    .HasName("UserPasswordResetCodes_pkey");

                entity.HasComment("密碼重置驗證碼表，用於儲存員工密碼重置時的驗證碼資訊。");

                entity.Property(e => e.UserPasswordResetCodesId)
                    .ValueGeneratedNever()
                    .HasComment("流水號，主鍵，使用 UUID 格式唯一識別驗證碼記錄。");

                entity.Property(e => e.ExpirationTime)
                    .HasColumnType("timestamp without time zone")
                    .HasComment("驗證碼到期時間，過期後無法使用。");

                entity.Property(e => e.IsUsed)
                    .HasDefaultValueSql("false")
                    .HasComment("是否已使用，true 表示已使用，false 表示未使用，預設為 false。");

                entity.Property(e => e.UserInfoId)
                    .HasMaxLength(15)
                    .HasComment("員工編號，對應 UserInfo 表的 UserInfoId。");

                entity.Property(e => e.VerificationCode)
                    .HasMaxLength(6)
                    .HasComment("驗證碼，通常為 6 位數。");
            });

            OnModelCreatingPartial(modelBuilder);
        }

        partial void OnModelCreatingPartial(ModelBuilder modelBuilder);
    }
}
