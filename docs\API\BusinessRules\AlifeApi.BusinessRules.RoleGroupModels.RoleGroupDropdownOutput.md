#### [AlifeApi.BusinessRules](index.md 'index')
### [AlifeApi.BusinessRules.RoleGroupModels](AlifeApi.BusinessRules.RoleGroupModels.md 'AlifeApi.BusinessRules.RoleGroupModels')

## RoleGroupDropdownOutput Class

角色群組下拉選單輸出資料

```csharp
public class RoleGroupDropdownOutput
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; RoleGroupDropdownOutput
### Properties

<a name='AlifeApi.BusinessRules.RoleGroupModels.RoleGroupDropdownOutput.Name'></a>

## RoleGroupDropdownOutput.Name Property

名稱

```csharp
public string Name { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.RoleGroupModels.RoleGroupDropdownOutput.Value'></a>

## RoleGroupDropdownOutput.Value Property

值

```csharp
public string Value { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')