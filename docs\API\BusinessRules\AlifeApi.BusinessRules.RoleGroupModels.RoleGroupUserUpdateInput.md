#### [AlifeApi.BusinessRules](index.md 'index')
### [AlifeApi.BusinessRules.RoleGroupModels](AlifeApi.BusinessRules.RoleGroupModels.md 'AlifeApi.BusinessRules.RoleGroupModels')

## RoleGroupUserUpdateInput Class

修改角色權限使用者

```csharp
public class RoleGroupUserUpdateInput
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; RoleGroupUserUpdateInput
### Properties

<a name='AlifeApi.BusinessRules.RoleGroupModels.RoleGroupUserUpdateInput.Id'></a>

## RoleGroupUserUpdateInput.Id Property

ID

```csharp
public string Id { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.RoleGroupModels.RoleGroupUserUpdateInput.UserIds'></a>

## RoleGroupUserUpdateInput.UserIds Property

使用者清單

```csharp
public System.Collections.Generic.IEnumerable<string> UserIds { get; set; }
```

#### Property Value
[System.Collections.Generic.IEnumerable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Collections.Generic.IEnumerable-1 'System.Collections.Generic.IEnumerable`1')[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Collections.Generic.IEnumerable-1 'System.Collections.Generic.IEnumerable`1')