using System.ComponentModel.DataAnnotations;

namespace AlifeApi.BusinessRules.OwnerModels
{
    /// <summary>
    /// 建立業主輸入模型
    /// </summary>
    public class OwnerCreateInput
    {
        /// <summary>
        /// 公司名稱
        /// </summary>
        [Required(ErrorMessage = "公司名稱為必填欄位")]
        [MaxLength(100, ErrorMessage = "公司名稱長度不可超過 100 個字元")]
        public string CompanyName { get; set; }

        /// <summary>
        /// 負責人姓名
        /// </summary>
        [MaxLength(50, ErrorMessage = "負責人姓名長度不可超過 50 個字元")]
        public string? ResponsiblePerson { get; set; }

        /// <summary>
        /// 公司登記電話
        /// </summary>
        [MaxLength(20, ErrorMessage = "公司登記電話長度不可超過 20 個字元")]
        public string? CompanyPhone { get; set; }

        /// <summary>
        /// 公司登記地址
        /// </summary>
        [MaxLength(200, ErrorMessage = "公司登記地址長度不可超過 200 個字元")]
        public string? CompanyAddress { get; set; }

        /// <summary>
        /// 郵件通訊地址 (若與公司地址不同)
        /// </summary>
        [MaxLength(200, ErrorMessage = "郵件通訊地址長度不可超過 200 個字元")]
        public string? MailingAddress { get; set; }

        /// <summary>
        /// 主要聯絡窗口人員姓名
        /// </summary>
        [MaxLength(50, ErrorMessage = "主要聯絡窗口人員姓名長度不可超過 50 個字元")]
        public string? ContactPerson { get; set; }

        /// <summary>
        /// 聯絡電子郵件地址
        /// </summary>
        [MaxLength(100, ErrorMessage = "聯絡電子郵件地址長度不可超過 100 個字元")]
        public string? Email { get; set; }

        /// <summary>
        /// 人別 (值為 法人 或 自然人)
        /// </summary>
        [Required(ErrorMessage = "人別為必填欄位")]
        [MaxLength(10, ErrorMessage = "人別長度不可超過 10 個字元")] // 例如 "法人", "自然人"
        public string PersonType { get; set; }

        /// <summary>
        /// 證號 (依人別決定是 統一編號 或 身分證ID)
        /// </summary>
        [Required(ErrorMessage = "證號為必填欄位")]
        [MaxLength(20, ErrorMessage = "證號長度不可超過 20 個字元")]
        public string IdentificationNumber { get; set; }

        /// <summary>
        /// 主要聯絡電話
        /// </summary>
        [MaxLength(20, ErrorMessage = "主要聯絡電話長度不可超過 20 個字元")]
        public string? ContactPhone1 { get; set; }

        /// <summary>
        /// 次要聯絡電話 (備用)
        /// </summary>
        [MaxLength(20, ErrorMessage = "次要聯絡電話長度不可超過 20 個字元")]
        public string? ContactPhone2 { get; set; }
    }
} 
