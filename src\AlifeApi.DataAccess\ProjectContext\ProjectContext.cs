﻿using System;
using System.Collections.Generic;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata;

namespace AlifeApi.DataAccess.ProjectContext
{
    public partial class ProjectContext : DbContext
    {
        public ProjectContext(DbContextOptions<ProjectContext> options) : base(options)
        {
        }

        public virtual DbSet<SysBulletins> SysBulletins { get; set; }

        public virtual DbSet<SysBulletinsAttach> SysBulletinsAttach { get; set; }

        public virtual DbSet<SysBulletinsAttachDownloadRecord> SysBulletinsAttachDownloadRecord { get; set; }

        public virtual DbSet<SysBulletinsClickRecord> SysBulletinsClickRecord { get; set; }

        public virtual DbSet<SysCode> SysCode { get; set; }

        public virtual DbSet<SysMenuFunc> SysMenuFunc { get; set; }

        public virtual DbSet<SysProblemReport> SysProblemReport { get; set; }

        public virtual DbSet<SysProjectVersion> SysProjectVersion { get; set; }

        public virtual DbSet<SysRoleGroup> SysRoleGroup { get; set; }

        public virtual DbSet<SysRoleGroupPermission> SysRoleGroupPermission { get; set; }

        public virtual DbSet<SysRoleGroupUser> SysRoleGroupUser { get; set; }

        public virtual DbSet<SysScheduleHistory> SysScheduleHistory { get; set; }

        public virtual DbSet<SysSystemSetting> SysSystemSetting { get; set; }

        public virtual DbSet<SysType> SysType { get; set; }

        public virtual DbSet<SysUserRecord> SysUserRecord { get; set; }

        public virtual DbSet<UserDept> UserDept { get; set; }

        public virtual DbSet<UserDeptPermission> UserDeptPermission { get; set; }

        public virtual DbSet<UserGrade> UserGrade { get; set; }

        public virtual DbSet<UserGradePermission> UserGradePermission { get; set; }

        public virtual DbSet<UserInfo> UserInfo { get; set; }

        public virtual DbSet<UserPasswordHistory> UserPasswordHistory { get; set; }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            modelBuilder.Entity<SysBulletins>(entity =>
            {
                entity.HasKey(e => e.BlId);

                entity.ToTable("SYS_Bulletins");

                entity.Property(e => e.BlId)
                    .HasColumnName("BL_Id")
                    .HasComment("公告流水號");

                entity.Property(e => e.BlContent)
                    .HasMaxLength(1000)
                    .HasColumnName("BL_Content")
                    .HasComment("公告內容");

                entity.Property(e => e.BlCrDatetime)
                    .HasColumnType("datetime")
                    .HasColumnName("BL_CrDatetime")
                    .HasComment("建立時間");

                entity.Property(e => e.BlCrUser)
                    .HasMaxLength(15)
                    .IsUnicode(false)
                    .HasColumnName("BL_CrUser")
                    .HasComment("建立者");

                entity.Property(e => e.BlIsEnable)
                    .HasColumnName("BL_IsEnable")
                    .HasComment("是否上架");

                entity.Property(e => e.BlIsTop)
                    .HasColumnName("BL_IsTop")
                    .HasComment("是否置頂");

                entity.Property(e => e.BlPostDateFrom)
                    .HasColumnType("date")
                    .HasColumnName("BL_PostDateFrom")
                    .HasComment("啟用日期");

                entity.Property(e => e.BlPostDateToxx)
                    .HasColumnType("date")
                    .HasColumnName("BL_PostDateToxx")
                    .HasComment("結束日期");

                entity.Property(e => e.BlTitle)
                    .HasMaxLength(200)
                    .HasColumnName("BL_Title")
                    .HasComment("公告標題");

                entity.Property(e => e.BlUpDatetime)
                    .HasColumnType("datetime")
                    .HasColumnName("BL_UpDatetime")
                    .HasComment("更新時間");

                entity.Property(e => e.BlUpUser)
                    .HasMaxLength(15)
                    .IsUnicode(false)
                    .HasColumnName("BL_UpUser")
                    .HasComment("更新者");
            });

            modelBuilder.Entity<SysBulletinsAttach>(entity =>
            {
                entity.HasKey(e => e.BlaId);

                entity.ToTable("SYS_BulletinsAttach");

                entity.Property(e => e.BlaId)
                    .HasColumnName("BLA_Id")
                    .HasComment("公告附檔流水號");

                entity.Property(e => e.BlId)
                    .HasColumnName("BL_Id")
                    .HasComment("公告流水號");

                entity.Property(e => e.BlaCrDatetime)
                    .HasColumnType("datetime")
                    .HasColumnName("BLA_CrDatetime")
                    .HasComment("建立時間");

                entity.Property(e => e.BlaCrUser)
                    .HasMaxLength(15)
                    .IsUnicode(false)
                    .HasColumnName("BLA_CrUser")
                    .HasComment("建立者");

                entity.Property(e => e.BlaFile)
                    .HasColumnName("BLA_File")
                    .HasComment("檔案");

                entity.Property(e => e.BlaFileSzie).HasColumnName("BLA_FileSzie");

                entity.Property(e => e.BlaFileType)
                    .HasMaxLength(100)
                    .IsUnicode(false)
                    .HasColumnName("BLA_FileType")
                    .HasComment("檔案類型");

                entity.Property(e => e.BlaOname)
                    .HasMaxLength(300)
                    .HasColumnName("BLA_OName")
                    .HasComment("檔案原名");

                entity.HasOne(d => d.Bl)
                    .WithMany(p => p.SysBulletinsAttach)
                    .HasForeignKey(d => d.BlId)
                    .HasConstraintName("FK_SYS_BulletinsAttach_SYS_Bulletins");
            });

            modelBuilder.Entity<SysBulletinsAttachDownloadRecord>(entity =>
            {
                entity.HasKey(e => new { e.BlaId, e.UserId });

                entity.ToTable("SYS_BulletinsAttachDownloadRecord");

                entity.Property(e => e.BlaId).HasColumnName("BLA_Id");

                entity.Property(e => e.UserId)
                    .HasMaxLength(15)
                    .IsUnicode(false);

                entity.Property(e => e.LastDownloadDate).HasColumnType("datetime");

                entity.HasOne(d => d.Bla)
                    .WithMany(p => p.SysBulletinsAttachDownloadRecord)
                    .HasForeignKey(d => d.BlaId)
                    .HasConstraintName("FK_SYS_BulletinsAttachDownloadRecord_SYS_BulletinsAttach");
            });

            modelBuilder.Entity<SysBulletinsClickRecord>(entity =>
            {
                entity.HasKey(e => new { e.BlId, e.UserId });

                entity.ToTable("SYS_BulletinsClickRecord");

                entity.Property(e => e.BlId).HasColumnName("BL_Id");

                entity.Property(e => e.UserId)
                    .HasMaxLength(15)
                    .IsUnicode(false);

                entity.Property(e => e.LastViewDate).HasColumnType("datetime");

                entity.HasOne(d => d.Bl)
                    .WithMany(p => p.SysBulletinsClickRecord)
                    .HasForeignKey(d => d.BlId)
                    .HasConstraintName("FK_SYS_BulletinsClickRecord_SYS_Bulletins");
            });

            modelBuilder.Entity<SysCode>(entity =>
            {
                entity.HasKey(e => new { e.Type, e.Code })
                    .HasName("PK_SYS_CODE");

                entity.ToTable("SYS_Code");

                entity.HasComment("系統代碼資料");

                entity.Property(e => e.Type)
                    .HasMaxLength(50)
                    .IsUnicode(false)
                    .HasComment("類型");

                entity.Property(e => e.Code)
                    .HasMaxLength(30)
                    .IsUnicode(false)
                    .HasComment("代碼");

                entity.Property(e => e.CodeDesc)
                    .IsRequired()
                    .HasMaxLength(300)
                    .HasComment("內容");

                entity.Property(e => e.CodeOrder).HasComment("排序");

                entity.Property(e => e.CreatedTime)
                    .HasColumnType("datetime")
                    .HasComment("建立時間");

                entity.Property(e => e.CreatedUserId)
                    .IsRequired()
                    .HasMaxLength(15)
                    .IsUnicode(false)
                    .HasComment("建立者");

                entity.Property(e => e.IsDisabled).HasComment("是否作廢");

                entity.Property(e => e.ParentCode)
                    .HasMaxLength(30)
                    .IsUnicode(false)
                    .HasComment("上層代碼");

                entity.Property(e => e.UpdatedTime)
                    .HasColumnType("datetime")
                    .HasComment("更新時間");

                entity.Property(e => e.UpdatedUserId)
                    .IsRequired()
                    .HasMaxLength(15)
                    .IsUnicode(false)
                    .HasComment("新增者");

                entity.HasOne(d => d.SysCodeNavigation)
                    .WithMany(p => p.InverseSysCodeNavigation)
                    .HasForeignKey(d => new { d.Type, d.ParentCode })
                    .HasConstraintName("FK_SYS_Code_SYS_Code");
            });

            modelBuilder.Entity<SysMenuFunc>(entity =>
            {
                entity.HasKey(e => new { e.System, e.FuncId });

                entity.ToTable("SYS_MenuFunc");

                entity.HasComment("系統選單資料");

                entity.Property(e => e.System)
                    .HasMaxLength(30)
                    .IsUnicode(false)
                    .HasComment("系統名稱");

                entity.Property(e => e.FuncId)
                    .HasMaxLength(50)
                    .IsUnicode(false)
                    .HasComment("系統項目識別編號");

                entity.Property(e => e.FuncName)
                    .IsRequired()
                    .HasMaxLength(50)
                    .HasComment("系統項目名稱");

                entity.Property(e => e.FuncOrder).HasComment("項目排序");

                entity.Property(e => e.ParentFuncId)
                    .IsRequired()
                    .HasMaxLength(50)
                    .IsUnicode(false)
                    .HasComment("項目路徑");
            });

            modelBuilder.Entity<SysProblemReport>(entity =>
            {
                entity.ToTable("SYS_ProblemReport");

                entity.HasComment("平台問題回報資料");

                entity.Property(e => e.Id).HasComment("流水號");

                entity.Property(e => e.CreatedTime)
                    .HasColumnType("datetime")
                    .HasComment("建立時間");

                entity.Property(e => e.CreatedUserId)
                    .IsRequired()
                    .HasMaxLength(15)
                    .IsUnicode(false)
                    .HasComment("建立者");

                entity.Property(e => e.DeptId)
                    .IsRequired()
                    .HasMaxLength(20)
                    .IsUnicode(false)
                    .HasComment("單位代碼");

                entity.Property(e => e.Menu)
                    .IsRequired()
                    .HasMaxLength(50)
                    .HasComment("功能頁籤");

                entity.Property(e => e.ProblemContent)
                    .IsRequired()
                    .HasMaxLength(200)
                    .HasComment("問題內容");

                entity.Property(e => e.ProblemType)
                    .IsRequired()
                    .HasMaxLength(10)
                    .IsUnicode(false)
                    .HasComment("問題類型");

                entity.Property(e => e.ProcessStatus)
                    .IsRequired()
                    .HasMaxLength(10)
                    .IsUnicode(false)
                    .HasComment("處理狀態");

                entity.Property(e => e.ReplyContent)
                    .IsRequired()
                    .HasMaxLength(200)
                    .HasComment("處理回報");

                entity.Property(e => e.ReplyTime).HasColumnType("datetime");

                entity.Property(e => e.ReplyUserId)
                    .HasMaxLength(15)
                    .IsUnicode(false)
                    .HasComment("處理人員");

                entity.Property(e => e.Subject)
                    .IsRequired()
                    .HasMaxLength(200)
                    .HasComment("問題主旨");

                entity.Property(e => e.System)
                    .IsRequired()
                    .HasMaxLength(30)
                    .IsUnicode(false);

                entity.Property(e => e.UpdatedTime)
                    .HasColumnType("datetime")
                    .HasComment("更新時間");

                entity.Property(e => e.UpdatedUserId)
                    .IsRequired()
                    .HasMaxLength(15)
                    .IsUnicode(false)
                    .HasComment("更新者");

                entity.HasOne(d => d.Dept)
                    .WithMany(p => p.SysProblemReport)
                    .HasForeignKey(d => d.DeptId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_SYS_ProblemReport_UserDept");

                entity.HasOne(d => d.ReplyUser)
                    .WithMany(p => p.SysProblemReport)
                    .HasForeignKey(d => d.ReplyUserId)
                    .HasConstraintName("FK_SYS_ProblemReport_UserInfo_Reply");
            });

            modelBuilder.Entity<SysProjectVersion>(entity =>
            {
                entity.HasKey(e => e.PId)
                    .HasName("PK_Project_Version");

                entity.ToTable("SYS_ProjectVersion");

                entity.Property(e => e.PId).HasColumnName("P_ID");

                entity.Property(e => e.PContent)
                    .HasColumnName("P_Content")
                    .HasComment("更新內容");

                entity.Property(e => e.PCrDateTime)
                    .HasColumnType("datetime")
                    .HasColumnName("P_CR_DateTime")
                    .HasComment("版更時間");

                entity.Property(e => e.PCrUser)
                    .HasMaxLength(50)
                    .HasColumnName("P_Cr_User")
                    .HasComment("上版人員");

                entity.Property(e => e.PProjectName)
                    .IsRequired()
                    .HasMaxLength(256)
                    .IsUnicode(false)
                    .HasColumnName("P_projectName")
                    .HasComment("專案代號");

                entity.Property(e => e.PVersionHash)
                    .HasMaxLength(256)
                    .IsUnicode(false)
                    .HasColumnName("P_versionHash")
                    .HasComment("git唯一值");

                entity.Property(e => e.PVersionName)
                    .HasMaxLength(256)
                    .IsUnicode(false)
                    .HasColumnName("P_versionName")
                    .HasComment("版號");
            });

            modelBuilder.Entity<SysRoleGroup>(entity =>
            {
                entity.HasKey(e => new { e.System, e.RoleGroupId })
                    .HasName("PK_SYS_RoleGroup_1");

                entity.ToTable("SYS_RoleGroup");

                entity.HasComment("角色群組資料");

                entity.Property(e => e.System)
                    .HasMaxLength(30)
                    .IsUnicode(false)
                    .HasComment("系統名稱");

                entity.Property(e => e.RoleGroupId)
                    .HasMaxLength(50)
                    .IsUnicode(false)
                    .HasComment("角色群組識別編號");

                entity.Property(e => e.CreatedTime)
                    .HasColumnType("datetime")
                    .HasComment("建立者");

                entity.Property(e => e.CreatedUserId)
                    .IsRequired()
                    .HasMaxLength(15)
                    .IsUnicode(false)
                    .HasComment("建立時間");

                entity.Property(e => e.IsAdmin).HasComment("是否為管理者");

                entity.Property(e => e.Name)
                    .IsRequired()
                    .HasMaxLength(50)
                    .HasComment("角色群組名稱");

                entity.Property(e => e.UpdatedTime)
                    .HasColumnType("datetime")
                    .HasComment("更新者");

                entity.Property(e => e.UpdatedUserId)
                    .IsRequired()
                    .HasMaxLength(15)
                    .IsUnicode(false)
                    .HasComment("更新時間");
            });

            modelBuilder.Entity<SysRoleGroupPermission>(entity =>
            {
                entity.HasKey(e => new { e.System, e.RoleGroupId, e.FuncId })
                    .HasName("PK_SYS_RoleGroupPermission_1");

                entity.ToTable("SYS_RoleGroupPermission");

                entity.Property(e => e.System)
                    .HasMaxLength(30)
                    .IsUnicode(false)
                    .HasComment("系統名稱");

                entity.Property(e => e.RoleGroupId)
                    .HasMaxLength(50)
                    .IsUnicode(false)
                    .HasComment("角色群組識別編號");

                entity.Property(e => e.FuncId)
                    .HasMaxLength(50)
                    .IsUnicode(false)
                    .HasComment("系統項目識別編號");

                entity.HasOne(d => d.SysMenuFunc)
                    .WithMany(p => p.SysRoleGroupPermission)
                    .HasForeignKey(d => new { d.System, d.FuncId })
                    .HasConstraintName("FK_SYS_RolePermission_SYS_MenuFunc");

                entity.HasOne(d => d.SysRoleGroup)
                    .WithMany(p => p.SysRoleGroupPermission)
                    .HasForeignKey(d => new { d.System, d.RoleGroupId })
                    .HasConstraintName("FK_SYS_RoleGroupPermission_SYS_RoleGroup");
            });

            modelBuilder.Entity<SysRoleGroupUser>(entity =>
            {
                entity.HasKey(e => new { e.System, e.RoleGroupId, e.UserId });

                entity.ToTable("SYS_RoleGroupUser");

                entity.Property(e => e.System)
                    .HasMaxLength(30)
                    .IsUnicode(false)
                    .HasComment("系統名稱");

                entity.Property(e => e.RoleGroupId)
                    .HasMaxLength(50)
                    .IsUnicode(false)
                    .HasComment("角色群組");

                entity.Property(e => e.UserId)
                    .HasMaxLength(15)
                    .IsUnicode(false)
                    .HasComment("權限群組人員");

                entity.HasOne(d => d.User)
                    .WithMany(p => p.SysRoleGroupUser)
                    .HasForeignKey(d => d.UserId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_SYS_RoleGroupUser_UserInfo");

                entity.HasOne(d => d.SysRoleGroup)
                    .WithMany(p => p.SysRoleGroupUser)
                    .HasForeignKey(d => new { d.System, d.RoleGroupId })
                    .HasConstraintName("FK_SYS_RoleGroupUser_SYS_RoleGroup");
            });

            modelBuilder.Entity<SysScheduleHistory>(entity =>
            {
                entity.HasKey(e => e.SsId);

                entity.ToTable("SYS_ScheduleHistory");

                entity.Property(e => e.SsId).HasColumnName("SS_ID");

                entity.Property(e => e.SsEndTime)
                    .HasColumnType("datetime")
                    .HasColumnName("SS_EndTime")
                    .HasComment("排程結束時間");

                entity.Property(e => e.SsError)
                    .IsUnicode(false)
                    .HasColumnName("SS_Error")
                    .HasComment("錯誤訊息");

                entity.Property(e => e.SsName)
                    .IsRequired()
                    .HasMaxLength(50)
                    .IsUnicode(false)
                    .HasColumnName("SS_Name")
                    .HasComment("排程名稱");

                entity.Property(e => e.SsStartTime)
                    .HasColumnType("datetime")
                    .HasColumnName("SS_StartTime")
                    .HasComment("排程開始時間");
            });

            modelBuilder.Entity<SysSystemSetting>(entity =>
            {
                entity.HasKey(e => new { e.Type, e.Key })
                    .HasName("PK_SYS_SystemSetting_1");

                entity.ToTable("SYS_SystemSetting");

                entity.HasComment("系統設定資料");

                entity.Property(e => e.Type)
                    .HasMaxLength(50)
                    .IsUnicode(false)
                    .HasComment("類別");

                entity.Property(e => e.Key)
                    .HasMaxLength(50)
                    .IsUnicode(false)
                    .HasComment("鍵");

                entity.Property(e => e.CreatedTime)
                    .HasColumnType("datetime")
                    .HasComment("建立時間");

                entity.Property(e => e.CreatedUserId)
                    .IsRequired()
                    .HasMaxLength(15)
                    .IsUnicode(false)
                    .HasComment("建立者");

                entity.Property(e => e.IsEnabled)
                    .IsRequired()
                    .HasDefaultValueSql("((1))")
                    .HasComment("是否啟用");

                entity.Property(e => e.Name)
                    .IsRequired()
                    .HasMaxLength(50)
                    .HasComment("詳細名稱");

                entity.Property(e => e.UpdatedTime)
                    .HasColumnType("datetime")
                    .HasComment("更新時間");

                entity.Property(e => e.UpdatedUserId)
                    .IsRequired()
                    .HasMaxLength(15)
                    .IsUnicode(false)
                    .HasComment("更新者");

                entity.Property(e => e.Value)
                    .HasMaxLength(100)
                    .HasComment("值");
            });

            modelBuilder.Entity<SysType>(entity =>
            {
                entity.HasKey(e => e.Type);

                entity.ToTable("SYS_Type");

                entity.HasComment("代碼類型資料");

                entity.Property(e => e.Type)
                    .HasMaxLength(50)
                    .IsUnicode(false)
                    .HasComment("類型");

                entity.Property(e => e.CanEdit).HasComment("是否可編輯");

                entity.Property(e => e.TypeDesc)
                    .HasMaxLength(300)
                    .HasComment("描述");
            });

            modelBuilder.Entity<SysUserRecord>(entity =>
            {
                entity.ToTable("SYS_UserRecord");

                entity.HasComment("使用者操作紀錄");

                entity.Property(e => e.Id).HasComment("流水號");

                entity.Property(e => e.DeptId)
                    .IsRequired()
                    .HasMaxLength(20)
                    .IsUnicode(false)
                    .HasComment("使用者部門");

                entity.Property(e => e.GradeCode)
                    .IsRequired()
                    .HasMaxLength(72)
                    .HasComment("職位");

                entity.Property(e => e.InputData)
                    .IsRequired()
                    .HasComment("輸入資料");

                entity.Property(e => e.Ip)
                    .IsRequired()
                    .HasMaxLength(40)
                    .IsUnicode(false)
                    .HasColumnName("IP")
                    .HasComment("IP");

                entity.Property(e => e.RecordEvent)
                    .IsRequired()
                    .HasMaxLength(50)
                    .HasComment("紀錄API");

                entity.Property(e => e.RecordTime)
                    .HasColumnType("datetime")
                    .HasComment("紀錄時間");

                entity.Property(e => e.UserId)
                    .IsRequired()
                    .HasMaxLength(15)
                    .IsUnicode(false)
                    .HasComment("使用者");

                entity.HasOne(d => d.Dept)
                    .WithMany(p => p.SysUserRecord)
                    .HasForeignKey(d => d.DeptId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_SYS_UserRecord_UserDept");

                entity.HasOne(d => d.User)
                    .WithMany(p => p.SysUserRecord)
                    .HasForeignKey(d => d.UserId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_SYS_UserRecord_UserInfo");
            });

            modelBuilder.Entity<UserDept>(entity =>
            {
                entity.HasComment("組織部門資料");

                entity.Property(e => e.Id)
                    .HasMaxLength(20)
                    .IsUnicode(false)
                    .HasComment("部門代碼");

                entity.Property(e => e.CreatedTime)
                    .HasColumnType("datetime")
                    .HasComment("建立時間");

                entity.Property(e => e.CreatedUserId)
                    .IsRequired()
                    .HasMaxLength(15)
                    .IsUnicode(false)
                    .HasComment("建立者");

                entity.Property(e => e.DeptName)
                    .IsRequired()
                    .HasMaxLength(30)
                    .HasComment("部門名稱");

                entity.Property(e => e.IsDisabled).HasComment("是否刪除");

                entity.Property(e => e.LeaderUserId)
                    .HasMaxLength(15)
                    .IsUnicode(false)
                    .HasComment("部門主管員工編號");

                entity.Property(e => e.ParentDeptId)
                    .HasMaxLength(20)
                    .IsUnicode(false)
                    .HasComment("上層部門代碼");

                entity.Property(e => e.UpdatedTime)
                    .HasColumnType("datetime")
                    .HasComment("更新時間");

                entity.Property(e => e.UpdatedUserId)
                    .IsRequired()
                    .HasMaxLength(15)
                    .IsUnicode(false)
                    .HasComment("更新者");

                entity.HasOne(d => d.LeaderUser)
                    .WithMany(p => p.UserDept)
                    .HasForeignKey(d => d.LeaderUserId)
                    .HasConstraintName("FK_UserDept_UserInfo");

                entity.HasOne(d => d.ParentDept)
                    .WithMany(p => p.InverseParentDept)
                    .HasForeignKey(d => d.ParentDeptId)
                    .HasConstraintName("FK_UserDept_UserDept");
            });

            modelBuilder.Entity<UserDeptPermission>(entity =>
            {
                entity.HasKey(e => new { e.System, e.DeptId, e.FuncId });

                entity.Property(e => e.System)
                    .HasMaxLength(30)
                    .IsUnicode(false)
                    .HasComment("系統名稱");

                entity.Property(e => e.DeptId)
                    .HasMaxLength(20)
                    .IsUnicode(false)
                    .HasComment("部門識別編號");

                entity.Property(e => e.FuncId)
                    .HasMaxLength(50)
                    .IsUnicode(false)
                    .HasComment("系統項目識別編號");

                entity.HasOne(d => d.Dept)
                    .WithMany(p => p.UserDeptPermission)
                    .HasForeignKey(d => d.DeptId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_UserDeptPermission_UserDept");

                entity.HasOne(d => d.SysMenuFunc)
                    .WithMany(p => p.UserDeptPermission)
                    .HasForeignKey(d => new { d.System, d.FuncId })
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_UserDeptPermission_SYS_MenuFunc");
            });

            modelBuilder.Entity<UserGrade>(entity =>
            {
                entity.Property(e => e.Id)
                    .HasMaxLength(20)
                    .IsUnicode(false)
                    .HasComment("職稱代碼");

                entity.Property(e => e.CreatedTime)
                    .HasColumnType("datetime")
                    .HasComment("建立時間");

                entity.Property(e => e.CreatedUserId)
                    .IsRequired()
                    .HasMaxLength(15)
                    .IsUnicode(false)
                    .HasComment("建立者");

                entity.Property(e => e.GradeName)
                    .IsRequired()
                    .HasMaxLength(30)
                    .HasComment("職稱");

                entity.Property(e => e.IsDisabled).HasComment("是否刪除");

                entity.Property(e => e.ParentGradeId)
                    .HasMaxLength(20)
                    .IsUnicode(false)
                    .HasComment("上層職稱代碼");

                entity.Property(e => e.UpdatedTime)
                    .HasColumnType("datetime")
                    .HasComment("更新時間");

                entity.Property(e => e.UpdatedUserId)
                    .IsRequired()
                    .HasMaxLength(15)
                    .IsUnicode(false)
                    .HasComment("更新者");

                entity.HasOne(d => d.ParentGrade)
                    .WithMany(p => p.InverseParentGrade)
                    .HasForeignKey(d => d.ParentGradeId)
                    .HasConstraintName("FK_UserGrade_UserGrade");
            });

            modelBuilder.Entity<UserGradePermission>(entity =>
            {
                entity.HasKey(e => new { e.System, e.GradeId, e.FuncId });

                entity.Property(e => e.System)
                    .HasMaxLength(30)
                    .IsUnicode(false)
                    .HasComment("系統名稱");

                entity.Property(e => e.GradeId)
                    .HasMaxLength(20)
                    .IsUnicode(false)
                    .HasComment("職稱識別編號");

                entity.Property(e => e.FuncId)
                    .HasMaxLength(50)
                    .IsUnicode(false)
                    .HasComment("系統項目識別編號");

                entity.HasOne(d => d.Grade)
                    .WithMany(p => p.UserGradePermission)
                    .HasForeignKey(d => d.GradeId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_UserGradePermission_UserGrade");

                entity.HasOne(d => d.SysMenuFunc)
                    .WithMany(p => p.UserGradePermission)
                    .HasForeignKey(d => new { d.System, d.FuncId })
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_UserGradePermission_SYS_MenuFunc");
            });

            modelBuilder.Entity<UserInfo>(entity =>
            {
                entity.HasComment("人員基本資料");

                entity.Property(e => e.Id)
                    .HasMaxLength(15)
                    .IsUnicode(false)
                    .HasComment("員工編號");

                entity.Property(e => e.CreatedTime)
                    .HasColumnType("datetime")
                    .HasComment("建立時間");

                entity.Property(e => e.CreatedUserId)
                    .IsRequired()
                    .HasMaxLength(15)
                    .IsUnicode(false)
                    .HasComment("建立者");

                entity.Property(e => e.DeptId)
                    .HasMaxLength(20)
                    .IsUnicode(false)
                    .HasComment("部門");

                entity.Property(e => e.Email)
                    .IsRequired()
                    .HasMaxLength(254)
                    .IsUnicode(false)
                    .HasComment("電子郵件");

                entity.Property(e => e.EnabledTtime)
                    .HasColumnType("datetime")
                    .HasComment("啟用時間");

                entity.Property(e => e.GradeCode)
                    .HasMaxLength(20)
                    .IsUnicode(false)
                    .HasComment("職位");

                entity.Property(e => e.IdNo)
                    .IsRequired()
                    .HasMaxLength(15)
                    .IsUnicode(false)
                    .HasComment("身分證字號");

                entity.Property(e => e.IsDisabled).HasComment("是否刪除");

                entity.Property(e => e.IsEnabled).HasComment("是否啟用");

                entity.Property(e => e.LastLoginIp)
                    .IsRequired()
                    .HasMaxLength(50)
                    .IsUnicode(false)
                    .HasColumnName("LastLoginIP")
                    .HasComment("最後一次登入IP");

                entity.Property(e => e.LastLoginTime)
                    .HasColumnType("datetime")
                    .HasComment("最後登入時間");

                entity.Property(e => e.LastLogoutTime)
                    .HasColumnType("datetime")
                    .HasComment("最後登出時間");

                entity.Property(e => e.LoginFailedCount).HasComment("登入失敗次數加總");

                entity.Property(e => e.Name)
                    .IsRequired()
                    .HasMaxLength(15)
                    .HasComment("人員姓名");

                entity.Property(e => e.Pw)
                    .IsRequired()
                    .HasMaxLength(255)
                    .IsUnicode(false)
                    .HasComment("密碼");

                entity.Property(e => e.UpdatedTime)
                    .HasColumnType("datetime")
                    .HasComment("更新時間");

                entity.Property(e => e.UpdatedUserId)
                    .IsRequired()
                    .HasMaxLength(15)
                    .IsUnicode(false)
                    .HasComment("更新者");

                entity.HasOne(d => d.Dept)
                    .WithMany(p => p.UserInfo)
                    .HasForeignKey(d => d.DeptId)
                    .HasConstraintName("FK_UserInfo_UserDept");

                entity.HasOne(d => d.GradeCodeNavigation)
                    .WithMany(p => p.UserInfo)
                    .HasForeignKey(d => d.GradeCode)
                    .HasConstraintName("FK_UserInfo_UserGrade");
            });

            modelBuilder.Entity<UserPasswordHistory>(entity =>
            {
                entity.HasComment("密碼歷史資料");

                entity.Property(e => e.Id).HasComment("流水號");

                entity.Property(e => e.CreatedTime)
                    .HasColumnType("datetime")
                    .HasComment("建立時間");

                entity.Property(e => e.CreatedUserId)
                    .IsRequired()
                    .HasMaxLength(15)
                    .IsUnicode(false)
                    .HasComment("建立者");

                entity.Property(e => e.Pw)
                    .IsRequired()
                    .HasMaxLength(255)
                    .IsUnicode(false)
                    .HasComment("密碼");

                entity.Property(e => e.UserId)
                    .IsRequired()
                    .HasMaxLength(15)
                    .IsUnicode(false)
                    .HasComment("員工編號");

                entity.HasOne(d => d.User)
                    .WithMany(p => p.UserPasswordHistory)
                    .HasForeignKey(d => d.UserId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_UserPasswordHistory_UserInfo");
            });

            OnModelCreatingPartial(modelBuilder);
        }

        partial void OnModelCreatingPartial(ModelBuilder modelBuilder);
    }
}
