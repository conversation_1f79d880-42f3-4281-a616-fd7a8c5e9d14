#### [AlifeApi.DataAccess](index.md 'index')
### [AlifeApi.DataAccess.ProjectContext](AlifeApi.DataAccess.ProjectContext.md 'AlifeApi.DataAccess.ProjectContext')

## SysBulletins Class

```csharp
public class SysBulletins
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; SysBulletins
### Properties

<a name='AlifeApi.DataAccess.ProjectContext.SysBulletins.BlContent'></a>

## SysBulletins.BlContent Property

公告內容

```csharp
public string BlContent { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContext.SysBulletins.BlCrDatetime'></a>

## SysBulletins.BlCrDatetime Property

建立時間

```csharp
public System.Nullable<System.DateTime> BlCrDatetime { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.DateTime](https://docs.microsoft.com/en-us/dotnet/api/System.DateTime 'System.DateTime')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

<a name='AlifeApi.DataAccess.ProjectContext.SysBulletins.BlCrUser'></a>

## SysBulletins.BlCrUser Property

建立者

```csharp
public string BlCrUser { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContext.SysBulletins.BlId'></a>

## SysBulletins.BlId Property

公告流水號

```csharp
public int BlId { get; set; }
```

#### Property Value
[System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')

<a name='AlifeApi.DataAccess.ProjectContext.SysBulletins.BlIsEnable'></a>

## SysBulletins.BlIsEnable Property

是否上架

```csharp
public bool BlIsEnable { get; set; }
```

#### Property Value
[System.Boolean](https://docs.microsoft.com/en-us/dotnet/api/System.Boolean 'System.Boolean')

<a name='AlifeApi.DataAccess.ProjectContext.SysBulletins.BlIsTop'></a>

## SysBulletins.BlIsTop Property

是否置頂

```csharp
public bool BlIsTop { get; set; }
```

#### Property Value
[System.Boolean](https://docs.microsoft.com/en-us/dotnet/api/System.Boolean 'System.Boolean')

<a name='AlifeApi.DataAccess.ProjectContext.SysBulletins.BlPostDateFrom'></a>

## SysBulletins.BlPostDateFrom Property

啟用日期

```csharp
public System.Nullable<System.DateTime> BlPostDateFrom { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.DateTime](https://docs.microsoft.com/en-us/dotnet/api/System.DateTime 'System.DateTime')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

<a name='AlifeApi.DataAccess.ProjectContext.SysBulletins.BlPostDateToxx'></a>

## SysBulletins.BlPostDateToxx Property

結束日期

```csharp
public System.Nullable<System.DateTime> BlPostDateToxx { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.DateTime](https://docs.microsoft.com/en-us/dotnet/api/System.DateTime 'System.DateTime')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

<a name='AlifeApi.DataAccess.ProjectContext.SysBulletins.BlTitle'></a>

## SysBulletins.BlTitle Property

公告標題

```csharp
public string BlTitle { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContext.SysBulletins.BlUpDatetime'></a>

## SysBulletins.BlUpDatetime Property

更新時間

```csharp
public System.Nullable<System.DateTime> BlUpDatetime { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.DateTime](https://docs.microsoft.com/en-us/dotnet/api/System.DateTime 'System.DateTime')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

<a name='AlifeApi.DataAccess.ProjectContext.SysBulletins.BlUpUser'></a>

## SysBulletins.BlUpUser Property

更新者

```csharp
public string BlUpUser { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')