#### [AlifeApi.BusinessRules](index.md 'index')
### [AlifeApi.BusinessRules.DepartmentModels](AlifeApi.BusinessRules.DepartmentModels.md 'AlifeApi.BusinessRules.DepartmentModels')

## DepartmentListGetInput Class

取得部門列表查詢條件

```csharp
public class DepartmentListGetInput : AlifeApi.BusinessRules.Infrastructure.PagedListInput
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; [PagedListInput](AlifeApi.BusinessRules.Infrastructure.PagedListInput.md 'AlifeApi.BusinessRules.Infrastructure.PagedListInput') &#129106; DepartmentListGetInput
### Properties

<a name='AlifeApi.BusinessRules.DepartmentModels.DepartmentListGetInput.CompanyId'></a>

## DepartmentListGetInput.CompanyId Property

公司ID (可選篩選條件)

```csharp
public string CompanyId { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.DepartmentModels.DepartmentListGetInput.Name'></a>

## DepartmentListGetInput.Name Property

部門名稱 (可選篩選條件)

```csharp
public string Name { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')