#### [AlifeApi.BusinessRules](index.md 'index')
### [AlifeApi.BusinessRules.LoginModels](AlifeApi.BusinessRules.LoginModels.md 'AlifeApi.BusinessRules.LoginModels')

## LoginServicePg Class

處理使用者登入邏輯的服務類別

```csharp
public class LoginServicePg : AlifeApi.BusinessRules.Infrastructure.ServiceBase<AlifeApi.DataAccess.ProjectContent.alifeContext>
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; [AlifeApi.Common.InfrastructureBase](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.Common.InfrastructureBase 'AlifeApi.Common.InfrastructureBase') &#129106; [AlifeApi.BusinessRules.Infrastructure.ServiceBase&lt;](AlifeApi.BusinessRules.Infrastructure.ServiceBase_TDbContext_.md 'AlifeApi.BusinessRules.Infrastructure.ServiceBase<TDbContext>')[AlifeApi.DataAccess.ProjectContent.alifeContext](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.DataAccess.ProjectContent.alifeContext 'AlifeApi.DataAccess.ProjectContent.alifeContext')[&gt;](AlifeApi.BusinessRules.Infrastructure.ServiceBase_TDbContext_.md 'AlifeApi.BusinessRules.Infrastructure.ServiceBase<TDbContext>') &#129106; LoginServicePg
### Methods

<a name='AlifeApi.BusinessRules.LoginModels.LoginServicePg.UserLoginAsync(AlifeApi.BusinessRules.LoginModels.UserLoginInput)'></a>

## LoginServicePg.UserLoginAsync(UserLoginInput) Method

使用者登入處理

```csharp
public System.Threading.Tasks.Task<AlifeApi.BusinessRules.LoginModels.UserLoginPgOutput> UserLoginAsync(AlifeApi.BusinessRules.LoginModels.UserLoginInput input);
```
#### Parameters

<a name='AlifeApi.BusinessRules.LoginModels.LoginServicePg.UserLoginAsync(AlifeApi.BusinessRules.LoginModels.UserLoginInput).input'></a>

`input` [UserLoginInput](AlifeApi.BusinessRules.LoginModels.UserLoginInput.md 'AlifeApi.BusinessRules.LoginModels.UserLoginInput')

登入輸入資料

#### Returns
[System.Threading.Tasks.Task&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')[UserLoginPgOutput](AlifeApi.BusinessRules.LoginModels.UserLoginPgOutput.md 'AlifeApi.BusinessRules.LoginModels.UserLoginPgOutput')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')  
登入結果