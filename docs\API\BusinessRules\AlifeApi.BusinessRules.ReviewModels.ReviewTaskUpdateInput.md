#### [AlifeApi.BusinessRules](index.md 'index')
### [AlifeApi.BusinessRules.ReviewModels](AlifeApi.BusinessRules.ReviewModels.md 'AlifeApi.BusinessRules.ReviewModels')

## ReviewTaskUpdateInput Class

審核流程更新輸入資料

```csharp
public class ReviewTaskUpdateInput
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; ReviewTaskUpdateInput
### Properties

<a name='AlifeApi.BusinessRules.ReviewModels.ReviewTaskUpdateInput.Deadline'></a>

## ReviewTaskUpdateInput.Deadline Property

截止日期

```csharp
public System.Nullable<System.DateTime> Deadline { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.DateTime](https://docs.microsoft.com/en-us/dotnet/api/System.DateTime 'System.DateTime')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

<a name='AlifeApi.BusinessRules.ReviewModels.ReviewTaskUpdateInput.Description'></a>

## ReviewTaskUpdateInput.Description Property

任務描述

```csharp
public string Description { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.ReviewModels.ReviewTaskUpdateInput.Name'></a>

## ReviewTaskUpdateInput.Name Property

任務名稱

```csharp
public string Name { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.ReviewModels.ReviewTaskUpdateInput.Status'></a>

## ReviewTaskUpdateInput.Status Property

任務狀態，可選值為 enable（啟用）、disable（停用）、deleted（刪除）

```csharp
public string? Status { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.ReviewModels.ReviewTaskUpdateInput.Steps'></a>

## ReviewTaskUpdateInput.Steps Property

審核步驟清單

```csharp
public System.Collections.Generic.List<AlifeApi.BusinessRules.ReviewModels.ReviewStepInput> Steps { get; set; }
```

#### Property Value
[System.Collections.Generic.List&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Collections.Generic.List-1 'System.Collections.Generic.List`1')[ReviewStepInput](AlifeApi.BusinessRules.ReviewModels.ReviewStepInput.md 'AlifeApi.BusinessRules.ReviewModels.ReviewStepInput')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Collections.Generic.List-1 'System.Collections.Generic.List`1')