#### [AlifeApi.BusinessRules](index.md 'index')
### [AlifeApi.BusinessRules.ParkingSpaceModels](AlifeApi.BusinessRules.ParkingSpaceModels.md 'AlifeApi.BusinessRules.ParkingSpaceModels')

## ParkingSpaceSalesStatistics Class

停車位銷售統計

```csharp
public class ParkingSpaceSalesStatistics
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; ParkingSpaceSalesStatistics
### Properties

<a name='AlifeApi.BusinessRules.ParkingSpaceModels.ParkingSpaceSalesStatistics.AvailableCount'></a>

## ParkingSpaceSalesStatistics.AvailableCount Property

可售數量

```csharp
public int AvailableCount { get; set; }
```

#### Property Value
[System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')

<a name='AlifeApi.BusinessRules.ParkingSpaceModels.ParkingSpaceSalesStatistics.BookedCount'></a>

## ParkingSpaceSalesStatistics.BookedCount Property

已預訂數量

```csharp
public int BookedCount { get; set; }
```

#### Property Value
[System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')

<a name='AlifeApi.BusinessRules.ParkingSpaceModels.ParkingSpaceSalesStatistics.OwnerReservedCount'></a>

## ParkingSpaceSalesStatistics.OwnerReservedCount Property

地主保留數量

```csharp
public int OwnerReservedCount { get; set; }
```

#### Property Value
[System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')

<a name='AlifeApi.BusinessRules.ParkingSpaceModels.ParkingSpaceSalesStatistics.ReservedCount'></a>

## ParkingSpaceSalesStatistics.ReservedCount Property

保留數量

```csharp
public int ReservedCount { get; set; }
```

#### Property Value
[System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')

<a name='AlifeApi.BusinessRules.ParkingSpaceModels.ParkingSpaceSalesStatistics.SalesRate'></a>

## ParkingSpaceSalesStatistics.SalesRate Property

銷售率 (%)

```csharp
public decimal SalesRate { get; set; }
```

#### Property Value
[System.Decimal](https://docs.microsoft.com/en-us/dotnet/api/System.Decimal 'System.Decimal')

<a name='AlifeApi.BusinessRules.ParkingSpaceModels.ParkingSpaceSalesStatistics.SiteCode'></a>

## ParkingSpaceSalesStatistics.SiteCode Property

案場編號

```csharp
public string SiteCode { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.ParkingSpaceModels.ParkingSpaceSalesStatistics.SoldCount'></a>

## ParkingSpaceSalesStatistics.SoldCount Property

已售數量

```csharp
public int SoldCount { get; set; }
```

#### Property Value
[System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')

<a name='AlifeApi.BusinessRules.ParkingSpaceModels.ParkingSpaceSalesStatistics.TotalAvailableListPrice'></a>

## ParkingSpaceSalesStatistics.TotalAvailableListPrice Property

可售總表價

```csharp
public decimal TotalAvailableListPrice { get; set; }
```

#### Property Value
[System.Decimal](https://docs.microsoft.com/en-us/dotnet/api/System.Decimal 'System.Decimal')

<a name='AlifeApi.BusinessRules.ParkingSpaceModels.ParkingSpaceSalesStatistics.TotalCount'></a>

## ParkingSpaceSalesStatistics.TotalCount Property

總數量

```csharp
public int TotalCount { get; set; }
```

#### Property Value
[System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')

<a name='AlifeApi.BusinessRules.ParkingSpaceModels.ParkingSpaceSalesStatistics.TotalReservedListPrice'></a>

## ParkingSpaceSalesStatistics.TotalReservedListPrice Property

保留總表價

```csharp
public decimal TotalReservedListPrice { get; set; }
```

#### Property Value
[System.Decimal](https://docs.microsoft.com/en-us/dotnet/api/System.Decimal 'System.Decimal')