﻿using System.Text;
using System.IO; // 需要 System.IO for MemoryStream
using SharpCompress.Archives; // SharpCompress 基本命名空間
using SharpCompress.Archives.Zip; // Zip 相關
using SharpCompress.Writers; // Writer 相關
using SharpCompress.Common; // CompressionType

namespace AlifeApi.Common.Util
{
    public static class ZipUtils
    {
        /// <summary>
        /// 根據檔案路徑列表創建 Zip 壓縮檔 (使用 SharpCompress)
        /// </summary>
        /// <param name="filePaths">要壓縮的檔案路徑集合</param>
        /// <returns>包含 Zip 檔案內容的 byte 陣列</returns>
        public static byte[] CreateZip(IEnumerable<string> filePaths)
        {
            using (var memoryStream = new MemoryStream())
            using (var archive = ZipArchive.Create())
            {
                foreach (var filePath in filePaths)
                {
                    if (File.Exists(filePath))
                    {
                        // 將實體檔案加入壓縮檔，第二個參數是壓縮檔內的相對路徑/檔名
                        archive.AddEntry(Path.GetFileName(filePath), filePath);
                    }
                    // 可以選擇在這裡處理檔案不存在的情況，例如拋出例外或記錄日誌
                }
                // 設定壓縮選項 (例如使用 Deflate 壓縮)
                archive.SaveTo(memoryStream, new WriterOptions(CompressionType.Deflate));
                return memoryStream.ToArray();
            }
        }

        /// <summary>
        /// 根據檔名和 Base64 (byte[]) 內容列表創建 Zip 壓縮檔 (使用 SharpCompress)
        /// </summary>
        /// <param name="fileContents">包含檔名和檔案內容 (byte[]) 的集合</param>
        /// <returns>包含 Zip 檔案內容的 byte 陣列</returns>
        public static byte[] CreateZipByBase64(IEnumerable<FileList> fileContents)
        {
            using (var memoryStream = new MemoryStream())
            using (var archive = ZipArchive.Create())
            {
                foreach (var content in fileContents)
                {
                    if (content.File != null && content.File.Length > 0)
                    {
                        // 從 MemoryStream 加入 Entry
                        using (var entryStream = new MemoryStream(content.File))
                        {
                            archive.AddEntry(content.FileName, entryStream, true); // true 表示 Stream 會在加入後關閉
                        }
                    }
                }
                archive.SaveTo(memoryStream, new WriterOptions(CompressionType.Deflate));
                return memoryStream.ToArray();
            }
        }

        /// <summary>
        /// 內部類別，用於傳遞檔名和檔案內容
        /// </summary>
        public class FileList
        {
            public string FileName { get; set; }
            public byte[] File { get; set; }
        }
    }
}
