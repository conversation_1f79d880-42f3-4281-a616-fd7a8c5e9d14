#### [AlifeApi.BusinessRules](index.md 'index')
### [AlifeApi.BusinessRules.BuildingModels](AlifeApi.BusinessRules.BuildingModels.md 'AlifeApi.BusinessRules.BuildingModels')

## BuildingListOutput Class

建築物列表輸出項目

```csharp
public class BuildingListOutput
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; BuildingListOutput
### Properties

<a name='AlifeApi.BusinessRules.BuildingModels.BuildingListOutput.BuildingId'></a>

## BuildingListOutput.BuildingId Property

建築物唯一識別碼

```csharp
public int BuildingId { get; set; }
```

#### Property Value
[System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')

<a name='AlifeApi.BusinessRules.BuildingModels.BuildingListOutput.BuildingName'></a>

## BuildingListOutput.BuildingName Property

建築物名稱

```csharp
public string BuildingName { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.BuildingModels.BuildingListOutput.BuildingType'></a>

## BuildingListOutput.BuildingType Property

建築物類型

```csharp
public string? BuildingType { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.BuildingModels.BuildingListOutput.CompletionDate'></a>

## BuildingListOutput.CompletionDate Property

完工日期

```csharp
public System.Nullable<System.DateOnly> CompletionDate { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.DateOnly](https://docs.microsoft.com/en-us/dotnet/api/System.DateOnly 'System.DateOnly')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

<a name='AlifeApi.BusinessRules.BuildingModels.BuildingListOutput.CreatedTime'></a>

## BuildingListOutput.CreatedTime Property

建立時間

```csharp
public System.DateTime CreatedTime { get; set; }
```

#### Property Value
[System.DateTime](https://docs.microsoft.com/en-us/dotnet/api/System.DateTime 'System.DateTime')

<a name='AlifeApi.BusinessRules.BuildingModels.BuildingListOutput.SiteCode'></a>

## BuildingListOutput.SiteCode Property

案場代碼

```csharp
public string SiteCode { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.BuildingModels.BuildingListOutput.SiteName'></a>

## BuildingListOutput.SiteName Property

案場名稱

```csharp
public string? SiteName { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.BuildingModels.BuildingListOutput.TotalAboveGroundFloors'></a>

## BuildingListOutput.TotalAboveGroundFloors Property

地上樓層總數

```csharp
public System.Nullable<int> TotalAboveGroundFloors { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

<a name='AlifeApi.BusinessRules.BuildingModels.BuildingListOutput.TotalBelowGroundFloors'></a>

## BuildingListOutput.TotalBelowGroundFloors Property

地下樓層總數

```csharp
public System.Nullable<int> TotalBelowGroundFloors { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')