﻿using System.Text.Json.Serialization;

namespace AlifeApi.BusinessRules.RoleGroupModels
{
    /// <summary>
    /// 權限
    /// </summary>
    public class MenuTreeOutput
    {
        /// <summary>
        /// Name
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// ID
        /// </summary>
        public string Id { get; set; }

        /// <summary>
        /// Selected
        /// </summary>
        public bool Selected { get; set; }

        /// <summary>
        /// Children
        /// </summary>
        [JsonPropertyName("Children")]
        public IEnumerable<MenuTreeOutput> Childrens { get; set; } = Enumerable.Empty<MenuTreeOutput>();
    }
}
