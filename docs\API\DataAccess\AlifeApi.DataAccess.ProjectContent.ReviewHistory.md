#### [AlifeApi.DataAccess](index.md 'index')
### [AlifeApi.DataAccess.ProjectContent](AlifeApi.DataAccess.ProjectContent.md 'AlifeApi.DataAccess.ProjectContent')

## ReviewHistory Class

審核歷史記錄表，用於儲存審核任務的歷史操作記錄。

```csharp
public class ReviewHistory
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; ReviewHistory
### Properties

<a name='AlifeApi.DataAccess.ProjectContent.ReviewHistory.Action'></a>

## ReviewHistory.Action Property

操作類型，可選值為 approve（批准）、reject（拒絕）、return（退回）、comment（評論）。

```csharp
public string Action { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContent.ReviewHistory.Comment'></a>

## ReviewHistory.Comment Property

操作評論，提供操作的詳細說明或意見。

```csharp
public string Comment { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContent.ReviewHistory.HistoryId'></a>

## ReviewHistory.HistoryId Property

歷史記錄流水號，主鍵，自動遞增，用於唯一識別歷史記錄。

```csharp
public int HistoryId { get; set; }
```

#### Property Value
[System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')

<a name='AlifeApi.DataAccess.ProjectContent.ReviewHistory.StepId'></a>

## ReviewHistory.StepId Property

步驟流水號，對應 ReviewSteps 表的 StepId。

```csharp
public System.Nullable<int> StepId { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

<a name='AlifeApi.DataAccess.ProjectContent.ReviewHistory.TaskId'></a>

## ReviewHistory.TaskId Property

任務流水號，對應 ReviewTasks 表的 TaskId。

```csharp
public System.Nullable<int> TaskId { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

<a name='AlifeApi.DataAccess.ProjectContent.ReviewHistory.Timestamp'></a>

## ReviewHistory.Timestamp Property

操作時間，記錄操作發生的時間，預設為當前時間。

```csharp
public System.Nullable<System.DateTime> Timestamp { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.DateTime](https://docs.microsoft.com/en-us/dotnet/api/System.DateTime 'System.DateTime')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

<a name='AlifeApi.DataAccess.ProjectContent.ReviewHistory.UserInfoId'></a>

## ReviewHistory.UserInfoId Property

使用者編號，對應 UserInfo 表的 UserInfoId。

```csharp
public string UserInfoId { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')