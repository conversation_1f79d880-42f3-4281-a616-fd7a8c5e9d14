using AlifeApi.BusinessRules.Infrastructure;
using AlifeApi.DataAccess.ProjectContent;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.Json;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;

namespace AlifeApi.BusinessRules.UnitModels
{
    public class UnitService : ServiceBase<alifeContext>
    {
        public UnitService(IServiceProvider serviceProvider, alifeContext dbContext) : base(serviceProvider, dbContext)
        {
        }

        public async Task<PagedListOutput<UnitOutput>> GetListAsync(UnitListInput input)
        {
            var query = from unit in Db.Units
                        join building in Db.Buildings on unit.BuildingId equals building.BuildingId
                        join site in Db.Sites on unit.SiteCode equals site.SiteCode
                        join floor in Db.Floors on unit.FloorId equals floor.FloorId
                        select new { unit, building, site, floor };


            if (!string.IsNullOrEmpty(input.SiteCode))
            {
                query = query.Where(x => x.unit.SiteCode == input.SiteCode);
            }

            if (input.BuildingId.HasValue)
            {
                query = query.Where(x => x.unit.BuildingId == input.BuildingId.Value);
            }

            var pagedResult = await query.Select(x => new UnitOutput
            {
                UnitId = x.unit.UnitId,
                FloorId = x.unit.FloorId,
                BuildingId = x.unit.BuildingId,
                SiteCode = x.unit.SiteCode,
                Layout = x.unit.Layout,
                Orientation = x.unit.Orientation,
                MainArea = x.unit.MainArea,
                AuxiliaryArea = x.unit.AuxiliaryArea,
                BalconyArea = x.unit.BalconyArea,
                AwningArea = x.unit.AwningArea,
                SmallPublicArea = x.unit.SmallPublicArea,
                LargePublicArea = x.unit.LargePublicArea,
                PublicAreaShare = x.unit.PublicAreaShare,
                TotalArea = x.unit.TotalArea,
                ListPrice = x.unit.ListPrice,
                MinimumPrice = x.unit.MinimumPrice,
                TransactionPrice = x.unit.TransactionPrice,
                Status = x.unit.Status,
                Remarks = x.unit.Remarks,
                CreatedTime = x.unit.CreatedTime,
                UpdatedTime = x.unit.UpdatedTime,
                CreatedUserInfoId = x.unit.CreatedUserInfoId,
                UpdatedUserInfoId = x.unit.UpdatedUserInfoId,
                SiteName = x.site.SiteName,
                BuildingName = x.building.BuildingName,
                FloorLabel = x.floor.FloorLabel
            }).ToPagedListOutputAsync(input);
            
            // 可以在此處加入查詢使用者姓名的邏輯，類似 SiteService

            return pagedResult;
        }

        public async Task<UnitOutput> GetAsync(int unitId)
        {
            var unit = await (from u in Db.Units
                              join b in Db.Buildings on u.BuildingId equals b.BuildingId
                              join s in Db.Sites on u.SiteCode equals s.SiteCode
                              join f in Db.Floors on u.FloorId equals f.FloorId
                              where u.UnitId == unitId
                              select new UnitOutput
                              {
                                  UnitId = u.UnitId,
                                  FloorId = u.FloorId,
                                  BuildingId = u.BuildingId,
                                  SiteCode = u.SiteCode,
                                  UnitNumber = u.UnitNumber,
                                  UnitType = u.UnitType,
                                  Layout = u.Layout,
                                  Orientation = u.Orientation,
                                  MainArea = u.MainArea,
                                  AuxiliaryArea = u.AuxiliaryArea,
                                  BalconyArea = u.BalconyArea,
                                  AwningArea = u.AwningArea,
                                  SmallPublicArea = u.SmallPublicArea,
                                  LargePublicArea = u.LargePublicArea,
                                  PublicAreaShare = u.PublicAreaShare,
                                  TotalArea = u.TotalArea,
                                  ListPrice = u.ListPrice,
                                  MinimumPrice = u.MinimumPrice,
                                  TransactionPrice = u.TransactionPrice,
                                  Status = u.Status,
                                  Remarks = u.Remarks,
                                  CreatedTime = u.CreatedTime,
                                  UpdatedTime = u.UpdatedTime,
                                  CreatedUserInfoId = u.CreatedUserInfoId,
                                  UpdatedUserInfoId = u.UpdatedUserInfoId,
                                  SiteName = s.SiteName,
                                  BuildingName = b.BuildingName,
                                  FloorLabel = f.FloorLabel
                              })
                .FirstOrDefaultAsync();

            return unit;
        }

        public async Task<Unit> CreateAsync(UnitCreateInput input)
        {
            // 檢查 FloorId 是否存在，並取得相關的 SiteCode 和 BuildingId
            var floor = await Db.Floors.FindAsync(input.FloorId);
            if (floor == null)
            {
                throw new KeyNotFoundException("指定的樓層 ID 不存在。");
            }

            var unit = new Unit
            {
                FloorId = input.FloorId,
                BuildingId = floor.BuildingId,
                SiteCode = floor.SiteCode,
                UnitNumber = input.UnitNumber,
                UnitType = input.UnitType,
                Layout = input.Layout,
                Orientation = input.Orientation,
                MainArea = input.MainArea,
                AuxiliaryArea = input.AuxiliaryArea,
                BalconyArea = input.BalconyArea,
                AwningArea = input.AwningArea,
                SmallPublicArea = input.SmallPublicArea,
                LargePublicArea = input.LargePublicArea,
                PublicAreaShare = input.PublicAreaShare,
                TotalArea = input.TotalArea,
                ListPrice = input.ListPrice,
                MinimumPrice = input.MinimumPrice,
                Status = input.Status,
                Remarks = input.Remarks,
                CreatedTime = DateTime.UtcNow,
                UpdatedTime = DateTime.UtcNow,
                CreatedUserInfoId = CurrentUser.UserId,
                UpdatedUserInfoId = CurrentUser.UserId
            };

            Db.Units.Add(unit);
            await Db.SaveChangesAsync();
            return unit;
        }

        public async Task UpdateAsync(int unitId, UnitUpdateInput input)
        {
            var unit = await Db.Units.FindAsync(unitId);
            if (unit == null)
            {
                throw new KeyNotFoundException("找不到要更新的房屋單位。");
            }
            
            if(input.FloorId.HasValue)
            {
                 var floor = await Db.Floors.FindAsync(input.FloorId.Value);
                 if (floor == null) throw new KeyNotFoundException("指定的樓層 ID 不存在。");
                 unit.FloorId = input.FloorId.Value;
                 unit.BuildingId = floor.BuildingId;
                 unit.SiteCode = floor.SiteCode;
            }

            unit.UnitNumber = input.UnitNumber ?? unit.UnitNumber;
            unit.UnitType = input.UnitType ?? unit.UnitType;
            unit.Layout = input.Layout ?? unit.Layout;
            unit.Orientation = input.Orientation ?? unit.Orientation;
            unit.MainArea = input.MainArea ?? unit.MainArea;
            unit.AuxiliaryArea = input.AuxiliaryArea ?? unit.AuxiliaryArea;
            unit.BalconyArea = input.BalconyArea ?? unit.BalconyArea;
            unit.AwningArea = input.AwningArea ?? unit.AwningArea;
            unit.SmallPublicArea = input.SmallPublicArea ?? unit.SmallPublicArea;
            unit.LargePublicArea = input.LargePublicArea ?? unit.LargePublicArea;
            unit.PublicAreaShare = input.PublicAreaShare ?? unit.PublicAreaShare;
            unit.TotalArea = input.TotalArea ?? unit.TotalArea;
            unit.ListPrice = input.ListPrice ?? unit.ListPrice;
            unit.MinimumPrice = input.MinimumPrice ?? unit.MinimumPrice;
            unit.TransactionPrice = input.TransactionPrice ?? unit.TransactionPrice;
            unit.Status = input.Status ?? unit.Status;
            unit.Remarks = input.Remarks ?? unit.Remarks;

            unit.UpdatedTime = DateTime.UtcNow;
            unit.UpdatedUserInfoId = CurrentUser.UserId;

            await Db.SaveChangesAsync();
        }

        public async Task DeleteAsync(int unitId)
        {
            var unit = await Db.Units.FindAsync(unitId);
            if (unit == null)
            {
                throw new KeyNotFoundException("找不到要刪除的房屋單位。");
            }

            Db.Units.Remove(unit);
            await Db.SaveChangesAsync();
        }

        // The import logic will be added here.

        public async Task ImportParkingSpacesAsync(ImportDataInput input)
        {
            var options = new JsonSerializerOptions { PropertyNameCaseInsensitive = true };
            var parkingSpaceDtos = input.Data.Select(d => JsonSerializer.Deserialize<ParkingSpaceImportDto>(JsonSerializer.Serialize(d), options)).ToList();

            var siteExists = await Db.Sites.AnyAsync(s => s.SiteCode == input.SiteCode);
            if (!siteExists)
            {
                throw new Exception($"案場代碼 '{input.SiteCode}' 不存在。");
            }

            var floorLabels = parkingSpaceDtos.Select(p => p.FloorLabel).Distinct();
            var floors = await Db.Floors.Where(f => f.SiteCode == input.SiteCode && floorLabels.Contains(f.FloorLabel)).ToDictionaryAsync(f => f.FloorLabel, f => f);

            var building = await Db.Buildings.FirstOrDefaultAsync(b => b.SiteCode == input.SiteCode);
            if (building == null)
            {
                throw new Exception($"案場代碼 '{input.SiteCode}' 下找不到對應的建築。");
            }

            using var transaction = await Db.Database.BeginTransactionAsync();
            try
            {
                foreach (var dto in parkingSpaceDtos)
                {
                    if (!floors.TryGetValue(dto.FloorLabel, out var floor))
                    {
                        throw new Exception($"在案場 '{input.SiteCode}' 中找不到樓層 '{dto.FloorLabel}'。");
                    }

                    var parkingSpace = await Db.ParkingSpaces.FirstOrDefaultAsync(p => p.SiteCode == input.SiteCode && p.FloorId == floor.FloorId && p.SpaceNumber == dto.SpaceNumber);

                    if (parkingSpace == null)
                    {
                        // Create new ParkingSpace
                        parkingSpace = new ParkingSpace
                        {
                            SiteCode = input.SiteCode,
                            BuildingId = building.BuildingId, 
                            FloorId = floor.FloorId,
                            SpaceNumber = dto.SpaceNumber,
                            SpaceType = dto.SpaceType,
                            ListPrice = dto.ListPrice,
                            MinimumPrice = dto.MinimumPrice,
                            Status = dto.Status,
                            Remarks = dto.Ownership,
                            CreatedUserInfoId = CurrentUser.UserId,
                            UpdatedUserInfoId = CurrentUser.UserId,
                            CreatedTime = DateTime.UtcNow,
                            UpdatedTime = DateTime.UtcNow
                        };
                        Db.ParkingSpaces.Add(parkingSpace);
                    }
                    else
                    {
                        // Update existing ParkingSpace
                        parkingSpace.SpaceType = dto.SpaceType;
                        parkingSpace.ListPrice = dto.ListPrice;
                        parkingSpace.MinimumPrice = dto.MinimumPrice;
                        parkingSpace.Status = dto.Status;
                        parkingSpace.Remarks = dto.Ownership;
                        parkingSpace.UpdatedUserInfoId = CurrentUser.UserId;
                        parkingSpace.UpdatedTime = DateTime.UtcNow;
                        Db.ParkingSpaces.Update(parkingSpace);
                    }
                }
                await Db.SaveChangesAsync();
                await transaction.CommitAsync();
            }
            catch (Exception)
            {
                await transaction.RollbackAsync();
                throw;
            }
        }
    }
} 
