﻿using AlifeApi.BusinessRules.Infrastructure;
using AlifeApi.DataAccess.ProjectContent;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace AlifeApi.BusinessRules.UnitModels
{
    /// <summary>
    /// 房屋單位服務
    /// </summary>
    public class UnitService : ServiceBase<alifeContext>
    {
        public UnitService(IServiceProvider serviceProvider, alifeContext dbContext)
            : base(serviceProvider, dbContext)
        {
        }

        /// <summary>
        /// 建立房屋單位
        /// </summary>
        /// <param name="input">房屋單位建立輸入資料</param>
        /// <returns>新建房屋單位的ID</returns>
        public async Task<int> CreateUnitAsync(UnitCreateInput input)
        {
            ArgumentNullException.ThrowIfNull(input);

            // 驗證 FloorId 是否存在且 FloorType 為住宅或商業
            var floor = await Db.Floors.FirstOrDefaultAsync(f => f.FloorId == input.FloorId);
            if (floor == null)
            {
                throw new Exception($"指定的樓層ID '{input.FloorId}' 不存在。");
            }
            // 根據實際 FloorType 名稱調整 (例如: "住宅", "商業", "店面")
            if (floor.FloorType != "住宅" && floor.FloorType != "商業" && floor.FloorType != "店面")
            {
                throw new Exception($"樓層ID '{input.FloorId}' 的類型 ('{floor.FloorType}') 不適用於建立房屋單位，必須是住宅或商業類型。");
            }

            // 驗證提供的 BuildingId 和 SiteCode 是否與 Floor 紀錄一致
            if (floor.BuildingId != input.BuildingId || floor.SiteCode != input.SiteCode)
            {
                throw new Exception("提供的 BuildingId 或 SiteCode 與指定 FloorId 的記錄不符。");
            }

            // 檢查同一樓層下是否已有相同戶號
            if (await Db.Units.AnyAsync(u => u.FloorId == input.FloorId && u.UnitNumber == input.UnitNumber))
            {
                throw new Exception($"樓層 '{input.FloorId}' 下已存在相同戶號 '{input.UnitNumber}'。");
            }

            // TODO: 驗證 AssociatedParkingSpaceIds 中的 ID 是否存在且有效 (可選)

            var unit = new Unit
            {
                FloorId = input.FloorId,
                BuildingId = input.BuildingId,
                SiteCode = input.SiteCode,
                UnitNumber = input.UnitNumber,
                UnitType = input.UnitType,
                Layout = input.Layout,
                Orientation = input.Orientation,
                MainArea = input.MainArea,
                AuxiliaryArea = input.AuxiliaryArea,
                PublicAreaShare = input.PublicAreaShare,
                TotalArea = input.TotalArea,
                ListPrice = input.ListPrice,
                MinimumPrice = input.MinimumPrice,
                // TransactionPrice 建立時通常為 null
                Status = input.Status,
                IsPublicAreaIncluded = input.IsPublicAreaIncluded,
                AssociatedParkingSpaceIds = input.AssociatedParkingSpaceIds,
                Remarks = input.Remarks,
                CreatedTime = DateTime.Now,
                UpdatedTime = DateTime.Now,
                CreatedUserInfoId = CurrentUser.UserId,
                UpdatedUserInfoId = CurrentUser.UserId
            };

            Db.Units.Add(unit);
            await Db.SaveChangesAsync();

            return unit.UnitId;
        }

        /// <summary>
        /// 取得指定樓層的房屋單位列表 (分頁)
        /// </summary>
        /// <param name="input">查詢條件</param>
        /// <returns>分頁的房屋單位列表</returns>
        public async Task<PagedListOutput<UnitListOutput>> GetUnitListAsync(UnitQueryInput input)
        {
            ArgumentNullException.ThrowIfNull(input);

            var query = Db.Units.AsQueryable();

            // 篩選 FloorId (如果提供)
            if (input.FloorId > 0)
            {
                query = query.Where(u => u.FloorId == input.FloorId);
            }

            // 篩選 SiteCode (如果提供) - 根據 DTO 新增
            // if (!string.IsNullOrEmpty(input.SiteCode))
            // {
            //      query = query.Where(u => u.SiteCode == input.SiteCode);
            // }

            // 篩選 BuildingId (如果提供) - 根據 DTO 新增
            // if (input.BuildingId.HasValue)
            // {
            //     query = query.Where(u => u.BuildingId == input.BuildingId.Value);
            // }                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            

            // 篩選 UnitNumber
            if (!string.IsNullOrEmpty(input.UnitNumber))
            {
                query = query.Where(u => u.UnitNumber == input.UnitNumber);
            }

            // 篩選 Status
            if (!string.IsNullOrEmpty(input.Status))
            {
                query = query.Where(u => u.Status == input.Status);
            }

            // 篩選 TotalArea 範圍
            if (input.MinTotalArea.HasValue)
            {
                query = query.Where(u => u.TotalArea >= input.MinTotalArea.Value);
            }
            if (input.MaxTotalArea.HasValue)
            {
                query = query.Where(u => u.TotalArea <= input.MaxTotalArea.Value);
            }

            var projectedQuery = query
                .OrderByDescending(u => u.CreatedTime)
                .Select(u => new UnitListOutput
                {
                    UnitId = u.UnitId,
                    FloorId = u.FloorId,
                    BuildingId = u.BuildingId,
                    SiteCode = u.SiteCode,
                    UnitNumber = u.UnitNumber,
                    UnitType = u.UnitType,
                    Layout = u.Layout,
                    TotalArea = u.TotalArea,
                    ListPrice = u.ListPrice,
                    Status = u.Status,
                    CreatedTime = u.CreatedTime,
                });

            var pagedResult = await projectedQuery.ToPagedListOutputAsync(input);

            // 手動查詢關聯資料並填充
            if (pagedResult.Details.Any()) // Use Details property
            {
                var floorIds = pagedResult.Details.Select(u => u.FloorId).Distinct().ToList(); // Use Details property
                var buildingIds = pagedResult.Details.Select(u => u.BuildingId).Distinct().ToList(); // Use Details property

                var floors = await Db.Floors
                                   .Where(f => floorIds.Contains(f.FloorId))
                                   .Select(f => new { f.FloorId, f.FloorLabel })
                                   .ToDictionaryAsync(f => f.FloorId, f => f.FloorLabel);

                var buildings = await Db.Buildings
                                    .Where(b => buildingIds.Contains(b.BuildingId))
                                    .Select(b => new { b.BuildingId, b.BuildingName })
                                    .ToDictionaryAsync(b => b.BuildingId, b => b.BuildingName);

                foreach (var unitOutput in pagedResult.Details) // Use Details property
                {
                    if (floors.TryGetValue(unitOutput.FloorId, out var floorLabel))
                    {
                        unitOutput.FloorLabel = floorLabel;
                    }
                    if (buildings.TryGetValue(unitOutput.BuildingId, out var buildingName))
                    {
                        unitOutput.BuildingName = buildingName;
                    }
                }
            }

            return pagedResult;
        }

        /// <summary>
        /// 根據ID取得房屋單位詳細資料
        /// </summary>
        /// <param name="unitId">房屋單位ID</param>
        /// <returns>房屋單位詳細資料</returns>
        public async Task<UnitOutput?> GetUnitByIdAsync(int unitId)
        {
            var unit = await Db.Units
                .Where(u => u.UnitId == unitId)
                .Select(u => new UnitOutput
                {
                    UnitId = u.UnitId,
                    FloorId = u.FloorId,
                    BuildingId = u.BuildingId,
                    SiteCode = u.SiteCode,
                    UnitNumber = u.UnitNumber,
                    UnitType = u.UnitType,
                    Layout = u.Layout,
                    Orientation = u.Orientation,
                    MainArea = u.MainArea,
                    AuxiliaryArea = u.AuxiliaryArea,
                    PublicAreaShare = u.PublicAreaShare,
                    TotalArea = u.TotalArea,
                    ListPrice = u.ListPrice,
                    MinimumPrice = u.MinimumPrice,
                    TransactionPrice = u.TransactionPrice,
                    Status = u.Status,
                    IsPublicAreaIncluded = u.IsPublicAreaIncluded,
                    AssociatedParkingSpaceIds = u.AssociatedParkingSpaceIds,
                    Remarks = u.Remarks,
                    CreatedTime = u.CreatedTime,
                    UpdatedTime = u.UpdatedTime,
                    CreatedUserInfoId = u.CreatedUserInfoId,
                    UpdatedUserInfoId = u.UpdatedUserInfoId
                })
                .FirstOrDefaultAsync();

            if (unit == null)
            {
                return null;
            }

            var floor = await Db.Floors.Where(f => f.FloorId == unit.FloorId).Select(f => f.FloorLabel).FirstOrDefaultAsync();
            var building = await Db.Buildings.Where(b => b.BuildingId == unit.BuildingId).Select(b => b.BuildingName).FirstOrDefaultAsync();

            unit.FloorLabel = floor;
            unit.BuildingName = building;

            var userIds = new[] { unit.CreatedUserInfoId, unit.UpdatedUserInfoId }.Where(id => !string.IsNullOrEmpty(id)).Distinct().ToList();
            if (userIds.Any())
            {
                var users = await Db.UserInfos
                    .Where(u => userIds.Contains(u.UserInfoId))
                    .Select(u => new { u.UserInfoId, u.Name })
                    .ToListAsync();
                var userMap = users.ToDictionary(u => u.UserInfoId, u => u.Name);

                if (userMap.TryGetValue(unit.CreatedUserInfoId, out var createdName))
                {
                    unit.CreatedUserName = createdName;
                }
                if (!string.IsNullOrEmpty(unit.UpdatedUserInfoId) && userMap.TryGetValue(unit.UpdatedUserInfoId, out var updatedName))
                {
                    unit.UpdatedUserName = updatedName;
                }
            }

            return unit;
        }

        /// <summary>
        /// 更新房屋單位資訊
        /// </summary>
        /// <param name="unitId">房屋單位ID</param>
        /// <param name="input">房屋單位更新輸入資料</param>
        public async Task UpdateUnitAsync(int unitId, UnitUpdateInput input)
        {
            ArgumentNullException.ThrowIfNull(input);

            var unit = await Db.Units.FindAsync(unitId);

            if (unit == null)
            {
                throw new Exception($"找不到指定的房屋單位 (ID: {unitId})");
            }

            // TODO: 驗證 AssociatedParkingSpaceIds 中的 ID 是否存在且有效 (可選)

            unit.UnitType = input.UnitType;
            unit.Layout = input.Layout;
            unit.Orientation = input.Orientation;
            unit.MainArea = input.MainArea;
            unit.AuxiliaryArea = input.AuxiliaryArea;
            unit.PublicAreaShare = input.PublicAreaShare;
            unit.TotalArea = input.TotalArea;
            unit.ListPrice = input.ListPrice;
            unit.MinimumPrice = input.MinimumPrice;
            unit.TransactionPrice = input.TransactionPrice;
            unit.Status = input.Status;
            unit.IsPublicAreaIncluded = input.IsPublicAreaIncluded;
            unit.AssociatedParkingSpaceIds = input.AssociatedParkingSpaceIds;
            unit.Remarks = input.Remarks;
            unit.UpdatedTime = DateTime.Now;
            unit.UpdatedUserInfoId = CurrentUser.UserId;

            await Db.SaveChangesAsync();
        }

        /// <summary>
        /// 刪除房屋單位
        /// </summary>
        /// <param name="unitId">房屋單位ID</param>
        public async Task DeleteUnitAsync(int unitId)
        {
            var unit = await Db.Units.FindAsync(unitId);

            if (unit == null)
            {
                return; // 允許刪除不存在的紀錄
            }

            // 檢查是否有相關連的訂單 (PurchaseOrders)
            bool hasRelatedOrders = await Db.PurchaseOrders.AnyAsync(po => po.UnitId == unitId);
            if (hasRelatedOrders)
            {
                throw new Exception($"無法刪除房屋單位 (ID: {unitId})，因為其已關聯到訂單。請先處理相關訂單。");
            }

            // 檢查是否有車位Units關聯到此單位 (現在關聯關係由Units表管理)
            // 註：由於現在車位也是Units記錄，我們不需要特別處理ParkingSpaces的關聯
            // 如果有需要解除關聯，應該檢查其他Units記錄的AssociatedParkingSpaceIds欄位

            Db.Units.Remove(unit);
            await Db.SaveChangesAsync();
        }

        /// <summary>
        /// 取得可售房屋列表 (專門用於銷售管理)
        /// </summary>
        /// <param name="input">查詢條件</param>
        /// <returns>可售房屋列表</returns>
        public async Task<PagedListOutput<UnitListOutput>> GetAvailableUnitsAsync(UnitQueryInput input)
        {
            ArgumentNullException.ThrowIfNull(input);
            
            // 強制設定 Status 為可售
            input.Status = "可售";
            
            return await GetUnitListAsync(input);
        }

        /// <summary>
        /// 取得保留房屋列表 (專門用於銷售管理)
        /// </summary>
        /// <param name="input">查詢條件</param>
        /// <returns>保留房屋列表</returns>
        public async Task<PagedListOutput<UnitListOutput>> GetReservedUnitsAsync(UnitQueryInput input)
        {
            ArgumentNullException.ThrowIfNull(input);
            
            // 強制設定 Status 為保留
            input.Status = "保留";
            
            return await GetUnitListAsync(input);
        }

        /// <summary>
        /// 變更房屋單位狀態
        /// </summary>
        /// <param name="unitId">房屋單位ID</param>
        /// <param name="newStatus">新狀態</param>
        /// <param name="orderId">關聯的訂單ID (如果有)</param>
        public async Task ChangeUnitStatusAsync(int unitId, string newStatus, int? orderId = null)
        {
            var unit = await Db.Units.FindAsync(unitId);
            if (unit == null)
            {
                throw new Exception($"找不到指定的房屋單位 (ID: {unitId})");
            }

            var oldStatus = unit.Status;
            unit.Status = newStatus;
            unit.UpdatedTime = DateTime.Now;
            unit.UpdatedUserInfoId = CurrentUser.UserId;

            await Db.SaveChangesAsync();

            // 記錄狀態變更 (可選：如果需要審計軌跡)
            // await LogStatusChangeAsync(unitId, "Unit", oldStatus, newStatus, orderId);
        }

        /// <summary>
        /// 批次變更房屋單位狀態 (用於訂單相關操作)
        /// </summary>
        /// <param name="unitIds">房屋單位ID列表</param>
        /// <param name="newStatus">新狀態</param>
        /// <param name="orderId">關聯的訂單ID</param>
        public async Task BatchChangeUnitStatusAsync(List<int> unitIds, string newStatus, int? orderId = null)
        {
            if (unitIds == null || !unitIds.Any())
            {
                return;
            }

            var units = await Db.Units.Where(u => unitIds.Contains(u.UnitId)).ToListAsync();
            
            foreach (var unit in units)
            {
                unit.Status = newStatus;
                unit.UpdatedTime = DateTime.Now;
                unit.UpdatedUserInfoId = CurrentUser.UserId;
            }

            await Db.SaveChangesAsync();
        }

        /// <summary>
        /// 取得房屋銷售統計
        /// </summary>
        /// <param name="siteCode">案場編號</param>
        /// <returns>銷售統計資料</returns>
        public async Task<UnitSalesStatistics> GetUnitSalesStatisticsAsync(string siteCode)
        {
            ArgumentNullException.ThrowIfNull(siteCode);

            var statistics = await Db.Units
                .Where(u => u.SiteCode == siteCode)
                .GroupBy(u => u.Status)
                .Select(g => new
                {
                    Status = g.Key,
                    Count = g.Count(),
                    TotalListPrice = g.Sum(u => u.ListPrice ?? 0),
                    TotalMinPrice = g.Sum(u => u.MinimumPrice ?? 0)
                })
                .ToListAsync();

            var result = new UnitSalesStatistics
            {
                SiteCode = siteCode,
                AvailableCount = statistics.FirstOrDefault(s => s.Status == "可售")?.Count ?? 0,
                ReservedCount = statistics.FirstOrDefault(s => s.Status == "保留")?.Count ?? 0,
                BookedCount = statistics.FirstOrDefault(s => s.Status == "已預訂")?.Count ?? 0,
                SoldCount = statistics.FirstOrDefault(s => s.Status == "已售")?.Count ?? 0,
                TotalAvailableListPrice = statistics.FirstOrDefault(s => s.Status == "可售")?.TotalListPrice ?? 0,
                TotalReservedListPrice = statistics.FirstOrDefault(s => s.Status == "保留")?.TotalListPrice ?? 0
            };

            result.TotalCount = result.AvailableCount + result.ReservedCount + result.BookedCount + result.SoldCount;
            result.SalesRate = result.TotalCount > 0 ? Math.Round((decimal)(result.SoldCount + result.BookedCount) / result.TotalCount * 100, 2) : 0;

            return result;
        }
    }
} 
