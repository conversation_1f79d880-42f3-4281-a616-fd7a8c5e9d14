using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using AlifeApi.BusinessRules.Infrastructure;
using AlifeApi.DataAccess.ProjectContent;
using Microsoft.EntityFrameworkCore;

namespace AlifeApi.BusinessRules.CategoryModels
{
    /// <summary>
    /// 小分類服務
    /// </summary>
    public class SmallCategoryService : ServiceBase<alifeContext>
    {
        public SmallCategoryService(IServiceProvider serviceProvider, alifeContext dbContext)
            : base(serviceProvider, dbContext)
        {
        }

        /// <summary>
        /// 建立小分類
        /// </summary>
        /// <param name="input">小分類建立輸入資料</param>
        /// <returns>新建小分類的ID</returns>
        public async Task<long> CreateSmallCategoryAsync(SmallCategoryInput input)
        {
            ArgumentNullException.ThrowIfNull(input);

            // 檢查中分類是否存在
            if (!await Db.MediumCategories.AnyAsync(c => c.MediumCategoryId == input.MediumCategoryId))
            {
                throw new Exception($"指定的中分類 (ID: {input.MediumCategoryId}) 不存在。");
            }

            // 檢查在同一中分類下名稱是否重複
            if (await Db.SmallCategories.AnyAsync(c => c.MediumCategoryId == input.MediumCategoryId && c.Name == input.Name))
            {
                throw new Exception($"在指定中分類下，小分類名稱 '{input.Name}' 已存在。");
            }

            var smallCategory = new SmallCategory
            {
                MediumCategoryId = input.MediumCategoryId,
                Name = input.Name,
                Description = input.Description,
                SortOrder = input.SortOrder,
                IsActive = input.IsActive,
                CreateTime = DateTime.Now,
                UpdateTime = DateTime.Now,
                CreatedUserInfoId = CurrentUser.UserId,
                UpdatedUserInfoId = CurrentUser.UserId
            };

            Db.SmallCategories.Add(smallCategory);
            await Db.SaveChangesAsync();

            return smallCategory.SmallCategoryId;
        }

        /// <summary>
        /// 取得小分類列表 (分頁)
        /// </summary>
        /// <param name="input">查詢條件</param>
        /// <returns>分頁的小分類列表</returns>
        public async Task<PagedListOutput<SmallCategoryListOutput>> GetSmallCategoryListAsync(SmallCategoryQueryInput input)
        {
            ArgumentNullException.ThrowIfNull(input);

            var query = from s in Db.SmallCategories
                        join m in Db.MediumCategories on s.MediumCategoryId equals m.MediumCategoryId
                        join l in Db.LargeCategories on m.LargeCategoryId equals l.LargeCategoryId
                        select new { SmallCategory = s, MediumCategory = m, LargeCategory = l };

            // 篩選大分類ID
            if (input.LargeCategoryId.HasValue)
            {
                query = query.Where(x => x.LargeCategory.LargeCategoryId == input.LargeCategoryId.Value);
            }

            // 篩選中分類ID
            if (input.MediumCategoryId.HasValue)
            {
                query = query.Where(x => x.SmallCategory.MediumCategoryId == input.MediumCategoryId.Value);
            }

            // 篩選名稱 (模糊查詢)
            if (!string.IsNullOrEmpty(input.Name))
            {
                query = query.Where(x => x.SmallCategory.Name.Contains(input.Name));
            }

            // 篩選是否啟用
            if (input.IsActive.HasValue)
            {
                query = query.Where(x => x.SmallCategory.IsActive == input.IsActive.Value);
            }

            var pagedResult = await query
                .OrderBy(x => x.LargeCategory.SortOrder)
                .ThenBy(x => x.MediumCategory.SortOrder)
                .ThenBy(x => x.SmallCategory.SortOrder)
                .ThenBy(x => x.SmallCategory.CreateTime)
                .Select(x => new SmallCategoryListOutput
                {
                    SmallCategoryId = x.SmallCategory.SmallCategoryId,
                    MediumCategoryId = x.SmallCategory.MediumCategoryId,
                    MediumCategoryName = x.MediumCategory.Name,
                    LargeCategoryId = x.MediumCategory.LargeCategoryId,
                    LargeCategoryName = x.LargeCategory.Name,
                    Name = x.SmallCategory.Name,
                    Description = x.SmallCategory.Description,
                    SortOrder = x.SmallCategory.SortOrder,
                    IsActive = x.SmallCategory.IsActive,
                    CreateTime = x.SmallCategory.CreateTime
                })
                .ToPagedListOutputAsync(input);

            return pagedResult;
        }

        /// <summary>
        /// 根據ID取得小分類詳細資料
        /// </summary>
        /// <param name="smallCategoryId">小分類ID</param>
        /// <returns>小分類詳細資料</returns>
        public async Task<SmallCategoryOutput?> GetSmallCategoryByIdAsync(long smallCategoryId)
        {
            var smallCategory = await (from s in Db.SmallCategories
                                       join m in Db.MediumCategories on s.MediumCategoryId equals m.MediumCategoryId
                                       join l in Db.LargeCategories on m.LargeCategoryId equals l.LargeCategoryId
                                       where s.SmallCategoryId == smallCategoryId
                                       select new SmallCategoryOutput
                                       {
                                           SmallCategoryId = s.SmallCategoryId,
                                           MediumCategoryId = s.MediumCategoryId,
                                           MediumCategoryName = m.Name,
                                           LargeCategoryId = m.LargeCategoryId,
                                           LargeCategoryName = l.Name,
                                           Name = s.Name,
                                           Description = s.Description,
                                           SortOrder = s.SortOrder,
                                           IsActive = s.IsActive,
                                           CreateTime = s.CreateTime,
                                           CreatedUserInfoId = s.CreatedUserInfoId,
                                           UpdateTime = s.UpdateTime,
                                           UpdatedUserInfoId = s.UpdatedUserInfoId
                                       })
                                       .FirstOrDefaultAsync();

            if (smallCategory == null)
            {
                return null;
            }

            // 查詢並填入建立者和更新者名稱
            var userIds = new[] { smallCategory.CreatedUserInfoId, smallCategory.UpdatedUserInfoId }.Distinct().ToList();
            var users = await Db.UserInfos
                .Where(u => userIds.Contains(u.UserInfoId))
                .Select(u => new { u.UserInfoId, u.Name })
                .ToListAsync();
            var userMap = users.ToDictionary(u => u.UserInfoId, u => u.Name);

            if (userMap.TryGetValue(smallCategory.CreatedUserInfoId, out var createdName))
            {
                smallCategory.CreatedUserName = createdName;
            }
            if (userMap.TryGetValue(smallCategory.UpdatedUserInfoId, out var updatedName))
            {
                smallCategory.UpdatedUserName = updatedName;
            }

            return smallCategory;
        }

        /// <summary>
        /// 更新小分類資訊
        /// </summary>
        /// <param name="smallCategoryId">小分類ID</param>
        /// <param name="input">小分類更新輸入資料</param>
        public async Task UpdateSmallCategoryAsync(long smallCategoryId, SmallCategoryInput input)
        {
            ArgumentNullException.ThrowIfNull(input);

            var smallCategory = await Db.SmallCategories.FindAsync(smallCategoryId);

            if (smallCategory == null)
            {
                throw new Exception($"找不到指定的小分類 (ID: {smallCategoryId})");
            }

            // 檢查中分類是否存在
            if (!await Db.MediumCategories.AnyAsync(c => c.MediumCategoryId == input.MediumCategoryId))
            {
                throw new Exception($"指定的中分類 (ID: {input.MediumCategoryId}) 不存在。");
            }

            // 檢查在同一中分類下名稱是否重複 (排除自己)
            if (await Db.SmallCategories.AnyAsync(c => c.MediumCategoryId == input.MediumCategoryId && c.Name == input.Name && c.SmallCategoryId != smallCategoryId))
            {
                throw new Exception($"在指定中分類下，小分類名稱 '{input.Name}' 已存在。");
            }

            smallCategory.MediumCategoryId = input.MediumCategoryId;
            smallCategory.Name = input.Name;
            smallCategory.Description = input.Description;
            smallCategory.SortOrder = input.SortOrder;
            smallCategory.IsActive = input.IsActive;
            smallCategory.UpdateTime = DateTime.Now;
            smallCategory.UpdatedUserInfoId = CurrentUser.UserId;

            await Db.SaveChangesAsync();
        }

        /// <summary>
        /// 刪除小分類
        /// </summary>
        /// <param name="smallCategoryId">小分類ID</param>
        public async Task DeleteSmallCategoryAsync(long smallCategoryId)
        {
            var smallCategory = await Db.SmallCategories.FindAsync(smallCategoryId);

            if (smallCategory == null)
            {
                throw new Exception($"找不到指定的小分類 (ID: {smallCategoryId})");
            }

            // 這裡可以添加檢查是否有關聯的商品或其他實體
            // 例如：
            // var hasProducts = await Db.Products.AnyAsync(p => p.SmallCategoryId == smallCategoryId);
            // if (hasProducts)
            // {
            //     throw new Exception("無法刪除小分類，因為存在關聯的商品。");
            // }

            Db.SmallCategories.Remove(smallCategory);
            await Db.SaveChangesAsync();
        }

        /// <summary>
        /// 取得小分類下拉選單列表
        /// </summary>
        /// <param name="largeCategoryId">可選的大分類ID，用於篩選特定大分類下的小分類</param>
        /// <param name="mediumCategoryId">可選的中分類ID，用於篩選特定中分類下的小分類</param>
        /// <returns>啟用的小分類下拉選單列表</returns>
        public async Task<List<SmallCategoryDropdownOutput>> GetSmallCategoryDropdownAsync(long? largeCategoryId = null, long? mediumCategoryId = null)
        {
            var query = from s in Db.SmallCategories
                        join m in Db.MediumCategories on s.MediumCategoryId equals m.MediumCategoryId
                        join l in Db.LargeCategories on m.LargeCategoryId equals l.LargeCategoryId
                        where s.IsActive == true && m.IsActive == true && l.IsActive == true
                        select new { SmallCategory = s, MediumCategory = m, LargeCategory = l };

            // 如果指定了大分類ID，則篩選該大分類下的小分類
            if (largeCategoryId.HasValue)
            {
                query = query.Where(x => x.LargeCategory.LargeCategoryId == largeCategoryId.Value);
            }

            // 如果指定了中分類ID，則篩選該中分類下的小分類
            if (mediumCategoryId.HasValue)
            {
                query = query.Where(x => x.SmallCategory.MediumCategoryId == mediumCategoryId.Value);
            }

            var result = await query
                .OrderBy(x => x.LargeCategory.SortOrder)
                .ThenBy(x => x.MediumCategory.SortOrder)
                .ThenBy(x => x.SmallCategory.SortOrder)
                .ThenBy(x => x.SmallCategory.CreateTime)
                .Select(x => new SmallCategoryDropdownOutput
                {
                    SmallCategoryId = x.SmallCategory.SmallCategoryId,
                    MediumCategoryId = x.SmallCategory.MediumCategoryId,
                    MediumCategoryName = x.MediumCategory.Name,
                    LargeCategoryId = x.MediumCategory.LargeCategoryId,
                    LargeCategoryName = x.LargeCategory.Name,
                    Name = x.SmallCategory.Name,
                    SortOrder = x.SmallCategory.SortOrder
                })
                .ToListAsync();

            return result;
        }
    }
} 
