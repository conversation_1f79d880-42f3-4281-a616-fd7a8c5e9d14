#### [AlifeApi.BusinessRules](index.md 'index')
### [AlifeApi.BusinessRules.CrmOptionModels](AlifeApi.BusinessRules.CrmOptionModels.md 'AlifeApi.BusinessRules.CrmOptionModels')

## CrmOptionTypeDropdownOutput Class

CRM選項類型下拉選單輸出模型

```csharp
public class CrmOptionTypeDropdownOutput
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; CrmOptionTypeDropdownOutput
### Properties

<a name='AlifeApi.BusinessRules.CrmOptionModels.CrmOptionTypeDropdownOutput.Description'></a>

## CrmOptionTypeDropdownOutput.Description Property

選項類型描述

```csharp
public string? Description { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.CrmOptionModels.CrmOptionTypeDropdownOutput.Name'></a>

## CrmOptionTypeDropdownOutput.Name Property

選項類型名稱 (顯示文字)

```csharp
public string Name { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.CrmOptionModels.CrmOptionTypeDropdownOutput.Value'></a>

## CrmOptionTypeDropdownOutput.Value Property

選項類型ID (選項值)

```csharp
public long Value { get; set; }
```

#### Property Value
[System.Int64](https://docs.microsoft.com/en-us/dotnet/api/System.Int64 'System.Int64')