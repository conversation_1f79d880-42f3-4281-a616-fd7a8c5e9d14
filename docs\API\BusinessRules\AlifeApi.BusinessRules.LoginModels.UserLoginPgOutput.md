#### [AlifeApi.BusinessRules](index.md 'index')
### [AlifeApi.BusinessRules.LoginModels](AlifeApi.BusinessRules.LoginModels.md 'AlifeApi.BusinessRules.LoginModels')

## UserLoginPgOutput Class

```csharp
public class UserLoginPgOutput :
AlifeApi.BusinessRules.Infrastructure.IApiMessage
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; UserLoginPgOutput

Implements [IApiMessage](AlifeApi.BusinessRules.Infrastructure.IApiMessage.md 'AlifeApi.BusinessRules.Infrastructure.IApiMessage')
### Properties

<a name='AlifeApi.BusinessRules.LoginModels.UserLoginPgOutput.BirthDate'></a>

## UserLoginPgOutput.BirthDate Property

出生日期

```csharp
public System.Nullable<System.DateTime> BirthDate { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.DateTime](https://docs.microsoft.com/en-us/dotnet/api/System.DateTime 'System.DateTime')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

<a name='AlifeApi.BusinessRules.LoginModels.UserLoginPgOutput.CompanyId'></a>

## UserLoginPgOutput.CompanyId Property

公司ID

```csharp
public string CompanyId { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.LoginModels.UserLoginPgOutput.Email'></a>

## UserLoginPgOutput.Email Property

使用者電子郵件

```csharp
public string Email { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.LoginModels.UserLoginPgOutput.EmergencyContactName'></a>

## UserLoginPgOutput.EmergencyContactName Property

緊急聯絡人姓名

```csharp
public string EmergencyContactName { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.LoginModels.UserLoginPgOutput.EmergencyContactPhone'></a>

## UserLoginPgOutput.EmergencyContactPhone Property

緊急聯絡人電話

```csharp
public string EmergencyContactPhone { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.LoginModels.UserLoginPgOutput.EmergencyContactRelation'></a>

## UserLoginPgOutput.EmergencyContactRelation Property

與緊急聯絡人的關係

```csharp
public string EmergencyContactRelation { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.LoginModels.UserLoginPgOutput.Gender'></a>

## UserLoginPgOutput.Gender Property

性別

```csharp
public string Gender { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.LoginModels.UserLoginPgOutput.HireDate'></a>

## UserLoginPgOutput.HireDate Property

到職日期

```csharp
public System.Nullable<System.DateTime> HireDate { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.DateTime](https://docs.microsoft.com/en-us/dotnet/api/System.DateTime 'System.DateTime')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

<a name='AlifeApi.BusinessRules.LoginModels.UserLoginPgOutput.IP'></a>

## UserLoginPgOutput.IP Property

使用者IP地址

```csharp
public string IP { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.LoginModels.UserLoginPgOutput.IsEmailNotificationEnabled'></a>

## UserLoginPgOutput.IsEmailNotificationEnabled Property

是否需要發送電子郵件通知

```csharp
public System.Nullable<bool> IsEmailNotificationEnabled { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.Boolean](https://docs.microsoft.com/en-us/dotnet/api/System.Boolean 'System.Boolean')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

<a name='AlifeApi.BusinessRules.LoginModels.UserLoginPgOutput.IsInside'></a>

## UserLoginPgOutput.IsInside Property

是否為內場/外場人員

```csharp
public System.Nullable<bool> IsInside { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.Boolean](https://docs.microsoft.com/en-us/dotnet/api/System.Boolean 'System.Boolean')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

<a name='AlifeApi.BusinessRules.LoginModels.UserLoginPgOutput.IsM365'></a>

## UserLoginPgOutput.IsM365 Property

是否為 M365 帳號

```csharp
public System.Nullable<bool> IsM365 { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.Boolean](https://docs.microsoft.com/en-us/dotnet/api/System.Boolean 'System.Boolean')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

<a name='AlifeApi.BusinessRules.LoginModels.UserLoginPgOutput.JwtToken'></a>

## UserLoginPgOutput.JwtToken Property

JWT Token

```csharp
public string JwtToken { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

### Remarks
這邊改名不是我嫌棄回傳前端大小寫有問題，只是單純 C# 遇到這種 3 碼以上的略縮字大小寫規則和別的語言不同

<a name='AlifeApi.BusinessRules.LoginModels.UserLoginPgOutput.JwtTokenExpireTime'></a>

## UserLoginPgOutput.JwtTokenExpireTime Property

JWT Token 到期日期時間

```csharp
public System.Nullable<System.DateTime> JwtTokenExpireTime { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.DateTime](https://docs.microsoft.com/en-us/dotnet/api/System.DateTime 'System.DateTime')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

### Remarks
這邊改名不是我嫌棄回傳前端大小寫有問題，只是單純 C# 遇到這種 3 碼以上的略縮字大小寫規則和別的語言不同

<a name='AlifeApi.BusinessRules.LoginModels.UserLoginPgOutput.LastLogoutTime'></a>

## UserLoginPgOutput.LastLogoutTime Property

最後一次登出的時間

```csharp
public System.Nullable<System.DateTime> LastLogoutTime { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.DateTime](https://docs.microsoft.com/en-us/dotnet/api/System.DateTime 'System.DateTime')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

<a name='AlifeApi.BusinessRules.LoginModels.UserLoginPgOutput.LoginFailedCount'></a>

## UserLoginPgOutput.LoginFailedCount Property

登入失敗次數

```csharp
public System.Nullable<short> LoginFailedCount { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.Int16](https://docs.microsoft.com/en-us/dotnet/api/System.Int16 'System.Int16')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

<a name='AlifeApi.BusinessRules.LoginModels.UserLoginPgOutput.MailingAddress'></a>

## UserLoginPgOutput.MailingAddress Property

通訊地址

```csharp
public string MailingAddress { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.LoginModels.UserLoginPgOutput.MenuTrees'></a>

## UserLoginPgOutput.MenuTrees Property

選單樹狀結構

```csharp
public System.Collections.Generic.IEnumerable<AlifeApi.BusinessRules.RoleGroupModels.MenuTreeOutput> MenuTrees { get; set; }
```

#### Property Value
[System.Collections.Generic.IEnumerable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Collections.Generic.IEnumerable-1 'System.Collections.Generic.IEnumerable`1')[MenuTreeOutput](AlifeApi.BusinessRules.RoleGroupModels.MenuTreeOutput.md 'AlifeApi.BusinessRules.RoleGroupModels.MenuTreeOutput')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Collections.Generic.IEnumerable-1 'System.Collections.Generic.IEnumerable`1')

<a name='AlifeApi.BusinessRules.LoginModels.UserLoginPgOutput.MobileNumber'></a>

## UserLoginPgOutput.MobileNumber Property

手機號碼

```csharp
public string MobileNumber { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.LoginModels.UserLoginPgOutput.RegisteredAddress'></a>

## UserLoginPgOutput.RegisteredAddress Property

戶籍地址

```csharp
public string RegisteredAddress { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.LoginModels.UserLoginPgOutput.Response'></a>

## UserLoginPgOutput.Response Property

Gets the response.

```csharp
public System.Enum Response { get; set; }
```

Implements [Response](AlifeApi.BusinessRules.Infrastructure.IApiMessage.md#AlifeApi.BusinessRules.Infrastructure.IApiMessage.Response 'AlifeApi.BusinessRules.Infrastructure.IApiMessage.Response')

#### Property Value
[System.Enum](https://docs.microsoft.com/en-us/dotnet/api/System.Enum 'System.Enum')

<a name='AlifeApi.BusinessRules.LoginModels.UserLoginPgOutput.ServiceUnit'></a>

## UserLoginPgOutput.ServiceUnit Property

服務單位

```csharp
public string ServiceUnit { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.LoginModels.UserLoginPgOutput.Status'></a>

## UserLoginPgOutput.Status Property

帳號狀態

```csharp
public bool Status { get; set; }
```

#### Property Value
[System.Boolean](https://docs.microsoft.com/en-us/dotnet/api/System.Boolean 'System.Boolean')

<a name='AlifeApi.BusinessRules.LoginModels.UserLoginPgOutput.TelephoneNumber'></a>

## UserLoginPgOutput.TelephoneNumber Property

電話號碼

```csharp
public string TelephoneNumber { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.LoginModels.UserLoginPgOutput.UserInfoId'></a>

## UserLoginPgOutput.UserInfoId Property

使用者帳號

```csharp
public string UserInfoId { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.LoginModels.UserLoginPgOutput.UserName'></a>

## UserLoginPgOutput.UserName Property

使用者姓名

```csharp
public string UserName { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.LoginModels.UserLoginPgOutput.UserPassword'></a>

## UserLoginPgOutput.UserPassword Property

使用者密碼

```csharp
public string UserPassword { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')