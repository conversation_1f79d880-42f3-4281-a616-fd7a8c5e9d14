#### [AlifeApi.BusinessRules](index.md 'index')
### [AlifeApi.BusinessRules.DeptModels](AlifeApi.BusinessRules.DeptModels.md 'AlifeApi.BusinessRules.DeptModels')

## CreateDeptCondition Class

```csharp
public class CreateDeptCondition
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; CreateDeptCondition
### Properties

<a name='AlifeApi.BusinessRules.DeptModels.CreateDeptCondition.DeptId'></a>

## CreateDeptCondition.DeptId Property

使用者單位

```csharp
public string DeptId { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.DeptModels.CreateDeptCondition.DeptName'></a>

## CreateDeptCondition.DeptName Property

使用者單位

```csharp
public string DeptName { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.DeptModels.CreateDeptCondition.FuncIds'></a>

## CreateDeptCondition.FuncIds Property

```csharp
public System.Collections.Generic.IEnumerable<string> FuncIds { get; }
```

#### Property Value
[System.Collections.Generic.IEnumerable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Collections.Generic.IEnumerable-1 'System.Collections.Generic.IEnumerable`1')[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Collections.Generic.IEnumerable-1 'System.Collections.Generic.IEnumerable`1')

### Remarks
前端會傳字串，只好這樣處理

<a name='AlifeApi.BusinessRules.DeptModels.CreateDeptCondition.LeaderUserId'></a>

## CreateDeptCondition.LeaderUserId Property

部門主管

```csharp
public string LeaderUserId { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.DeptModels.CreateDeptCondition.ParentDeptId'></a>

## CreateDeptCondition.ParentDeptId Property

上級使用者單位

```csharp
public string ParentDeptId { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.DeptModels.CreateDeptCondition.Permission'></a>

## CreateDeptCondition.Permission Property

權限

```csharp
public string Permission { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')