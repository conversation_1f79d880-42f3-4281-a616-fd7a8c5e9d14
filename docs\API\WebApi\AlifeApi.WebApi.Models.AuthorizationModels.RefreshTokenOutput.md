#### [AlifeApi.WebApi](index.md 'index')
### [AlifeApi.WebApi.Models.AuthorizationModels](AlifeApi.WebApi.Models.AuthorizationModels.md 'AlifeApi.WebApi.Models.AuthorizationModels')

## RefreshTokenOutput Class

刷新Token

```csharp
public class RefreshTokenOutput
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; RefreshTokenOutput
### Properties

<a name='AlifeApi.WebApi.Models.AuthorizationModels.RefreshTokenOutput.ExpireDate'></a>

## RefreshTokenOutput.ExpireDate Property

到期日

```csharp
public System.Nullable<System.DateTime> ExpireDate { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.DateTime](https://docs.microsoft.com/en-us/dotnet/api/System.DateTime 'System.DateTime')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

<a name='AlifeApi.WebApi.Models.AuthorizationModels.RefreshTokenOutput.Message'></a>

## RefreshTokenOutput.Message Property

訊息內容

```csharp
public string Message { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.WebApi.Models.AuthorizationModels.RefreshTokenOutput.Token'></a>

## RefreshTokenOutput.Token Property

JWT Token

```csharp
public string Token { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')