#### [AlifeApi.BusinessRules](index.md 'index')
### [AlifeApi.BusinessRules.WeeklyReportModels](AlifeApi.BusinessRules.WeeklyReportModels.md 'AlifeApi.BusinessRules.WeeklyReportModels')

## DailyStatistic Class

每日統計

```csharp
public class DailyStatistic
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; DailyStatistic
### Properties

<a name='AlifeApi.BusinessRules.WeeklyReportModels.DailyStatistic.Count'></a>

## DailyStatistic.Count Property

數量

```csharp
public int Count { get; set; }
```

#### Property Value
[System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')

<a name='AlifeApi.BusinessRules.WeeklyReportModels.DailyStatistic.Date'></a>

## DailyStatistic.Date Property

日期

```csharp
public System.DateTime Date { get; set; }
```

#### Property Value
[System.DateTime](https://docs.microsoft.com/en-us/dotnet/api/System.DateTime 'System.DateTime')

<a name='AlifeApi.BusinessRules.WeeklyReportModels.DailyStatistic.DayOfWeek'></a>

## DailyStatistic.DayOfWeek Property

星期幾

```csharp
public string DayOfWeek { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')