using System.Text.Json.Serialization;
using System.ComponentModel.DataAnnotations;

namespace AlifeApi.BusinessRules.UserInfoModels
{
    /// <summary>
    /// 更新使用者狀態輸入資料 (PostgreSQL版本)
    /// </summary>
    public class UserInfoStatusUpdateInputPg
    {
        /// <summary>
        /// 使用者ID
        /// </summary>
        [JsonPropertyName("UserInfoId")]
        [Required]
        public string UserInfoId { get; set; }

        /// <summary>
        /// 帳號狀態 (true: 啟用, false: 停用)
        /// </summary>
        [JsonPropertyName("Status")]
        [Required]
        public bool Status { get; set; }
    }
} 
