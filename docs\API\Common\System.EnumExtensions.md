#### [AlifeApi.Common](index.md 'index')
### [System](System.md 'System')

## EnumExtensions Class

Enum 擴充方法

```csharp
public static class EnumExtensions
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; EnumExtensions
### Methods

<a name='System.EnumExtensions.GetDescription(thisSystem.Enum)'></a>

## EnumExtensions.GetDescription(this Enum) Method

取得 Enum DescriptionAttribute 的 Description

```csharp
public static string GetDescription(this System.Enum value);
```
#### Parameters

<a name='System.EnumExtensions.GetDescription(thisSystem.Enum).value'></a>

`value` [System.Enum](https://docs.microsoft.com/en-us/dotnet/api/System.Enum 'System.Enum')

#### Returns
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')