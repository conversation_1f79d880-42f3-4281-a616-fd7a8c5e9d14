#### [AlifeApi.BusinessRules](index.md 'index')
### [AlifeApi.BusinessRules.ParkingSpaceModels](AlifeApi.BusinessRules.ParkingSpaceModels.md 'AlifeApi.BusinessRules.ParkingSpaceModels')

## ParkingSpaceUpdateInput Class

停車位更新輸入模型

```csharp
public class ParkingSpaceUpdateInput
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; ParkingSpaceUpdateInput
### Properties

<a name='AlifeApi.BusinessRules.ParkingSpaceModels.ParkingSpaceUpdateInput.Location'></a>

## ParkingSpaceUpdateInput.Location Property

車位詳細位置描述 (例如 "靠近電梯", "角落位置")

```csharp
public string? Location { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')