#### [AlifeApi.WebApi](index.md 'index')
### [AlifeApi.WebApi.Controllers](AlifeApi.WebApi.Controllers.md 'AlifeApi.WebApi.Controllers')

## CrmOptionsController Class

CRM選項管理

```csharp
public class CrmOptionsController : AlifeApi.WebApi.Controllers.AuthenticatedController
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; [Microsoft.AspNetCore.Mvc.ControllerBase](https://docs.microsoft.com/en-us/dotnet/api/Microsoft.AspNetCore.Mvc.ControllerBase 'Microsoft.AspNetCore.Mvc.ControllerBase') &#129106; [Microsoft.AspNetCore.Mvc.Controller](https://docs.microsoft.com/en-us/dotnet/api/Microsoft.AspNetCore.Mvc.Controller 'Microsoft.AspNetCore.Mvc.Controller') &#129106; [AlifeApi.WebApi.Controllers.BasicController](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.WebApi.Controllers.BasicController 'AlifeApi.WebApi.Controllers.BasicController') &#129106; [AlifeApi.WebApi.Controllers.AuthenticatedController](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.WebApi.Controllers.AuthenticatedController 'AlifeApi.WebApi.Controllers.AuthenticatedController') &#129106; CrmOptionsController
### Methods

<a name='AlifeApi.WebApi.Controllers.CrmOptionsController.CreateCrmOption(AlifeApi.BusinessRules.CrmOptionModels.CrmOptionCreateInput)'></a>

## CrmOptionsController.CreateCrmOption(CrmOptionCreateInput) Method

新增CRM選項

```csharp
public System.Threading.Tasks.Task<Microsoft.AspNetCore.Mvc.ActionResult<long>> CreateCrmOption(AlifeApi.BusinessRules.CrmOptionModels.CrmOptionCreateInput input);
```
#### Parameters

<a name='AlifeApi.WebApi.Controllers.CrmOptionsController.CreateCrmOption(AlifeApi.BusinessRules.CrmOptionModels.CrmOptionCreateInput).input'></a>

`input` [AlifeApi.BusinessRules.CrmOptionModels.CrmOptionCreateInput](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.BusinessRules.CrmOptionModels.CrmOptionCreateInput 'AlifeApi.BusinessRules.CrmOptionModels.CrmOptionCreateInput')

CRM選項建立輸入資料

#### Returns
[System.Threading.Tasks.Task&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')[Microsoft.AspNetCore.Mvc.ActionResult&lt;](https://docs.microsoft.com/en-us/dotnet/api/Microsoft.AspNetCore.Mvc.ActionResult-1 'Microsoft.AspNetCore.Mvc.ActionResult`1')[System.Int64](https://docs.microsoft.com/en-us/dotnet/api/System.Int64 'System.Int64')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/Microsoft.AspNetCore.Mvc.ActionResult-1 'Microsoft.AspNetCore.Mvc.ActionResult`1')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')  
新增的CRM選項ID

<a name='AlifeApi.WebApi.Controllers.CrmOptionsController.DeleteCrmOption(long)'></a>

## CrmOptionsController.DeleteCrmOption(long) Method

刪除CRM選項

```csharp
public System.Threading.Tasks.Task<Microsoft.AspNetCore.Mvc.ActionResult> DeleteCrmOption(long siteCrmOptionId);
```
#### Parameters

<a name='AlifeApi.WebApi.Controllers.CrmOptionsController.DeleteCrmOption(long).siteCrmOptionId'></a>

`siteCrmOptionId` [System.Int64](https://docs.microsoft.com/en-us/dotnet/api/System.Int64 'System.Int64')

CRM選項ID

#### Returns
[System.Threading.Tasks.Task&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')[Microsoft.AspNetCore.Mvc.ActionResult](https://docs.microsoft.com/en-us/dotnet/api/Microsoft.AspNetCore.Mvc.ActionResult 'Microsoft.AspNetCore.Mvc.ActionResult')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')  
NoContent

<a name='AlifeApi.WebApi.Controllers.CrmOptionsController.GetCrmOption(long)'></a>

## CrmOptionsController.GetCrmOption(long) Method

根據CRM選項ID取得詳細資訊

```csharp
public System.Threading.Tasks.Task<Microsoft.AspNetCore.Mvc.ActionResult<AlifeApi.BusinessRules.CrmOptionModels.CrmOptionOutput>> GetCrmOption(long siteCrmOptionId);
```
#### Parameters

<a name='AlifeApi.WebApi.Controllers.CrmOptionsController.GetCrmOption(long).siteCrmOptionId'></a>

`siteCrmOptionId` [System.Int64](https://docs.microsoft.com/en-us/dotnet/api/System.Int64 'System.Int64')

CRM選項ID

#### Returns
[System.Threading.Tasks.Task&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')[Microsoft.AspNetCore.Mvc.ActionResult&lt;](https://docs.microsoft.com/en-us/dotnet/api/Microsoft.AspNetCore.Mvc.ActionResult-1 'Microsoft.AspNetCore.Mvc.ActionResult`1')[AlifeApi.BusinessRules.CrmOptionModels.CrmOptionOutput](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.BusinessRules.CrmOptionModels.CrmOptionOutput 'AlifeApi.BusinessRules.CrmOptionModels.CrmOptionOutput')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/Microsoft.AspNetCore.Mvc.ActionResult-1 'Microsoft.AspNetCore.Mvc.ActionResult`1')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')  
CRM選項詳細資訊

<a name='AlifeApi.WebApi.Controllers.CrmOptionsController.GetCrmOptionDropdown(AlifeApi.BusinessRules.CrmOptionModels.CrmOptionDropdownInput)'></a>

## CrmOptionsController.GetCrmOptionDropdown(CrmOptionDropdownInput) Method

取得CRM選項下拉選單

```csharp
public System.Threading.Tasks.Task<Microsoft.AspNetCore.Mvc.ActionResult> GetCrmOptionDropdown(AlifeApi.BusinessRules.CrmOptionModels.CrmOptionDropdownInput input);
```
#### Parameters

<a name='AlifeApi.WebApi.Controllers.CrmOptionsController.GetCrmOptionDropdown(AlifeApi.BusinessRules.CrmOptionModels.CrmOptionDropdownInput).input'></a>

`input` [AlifeApi.BusinessRules.CrmOptionModels.CrmOptionDropdownInput](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.BusinessRules.CrmOptionModels.CrmOptionDropdownInput 'AlifeApi.BusinessRules.CrmOptionModels.CrmOptionDropdownInput')

查詢條件

#### Returns
[System.Threading.Tasks.Task&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')[Microsoft.AspNetCore.Mvc.ActionResult](https://docs.microsoft.com/en-us/dotnet/api/Microsoft.AspNetCore.Mvc.ActionResult 'Microsoft.AspNetCore.Mvc.ActionResult')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')  
CRM選項下拉選單列表

<a name='AlifeApi.WebApi.Controllers.CrmOptionsController.GetCrmOptions(AlifeApi.BusinessRules.CrmOptionModels.CrmOptionQueryInput)'></a>

## CrmOptionsController.GetCrmOptions(CrmOptionQueryInput) Method

取得CRM選項列表 (分頁)

```csharp
public System.Threading.Tasks.Task<Microsoft.AspNetCore.Mvc.ActionResult<AlifeApi.BusinessRules.Infrastructure.PagedListOutput<AlifeApi.BusinessRules.CrmOptionModels.CrmOptionListOutput>>> GetCrmOptions(AlifeApi.BusinessRules.CrmOptionModels.CrmOptionQueryInput input);
```
#### Parameters

<a name='AlifeApi.WebApi.Controllers.CrmOptionsController.GetCrmOptions(AlifeApi.BusinessRules.CrmOptionModels.CrmOptionQueryInput).input'></a>

`input` [AlifeApi.BusinessRules.CrmOptionModels.CrmOptionQueryInput](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.BusinessRules.CrmOptionModels.CrmOptionQueryInput 'AlifeApi.BusinessRules.CrmOptionModels.CrmOptionQueryInput')

查詢條件

#### Returns
[System.Threading.Tasks.Task&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')[Microsoft.AspNetCore.Mvc.ActionResult&lt;](https://docs.microsoft.com/en-us/dotnet/api/Microsoft.AspNetCore.Mvc.ActionResult-1 'Microsoft.AspNetCore.Mvc.ActionResult`1')[AlifeApi.BusinessRules.Infrastructure.PagedListOutput&lt;](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.BusinessRules.Infrastructure.PagedListOutput-1 'AlifeApi.BusinessRules.Infrastructure.PagedListOutput`1')[AlifeApi.BusinessRules.CrmOptionModels.CrmOptionListOutput](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.BusinessRules.CrmOptionModels.CrmOptionListOutput 'AlifeApi.BusinessRules.CrmOptionModels.CrmOptionListOutput')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.BusinessRules.Infrastructure.PagedListOutput-1 'AlifeApi.BusinessRules.Infrastructure.PagedListOutput`1')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/Microsoft.AspNetCore.Mvc.ActionResult-1 'Microsoft.AspNetCore.Mvc.ActionResult`1')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')  
分頁的CRM選項列表

<a name='AlifeApi.WebApi.Controllers.CrmOptionsController.GetCrmOptionTypeDropdownAsync()'></a>

## CrmOptionsController.GetCrmOptionTypeDropdownAsync() Method

取得CRM選項類型下拉選單

```csharp
public System.Threading.Tasks.Task<Microsoft.AspNetCore.Mvc.ActionResult> GetCrmOptionTypeDropdownAsync();
```

#### Returns
[System.Threading.Tasks.Task&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')[Microsoft.AspNetCore.Mvc.ActionResult](https://docs.microsoft.com/en-us/dotnet/api/Microsoft.AspNetCore.Mvc.ActionResult 'Microsoft.AspNetCore.Mvc.ActionResult')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')  
CRM選項類型下拉選單列表

<a name='AlifeApi.WebApi.Controllers.CrmOptionsController.UpdateCrmOption(long,AlifeApi.BusinessRules.CrmOptionModels.CrmOptionUpdateInput)'></a>

## CrmOptionsController.UpdateCrmOption(long, CrmOptionUpdateInput) Method

更新CRM選項

```csharp
public System.Threading.Tasks.Task<Microsoft.AspNetCore.Mvc.ActionResult> UpdateCrmOption(long siteCrmOptionId, AlifeApi.BusinessRules.CrmOptionModels.CrmOptionUpdateInput input);
```
#### Parameters

<a name='AlifeApi.WebApi.Controllers.CrmOptionsController.UpdateCrmOption(long,AlifeApi.BusinessRules.CrmOptionModels.CrmOptionUpdateInput).siteCrmOptionId'></a>

`siteCrmOptionId` [System.Int64](https://docs.microsoft.com/en-us/dotnet/api/System.Int64 'System.Int64')

CRM選項ID

<a name='AlifeApi.WebApi.Controllers.CrmOptionsController.UpdateCrmOption(long,AlifeApi.BusinessRules.CrmOptionModels.CrmOptionUpdateInput).input'></a>

`input` [AlifeApi.BusinessRules.CrmOptionModels.CrmOptionUpdateInput](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.BusinessRules.CrmOptionModels.CrmOptionUpdateInput 'AlifeApi.BusinessRules.CrmOptionModels.CrmOptionUpdateInput')

CRM選項更新輸入資料

#### Returns
[System.Threading.Tasks.Task&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')[Microsoft.AspNetCore.Mvc.ActionResult](https://docs.microsoft.com/en-us/dotnet/api/Microsoft.AspNetCore.Mvc.ActionResult 'Microsoft.AspNetCore.Mvc.ActionResult')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')  
NoContent