#### [AlifeApi.BusinessRules](index.md 'index')
### [AlifeApi.BusinessRules.SupplierModels](AlifeApi.BusinessRules.SupplierModels.md 'AlifeApi.BusinessRules.SupplierModels')

## SupplierUpdateInput Class

```csharp
public class SupplierUpdateInput
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; SupplierUpdateInput
### Properties

<a name='AlifeApi.BusinessRules.SupplierModels.SupplierUpdateInput.ActualFiles'></a>

## SupplierUpdateInput.ActualFiles Property

The actual new/updated file uploads.

```csharp
public System.Collections.Generic.List<Microsoft.AspNetCore.Http.IFormFile> ActualFiles { get; set; }
```

#### Property Value
[System.Collections.Generic.List&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Collections.Generic.List-1 'System.Collections.Generic.List`1')[Microsoft.AspNetCore.Http.IFormFile](https://docs.microsoft.com/en-us/dotnet/api/Microsoft.AspNetCore.Http.IFormFile 'Microsoft.AspNetCore.Http.IFormFile')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Collections.Generic.List-1 'System.Collections.Generic.List`1')

<a name='AlifeApi.BusinessRules.SupplierModels.SupplierUpdateInput.Files'></a>

## SupplierUpdateInput.Files Property

List of metadata for new/updated files. Should correspond to ActualFiles.

```csharp
public System.Collections.Generic.List<AlifeApi.BusinessRules.SupplierModels.SupplierFileInput> Files { get; set; }
```

#### Property Value
[System.Collections.Generic.List&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Collections.Generic.List-1 'System.Collections.Generic.List`1')[AlifeApi.BusinessRules.SupplierModels.SupplierFileInput](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.BusinessRules.SupplierModels.SupplierFileInput 'AlifeApi.BusinessRules.SupplierModels.SupplierFileInput')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Collections.Generic.List-1 'System.Collections.Generic.List`1')