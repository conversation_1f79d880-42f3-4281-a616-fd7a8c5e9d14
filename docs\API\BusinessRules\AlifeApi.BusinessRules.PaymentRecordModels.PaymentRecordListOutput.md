#### [AlifeApi.BusinessRules](index.md 'index')
### [AlifeApi.BusinessRules.PaymentRecordModels](AlifeApi.BusinessRules.PaymentRecordModels.md 'AlifeApi.BusinessRules.PaymentRecordModels')

## PaymentRecordListOutput Class

收款記錄列表輸出項目

```csharp
public class PaymentRecordListOutput
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; PaymentRecordListOutput
### Properties

<a name='AlifeApi.BusinessRules.PaymentRecordModels.PaymentRecordListOutput.Amount'></a>

## PaymentRecordListOutput.Amount Property

收款金額

```csharp
public decimal Amount { get; set; }
```

#### Property Value
[System.Decimal](https://docs.microsoft.com/en-us/dotnet/api/System.Decimal 'System.Decimal')

<a name='AlifeApi.BusinessRules.PaymentRecordModels.PaymentRecordListOutput.CreatedTime'></a>

## PaymentRecordListOutput.CreatedTime Property

建立時間

```csharp
public System.DateTime CreatedTime { get; set; }
```

#### Property Value
[System.DateTime](https://docs.microsoft.com/en-us/dotnet/api/System.DateTime 'System.DateTime')

<a name='AlifeApi.BusinessRules.PaymentRecordModels.PaymentRecordListOutput.CreatedUserInfoId'></a>

## PaymentRecordListOutput.CreatedUserInfoId Property

建立者 UserInfoId

```csharp
public string CreatedUserInfoId { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.PaymentRecordModels.PaymentRecordListOutput.CreatedUserName'></a>

## PaymentRecordListOutput.CreatedUserName Property

建立者名稱 (選填)

```csharp
public string? CreatedUserName { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.PaymentRecordModels.PaymentRecordListOutput.OrderId'></a>

## PaymentRecordListOutput.OrderId Property

訂單ID

```csharp
public int OrderId { get; set; }
```

#### Property Value
[System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')

<a name='AlifeApi.BusinessRules.PaymentRecordModels.PaymentRecordListOutput.PaymentDate'></a>

## PaymentRecordListOutput.PaymentDate Property

收款日期

```csharp
public System.DateOnly PaymentDate { get; set; }
```

#### Property Value
[System.DateOnly](https://docs.microsoft.com/en-us/dotnet/api/System.DateOnly 'System.DateOnly')

<a name='AlifeApi.BusinessRules.PaymentRecordModels.PaymentRecordListOutput.PaymentMethod'></a>

## PaymentRecordListOutput.PaymentMethod Property

收款方式

```csharp
public string PaymentMethod { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.PaymentRecordModels.PaymentRecordListOutput.PaymentRecordId'></a>

## PaymentRecordListOutput.PaymentRecordId Property

收款紀錄唯一識別碼

```csharp
public int PaymentRecordId { get; set; }
```

#### Property Value
[System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')

<a name='AlifeApi.BusinessRules.PaymentRecordModels.PaymentRecordListOutput.PaymentType'></a>

## PaymentRecordListOutput.PaymentType Property

款別

```csharp
public string PaymentType { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.PaymentRecordModels.PaymentRecordListOutput.Remarks'></a>

## PaymentRecordListOutput.Remarks Property

備註

```csharp
public string? Remarks { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')