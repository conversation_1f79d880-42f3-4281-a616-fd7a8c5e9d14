﻿using AlifeApi.BusinessRules.Infrastructure;
using AlifeApi.WebApi.Options;
using Microsoft.Extensions.Options;

namespace AlifeApi.WebApi.Util
{
    public class CurrentUser : ICurrentUser
    {
        private readonly HttpContext _httpContext;
        private readonly SystemOptions _systemOptions;

        public CurrentUser(IHttpContextAccessor httpContextAccessor, IOptionsSnapshot<SystemOptions> systemOptionsAccessor)
        {
            ArgumentNullException.ThrowIfNull(httpContextAccessor, nameof(httpContextAccessor));
            ArgumentNullException.ThrowIfNull(systemOptionsAccessor, nameof(systemOptionsAccessor));

            _httpContext = httpContextAccessor.HttpContext;
            _systemOptions = systemOptionsAccessor.Value;
        }

        public string UserId => _httpContext.User?.Identity?.Name;

        public string DeptId => _httpContext.User?.Claims.Where(x => x.Type == KunYouClaimTypes.DeptId)
            .SingleOrDefault()?.Value;

        public string GradeCode => _httpContext.User?.Claims.Where(x => x.Type == KunYouClaimTypes.GradeCode)
            .SingleOrDefault()?.Value;

        public IEnumerable<string> RoleIds => _httpContext.User.Claims.Where(x => x.Type == KunYouClaimTypes.Role)
            .Select(x => x.Value);

        public string System => _systemOptions.SystemName;

        public string IPAddress => _httpContext.Request.Headers.ContainsKey("X-Forwarded-For")
            ? _httpContext.Request.Headers["X-Forwarded-For"]
            : _httpContext.Connection?.RemoteIpAddress.MapToIPv4().ToString();
    }
}
