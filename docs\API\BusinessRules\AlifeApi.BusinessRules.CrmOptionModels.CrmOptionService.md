#### [AlifeApi.BusinessRules](index.md 'index')
### [AlifeApi.BusinessRules.CrmOptionModels](AlifeApi.BusinessRules.CrmOptionModels.md 'AlifeApi.BusinessRules.CrmOptionModels')

## CrmOptionService Class

CRM選項服務

```csharp
public class CrmOptionService : AlifeApi.BusinessRules.Infrastructure.ServiceBase<AlifeApi.DataAccess.ProjectContent.alifeContext>
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; [AlifeApi.Common.InfrastructureBase](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.Common.InfrastructureBase 'AlifeApi.Common.InfrastructureBase') &#129106; [AlifeApi.BusinessRules.Infrastructure.ServiceBase&lt;](AlifeApi.BusinessRules.Infrastructure.ServiceBase_TDbContext_.md 'AlifeApi.BusinessRules.Infrastructure.ServiceBase<TDbContext>')[AlifeApi.DataAccess.ProjectContent.alifeContext](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.DataAccess.ProjectContent.alifeContext 'AlifeApi.DataAccess.ProjectContent.alifeContext')[&gt;](AlifeApi.BusinessRules.Infrastructure.ServiceBase_TDbContext_.md 'AlifeApi.BusinessRules.Infrastructure.ServiceBase<TDbContext>') &#129106; CrmOptionService
### Methods

<a name='AlifeApi.BusinessRules.CrmOptionModels.CrmOptionService.CreateCrmOptionAsync(AlifeApi.BusinessRules.CrmOptionModels.CrmOptionCreateInput)'></a>

## CrmOptionService.CreateCrmOptionAsync(CrmOptionCreateInput) Method

建立CRM選項

```csharp
public System.Threading.Tasks.Task<long> CreateCrmOptionAsync(AlifeApi.BusinessRules.CrmOptionModels.CrmOptionCreateInput input);
```
#### Parameters

<a name='AlifeApi.BusinessRules.CrmOptionModels.CrmOptionService.CreateCrmOptionAsync(AlifeApi.BusinessRules.CrmOptionModels.CrmOptionCreateInput).input'></a>

`input` [CrmOptionCreateInput](AlifeApi.BusinessRules.CrmOptionModels.CrmOptionCreateInput.md 'AlifeApi.BusinessRules.CrmOptionModels.CrmOptionCreateInput')

CRM選項建立輸入資料

#### Returns
[System.Threading.Tasks.Task&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')[System.Int64](https://docs.microsoft.com/en-us/dotnet/api/System.Int64 'System.Int64')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')  
新建CRM選項的ID

<a name='AlifeApi.BusinessRules.CrmOptionModels.CrmOptionService.DeleteCrmOptionAsync(long)'></a>

## CrmOptionService.DeleteCrmOptionAsync(long) Method

刪除CRM選項

```csharp
public System.Threading.Tasks.Task DeleteCrmOptionAsync(long siteCrmOptionId);
```
#### Parameters

<a name='AlifeApi.BusinessRules.CrmOptionModels.CrmOptionService.DeleteCrmOptionAsync(long).siteCrmOptionId'></a>

`siteCrmOptionId` [System.Int64](https://docs.microsoft.com/en-us/dotnet/api/System.Int64 'System.Int64')

CRM選項ID

#### Returns
[System.Threading.Tasks.Task](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task 'System.Threading.Tasks.Task')

<a name='AlifeApi.BusinessRules.CrmOptionModels.CrmOptionService.FillSiteAndTypeNamesAsync_T_(System.Collections.Generic.IEnumerable_T_)'></a>

## CrmOptionService.FillSiteAndTypeNamesAsync<T>(IEnumerable<T>) Method

填充案場名稱和選項類型名稱

```csharp
private System.Threading.Tasks.Task FillSiteAndTypeNamesAsync<T>(System.Collections.Generic.IEnumerable<T> items)
    where T : class;
```
#### Type parameters

<a name='AlifeApi.BusinessRules.CrmOptionModels.CrmOptionService.FillSiteAndTypeNamesAsync_T_(System.Collections.Generic.IEnumerable_T_).T'></a>

`T`
#### Parameters

<a name='AlifeApi.BusinessRules.CrmOptionModels.CrmOptionService.FillSiteAndTypeNamesAsync_T_(System.Collections.Generic.IEnumerable_T_).items'></a>

`items` [System.Collections.Generic.IEnumerable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Collections.Generic.IEnumerable-1 'System.Collections.Generic.IEnumerable`1')[T](AlifeApi.BusinessRules.CrmOptionModels.CrmOptionService.md#AlifeApi.BusinessRules.CrmOptionModels.CrmOptionService.FillSiteAndTypeNamesAsync_T_(System.Collections.Generic.IEnumerable_T_).T 'AlifeApi.BusinessRules.CrmOptionModels.CrmOptionService.FillSiteAndTypeNamesAsync<T>(System.Collections.Generic.IEnumerable<T>).T')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Collections.Generic.IEnumerable-1 'System.Collections.Generic.IEnumerable`1')

需要填充的項目列表

#### Returns
[System.Threading.Tasks.Task](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task 'System.Threading.Tasks.Task')

<a name='AlifeApi.BusinessRules.CrmOptionModels.CrmOptionService.GetCrmOptionByIdAsync(long)'></a>

## CrmOptionService.GetCrmOptionByIdAsync(long) Method

根據ID取得CRM選項詳細資料

```csharp
public System.Threading.Tasks.Task<AlifeApi.BusinessRules.CrmOptionModels.CrmOptionOutput?> GetCrmOptionByIdAsync(long siteCrmOptionId);
```
#### Parameters

<a name='AlifeApi.BusinessRules.CrmOptionModels.CrmOptionService.GetCrmOptionByIdAsync(long).siteCrmOptionId'></a>

`siteCrmOptionId` [System.Int64](https://docs.microsoft.com/en-us/dotnet/api/System.Int64 'System.Int64')

CRM選項ID

#### Returns
[System.Threading.Tasks.Task&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')[CrmOptionOutput](AlifeApi.BusinessRules.CrmOptionModels.CrmOptionOutput.md 'AlifeApi.BusinessRules.CrmOptionModels.CrmOptionOutput')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')  
CRM選項詳細資料

<a name='AlifeApi.BusinessRules.CrmOptionModels.CrmOptionService.GetCrmOptionDropdownListAsync(AlifeApi.BusinessRules.CrmOptionModels.CrmOptionDropdownInput)'></a>

## CrmOptionService.GetCrmOptionDropdownListAsync(CrmOptionDropdownInput) Method

取得CRM選項下拉選單

```csharp
public System.Threading.Tasks.Task<System.Collections.Generic.List<AlifeApi.BusinessRules.CrmOptionModels.CrmOptionDropdownOutput>> GetCrmOptionDropdownListAsync(AlifeApi.BusinessRules.CrmOptionModels.CrmOptionDropdownInput input);
```
#### Parameters

<a name='AlifeApi.BusinessRules.CrmOptionModels.CrmOptionService.GetCrmOptionDropdownListAsync(AlifeApi.BusinessRules.CrmOptionModels.CrmOptionDropdownInput).input'></a>

`input` [CrmOptionDropdownInput](AlifeApi.BusinessRules.CrmOptionModels.CrmOptionDropdownInput.md 'AlifeApi.BusinessRules.CrmOptionModels.CrmOptionDropdownInput')

查詢條件

#### Returns
[System.Threading.Tasks.Task&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')[System.Collections.Generic.List&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Collections.Generic.List-1 'System.Collections.Generic.List`1')[CrmOptionDropdownOutput](AlifeApi.BusinessRules.CrmOptionModels.CrmOptionDropdownOutput.md 'AlifeApi.BusinessRules.CrmOptionModels.CrmOptionDropdownOutput')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Collections.Generic.List-1 'System.Collections.Generic.List`1')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')  
CRM選項下拉選單列表

<a name='AlifeApi.BusinessRules.CrmOptionModels.CrmOptionService.GetCrmOptionListAsync(AlifeApi.BusinessRules.CrmOptionModels.CrmOptionQueryInput)'></a>

## CrmOptionService.GetCrmOptionListAsync(CrmOptionQueryInput) Method

取得CRM選項列表 (分頁)

```csharp
public System.Threading.Tasks.Task<AlifeApi.BusinessRules.Infrastructure.PagedListOutput<AlifeApi.BusinessRules.CrmOptionModels.CrmOptionListOutput>> GetCrmOptionListAsync(AlifeApi.BusinessRules.CrmOptionModels.CrmOptionQueryInput input);
```
#### Parameters

<a name='AlifeApi.BusinessRules.CrmOptionModels.CrmOptionService.GetCrmOptionListAsync(AlifeApi.BusinessRules.CrmOptionModels.CrmOptionQueryInput).input'></a>

`input` [CrmOptionQueryInput](AlifeApi.BusinessRules.CrmOptionModels.CrmOptionQueryInput.md 'AlifeApi.BusinessRules.CrmOptionModels.CrmOptionQueryInput')

查詢條件

#### Returns
[System.Threading.Tasks.Task&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')[AlifeApi.BusinessRules.Infrastructure.PagedListOutput&lt;](AlifeApi.BusinessRules.Infrastructure.PagedListOutput_TDetail_.md 'AlifeApi.BusinessRules.Infrastructure.PagedListOutput<TDetail>')[CrmOptionListOutput](AlifeApi.BusinessRules.CrmOptionModels.CrmOptionListOutput.md 'AlifeApi.BusinessRules.CrmOptionModels.CrmOptionListOutput')[&gt;](AlifeApi.BusinessRules.Infrastructure.PagedListOutput_TDetail_.md 'AlifeApi.BusinessRules.Infrastructure.PagedListOutput<TDetail>')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')  
分頁的CRM選項列表

<a name='AlifeApi.BusinessRules.CrmOptionModels.CrmOptionService.GetCrmOptionTypeDropdownListAsync()'></a>

## CrmOptionService.GetCrmOptionTypeDropdownListAsync() Method

取得CRM選項類型下拉選單

```csharp
public System.Threading.Tasks.Task<System.Collections.Generic.List<AlifeApi.BusinessRules.CrmOptionModels.CrmOptionTypeDropdownOutput>> GetCrmOptionTypeDropdownListAsync();
```

#### Returns
[System.Threading.Tasks.Task&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')[System.Collections.Generic.List&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Collections.Generic.List-1 'System.Collections.Generic.List`1')[CrmOptionTypeDropdownOutput](AlifeApi.BusinessRules.CrmOptionModels.CrmOptionTypeDropdownOutput.md 'AlifeApi.BusinessRules.CrmOptionModels.CrmOptionTypeDropdownOutput')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Collections.Generic.List-1 'System.Collections.Generic.List`1')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')  
CRM選項類型下拉選單列表

<a name='AlifeApi.BusinessRules.CrmOptionModels.CrmOptionService.UpdateCrmOptionAsync(long,AlifeApi.BusinessRules.CrmOptionModels.CrmOptionUpdateInput)'></a>

## CrmOptionService.UpdateCrmOptionAsync(long, CrmOptionUpdateInput) Method

更新CRM選項資訊

```csharp
public System.Threading.Tasks.Task UpdateCrmOptionAsync(long siteCrmOptionId, AlifeApi.BusinessRules.CrmOptionModels.CrmOptionUpdateInput input);
```
#### Parameters

<a name='AlifeApi.BusinessRules.CrmOptionModels.CrmOptionService.UpdateCrmOptionAsync(long,AlifeApi.BusinessRules.CrmOptionModels.CrmOptionUpdateInput).siteCrmOptionId'></a>

`siteCrmOptionId` [System.Int64](https://docs.microsoft.com/en-us/dotnet/api/System.Int64 'System.Int64')

CRM選項ID

<a name='AlifeApi.BusinessRules.CrmOptionModels.CrmOptionService.UpdateCrmOptionAsync(long,AlifeApi.BusinessRules.CrmOptionModels.CrmOptionUpdateInput).input'></a>

`input` [CrmOptionUpdateInput](AlifeApi.BusinessRules.CrmOptionModels.CrmOptionUpdateInput.md 'AlifeApi.BusinessRules.CrmOptionModels.CrmOptionUpdateInput')

CRM選項更新輸入資料

#### Returns
[System.Threading.Tasks.Task](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task 'System.Threading.Tasks.Task')