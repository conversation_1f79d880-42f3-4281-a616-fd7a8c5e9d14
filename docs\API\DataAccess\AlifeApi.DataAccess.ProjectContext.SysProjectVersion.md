#### [AlifeApi.DataAccess](index.md 'index')
### [AlifeApi.DataAccess.ProjectContext](AlifeApi.DataAccess.ProjectContext.md 'AlifeApi.DataAccess.ProjectContext')

## SysProjectVersion Class

```csharp
public class SysProjectVersion
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; SysProjectVersion
### Properties

<a name='AlifeApi.DataAccess.ProjectContext.SysProjectVersion.PContent'></a>

## SysProjectVersion.PContent Property

更新內容

```csharp
public string PContent { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContext.SysProjectVersion.PCrDateTime'></a>

## SysProjectVersion.PCrDateTime Property

版更時間

```csharp
public System.Nullable<System.DateTime> PCrDateTime { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.DateTime](https://docs.microsoft.com/en-us/dotnet/api/System.DateTime 'System.DateTime')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

<a name='AlifeApi.DataAccess.ProjectContext.SysProjectVersion.PCrUser'></a>

## SysProjectVersion.PCrUser Property

上版人員

```csharp
public string PCrUser { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContext.SysProjectVersion.PProjectName'></a>

## SysProjectVersion.PProjectName Property

專案代號

```csharp
public string PProjectName { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContext.SysProjectVersion.PVersionHash'></a>

## SysProjectVersion.PVersionHash Property

git唯一值

```csharp
public string PVersionHash { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContext.SysProjectVersion.PVersionName'></a>

## SysProjectVersion.PVersionName Property

版號

```csharp
public string PVersionName { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')