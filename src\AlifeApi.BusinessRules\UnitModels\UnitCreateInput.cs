using System;
using System.ComponentModel.DataAnnotations;

namespace AlifeApi.BusinessRules.UnitModels
{
    /// <summary>
    /// 房屋單位建立輸入模型
    /// </summary>
    public class UnitCreateInput
    {
        [Required(ErrorMessage = "缺少樓層ID")]
        public int FloorId { get; set; }

        [Required(ErrorMessage = "缺少建築物ID")]
        public int BuildingId { get; set; }

        [Required(ErrorMessage = "缺少案場代碼")]
        public string SiteCode { get; set; } = null!;

        [Required(ErrorMessage = "缺少戶號")]
        public string UnitNumber { get; set; } = null!;

        [Required(ErrorMessage = "缺少單位類型")]
        public string UnitType { get; set; } = null!;

        public string? Layout { get; set; }
        public string? Orientation { get; set; }

        [Required(ErrorMessage = "缺少主建物坪數")]
        [Range(0.01, double.MaxValue, ErrorMessage = "主建物坪數必須大於 0")]
        public decimal MainArea { get; set; }

        public decimal? AuxiliaryArea { get; set; }
        public decimal? PublicAreaShare { get; set; }

        [Required(ErrorMessage = "缺少權狀總坪數")]
        [Range(0.01, double.MaxValue, ErrorMessage = "權狀總坪數必須大於 0")]
        public decimal TotalArea { get; set; }

        public decimal? ListPrice { get; set; }
        public decimal? MinimumPrice { get; set; }

        [Required(ErrorMessage = "缺少銷售狀態")]
        public string Status { get; set; } = null!;

        public bool? IsPublicAreaIncluded { get; set; }
        public string? AssociatedParkingSpaceIds { get; set; }
        public string? Remarks { get; set; }
    }
} 
