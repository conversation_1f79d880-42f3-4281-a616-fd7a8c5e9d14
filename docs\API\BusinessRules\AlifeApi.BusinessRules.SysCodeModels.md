#### [AlifeApi.BusinessRules](index.md 'index')

## <PERSON>feApi.BusinessRules.SysCodeModels Namespace

| Classes | |
| :--- | :--- |
| [SysCodeAutoCompleteGetInput](AlifeApi.BusinessRules.SysCodeModels.SysCodeAutoCompleteGetInput.md 'AlifeApi.BusinessRules.SysCodeModels.SysCodeAutoCompleteGetInput') | 配合 SysCode 的輸入自動提示 |
| [SysCodeByTypeGetInput](AlifeApi.BusinessRules.SysCodeModels.SysCodeByTypeGetInput.md 'AlifeApi.BusinessRules.SysCodeModels.SysCodeByTypeGetInput') | |
| [SysCodeCreateInput](AlifeApi.BusinessRules.SysCodeModels.SysCodeCreateInput.md 'AlifeApi.BusinessRules.SysCodeModels.SysCodeCreateInput') | 新增 SysCode 代碼 |
| [SysCodeOutput](AlifeApi.BusinessRules.SysCodeModels.SysCodeOutput.md 'AlifeApi.BusinessRules.SysCodeModels.SysCodeOutput') | 單層 SysCode |
| [SysCodeService](AlifeApi.BusinessRules.SysCodeModels.SysCodeService.md 'AlifeApi.BusinessRules.SysCodeModels.SysCodeService') | SYSCode |
| [SysCodeTypeInput](AlifeApi.BusinessRules.SysCodeModels.SysCodeTypeInput.md 'AlifeApi.BusinessRules.SysCodeModels.SysCodeTypeInput') | 查詢特定代碼類別的輸入 DTO |
| [SysCodeUpdateInput](AlifeApi.BusinessRules.SysCodeModels.SysCodeUpdateInput.md 'AlifeApi.BusinessRules.SysCodeModels.SysCodeUpdateInput') | 編輯、作廢 SysCode 代碼 |
| [SysTypeGetInput](AlifeApi.BusinessRules.SysCodeModels.SysTypeGetInput.md 'AlifeApi.BusinessRules.SysCodeModels.SysTypeGetInput') | 取得系統代碼類型 |
| [SysTypeOutput](AlifeApi.BusinessRules.SysCodeModels.SysTypeOutput.md 'AlifeApi.BusinessRules.SysCodeModels.SysTypeOutput') | SysType 類型 |
