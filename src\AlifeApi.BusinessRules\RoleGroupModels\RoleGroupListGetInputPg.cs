using AlifeApi.BusinessRules.Infrastructure;
using System.Text.Json.Serialization;

namespace AlifeApi.BusinessRules.RoleGroupModels
{
    /// <summary>
    /// 取得角色權限列表（PostgreSQL 版本）
    /// </summary>
    public class RoleGroupListGetInputPg : PagedListInput
    {
        /// <summary>
        /// 案場代碼，對應 Sites 表的 SiteCode
        /// </summary>
        [JsonPropertyName("SiteCode")]
        public string SiteCode { get; set; }

        /// <summary>
        /// 是否包含全局角色（SiteCode 為 null 的角色）
        /// </summary>
        [JsonPropertyName("IncludeGlobalRoles")]
        public bool IncludeGlobalRoles { get; set; } = false;
    }
} 
