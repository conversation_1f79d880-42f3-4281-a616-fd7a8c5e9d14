using System;
using System.ComponentModel.DataAnnotations;

namespace AlifeApi.BusinessRules.ParkingSpaceModels
{
    /// <summary>
    /// 停車位建立輸入模型
    /// </summary>
    public class ParkingSpaceCreateInput
    {
        [Required(ErrorMessage = "缺少案場代碼")]
        public string SiteCode { get; set; } = null!;

        [Required(ErrorMessage = "缺少建築物ID")]
        public int BuildingId { get; set; }

        [Required(ErrorMessage = "缺少樓層ID")]
        public int FloorId { get; set; }

        [Required(ErrorMessage = "缺少車位編號")]
        public string SpaceNumber { get; set; } = null!;

        [Required(ErrorMessage = "缺少車位類型")]
        public string SpaceType { get; set; } = null!;

        public string? Remarks { get; set; }

        public decimal? ListPrice { get; set; }
        public decimal? MinimumPrice { get; set; }
        public string Status { get; set; }
    }
} 
