using System;
using System.ComponentModel.DataAnnotations;

namespace AlifeApi.BusinessRules.ParkingSpaceModels
{
    /// <summary>
    /// 停車位建立輸入模型
    /// </summary>
    public class ParkingSpaceCreateInput
    {
        [Required(ErrorMessage = "缺少案場代碼")]
        public string SiteCode { get; set; } = null!;

        [Required(ErrorMessage = "缺少建築物ID")]
        public int BuildingId { get; set; }

        [Required(ErrorMessage = "缺少樓層ID")]
        public int FloorId { get; set; }

        [Required(ErrorMessage = "缺少車位編號")]
        public string SpaceNumber { get; set; } = null!;

        [Required(ErrorMessage = "缺少車位類型")]
        public string SpaceType { get; set; } = null!;

        public string? Dimensions { get; set; }
        
        /// <summary>
        /// 車位詳細位置描述 (例如 "靠近電梯", "角落位置")
        /// </summary>
        public string? Location { get; set; }
        
        public string? Remarks { get; set; }
    }
} 
