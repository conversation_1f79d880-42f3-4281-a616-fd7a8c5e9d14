﻿using AlifeApi.BusinessRules.CustomerModels;
using AlifeApi.BusinessRules.Infrastructure;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Threading.Tasks;

namespace AlifeApi.WebApi.Controllers
{
    /// <summary>
    /// 客戶管理
    /// </summary>
    public class CustomersController : AuthenticatedController
    {
        private readonly CustomerService _customerService;

        public CustomersController(CustomerService customerService)
        {
            _customerService = customerService;
        }

        /// <summary>
        /// 取得客戶列表 (分頁)
        /// </summary>
        /// <param name="input">查詢條件</param>
        /// <returns>分頁的客戶列表</returns>
        [HttpPost]
        public async Task<IActionResult> GetCustomers([FromBody] CustomerQueryInput input)
        {
            var result = await _customerService.GetCustomerListAsync(input);
            return Ok(result);
        }

        /// <summary>
        /// 根據客戶ID取得單一客戶資訊
        /// </summary>
        /// <param name="customerId">客戶ID</param>
        /// <returns>客戶詳細資訊</returns>
        [HttpGet("{customerId}")]
        public async Task<IActionResult> GetCustomer(string customerId)
        {
            var result = await _customerService.GetCustomerByIdAsync(customerId);
            if (result == null)
            {
                return NotFound();
            }
            return Ok(result);
        }

        /// <summary>
        /// 新增客戶
        /// </summary>
        /// <param name="input">新增客戶輸入資料</param>
        /// <returns>新增的客戶ID</returns>
        [HttpPost]
        public async Task<IActionResult> CreateCustomer([FromBody] CustomerInput input)
        {
            try
            {
                var customerId = await _customerService.CreateCustomerAsync(input);
                // 返回 201 Created，並在 Location Header 中提供新資源的 URL
                return CreatedAtAction(nameof(GetCustomer), new { customerId = customerId }, new { CustomerId = customerId });
            }
            catch (Exception ex)
            {
                // 處理服務層可能拋出的錯誤
                return BadRequest(new { message = ex.Message });
            }
        }

        /// <summary>
        /// 更新客戶資料 (包含訪談紀錄的新增/更新)
        /// </summary>
        /// <param name="customerId">要更新的客戶ID</param>
        /// <param name="input">更新的客戶資料 (包含基本資料和訪談紀錄列表)</param>
        /// <returns>成功時返回 NoContent (204)，失敗時返回 NotFound 或 BadRequest</returns>
        [HttpPost("{customerId}")] // 使用 POST 帶 customerId
        public async Task<IActionResult> UpdateCustomer(string customerId, [FromBody] CustomerInput input)
        {
            if (string.IsNullOrEmpty(customerId))
            {
                return BadRequest("未提供客戶 ID");
            }

            try
            {
                // 這裡假設 UpdateCustomerAsync 在找不到客戶時會拋出異常
                await _customerService.UpdateCustomerAsync(customerId, input);
                return Ok(); // 更新成功，返回 204 No Content
            }
            catch (Exception ex) when (ex.Message.Contains("找不到客戶")) // 捕捉找不到客戶的特定錯誤
            {
                return NotFound(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                // 處理其他可能的錯誤
                // 記錄錯誤日誌
                Console.WriteLine($"更新客戶 {customerId} 時發生錯誤: {ex}");
                return StatusCode(500, "更新客戶時發生內部錯誤"); // 返回通用錯誤訊息
            }
        }
    }
} 
