using AlifeApi.BusinessRules.Infrastructure;
using AlifeApi.BusinessRules.UnitModels;
using AlifeApi.BusinessRules.ParkingSpaceModels;
using AlifeApi.DataAccess.ProjectContent;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace AlifeApi.BusinessRules.CommonModels
{
    /// <summary>
    /// 統一銷售管理服務 - 統一處理"可售/保留(房屋資料)"和"可售/保留(車位資料)"
    /// </summary>
    public class UnifiedSalesManagementService : ServiceBase<alifeContext>
    {
        private readonly UnitService _unitService;
        private readonly ParkingSpaceService _parkingSpaceService;

        public UnifiedSalesManagementService(
            IServiceProvider serviceProvider,
            alifeContext dbContext,
            UnitService unitService,
            ParkingSpaceService parkingSpaceService)
            : base(serviceProvider, dbContext)
        {
            _unitService = unitService;
            _parkingSpaceService = parkingSpaceService;
        }

        /// <summary>
        /// 取得統一的銷售物件列表（包含房屋和車位）
        /// </summary>
        /// <param name="input">查詢條件</param>
        /// <returns>統一的銷售物件列表</returns>
        public async Task<PagedListOutput<UnifiedSalesItemOutput>> GetUnifiedSalesListAsync(UnifiedSalesQueryInput input)
        {
            ArgumentNullException.ThrowIfNull(input);

            var allItems = new List<UnifiedSalesItemOutput>();

            // 如果類別為空或包含房屋，查詢房屋資料
            if (string.IsNullOrEmpty(input.ItemType) || input.ItemType == "房屋")
            {
                var unitQuery = BuildUnitQuery(input);
                var units = await unitQuery.ToListAsync();
                
                var unitItems = units.Select(u => new UnifiedSalesItemOutput
                {
                    Id = u.UnitId,
                    ItemType = "房屋",
                    SiteCode = u.SiteCode,
                    BuildingId = u.BuildingId,
                    FloorId = u.FloorId,
                    Number = u.UnitNumber,
                    Type = u.UnitType,
                    Area = u.TotalArea,
                    Layout = u.Layout,
                    ListPrice = u.ListPrice,
                    MinimumPrice = u.MinimumPrice,
                    TransactionPrice = u.TransactionPrice,
                    Status = u.Status,
                    Remarks = u.Remarks,
                    CreatedTime = u.CreatedTime,
                    UpdatedTime = u.UpdatedTime
                }).ToList();

                allItems.AddRange(unitItems);
            }

            // 如果類別為空或包含車位，查詢車位資料
            if (string.IsNullOrEmpty(input.ItemType) || input.ItemType == "車位")
            {
                var parkingQuery = BuildParkingQuery(input);
                var parkingSpaces = await parkingQuery.ToListAsync();

                // 建立車位項目，需要從Units表查詢銷售相關資訊
                var parkingItems = new List<UnifiedSalesItemOutput>();
                foreach (var p in parkingSpaces)
                {
                    // 查詢關聯的車位銷售單位
                    var parkingUnit = await Db.Units
                        .FirstOrDefaultAsync(u => u.UnitType == "車位" &&
                                                  u.AssociatedParkingSpaceIds != null &&
                                                  u.AssociatedParkingSpaceIds.Contains(p.ParkingSpaceId.ToString()));

                    var item = new UnifiedSalesItemOutput
                {
                    Id = p.ParkingSpaceId,
                    ItemType = "車位",
                    SiteCode = p.SiteCode,
                    BuildingId = p.BuildingId,
                    FloorId = p.FloorId,
                    Number = p.SpaceNumber,
                    Type = p.SpaceType,
                    Area = null, // 車位沒有面積概念
                    Layout = p.Dimensions, // 使用尺寸代替格局
                        ListPrice = parkingUnit?.ListPrice,
                        MinimumPrice = parkingUnit?.MinimumPrice,
                        TransactionPrice = parkingUnit?.TransactionPrice,
                        Status = parkingUnit?.Status ?? "未知",
                    Remarks = p.Remarks,
                    CreatedTime = p.CreatedTime,
                    UpdatedTime = p.UpdatedTime
                    };
                    parkingItems.Add(item);
                }

                allItems.AddRange(parkingItems);
            }

            // 排序並分頁
            var sortedItems = allItems
                .OrderBy(item => item.SiteCode)
                .ThenBy(item => item.BuildingId)
                .ThenBy(item => item.FloorId)
                .ThenBy(item => item.ItemType)
                .ThenBy(item => item.Number)
                .ToList();

            // 填充關聯資料
            await FillRelatedDataAsync(sortedItems);

            // 手動分頁
            var totalCount = sortedItems.Count;
            var pagedItems = input.UsingPaging && input.NumberOfPerPage > 0
                ? sortedItems
                    .Skip((input.PageIndex - 1) * input.NumberOfPerPage)
                    .Take(input.NumberOfPerPage)
                    .ToList()
                : sortedItems;

            var output = new PagedListOutput<UnifiedSalesItemOutput>(input)
            {
                Details = pagedItems,
                RecordCount = totalCount
            };

            return output;
        }

        /// <summary>
        /// 取得可售物件列表
        /// </summary>
        public async Task<PagedListOutput<UnifiedSalesItemOutput>> GetAvailableItemsAsync(UnifiedSalesQueryInput input)
        {
            input.Status = "可售";
            return await GetUnifiedSalesListAsync(input);
        }

        /// <summary>
        /// 取得保留物件列表
        /// </summary>
        public async Task<PagedListOutput<UnifiedSalesItemOutput>> GetReservedItemsAsync(UnifiedSalesQueryInput input)
        {
            input.Status = "保留";
            return await GetUnifiedSalesListAsync(input);
        }

        /// <summary>
        /// 建立房屋查詢
        /// </summary>
        private IQueryable<Unit> BuildUnitQuery(UnifiedSalesQueryInput input)
        {
            var query = Db.Units.AsQueryable();

            if (!string.IsNullOrEmpty(input.SiteCode))
                query = query.Where(u => u.SiteCode == input.SiteCode);
            
            if (input.BuildingId.HasValue)
                query = query.Where(u => u.BuildingId == input.BuildingId.Value);
            
            if (input.FloorId.HasValue)
                query = query.Where(u => u.FloorId == input.FloorId.Value);
            
            if (!string.IsNullOrEmpty(input.Status))
                query = query.Where(u => u.Status == input.Status);

            if (!string.IsNullOrEmpty(input.Number))
                query = query.Where(u => u.UnitNumber.Contains(input.Number));

            return query;
        }

        /// <summary>
        /// 建立車位查詢
        /// </summary>
        private IQueryable<ParkingSpace> BuildParkingQuery(UnifiedSalesQueryInput input)
        {
            var query = Db.ParkingSpaces.AsQueryable();

            if (!string.IsNullOrEmpty(input.SiteCode))
                query = query.Where(p => p.SiteCode == input.SiteCode);
            
            if (input.BuildingId.HasValue)
                query = query.Where(p => p.BuildingId == input.BuildingId.Value);
            
            if (input.FloorId.HasValue)
                query = query.Where(p => p.FloorId == input.FloorId.Value);
            
            // 車位狀態現在由Units表管理，暫時移除狀態篩選
            // 後續需要改為透過Units表聯合查詢

            if (!string.IsNullOrEmpty(input.Number))
                query = query.Where(p => p.SpaceNumber.Contains(input.Number));

            return query;
        }

        /// <summary>
        /// 填充關聯資料（案場名稱、建築物名稱、樓層標示）
        /// </summary>
        private async Task FillRelatedDataAsync(List<UnifiedSalesItemOutput> items)
        {
            if (!items.Any()) return;

            var siteCodes = items.Select(i => i.SiteCode).Distinct().ToList();
            var buildingIds = items.Select(i => i.BuildingId).Distinct().ToList();
            var floorIds = items.Select(i => i.FloorId).Distinct().ToList();

            // 查詢關聯資料
            var sites = await Db.Sites
                .Where(s => siteCodes.Contains(s.SiteCode))
                .ToDictionaryAsync(s => s.SiteCode, s => s.SiteName);

            var buildings = await Db.Buildings
                .Where(b => buildingIds.Contains(b.BuildingId))
                .ToDictionaryAsync(b => b.BuildingId, b => b.BuildingName);

            var floors = await Db.Floors
                .Where(f => floorIds.Contains(f.FloorId))
                .ToDictionaryAsync(f => f.FloorId, f => f.FloorLabel);

            // 填充資料
            foreach (var item in items)
            {
                if (sites.TryGetValue(item.SiteCode, out var siteName))
                    item.SiteName = siteName;

                if (buildings.TryGetValue(item.BuildingId, out var buildingName))
                    item.BuildingName = buildingName;

                if (floors.TryGetValue(item.FloorId, out var floorLabel))
                    item.FloorLabel = floorLabel;
            }
        }

        /// <summary>
        /// 取得銷售統計
        /// </summary>
        public async Task<UnifiedSalesStatistics> GetUnifiedSalesStatisticsAsync(string siteCode)
        {
            var unitStats = await _unitService.GetUnitSalesStatisticsAsync(siteCode);
            var parkingStats = await GetParkingSpaceStatisticsFromUnitsAsync(siteCode);

            return new UnifiedSalesStatistics
            {
                SiteCode = siteCode,
                UnitStatistics = unitStats,
                ParkingSpaceStatistics = parkingStats,
                TotalAvailableCount = unitStats.AvailableCount + parkingStats.AvailableCount,
                TotalReservedCount = unitStats.ReservedCount + parkingStats.ReservedCount,
                TotalSoldCount = unitStats.SoldCount + parkingStats.SoldCount,
                GeneratedTime = DateTime.Now
            };
        }

        /// <summary>
        /// 從Units表獲取車位統計資料
        /// </summary>
        private async Task<ParkingSpaceSalesStatistics> GetParkingSpaceStatisticsFromUnitsAsync(string siteCode)
        {
            var parkingUnits = await Db.Units
                .Where(u => u.SiteCode == siteCode && u.UnitType == "車位")
                .ToListAsync();

            var totalCount = parkingUnits.Count();
            var soldCount = parkingUnits.Count(u => u.Status == "已售");

            return new ParkingSpaceSalesStatistics
            {
                SiteCode = siteCode,
                TotalCount = totalCount,
                AvailableCount = parkingUnits.Count(u => u.Status == "可售"),
                ReservedCount = parkingUnits.Count(u => u.Status == "保留"),
                BookedCount = parkingUnits.Count(u => u.Status == "已預訂"),
                SoldCount = soldCount,
                OwnerReservedCount = parkingUnits.Count(u => u.Status == "地主保留"),
                SalesRate = totalCount > 0 ? (decimal)soldCount / totalCount * 100 : 0,
                TotalAvailableListPrice = parkingUnits
                    .Where(u => u.Status == "可售" && u.ListPrice.HasValue)
                    .Sum(u => u.ListPrice.Value),
                TotalReservedListPrice = parkingUnits
                    .Where(u => u.Status == "保留" && u.ListPrice.HasValue)
                    .Sum(u => u.ListPrice.Value)
            };
        }
    }

    /// <summary>
    /// 統一銷售查詢輸入
    /// </summary>
    public class UnifiedSalesQueryInput : PagedListInput
    {
        /// <summary>
        /// 物件類別 ("房屋"、"車位"，空值表示全部)
        /// </summary>
        public string? ItemType { get; set; }

        /// <summary>
        /// 案場編號
        /// </summary>
        public string? SiteCode { get; set; }

        /// <summary>
        /// 建築物ID
        /// </summary>
        public int? BuildingId { get; set; }

        /// <summary>
        /// 樓層ID
        /// </summary>
        public int? FloorId { get; set; }

        /// <summary>
        /// 編號/戶號（模糊查詢）
        /// </summary>
        public string? Number { get; set; }

        /// <summary>
        /// 狀態
        /// </summary>
        public string? Status { get; set; }
    }

    /// <summary>
    /// 統一銷售物件輸出
    /// </summary>
    public class UnifiedSalesItemOutput
    {
        /// <summary>
        /// 物件ID（房屋為UnitId，車位為ParkingSpaceId）
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// 物件類別（"房屋"、"車位"）
        /// </summary>
        public string ItemType { get; set; } = null!;

        /// <summary>
        /// 案場編號
        /// </summary>
        public string SiteCode { get; set; } = null!;

        /// <summary>
        /// 案場名稱
        /// </summary>
        public string? SiteName { get; set; }

        /// <summary>
        /// 建築物ID
        /// </summary>
        public int BuildingId { get; set; }

        /// <summary>
        /// 建築物名稱
        /// </summary>
        public string? BuildingName { get; set; }

        /// <summary>
        /// 樓層ID
        /// </summary>
        public int FloorId { get; set; }

        /// <summary>
        /// 樓層標示
        /// </summary>
        public string? FloorLabel { get; set; }

        /// <summary>
        /// 編號（房屋為戶號，車位為車位編號）
        /// </summary>
        public string Number { get; set; } = null!;

        /// <summary>
        /// 類型（房屋為UnitType，車位為SpaceType）
        /// </summary>
        public string Type { get; set; } = null!;

        /// <summary>
        /// 面積（僅房屋有，車位為null）
        /// </summary>
        public decimal? Area { get; set; }

        /// <summary>
        /// 格局/尺寸（房屋為格局，車位為尺寸）
        /// </summary>
        public string? Layout { get; set; }

        /// <summary>
        /// 表價
        /// </summary>
        public decimal? ListPrice { get; set; }

        /// <summary>
        /// 底價
        /// </summary>
        public decimal? MinimumPrice { get; set; }

        /// <summary>
        /// 成交價
        /// </summary>
        public decimal? TransactionPrice { get; set; }

        /// <summary>
        /// 狀態
        /// </summary>
        public string Status { get; set; } = null!;

        /// <summary>
        /// 備註
        /// </summary>
        public string? Remarks { get; set; }

        /// <summary>
        /// 建立時間
        /// </summary>
        public DateTime CreatedTime { get; set; }

        /// <summary>
        /// 更新時間
        /// </summary>
        public DateTime UpdatedTime { get; set; }
    }

    /// <summary>
    /// 統一銷售統計
    /// </summary>
    public class UnifiedSalesStatistics
    {
        /// <summary>
        /// 案場編號
        /// </summary>
        public string SiteCode { get; set; } = null!;

        /// <summary>
        /// 房屋統計
        /// </summary>
        public UnitSalesStatistics UnitStatistics { get; set; } = null!;

        /// <summary>
        /// 車位統計
        /// </summary>
        public ParkingSpaceSalesStatistics ParkingSpaceStatistics { get; set; } = null!;

        /// <summary>
        /// 總可售數量
        /// </summary>
        public int TotalAvailableCount { get; set; }

        /// <summary>
        /// 總保留數量
        /// </summary>
        public int TotalReservedCount { get; set; }

        /// <summary>
        /// 總已售數量
        /// </summary>
        public int TotalSoldCount { get; set; }

        /// <summary>
        /// 統計產生時間
        /// </summary>
        public DateTime GeneratedTime { get; set; }
    }
} 
