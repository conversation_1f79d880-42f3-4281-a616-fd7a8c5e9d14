#### [<PERSON>feApi.WebApi](index.md 'index')
### [AlifeApi.WebApi.Controllers](AlifeApi.WebApi.Controllers.md 'AlifeApi.WebApi.Controllers')

## BuildingsController Class

建築物管理

```csharp
public class BuildingsController : AlifeApi.WebApi.Controllers.AuthenticatedController
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; [Microsoft.AspNetCore.Mvc.ControllerBase](https://docs.microsoft.com/en-us/dotnet/api/Microsoft.AspNetCore.Mvc.ControllerBase 'Microsoft.AspNetCore.Mvc.ControllerBase') &#129106; [Microsoft.AspNetCore.Mvc.Controller](https://docs.microsoft.com/en-us/dotnet/api/Microsoft.AspNetCore.Mvc.Controller 'Microsoft.AspNetCore.Mvc.Controller') &#129106; [AlifeApi.WebApi.Controllers.BasicController](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.WebApi.Controllers.BasicController 'AlifeApi.WebApi.Controllers.BasicController') &#129106; [AlifeApi.WebApi.Controllers.AuthenticatedController](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.WebApi.Controllers.AuthenticatedController 'AlifeApi.WebApi.Controllers.AuthenticatedController') &#129106; BuildingsController
### Methods

<a name='AlifeApi.WebApi.Controllers.BuildingsController.CreateBuilding(AlifeApi.BusinessRules.BuildingModels.BuildingCreateInput)'></a>

## BuildingsController.CreateBuilding(BuildingCreateInput) Method

新增建築物

```csharp
public System.Threading.Tasks.Task<Microsoft.AspNetCore.Mvc.ActionResult<int>> CreateBuilding(AlifeApi.BusinessRules.BuildingModels.BuildingCreateInput input);
```
#### Parameters

<a name='AlifeApi.WebApi.Controllers.BuildingsController.CreateBuilding(AlifeApi.BusinessRules.BuildingModels.BuildingCreateInput).input'></a>

`input` [AlifeApi.BusinessRules.BuildingModels.BuildingCreateInput](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.BusinessRules.BuildingModels.BuildingCreateInput 'AlifeApi.BusinessRules.BuildingModels.BuildingCreateInput')

建築物建立輸入資料

#### Returns
[System.Threading.Tasks.Task&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')[Microsoft.AspNetCore.Mvc.ActionResult&lt;](https://docs.microsoft.com/en-us/dotnet/api/Microsoft.AspNetCore.Mvc.ActionResult-1 'Microsoft.AspNetCore.Mvc.ActionResult`1')[System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/Microsoft.AspNetCore.Mvc.ActionResult-1 'Microsoft.AspNetCore.Mvc.ActionResult`1')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')  
新增的建築物ID

<a name='AlifeApi.WebApi.Controllers.BuildingsController.DeleteBuilding(int)'></a>

## BuildingsController.DeleteBuilding(int) Method

刪除建築物

```csharp
public System.Threading.Tasks.Task<Microsoft.AspNetCore.Mvc.ActionResult> DeleteBuilding(int buildingId);
```
#### Parameters

<a name='AlifeApi.WebApi.Controllers.BuildingsController.DeleteBuilding(int).buildingId'></a>

`buildingId` [System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')

建築物ID

#### Returns
[System.Threading.Tasks.Task&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')[Microsoft.AspNetCore.Mvc.ActionResult](https://docs.microsoft.com/en-us/dotnet/api/Microsoft.AspNetCore.Mvc.ActionResult 'Microsoft.AspNetCore.Mvc.ActionResult')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')  
NoContent

<a name='AlifeApi.WebApi.Controllers.BuildingsController.GetBuilding(int)'></a>

## BuildingsController.GetBuilding(int) Method

根據建築物ID取得詳細資訊

```csharp
public System.Threading.Tasks.Task<Microsoft.AspNetCore.Mvc.ActionResult<AlifeApi.BusinessRules.BuildingModels.BuildingOutput>> GetBuilding(int buildingId);
```
#### Parameters

<a name='AlifeApi.WebApi.Controllers.BuildingsController.GetBuilding(int).buildingId'></a>

`buildingId` [System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')

建築物ID

#### Returns
[System.Threading.Tasks.Task&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')[Microsoft.AspNetCore.Mvc.ActionResult&lt;](https://docs.microsoft.com/en-us/dotnet/api/Microsoft.AspNetCore.Mvc.ActionResult-1 'Microsoft.AspNetCore.Mvc.ActionResult`1')[AlifeApi.BusinessRules.BuildingModels.BuildingOutput](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.BusinessRules.BuildingModels.BuildingOutput 'AlifeApi.BusinessRules.BuildingModels.BuildingOutput')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/Microsoft.AspNetCore.Mvc.ActionResult-1 'Microsoft.AspNetCore.Mvc.ActionResult`1')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')  
建築物詳細資訊

<a name='AlifeApi.WebApi.Controllers.BuildingsController.GetBuildings(AlifeApi.BusinessRules.BuildingModels.BuildingQueryInput)'></a>

## BuildingsController.GetBuildings(BuildingQueryInput) Method

取得建築物列表 (分頁)

```csharp
public System.Threading.Tasks.Task<Microsoft.AspNetCore.Mvc.ActionResult<AlifeApi.BusinessRules.Infrastructure.PagedListOutput<AlifeApi.BusinessRules.BuildingModels.BuildingListOutput>>> GetBuildings(AlifeApi.BusinessRules.BuildingModels.BuildingQueryInput input);
```
#### Parameters

<a name='AlifeApi.WebApi.Controllers.BuildingsController.GetBuildings(AlifeApi.BusinessRules.BuildingModels.BuildingQueryInput).input'></a>

`input` [AlifeApi.BusinessRules.BuildingModels.BuildingQueryInput](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.BusinessRules.BuildingModels.BuildingQueryInput 'AlifeApi.BusinessRules.BuildingModels.BuildingQueryInput')

查詢條件

#### Returns
[System.Threading.Tasks.Task&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')[Microsoft.AspNetCore.Mvc.ActionResult&lt;](https://docs.microsoft.com/en-us/dotnet/api/Microsoft.AspNetCore.Mvc.ActionResult-1 'Microsoft.AspNetCore.Mvc.ActionResult`1')[AlifeApi.BusinessRules.Infrastructure.PagedListOutput&lt;](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.BusinessRules.Infrastructure.PagedListOutput-1 'AlifeApi.BusinessRules.Infrastructure.PagedListOutput`1')[AlifeApi.BusinessRules.BuildingModels.BuildingListOutput](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.BusinessRules.BuildingModels.BuildingListOutput 'AlifeApi.BusinessRules.BuildingModels.BuildingListOutput')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.BusinessRules.Infrastructure.PagedListOutput-1 'AlifeApi.BusinessRules.Infrastructure.PagedListOutput`1')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/Microsoft.AspNetCore.Mvc.ActionResult-1 'Microsoft.AspNetCore.Mvc.ActionResult`1')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')  
分頁的建築物列表

<a name='AlifeApi.WebApi.Controllers.BuildingsController.GetCommonDropdownList(AlifeApi.BusinessRules.CommonModels.CommonDropdownInput)'></a>

## BuildingsController.GetCommonDropdownList(CommonDropdownInput) Method

取得通用下拉選單 (建築物或樓層)

```csharp
public System.Threading.Tasks.Task<Microsoft.AspNetCore.Mvc.IActionResult> GetCommonDropdownList(AlifeApi.BusinessRules.CommonModels.CommonDropdownInput input);
```
#### Parameters

<a name='AlifeApi.WebApi.Controllers.BuildingsController.GetCommonDropdownList(AlifeApi.BusinessRules.CommonModels.CommonDropdownInput).input'></a>

`input` [AlifeApi.BusinessRules.CommonModels.CommonDropdownInput](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.BusinessRules.CommonModels.CommonDropdownInput 'AlifeApi.BusinessRules.CommonModels.CommonDropdownInput')

通用查詢條件 (包含 SiteCode? 或 BuildingId?)

#### Returns
[System.Threading.Tasks.Task&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')[Microsoft.AspNetCore.Mvc.IActionResult](https://docs.microsoft.com/en-us/dotnet/api/Microsoft.AspNetCore.Mvc.IActionResult 'Microsoft.AspNetCore.Mvc.IActionResult')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')  
建築物下拉選單列表或樓層下拉選單列表

<a name='AlifeApi.WebApi.Controllers.BuildingsController.ImportFromExcel(Microsoft.AspNetCore.Http.IFormFile)'></a>

## BuildingsController.ImportFromExcel(IFormFile) Method

批次匯入建築物、樓層及車位

```csharp
public System.Threading.Tasks.Task<Microsoft.AspNetCore.Mvc.IActionResult> ImportFromExcel(Microsoft.AspNetCore.Http.IFormFile file);
```
#### Parameters

<a name='AlifeApi.WebApi.Controllers.BuildingsController.ImportFromExcel(Microsoft.AspNetCore.Http.IFormFile).file'></a>

`file` [Microsoft.AspNetCore.Http.IFormFile](https://docs.microsoft.com/en-us/dotnet/api/Microsoft.AspNetCore.Http.IFormFile 'Microsoft.AspNetCore.Http.IFormFile')

包含三個工作表 (建築物, 樓層, 車位) 的 Excel 檔案

#### Returns
[System.Threading.Tasks.Task&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')[Microsoft.AspNetCore.Mvc.IActionResult](https://docs.microsoft.com/en-us/dotnet/api/Microsoft.AspNetCore.Mvc.IActionResult 'Microsoft.AspNetCore.Mvc.IActionResult')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')

<a name='AlifeApi.WebApi.Controllers.BuildingsController.UpdateBuilding(int,AlifeApi.BusinessRules.BuildingModels.BuildingUpdateInput)'></a>

## BuildingsController.UpdateBuilding(int, BuildingUpdateInput) Method

更新建築物

```csharp
public System.Threading.Tasks.Task<Microsoft.AspNetCore.Mvc.ActionResult> UpdateBuilding(int buildingId, AlifeApi.BusinessRules.BuildingModels.BuildingUpdateInput input);
```
#### Parameters

<a name='AlifeApi.WebApi.Controllers.BuildingsController.UpdateBuilding(int,AlifeApi.BusinessRules.BuildingModels.BuildingUpdateInput).buildingId'></a>

`buildingId` [System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')

建築物ID

<a name='AlifeApi.WebApi.Controllers.BuildingsController.UpdateBuilding(int,AlifeApi.BusinessRules.BuildingModels.BuildingUpdateInput).input'></a>

`input` [AlifeApi.BusinessRules.BuildingModels.BuildingUpdateInput](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.BusinessRules.BuildingModels.BuildingUpdateInput 'AlifeApi.BusinessRules.BuildingModels.BuildingUpdateInput')

建築物更新輸入資料

#### Returns
[System.Threading.Tasks.Task&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')[Microsoft.AspNetCore.Mvc.ActionResult](https://docs.microsoft.com/en-us/dotnet/api/Microsoft.AspNetCore.Mvc.ActionResult 'Microsoft.AspNetCore.Mvc.ActionResult')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')  
NoContent