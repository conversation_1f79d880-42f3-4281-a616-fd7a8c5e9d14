﻿using AlifeApi.BusinessRules.JobTitleModels;
using Microsoft.AspNetCore.Mvc;

namespace AlifeApi.WebApi.Controllers
{
    /// <summary>
    /// 職位管理控制器
    /// </summary>
    public class JobTitleController : AuthenticatedController
    {
        private readonly JobTitleService _jobTitleService;

        /// <summary>
        /// 建構函數
        /// </summary>
        public JobTitleController(JobTitleService jobTitleService)
        {
            _jobTitleService = jobTitleService;
        }

        /// <summary>
        /// 取得職位下拉選單
        /// </summary>
        /// <param name="input">查詢條件</param>
        /// <returns>職位下拉選單列表</returns>
        [HttpPost]
        public async Task<ActionResult<List<JobTitleDropdownOutput>>> GetJobTitleDropdownListAsync(JobTitleDropdownInput input)
        {
            try
            {
                return await _jobTitleService.GetJobTitleDropdownListAsync(input);
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }
    }
} 
