﻿using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;

namespace AlifeApi.BusinessRules.RoleGroupModels
{
    /// <summary>
    /// 角色權限
    /// </summary>
    public class RoleGroupCreateInput
    {
        /// <summary>
        /// 角色群組名稱
        /// </summary>
        [JsonPropertyName("CName")]
        [MaxLength(50)]
        public string Name { get; set; }

        /// <summary>
        /// 權限
        /// </summary>
        public string Permission { get; set; }

        /// <remarks>
        /// 前端會傳字串，只好這樣處理
        /// </remarks>
        public IEnumerable<string> FuncIds => Permission?.Split(',').Distinct() ?? Enumerable.Empty<string>();
    }
}
