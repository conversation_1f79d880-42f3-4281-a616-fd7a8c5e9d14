using System;

namespace AlifeApi.BusinessRules.PaymentRecordModels
{
    /// <summary>
    /// 收款記錄詳細輸出模型
    /// </summary>
    public class PaymentRecordOutput
    {
        /// <summary>
        /// 收款紀錄唯一識別碼
        /// </summary>
        public int PaymentRecordId { get; set; }

        /// <summary>
        /// 訂單ID
        /// </summary>
        public int OrderId { get; set; }

        /// <summary>
        /// 收款日期
        /// </summary>
        public DateOnly PaymentDate { get; set; }

        /// <summary>
        /// 收款金額
        /// </summary>
        public decimal Amount { get; set; }

        /// <summary>
        /// 款別
        /// </summary>
        public string PaymentType { get; set; } = null!;

        /// <summary>
        /// 收款方式
        /// </summary>
        public string PaymentMethod { get; set; } = null!;

        /// <summary>
        /// 附件路徑
        /// </summary>
        public string? AttachmentPath { get; set; }

        /// <summary>
        /// 備註
        /// </summary>
        public string? Remarks { get; set; }

        /// <summary>
        /// 手續費
        /// </summary>
        public decimal? HandlingFee { get; set; }

        /// <summary>
        /// 是否已轉給建商
        /// </summary>
        public bool? TransferredToDeveloper { get; set; }

        /// <summary>
        /// 建立時間
        /// </summary>
        public DateTime CreatedTime { get; set; }

        /// <summary>
        /// 更新時間
        /// </summary>
        public DateTime UpdatedTime { get; set; }

        /// <summary>
        /// 建立者 UserInfoId
        /// </summary>
        public string CreatedUserInfoId { get; set; } = null!;

        /// <summary>
        /// 更新者 UserInfoId
        /// </summary>
        public string UpdatedUserInfoId { get; set; } = null!;

        /// <summary>
        /// 建立者名稱 (選填)
        /// </summary>
        public string? CreatedUserName { get; set; }

        /// <summary>
        /// 更新者名稱 (選填)
        /// </summary>
        public string? UpdatedUserName { get; set; }

        /// <summary>
        /// 訂單號碼 (選填)
        /// </summary>
        public string? OrderNumber { get; set; }
    }
} 
