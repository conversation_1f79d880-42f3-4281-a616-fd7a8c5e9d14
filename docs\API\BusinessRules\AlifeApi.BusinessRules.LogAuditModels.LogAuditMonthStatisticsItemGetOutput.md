#### [AlifeApi.BusinessRules](index.md 'index')
### [AlifeApi.BusinessRules.LogAuditModels](AlifeApi.BusinessRules.LogAuditModels.md 'AlifeApi.BusinessRules.LogAuditModels')

## LogAuditMonthStatisticsItemGetOutput Class

日誌稽核列表

```csharp
public class LogAuditMonthStatisticsItemGetOutput
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; LogAuditMonthStatisticsItemGetOutput
### Properties

<a name='AlifeApi.BusinessRules.LogAuditModels.LogAuditMonthStatisticsItemGetOutput.DeptId'></a>

## LogAuditMonthStatisticsItemGetOutput.DeptId Property

部門代碼

```csharp
public string DeptId { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.LogAuditModels.LogAuditMonthStatisticsItemGetOutput.DeptName'></a>

## LogAuditMonthStatisticsItemGetOutput.DeptName Property

部門名稱

```csharp
public string DeptName { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.LogAuditModels.LogAuditMonthStatisticsItemGetOutput.Month'></a>

## LogAuditMonthStatisticsItemGetOutput.Month Property

月份

```csharp
public string Month { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.LogAuditModels.LogAuditMonthStatisticsItemGetOutput.RecordCount'></a>

## LogAuditMonthStatisticsItemGetOutput.RecordCount Property

紀錄數量

```csharp
public int RecordCount { get; set; }
```

#### Property Value
[System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')

<a name='AlifeApi.BusinessRules.LogAuditModels.LogAuditMonthStatisticsItemGetOutput.UserCount'></a>

## LogAuditMonthStatisticsItemGetOutput.UserCount Property

使用者數量

```csharp
public int UserCount { get; set; }
```

#### Property Value
[System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')