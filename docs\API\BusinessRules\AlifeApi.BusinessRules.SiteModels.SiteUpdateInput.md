#### [AlifeApi.BusinessRules](index.md 'index')
### [AlifeApi.BusinessRules.SiteModels](AlifeApi.BusinessRules.SiteModels.md 'AlifeApi.BusinessRules.SiteModels')

## SiteUpdateInput Class

案場更新輸入

```csharp
public class SiteUpdateInput
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; SiteUpdateInput
### Properties

<a name='AlifeApi.BusinessRules.SiteModels.SiteUpdateInput.AboveGroundFloors'></a>

## SiteUpdateInput.AboveGroundFloors Property

地上層數

```csharp
public string AboveGroundFloors { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.SiteModels.SiteUpdateInput.AdvertisingBudget'></a>

## SiteUpdateInput.AdvertisingBudget Property

應編廣告預算

```csharp
public System.Nullable<decimal> AdvertisingBudget { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.Decimal](https://docs.microsoft.com/en-us/dotnet/api/System.Decimal 'System.Decimal')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

<a name='AlifeApi.BusinessRules.SiteModels.SiteUpdateInput.AdvertisingBudgetRate'></a>

## SiteUpdateInput.AdvertisingBudgetRate Property

廣告預算率

```csharp
public System.Nullable<decimal> AdvertisingBudgetRate { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.Decimal](https://docs.microsoft.com/en-us/dotnet/api/System.Decimal 'System.Decimal')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

<a name='AlifeApi.BusinessRules.SiteModels.SiteUpdateInput.BelowGroundFloors'></a>

## SiteUpdateInput.BelowGroundFloors Property

地下層數

```csharp
public string BelowGroundFloors { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.SiteModels.SiteUpdateInput.Broker'></a>

## SiteUpdateInput.Broker Property

經紀人

```csharp
public string Broker { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.SiteModels.SiteUpdateInput.BusinessIds'></a>

## SiteUpdateInput.BusinessIds Property

業務

```csharp
public string BusinessIds { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.SiteModels.SiteUpdateInput.Chairman'></a>

## SiteUpdateInput.Chairman Property

主委

```csharp
public string Chairman { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.SiteModels.SiteUpdateInput.City'></a>

## SiteUpdateInput.City Property

所在縣市

```csharp
public string City { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.SiteModels.SiteUpdateInput.CompanyId'></a>

## SiteUpdateInput.CompanyId Property

執行公司別

```csharp
public string CompanyId { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.SiteModels.SiteUpdateInput.ContractedAmount'></a>

## SiteUpdateInput.ContractedAmount Property

已發包金額

```csharp
public System.Nullable<decimal> ContractedAmount { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.Decimal](https://docs.microsoft.com/en-us/dotnet/api/System.Decimal 'System.Decimal')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

<a name='AlifeApi.BusinessRules.SiteModels.SiteUpdateInput.ContractPeriod'></a>

## SiteUpdateInput.ContractPeriod Property

合約期間

```csharp
public System.Nullable<System.DateOnly> ContractPeriod { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.DateOnly](https://docs.microsoft.com/en-us/dotnet/api/System.DateOnly 'System.DateOnly')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

<a name='AlifeApi.BusinessRules.SiteModels.SiteUpdateInput.ControlReserveRate'></a>

## SiteUpdateInput.ControlReserveRate Property

控存率

```csharp
public System.Nullable<decimal> ControlReserveRate { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.Decimal](https://docs.microsoft.com/en-us/dotnet/api/System.Decimal 'System.Decimal')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

<a name='AlifeApi.BusinessRules.SiteModels.SiteUpdateInput.DeputyProjectManager'></a>

## SiteUpdateInput.DeputyProjectManager Property

副專

```csharp
public string DeputyProjectManager { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.SiteModels.SiteUpdateInput.Developer'></a>

## SiteUpdateInput.Developer Property

投資興建

```csharp
public string Developer { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.SiteModels.SiteUpdateInput.District'></a>

## SiteUpdateInput.District Property

所在區域

```csharp
public string District { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.SiteModels.SiteUpdateInput.ExcessPriceAllocation'></a>

## SiteUpdateInput.ExcessPriceAllocation Property

超價款分配

```csharp
public string ExcessPriceAllocation { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.SiteModels.SiteUpdateInput.ExtensionPeriod'></a>

## SiteUpdateInput.ExtensionPeriod Property

展延期間

```csharp
public System.Nullable<System.DateOnly> ExtensionPeriod { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.DateOnly](https://docs.microsoft.com/en-us/dotnet/api/System.DateOnly 'System.DateOnly')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

<a name='AlifeApi.BusinessRules.SiteModels.SiteUpdateInput.LandArea'></a>

## SiteUpdateInput.LandArea Property

基地面積

```csharp
public System.Nullable<decimal> LandArea { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.Decimal](https://docs.microsoft.com/en-us/dotnet/api/System.Decimal 'System.Decimal')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

<a name='AlifeApi.BusinessRules.SiteModels.SiteUpdateInput.PaidAmount'></a>

## SiteUpdateInput.PaidAmount Property

已請款金額

```csharp
public System.Nullable<decimal> PaidAmount { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.Decimal](https://docs.microsoft.com/en-us/dotnet/api/System.Decimal 'System.Decimal')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

<a name='AlifeApi.BusinessRules.SiteModels.SiteUpdateInput.ParkingType'></a>

## SiteUpdateInput.ParkingType Property

車位類別

```csharp
public string ParkingType { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.SiteModels.SiteUpdateInput.PlannedFloors'></a>

## SiteUpdateInput.PlannedFloors Property

規劃樓層

```csharp
public string PlannedFloors { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.SiteModels.SiteUpdateInput.PlannedParkingSpaces'></a>

## SiteUpdateInput.PlannedParkingSpaces Property

規劃戶數(車位)

```csharp
public System.Nullable<int> PlannedParkingSpaces { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

<a name='AlifeApi.BusinessRules.SiteModels.SiteUpdateInput.PlannedResidentialUnits'></a>

## SiteUpdateInput.PlannedResidentialUnits Property

規劃戶數(住家)

```csharp
public System.Nullable<int> PlannedResidentialUnits { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

<a name='AlifeApi.BusinessRules.SiteModels.SiteUpdateInput.PlannedStoreUnits'></a>

## SiteUpdateInput.PlannedStoreUnits Property

規劃戶數(店面)

```csharp
public System.Nullable<int> PlannedStoreUnits { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

<a name='AlifeApi.BusinessRules.SiteModels.SiteUpdateInput.PlannedUnitsAndParking'></a>

## SiteUpdateInput.PlannedUnitsAndParking Property

規劃戶車

```csharp
public string PlannedUnitsAndParking { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.SiteModels.SiteUpdateInput.ProjectManager'></a>

## SiteUpdateInput.ProjectManager Property

專案

```csharp
public string ProjectManager { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.SiteModels.SiteUpdateInput.PromotionType'></a>

## SiteUpdateInput.PromotionType Property

推案型態

```csharp
public string PromotionType { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.SiteModels.SiteUpdateInput.PublicFacilityRatio'></a>

## SiteUpdateInput.PublicFacilityRatio Property

公設比

```csharp
public System.Nullable<decimal> PublicFacilityRatio { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.Decimal](https://docs.microsoft.com/en-us/dotnet/api/System.Decimal 'System.Decimal')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

<a name='AlifeApi.BusinessRules.SiteModels.SiteUpdateInput.ReceptionCenter'></a>

## SiteUpdateInput.ReceptionCenter Property

接待中心

```csharp
public string ReceptionCenter { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.SiteModels.SiteUpdateInput.ReserveAmount'></a>

## SiteUpdateInput.ReserveAmount Property

保留款

```csharp
public System.Nullable<decimal> ReserveAmount { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.Decimal](https://docs.microsoft.com/en-us/dotnet/api/System.Decimal 'System.Decimal')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

<a name='AlifeApi.BusinessRules.SiteModels.SiteUpdateInput.RunnerIds'></a>

## SiteUpdateInput.RunnerIds Property

跑單

```csharp
public string RunnerIds { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.SiteModels.SiteUpdateInput.SellableTotalPrice'></a>

## SiteUpdateInput.SellableTotalPrice Property

可售總銷

```csharp
public System.Nullable<decimal> SellableTotalPrice { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.Decimal](https://docs.microsoft.com/en-us/dotnet/api/System.Decimal 'System.Decimal')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

<a name='AlifeApi.BusinessRules.SiteModels.SiteUpdateInput.ServiceFeeCalculation'></a>

## SiteUpdateInput.ServiceFeeCalculation Property

服務費計算

```csharp
public string ServiceFeeCalculation { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.SiteModels.SiteUpdateInput.ServiceFeeRate'></a>

## SiteUpdateInput.ServiceFeeRate Property

服務費率

```csharp
public System.Nullable<decimal> ServiceFeeRate { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.Decimal](https://docs.microsoft.com/en-us/dotnet/api/System.Decimal 'System.Decimal')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

<a name='AlifeApi.BusinessRules.SiteModels.SiteUpdateInput.SiteCode'></a>

## SiteUpdateInput.SiteCode Property

案場代碼

```csharp
public string SiteCode { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.SiteModels.SiteUpdateInput.SiteLocation'></a>

## SiteUpdateInput.SiteLocation Property

基地位置

```csharp
public string SiteLocation { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.SiteModels.SiteUpdateInput.SiteName'></a>

## SiteUpdateInput.SiteName Property

案場名稱

```csharp
public string SiteName { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.SiteModels.SiteUpdateInput.SitePhone'></a>

## SiteUpdateInput.SitePhone Property

案場電話

```csharp
public string SitePhone { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.SiteModels.SiteUpdateInput.Structure'></a>

## SiteUpdateInput.Structure Property

結構

```csharp
public string Structure { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.SiteModels.SiteUpdateInput.TotalPlannedFloors'></a>

## SiteUpdateInput.TotalPlannedFloors Property

規劃總層

```csharp
public System.Nullable<int> TotalPlannedFloors { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

<a name='AlifeApi.BusinessRules.SiteModels.SiteUpdateInput.TotalSalePrice'></a>

## SiteUpdateInput.TotalSalePrice Property

全案總銷

```csharp
public System.Nullable<decimal> TotalSalePrice { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.Decimal](https://docs.microsoft.com/en-us/dotnet/api/System.Decimal 'System.Decimal')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

<a name='AlifeApi.BusinessRules.SiteModels.SiteUpdateInput.UnitSize'></a>

## SiteUpdateInput.UnitSize Property

坪數

```csharp
public string UnitSize { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.SiteModels.SiteUpdateInput.ViceChairman'></a>

## SiteUpdateInput.ViceChairman Property

副主委

```csharp
public string ViceChairman { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.SiteModels.SiteUpdateInput.Zoning'></a>

## SiteUpdateInput.Zoning Property

使用分區

```csharp
public string Zoning { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')