#### [AlifeApi.BusinessRules](index.md 'index')
### [AlifeApi.BusinessRules.LoginModels](AlifeApi.BusinessRules.LoginModels.md 'AlifeApi.BusinessRules.LoginModels')

## UserLoginOutput Class

使用者登入

```csharp
public class UserLoginOutput :
AlifeApi.BusinessRules.Infrastructure.IApiMessage
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; UserLoginOutput

Implements [IApiMessage](AlifeApi.BusinessRules.Infrastructure.IApiMessage.md 'AlifeApi.BusinessRules.Infrastructure.IApiMessage')
### Fields

<a name='AlifeApi.BusinessRules.LoginModels.UserLoginOutput.DeptFuncIds'></a>

## UserLoginOutput.DeptFuncIds Field

```csharp
public IEnumerable<string> DeptFuncIds;
```

#### Field Value
[System.Collections.Generic.IEnumerable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Collections.Generic.IEnumerable-1 'System.Collections.Generic.IEnumerable`1')[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Collections.Generic.IEnumerable-1 'System.Collections.Generic.IEnumerable`1')

### Remarks
部門權限

<a name='AlifeApi.BusinessRules.LoginModels.UserLoginOutput.GradeFuncIds'></a>

## UserLoginOutput.GradeFuncIds Field

```csharp
public IEnumerable<string> GradeFuncIds;
```

#### Field Value
[System.Collections.Generic.IEnumerable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Collections.Generic.IEnumerable-1 'System.Collections.Generic.IEnumerable`1')[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Collections.Generic.IEnumerable-1 'System.Collections.Generic.IEnumerable`1')

### Remarks
職稱權限

<a name='AlifeApi.BusinessRules.LoginModels.UserLoginOutput.RoleFuncIds'></a>

## UserLoginOutput.RoleFuncIds Field

```csharp
public IEnumerable<string> RoleFuncIds;
```

#### Field Value
[System.Collections.Generic.IEnumerable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Collections.Generic.IEnumerable-1 'System.Collections.Generic.IEnumerable`1')[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Collections.Generic.IEnumerable-1 'System.Collections.Generic.IEnumerable`1')

### Remarks
角色權限
### Properties

<a name='AlifeApi.BusinessRules.LoginModels.UserLoginOutput.DeptId'></a>

## UserLoginOutput.DeptId Property

單位

```csharp
public string DeptId { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.LoginModels.UserLoginOutput.DeptName'></a>

## UserLoginOutput.DeptName Property

單位

```csharp
public string DeptName { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.LoginModels.UserLoginOutput.Email'></a>

## UserLoginOutput.Email Property

信箱

```csharp
public string Email { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.LoginModels.UserLoginOutput.FuncIds'></a>

## UserLoginOutput.FuncIds Property

權限

```csharp
public System.Collections.Generic.IEnumerable<string> FuncIds { get; }
```

#### Property Value
[System.Collections.Generic.IEnumerable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Collections.Generic.IEnumerable-1 'System.Collections.Generic.IEnumerable`1')[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Collections.Generic.IEnumerable-1 'System.Collections.Generic.IEnumerable`1')

<a name='AlifeApi.BusinessRules.LoginModels.UserLoginOutput.GradeCode'></a>

## UserLoginOutput.GradeCode Property

職稱

```csharp
public string GradeCode { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.LoginModels.UserLoginOutput.GradeName'></a>

## UserLoginOutput.GradeName Property

職稱

```csharp
public string GradeName { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.LoginModels.UserLoginOutput.IdNo'></a>

## UserLoginOutput.IdNo Property

身份證字號

```csharp
public string IdNo { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

### Remarks
別的地方叫 USERIDNO，這邊只叫 IDNO？

<a name='AlifeApi.BusinessRules.LoginModels.UserLoginOutput.IP'></a>

## UserLoginOutput.IP Property

IP

```csharp
public string IP { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.LoginModels.UserLoginOutput.IsAdmin'></a>

## UserLoginOutput.IsAdmin Property

是否為管理者

```csharp
public bool IsAdmin { get; set; }
```

#### Property Value
[System.Boolean](https://docs.microsoft.com/en-us/dotnet/api/System.Boolean 'System.Boolean')

<a name='AlifeApi.BusinessRules.LoginModels.UserLoginOutput.JwtToken'></a>

## UserLoginOutput.JwtToken Property

JWT Token

```csharp
public string JwtToken { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

### Remarks
這邊改名不是我嫌棄回傳前端大小寫有問題，只是單純 C# 遇到這種 3 碼以上的略縮字大小寫規則和別的語言不同

<a name='AlifeApi.BusinessRules.LoginModels.UserLoginOutput.JwtTokenExpireTime'></a>

## UserLoginOutput.JwtTokenExpireTime Property

JWT Token 到期日期時間

```csharp
public System.Nullable<System.DateTime> JwtTokenExpireTime { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.DateTime](https://docs.microsoft.com/en-us/dotnet/api/System.DateTime 'System.DateTime')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

### Remarks
這邊改名不是我嫌棄回傳前端大小寫有問題，只是單純 C# 遇到這種 3 碼以上的略縮字大小寫規則和別的語言不同

<a name='AlifeApi.BusinessRules.LoginModels.UserLoginOutput.MenuTrees'></a>

## UserLoginOutput.MenuTrees Property

權限樹

```csharp
public System.Collections.Generic.IEnumerable<AlifeApi.BusinessRules.RoleGroupModels.MenuTreeOutput> MenuTrees { get; set; }
```

#### Property Value
[System.Collections.Generic.IEnumerable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Collections.Generic.IEnumerable-1 'System.Collections.Generic.IEnumerable`1')[MenuTreeOutput](AlifeApi.BusinessRules.RoleGroupModels.MenuTreeOutput.md 'AlifeApi.BusinessRules.RoleGroupModels.MenuTreeOutput')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Collections.Generic.IEnumerable-1 'System.Collections.Generic.IEnumerable`1')

<a name='AlifeApi.BusinessRules.LoginModels.UserLoginOutput.Response'></a>

## UserLoginOutput.Response Property

Gets the response.

```csharp
public System.Enum Response { get; set; }
```

Implements [Response](AlifeApi.BusinessRules.Infrastructure.IApiMessage.md#AlifeApi.BusinessRules.Infrastructure.IApiMessage.Response 'AlifeApi.BusinessRules.Infrastructure.IApiMessage.Response')

#### Property Value
[System.Enum](https://docs.microsoft.com/en-us/dotnet/api/System.Enum 'System.Enum')

<a name='AlifeApi.BusinessRules.LoginModels.UserLoginOutput.Roles'></a>

## UserLoginOutput.Roles Property

角色群組

```csharp
public System.Collections.Generic.IEnumerable<AlifeApi.BusinessRules.RoleGroupModels.RoleOutput> Roles { get; set; }
```

#### Property Value
[System.Collections.Generic.IEnumerable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Collections.Generic.IEnumerable-1 'System.Collections.Generic.IEnumerable`1')[RoleOutput](AlifeApi.BusinessRules.RoleGroupModels.RoleOutput.md 'AlifeApi.BusinessRules.RoleGroupModels.RoleOutput')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Collections.Generic.IEnumerable-1 'System.Collections.Generic.IEnumerable`1')

<a name='AlifeApi.BusinessRules.LoginModels.UserLoginOutput.UserInfoId'></a>

## UserLoginOutput.UserInfoId Property

使用者帳號

```csharp
public string UserInfoId { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.LoginModels.UserLoginOutput.UserName'></a>

## UserLoginOutput.UserName Property

使用者姓名

```csharp
public string UserName { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.LoginModels.UserLoginOutput.UserPassword'></a>

## UserLoginOutput.UserPassword Property

使用者密碼

```csharp
public string UserPassword { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')