using System;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using AlifeApi.BusinessRules.FloorModels;
using AlifeApi.BusinessRules.Infrastructure;

namespace AlifeApi.WebApi.Controllers
{
    /// <summary>
    /// 樓層管理
    /// </summary>
    public class FloorsController : AuthenticatedController
    {
        private readonly FloorService _floorService;

        public FloorsController(FloorService floorService)
        {
            _floorService = floorService ?? throw new ArgumentNullException(nameof(floorService));
        }

        /// <summary>
        /// 取得樓層列表 (分頁)
        /// </summary>
        /// <param name="input">查詢條件</param>
        /// <returns>分頁的樓層列表</returns>
        [HttpPost]
        public async Task<ActionResult<PagedListOutput<FloorListOutput>>> GetFloors([FromBody] FloorQueryInput input)
        {
            var result = await _floorService.GetFloorListAsync(input);
            return Ok(result);
        }

        /// <summary>
        /// 根據樓層ID取得詳細資訊
        /// </summary>
        /// <param name="floorId">樓層ID</param>
        /// <returns>樓層詳細資訊</returns>
        [HttpGet("{floorId}")]
        public async Task<ActionResult<FloorOutput>> GetFloor(int floorId)
        {
            var result = await _floorService.GetFloorByIdAsync(floorId);
            if (result == null)
                return NotFound();
            return Ok(result);
        }

        /// <summary>
        /// 新增樓層
        /// </summary>
        /// <param name="input">樓層建立輸入資料</param>
        /// <returns>新增的樓層ID</returns>
        [HttpPost]
        public async Task<ActionResult<int>> CreateFloor([FromBody] FloorCreateInput input)
        {
            try
            {
                var id = await _floorService.CreateFloorAsync(input);
                return CreatedAtAction(nameof(GetFloor), new { floorId = id }, new { FloorId = id });
            }
            catch (Exception ex)
            {
                return BadRequest(new { message = ex.Message });
            }
        }

        /// <summary>
        /// 更新樓層
        /// </summary>
        /// <param name="floorId">樓層ID</param>
        /// <param name="input">樓層更新輸入資料</param>
        /// <returns>NoContent</returns>
        [HttpPut("{floorId}")]
        public async Task<ActionResult> UpdateFloor(int floorId, [FromBody] FloorUpdateInput input)
        {
            try
            {
                await _floorService.UpdateFloorAsync(floorId, input);
                return NoContent();
            }
            catch (Exception ex)
            {
                return BadRequest(new { message = ex.Message });
            }
        }

        /// <summary>
        /// 刪除樓層
        /// </summary>
        /// <param name="floorId">樓層ID</param>
        /// <returns>NoContent</returns>
        [HttpDelete("{floorId}")]
        public async Task<ActionResult> DeleteFloor(int floorId)
        {
            try
            {
                await _floorService.DeleteFloorAsync(floorId);
                return NoContent();
            }
            catch (Exception ex)
            {
                return BadRequest(new { message = ex.Message });
            }
        }
    }
} 
