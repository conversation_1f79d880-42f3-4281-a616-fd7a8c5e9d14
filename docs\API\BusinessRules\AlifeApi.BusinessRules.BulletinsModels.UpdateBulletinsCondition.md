#### [AlifeApi.BusinessRules](index.md 'index')
### [AlifeApi.BusinessRules.BulletinsModels](AlifeApi.BusinessRules.BulletinsModels.md 'AlifeApi.BusinessRules.BulletinsModels')

## UpdateBulletinsCondition Class

```csharp
public class UpdateBulletinsCondition : AlifeApi.BusinessRules.BulletinsModels.BulletinsCondition
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; [BulletinsCondition](AlifeApi.BusinessRules.BulletinsModels.BulletinsCondition.md 'AlifeApi.BusinessRules.BulletinsModels.BulletinsCondition') &#129106; UpdateBulletinsCondition
### Properties

<a name='AlifeApi.BusinessRules.BulletinsModels.UpdateBulletinsCondition.AttachList'></a>

## UpdateBulletinsCondition.AttachList Property

公告附件清單

```csharp
public System.Collections.Generic.List<AlifeApi.BusinessRules.BulletinsModels.AttachListItem> AttachList { get; set; }
```

#### Property Value
[System.Collections.Generic.List&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Collections.Generic.List-1 'System.Collections.Generic.List`1')[AttachListItem](AlifeApi.BusinessRules.BulletinsModels.AttachListItem.md 'AlifeApi.BusinessRules.BulletinsModels.AttachListItem')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Collections.Generic.List-1 'System.Collections.Generic.List`1')

<a name='AlifeApi.BusinessRules.BulletinsModels.UpdateBulletinsCondition.Content'></a>

## UpdateBulletinsCondition.Content Property

公告內容

```csharp
public string Content { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.BulletinsModels.UpdateBulletinsCondition.IsTop'></a>

## UpdateBulletinsCondition.IsTop Property

公告是否至頂

```csharp
public System.Nullable<bool> IsTop { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.Boolean](https://docs.microsoft.com/en-us/dotnet/api/System.Boolean 'System.Boolean')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

<a name='AlifeApi.BusinessRules.BulletinsModels.UpdateBulletinsCondition.PostDateFrom'></a>

## UpdateBulletinsCondition.PostDateFrom Property

公告開始日期

```csharp
public System.Nullable<System.DateTime> PostDateFrom { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.DateTime](https://docs.microsoft.com/en-us/dotnet/api/System.DateTime 'System.DateTime')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

<a name='AlifeApi.BusinessRules.BulletinsModels.UpdateBulletinsCondition.PostDateTo'></a>

## UpdateBulletinsCondition.PostDateTo Property

公告結束日期

```csharp
public System.Nullable<System.DateTime> PostDateTo { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.DateTime](https://docs.microsoft.com/en-us/dotnet/api/System.DateTime 'System.DateTime')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

<a name='AlifeApi.BusinessRules.BulletinsModels.UpdateBulletinsCondition.Title'></a>

## UpdateBulletinsCondition.Title Property

公告標題

```csharp
public string Title { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')