#### [AlifeApi.BusinessRules](index.md 'index')
### [AlifeApi.BusinessRules.UnitModels](AlifeApi.BusinessRules.UnitModels.md 'AlifeApi.BusinessRules.UnitModels')

## UnitSalesStatistics Class

房屋銷售統計

```csharp
public class UnitSalesStatistics
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; UnitSalesStatistics
### Properties

<a name='AlifeApi.BusinessRules.UnitModels.UnitSalesStatistics.AvailableCount'></a>

## UnitSalesStatistics.AvailableCount Property

可售數量

```csharp
public int AvailableCount { get; set; }
```

#### Property Value
[System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')

<a name='AlifeApi.BusinessRules.UnitModels.UnitSalesStatistics.BookedCount'></a>

## UnitSalesStatistics.BookedCount Property

已預訂數量

```csharp
public int BookedCount { get; set; }
```

#### Property Value
[System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')

<a name='AlifeApi.BusinessRules.UnitModels.UnitSalesStatistics.ReservedCount'></a>

## UnitSalesStatistics.ReservedCount Property

保留數量

```csharp
public int ReservedCount { get; set; }
```

#### Property Value
[System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')

<a name='AlifeApi.BusinessRules.UnitModels.UnitSalesStatistics.SalesRate'></a>

## UnitSalesStatistics.SalesRate Property

銷售率 (%)

```csharp
public decimal SalesRate { get; set; }
```

#### Property Value
[System.Decimal](https://docs.microsoft.com/en-us/dotnet/api/System.Decimal 'System.Decimal')

<a name='AlifeApi.BusinessRules.UnitModels.UnitSalesStatistics.SiteCode'></a>

## UnitSalesStatistics.SiteCode Property

案場編號

```csharp
public string SiteCode { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.UnitModels.UnitSalesStatistics.SoldCount'></a>

## UnitSalesStatistics.SoldCount Property

已售數量

```csharp
public int SoldCount { get; set; }
```

#### Property Value
[System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')

<a name='AlifeApi.BusinessRules.UnitModels.UnitSalesStatistics.TotalAvailableListPrice'></a>

## UnitSalesStatistics.TotalAvailableListPrice Property

可售總表價

```csharp
public decimal TotalAvailableListPrice { get; set; }
```

#### Property Value
[System.Decimal](https://docs.microsoft.com/en-us/dotnet/api/System.Decimal 'System.Decimal')

<a name='AlifeApi.BusinessRules.UnitModels.UnitSalesStatistics.TotalCount'></a>

## UnitSalesStatistics.TotalCount Property

總數量

```csharp
public int TotalCount { get; set; }
```

#### Property Value
[System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')

<a name='AlifeApi.BusinessRules.UnitModels.UnitSalesStatistics.TotalReservedListPrice'></a>

## UnitSalesStatistics.TotalReservedListPrice Property

保留總表價

```csharp
public decimal TotalReservedListPrice { get; set; }
```

#### Property Value
[System.Decimal](https://docs.microsoft.com/en-us/dotnet/api/System.Decimal 'System.Decimal')