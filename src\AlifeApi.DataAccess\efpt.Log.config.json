﻿{
   "CodeGenerationMode": 2,
   "ContextClassName": "LogContext",
   "ContextNamespace": null,
   "DefaultDacpacSchema": null,
   "FilterSchemas": false,
   "IncludeConnectionString": false,
   "ModelNamespace": null,
   "OutputContextPath": null,
   "OutputPath": "LogContext",
   "PreserveCasingWithRegex": true,
   "ProjectRootNamespace": "AlifeApi.DataAccess",
   "Schemas": null,
   "SelectedHandlebarsLanguage": 0,
   "SelectedToBeGenerated": 0,
   "Tables": [
      {
         "Name": "[dbo].[SYS_ApiLog]",
         "ObjectType": 0
      }
   ],
   "UiHint": "p-moda-por-109.BasicLog_Wing.dbo",
   "UncountableWords": null,
   "UseBoolPropertiesWithoutDefaultSql": false,
   "UseDatabaseNames": false,
   "UseDateOnlyTimeOnly": false,
   "UseDbContextSplitting": false,
   "UseFluentApiOnly": true,
   "UseHandleBars": true,
   "UseHierarchyId": false,
   "UseInflector": false,
   "UseLegacyPluralizer": false,
   "UseManyToManyEntity": false,
   "UseNoDefaultConstructor": false,
   "UseNoObjectFilter": false,
   "UseNodaTime": false,
   "UseNullableReferences": false,
   "UseSchemaFolders": false,
   "UseSpatial": false,
   "UseT4": false
}