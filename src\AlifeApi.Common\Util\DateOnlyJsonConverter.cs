using System.Text.Json;
using System.Text.Json.Serialization;
using System.Globalization;

namespace AlifeApi.Common.Util
{
    /// <summary>
    /// DateOnly 的 JSON 轉換器
    /// </summary>
    public class DateOnlyJsonConverter : JsonConverter<DateOnly>
    {
        private const string DateFormat = "yyyy-MM-dd";
        private static readonly string[] DateFormats = new[]
        {
            "yyyy-MM-dd",
            "yyyy/MM/dd",
            "yyyyMMdd"
        };

        /// <summary>
        /// 讀取 JSON
        /// </summary>
        public override DateOnly Read(ref Utf8JsonReader reader, Type typeToConvert, JsonSerializerOptions options)
        {
            var value = reader.GetString();
            if (string.IsNullOrEmpty(value))
                throw new JsonException("日期不能為空");

            // 嘗試使用多種格式解析日期
            if (DateOnly.TryParseExact(value, DateFormats, CultureInfo.InvariantCulture, DateTimeStyles.None, out var result))
            {
                return result;
            }

            // 如果上述格式都無法解析，嘗試解析為 DateTime 然後轉換為 DateOnly
            if (DateTime.TryParse(value, CultureInfo.InvariantCulture, DateTimeStyles.None, out var dateTime))
            {
                return DateOnly.FromDateTime(dateTime);
            }

            throw new JsonException($"無法將 '{value}' 轉換為日期格式");
        }

        /// <summary>
        /// 寫入 JSON
        /// </summary>
        public override void Write(Utf8JsonWriter writer, DateOnly value, JsonSerializerOptions options)
        {
            writer.WriteStringValue(value.ToString(DateFormat));
        }
    }

    /// <summary>
    /// Nullable DateOnly 的 JSON 轉換器
    /// </summary>
    public class NullableDateOnlyJsonConverter : JsonConverter<DateOnly?>
    {
        private readonly DateOnlyJsonConverter _converter = new();

        /// <summary>
        /// 讀取 JSON
        /// </summary>
        public override DateOnly? Read(ref Utf8JsonReader reader, Type typeToConvert, JsonSerializerOptions options)
        {
            if (reader.TokenType == JsonTokenType.Null)
            {
                return null;
            }

            try
            {
                return _converter.Read(ref reader, typeof(DateOnly), options);
            }
            catch (JsonException)
            {
                return null;
            }
        }

        /// <summary>
        /// 寫入 JSON
        /// </summary>
        public override void Write(Utf8JsonWriter writer, DateOnly? value, JsonSerializerOptions options)
        {
            if (value == null)
            {
                writer.WriteNullValue();
            }
            else
            {
                _converter.Write(writer, value.Value, options);
            }
        }
    }
} 
