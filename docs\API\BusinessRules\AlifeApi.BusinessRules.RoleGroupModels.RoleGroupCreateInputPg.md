#### [AlifeApi.BusinessRules](index.md 'index')
### [AlifeApi.BusinessRules.RoleGroupModels](AlifeApi.BusinessRules.RoleGroupModels.md 'AlifeApi.BusinessRules.RoleGroupModels')

## RoleGroupCreateInputPg Class

角色權限建立輸入(PostgreSQL 版本)

```csharp
public class RoleGroupCreateInputPg
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; RoleGroupCreateInputPg

Derived  
&#8627; [RoleGroupPermissionUpdateInputPg](AlifeApi.BusinessRules.RoleGroupModels.RoleGroupPermissionUpdateInputPg.md 'AlifeApi.BusinessRules.RoleGroupModels.RoleGroupPermissionUpdateInputPg')
### Properties

<a name='AlifeApi.BusinessRules.RoleGroupModels.RoleGroupCreateInputPg.FuncIds'></a>

## RoleGroupCreateInputPg.FuncIds Property

```csharp
public System.Collections.Generic.IEnumerable<string> FuncIds { get; }
```

#### Property Value
[System.Collections.Generic.IEnumerable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Collections.Generic.IEnumerable-1 'System.Collections.Generic.IEnumerable`1')[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Collections.Generic.IEnumerable-1 'System.Collections.Generic.IEnumerable`1')

### Remarks
前端會傳字串，只好這樣處理

<a name='AlifeApi.BusinessRules.RoleGroupModels.RoleGroupCreateInputPg.IsAdmin'></a>

## RoleGroupCreateInputPg.IsAdmin Property

是否為管理者角色

```csharp
public bool IsAdmin { get; set; }
```

#### Property Value
[System.Boolean](https://docs.microsoft.com/en-us/dotnet/api/System.Boolean 'System.Boolean')

<a name='AlifeApi.BusinessRules.RoleGroupModels.RoleGroupCreateInputPg.Name'></a>

## RoleGroupCreateInputPg.Name Property

角色群組名稱

```csharp
public string Name { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.RoleGroupModels.RoleGroupCreateInputPg.Permissions'></a>

## RoleGroupCreateInputPg.Permissions Property

權限

```csharp
public string Permissions { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.RoleGroupModels.RoleGroupCreateInputPg.SiteCode'></a>

## RoleGroupCreateInputPg.SiteCode Property

案場代碼，對應 Sites 表的 SiteCode

```csharp
public string SiteCode { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.RoleGroupModels.RoleGroupCreateInputPg.UserIds'></a>

## RoleGroupCreateInputPg.UserIds Property

使用者清單

```csharp
public System.Collections.Generic.IEnumerable<string> UserIds { get; set; }
```

#### Property Value
[System.Collections.Generic.IEnumerable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Collections.Generic.IEnumerable-1 'System.Collections.Generic.IEnumerable`1')[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Collections.Generic.IEnumerable-1 'System.Collections.Generic.IEnumerable`1')