#### [AlifeApi.BusinessRules](index.md 'index')
### [AlifeApi.BusinessRules.DailyReportModels](AlifeApi.BusinessRules.DailyReportModels.md 'AlifeApi.BusinessRules.DailyReportModels')

## DailyReportPeriodSummary Class

日報期間總計統計

```csharp
public class DailyReportPeriodSummary
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; DailyReportPeriodSummary
### Properties

<a name='AlifeApi.BusinessRules.DailyReportModels.DailyReportPeriodSummary.AverageCallConversionRate'></a>

## DailyReportPeriodSummary.AverageCallConversionRate Property

平均來電轉換率

```csharp
public decimal AverageCallConversionRate { get; set; }
```

#### Property Value
[System.Decimal](https://docs.microsoft.com/en-us/dotnet/api/System.Decimal 'System.Decimal')

<a name='AlifeApi.BusinessRules.DailyReportModels.DailyReportPeriodSummary.AverageTransactionConversionRate'></a>

## DailyReportPeriodSummary.AverageTransactionConversionRate Property

平均成交轉換率

```csharp
public decimal AverageTransactionConversionRate { get; set; }
```

#### Property Value
[System.Decimal](https://docs.microsoft.com/en-us/dotnet/api/System.Decimal 'System.Decimal')

<a name='AlifeApi.BusinessRules.DailyReportModels.DailyReportPeriodSummary.AverageVisitorConversionRate'></a>

## DailyReportPeriodSummary.AverageVisitorConversionRate Property

平均來客轉換率

```csharp
public decimal AverageVisitorConversionRate { get; set; }
```

#### Property Value
[System.Decimal](https://docs.microsoft.com/en-us/dotnet/api/System.Decimal 'System.Decimal')

<a name='AlifeApi.BusinessRules.DailyReportModels.DailyReportPeriodSummary.EndDate'></a>

## DailyReportPeriodSummary.EndDate Property

結束日期

```csharp
public System.DateTime EndDate { get; set; }
```

#### Property Value
[System.DateTime](https://docs.microsoft.com/en-us/dotnet/api/System.DateTime 'System.DateTime')

<a name='AlifeApi.BusinessRules.DailyReportModels.DailyReportPeriodSummary.StartDate'></a>

## DailyReportPeriodSummary.StartDate Property

開始日期

```csharp
public System.DateTime StartDate { get; set; }
```

#### Property Value
[System.DateTime](https://docs.microsoft.com/en-us/dotnet/api/System.DateTime 'System.DateTime')

<a name='AlifeApi.BusinessRules.DailyReportModels.DailyReportPeriodSummary.TotalCallCount'></a>

## DailyReportPeriodSummary.TotalCallCount Property

總來電數

```csharp
public int TotalCallCount { get; set; }
```

#### Property Value
[System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')

<a name='AlifeApi.BusinessRules.DailyReportModels.DailyReportPeriodSummary.TotalLeadCount'></a>

## DailyReportPeriodSummary.TotalLeadCount Property

總留單數

```csharp
public int TotalLeadCount { get; set; }
```

#### Property Value
[System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')

<a name='AlifeApi.BusinessRules.DailyReportModels.DailyReportPeriodSummary.TotalTransactionCount'></a>

## DailyReportPeriodSummary.TotalTransactionCount Property

總成交數

```csharp
public int TotalTransactionCount { get; set; }
```

#### Property Value
[System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')

<a name='AlifeApi.BusinessRules.DailyReportModels.DailyReportPeriodSummary.TotalVisitorCount'></a>

## DailyReportPeriodSummary.TotalVisitorCount Property

總來客數

```csharp
public int TotalVisitorCount { get; set; }
```

#### Property Value
[System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')