#### [AlifeApi.BusinessRules](index.md 'index')
### [AlifeApi.BusinessRules.SalesModels](AlifeApi.BusinessRules.SalesModels.md 'AlifeApi.BusinessRules.SalesModels')

## FloorInfo Class

樓層資訊

```csharp
public class FloorInfo
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; FloorInfo
### Properties

<a name='AlifeApi.BusinessRules.SalesModels.FloorInfo.FloorId'></a>

## FloorInfo.FloorId Property

樓層ID

```csharp
public int FloorId { get; set; }
```

#### Property Value
[System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')

<a name='AlifeApi.BusinessRules.SalesModels.FloorInfo.FloorLevel'></a>

## FloorInfo.FloorLevel Property

樓層數值 (用於排序)

```csharp
public int FloorLevel { get; set; }
```

#### Property Value
[System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')

<a name='AlifeApi.BusinessRules.SalesModels.FloorInfo.FloorNumber'></a>

## FloorInfo.FloorNumber Property

樓層編號 (例如: "15F", "14F", "店面")

```csharp
public string FloorNumber { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.SalesModels.FloorInfo.Summary'></a>

## FloorInfo.Summary Property

樓層統計摘要

```csharp
public global::FloorSummary Summary { get; set; }
```

#### Property Value
[FloorSummary](https://docs.microsoft.com/en-us/dotnet/api/FloorSummary 'FloorSummary')

<a name='AlifeApi.BusinessRules.SalesModels.FloorInfo.Units'></a>

## FloorInfo.Units Property

該樓層的房屋單位列表

```csharp
public System.Collections.Generic.List<AlifeApi.BusinessRules.SalesModels.UnitInfo> Units { get; set; }
```

#### Property Value
[System.Collections.Generic.List&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Collections.Generic.List-1 'System.Collections.Generic.List`1')[UnitInfo](AlifeApi.BusinessRules.SalesModels.UnitInfo.md 'AlifeApi.BusinessRules.SalesModels.UnitInfo')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Collections.Generic.List-1 'System.Collections.Generic.List`1')