﻿using System.Text.Json.Serialization;
using AlifeApi.BusinessRules.Infrastructure;

namespace AlifeApi.BusinessRules.LogAuditModels
{
    /// <summary>
    /// 日誌稽核列表查詢
    /// </summary>
    public class LogAuditMonthStatisticsGetInput : PagedListInput
    {
        /// <summary>
        /// 開始日期
        /// </summary>
        public DateTime? StartDate { get; set; }


        /// <summary>
        /// 結束日期
        /// </summary>
        public DateTime? EndDate { get; set; }

        /// <summary>
        /// 機關
        /// </summary>
        [JsonPropertyName("Depts")]
        public IEnumerable<string> DeptIds { get; set; } = Enumerable.Empty<string>();
    }
}
