﻿using AlifeApi.BusinessRules.Infrastructure;
using AlifeApi.DataAccess.ProjectContent;
using AlifeApi.DataAccess.ProjectContext;
using Microsoft.EntityFrameworkCore;

namespace AlifeApi.BusinessRules.UserRecordModels
{
    /// <summary>
    /// 使用者紀錄，稽核用
    /// </summary>
    public class UserRecordService : ServiceBase<alifeContext>
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="UserRecordService"/> class.
        /// </summary>
        /// <param name="serviceProvider">The service provider.</param>
        /// <param name="dbContext">The database context.</param>
        public UserRecordService(IServiceProvider serviceProvider, alifeContext dbContext) : base(serviceProvider, dbContext)
        {
        }

        ///// <summary>
        ///// 寫入 User 控制
        ///// </summary>
        ///// <param name="functionName">Name of the function.</param>
        ///// <param name="inputData">The input data.</param>
        //public async Task TryCreateUserRecordAsync(string functionName, string inputData)
        //{
        //    if (!await Db.SysCodes.AnyAsync(x => x.Type == "FunctionName" && x.Code == functionName && !x.IsDisabled))
        //    {
        //        return;
        //    }

        //    Db.sys.Add(new SysUserRecord
        //    {
        //        UserId = CurrentUser.UserId,
        //        DeptId = CurrentUser.DeptId,
        //        GradeCode = CurrentUser.GradeCode,
        //        RecordTime = DateTime.Now,
        //        RecordEvent = functionName,
        //        InputData = inputData,
        //        Ip = CurrentUser.IPAddress

        //    });

        //    await Db.SaveChangesAsync();
        //}
    }
}
