﻿using AlifeApi.Common.Util;
using AlifeApi.WebApi.Models.AuthorizationModels;
using AlifeApi.WebApi.Util;
using Microsoft.AspNetCore.Mvc;

namespace AlifeApi.WebApi.Controllers
{
    /// <summary>
    /// 權限驗證(Authorization)
    /// </summary>
    [Route("api/[controller]")]
    public class AuthorizationController : BasicController
    {
        private readonly AuthorizationAppService _appService;

        /// <summary>
        /// Initializes a new instance of the <see cref="AuthorizationController"/> class.
        /// </summary>
        /// <param name="appService">The application service.</param>
        /// <exception cref=System.ArgumentNullException">appService</exception>
        public AuthorizationController(AuthorizationAppService appService)
        {
            _appService = appService ?? throw new ArgumentNullException(nameof(appService));
        }

        /// <summary>
        /// 取得網頁登入驗證碼圖形
        /// </summary>
        /// <param name="captchaHelper">The captcha helper.</param>
        /// <param name="encryptionHelper">The encryption helper.</param>
        /// <returns>圖形驗證碼的 FileContentResult</returns>
        /// <exception cref="ArgumentNullException">captchaHelper</exception>
        /// <exception cref="ArgumentNullException">encryptionHelper</exception>
        [HttpGet]
        public FileContentResult Captcha([FromServices] CaptchaHelper captchaHelper, [FromServices] IEncryptionHelper encryptionHelper)
        {
            ArgumentNullException.ThrowIfNull(captchaHelper, nameof(captchaHelper));
            ArgumentNullException.ThrowIfNull(encryptionHelper, nameof(encryptionHelper));

            string randomCode = captchaHelper.GenerateRandomText();

            //將認證資訊放入Session
            HttpContext.Session.SetString("AlifeAuth", encryptionHelper.Encrypt(randomCode));

            byte[] result = captchaHelper.GenerateCaptchaImage(randomCode);

            return File(result, "image/png");
        }

        /// <summary>
        /// 刷新 Token
        /// </summary>
        /// <param name="token">The token.</param>
        /// <returns>Token info.</returns>
        [HttpPost]
        public RefreshTokenOutput RefreshTokenAsync(string token)
            => _appService.RefreshToken(token);
    }
}
