using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;

namespace AlifeApi.BusinessRules.DepartmentModels
{
    /// <summary>
    /// 新增部門輸入資料
    /// </summary>
    public class DepartmentCreateInput
    {
        /// <summary>
        /// 公司ID
        /// </summary>
        [JsonPropertyName("CompanyId")]
        [Required(ErrorMessage = "公司ID 為必填項")]
        public string CompanyId { get; set; }

        /// <summary>
        /// 部門ID
        /// </summary>
        [JsonPropertyName("DepartmentId")]
        [Required(ErrorMessage = "部門ID 為必填項")]
        [MaxLength(10, ErrorMessage = "部門ID 最多 10 個字元")]
        public string DepartmentId { get; set; }

        /// <summary>
        /// 部門名稱
        /// </summary>
        [JsonPropertyName("Name")]
        [Required(ErrorMessage = "部門名稱 為必填項")]
        [MaxLength(50, ErrorMessage = "部門名稱 最多 50 個字元")]
        public string Name { get; set; }
    }
} 
