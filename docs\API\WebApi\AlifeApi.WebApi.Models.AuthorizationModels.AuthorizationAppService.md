#### [AlifeApi.WebApi](index.md 'index')
### [AlifeApi.WebApi.Models.AuthorizationModels](AlifeApi.WebApi.Models.AuthorizationModels.md 'AlifeApi.WebApi.Models.AuthorizationModels')

## AuthorizationAppService Class

```csharp
public class AuthorizationAppService : AlifeApi.WebApi.Models.ApplicationService
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; [AlifeApi.Common.InfrastructureBase](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.Common.InfrastructureBase 'AlifeApi.Common.InfrastructureBase') &#129106; [AlifeApi.WebApi.Models.ApplicationService](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.WebApi.Models.ApplicationService 'AlifeApi.WebApi.Models.ApplicationService') &#129106; AuthorizationAppService
### Methods

<a name='AlifeApi.WebApi.Models.AuthorizationModels.AuthorizationAppService.RefreshToken(string)'></a>

## AuthorizationAppService.RefreshToken(string) Method

Refreshes the token.

```csharp
public AlifeApi.WebApi.Models.AuthorizationModels.RefreshTokenOutput RefreshToken(string token);
```
#### Parameters

<a name='AlifeApi.WebApi.Models.AuthorizationModels.AuthorizationAppService.RefreshToken(string).token'></a>

`token` [System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

The token.

#### Returns
[RefreshTokenOutput](AlifeApi.WebApi.Models.AuthorizationModels.RefreshTokenOutput.md 'AlifeApi.WebApi.Models.AuthorizationModels.RefreshTokenOutput')  
Token info.