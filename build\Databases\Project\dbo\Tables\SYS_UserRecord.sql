﻿CREATE TABLE [dbo].[SYS_UserRecord] (
    [Id]          BIGINT         IDENTITY (1, 1) NOT NULL,
    [UserId]      VARCHAR (15)   NOT NULL,
    [DeptId]      VARCHAR (20)   NOT NULL,
    [GradeCode]   NVARCHAR (72)  NOT NULL,
    [RecordTime]  DATETIME       NOT NULL,
    [RecordEvent] NVARCHAR (50)  NOT NULL,
    [InputData]   NVARCHAR (MAX) NOT NULL,
    [IP]          VARCHAR (40)   NOT NULL,
    CONSTRAINT [PK_SYS_UserRecord] PRIMARY KEY CLUSTERED ([Id] ASC),
    CONSTRAINT [FK_SYS_UserRecord_UserDept] FOREIGN KEY ([DeptId]) REFERENCES [dbo].[UserDept] ([Id]),
    CONSTRAINT [FK_SYS_UserRecord_UserInfo] FOREIGN KEY ([UserId]) REFERENCES [dbo].[UserInfo] ([Id])
);


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'使用者操作紀錄', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'SYS_UserRecord';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'流水號', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'SYS_UserRecord', @level2type = N'COLUMN', @level2name = N'Id';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'使用者', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'SYS_UserRecord', @level2type = N'COLUMN', @level2name = N'UserId';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'使用者部門', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'SYS_UserRecord', @level2type = N'COLUMN', @level2name = N'DeptId';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'職位', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'SYS_UserRecord', @level2type = N'COLUMN', @level2name = N'GradeCode';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'紀錄時間', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'SYS_UserRecord', @level2type = N'COLUMN', @level2name = N'RecordTime';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'紀錄API', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'SYS_UserRecord', @level2type = N'COLUMN', @level2name = N'RecordEvent';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'輸入資料', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'SYS_UserRecord', @level2type = N'COLUMN', @level2name = N'InputData';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'IP', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'SYS_UserRecord', @level2type = N'COLUMN', @level2name = N'IP';

