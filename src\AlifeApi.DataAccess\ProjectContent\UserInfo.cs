﻿using System;
using System.Collections.Generic;

namespace AlifeApi.DataAccess.ProjectContent
{
    /// <summary>
    /// 員工基本資料表，用於儲存員工的個人資訊、聯絡方式等相關資料，角色、部門、職位資訊透過關聯表記錄。
    /// </summary>
    public partial class UserInfo
    {
        public UserInfo()
        {
            SiteChairmanNavigations = new HashSet<Site>();
            SiteCreatedUserInfos = new HashSet<Site>();
            SiteDeputyProjectManagerNavigations = new HashSet<Site>();
            SiteProjectManagerNavigations = new HashSet<Site>();
            SiteUpdatedUserInfos = new HashSet<Site>();
            SiteViceChairmanNavigations = new HashSet<Site>();
            SysRoleGroupUsers = new HashSet<SysRoleGroupUser>();
            UserDepartments = new HashSet<UserDepartment>();
            UserJobTitles = new HashSet<UserJobTitle>();
        }

        /// <summary>
        /// 員工編號，主鍵，用於唯一識別員工。
        /// </summary>
        public string UserInfoId { get; set; }
        /// <summary>
        /// 公司編號，對應 Company 表的 CompanyId，記錄員工所屬的主要公司。
        /// </summary>
        public string CompanyId { get; set; }
        /// <summary>
        /// 密碼，儲存加密後的密碼內容。
        /// </summary>
        public string Password { get; set; }
        /// <summary>
        /// 員工姓名。
        /// </summary>
        public string Name { get; set; }
        /// <summary>
        /// 性別，例如 M（男）、F（女）。
        /// </summary>
        public string Gender { get; set; }
        /// <summary>
        /// 出生日期。
        /// </summary>
        public DateOnly? BirthDate { get; set; }
        /// <summary>
        /// 電話號碼。
        /// </summary>
        public string TelephoneNumber { get; set; }
        /// <summary>
        /// 手機號碼。
        /// </summary>
        public string MobileNumber { get; set; }
        /// <summary>
        /// 電子郵件地址。
        /// </summary>
        public string Email { get; set; }
        /// <summary>
        /// 戶籍地址。
        /// </summary>
        public string RegisteredAddress { get; set; }
        /// <summary>
        /// 通訊地址。
        /// </summary>
        public string MailingAddress { get; set; }
        /// <summary>
        /// 緊急聯絡人姓名。
        /// </summary>
        public string EmergencyContactName { get; set; }
        /// <summary>
        /// 緊急聯絡人電話。
        /// </summary>
        public string EmergencyContactPhone { get; set; }
        /// <summary>
        /// 與緊急聯絡人的關係，例如父母、配偶等。
        /// </summary>
        public string EmergencyContactRelation { get; set; }
        /// <summary>
        /// 所屬案場。
        /// </summary>
        public string ServiceUnit { get; set; }
        /// <summary>
        /// 到職日期。
        /// </summary>
        public DateOnly? HireDate { get; set; }
        /// <summary>
        /// 狀態，true 表示啟用，false 表示停用。
        /// </summary>
        public bool Status { get; set; }
        /// <summary>
        /// 創建時間，記錄資料創建的時間。
        /// </summary>
        public DateTime CreatedTime { get; set; }
        /// <summary>
        /// 更新時間，記錄資料最後更新的時間。
        /// </summary>
        public DateTime UpdatedTime { get; set; }
        /// <summary>
        /// 建立者，記錄創建此資料的員工編號。
        /// </summary>
        public string CreatedUserInfoId { get; set; }
        /// <summary>
        /// 更新者，記錄最後更新此資料的員工編號。
        /// </summary>
        public string UpdatedUserInfoId { get; set; }
        /// <summary>
        /// 最後一次登入的 IP 地址。
        /// </summary>
        public string LastLoginIp { get; set; }
        /// <summary>
        /// 最後一次登入的時間。
        /// </summary>
        public DateTime? LastLoginTime { get; set; }
        /// <summary>
        /// 最後一次登出的時間。
        /// </summary>
        public DateTime? LastLogoutTime { get; set; }
        /// <summary>
        /// 登入失敗次數加總，用於追蹤密碼錯誤次數。
        /// </summary>
        public short? LoginFailedCount { get; set; }
        /// <summary>
        /// 使否為內場/外場人員
        /// </summary>
        public bool? IsInside { get; set; }
        /// <summary>
        /// 是否為 M365 帳號，true 表示是，false 表示否。
        /// </summary>
        public bool? IsM365 { get; set; }
        /// <summary>
        /// 是否需要發送電子郵件通知，true 表示是，false 表示否。
        /// </summary>
        public bool? IsEmailNotificationEnabled { get; set; }
        /// <summary>
        /// 身分證字號
        /// </summary>
        public string Identity { get; set; }

        public virtual ICollection<Site> SiteChairmanNavigations { get; set; }
        public virtual ICollection<Site> SiteCreatedUserInfos { get; set; }
        public virtual ICollection<Site> SiteDeputyProjectManagerNavigations { get; set; }
        public virtual ICollection<Site> SiteProjectManagerNavigations { get; set; }
        public virtual ICollection<Site> SiteUpdatedUserInfos { get; set; }
        public virtual ICollection<Site> SiteViceChairmanNavigations { get; set; }
        public virtual ICollection<SysRoleGroupUser> SysRoleGroupUsers { get; set; }
        public virtual ICollection<UserDepartment> UserDepartments { get; set; }
        public virtual ICollection<UserJobTitle> UserJobTitles { get; set; }
    }
}
