using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;

namespace AlifeApi.BusinessRules.RoleGroupModels
{
    /// <summary>
    /// 修改角色權限使用者（PostgreSQL 版本）
    /// </summary>
    public class RoleGroupUserUpdateInputPg
    {
        /// <summary>
        /// 角色ID
        /// </summary>
        [Required]
        public string Id { get; set; }

        /// <summary>
        /// 案場代碼，對應 Sites 表的 SiteCode
        /// </summary>
        [JsonPropertyName("SiteCode")]
        [MaxLength(50)]
        public string SiteCode { get; set; }

        /// <summary>
        /// 使用者清單
        /// </summary>
        [JsonPropertyName("UserId")]
        public IEnumerable<string> UserIds { get; set; } = Enumerable.Empty<string>();
    }
} 
