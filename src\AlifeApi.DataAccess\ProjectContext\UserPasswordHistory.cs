﻿using System;
using System.Collections.Generic;

namespace AlifeApi.DataAccess.ProjectContext
{
    /// <summary>
    /// 密碼歷史資料
    /// </summary>
    public partial class UserPasswordHistory
    {
        /// <summary>
        /// 流水號
        /// </summary>
        public long Id { get; set; }

        /// <summary>
        /// 員工編號
        /// </summary>
        public string UserId { get; set; }

        /// <summary>
        /// 密碼
        /// </summary>
        public string Pw { get; set; }

        /// <summary>
        /// 建立者
        /// </summary>
        public string CreatedUserId { get; set; }

        /// <summary>
        /// 建立時間
        /// </summary>
        public DateTime CreatedTime { get; set; }

        public virtual UserInfo User { get; set; }

    }
}
