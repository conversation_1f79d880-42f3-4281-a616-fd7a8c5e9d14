#### [AlifeApi.DataAccess](index.md 'index')
### [AlifeApi.DataAccess.ProjectContent](AlifeApi.DataAccess.ProjectContent.md 'AlifeApi.DataAccess.ProjectContent')

## UserPasswordResetCode Class

密碼重置驗證碼表，用於儲存員工密碼重置時的驗證碼資訊。

```csharp
public class UserPasswordResetCode
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; UserPasswordResetCode
### Properties

<a name='AlifeApi.DataAccess.ProjectContent.UserPasswordResetCode.ExpirationTime'></a>

## UserPasswordResetCode.ExpirationTime Property

驗證碼到期時間，過期後無法使用。

```csharp
public System.Nullable<System.DateTime> ExpirationTime { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.DateTime](https://docs.microsoft.com/en-us/dotnet/api/System.DateTime 'System.DateTime')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

<a name='AlifeApi.DataAccess.ProjectContent.UserPasswordResetCode.IsUsed'></a>

## UserPasswordResetCode.IsUsed Property

是否已使用，true 表示已使用，false 表示未使用，預設為 false。

```csharp
public System.Nullable<bool> IsUsed { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.Boolean](https://docs.microsoft.com/en-us/dotnet/api/System.Boolean 'System.Boolean')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

<a name='AlifeApi.DataAccess.ProjectContent.UserPasswordResetCode.UserInfoId'></a>

## UserPasswordResetCode.UserInfoId Property

員工編號，對應 UserInfo 表的 UserInfoId。

```csharp
public string UserInfoId { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContent.UserPasswordResetCode.UserPasswordResetCodesId'></a>

## UserPasswordResetCode.UserPasswordResetCodesId Property

流水號，主鍵，使用 UUID 格式唯一識別驗證碼記錄。

```csharp
public System.Guid UserPasswordResetCodesId { get; set; }
```

#### Property Value
[System.Guid](https://docs.microsoft.com/en-us/dotnet/api/System.Guid 'System.Guid')

<a name='AlifeApi.DataAccess.ProjectContent.UserPasswordResetCode.VerificationCode'></a>

## UserPasswordResetCode.VerificationCode Property

驗證碼，通常為 6 位數。

```csharp
public string VerificationCode { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')