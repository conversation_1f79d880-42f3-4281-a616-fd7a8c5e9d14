#### [AlifeApi.DataAccess](index.md 'index')
### [AlifeApi.DataAccess.ProjectContext](AlifeApi.DataAccess.ProjectContext.md 'AlifeApi.DataAccess.ProjectContext')

## SysRoleGroup Class

角色群組資料

```csharp
public class SysRoleGroup
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; SysRoleGroup
### Properties

<a name='AlifeApi.DataAccess.ProjectContext.SysRoleGroup.CreatedTime'></a>

## SysRoleGroup.CreatedTime Property

建立者

```csharp
public System.DateTime CreatedTime { get; set; }
```

#### Property Value
[System.DateTime](https://docs.microsoft.com/en-us/dotnet/api/System.DateTime 'System.DateTime')

<a name='AlifeApi.DataAccess.ProjectContext.SysRoleGroup.CreatedUserId'></a>

## SysRoleGroup.CreatedUserId Property

建立時間

```csharp
public string CreatedUserId { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContext.SysRoleGroup.IsAdmin'></a>

## SysRoleGroup.IsAdmin Property

是否為管理者

```csharp
public bool IsAdmin { get; set; }
```

#### Property Value
[System.Boolean](https://docs.microsoft.com/en-us/dotnet/api/System.Boolean 'System.Boolean')

<a name='AlifeApi.DataAccess.ProjectContext.SysRoleGroup.Name'></a>

## SysRoleGroup.Name Property

角色群組名稱

```csharp
public string Name { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContext.SysRoleGroup.RoleGroupId'></a>

## SysRoleGroup.RoleGroupId Property

角色群組識別編號

```csharp
public string RoleGroupId { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContext.SysRoleGroup.System'></a>

## SysRoleGroup.System Property

系統名稱

```csharp
public string System { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContext.SysRoleGroup.UpdatedTime'></a>

## SysRoleGroup.UpdatedTime Property

更新者

```csharp
public System.DateTime UpdatedTime { get; set; }
```

#### Property Value
[System.DateTime](https://docs.microsoft.com/en-us/dotnet/api/System.DateTime 'System.DateTime')

<a name='AlifeApi.DataAccess.ProjectContext.SysRoleGroup.UpdatedUserId'></a>

## SysRoleGroup.UpdatedUserId Property

更新時間

```csharp
public string UpdatedUserId { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')