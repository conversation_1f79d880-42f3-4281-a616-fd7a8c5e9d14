#### [AlifeApi.BusinessRules](index.md 'index')
### [AlifeApi.BusinessRules.LogAuditModels](AlifeApi.BusinessRules.LogAuditModels.md 'AlifeApi.BusinessRules.LogAuditModels')

## LogAuditMonthStatisticsGetInput Class

日誌稽核列表查詢

```csharp
public class LogAuditMonthStatisticsGetInput : AlifeApi.BusinessRules.Infrastructure.PagedListInput
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; [PagedListInput](AlifeApi.BusinessRules.Infrastructure.PagedListInput.md 'AlifeApi.BusinessRules.Infrastructure.PagedListInput') &#129106; LogAuditMonthStatisticsGetInput
### Properties

<a name='AlifeApi.BusinessRules.LogAuditModels.LogAuditMonthStatisticsGetInput.DeptIds'></a>

## LogAuditMonthStatisticsGetInput.DeptIds Property

機關

```csharp
public System.Collections.Generic.IEnumerable<string> DeptIds { get; set; }
```

#### Property Value
[System.Collections.Generic.IEnumerable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Collections.Generic.IEnumerable-1 'System.Collections.Generic.IEnumerable`1')[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Collections.Generic.IEnumerable-1 'System.Collections.Generic.IEnumerable`1')

<a name='AlifeApi.BusinessRules.LogAuditModels.LogAuditMonthStatisticsGetInput.EndDate'></a>

## LogAuditMonthStatisticsGetInput.EndDate Property

結束日期

```csharp
public System.Nullable<System.DateTime> EndDate { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.DateTime](https://docs.microsoft.com/en-us/dotnet/api/System.DateTime 'System.DateTime')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

<a name='AlifeApi.BusinessRules.LogAuditModels.LogAuditMonthStatisticsGetInput.StartDate'></a>

## LogAuditMonthStatisticsGetInput.StartDate Property

開始日期

```csharp
public System.Nullable<System.DateTime> StartDate { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.DateTime](https://docs.microsoft.com/en-us/dotnet/api/System.DateTime 'System.DateTime')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')