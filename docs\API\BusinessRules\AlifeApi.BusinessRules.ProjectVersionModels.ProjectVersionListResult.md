#### [AlifeApi.BusinessRules](index.md 'index')
### [AlifeApi.BusinessRules.ProjectVersionModels](AlifeApi.BusinessRules.ProjectVersionModels.md 'AlifeApi.BusinessRules.ProjectVersionModels')

## ProjectVersionListResult Class

```csharp
public class ProjectVersionListResult : AlifeApi.BusinessRules.ProjectVersionModels.ProjectVersionBase
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; [ProjectVersionBase](AlifeApi.BusinessRules.ProjectVersionModels.ProjectVersionBase.md 'AlifeApi.BusinessRules.ProjectVersionModels.ProjectVersionBase') &#129106; ProjectVersionListResult
### Properties

<a name='AlifeApi.BusinessRules.ProjectVersionModels.ProjectVersionListResult.Content'></a>

## ProjectVersionListResult.Content Property

更新內容

```csharp
public string Content { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.ProjectVersionModels.ProjectVersionListResult.LastUpdateTime'></a>

## ProjectVersionListResult.LastUpdateTime Property

版本發布時間(DateTime)

```csharp
public System.Nullable<System.DateTime> LastUpdateTime { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.DateTime](https://docs.microsoft.com/en-us/dotnet/api/System.DateTime 'System.DateTime')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

<a name='AlifeApi.BusinessRules.ProjectVersionModels.ProjectVersionListResult.LastUpdateUser'></a>

## ProjectVersionListResult.LastUpdateUser Property

版本發佈人

```csharp
public string LastUpdateUser { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.ProjectVersionModels.ProjectVersionListResult.P_ID'></a>

## ProjectVersionListResult.P_ID Property

版本號唯一值

```csharp
public int P_ID { get; set; }
```

#### Property Value
[System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')

<a name='AlifeApi.BusinessRules.ProjectVersionModels.ProjectVersionListResult.Version'></a>

## ProjectVersionListResult.Version Property

系統專案版號

```csharp
public string Version { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.ProjectVersionModels.ProjectVersionListResult.VersionHash'></a>

## ProjectVersionListResult.VersionHash Property

Git唯一值

```csharp
public string VersionHash { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')