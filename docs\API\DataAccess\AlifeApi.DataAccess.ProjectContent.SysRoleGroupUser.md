#### [AlifeApi.DataAccess](index.md 'index')
### [AlifeApi.DataAccess.ProjectContent](AlifeApi.DataAccess.ProjectContent.md 'AlifeApi.DataAccess.ProjectContent')

## SysRoleGroupUser Class

角色群組使用者關聯表，用於儲存角色群組與使用者的關聯關係。

```csharp
public class SysRoleGroupUser
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; SysRoleGroupUser
### Properties

<a name='AlifeApi.DataAccess.ProjectContent.SysRoleGroupUser.RoleGroupId'></a>

## SysRoleGroupUser.RoleGroupId Property

角色群組識別編號，主鍵的一部分，對應 SYS_RoleGroup 表的 RoleGroupId。

```csharp
public string RoleGroupId { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContent.SysRoleGroupUser.System'></a>

## SysRoleGroupUser.System Property

系統名稱，主鍵的一部分，用於區分不同系統。

```csharp
public string System { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContent.SysRoleGroupUser.UserInfoId'></a>

## SysRoleGroupUser.UserInfoId Property

使用者編號，主鍵的一部分，對應 UserInfo 表的 UserInfoId。

```csharp
public string UserInfoId { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')