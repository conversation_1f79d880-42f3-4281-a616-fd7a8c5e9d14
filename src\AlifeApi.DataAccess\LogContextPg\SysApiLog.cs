﻿using System;
using System.Collections.Generic;

namespace LogcontextPg
{
    /// <summary>
    /// API 日誌記錄表
    /// </summary>
    public partial class SysApiLog
    {
        /// <summary>
        /// 0
        /// </summary>
        public long Id { get; set; }
        /// <summary>
        /// 系統名稱
        /// </summary>
        public string System { get; set; }
        /// <summary>
        /// 輸入資料
        /// </summary>
        public string InputData { get; set; }
        /// <summary>
        /// 請求標頭
        /// </summary>
        public string Headers { get; set; }
        /// <summary>
        /// 輸出資料
        /// </summary>
        public string OutputData { get; set; }
        /// <summary>
        /// 操作時間
        /// </summary>
        public DateTime ExecutedTime { get; set; }
        /// <summary>
        /// 控制器名稱
        /// </summary>
        public string ControllerName { get; set; }
        /// <summary>
        /// 執行方法名稱
        /// </summary>
        public string ActionName { get; set; }
        /// <summary>
        /// 執行時間（秒）
        /// </summary>
        public decimal Seconds { get; set; }
        /// <summary>
        /// 請求來源
        /// </summary>
        public string Source { get; set; }
        /// <summary>
        /// 錯誤異常
        /// </summary>
        public string Exception { get; set; }
        /// <summary>
        /// Session ID
        /// </summary>
        public string SessionId { get; set; }
    }
}
