#### [AlifeApi.BusinessRules](index.md 'index')
### [AlifeApi.BusinessRules.RoleGroupModels](AlifeApi.BusinessRules.RoleGroupModels.md 'AlifeApi.BusinessRules.RoleGroupModels')

## RoleGroupDeleteInputPg Class

刪除角色權限（PostgreSQL 版本）

```csharp
public class RoleGroupDeleteInputPg
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; RoleGroupDeleteInputPg
### Properties

<a name='AlifeApi.BusinessRules.RoleGroupModels.RoleGroupDeleteInputPg.Id'></a>

## RoleGroupDeleteInputPg.Id Property

角色ID

```csharp
public string Id { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.RoleGroupModels.RoleGroupDeleteInputPg.SiteCode'></a>

## RoleGroupDeleteInputPg.SiteCode Property

案場代碼，對應 Sites 表的 SiteCode

```csharp
public string SiteCode { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')