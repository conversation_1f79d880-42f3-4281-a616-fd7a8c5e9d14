#### [AlifeApi.DataAccess](index.md 'index')
### [AlifeApi.DataAccess.ProjectContent](AlifeApi.DataAccess.ProjectContent.md 'AlifeApi.DataAccess.ProjectContent')

## UserPasswordHistory Class

密碼歷史記錄表，用於儲存員工的歷史密碼資訊。

```csharp
public class UserPasswordHistory
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; UserPasswordHistory
### Properties

<a name='AlifeApi.DataAccess.ProjectContent.UserPasswordHistory.CreatedTime'></a>

## UserPasswordHistory.CreatedTime Property

創建時間，記錄密碼記錄創建的時間。

```csharp
public System.DateTime CreatedTime { get; set; }
```

#### Property Value
[System.DateTime](https://docs.microsoft.com/en-us/dotnet/api/System.DateTime 'System.DateTime')

<a name='AlifeApi.DataAccess.ProjectContent.UserPasswordHistory.CreatedUserInfoId'></a>

## UserPasswordHistory.CreatedUserInfoId Property

建立者，記錄創建此記錄的員工編號。

```csharp
public string CreatedUserInfoId { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContent.UserPasswordHistory.Password'></a>

## UserPasswordHistory.Password Property

歷史密碼，儲存加密後的密碼內容。

```csharp
public string Password { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContent.UserPasswordHistory.UserInfoId'></a>

## UserPasswordHistory.UserInfoId Property

員工編號，對應 UserInfo 表的 UserInfoId。

```csharp
public string UserInfoId { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContent.UserPasswordHistory.UserPasswordHistoryId'></a>

## UserPasswordHistory.UserPasswordHistoryId Property

流水號，主鍵，用於唯一識別密碼記錄。

```csharp
public long UserPasswordHistoryId { get; set; }
```

#### Property Value
[System.Int64](https://docs.microsoft.com/en-us/dotnet/api/System.Int64 'System.Int64')