#### [AlifeApi.BusinessRules](index.md 'index')
### [AlifeApi.BusinessRules.UserInfoModels](AlifeApi.BusinessRules.UserInfoModels.md 'AlifeApi.BusinessRules.UserInfoModels')

## UserInfoUpdateInputPg Class

使用者更新輸入資料 (PostgreSQL版本)

```csharp
public class UserInfoUpdateInputPg
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; UserInfoUpdateInputPg
### Properties

<a name='AlifeApi.BusinessRules.UserInfoModels.UserInfoUpdateInputPg.BirthDate'></a>

## UserInfoUpdateInputPg.BirthDate Property

出生日期

```csharp
public System.Nullable<System.DateOnly> BirthDate { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.DateOnly](https://docs.microsoft.com/en-us/dotnet/api/System.DateOnly 'System.DateOnly')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

<a name='AlifeApi.BusinessRules.UserInfoModels.UserInfoUpdateInputPg.CompanyId'></a>

## UserInfoUpdateInputPg.CompanyId Property

公司ID

```csharp
public string CompanyId { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.UserInfoModels.UserInfoUpdateInputPg.DepartmentId'></a>

## UserInfoUpdateInputPg.DepartmentId Property

部門ID

```csharp
public string DepartmentId { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.UserInfoModels.UserInfoUpdateInputPg.Email'></a>

## UserInfoUpdateInputPg.Email Property

電子郵件

```csharp
public string Email { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.UserInfoModels.UserInfoUpdateInputPg.EmergencyContactName'></a>

## UserInfoUpdateInputPg.EmergencyContactName Property

緊急聯絡人姓名

```csharp
public string EmergencyContactName { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.UserInfoModels.UserInfoUpdateInputPg.EmergencyContactPhone'></a>

## UserInfoUpdateInputPg.EmergencyContactPhone Property

緊急聯絡人電話

```csharp
public string EmergencyContactPhone { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.UserInfoModels.UserInfoUpdateInputPg.EmergencyContactRelation'></a>

## UserInfoUpdateInputPg.EmergencyContactRelation Property

緊急聯絡人關係

```csharp
public string EmergencyContactRelation { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.UserInfoModels.UserInfoUpdateInputPg.Gender'></a>

## UserInfoUpdateInputPg.Gender Property

性別

```csharp
public string Gender { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.UserInfoModels.UserInfoUpdateInputPg.HireDate'></a>

## UserInfoUpdateInputPg.HireDate Property

到職日期

```csharp
public System.Nullable<System.DateOnly> HireDate { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.DateOnly](https://docs.microsoft.com/en-us/dotnet/api/System.DateOnly 'System.DateOnly')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

<a name='AlifeApi.BusinessRules.UserInfoModels.UserInfoUpdateInputPg.IsEmailNotificationEnabled'></a>

## UserInfoUpdateInputPg.IsEmailNotificationEnabled Property

是否啟用郵件通知

```csharp
public System.Nullable<bool> IsEmailNotificationEnabled { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.Boolean](https://docs.microsoft.com/en-us/dotnet/api/System.Boolean 'System.Boolean')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

<a name='AlifeApi.BusinessRules.UserInfoModels.UserInfoUpdateInputPg.IsInside'></a>

## UserInfoUpdateInputPg.IsInside Property

是否為內部人員

```csharp
public System.Nullable<bool> IsInside { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.Boolean](https://docs.microsoft.com/en-us/dotnet/api/System.Boolean 'System.Boolean')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

<a name='AlifeApi.BusinessRules.UserInfoModels.UserInfoUpdateInputPg.IsM365'></a>

## UserInfoUpdateInputPg.IsM365 Property

是否啟用M365

```csharp
public System.Nullable<bool> IsM365 { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.Boolean](https://docs.microsoft.com/en-us/dotnet/api/System.Boolean 'System.Boolean')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

<a name='AlifeApi.BusinessRules.UserInfoModels.UserInfoUpdateInputPg.JobTitleId'></a>

## UserInfoUpdateInputPg.JobTitleId Property

職稱ID

```csharp
public string JobTitleId { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.UserInfoModels.UserInfoUpdateInputPg.MailingAddress'></a>

## UserInfoUpdateInputPg.MailingAddress Property

通訊地址

```csharp
public string MailingAddress { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.UserInfoModels.UserInfoUpdateInputPg.MobileNumber'></a>

## UserInfoUpdateInputPg.MobileNumber Property

手機

```csharp
public string MobileNumber { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.UserInfoModels.UserInfoUpdateInputPg.Name'></a>

## UserInfoUpdateInputPg.Name Property

姓名

```csharp
public string Name { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.UserInfoModels.UserInfoUpdateInputPg.Password'></a>

## UserInfoUpdateInputPg.Password Property

密碼

```csharp
public string Password { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.UserInfoModels.UserInfoUpdateInputPg.RegisteredAddress'></a>

## UserInfoUpdateInputPg.RegisteredAddress Property

戶籍地址

```csharp
public string RegisteredAddress { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.UserInfoModels.UserInfoUpdateInputPg.RoleGroupIds'></a>

## UserInfoUpdateInputPg.RoleGroupIds Property

角色群組ID列表

```csharp
public System.Collections.Generic.List<string> RoleGroupIds { get; set; }
```

#### Property Value
[System.Collections.Generic.List&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Collections.Generic.List-1 'System.Collections.Generic.List`1')[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Collections.Generic.List-1 'System.Collections.Generic.List`1')

<a name='AlifeApi.BusinessRules.UserInfoModels.UserInfoUpdateInputPg.ServiceUnit'></a>

## UserInfoUpdateInputPg.ServiceUnit Property

服務單位

```csharp
public string ServiceUnit { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.UserInfoModels.UserInfoUpdateInputPg.Status'></a>

## UserInfoUpdateInputPg.Status Property

帳號狀態

```csharp
public System.Nullable<bool> Status { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.Boolean](https://docs.microsoft.com/en-us/dotnet/api/System.Boolean 'System.Boolean')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

<a name='AlifeApi.BusinessRules.UserInfoModels.UserInfoUpdateInputPg.TelephoneNumber'></a>

## UserInfoUpdateInputPg.TelephoneNumber Property

市話

```csharp
public string TelephoneNumber { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.UserInfoModels.UserInfoUpdateInputPg.UserInfoId'></a>

## UserInfoUpdateInputPg.UserInfoId Property

使用者ID

```csharp
public string UserInfoId { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')