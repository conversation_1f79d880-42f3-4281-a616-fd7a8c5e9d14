#### [AlifeApi.BusinessRules](index.md 'index')

## AlifeApi.BusinessRules.Infrastructure Namespace

| Classes | |
| :--- | :--- |
| [ApiResult](AlifeApi.BusinessRules.Infrastructure.ApiResult.md 'AlifeApi.BusinessRules.Infrastructure.ApiResult') | API 回傳的通用結果 |
| [ApiResult&lt;TBody&gt;](AlifeApi.BusinessRules.Infrastructure.ApiResult_TBody_.md 'AlifeApi.BusinessRules.Infrastructure.ApiResult<TBody>') | API 回傳的通用結果 |
| [ImageStorageService](AlifeApi.BusinessRules.Infrastructure.ImageStorageService.md 'AlifeApi.BusinessRules.Infrastructure.ImageStorageService') | 圖片儲存服務實作 |
| [LeftJoinExtensions](AlifeApi.BusinessRules.Infrastructure.LeftJoinExtensions.md 'AlifeApi.BusinessRules.Infrastructure.LeftJoinExtensions') | https://stackoverflow.com/questions/5537995/entity-framework-left-join |
| [PagedListInput](AlifeApi.BusinessRules.Infrastructure.PagedListInput.md 'AlifeApi.BusinessRules.Infrastructure.PagedListInput') | 分頁列表輸入 |
| [PagedListOutput&lt;TDetail&gt;](AlifeApi.BusinessRules.Infrastructure.PagedListOutput_TDetail_.md 'AlifeApi.BusinessRules.Infrastructure.PagedListOutput<TDetail>') | |
| [RecordDisableInput&lt;T&gt;](AlifeApi.BusinessRules.Infrastructure.RecordDisableInput_T_.md 'AlifeApi.BusinessRules.Infrastructure.RecordDisableInput<T>') | 停用/啟用資料的參數 |
| [RecordDisableInputWithStringId](AlifeApi.BusinessRules.Infrastructure.RecordDisableInputWithStringId.md 'AlifeApi.BusinessRules.Infrastructure.RecordDisableInputWithStringId') | 停用/啟用資料的參數 |
| [SearchTermInfo](AlifeApi.BusinessRules.Infrastructure.SearchTermInfo.md 'AlifeApi.BusinessRules.Infrastructure.SearchTermInfo') | 過濾資訊 |
| [ServiceBase&lt;TDbContext&gt;](AlifeApi.BusinessRules.Infrastructure.ServiceBase_TDbContext_.md 'AlifeApi.BusinessRules.Infrastructure.ServiceBase<TDbContext>') | The service base. |
| [SortOrderInfo](AlifeApi.BusinessRules.Infrastructure.SortOrderInfo.md 'AlifeApi.BusinessRules.Infrastructure.SortOrderInfo') | 排序資訊 |

| Interfaces | |
| :--- | :--- |
| [IApiMessage](AlifeApi.BusinessRules.Infrastructure.IApiMessage.md 'AlifeApi.BusinessRules.Infrastructure.IApiMessage') | |
| [ICurrentUser](AlifeApi.BusinessRules.Infrastructure.ICurrentUser.md 'AlifeApi.BusinessRules.Infrastructure.ICurrentUser') | 表示當前使用者的介面 |
| [IImageStorageService](AlifeApi.BusinessRules.Infrastructure.IImageStorageService.md 'AlifeApi.BusinessRules.Infrastructure.IImageStorageService') | 圖片儲存服務介面 |

| Enums | |
| :--- | :--- |
| [Message](AlifeApi.BusinessRules.Infrastructure.Message.md 'AlifeApi.BusinessRules.Infrastructure.Message') | 系統訊息 |
