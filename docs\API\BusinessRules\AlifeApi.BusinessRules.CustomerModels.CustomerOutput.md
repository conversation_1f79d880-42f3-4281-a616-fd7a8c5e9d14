#### [AlifeApi.BusinessRules](index.md 'index')
### [AlifeApi.BusinessRules.CustomerModels](AlifeApi.BusinessRules.CustomerModels.md 'AlifeApi.BusinessRules.CustomerModels')

## CustomerOutput Class

客戶資料輸出 DTO (詳細資訊)

```csharp
public class CustomerOutput
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; CustomerOutput
### Properties

<a name='AlifeApi.BusinessRules.CustomerModels.CustomerOutput.Address'></a>

## CustomerOutput.Address Property

詳細地址

```csharp
public string Address { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.CustomerModels.CustomerOutput.Birthday'></a>

## CustomerOutput.Birthday Property

生日

```csharp
public System.Nullable<System.DateOnly> Birthday { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.DateOnly](https://docs.microsoft.com/en-us/dotnet/api/System.DateOnly 'System.DateOnly')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

<a name='AlifeApi.BusinessRules.CustomerModels.CustomerOutput.Budget'></a>

## CustomerOutput.Budget Property

預算範圍

```csharp
public string Budget { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.CustomerModels.CustomerOutput.City'></a>

## CustomerOutput.City Property

縣市

```csharp
public string City { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.CustomerModels.CustomerOutput.CreatedTime'></a>

## CustomerOutput.CreatedTime Property

建立時間

```csharp
public System.DateTime CreatedTime { get; set; }
```

#### Property Value
[System.DateTime](https://docs.microsoft.com/en-us/dotnet/api/System.DateTime 'System.DateTime')

<a name='AlifeApi.BusinessRules.CustomerModels.CustomerOutput.CreatedUserId'></a>

## CustomerOutput.CreatedUserId Property

建立人員ID

```csharp
public string CreatedUserId { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.CustomerModels.CustomerOutput.CreatedUserName'></a>

## CustomerOutput.CreatedUserName Property

建立人員名稱

```csharp
public string CreatedUserName { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.CustomerModels.CustomerOutput.CustomerId'></a>

## CustomerOutput.CustomerId Property

客戶ID

```csharp
public string CustomerId { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.CustomerModels.CustomerOutput.CustomerRecords'></a>

## CustomerOutput.CustomerRecords Property

客戶訪談紀錄列表

```csharp
public System.Collections.Generic.List<AlifeApi.BusinessRules.CustomerModels.CustomerRecordOutput> CustomerRecords { get; set; }
```

#### Property Value
[System.Collections.Generic.List&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Collections.Generic.List-1 'System.Collections.Generic.List`1')[CustomerRecordOutput](AlifeApi.BusinessRules.CustomerModels.CustomerRecordOutput.md 'AlifeApi.BusinessRules.CustomerModels.CustomerRecordOutput')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Collections.Generic.List-1 'System.Collections.Generic.List`1')

<a name='AlifeApi.BusinessRules.CustomerModels.CustomerOutput.District'></a>

## CustomerOutput.District Property

區域

```csharp
public string District { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.CustomerModels.CustomerOutput.Email'></a>

## CustomerOutput.Email Property

電子信箱

```csharp
public string Email { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.CustomerModels.CustomerOutput.Gender'></a>

## CustomerOutput.Gender Property

性別

```csharp
public string Gender { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.CustomerModels.CustomerOutput.ImagePath'></a>

## CustomerOutput.ImagePath Property

圖片路徑 (對應 Customer.WebPath)

```csharp
public string ImagePath { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.CustomerModels.CustomerOutput.LeadSource'></a>

## CustomerOutput.LeadSource Property

得知管道 (客戶來源)

```csharp
public string LeadSource { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.CustomerModels.CustomerOutput.Name'></a>

## CustomerOutput.Name Property

客戶姓名

```csharp
public string Name { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.CustomerModels.CustomerOutput.Occupation'></a>

## CustomerOutput.Occupation Property

職業

```csharp
public string Occupation { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.CustomerModels.CustomerOutput.PhoneNumber'></a>

## CustomerOutput.PhoneNumber Property

電話

```csharp
public string PhoneNumber { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.CustomerModels.CustomerOutput.PurchaseConditions'></a>

## CustomerOutput.PurchaseConditions Property

其他購屋條件/備註 (對應 Customer.PurchaseConditions)

```csharp
public string PurchaseConditions { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.CustomerModels.CustomerOutput.RequiredLayout'></a>

## CustomerOutput.RequiredLayout Property

需求格局

```csharp
public string RequiredLayout { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.CustomerModels.CustomerOutput.RequiredPingArea'></a>

## CustomerOutput.RequiredPingArea Property

需求坪數

```csharp
public string RequiredPingArea { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.CustomerModels.CustomerOutput.SiteCode'></a>

## CustomerOutput.SiteCode Property

案場代碼

```csharp
public string SiteCode { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.CustomerModels.CustomerOutput.UpdatedTime'></a>

## CustomerOutput.UpdatedTime Property

更新時間

```csharp
public System.DateTime UpdatedTime { get; set; }
```

#### Property Value
[System.DateTime](https://docs.microsoft.com/en-us/dotnet/api/System.DateTime 'System.DateTime')

<a name='AlifeApi.BusinessRules.CustomerModels.CustomerOutput.UpdatedUserId'></a>

## CustomerOutput.UpdatedUserId Property

更新人員ID

```csharp
public string UpdatedUserId { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.CustomerModels.CustomerOutput.UpdatedUserName'></a>

## CustomerOutput.UpdatedUserName Property

更新人員名稱

```csharp
public string UpdatedUserName { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')