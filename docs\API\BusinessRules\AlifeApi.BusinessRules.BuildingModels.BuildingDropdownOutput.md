#### [AlifeApi.BusinessRules](index.md 'index')
### [AlifeApi.BusinessRules.BuildingModels](AlifeApi.BusinessRules.BuildingModels.md 'AlifeApi.BusinessRules.BuildingModels')

## BuildingDropdownOutput Class

建築物下拉選單輸出資料

```csharp
public class BuildingDropdownOutput
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; BuildingDropdownOutput
### Properties

<a name='AlifeApi.BusinessRules.BuildingModels.BuildingDropdownOutput.Name'></a>

## BuildingDropdownOutput.Name Property

名稱

```csharp
public string Name { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.BuildingModels.BuildingDropdownOutput.Value'></a>

## BuildingDropdownOutput.Value Property

值

```csharp
public int Value { get; set; }
```

#### Property Value
[System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')