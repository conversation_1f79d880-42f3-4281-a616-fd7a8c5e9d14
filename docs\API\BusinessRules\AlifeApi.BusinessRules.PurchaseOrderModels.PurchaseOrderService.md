#### [AlifeApi.BusinessRules](index.md 'index')
### [AlifeApi.BusinessRules.PurchaseOrderModels](AlifeApi.BusinessRules.PurchaseOrderModels.md 'AlifeApi.BusinessRules.PurchaseOrderModels')

## PurchaseOrderService Class

買賣預定單服務

```csharp
public class PurchaseOrderService : AlifeApi.BusinessRules.Infrastructure.ServiceBase<AlifeApi.DataAccess.ProjectContent.alifeContext>
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; [AlifeApi.Common.InfrastructureBase](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.Common.InfrastructureBase 'AlifeApi.Common.InfrastructureBase') &#129106; [AlifeApi.BusinessRules.Infrastructure.ServiceBase&lt;](AlifeApi.BusinessRules.Infrastructure.ServiceBase_TDbContext_.md 'AlifeApi.BusinessRules.Infrastructure.ServiceBase<TDbContext>')[AlifeApi.DataAccess.ProjectContent.alifeContext](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.DataAccess.ProjectContent.alifeContext 'AlifeApi.DataAccess.ProjectContent.alifeContext')[&gt;](AlifeApi.BusinessRules.Infrastructure.ServiceBase_TDbContext_.md 'AlifeApi.BusinessRules.Infrastructure.ServiceBase<TDbContext>') &#129106; PurchaseOrderService
### Methods

<a name='AlifeApi.BusinessRules.PurchaseOrderModels.PurchaseOrderService.CreatePurchaseOrderAsync(AlifeApi.BusinessRules.PurchaseOrderModels.PurchaseOrderCreateInput)'></a>

## PurchaseOrderService.CreatePurchaseOrderAsync(PurchaseOrderCreateInput) Method

建立買賣預定單

```csharp
public System.Threading.Tasks.Task<int> CreatePurchaseOrderAsync(AlifeApi.BusinessRules.PurchaseOrderModels.PurchaseOrderCreateInput input);
```
#### Parameters

<a name='AlifeApi.BusinessRules.PurchaseOrderModels.PurchaseOrderService.CreatePurchaseOrderAsync(AlifeApi.BusinessRules.PurchaseOrderModels.PurchaseOrderCreateInput).input'></a>

`input` [PurchaseOrderCreateInput](AlifeApi.BusinessRules.PurchaseOrderModels.PurchaseOrderCreateInput.md 'AlifeApi.BusinessRules.PurchaseOrderModels.PurchaseOrderCreateInput')

買賣預定單建立輸入資料

#### Returns
[System.Threading.Tasks.Task&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')[System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')  
新建買賣預定單的ID

<a name='AlifeApi.BusinessRules.PurchaseOrderModels.PurchaseOrderService.DeletePurchaseOrderAsync(int)'></a>

## PurchaseOrderService.DeletePurchaseOrderAsync(int) Method

刪除買賣預定單

```csharp
public System.Threading.Tasks.Task DeletePurchaseOrderAsync(int orderId);
```
#### Parameters

<a name='AlifeApi.BusinessRules.PurchaseOrderModels.PurchaseOrderService.DeletePurchaseOrderAsync(int).orderId'></a>

`orderId` [System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')

買賣預定單ID

#### Returns
[System.Threading.Tasks.Task](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task 'System.Threading.Tasks.Task')

<a name='AlifeApi.BusinessRules.PurchaseOrderModels.PurchaseOrderService.GetPurchaseOrderByIdAsync(int)'></a>

## PurchaseOrderService.GetPurchaseOrderByIdAsync(int) Method

根據ID取得買賣預定單詳細資料

```csharp
public System.Threading.Tasks.Task<AlifeApi.BusinessRules.PurchaseOrderModels.PurchaseOrderOutput?> GetPurchaseOrderByIdAsync(int orderId);
```
#### Parameters

<a name='AlifeApi.BusinessRules.PurchaseOrderModels.PurchaseOrderService.GetPurchaseOrderByIdAsync(int).orderId'></a>

`orderId` [System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')

買賣預定單ID

#### Returns
[System.Threading.Tasks.Task&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')[PurchaseOrderOutput](AlifeApi.BusinessRules.PurchaseOrderModels.PurchaseOrderOutput.md 'AlifeApi.BusinessRules.PurchaseOrderModels.PurchaseOrderOutput')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')  
買賣預定單詳細資料

<a name='AlifeApi.BusinessRules.PurchaseOrderModels.PurchaseOrderService.GetPurchaseOrderListAsync(AlifeApi.BusinessRules.PurchaseOrderModels.PurchaseOrderQueryInput)'></a>

## PurchaseOrderService.GetPurchaseOrderListAsync(PurchaseOrderQueryInput) Method

取得買賣預定單列表 (分頁)

```csharp
public System.Threading.Tasks.Task<AlifeApi.BusinessRules.Infrastructure.PagedListOutput<AlifeApi.BusinessRules.PurchaseOrderModels.PurchaseOrderListOutput>> GetPurchaseOrderListAsync(AlifeApi.BusinessRules.PurchaseOrderModels.PurchaseOrderQueryInput input);
```
#### Parameters

<a name='AlifeApi.BusinessRules.PurchaseOrderModels.PurchaseOrderService.GetPurchaseOrderListAsync(AlifeApi.BusinessRules.PurchaseOrderModels.PurchaseOrderQueryInput).input'></a>

`input` [PurchaseOrderQueryInput](AlifeApi.BusinessRules.PurchaseOrderModels.PurchaseOrderQueryInput.md 'AlifeApi.BusinessRules.PurchaseOrderModels.PurchaseOrderQueryInput')

查詢條件

#### Returns
[System.Threading.Tasks.Task&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')[AlifeApi.BusinessRules.Infrastructure.PagedListOutput&lt;](AlifeApi.BusinessRules.Infrastructure.PagedListOutput_TDetail_.md 'AlifeApi.BusinessRules.Infrastructure.PagedListOutput<TDetail>')[PurchaseOrderListOutput](AlifeApi.BusinessRules.PurchaseOrderModels.PurchaseOrderListOutput.md 'AlifeApi.BusinessRules.PurchaseOrderModels.PurchaseOrderListOutput')[&gt;](AlifeApi.BusinessRules.Infrastructure.PagedListOutput_TDetail_.md 'AlifeApi.BusinessRules.Infrastructure.PagedListOutput<TDetail>')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')  
分頁的買賣預定單列表

<a name='AlifeApi.BusinessRules.PurchaseOrderModels.PurchaseOrderService.LogPurchaseOrderHistoryAsync(int,string,string,string,string,string)'></a>

## PurchaseOrderService.LogPurchaseOrderHistoryAsync(int, string, string, string, string, string) Method

記錄訂單歷史

```csharp
private System.Threading.Tasks.Task LogPurchaseOrderHistoryAsync(int orderId, string actionType, string? oldStatus, string? newStatus, string contentRecord, string userInfoId);
```
#### Parameters

<a name='AlifeApi.BusinessRules.PurchaseOrderModels.PurchaseOrderService.LogPurchaseOrderHistoryAsync(int,string,string,string,string,string).orderId'></a>

`orderId` [System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')

<a name='AlifeApi.BusinessRules.PurchaseOrderModels.PurchaseOrderService.LogPurchaseOrderHistoryAsync(int,string,string,string,string,string).actionType'></a>

`actionType` [System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.PurchaseOrderModels.PurchaseOrderService.LogPurchaseOrderHistoryAsync(int,string,string,string,string,string).oldStatus'></a>

`oldStatus` [System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.PurchaseOrderModels.PurchaseOrderService.LogPurchaseOrderHistoryAsync(int,string,string,string,string,string).newStatus'></a>

`newStatus` [System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.PurchaseOrderModels.PurchaseOrderService.LogPurchaseOrderHistoryAsync(int,string,string,string,string,string).contentRecord'></a>

`contentRecord` [System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.PurchaseOrderModels.PurchaseOrderService.LogPurchaseOrderHistoryAsync(int,string,string,string,string,string).userInfoId'></a>

`userInfoId` [System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

#### Returns
[System.Threading.Tasks.Task](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task 'System.Threading.Tasks.Task')

<a name='AlifeApi.BusinessRules.PurchaseOrderModels.PurchaseOrderService.UpdateBusinessDatesAsync(int,dynamic)'></a>

## PurchaseOrderService.UpdateBusinessDatesAsync(int, dynamic) Method

更新訂單業務流程日期

```csharp
public System.Threading.Tasks.Task UpdateBusinessDatesAsync(int orderId, dynamic input);
```
#### Parameters

<a name='AlifeApi.BusinessRules.PurchaseOrderModels.PurchaseOrderService.UpdateBusinessDatesAsync(int,dynamic).orderId'></a>

`orderId` [System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')

訂單ID

<a name='AlifeApi.BusinessRules.PurchaseOrderModels.PurchaseOrderService.UpdateBusinessDatesAsync(int,dynamic).input'></a>

`input` [dynamic](https://docs.microsoft.com/en-us/dotnet/csharp/programming-guide/types/using-type-dynamic 'https://docs.microsoft.com/en-us/dotnet/csharp/programming-guide/types/using-type-dynamic')

業務日期更新輸入

#### Returns
[System.Threading.Tasks.Task](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task 'System.Threading.Tasks.Task')

<a name='AlifeApi.BusinessRules.PurchaseOrderModels.PurchaseOrderService.UpdatePurchaseOrderAsync(int,AlifeApi.BusinessRules.PurchaseOrderModels.PurchaseOrderUpdateInput)'></a>

## PurchaseOrderService.UpdatePurchaseOrderAsync(int, PurchaseOrderUpdateInput) Method

更新買賣預定單資訊

```csharp
public System.Threading.Tasks.Task UpdatePurchaseOrderAsync(int orderId, AlifeApi.BusinessRules.PurchaseOrderModels.PurchaseOrderUpdateInput input);
```
#### Parameters

<a name='AlifeApi.BusinessRules.PurchaseOrderModels.PurchaseOrderService.UpdatePurchaseOrderAsync(int,AlifeApi.BusinessRules.PurchaseOrderModels.PurchaseOrderUpdateInput).orderId'></a>

`orderId` [System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')

買賣預定單ID

<a name='AlifeApi.BusinessRules.PurchaseOrderModels.PurchaseOrderService.UpdatePurchaseOrderAsync(int,AlifeApi.BusinessRules.PurchaseOrderModels.PurchaseOrderUpdateInput).input'></a>

`input` [PurchaseOrderUpdateInput](AlifeApi.BusinessRules.PurchaseOrderModels.PurchaseOrderUpdateInput.md 'AlifeApi.BusinessRules.PurchaseOrderModels.PurchaseOrderUpdateInput')

買賣預定單更新輸入資料

#### Returns
[System.Threading.Tasks.Task](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task 'System.Threading.Tasks.Task')