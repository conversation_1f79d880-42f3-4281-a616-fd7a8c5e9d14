#### [AlifeApi.BusinessRules](index.md 'index')
### [AlifeApi.BusinessRules.Infrastructure](AlifeApi.BusinessRules.Infrastructure.md 'AlifeApi.BusinessRules.Infrastructure')

## RecordDisableInputWithStringId Class

停用/啟用資料的參數

```csharp
public class RecordDisableInputWithStringId
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; RecordDisableInputWithStringId
### Properties

<a name='AlifeApi.BusinessRules.Infrastructure.RecordDisableInputWithStringId.Id'></a>

## RecordDisableInputWithStringId.Id Property

Gets or sets the identifier.

```csharp
public string Id { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.Infrastructure.RecordDisableInputWithStringId.IsDisabled'></a>

## RecordDisableInputWithStringId.IsDisabled Property

Gets or sets the is disabled.

```csharp
public System.Nullable<bool> IsDisabled { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.Boolean](https://docs.microsoft.com/en-us/dotnet/api/System.Boolean 'System.Boolean')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')