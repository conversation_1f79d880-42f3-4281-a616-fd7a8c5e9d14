﻿using AlifeApi.BusinessRules.Infrastructure;
using AlifeApi.DataAccess.ProjectContent;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using AlifeApi.Common.DataAnnotations;
using AlifeApi.BusinessRules.CommonModels;
using AlifeApi.BusinessRules.UnitModels;
using AlifeApi.BusinessRules.ParkingSpaceModels;
using AlifeApi.BusinessRules.SalesModels;

namespace AlifeApi.BusinessRules.SalesModels
{
    /// <summary>
    /// - 2024/07/26  車位銷售管理
    /// - 關聯 PurchaseOrder.Id and PurchaseOrderItems
    /// - 車位透過 PurchaseOrderItems.ParkingSpaceId 關聯到 ParkingSpaces
    /// - 移除 PurchaseOrderParkingSpaces 表
    /// - 移除 PurchaseOrder 中的 UnitId, PropertyPrice, ParkingSpacePrice 欄位
    /// </summary>
    public class SalesService : ServiceBase<alifeContext>
    {
        public SalesService(IServiceProvider serviceProvider, alifeContext dbContext)
            : base(serviceProvider, dbContext)
        {
        }

        public async Task<BuildingSalesData> GetSalesControlAsync(SalesControlInput input)
        {
            ArgumentNullException.ThrowIfNull(input);

            var result = new BuildingSalesData();

            var floorsQuery = Db.Floors.AsQueryable()
                .Where(f => f.SiteCode == input.SiteCode &&
                           f.FloorType != null &&
                           !f.FloorType.Contains("停車"));

            if (input.BuildingId.HasValue)
            {
                floorsQuery = floorsQuery.Where(f => f.BuildingId == input.BuildingId.Value);
                var building = await Db.Buildings
                    .Where(b => b.BuildingId == input.BuildingId.Value)
                    .Select(b => b.BuildingName)
                    .FirstOrDefaultAsync();
                result.BuildingName = building;
            }

            var floors = await floorsQuery
                .OrderByDescending(f => f.FloorLevel)
                .ToListAsync();

            if (!floors.Any())
            {
                return result;
            }

            var floorIds = floors.Select(f => f.FloorId).ToList();

            var unitsWithOrderQuery =
                from unit in Db.Units
                join floor in Db.Floors on unit.FloorId equals floor.FloorId
                join building in Db.Buildings on unit.BuildingId equals building.BuildingId
                join poi in Db.PurchaseOrderItems on unit.UnitId equals poi.UnitId into poiGroup
                from poi in poiGroup.DefaultIfEmpty()
                join po in Db.PurchaseOrders on poi.OrderId equals po.OrderId into poGroup
                from po in poGroup.DefaultIfEmpty()
                where po == null || (po.Status != "已取消" && po.Status != "已作廢")
                join c in Db.Customers on po.CustomerId equals c.CustomerId into cGroup
                from c in cGroup.DefaultIfEmpty()
                where floorIds.Contains(unit.FloorId)
                select new
                {
                    Unit = unit,
                    Floor = floor,
                    Building = building,
                    LatestOrder = po,
                    Customer = c,
                };

            var unitData = await unitsWithOrderQuery.ToListAsync();

            foreach (var floor in floors)
            {
                var floorUnits = unitData.Where(u => u.Unit.FloorId == floor.FloorId)
                                        .OrderBy(u => u.Unit.UnitNumber)
                                        .ToList();

                var floorInfo = new FloorSalesData
                {
                    FloorLabel = floor.FloorLabel,
                    FloorId = floor.FloorId,
                    Units = floorUnits.Select(u => new UnitSalesData
                    {
                        UnitId = u.Unit.UnitId,
                        UnitNumber = u.Unit.UnitNumber,
                        Status = u.Unit.Status,
                        CustomerId = u.Customer?.CustomerId ?? 0,
                        CustomerName = u.Customer?.Name ?? string.Empty,
                        SaleDate = u.LatestOrder?.SaleDate,
                        RequestDate = u.LatestOrder?.RequestDate,
                        ReceiveDate = u.LatestOrder?.ReceiveDate,
                        PriceRegistrationSubmissionDate = u.LatestOrder?.PriceRegistrationSubmissionDate,
                        Area = u.Unit.TotalArea,
                        PurchaseInfo = u.LatestOrder?.OrderRemarks
                    }).ToList()
                };

                CalculateFloorSummary(floorInfo);
                result.Floors.Add(floorInfo);
            }

            CalculateColumnSummary(result);
            CalculateTotalSummary(result);
            return result;
        }

        private static void CalculateFloorSummary(FloorSalesData floorInfo)
        {
            var availableCount = floorInfo.Units.Count(u => u.Status == "可售");
            var soldCount = floorInfo.Units.Count(u => u.Status == "售" || u.Status == "簽" || u.Status == "足");
            var reservedCount = floorInfo.Units.Count(u => u.Status != null && u.Status.Contains("保留"));

            floorInfo.Summary = new FloorSummary
            {
                Available = availableCount,
                Sold = soldCount,
                Reserved = reservedCount,
                SalesRate = (availableCount + soldCount) > 0
                    ? $"{Math.Round((decimal)soldCount / (availableCount + soldCount) * 100)}%"
                    : "0%"
            };
        }

        private static void CalculateColumnSummary(BuildingSalesData result)
        {
            var allUnits = result.Floors.SelectMany(f => f.Units).ToList();

            if (!allUnits.Any()) return;

            var columnGroups = allUnits.GroupBy(u => u.UnitNumber).OrderBy(g => g.Key);

            foreach (var columnGroup in columnGroups)
            {
                var units = columnGroup.ToList();
                var availableCount = units.Count(u => u.Status == "可售");
                var soldCount = units.Count(u => u.Status == "售" || u.Status == "簽" || u.Status == "足");
                var reservedCount = units.Count(u => u.Status != null && u.Status.Contains("保留"));

                result.ColumnSummary.Add(new ColumnSummary
                {
                    Column = columnGroup.Key,
                    Available = availableCount,
                    Sold = soldCount,
                    Reserved = reservedCount,
                    SalesRate = (availableCount + soldCount) > 0
                        ? $"{Math.Round((decimal)soldCount / (availableCount + soldCount) * 100)}%"
                        : "0%"
                });
            }
        }

        private static void CalculateTotalSummary(BuildingSalesData result)
        {
            var totalAvailable = result.ColumnSummary.Sum(c => c.Available);
            var totalSold = result.ColumnSummary.Sum(c => c.Sold);
            var totalReserved = result.ColumnSummary.Sum(c => c.Reserved);

            result.TotalSummary = new TotalSummary
            {
                TotalAvailable = totalAvailable,
                TotalSold = totalSold,
                TotalReserved = totalReserved,
                OverallSalesRate = (totalAvailable + totalSold) > 0
                    ? $"{Math.Round((decimal)totalSold / (totalAvailable + totalSold) * 100)}%"
                    : "0%"
            };
        }

        public async Task<SalesControlListOutput> GetSalesControlListAsync(SalesControlListInput input)
        {
            var query = from poi in Db.PurchaseOrderItems
                        join po in Db.PurchaseOrders on poi.OrderId equals po.OrderId
                        join u in Db.UserInfos on po.SalespersonUserInfoId equals u.UserInfoId
                        join c in Db.Customers on po.CustomerId equals c.CustomerId
                        join unit in Db.Units on poi.UnitId equals unit.UnitId into unitJoin
                        from unit in unitJoin.DefaultIfEmpty()
                        join floor in Db.Floors on unit.FloorId equals floor.FloorId into floorJoin
                        from floor in floorJoin.DefaultIfEmpty()
                        join ps in Db.ParkingSpaces on poi.ParkingSpaceId equals ps.ParkingSpaceId into psJoin
                        from ps in psJoin.DefaultIfEmpty()
                        join b in Db.Buildings on (unit != null ? unit.BuildingId : ps.BuildingId) equals b.BuildingId
                        where po.SiteCode == input.SiteCode
                        select new
                        {
                            po,
                            poi,
                            SalespersonName = u.Name,
                            CustomerName = c.Name,
                            Unit = unit,
                            Floor = floor,
                            ParkingSpace = ps,
                            BuildingName = b.BuildingName
                        };

            if (input.BuildingId.HasValue)
            {
                query = query.Where(x => x.Unit.BuildingId == input.BuildingId.Value || x.ParkingSpace.BuildingId == input.BuildingId.Value);
            }

            if (input.StartDate.HasValue)
            {
                query = query.Where(x => x.po.SaleDate >= input.StartDate.Value);
            }

            if (input.EndDate.HasValue)
            {
                query = query.Where(x => x.po.SaleDate <= input.EndDate.Value);
            }

            var salesData = await query.ToListAsync();

            var records = salesData.Select(s => new SalesRecordItem
            {
                SaleDate = s.po.SaleDate,
                DepositFullPaidDate = s.po.DepositFullPaidDate,
                ContractSignedDate = s.po.ContractSignedDate,
                CustomerName = s.CustomerName,
                UnitDesignation = s.Unit != null && s.Floor != null ? $"{s.Unit.UnitNumber}-{s.Floor.FloorLabel}" : (s.ParkingSpace?.SpaceNumber ?? "N/A"),
                Area = s.Unit?.TotalArea,
                PropertyTransactionPrice = s.poi.UnitId.HasValue ? s.poi.ItemPrice : 0,
                ParkingSpaces = !s.poi.UnitId.HasValue ? s.ParkingSpace?.SpaceNumber : "",
                ParkingTransactionPrice = !s.poi.UnitId.HasValue ? s.poi.ItemPrice : 0,
                RealPriceRegistration = s.po.PriceRegistrationSubmissionDate.HasValue ? "Y" : "N",
                SalespersonName = s.SalespersonName,
                OrderId = s.po.OrderId,
                UnitId = s.Unit?.UnitId ?? 0
            }).ToList();

            var summary = new SalesListSummary
            {
                TotalSalesCount = records.Count(),
                TotalPropertyAmount = records.Sum(r => r.PropertyTransactionPrice ?? 0),
                TotalParkingAmount = records.Sum(r => r.ParkingTransactionPrice ?? 0),
                SoldCount = records.Count(r => r.SaleDate.HasValue),
                DepositFullCount = records.Count(r => r.DepositFullPaidDate.HasValue),
                ContractedCount = records.Count(r => r.ContractSignedDate.HasValue)
            };

            var building = await Db.Buildings.FirstOrDefaultAsync(b => b.BuildingId == input.BuildingId);

            return new SalesControlListOutput
            {
                SiteCode = input.SiteCode,
                BuildingId = input.BuildingId,
                BuildingName = building?.BuildingName,
                Records = records,
                Summary = summary
            };
        }

        public async Task UpdateUnitSalesStatusAsync(int unitId, string status, string updatedByUserId)
        {
            var unit = await Db.Units.FindAsync(unitId);
            if (unit != null)
            {
                unit.Status = status;
                unit.UpdatedUserInfoId = updatedByUserId;
                unit.UpdatedTime = DateTime.UtcNow;
                await Db.SaveChangesAsync();
            }
        }

        public async Task UpdateSalesControlUnitAsync(int unitId, SalesControlUnitUpdateInput input, string updatedByUserId)
        {
            var unit = await Db.Units.FindAsync(unitId);
            if (unit == null)
            {
                throw new Exception($"找不到ID為 {unitId} 的房屋單位。");
            }

            // 更新狀態
            unit.Status = input.Status;

            // 如果有提供其他欄位，也一併更新
            if (input.SalePrice.HasValue)
            {
                unit.TransactionPrice = input.SalePrice;
            }
            if (input.Area.HasValue)
            {
                unit.TotalArea = input.Area.Value;
            }
            if (!string.IsNullOrEmpty(input.UnitType))
            {
                unit.Layout = input.UnitType; // 假設 UnitType 對應到 Layout
            }
            if (!string.IsNullOrEmpty(input.Remarks))
            {
                unit.Remarks = input.Remarks;
            }

            unit.UpdatedTime = DateTime.Now;
            unit.UpdatedUserInfoId = updatedByUserId;

            // 這邊可以根據 input.PurchaseInfo, input.OrderId 等資訊，
            // 觸發更新訂單或其他關聯資料的邏輯，目前暫時只更新單位本身。

            await Db.SaveChangesAsync();
        }

        public async Task<ComprehensiveSalesStatistics> GetSalesStatisticsAsync(string siteCode)
        {
            var unitStats = await Db.Units
                .Where(u => u.SiteCode == siteCode)
                .GroupBy(u => u.Status)
                .Select(g => new { Status = g.Key, Count = g.Count() })
                .ToListAsync();

            var nonSoldStatuses = new[] { "可售", "保留", "業主保留", "地主保留" };

            var unitStatistics = new UnitSalesStatistics
            {
                TotalCount = unitStats.Sum(s => s.Count),
                AvailableCount = unitStats.FirstOrDefault(s => s.Status == "可售")?.Count ?? 0,
                ReservedCount = unitStats.Where(s => s.Status == "保留" || s.Status == "業主保留" || s.Status == "地主保留").Sum(s => s.Count),
                SoldCount = unitStats.Where(s => s.Status != null && !nonSoldStatuses.Contains(s.Status)).Sum(s => s.Count),
            };
            
            var parkingSpaceStats = await Db.ParkingSpaces
                .Where(ps => ps.SiteCode == siteCode)
                .GroupBy(ps => ps.Status)
                .Select(g => new { Status = g.Key, Count = g.Count() })
                .ToListAsync();

            var parkingSpaceStatistics = new ParkingSpaceSalesStatistics
            {
                TotalCount = parkingSpaceStats.Sum(s => s.Count),
                AvailableCount = parkingSpaceStats.FirstOrDefault(s => s.Status == "可售")?.Count ?? 0,
                ReservedCount = parkingSpaceStats.Where(s => s.Status == "保留" || s.Status == "業主保留" || s.Status == "地主保留").Sum(s => s.Count),
                SoldCount = parkingSpaceStats.Where(s => s.Status != null && !nonSoldStatuses.Contains(s.Status)).Sum(s => s.Count),
            };

            return new ComprehensiveSalesStatistics
            {
                SiteCode = siteCode,
                UnitStatistics = unitStatistics,
                ParkingSpaceStatistics = parkingSpaceStatistics,
                GeneratedTime = DateTime.Now
            };
        }
    }
}