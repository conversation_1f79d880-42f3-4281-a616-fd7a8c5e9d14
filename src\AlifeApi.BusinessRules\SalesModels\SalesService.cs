using AlifeApi.BusinessRules.Infrastructure;
using AlifeApi.DataAccess.ProjectContent;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace AlifeApi.BusinessRules.SalesModels
{
    /// <summary>
    /// 銷控表服務
    /// </summary>
    public class SalesService : ServiceBase<alifeContext>
    {
        public SalesService(IServiceProvider serviceProvider, alifeContext dbContext)
            : base(serviceProvider, dbContext)
        {
        }

        /// <summary>
        /// 取得銷控表資料
        /// </summary>
        /// <param name="input">銷控表查詢條件</param>
        /// <returns>銷控表格式的資料</returns>
        public async Task<BuildingSalesData> GetSalesControlAsync(SalesControlInput input)
        {
            ArgumentNullException.ThrowIfNull(input);

            var result = new BuildingSalesData
            {
                SiteCode = input.SiteCode,
                BuildingId = input.BuildingId
            };

            // 查詢樓層 (排除停車場樓層)
            var floorsQuery = Db.Floors.AsQueryable()
                .Where(f => f.SiteCode == input.SiteCode && 
                           f.FloorType != null && 
                           !f.FloorType.Contains("停車"));

            // 如果指定建築物ID，則篩選
            if (input.BuildingId.HasValue)
            {
                floorsQuery = floorsQuery.Where(f => f.BuildingId == input.BuildingId.Value);
                
                // 取得建築物名稱
                var building = await Db.Buildings
                    .Where(b => b.BuildingId == input.BuildingId.Value)
                    .Select(b => b.BuildingName)
                    .FirstOrDefaultAsync();
                result.BuildingName = building;
            }

            // 取得樓層列表，按樓層數值降序排列
            var floors = await floorsQuery
                .OrderByDescending(f => f.FloorLevel)
                .ToListAsync();

            if (!floors.Any())
            {
                return result;
            }

            var floorIds = floors.Select(f => f.FloorId).ToList();

            // 取得所有樓層的房屋單位，包含相關資訊和對應的訂單資訊
            var unitsWithOrderQuery = from unit in Db.Units
                                    join floor in Db.Floors on unit.FloorId equals floor.FloorId
                                    join building in Db.Buildings on unit.BuildingId equals building.BuildingId
                                    where floorIds.Contains(unit.FloorId)
                                    select new
                                    {
                                        Unit = unit,
                                        FloorLabel = floor.FloorLabel,
                                        FloorLevel = floor.FloorLevel,
                                        BuildingName = building.BuildingName,
                                        // 查詢最新有效的訂單（非取消狀態）
                                        LatestOrder = Db.PurchaseOrders
                                            .Where(po => po.UnitId == unit.UnitId && 
                                                        po.Status != "已取消" && po.Status != "已作廢")
                                            .OrderByDescending(po => po.CreatedTime)
                                            .FirstOrDefault()
                                    };

            var unitData = await unitsWithOrderQuery.ToListAsync();

            // 查詢客戶資訊
            var customerIds = unitData
                .Where(u => u.LatestOrder != null)
                .Select(u => u.LatestOrder.CustomerId)
                .Distinct()
                .ToList();

            var customers = new Dictionary<int, string>();
            if (customerIds.Any())
            {
                customers = await Db.Customers
                    .Where(c => customerIds.Contains(c.CustomerId))
                    .ToDictionaryAsync(c => c.CustomerId, c => c.Name ?? c.CustomerId.ToString());
            }

            // 查詢車位資訊
            var allParkingSpaceIds = unitData
                .Where(u => u.LatestOrder != null && !string.IsNullOrEmpty(u.LatestOrder.PurchasedParkingSpaceIds))
                .SelectMany(u => u.LatestOrder.PurchasedParkingSpaceIds!.Split(',', StringSplitOptions.RemoveEmptyEntries)
                    .Select(id => int.TryParse(id.Trim(), out var parkingId) ? parkingId : (int?)null)
                    .Where(id => id.HasValue)
                    .Select(id => id!.Value))
                .Distinct()
                .ToList();

            var parkingSpaces = new Dictionary<int, string>();
            if (allParkingSpaceIds.Any())
            {
                parkingSpaces = await Db.ParkingSpaces
                    .Where(ps => allParkingSpaceIds.Contains(ps.ParkingSpaceId))
                    .ToDictionaryAsync(ps => ps.ParkingSpaceId, ps => ps.SpaceNumber ?? ps.ParkingSpaceId.ToString());
            }

            // 如果沒有指定建築物ID，需要按樓層號合併不同建築物的同樓層資料
            if (!input.BuildingId.HasValue)
            {
                // 按樓層標籤和樓層數值分組，合併同樓層的不同建築物資料
                var floorGroups = floors
                    .GroupBy(f => new { f.FloorLabel, f.FloorLevel })
                    .OrderByDescending(g => g.Key.FloorLevel)
                    .ToList();

                foreach (var floorGroup in floorGroups)
                {
                    var groupFloorIds = floorGroup.Select(f => f.FloorId).ToList();
                    var floorUnits = unitData
                        .Where(u => groupFloorIds.Contains(u.Unit.FloorId))
                        .OrderBy(u => u.Unit.UnitNumber)
                        .ToList();
                    
                    var floorInfo = new FloorInfo
                    {
                        FloorNumber = floorGroup.Key.FloorLabel,
                        FloorId = floorGroup.First().FloorId, // 使用第一個樓層的ID作為代表
                        FloorLevel = floorGroup.Key.FloorLevel,
                        Units = floorUnits.Select(u => new UnitInfo
                        {
                            UnitId = u.Unit.UnitId,
                            UnitNumber = u.Unit.UnitNumber,
                            Status = DetermineUnitStatus(u.Unit, u.LatestOrder),
                            CustomerName = u.LatestOrder != null && customers.TryGetValue(u.LatestOrder.CustomerId, out var custName) 
                                ? custName : string.Empty, // 客戶姓名，沒有時返回空字串
                            SaleDate = u.LatestOrder?.SaleDate, // 售出日期，保持可為 null
                            RequestDate = u.LatestOrder?.RequestDate, // 請款日期
                            ReceiveDate = u.LatestOrder?.ReceiveDate, // 領款日期
                            PriceRegistrationSubmissionDate = null, // 實價登錄提交日期 (暫時設為 null，需要確認資料來源)
                            PurchasedParkingSpaces = GetPurchasedParkingSpacesString(u.LatestOrder, parkingSpaces) ?? string.Empty, // 確保不為 null
                            SalePrice = u.LatestOrder?.TotalPrice ?? u.Unit.TransactionPrice,
                            Area = u.Unit.TotalArea,
                            UnitType = u.Unit.Layout ?? string.Empty, // 確保不為 null
                            FloorLabel = u.FloorLabel,
                            BuildingName = u.BuildingName
                        }).ToList()
                    };

                    // 計算樓層統計
                    CalculateFloorSummary(floorInfo);

                    result.Floors.Add(floorInfo);
                }
            }
            else
            {
                // 指定建築物時，按原來的邏輯處理
                foreach (var floor in floors)
                {
                    var floorUnits = unitData.Where(u => u.Unit.FloorId == floor.FloorId)
                                            .OrderBy(u => u.Unit.UnitNumber)
                                            .ToList();
                    
                    var floorInfo = new FloorInfo
                    {
                        FloorNumber = floor.FloorLabel,
                        FloorId = floor.FloorId,
                        FloorLevel = floor.FloorLevel,
                        Units = floorUnits.Select(u => new UnitInfo
                        {
                            UnitId = u.Unit.UnitId,
                            UnitNumber = u.Unit.UnitNumber,
                            Status = DetermineUnitStatus(u.Unit, u.LatestOrder),
                            CustomerName = u.LatestOrder != null && customers.TryGetValue(u.LatestOrder.CustomerId, out var custName) 
                                ? custName : string.Empty, // 客戶姓名，沒有時返回空字串
                            SaleDate = u.LatestOrder?.SaleDate, // 售出日期，保持可為 null
                            RequestDate = u.LatestOrder?.RequestDate, // 請款日期
                            ReceiveDate = u.LatestOrder?.ReceiveDate, // 領款日期
                            PriceRegistrationSubmissionDate = null, // 實價登錄提交日期 (暫時設為 null，需要確認資料來源)
                            PurchasedParkingSpaces = GetPurchasedParkingSpacesString(u.LatestOrder, parkingSpaces) ?? string.Empty, // 確保不為 null
                            SalePrice = u.LatestOrder?.TotalPrice ?? u.Unit.TransactionPrice,
                            Area = u.Unit.TotalArea,
                            UnitType = u.Unit.Layout ?? string.Empty, // 確保不為 null
                            FloorLabel = u.FloorLabel,
                            BuildingName = u.BuildingName
                        }).ToList()
                    };

                    // 計算樓層統計
                    CalculateFloorSummary(floorInfo);

                    result.Floors.Add(floorInfo);
                }
            }

            // 計算戶別統計 (A戶、B戶等)
            CalculateColumnSummary(result);

            // 計算總體統計
            CalculateTotalSummary(result);

            return result;
        }

        /// <summary>
        /// 根據房屋單位資料和訂單資料，判斷目前的銷售狀態
        /// 業務流程：可售 -> 售(訂金未付足) -> 足(補足訂金) -> 簽(已簽合約)
        /// 請款和領款不再是狀態，而是透過 RequestDate 和 ReceiveDate 欄位記錄
        /// </summary>
        /// <param name="unit">房屋單位資料</param>
        /// <param name="latestOrder">最新有效訂單</param>
        /// <returns>銷售狀態</returns>
        private static string DetermineUnitStatus(Unit unit, PurchaseOrder? latestOrder)
        {
            // 直接使用 Units.Status 欄位的值，這是實際的銷控表狀態
            return unit.Status switch
            {
                "可售" => UnitSalesStatus.Available,
                "保留" or "地主保留" or "業主保留" => UnitSalesStatus.Reserved,
                "售" => UnitSalesStatus.Sold,
                "足" => UnitSalesStatus.DepositFull,
                "簽" => UnitSalesStatus.Contracted,
                // 移除樣品屋和請領狀態，但保留舊資料的相容性
                "樣品屋" or "樣" => UnitSalesStatus.Reserved, // 舊資料轉為保留狀態
                "請" => UnitSalesStatus.Contracted, // 舊資料轉為簽約狀態
                "領" => UnitSalesStatus.Contracted, // 舊資料轉為簽約狀態
                _ => UnitSalesStatus.Available // 預設為可售
            };
        }

        /// <summary>
        /// 計算戶別統計摘要 (A戶、B戶等)
        /// </summary>
        /// <param name="result">銷控表資料</param>
        private static void CalculateColumnSummary(BuildingSalesData result)
        {
            var allUnits = result.Floors.SelectMany(f => f.Units).ToList();
            
            if (!allUnits.Any())
                return;

            var columnGroups = allUnits.GroupBy(u => u.UnitNumber).OrderBy(g => g.Key);

            foreach (var columnGroup in columnGroups)
            {
                var units = columnGroup.ToList();
                
                // 可售：只計算狀態為「可售」的
                var availableCount = units.Count(u => u.Status == UnitSalesStatus.Available);
                
                // 已售：計算所有銷售相關狀態（售、足、簽）
                var soldCount = units.Count(u => 
                    u.Status == UnitSalesStatus.Sold || 
                    u.Status == UnitSalesStatus.DepositFull || 
                    u.Status == UnitSalesStatus.Contracted);
                
                // 保留：計算保留狀態
                var reservedCount = units.Count(u => u.Status == UnitSalesStatus.Reserved);
                    
                var totalCount = units.Count;

                result.ColumnSummary.Add(new ColumnSummary
                {
                    Column = columnGroup.Key,
                    Available = availableCount,
                    Sold = soldCount,
                    Reserved = reservedCount,
                    // 銷售率 = 已售 / (可售 + 已售) * 100%，不包含保留
                    SalesRate = (availableCount + soldCount) > 0 
                        ? $"{Math.Round((decimal)soldCount / (availableCount + soldCount) * 100)}%" 
                        : "0%"
                });
            }
        }

        /// <summary>
        /// 計算總體統計摘要
        /// </summary>
        /// <param name="result">銷控表資料</param>
        private static void CalculateTotalSummary(BuildingSalesData result)
        {
            var totalAvailable = result.ColumnSummary.Sum(c => c.Available);
            var totalSold = result.ColumnSummary.Sum(c => c.Sold);
            var totalReserved = result.ColumnSummary.Sum(c => c.Reserved);

            result.TotalSummary = new TotalSummary
            {
                TotalAvailable = totalAvailable,
                TotalSold = totalSold,
                TotalReserved = totalReserved,
                // 銷售率 = 已售 / (可售 + 已售) * 100%，不包含保留
                OverallSalesRate = (totalAvailable + totalSold) > 0 
                    ? $"{Math.Round((decimal)totalSold / (totalAvailable + totalSold) * 100)}%" 
                    : "0%"
            };
        }

        /// <summary>
        /// 計算樓層統計摘要
        /// </summary>
        /// <param name="floorInfo">樓層資訊</param>
        private static void CalculateFloorSummary(FloorInfo floorInfo)
        {
            var availableCount = floorInfo.Units.Count(u => u.Status == UnitSalesStatus.Available);
            
            // 已售：計算 售、足、簽 狀態
            var soldCount = floorInfo.Units.Count(u => 
                u.Status == UnitSalesStatus.Sold || 
                u.Status == UnitSalesStatus.DepositFull || 
                u.Status == UnitSalesStatus.Contracted);
            
            // 保留：計算保留狀態
            var reservedCount = floorInfo.Units.Count(u => u.Status == UnitSalesStatus.Reserved);

            floorInfo.Summary = new FloorSummary
            {
                Available = availableCount,
                Sold = soldCount,
                Reserved = reservedCount,
                // 銷售率 = 已售 / (可售 + 已售) * 100%，不包含保留
                SalesRate = (availableCount + soldCount) > 0 
                    ? $"{Math.Round((decimal)soldCount / (availableCount + soldCount) * 100)}%" 
                    : "0%"
            };
        }

        /// <summary>
        /// 更新房屋單位的銷售狀態
        /// 此方法用於在創建或更新訂單後，同步更新相關房屋單位的狀態
        /// </summary>
        /// <param name="unitId">房屋單位ID</param>
        /// <param name="newStatus">新的銷售狀態</param>
        /// <param name="userInfoId">執行更新的使用者ID</param>
        /// <returns></returns>
        public async Task UpdateUnitSalesStatusAsync(int unitId, string newStatus, string userInfoId)
        {
            var unit = await Db.Units.FindAsync(unitId);
            if (unit == null)
            {
                throw new InvalidOperationException($"找不到房屋單位 ID: {unitId}");
            }

            var oldStatus = unit.Status;
            unit.Status = newStatus;
            unit.UpdatedUserInfoId = userInfoId;
            unit.UpdatedTime = DateTime.UtcNow;

            // 記錄單位狀態變更歷史
            await LogUnitStatusChangeAsync(unitId, oldStatus, newStatus, userInfoId, $"房屋單位狀態由 '{oldStatus}' 變更為 '{newStatus}'");

            await Db.SaveChangesAsync();
        }

        /// <summary>
        /// 銷控表單位完整狀態更新 (先更新訂單狀態，再更新Units狀態)
        /// </summary>
        /// <param name="unitId">房屋單位ID</param>
        /// <param name="input">銷控表單位更新輸入</param>
        /// <param name="userInfoId">執行更新的使用者ID</param>
        /// <returns></returns>
        public async Task UpdateSalesControlUnitAsync(int unitId, dynamic input, string userInfoId)
        {
            var unit = await Db.Units.FindAsync(unitId);
            if (unit == null)
            {
                throw new InvalidOperationException($"找不到房屋單位 ID: {unitId}");
            }

            // 查找與該房屋單位相關的最新有效訂單
            var latestOrder = await Db.PurchaseOrders
                .Where(po => po.UnitId == unitId && po.Status != "已取消" && po.Status != "已作廢")
                .OrderByDescending(po => po.CreatedTime)
                .FirstOrDefaultAsync();

            var oldUnitStatus = unit.Status;
            var newStatus = input.Status?.ToString() ?? oldUnitStatus;

            using var transaction = await Db.Database.BeginTransactionAsync();
            try
            {
                // 1. 處理訂單相關邏輯
                if (latestOrder != null)
                {
                    // 有現有訂單，更新訂單狀態和業務日期
                    var oldOrderStatus = latestOrder.Status;
                    
                    // 根據銷控表狀態更新訂單的業務流程日期
                    switch (newStatus)
                    {
                        case UnitSalesStatus.Sold: // 售
                            if (!latestOrder.SaleDate.HasValue)
                                latestOrder.SaleDate = DateOnly.FromDateTime(DateTime.Now);
                            latestOrder.Status = "預訂中"; // 訂單狀態
                            break;
                            
                        case UnitSalesStatus.DepositFull: // 足
                            if (!latestOrder.SaleDate.HasValue)
                                latestOrder.SaleDate = DateOnly.FromDateTime(DateTime.Now);
                            if (!latestOrder.DepositFullPaidDate.HasValue)
                                latestOrder.DepositFullPaidDate = DateOnly.FromDateTime(DateTime.Now);
                            latestOrder.Status = "已轉訂"; // 訂單狀態
                            break;
                            
                        case UnitSalesStatus.Contracted: // 簽
                            if (!latestOrder.SaleDate.HasValue)
                                latestOrder.SaleDate = DateOnly.FromDateTime(DateTime.Now);
                            if (!latestOrder.DepositFullPaidDate.HasValue)
                                latestOrder.DepositFullPaidDate = DateOnly.FromDateTime(DateTime.Now);
                            if (!latestOrder.ContractSignedDate.HasValue)
                                latestOrder.ContractSignedDate = DateOnly.FromDateTime(DateTime.Now);
                            latestOrder.Status = "已簽約"; // 訂單狀態
                            break;
                            
                        case UnitSalesStatus.Available: // 可售 - 可能是取消訂單
                            latestOrder.Status = "已取消";
                            latestOrder.CancellationDate = DateOnly.FromDateTime(DateTime.Now);
                            break;
                    }

                    // 處理請款和領款日期更新（這些不再是狀態，而是獨立的日期欄位）
                    if (input.RequestDate != null)
                    {
                        latestOrder.RequestDate = input.RequestDate;
                    }
                    
                    if (input.ReceiveDate != null)
                    {
                        latestOrder.ReceiveDate = input.ReceiveDate;
                    }

                    // 更新售價 (如果有提供)
                    if (input.SalePrice != null)
                    {
                        latestOrder.TotalPrice = input.SalePrice;
                        latestOrder.PropertyPrice = input.SalePrice; // 假設是房地售價
                    }

                    latestOrder.UpdatedTime = DateTime.Now;
                    latestOrder.UpdatedUserInfoId = userInfoId;

                    // 記錄訂單狀態變更歷史
                    if (oldOrderStatus != latestOrder.Status)
                    {
                        await LogPurchaseOrderHistoryAsync(latestOrder.OrderId, "STATUS_CHANGE", 
                            oldOrderStatus, latestOrder.Status, 
                            $"銷控表更新：單位狀態 '{oldUnitStatus}' → '{newStatus}'，訂單狀態 '{oldOrderStatus}' → '{latestOrder.Status}'", 
                            userInfoId);
                    }
                    else
                    {
                        await LogPurchaseOrderHistoryAsync(latestOrder.OrderId, "SALES_CONTROL_UPDATE", 
                            null, null, 
                            $"銷控表更新：單位狀態 '{oldUnitStatus}' → '{newStatus}'", 
                            userInfoId);
                    }
                }
                else if (newStatus != UnitSalesStatus.Available && newStatus != UnitSalesStatus.Reserved)
                {
                    // 沒有現有訂單但狀態是銷售相關 (售、足、簽)，需要提示應該先創建訂單
                    throw new InvalidOperationException($"房屋單位 {unit.UnitNumber} 沒有相關訂單，無法設定為 '{newStatus}' 狀態。請先創建訂單。");
                }

                // 2. 更新Units狀態和相關資訊
                unit.Status = newStatus;
                
                // 更新坪數 (如果有提供)
                if (input.Area != null)
                {
                    unit.TotalArea = input.Area;
                }
                
                // 更新房型 (如果有提供)
                if (!string.IsNullOrEmpty(input.UnitType?.ToString()))
                {
                    unit.Layout = input.UnitType.ToString();
                }
                
                // 更新成交價 (如果有提供)
                if (input.SalePrice != null)
                {
                    unit.TransactionPrice = input.SalePrice;
                }
                
                unit.UpdatedUserInfoId = userInfoId;
                unit.UpdatedTime = DateTime.UtcNow;

                // 記錄單位狀態變更歷史
                await LogUnitStatusChangeAsync(unitId, oldUnitStatus, newStatus, userInfoId, 
                    $"銷控表更新：房屋單位狀態由 '{oldUnitStatus}' 變更為 '{newStatus}'{(input.Remarks != null ? $"，備註：{input.Remarks}" : "")}");

                await Db.SaveChangesAsync();
                await transaction.CommitAsync();
            }
            catch
            {
                await transaction.RollbackAsync();
                throw;
            }
        }

        /// <summary>
        /// 取得案場銷售統計
        /// </summary>
        /// <param name="siteCode">案場編號</param>
        /// <returns>銷售統計資料</returns>
        public async Task<SalesSummaryData> GetSitesSalesStatisticsAsync(string siteCode)
        {
            // 查詢該案場所有房屋單位
            var units = await Db.Units
                .Where(u => u.SiteCode == siteCode)
                .ToListAsync();

            // 查詢該案場所有有效訂單
            var orders = await Db.PurchaseOrders
                .Where(po => po.SiteCode == siteCode && 
                            po.Status != "已取消" && po.Status != "已作廢")
                .ToListAsync();

            var totalUnits = units.Count;
            var availableUnits = 0;
            var soldUnits = 0;
            var reservedUnits = 0;

            // 計算各狀態數量
            foreach (var unit in units)
            {
                var latestOrder = orders
                    .Where(o => o.UnitId == unit.UnitId)
                    .OrderByDescending(o => o.CreatedTime)
                    .FirstOrDefault();

                var status = DetermineUnitStatus(unit, latestOrder);

                switch (status)
                {
                    case UnitSalesStatus.Available:
                        availableUnits++;
                        break;
                    case UnitSalesStatus.Reserved:
                        reservedUnits++;
                        break;
                    case UnitSalesStatus.Sold:
                    case UnitSalesStatus.DepositFull:
                    case UnitSalesStatus.Contracted:
                        soldUnits++;
                        break;
                }
            }

            return new SalesSummaryData
            {
                SiteCode = siteCode,
                TotalUnits = totalUnits,
                AvailableUnits = availableUnits,
                SoldUnits = soldUnits,
                ReservedUnits = reservedUnits,
                SalesRate = (availableUnits + soldUnits) > 0 && soldUnits > 0 
                    ? Math.Round((decimal)soldUnits / (availableUnits + soldUnits) * 100, 2) 
                    : 0
            };
        }

        /// <summary>
        /// 記錄房屋單位狀態變更歷史
        /// </summary>
        /// <param name="unitId">房屋單位ID</param>
        /// <param name="oldStatus">舊狀態</param>
        /// <param name="newStatus">新狀態</param>
        /// <param name="userInfoId">操作使用者ID</param>
        /// <param name="description">變更描述</param>
        /// <returns></returns>
        private async Task LogUnitStatusChangeAsync(int unitId, string oldStatus, string newStatus, string userInfoId, string description)
        {
            // 查找與該房屋單位相關的最新訂單
            var latestOrder = await Db.PurchaseOrders
                .Where(po => po.UnitId == unitId && po.Status != "已取消" && po.Status != "已作廢")
                .OrderByDescending(po => po.CreatedTime)
                .FirstOrDefaultAsync();

            if (latestOrder != null)
            {
                // 如果有相關訂單，記錄在PurchaseOrdersHistory中
                await LogPurchaseOrderHistoryAsync(latestOrder.OrderId, "UNIT_STATUS_CHANGE", 
                    oldStatus, newStatus, description, userInfoId);
            }
            
            // 備選方案：記錄在系統日誌中或創建獨立的Units歷史記錄表
            // 目前先使用控制台輸出作為記錄
            Console.WriteLine($"[{DateTime.UtcNow:yyyy-MM-dd HH:mm:ss}] 房屋單位狀態變更 - 單位ID: {unitId}, {description}, 操作者: {userInfoId}");
        }

        /// <summary>
        /// 記錄訂單歷史 (用於SalesService中的訂單相關歷史記錄)
        /// </summary>
        /// <param name="orderId">訂單ID</param>
        /// <param name="actionType">操作類型</param>
        /// <param name="oldStatus">舊狀態</param>
        /// <param name="newStatus">新狀態</param>
        /// <param name="contentRecord">記錄內容</param>
        /// <param name="userInfoId">操作使用者ID</param>
        /// <returns></returns>
        private async Task LogPurchaseOrderHistoryAsync(int orderId, string actionType, string? oldStatus, string? newStatus, string contentRecord, string userInfoId)
        {
            var history = new PurchaseOrdersHistory
            {
                OrderId = orderId,
                ActionType = actionType,
                OldStatus = oldStatus,
                NewStatus = newStatus,
                ContentRecord = contentRecord,
                CreatedUserInfoId = userInfoId,
                CreatedTime = DateTime.UtcNow
            };

            Db.PurchaseOrdersHistories.Add(history);
            // 注意：不在這裡SaveChanges，讓調用者決定何時保存
        }

        /// <summary>
        /// 輔助方法：將車位資訊轉換為字符串格式
        /// </summary>
        /// <param name="latestOrder">最新有效訂單</param>
        /// <param name="parkingSpaces">車位資訊字典</param>
        /// <returns>車位資訊字符串</returns>
        private string GetPurchasedParkingSpacesString(PurchaseOrder? latestOrder, Dictionary<int, string> parkingSpaces)
        {
            if (latestOrder == null || string.IsNullOrEmpty(latestOrder.PurchasedParkingSpaceIds))
                return string.Empty;

            var parkingIds = latestOrder.PurchasedParkingSpaceIds.Split(',', StringSplitOptions.RemoveEmptyEntries)
                .Select(id => int.TryParse(id.Trim(), out var parkingId) ? parkingId : (int?)null)
                .Where(id => id.HasValue)
                .Select(id => id!.Value);

            var parkingSpaceStrings = parkingIds.Select(id => parkingSpaces.TryGetValue(id, out var spaceNumber) ? spaceNumber : id.ToString());
            return string.Join(", ", parkingSpaceStrings);
        }

        /// <summary>
        /// 取得銷控表列表格式資料（只返回有訂單的資料）
        /// </summary>
        /// <param name="input">查詢條件</param>
        /// <returns>銷控表列表格式資料</returns>
        public async Task<SalesControlListOutput> GetSalesControlListAsync(SalesControlListInput input)
        {
            ArgumentNullException.ThrowIfNull(input);

            var result = new SalesControlListOutput
            {
                SiteCode = input.SiteCode,
                BuildingId = input.BuildingId
            };

            // 取得建築物名稱（如果指定了建築物ID）
            if (input.BuildingId.HasValue)
            {
                var building = await Db.Buildings
                    .Where(b => b.BuildingId == input.BuildingId.Value)
                    .Select(b => b.BuildingName)
                    .FirstOrDefaultAsync();
                result.BuildingName = building;
            }

            // 查詢有效的訂單（非取消、非作廢狀態）
            var ordersQuery = Db.PurchaseOrders.AsQueryable()
                .Where(po => po.SiteCode == input.SiteCode && 
                            po.Status != "已取消" && po.Status != "已作廢");

            // 日期篩選
            if (input.StartDate.HasValue)
            {
                ordersQuery = ordersQuery.Where(po => po.SaleDate >= input.StartDate.Value);
            }

            if (input.EndDate.HasValue)
            {
                ordersQuery = ordersQuery.Where(po => po.SaleDate <= input.EndDate.Value);
            }

            // 建築物篩選
            if (input.BuildingId.HasValue)
            {
                ordersQuery = ordersQuery.Where(po => po.UnitId.HasValue && 
                    Db.Units.Any(u => u.UnitId == po.UnitId.Value && u.BuildingId == input.BuildingId.Value));
            }

            // 執行查詢，包含相關聯的資料
            var ordersWithDetails = await (
                from order in ordersQuery
                join unit in Db.Units on order.UnitId equals unit.UnitId into unitJoin
                from unit in unitJoin.DefaultIfEmpty()
                join floor in Db.Floors on unit.FloorId equals floor.FloorId into floorJoin
                from floor in floorJoin.DefaultIfEmpty()
                join building in Db.Buildings on unit.BuildingId equals building.BuildingId into buildingJoin
                from building in buildingJoin.DefaultIfEmpty()
                join customer in Db.Customers on order.CustomerId equals customer.CustomerId into customerJoin
                from customer in customerJoin.DefaultIfEmpty()
                join salesperson in Db.UserInfos on order.SalespersonUserInfoId equals salesperson.UserInfoId into salespersonJoin
                from salesperson in salespersonJoin.DefaultIfEmpty()
                select new
                {
                    Order = order,
                    Unit = unit,
                    Floor = floor,
                    Building = building,
                    Customer = customer,
                    Salesperson = salesperson
                }
            ).ToListAsync();

            // 取得所有相關的車位資訊
            var allParkingSpaceIds = ordersWithDetails
                .Where(item => !string.IsNullOrEmpty(item.Order.PurchasedParkingSpaceIds))
                .SelectMany(item => item.Order.PurchasedParkingSpaceIds!.Split(',', StringSplitOptions.RemoveEmptyEntries)
                    .Select(id => int.TryParse(id.Trim(), out var parkingId) ? parkingId : (int?)null)
                    .Where(id => id.HasValue)
                    .Select(id => id!.Value))
                .Distinct()
                .ToList();

            // 查詢車位基本資訊
            var parkingSpaces = new Dictionary<int, string>();
            if (allParkingSpaceIds.Any())
            {
                parkingSpaces = await Db.ParkingSpaces
                    .Where(ps => allParkingSpaceIds.Contains(ps.ParkingSpaceId))
                    .ToDictionaryAsync(ps => ps.ParkingSpaceId, ps => ps.SpaceNumber ?? ps.ParkingSpaceId.ToString());
            }

            // 查詢車位單位資訊（用於取得車位成交價）
            var parkingUnits = new Dictionary<int, decimal?>();
            if (allParkingSpaceIds.Any())
            {
                // 從Units表中查詢車位類型的單位，其AssociatedParkingSpaceIds包含我們要的車位ID
                var parkingUnitsList = await Db.Units
                    .Where(u => u.UnitType == "車位" && !string.IsNullOrEmpty(u.AssociatedParkingSpaceIds))
                    .ToListAsync();

                foreach (var parkingUnit in parkingUnitsList)
                {
                    if (string.IsNullOrEmpty(parkingUnit.AssociatedParkingSpaceIds))
                        continue;

                    var associatedIds = parkingUnit.AssociatedParkingSpaceIds.Split(',', StringSplitOptions.RemoveEmptyEntries)
                        .Select(id => int.TryParse(id.Trim(), out var parkingId) ? parkingId : (int?)null)
                        .Where(id => id.HasValue)
                        .Select(id => id!.Value)
                        .ToList();

                    foreach (var parkingId in associatedIds.Where(id => allParkingSpaceIds.Contains(id)))
                    {
                        if (!parkingUnits.ContainsKey(parkingId))
                        {
                            parkingUnits[parkingId] = parkingUnit.TransactionPrice;
                        }
                    }
                }
            }

            // 轉換為輸出格式
            foreach (var item in ordersWithDetails)
            {
                // 計算車位成交價總額
                var parkingTransactionPrice = CalculateParkingTransactionPrice(item.Order, parkingUnits);

                // 建立戶別格式（建築物名稱-樓層標籤）
                var unitDesignation = string.Empty;
                if (item.Building != null && item.Floor != null && item.Unit != null)
                {
                    unitDesignation = $"{item.Building.BuildingName}-{item.Floor.FloorLabel}";
                }

                var record = new SalesRecordItem
                {
                    OrderId = item.Order.OrderId,
                    UnitId = item.Unit?.UnitId ?? 0,
                    SaleDate = item.Order.SaleDate,
                    DepositFullPaidDate = item.Order.DepositFullPaidDate,
                    ContractSignedDate = item.Order.ContractSignedDate,
                    CustomerName = item.Customer?.Name ?? string.Empty,
                    UnitDesignation = unitDesignation,
                    Area = item.Unit?.TotalArea,
                    PropertyTransactionPrice = item.Unit?.TransactionPrice,
                    ParkingSpaces = GetPurchasedParkingSpacesString(item.Order, parkingSpaces),
                    ParkingTransactionPrice = parkingTransactionPrice,
                    RealPriceRegistration = string.Empty, // 這個欄位需要確認資料來源
                    SalespersonName = item.Salesperson?.Name ?? string.Empty
                };

                result.Records.Add(record);
            }

            // 計算統計摘要
            CalculateListSummary(result);

            // 按售出日期降序排列
            result.Records = result.Records.OrderByDescending(r => r.SaleDate).ToList();

            return result;
        }

        /// <summary>
        /// 計算車位成交價總額
        /// </summary>
        /// <param name="order">訂單資料</param>
        /// <param name="parkingUnits">車位單位價格字典</param>
        /// <returns>車位成交價總額</returns>
        private decimal? CalculateParkingTransactionPrice(PurchaseOrder order, Dictionary<int, decimal?> parkingUnits)
        {
            if (string.IsNullOrEmpty(order.PurchasedParkingSpaceIds))
                return null;

            var parkingIds = order.PurchasedParkingSpaceIds.Split(',', StringSplitOptions.RemoveEmptyEntries)
                .Select(id => int.TryParse(id.Trim(), out var parkingId) ? parkingId : (int?)null)
                .Where(id => id.HasValue)
                .Select(id => id!.Value)
                .ToList();

            if (!parkingIds.Any())
                return null;

            decimal totalPrice = 0;
            bool hasAnyPrice = false;

            foreach (var parkingId in parkingIds)
            {
                if (parkingUnits.TryGetValue(parkingId, out var price) && price.HasValue)
                {
                    totalPrice += price.Value;
                    hasAnyPrice = true;
                }
            }

            // 如果沒有從Units取得車位價格，嘗試使用訂單中的車位售價
            if (!hasAnyPrice && order.ParkingSpacePrice.HasValue)
            {
                return order.ParkingSpacePrice.Value;
            }

            return hasAnyPrice ? totalPrice : null;
        }

        /// <summary>
        /// 計算列表統計摘要
        /// </summary>
        /// <param name="result">銷控表列表輸出結果</param>
        private void CalculateListSummary(SalesControlListOutput result)
        {
            var records = result.Records;

            result.Summary.TotalSalesCount = records.Count;
            result.Summary.TotalPropertyAmount = records.Sum(r => r.PropertyTransactionPrice ?? 0);
            result.Summary.TotalParkingAmount = records.Sum(r => r.ParkingTransactionPrice ?? 0);

            // 統計各階段筆數
            result.Summary.SoldCount = records.Count(r => r.SaleDate.HasValue);
            result.Summary.DepositFullCount = records.Count(r => r.DepositFullPaidDate.HasValue);
            result.Summary.ContractedCount = records.Count(r => r.ContractSignedDate.HasValue);
        }
    }
} 
 