#### [AlifeApi.BusinessRules](index.md 'index')

## AlifeApi.BusinessRules.CrmOptionModels Namespace

| Classes | |
| :--- | :--- |
| [CrmOptionCreateInput](AlifeApi.BusinessRules.CrmOptionModels.CrmOptionCreateInput.md 'AlifeApi.BusinessRules.CrmOptionModels.CrmOptionCreateInput') | CRM選項建立輸入模型 |
| [CrmOptionDropdownInput](AlifeApi.BusinessRules.CrmOptionModels.CrmOptionDropdownInput.md 'AlifeApi.BusinessRules.CrmOptionModels.CrmOptionDropdownInput') | CRM選項下拉選單查詢輸入模型 |
| [CrmOptionDropdownOutput](AlifeApi.BusinessRules.CrmOptionModels.CrmOptionDropdownOutput.md 'AlifeApi.BusinessRules.CrmOptionModels.CrmOptionDropdownOutput') | CRM選項下拉選單輸出模型 |
| [CrmOptionListOutput](AlifeApi.BusinessRules.CrmOptionModels.CrmOptionListOutput.md 'AlifeApi.BusinessRules.CrmOptionModels.CrmOptionListOutput') | CRM選項列表輸出模型 |
| [CrmOptionOutput](AlifeApi.BusinessRules.CrmOptionModels.CrmOptionOutput.md 'AlifeApi.BusinessRules.CrmOptionModels.CrmOptionOutput') | CRM選項詳細輸出模型 |
| [CrmOptionQueryInput](AlifeApi.BusinessRules.CrmOptionModels.CrmOptionQueryInput.md 'AlifeApi.BusinessRules.CrmOptionModels.CrmOptionQueryInput') | CRM選項查詢輸入模型 |
| [CrmOptionService](AlifeApi.BusinessRules.CrmOptionModels.CrmOptionService.md 'AlifeApi.BusinessRules.CrmOptionModels.CrmOptionService') | CRM選項服務 |
| [CrmOptionTypeDropdownOutput](AlifeApi.BusinessRules.CrmOptionModels.CrmOptionTypeDropdownOutput.md 'AlifeApi.BusinessRules.CrmOptionModels.CrmOptionTypeDropdownOutput') | CRM選項類型下拉選單輸出模型 |
| [CrmOptionUpdateInput](AlifeApi.BusinessRules.CrmOptionModels.CrmOptionUpdateInput.md 'AlifeApi.BusinessRules.CrmOptionModels.CrmOptionUpdateInput') | CRM選項更新輸入模型 |
