#### [AlifeApi.BusinessRules](index.md 'index')
### [AlifeApi.BusinessRules.WeeklyReportModels](AlifeApi.BusinessRules.WeeklyReportModels.md 'AlifeApi.BusinessRules.WeeklyReportModels')

## WeeklyStatisticItem Class

週統計項目

```csharp
public class WeeklyStatisticItem
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; WeeklyStatisticItem
### Properties

<a name='AlifeApi.BusinessRules.WeeklyReportModels.WeeklyStatisticItem.CurrentWeekTotal'></a>

## WeeklyStatisticItem.CurrentWeekTotal Property

本週總計

```csharp
public int CurrentWeekTotal { get; set; }
```

#### Property Value
[System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')

<a name='AlifeApi.BusinessRules.WeeklyReportModels.WeeklyStatisticItem.DailyDetails'></a>

## WeeklyStatisticItem.DailyDetails Property

每日明細

```csharp
public System.Collections.Generic.List<AlifeApi.BusinessRules.WeeklyReportModels.DailyStatistic> DailyDetails { get; set; }
```

#### Property Value
[System.Collections.Generic.List&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Collections.Generic.List-1 'System.Collections.Generic.List`1')[DailyStatistic](AlifeApi.BusinessRules.WeeklyReportModels.DailyStatistic.md 'AlifeApi.BusinessRules.WeeklyReportModels.DailyStatistic')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Collections.Generic.List-1 'System.Collections.Generic.List`1')

<a name='AlifeApi.BusinessRules.WeeklyReportModels.WeeklyStatisticItem.PreviousPeriodTotal'></a>

## WeeklyStatisticItem.PreviousPeriodTotal Property

前期累計 (本週之前的總計)

```csharp
public int PreviousPeriodTotal { get; set; }
```

#### Property Value
[System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')

<a name='AlifeApi.BusinessRules.WeeklyReportModels.WeeklyStatisticItem.Total'></a>

## WeeklyStatisticItem.Total Property

總計 (前期累計 + 本週總計)

```csharp
public int Total { get; set; }
```

#### Property Value
[System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')