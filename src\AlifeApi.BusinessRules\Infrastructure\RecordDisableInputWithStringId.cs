﻿using System.ComponentModel.DataAnnotations;

namespace AlifeApi.BusinessRules.Infrastructure
{
    /// <summary>
    /// 停用/啟用資料的參數
    /// </summary>
    public class RecordDisableInputWithStringId
    {
        /// <summary>
        /// Gets or sets the identifier.
        /// </summary>
        [Required]
        public string Id { get; set; }

        /// <summary>
        /// Gets or sets the is disabled.
        /// </summary>
        [Required]
        public bool? IsDisabled { get; set; }
    }
}
