#### [AlifeApi.DataAccess](index.md 'index')
### [AlifeApi.DataAccess.ProjectContent](AlifeApi.DataAccess.ProjectContent.md 'AlifeApi.DataAccess.ProjectContent')

## CrmOptionType Class

CRM中可由案場自訂的下拉選單類型

```csharp
public class CrmOptionType
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; CrmOptionType
### Properties

<a name='AlifeApi.DataAccess.ProjectContent.CrmOptionType.CreatedUserInfoId'></a>

## CrmOptionType.CreatedUserInfoId Property

建立者，記錄創建此資料的員工編號。

```csharp
public string CreatedUserInfoId { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContent.CrmOptionType.CreateTime'></a>

## CrmOptionType.CreateTime Property

創建時間，記錄資料創建的時間。

```csharp
public System.DateTime CreateTime { get; set; }
```

#### Property Value
[System.DateTime](https://docs.microsoft.com/en-us/dotnet/api/System.DateTime 'System.DateTime')

<a name='AlifeApi.DataAccess.ProjectContent.CrmOptionType.CrmOptionTypeId'></a>

## CrmOptionType.CrmOptionTypeId Property

主鍵，選項類型唯一識別碼

```csharp
public long CrmOptionTypeId { get; set; }
```

#### Property Value
[System.Int64](https://docs.microsoft.com/en-us/dotnet/api/System.Int64 'System.Int64')

<a name='AlifeApi.DataAccess.ProjectContent.CrmOptionType.Description'></a>

## CrmOptionType.Description Property

此選項類型的描述，方便後台管理人員理解

```csharp
public string Description { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContent.CrmOptionType.TypeName'></a>

## CrmOptionType.TypeName Property

選項類型名稱 (例如: 需求坪數, 需求格局, 預算範圍)

```csharp
public string TypeName { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContent.CrmOptionType.UpdatedUserInfoId'></a>

## CrmOptionType.UpdatedUserInfoId Property

更新者，記錄最後更新此資料的員工編號。

```csharp
public string UpdatedUserInfoId { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContent.CrmOptionType.UpdateTime'></a>

## CrmOptionType.UpdateTime Property

更新時間，記錄資料最後更新的時間。

```csharp
public System.DateTime UpdateTime { get; set; }
```

#### Property Value
[System.DateTime](https://docs.microsoft.com/en-us/dotnet/api/System.DateTime 'System.DateTime')