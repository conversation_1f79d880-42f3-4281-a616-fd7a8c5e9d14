#### [AlifeApi.DataAccess](index.md 'index')
### [AlifeApi.DataAccess.ProjectContent](AlifeApi.DataAccess.ProjectContent.md 'AlifeApi.DataAccess.ProjectContent')

## Floor Class

儲存建築物內每個樓層的資訊，包含住宅/商業樓層和停車場樓層。

```csharp
public class Floor
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; Floor
### Properties

<a name='AlifeApi.DataAccess.ProjectContent.Floor.BuildingId'></a>

## Floor.BuildingId Property

所屬建築物識別碼 (參考 Buildings 表)。

```csharp
public int BuildingId { get; set; }
```

#### Property Value
[System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')

<a name='AlifeApi.DataAccess.ProjectContent.Floor.CreatedTime'></a>

## Floor.CreatedTime Property

資料創建時間 (TIMESTAMP WITHOUT TIME ZONE)。

```csharp
public System.DateTime CreatedTime { get; set; }
```

#### Property Value
[System.DateTime](https://docs.microsoft.com/en-us/dotnet/api/System.DateTime 'System.DateTime')

<a name='AlifeApi.DataAccess.ProjectContent.Floor.CreatedUserInfoId'></a>

## Floor.CreatedUserInfoId Property

建立者 (參考 UserInfo 表)。

```csharp
public string CreatedUserInfoId { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContent.Floor.FloorHeight'></a>

## Floor.FloorHeight Property

樓層高度 (米)。

```csharp
public System.Nullable<decimal> FloorHeight { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.Decimal](https://docs.microsoft.com/en-us/dotnet/api/System.Decimal 'System.Decimal')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

<a name='AlifeApi.DataAccess.ProjectContent.Floor.FloorId'></a>

## Floor.FloorId Property

樓層唯一識別碼 (主鍵, 自動遞增)。

```csharp
public int FloorId { get; set; }
```

#### Property Value
[System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')

<a name='AlifeApi.DataAccess.ProjectContent.Floor.FloorLabel'></a>

## Floor.FloorLabel Property

樓層的顯示標示 (例如: "15F", "1F", "店面", "P1F", "B1")。

```csharp
public string FloorLabel { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContent.Floor.FloorLevel'></a>

## Floor.FloorLevel Property

樓層的數值，用於排序 (例如: 15, 1, 0, -1, -2)。

```csharp
public int FloorLevel { get; set; }
```

#### Property Value
[System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')

<a name='AlifeApi.DataAccess.ProjectContent.Floor.FloorType'></a>

## Floor.FloorType Property

樓層主要用途，**用於區分住宅/商業樓層與停車場樓層** (例如: "住宅", "商業", "停車場" - 建議關聯 SYS_Code)。

```csharp
public string FloorType { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContent.Floor.Remarks'></a>

## Floor.Remarks Property

備註。

```csharp
public string Remarks { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContent.Floor.SiteCode'></a>

## Floor.SiteCode Property

所屬案場編號 (參考 Sites 表) - 方便查詢用。

```csharp
public string SiteCode { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContent.Floor.UpdatedTime'></a>

## Floor.UpdatedTime Property

資料最後更新時間 (TIMESTAMP WITHOUT TIME ZONE)。

```csharp
public System.DateTime UpdatedTime { get; set; }
```

#### Property Value
[System.DateTime](https://docs.microsoft.com/en-us/dotnet/api/System.DateTime 'System.DateTime')

<a name='AlifeApi.DataAccess.ProjectContent.Floor.UpdatedUserInfoId'></a>

## Floor.UpdatedUserInfoId Property

更新者 (參考 UserInfo 表)。

```csharp
public string UpdatedUserInfoId { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')