using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using AlifeApi.BusinessRules.Infrastructure;
using AlifeApi.DataAccess.ProjectContent;
using Microsoft.EntityFrameworkCore;

namespace AlifeApi.BusinessRules.CategoryModels
{
    /// <summary>
    /// 大分類服務
    /// </summary>
    public class LargeCategoryService : ServiceBase<alifeContext>
    {
        public LargeCategoryService(IServiceProvider serviceProvider, alifeContext dbContext)
            : base(serviceProvider, dbContext)
        {
        }

        /// <summary>
        /// 建立大分類
        /// </summary>
        /// <param name="input">大分類建立輸入資料</param>
        /// <returns>新建大分類的ID</returns>
        public async Task<long> CreateLargeCategoryAsync(LargeCategoryInput input)
        {
            ArgumentNullException.ThrowIfNull(input);

            // 檢查名稱是否重複
            if (await Db.LargeCategories.AnyAsync(c => c.Name == input.Name))
            {
                throw new Exception($"大分類名稱 '{input.Name}' 已存在。");
            }

            var largeCategory = new LargeCategory
            {
                Name = input.Name,
                Description = input.Description,
                SortOrder = input.SortOrder,
                IsActive = input.IsActive,
                CreateTime = DateTime.Now,
                UpdateTime = DateTime.Now,
                CreatedUserInfoId = CurrentUser.UserId,
                UpdatedUserInfoId = CurrentUser.UserId
            };

            Db.LargeCategories.Add(largeCategory);
            await Db.SaveChangesAsync();

            return largeCategory.LargeCategoryId;
        }

        /// <summary>
        /// 取得大分類列表 (分頁)
        /// </summary>
        /// <param name="input">查詢條件</param>
        /// <returns>分頁的大分類列表</returns>
        public async Task<PagedListOutput<LargeCategoryListOutput>> GetLargeCategoryListAsync(LargeCategoryQueryInput input)
        {
            ArgumentNullException.ThrowIfNull(input);

            var query = Db.LargeCategories.AsQueryable();

            // 篩選名稱 (模糊查詢)
            if (!string.IsNullOrEmpty(input.Name))
            {
                query = query.Where(c => c.Name.Contains(input.Name));
            }

            // 篩選是否啟用
            if (input.IsActive.HasValue)
            {
                query = query.Where(c => c.IsActive == input.IsActive.Value);
            }

            var pagedResult = await query
                .OrderBy(c => c.SortOrder)
                .ThenBy(c => c.CreateTime)
                .Select(c => new LargeCategoryListOutput
                {
                    LargeCategoryId = c.LargeCategoryId,
                    Name = c.Name,
                    Description = c.Description,
                    SortOrder = c.SortOrder,
                    IsActive = c.IsActive,
                    CreateTime = c.CreateTime
                })
                .ToPagedListOutputAsync(input);

            return pagedResult;
        }

        /// <summary>
        /// 根據ID取得大分類詳細資料
        /// </summary>
        /// <param name="largeCategoryId">大分類ID</param>
        /// <returns>大分類詳細資料</returns>
        public async Task<LargeCategoryOutput?> GetLargeCategoryByIdAsync(long largeCategoryId)
        {
            var largeCategory = await Db.LargeCategories
                .Where(c => c.LargeCategoryId == largeCategoryId)
                .Select(c => new LargeCategoryOutput
                {
                    LargeCategoryId = c.LargeCategoryId,
                    Name = c.Name,
                    Description = c.Description,
                    SortOrder = c.SortOrder,
                    IsActive = c.IsActive,
                    CreateTime = c.CreateTime,
                    CreatedUserInfoId = c.CreatedUserInfoId,
                    UpdateTime = c.UpdateTime,
                    UpdatedUserInfoId = c.UpdatedUserInfoId
                })
                .FirstOrDefaultAsync();

            if (largeCategory == null)
            {
                return null;
            }

            // 查詢並填入建立者和更新者名稱
            var userIds = new[] { largeCategory.CreatedUserInfoId, largeCategory.UpdatedUserInfoId }.Distinct().ToList();
            var users = await Db.UserInfos
                .Where(u => userIds.Contains(u.UserInfoId))
                .Select(u => new { u.UserInfoId, u.Name })
                .ToListAsync();
            var userMap = users.ToDictionary(u => u.UserInfoId, u => u.Name);

            if (userMap.TryGetValue(largeCategory.CreatedUserInfoId, out var createdName))
            {
                largeCategory.CreatedUserName = createdName;
            }
            if (userMap.TryGetValue(largeCategory.UpdatedUserInfoId, out var updatedName))
            {
                largeCategory.UpdatedUserName = updatedName;
            }

            return largeCategory;
        }

        /// <summary>
        /// 更新大分類資訊
        /// </summary>
        /// <param name="largeCategoryId">大分類ID</param>
        /// <param name="input">大分類更新輸入資料</param>
        public async Task UpdateLargeCategoryAsync(long largeCategoryId, LargeCategoryInput input)
        {
            ArgumentNullException.ThrowIfNull(input);

            var largeCategory = await Db.LargeCategories.FindAsync(largeCategoryId);

            if (largeCategory == null)
            {
                throw new Exception($"找不到指定的大分類 (ID: {largeCategoryId})");
            }

            // 檢查名稱是否重複 (排除自己)
            if (await Db.LargeCategories.AnyAsync(c => c.Name == input.Name && c.LargeCategoryId != largeCategoryId))
            {
                throw new Exception($"大分類名稱 '{input.Name}' 已存在。");
            }

            largeCategory.Name = input.Name;
            largeCategory.Description = input.Description;
            largeCategory.SortOrder = input.SortOrder;
            largeCategory.IsActive = input.IsActive;
            largeCategory.UpdateTime = DateTime.Now;
            largeCategory.UpdatedUserInfoId = CurrentUser.UserId;

            await Db.SaveChangesAsync();
        }

        /// <summary>
        /// 刪除大分類
        /// </summary>
        /// <param name="largeCategoryId">大分類ID</param>
        public async Task DeleteLargeCategoryAsync(long largeCategoryId)
        {
            var largeCategory = await Db.LargeCategories.FindAsync(largeCategoryId);

            if (largeCategory == null)
            {
                throw new Exception($"找不到指定的大分類 (ID: {largeCategoryId})");
            }

            // 檢查是否有關聯的中分類
            var hasMediumCategories = await Db.MediumCategories.AnyAsync(m => m.LargeCategoryId == largeCategoryId);
            if (hasMediumCategories)
            {
                throw new Exception("無法刪除大分類，因為存在關聯的中分類。");
            }

            Db.LargeCategories.Remove(largeCategory);
            await Db.SaveChangesAsync();
        }

        /// <summary>
        /// 取得大分類下拉選單列表
        /// </summary>
        /// <returns>啟用的大分類下拉選單列表</returns>
        public async Task<List<LargeCategoryDropdownOutput>> GetLargeCategoryDropdownAsync()
        {
            var result = await Db.LargeCategories
                .Where(c => c.IsActive == true)
                .OrderBy(c => c.SortOrder)
                .ThenBy(c => c.CreateTime)
                .Select(c => new LargeCategoryDropdownOutput
                {
                    LargeCategoryId = c.LargeCategoryId,
                    Name = c.Name,
                    SortOrder = c.SortOrder
                })
                .ToListAsync();

            return result;
        }
    }
} 
