﻿using System;
using System.Collections.Generic;

namespace AlifeApi.DataAccess.ProjectContext
{
    public partial class SysProjectVersion
    {
        public int PId { get; set; }

        /// <summary>
        /// 專案代號
        /// </summary>
        public string PProjectName { get; set; }

        /// <summary>
        /// git唯一值
        /// </summary>
        public string PVersionHash { get; set; }

        /// <summary>
        /// 版號
        /// </summary>
        public string PVersionName { get; set; }

        /// <summary>
        /// 版更時間
        /// </summary>
        public DateTime? PCrDateTime { get; set; }

        /// <summary>
        /// 上版人員
        /// </summary>
        public string PCrUser { get; set; }

        /// <summary>
        /// 更新內容
        /// </summary>
        public string PContent { get; set; }

    }
}
