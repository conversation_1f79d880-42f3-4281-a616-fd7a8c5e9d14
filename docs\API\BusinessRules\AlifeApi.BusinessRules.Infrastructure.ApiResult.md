#### [AlifeApi.BusinessRules](index.md 'index')
### [AlifeApi.BusinessRules.Infrastructure](AlifeApi.BusinessRules.Infrastructure.md 'AlifeApi.BusinessRules.Infrastructure')

## ApiResult Class

API 回傳的通用結果

```csharp
public class ApiResult : AlifeApi.BusinessRules.Infrastructure.ApiResult<object>
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; [AlifeApi.BusinessRules.Infrastructure.ApiResult&lt;](AlifeApi.BusinessRules.Infrastructure.ApiResult_TBody_.md 'AlifeApi.BusinessRules.Infrastructure.ApiResult<TBody>')[System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object')[&gt;](AlifeApi.BusinessRules.Infrastructure.ApiResult_TBody_.md 'AlifeApi.BusinessRules.Infrastructure.ApiResult<TBody>') &#129106; ApiResult