﻿using System.Linq.Expressions;
using System.Reflection;
using System.Text.Json;
using AlifeApi.BusinessRules.Infrastructure;
using Microsoft.EntityFrameworkCore;

namespace System.Linq
{
    /// <summary>
    /// IQueryable 的擴充方法
    /// </summary>
    internal static class QueryableExtensions
    {
        /// <summary>
        /// Wheres if.
        /// </summary>
        /// <typeparam name="TEntity">The type of the entity.</typeparam>
        /// <param name="source">The source.</param>
        /// <param name="condition">if set to <c>true</c> [condition].</param>
        /// <param name="predicate">The predicate.</param>
        /// <returns></returns>
        public static IQueryable<TEntity> WhereIf<TEntity>(this IQueryable<TEntity> source, bool condition, Expression<Func<TEntity, bool>> predicate)
        {
            return condition
                ? source.Where(predicate) : source;
        }

        /// <summary>
        /// Wheres if.
        /// </summary>
        /// <typeparam name="TEntity">The type of the entity.</typeparam>
        /// <param name="source">The source.</param>
        /// <param name="condition">if set to <c>true</c> [condition].</param>
        /// <param name="predicateGenerator">The predicate generator.</param>
        /// <returns></returns>
        public static IQueryable<TEntity> WhereIf<TEntity>(this IQueryable<TEntity> source, bool condition, Func<Expression<Func<TEntity, bool>>> predicateGenerator)
        {
            if (condition)
            {
                source = source.Where(predicateGenerator());
            }
            return source;
        }

        /// <summary>
        /// 將 IQueryable 來源根據提供的輸入參數轉換為分頁列表
        /// </summary>
        /// <typeparam name="TDetail">IQueryable 來源中的元素類型</typeparam>
        /// <param name="source">要進行分頁和篩選的 IQueryable 來源</param>
        /// <param name="input">分頁和篩選的輸入參數</param>
        /// <returns>包含分頁列表輸出結果</returns>
        public static async Task<PagedListOutput<TDetail>> ToPagedListOutputAsync<TDetail>(
            this IQueryable<TDetail> source, PagedListInput input)
        {
            source = ApplySearchFilters(source, input);

            source = ApplySortOrders(source, input.SortOrderInfos);

            return await CreatePagedListOutputAsync(source, input);
        }

        /// <summary>
        /// 將 IQueryable 來源根據提供的輸入參數轉換為分頁列表
        /// </summary>
        /// <typeparam name="TDetail">IQueryable 來源中的元素類型</typeparam>
        /// <param name="source">要進行分頁和篩選的 IQueryable 來源</param>
        /// <param name="input">分頁和篩選的輸入參數</param>
        /// <returns>包含分頁列表輸出結果</returns>
        public static PagedListOutput<TDetail> ToPagedListOutput<TDetail>(
            this IEnumerable<TDetail> source, PagedListInput input)
        {
            foreach (SearchTermInfo searchInfo in input.SearchTermInfos
                .Where(searchInfo => !string.IsNullOrEmpty(searchInfo.SearchField)
                                    && searchInfo.SearchValue is not null))
            {
                source = source.Where(ApplySearchFilter<TDetail>(searchInfo).Compile());
            }

            source = ApplySortOrders(source, input.SortOrderInfos);

            return CreatePagedListOutput(source, input);
        }

        private static IQueryable<TDetail> ApplySearchFilters<TDetail>(
            IQueryable<TDetail> source, PagedListInput input)
        {
            foreach (SearchTermInfo searchInfo in input.SearchTermInfos
                .Where(searchInfo => !string.IsNullOrEmpty(searchInfo.SearchField)
                                    && searchInfo.SearchValue is not null))
            {
                source = source.Where(ApplySearchFilter<TDetail>(searchInfo));
            }

            return source;
        }

        private static Expression<Func<TDetail, bool>> ApplySearchFilter<TDetail>(SearchTermInfo searchTermInfo)
        {
            PropertyInfo propertyInfo = GetPropertyInfo<TDetail>(searchTermInfo.SearchField);

            if (propertyInfo is null || !propertyInfo.CanWrite || !propertyInfo.CanRead
                || searchTermInfo.SearchValue is not JsonElement jsonElement)
            {
                return x => true;
            }

            string jsonString = jsonElement.GetRawText();
            using JsonDocument doc = JsonDocument.Parse(jsonString);
            object realValue = doc.Deserialize(propertyInfo.PropertyType);

            if (realValue is null)
            {
                return x => true;
            }

            ParameterExpression parameter = Expression.Parameter(typeof(TDetail), "item");
            MemberExpression property = Expression.Property(parameter, propertyInfo);

            if (propertyInfo.PropertyType == typeof(string))
            {
                return ApplyStringSearchFilter<TDetail>(parameter, property, realValue);
            }

            if (propertyInfo.PropertyType == typeof(DateTime) || propertyInfo.PropertyType == typeof(DateTime?))
            {
                return ApplyDateSearchFilter<TDetail>(parameter, property, propertyInfo.PropertyType, realValue);
            }

            return ApplyDefaultSearchFilter<TDetail>(parameter, property, propertyInfo.PropertyType, realValue);
        }

        private static PropertyInfo GetPropertyInfo<TDetail>(string name)
        {
            BindingFlags flags = BindingFlags.Instance
                | BindingFlags.Public | BindingFlags.NonPublic
                | BindingFlags.IgnoreCase;

            return typeof(TDetail).GetProperty(name, flags);
        }

        private static Expression<Func<TDetail, bool>> ApplyStringSearchFilter<TDetail>(
            ParameterExpression parameter, MemberExpression property, object realValue)
        {
            ConstantExpression convertedValue = Expression.Constant(realValue, typeof(string));
            MethodInfo containsMethod = typeof(string).GetMethod("Contains", new[] { typeof(string) });
            MethodCallExpression containsExpression = Expression.Call(property, containsMethod, convertedValue);

            return Expression.Lambda<Func<TDetail, bool>>(containsExpression, parameter);
        }

        private static Expression<Func<TDetail, bool>> ApplyDateSearchFilter<TDetail>(
            ParameterExpression parameter, MemberExpression property, Type propertyType, object realValue)
        {
            DateTime startDate = ((DateTime)realValue).Date;
            DateTime endDate = startDate.AddDays(1);

            MemberExpression propertyExtensions = propertyType == typeof(DateTime?)
                ? Expression.Property(Expression.Property(property, "Value"), "Date")
                : Expression.Property(property, "Date");

            BinaryExpression greaterThanOrEqual = Expression.GreaterThanOrEqual(
                propertyExtensions,
                Expression.Constant(startDate)
            );

            BinaryExpression lessThan = Expression.LessThan(
                propertyExtensions,
                Expression.Constant(endDate)
            );

            BinaryExpression dateRangeExpression = Expression.And(greaterThanOrEqual, lessThan);
            Expression<Func<TDetail, bool>> lambda =
                Expression.Lambda<Func<TDetail, bool>>(dateRangeExpression, parameter);

            return lambda;
        }

        private static Expression<Func<TDetail, bool>> ApplyDefaultSearchFilter<TDetail>(
            ParameterExpression parameter, MemberExpression property, Type propertyType, object realValue)
        {
            ConstantExpression valueExpression = Expression.Constant(realValue, propertyType);
            BinaryExpression equalExpression = Expression.Equal(property, valueExpression);

            return Expression.Lambda<Func<TDetail, bool>>(equalExpression, parameter);
        }

        private static IQueryable<TDetail> ApplySortOrders<TDetail>(
            IQueryable<TDetail> source, IEnumerable<SortOrderInfo> orderInfos)
        {
            bool isFirstOrder = true;
            foreach (var orderInfo in orderInfos
                .Where(orderInfo => !string.IsNullOrEmpty(orderInfo.SortField)))
            {
                PropertyInfo propertyInfo = GetPropertyInfo<TDetail>(orderInfo.SortField);
                if (propertyInfo is not null)
                {
                    ParameterExpression parameter = Expression.Parameter(typeof(TDetail), "item");
                    MemberExpression property = Expression.Property(parameter, propertyInfo);
                    Expression converted = Expression.Convert(property, typeof(object));
                    Expression<Func<TDetail, object>> lambda =
                        Expression.Lambda<Func<TDetail, object>>(converted, parameter);

                    source = ApplySortOrder(source, lambda, orderInfo.SortOrder, isFirstOrder);
                    isFirstOrder = false;
                }
            }

            return source;
        }

        private static IQueryable<TDetail> ApplySortOrder<TDetail>(
            IQueryable<TDetail> source, Expression<Func<TDetail, object>> lambda,
            string sortOrder, bool isFirstOrder)
        {
            if (sortOrder?.Equals("desc", StringComparison.OrdinalIgnoreCase) == true)
            {
                return isFirstOrder
                    ? source.OrderByDescending(lambda)
                    : ((IOrderedQueryable<TDetail>)source).ThenByDescending(lambda);
            }

            // 預設升冪
            return isFirstOrder
                ? source.OrderBy(lambda)
                : ((IOrderedQueryable<TDetail>)source).ThenBy(lambda);
        }

        private static IEnumerable<TDetail> ApplySortOrders<TDetail>(
            IEnumerable<TDetail> source, IEnumerable<SortOrderInfo> orderInfos)
        {
            bool isFirstOrder = true;

            foreach (var orderInfo in orderInfos
            .Where(orderInfo => !string.IsNullOrEmpty(orderInfo.SortField)))
            {
                PropertyInfo propertyInfo = GetPropertyInfo<TDetail>(orderInfo.SortField);
                if (propertyInfo is not null)
                {
                    ParameterExpression parameter = Expression.Parameter(typeof(TDetail), "item");
                    MemberExpression property = Expression.Property(parameter, propertyInfo);
                    UnaryExpression converted = Expression.Convert(property, typeof(object));

                    Expression<Func<TDetail, object>> lambda =
                        Expression.Lambda<Func<TDetail, object>>(converted, parameter);


                    source = ApplySortOrder(source, lambda.Compile(), orderInfo.SortOrder, isFirstOrder);
                    isFirstOrder = false;
                }
            }

            return source;
        }

        private static IEnumerable<TDetail> ApplySortOrder<TDetail>(
            IEnumerable<TDetail> source, Func<TDetail, object> lambda,
            string sortOrder, bool isFirstOrder)
        {
            if (sortOrder?.Equals("desc", StringComparison.OrdinalIgnoreCase) == true)
            {
                return isFirstOrder
                    ? source.OrderByDescending(lambda)
                    : ((IOrderedEnumerable<TDetail>)source).ThenByDescending(lambda);
            }

            // 預設升冪
            return isFirstOrder
                ? source.OrderBy(lambda)
                : ((IOrderedEnumerable<TDetail>)source).ThenBy(lambda);
        }

        private static async Task<PagedListOutput<TDetail>> CreatePagedListOutputAsync<TDetail>(IQueryable<TDetail> source, PagedListInput input)
        {
            PagedListOutput<TDetail> output = new(input);

            if (output.UsingPaging)
            {
                output.RecordCount = await source.CountAsync();
                if (output.RecordCount > 0)
                {
                    output.Details = await source
                        .Skip((input.PageIndex - 1) * input.NumberOfPerPage)
                        .Take(input.NumberOfPerPage)
                        .ToListAsync();
                }
            }
            else
            {
                output.Details = await source.ToListAsync();
            }

            return output;
        }

        private static PagedListOutput<TDetail> CreatePagedListOutput<TDetail>(IEnumerable<TDetail> source, PagedListInput input)
        {
            PagedListOutput<TDetail> output = new(input);

            if (output.UsingPaging)
            {
                output.RecordCount = source.Count();
                if (output.RecordCount > 0)
                {
                    output.Details = source
                        .Skip((input.PageIndex - 1) * input.NumberOfPerPage)
                        .Take(input.NumberOfPerPage)
                        .ToList();
                }
            }
            else
            {
                output.Details = source.ToList();
            }

            return output;
        }
    }
}
