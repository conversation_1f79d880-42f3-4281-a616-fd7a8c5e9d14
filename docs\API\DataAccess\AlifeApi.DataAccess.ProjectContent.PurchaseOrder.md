#### [AlifeApi.DataAccess](index.md 'index')
### [AlifeApi.DataAccess.ProjectContent](AlifeApi.DataAccess.ProjectContent.md 'AlifeApi.DataAccess.ProjectContent')

## PurchaseOrder Class

儲存客戶預定或購買房屋單位與停車位的訂單詳細資訊 (交易記錄)。

```csharp
public class PurchaseOrder
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; PurchaseOrder
### Properties

<a name='AlifeApi.DataAccess.ProjectContent.PurchaseOrder.CancellationDate'></a>

## PurchaseOrder.CancellationDate Property

退訂日期 (客戶退訂或訂單取消的日期)

```csharp
public System.Nullable<System.DateOnly> CancellationDate { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.DateOnly](https://docs.microsoft.com/en-us/dotnet/api/System.DateOnly 'System.DateOnly')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

<a name='AlifeApi.DataAccess.ProjectContent.PurchaseOrder.ConsentToDataUsage'></a>

## PurchaseOrder.ConsentToDataUsage Property

是否同意個人資料收集與利用 (True/False)。

```csharp
public System.Nullable<bool> ConsentToDataUsage { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.Boolean](https://docs.microsoft.com/en-us/dotnet/api/System.Boolean 'System.Boolean')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

<a name='AlifeApi.DataAccess.ProjectContent.PurchaseOrder.ContractSignedDate'></a>

## PurchaseOrder.ContractSignedDate Property

實際簽約日期 (正式簽約完成的日期) - 狀態：簽

```csharp
public System.Nullable<System.DateOnly> ContractSignedDate { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.DateOnly](https://docs.microsoft.com/en-us/dotnet/api/System.DateOnly 'System.DateOnly')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

<a name='AlifeApi.DataAccess.ProjectContent.PurchaseOrder.ContractSigningAmount'></a>

## PurchaseOrder.ContractSigningAmount Property

簽約金 (新台幣)。

```csharp
public System.Nullable<decimal> ContractSigningAmount { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.Decimal](https://docs.microsoft.com/en-us/dotnet/api/System.Decimal 'System.Decimal')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

<a name='AlifeApi.DataAccess.ProjectContent.PurchaseOrder.ContractSigningAppointment'></a>

## PurchaseOrder.ContractSigningAppointment Property

約定的簽約日期與時間。

```csharp
public System.Nullable<System.DateTime> ContractSigningAppointment { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.DateTime](https://docs.microsoft.com/en-us/dotnet/api/System.DateTime 'System.DateTime')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

<a name='AlifeApi.DataAccess.ProjectContent.PurchaseOrder.CreatedTime'></a>

## PurchaseOrder.CreatedTime Property

訂單記錄創建時間 (TIMESTAMP WITHOUT TIME ZONE)。

```csharp
public System.DateTime CreatedTime { get; set; }
```

#### Property Value
[System.DateTime](https://docs.microsoft.com/en-us/dotnet/api/System.DateTime 'System.DateTime')

<a name='AlifeApi.DataAccess.ProjectContent.PurchaseOrder.CreatedUserInfoId'></a>

## PurchaseOrder.CreatedUserInfoId Property

建立者 (參考 UserInfo 表)。

```csharp
public string CreatedUserInfoId { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContent.PurchaseOrder.CustomerId'></a>

## PurchaseOrder.CustomerId Property

買受人ID (參考 Customer 表)。

```csharp
public int CustomerId { get; set; }
```

#### Property Value
[System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')

<a name='AlifeApi.DataAccess.ProjectContent.PurchaseOrder.DepositAmount'></a>

## PurchaseOrder.DepositAmount Property

定金總額 (新台幣)。

```csharp
public System.Nullable<decimal> DepositAmount { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.Decimal](https://docs.microsoft.com/en-us/dotnet/api/System.Decimal 'System.Decimal')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

<a name='AlifeApi.DataAccess.ProjectContent.PurchaseOrder.DepositBalanceAmount'></a>

## PurchaseOrder.DepositBalanceAmount Property

應補足的定金金額 (新台幣)。

```csharp
public System.Nullable<decimal> DepositBalanceAmount { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.Decimal](https://docs.microsoft.com/en-us/dotnet/api/System.Decimal 'System.Decimal')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

<a name='AlifeApi.DataAccess.ProjectContent.PurchaseOrder.DepositDueDate'></a>

## PurchaseOrder.DepositDueDate Property

應補足定金的截止日期與時間。

```csharp
public System.Nullable<System.DateTime> DepositDueDate { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.DateTime](https://docs.microsoft.com/en-us/dotnet/api/System.DateTime 'System.DateTime')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

<a name='AlifeApi.DataAccess.ProjectContent.PurchaseOrder.DepositFullPaidDate'></a>

## PurchaseOrder.DepositFullPaidDate Property

足訂日期 (定金足額到位的實際日期) - 狀態：足

```csharp
public System.Nullable<System.DateOnly> DepositFullPaidDate { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.DateOnly](https://docs.microsoft.com/en-us/dotnet/api/System.DateOnly 'System.DateOnly')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

<a name='AlifeApi.DataAccess.ProjectContent.PurchaseOrder.DepositPaidAmount'></a>

## PurchaseOrder.DepositPaidAmount Property

已付定金 (新台幣)。

```csharp
public System.Nullable<decimal> DepositPaidAmount { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.Decimal](https://docs.microsoft.com/en-us/dotnet/api/System.Decimal 'System.Decimal')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

<a name='AlifeApi.DataAccess.ProjectContent.PurchaseOrder.DepositPayee'></a>

## PurchaseOrder.DepositPayee Property

定金收款人。

```csharp
public string DepositPayee { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContent.PurchaseOrder.DepositPaymentMethod'></a>

## PurchaseOrder.DepositPaymentMethod Property

定金付款方式 (建議使用 SYS_Code)。

```csharp
public string DepositPaymentMethod { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContent.PurchaseOrder.FinalPaymentDate'></a>

## PurchaseOrder.FinalPaymentDate Property

尾款繳清日期 (最後一筆款項收款完成的日期)

```csharp
public System.Nullable<System.DateOnly> FinalPaymentDate { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.DateOnly](https://docs.microsoft.com/en-us/dotnet/api/System.DateOnly 'System.DateOnly')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

<a name='AlifeApi.DataAccess.ProjectContent.PurchaseOrder.HandoverDate'></a>

## PurchaseOrder.HandoverDate Property

交屋日期 (房屋正式交付給客戶的日期)

```csharp
public System.Nullable<System.DateOnly> HandoverDate { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.DateOnly](https://docs.microsoft.com/en-us/dotnet/api/System.DateOnly 'System.DateOnly')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

<a name='AlifeApi.DataAccess.ProjectContent.PurchaseOrder.LandShareArea'></a>

## PurchaseOrder.LandShareArea Property

土地持分面積 (坪)。

```csharp
public System.Nullable<decimal> LandShareArea { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.Decimal](https://docs.microsoft.com/en-us/dotnet/api/System.Decimal 'System.Decimal')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

<a name='AlifeApi.DataAccess.ProjectContent.PurchaseOrder.OrderDate'></a>

## PurchaseOrder.OrderDate Property

訂單建立日期。

```csharp
public System.DateOnly OrderDate { get; set; }
```

#### Property Value
[System.DateOnly](https://docs.microsoft.com/en-us/dotnet/api/System.DateOnly 'System.DateOnly')

<a name='AlifeApi.DataAccess.ProjectContent.PurchaseOrder.OrderId'></a>

## PurchaseOrder.OrderId Property

訂單唯一識別碼 (主鍵, 自動遞增)。

```csharp
public int OrderId { get; set; }
```

#### Property Value
[System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')

<a name='AlifeApi.DataAccess.ProjectContent.PurchaseOrder.OrderNumber'></a>

## PurchaseOrder.OrderNumber Property

預定單編號 (可選, 應具備唯一性)。

```csharp
public string OrderNumber { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContent.PurchaseOrder.OrderRemarks'></a>

## PurchaseOrder.OrderRemarks Property

訂單備註 (例如：特殊協議)。

```csharp
public string OrderRemarks { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContent.PurchaseOrder.PriceRegistrationSubmissionDate'></a>

## PurchaseOrder.PriceRegistrationSubmissionDate Property

實價登錄申報日

```csharp
public System.Nullable<System.DateOnly> PriceRegistrationSubmissionDate { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.DateOnly](https://docs.microsoft.com/en-us/dotnet/api/System.DateOnly 'System.DateOnly')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

<a name='AlifeApi.DataAccess.ProjectContent.PurchaseOrder.ReceiveDate'></a>

## PurchaseOrder.ReceiveDate Property

領款日期 (已向業主領到費用的日期) - 狀態：領

```csharp
public System.Nullable<System.DateOnly> ReceiveDate { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.DateOnly](https://docs.microsoft.com/en-us/dotnet/api/System.DateOnly 'System.DateOnly')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

<a name='AlifeApi.DataAccess.ProjectContent.PurchaseOrder.RequestDate'></a>

## PurchaseOrder.RequestDate Property

請款日期 (向業主請費用的日期) - 狀態：請

```csharp
public System.Nullable<System.DateOnly> RequestDate { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.DateOnly](https://docs.microsoft.com/en-us/dotnet/api/System.DateOnly 'System.DateOnly')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

<a name='AlifeApi.DataAccess.ProjectContent.PurchaseOrder.SaleDate'></a>

## PurchaseOrder.SaleDate Property

實際售出日期 (客戶確認購買意願的日期) - 狀態：售

```csharp
public System.Nullable<System.DateOnly> SaleDate { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.DateOnly](https://docs.microsoft.com/en-us/dotnet/api/System.DateOnly 'System.DateOnly')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

<a name='AlifeApi.DataAccess.ProjectContent.PurchaseOrder.SalesAgencyEmail'></a>

## PurchaseOrder.SalesAgencyEmail Property

代銷公司 E-mail。

```csharp
public string SalesAgencyEmail { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContent.PurchaseOrder.SalesAgencyLandline'></a>

## PurchaseOrder.SalesAgencyLandline Property

代銷公司市話。

```csharp
public string SalesAgencyLandline { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContent.PurchaseOrder.SalesAgencyMobile'></a>

## PurchaseOrder.SalesAgencyMobile Property

代銷公司行動電話。

```csharp
public string SalesAgencyMobile { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContent.PurchaseOrder.SalesAgencyName'></a>

## PurchaseOrder.SalesAgencyName Property

代銷公司名稱。

```csharp
public string SalesAgencyName { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContent.PurchaseOrder.SalespersonUserInfoId'></a>

## PurchaseOrder.SalespersonUserInfoId Property

執行此訂單的銷售人員ID (參考 UserInfo 表)。

```csharp
public string SalespersonUserInfoId { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContent.PurchaseOrder.SaleType'></a>

## PurchaseOrder.SaleType Property

銷售類型 (例如: '委售', '自售' - 建議使用 SYS_Code)。

```csharp
public string SaleType { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContent.PurchaseOrder.SiteCode'></a>

## PurchaseOrder.SiteCode Property

建案編號 (參考 Sites 表)。

```csharp
public string SiteCode { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContent.PurchaseOrder.Status'></a>

## PurchaseOrder.Status Property

訂單本身的狀態 (例如: '預訂中', '已轉訂', '已簽約', '已取消', '已作廢' - 建議使用 SYS_Code)。

```csharp
public string Status { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContent.PurchaseOrder.TotalPrice'></a>

## PurchaseOrder.TotalPrice Property

本次交易的總價 (新台幣 = PropertyPrice + ParkingSpacePrice)。

```csharp
public decimal TotalPrice { get; set; }
```

#### Property Value
[System.Decimal](https://docs.microsoft.com/en-us/dotnet/api/System.Decimal 'System.Decimal')

<a name='AlifeApi.DataAccess.ProjectContent.PurchaseOrder.UpdatedTime'></a>

## PurchaseOrder.UpdatedTime Property

訂單記錄最後更新時間 (TIMESTAMP WITHOUT TIME ZONE)。

```csharp
public System.DateTime UpdatedTime { get; set; }
```

#### Property Value
[System.DateTime](https://docs.microsoft.com/en-us/dotnet/api/System.DateTime 'System.DateTime')

<a name='AlifeApi.DataAccess.ProjectContent.PurchaseOrder.UpdatedUserInfoId'></a>

## PurchaseOrder.UpdatedUserInfoId Property

更新者 (參考 UserInfo 表)。

```csharp
public string UpdatedUserInfoId { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')