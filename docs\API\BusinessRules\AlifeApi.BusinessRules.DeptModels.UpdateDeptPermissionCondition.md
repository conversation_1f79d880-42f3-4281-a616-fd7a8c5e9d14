#### [AlifeApi.BusinessRules](index.md 'index')
### [AlifeApi.BusinessRules.DeptModels](AlifeApi.BusinessRules.DeptModels.md 'AlifeApi.BusinessRules.DeptModels')

## UpdateDeptPermissionCondition Class

```csharp
public class UpdateDeptPermissionCondition
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; UpdateDeptPermissionCondition
### Properties

<a name='AlifeApi.BusinessRules.DeptModels.UpdateDeptPermissionCondition.DeptId'></a>

## UpdateDeptPermissionCondition.DeptId Property

使用者單位

```csharp
public string DeptId { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.DeptModels.UpdateDeptPermissionCondition.FuncIds'></a>

## UpdateDeptPermissionCondition.FuncIds Property

```csharp
public System.Collections.Generic.IEnumerable<string> FuncIds { get; }
```

#### Property Value
[System.Collections.Generic.IEnumerable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Collections.Generic.IEnumerable-1 'System.Collections.Generic.IEnumerable`1')[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Collections.Generic.IEnumerable-1 'System.Collections.Generic.IEnumerable`1')

### Remarks
前端會傳字串，只好這樣處理

<a name='AlifeApi.BusinessRules.DeptModels.UpdateDeptPermissionCondition.Permission'></a>

## UpdateDeptPermissionCondition.Permission Property

權限

```csharp
public string Permission { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')