#### [AlifeApi.BusinessRules](index.md 'index')
### [AlifeApi.BusinessRules.CategoryModels](AlifeApi.BusinessRules.CategoryModels.md 'AlifeApi.BusinessRules.CategoryModels')

## LargeCategoryInput Class

大分類資料輸入 DTO

```csharp
public class LargeCategoryInput
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; LargeCategoryInput
### Properties

<a name='AlifeApi.BusinessRules.CategoryModels.LargeCategoryInput.Description'></a>

## LargeCategoryInput.Description Property

大分類描述

```csharp
public string Description { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.CategoryModels.LargeCategoryInput.IsActive'></a>

## LargeCategoryInput.IsActive Property

是否啟用

```csharp
public bool IsActive { get; set; }
```

#### Property Value
[System.Boolean](https://docs.microsoft.com/en-us/dotnet/api/System.Boolean 'System.Boolean')

<a name='AlifeApi.BusinessRules.CategoryModels.LargeCategoryInput.Name'></a>

## LargeCategoryInput.Name Property

大分類名稱

```csharp
public string Name { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.CategoryModels.LargeCategoryInput.SortOrder'></a>

## LargeCategoryInput.SortOrder Property

排序順序

```csharp
public int SortOrder { get; set; }
```

#### Property Value
[System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')