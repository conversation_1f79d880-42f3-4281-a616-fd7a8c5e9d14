#### [AlifeApi.BusinessRules](index.md 'index')
### [AlifeApi.BusinessRules.CategoryModels](AlifeApi.BusinessRules.CategoryModels.md 'AlifeApi.BusinessRules.CategoryModels')

## SmallCategoryListOutput Class

小分類列表輸出 DTO (摘要資訊)

```csharp
public class SmallCategoryListOutput
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; SmallCategoryListOutput
### Properties

<a name='AlifeApi.BusinessRules.CategoryModels.SmallCategoryListOutput.CreateTime'></a>

## SmallCategoryListOutput.CreateTime Property

建立時間

```csharp
public System.DateTime CreateTime { get; set; }
```

#### Property Value
[System.DateTime](https://docs.microsoft.com/en-us/dotnet/api/System.DateTime 'System.DateTime')

<a name='AlifeApi.BusinessRules.CategoryModels.SmallCategoryListOutput.Description'></a>

## SmallCategoryListOutput.Description Property

小分類描述

```csharp
public string Description { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.CategoryModels.SmallCategoryListOutput.IsActive'></a>

## SmallCategoryListOutput.IsActive Property

是否啟用

```csharp
public System.Nullable<bool> IsActive { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.Boolean](https://docs.microsoft.com/en-us/dotnet/api/System.Boolean 'System.Boolean')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

<a name='AlifeApi.BusinessRules.CategoryModels.SmallCategoryListOutput.LargeCategoryId'></a>

## SmallCategoryListOutput.LargeCategoryId Property

所屬大分類ID

```csharp
public long LargeCategoryId { get; set; }
```

#### Property Value
[System.Int64](https://docs.microsoft.com/en-us/dotnet/api/System.Int64 'System.Int64')

<a name='AlifeApi.BusinessRules.CategoryModels.SmallCategoryListOutput.LargeCategoryName'></a>

## SmallCategoryListOutput.LargeCategoryName Property

所屬大分類名稱

```csharp
public string LargeCategoryName { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.CategoryModels.SmallCategoryListOutput.MediumCategoryId'></a>

## SmallCategoryListOutput.MediumCategoryId Property

所屬中分類ID

```csharp
public long MediumCategoryId { get; set; }
```

#### Property Value
[System.Int64](https://docs.microsoft.com/en-us/dotnet/api/System.Int64 'System.Int64')

<a name='AlifeApi.BusinessRules.CategoryModels.SmallCategoryListOutput.MediumCategoryName'></a>

## SmallCategoryListOutput.MediumCategoryName Property

所屬中分類名稱

```csharp
public string MediumCategoryName { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.CategoryModels.SmallCategoryListOutput.Name'></a>

## SmallCategoryListOutput.Name Property

小分類名稱

```csharp
public string Name { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.CategoryModels.SmallCategoryListOutput.SmallCategoryId'></a>

## SmallCategoryListOutput.SmallCategoryId Property

小分類ID

```csharp
public long SmallCategoryId { get; set; }
```

#### Property Value
[System.Int64](https://docs.microsoft.com/en-us/dotnet/api/System.Int64 'System.Int64')

<a name='AlifeApi.BusinessRules.CategoryModels.SmallCategoryListOutput.SortOrder'></a>

## SmallCategoryListOutput.SortOrder Property

排序順序

```csharp
public int SortOrder { get; set; }
```

#### Property Value
[System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')