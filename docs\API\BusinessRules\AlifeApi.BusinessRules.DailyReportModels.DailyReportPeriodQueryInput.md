#### [AlifeApi.BusinessRules](index.md 'index')
### [AlifeApi.BusinessRules.DailyReportModels](AlifeApi.BusinessRules.DailyReportModels.md 'AlifeApi.BusinessRules.DailyReportModels')

## DailyReportPeriodQueryInput Class

日報期間查詢輸入DTO

```csharp
public class DailyReportPeriodQueryInput
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; DailyReportPeriodQueryInput
### Properties

<a name='AlifeApi.BusinessRules.DailyReportModels.DailyReportPeriodQueryInput.EndDate'></a>

## DailyReportPeriodQueryInput.EndDate Property

結束日期

```csharp
public System.DateTime EndDate { get; set; }
```

#### Property Value
[System.DateTime](https://docs.microsoft.com/en-us/dotnet/api/System.DateTime 'System.DateTime')

<a name='AlifeApi.BusinessRules.DailyReportModels.DailyReportPeriodQueryInput.Page'></a>

## DailyReportPeriodQueryInput.Page Property

頁碼

```csharp
public int Page { get; set; }
```

#### Property Value
[System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')

<a name='AlifeApi.BusinessRules.DailyReportModels.DailyReportPeriodQueryInput.PageSize'></a>

## DailyReportPeriodQueryInput.PageSize Property

每頁筆數

```csharp
public int PageSize { get; set; }
```

#### Property Value
[System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')

<a name='AlifeApi.BusinessRules.DailyReportModels.DailyReportPeriodQueryInput.SiteCode'></a>

## DailyReportPeriodQueryInput.SiteCode Property

案場編號

```csharp
public string SiteCode { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.DailyReportModels.DailyReportPeriodQueryInput.StartDate'></a>

## DailyReportPeriodQueryInput.StartDate Property

開始日期

```csharp
public System.DateTime StartDate { get; set; }
```

#### Property Value
[System.DateTime](https://docs.microsoft.com/en-us/dotnet/api/System.DateTime 'System.DateTime')