﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.Serialization;
using System.Text;
using System.Threading.Tasks;

namespace AlifeApi.Common.DataAnnotations
{
    public class KyException : Exception, ISerializable
    {
        public KyException()
            : base("show message") { }
        public KyException(string message)
            : base(message) { }
        public KyException(string message, Exception inner)
            : base(message, inner) { }
        protected KyException(SerializationInfo info, StreamingContext context)
            : base(info, context) { }
    }
}
