#### [AlifeApi.BusinessRules](index.md 'index')
### [AlifeApi.BusinessRules.SysCodeModels](AlifeApi.BusinessRules.SysCodeModels.md 'AlifeApi.BusinessRules.SysCodeModels')

## SysCodeByTypeGetInput Class

```csharp
public class SysCodeByTypeGetInput : AlifeApi.BusinessRules.Infrastructure.PagedListInput
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; [PagedListInput](AlifeApi.BusinessRules.Infrastructure.PagedListInput.md 'AlifeApi.BusinessRules.Infrastructure.PagedListInput') &#129106; SysCodeByTypeGetInput
### Properties

<a name='AlifeApi.BusinessRules.SysCodeModels.SysCodeByTypeGetInput.Type'></a>

## SysCodeByTypeGetInput.Type Property

類型

```csharp
public string Type { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')