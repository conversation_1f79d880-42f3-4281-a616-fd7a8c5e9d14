﻿using System;
using System.Collections.Generic;

namespace AlifeApi.DataAccess.ProjectContent
{
    /// <summary>
    /// 審核通知設定表，用於儲存每個審核步驟的通知設定資訊。
    /// </summary>
    public partial class NotificationSetting
    {
        /// <summary>
        /// 設定流水號，主鍵，自動遞增，用於唯一識別通知設定記錄。
        /// </summary>
        public int SettingId { get; set; }
        /// <summary>
        /// 步驟流水號，對應 ReviewSteps 表的 StepId。
        /// </summary>
        public int? StepId { get; set; }
        /// <summary>
        /// 通知類型，可選值為 system（系統通知）、email（電子郵件）、sms（簡訊）。
        /// </summary>
        public string NotifyType { get; set; }
        /// <summary>
        /// 是否啟用，true 表示啟用，false 表示停用，預設為 true。
        /// </summary>
        public bool? Enabled { get; set; }

        public virtual ReviewStep Step { get; set; }
    }
}
