#### [AlifeApi.BusinessRules](index.md 'index')
### [AlifeApi.BusinessRules.RoleGroupModels](AlifeApi.BusinessRules.RoleGroupModels.md 'AlifeApi.BusinessRules.RoleGroupModels')

## RoleGroupServicePg Class

角色權限商業邏輯（PostgreSQL 版本）

```csharp
public class RoleGroupServicePg : AlifeApi.BusinessRules.Infrastructure.ServiceBase<AlifeApi.DataAccess.ProjectContent.alifeContext>
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; [AlifeApi.Common.InfrastructureBase](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.Common.InfrastructureBase 'AlifeApi.Common.InfrastructureBase') &#129106; [AlifeApi.BusinessRules.Infrastructure.ServiceBase&lt;](AlifeApi.BusinessRules.Infrastructure.ServiceBase_TDbContext_.md 'AlifeApi.BusinessRules.Infrastructure.ServiceBase<TDbContext>')[AlifeApi.DataAccess.ProjectContent.alifeContext](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.DataAccess.ProjectContent.alifeContext 'AlifeApi.DataAccess.ProjectContent.alifeContext')[&gt;](AlifeApi.BusinessRules.Infrastructure.ServiceBase_TDbContext_.md 'AlifeApi.BusinessRules.Infrastructure.ServiceBase<TDbContext>') &#129106; RoleGroupServicePg
### Methods

<a name='AlifeApi.BusinessRules.RoleGroupModels.RoleGroupServicePg.CreateRoleGroupAsync(AlifeApi.BusinessRules.RoleGroupModels.RoleGroupCreateInputPg)'></a>

## RoleGroupServicePg.CreateRoleGroupAsync(RoleGroupCreateInputPg) Method

新增角色權限

```csharp
public System.Threading.Tasks.Task CreateRoleGroupAsync(AlifeApi.BusinessRules.RoleGroupModels.RoleGroupCreateInputPg input);
```
#### Parameters

<a name='AlifeApi.BusinessRules.RoleGroupModels.RoleGroupServicePg.CreateRoleGroupAsync(AlifeApi.BusinessRules.RoleGroupModels.RoleGroupCreateInputPg).input'></a>

`input` [RoleGroupCreateInputPg](AlifeApi.BusinessRules.RoleGroupModels.RoleGroupCreateInputPg.md 'AlifeApi.BusinessRules.RoleGroupModels.RoleGroupCreateInputPg')

角色權限輸入資料

#### Returns
[System.Threading.Tasks.Task](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task 'System.Threading.Tasks.Task')

#### Exceptions

[System.ArgumentNullException](https://docs.microsoft.com/en-us/dotnet/api/System.ArgumentNullException 'System.ArgumentNullException')  
input

<a name='AlifeApi.BusinessRules.RoleGroupModels.RoleGroupServicePg.DeleteRoleGroupAsync(AlifeApi.BusinessRules.RoleGroupModels.RoleGroupDeleteInputPg)'></a>

## RoleGroupServicePg.DeleteRoleGroupAsync(RoleGroupDeleteInputPg) Method

刪除角色

```csharp
public System.Threading.Tasks.Task DeleteRoleGroupAsync(AlifeApi.BusinessRules.RoleGroupModels.RoleGroupDeleteInputPg input);
```
#### Parameters

<a name='AlifeApi.BusinessRules.RoleGroupModels.RoleGroupServicePg.DeleteRoleGroupAsync(AlifeApi.BusinessRules.RoleGroupModels.RoleGroupDeleteInputPg).input'></a>

`input` [RoleGroupDeleteInputPg](AlifeApi.BusinessRules.RoleGroupModels.RoleGroupDeleteInputPg.md 'AlifeApi.BusinessRules.RoleGroupModels.RoleGroupDeleteInputPg')

角色刪除輸入資料

#### Returns
[System.Threading.Tasks.Task](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task 'System.Threading.Tasks.Task')

#### Exceptions

[System.ArgumentNullException](https://docs.microsoft.com/en-us/dotnet/api/System.ArgumentNullException 'System.ArgumentNullException')  
input

<a name='AlifeApi.BusinessRules.RoleGroupModels.RoleGroupServicePg.GetLoginMenuTreesAsync(System.Collections.Generic.IEnumerable_string_,string)'></a>

## RoleGroupServicePg.GetLoginMenuTreesAsync(IEnumerable<string>, string) Method

取得登入用的選單樹 (父節點強制 Selected = true)

```csharp
public System.Threading.Tasks.Task<System.Collections.Generic.IEnumerable<AlifeApi.BusinessRules.RoleGroupModels.MenuTreeOutput>> GetLoginMenuTreesAsync(System.Collections.Generic.IEnumerable<string> userFuncIds=null, string rootMenu="");
```
#### Parameters

<a name='AlifeApi.BusinessRules.RoleGroupModels.RoleGroupServicePg.GetLoginMenuTreesAsync(System.Collections.Generic.IEnumerable_string_,string).userFuncIds'></a>

`userFuncIds` [System.Collections.Generic.IEnumerable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Collections.Generic.IEnumerable-1 'System.Collections.Generic.IEnumerable`1')[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Collections.Generic.IEnumerable-1 'System.Collections.Generic.IEnumerable`1')

使用者擁有的功能ID列表

<a name='AlifeApi.BusinessRules.RoleGroupModels.RoleGroupServicePg.GetLoginMenuTreesAsync(System.Collections.Generic.IEnumerable_string_,string).rootMenu'></a>

`rootMenu` [System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

根選單ID

#### Returns
[System.Threading.Tasks.Task&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')[System.Collections.Generic.IEnumerable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Collections.Generic.IEnumerable-1 'System.Collections.Generic.IEnumerable`1')[MenuTreeOutput](AlifeApi.BusinessRules.RoleGroupModels.MenuTreeOutput.md 'AlifeApi.BusinessRules.RoleGroupModels.MenuTreeOutput')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Collections.Generic.IEnumerable-1 'System.Collections.Generic.IEnumerable`1')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')  
登入用的選單樹

<a name='AlifeApi.BusinessRules.RoleGroupModels.RoleGroupServicePg.GetMenuTreesAsync(System.Collections.Generic.IEnumerable_string_,string)'></a>

## RoleGroupServicePg.GetMenuTreesAsync(IEnumerable<string>, string) Method

取得選單樹

```csharp
public System.Threading.Tasks.Task<System.Collections.Generic.IEnumerable<AlifeApi.BusinessRules.RoleGroupModels.MenuTreeOutput>> GetMenuTreesAsync(System.Collections.Generic.IEnumerable<string> userFuncIds=null, string rootMenu="");
```
#### Parameters

<a name='AlifeApi.BusinessRules.RoleGroupModels.RoleGroupServicePg.GetMenuTreesAsync(System.Collections.Generic.IEnumerable_string_,string).userFuncIds'></a>

`userFuncIds` [System.Collections.Generic.IEnumerable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Collections.Generic.IEnumerable-1 'System.Collections.Generic.IEnumerable`1')[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Collections.Generic.IEnumerable-1 'System.Collections.Generic.IEnumerable`1')

<a name='AlifeApi.BusinessRules.RoleGroupModels.RoleGroupServicePg.GetMenuTreesAsync(System.Collections.Generic.IEnumerable_string_,string).rootMenu'></a>

`rootMenu` [System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

#### Returns
[System.Threading.Tasks.Task&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')[System.Collections.Generic.IEnumerable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Collections.Generic.IEnumerable-1 'System.Collections.Generic.IEnumerable`1')[MenuTreeOutput](AlifeApi.BusinessRules.RoleGroupModels.MenuTreeOutput.md 'AlifeApi.BusinessRules.RoleGroupModels.MenuTreeOutput')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Collections.Generic.IEnumerable-1 'System.Collections.Generic.IEnumerable`1')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')  
選單樹

<a name='AlifeApi.BusinessRules.RoleGroupModels.RoleGroupServicePg.GetRoleGroupByIdAsync(string,string)'></a>

## RoleGroupServicePg.GetRoleGroupByIdAsync(string, string) Method

根據ID取得角色權限詳細資訊

```csharp
public System.Threading.Tasks.Task<AlifeApi.BusinessRules.RoleGroupModels.RoleGroupOutputPg> GetRoleGroupByIdAsync(string id, string siteCode=null);
```
#### Parameters

<a name='AlifeApi.BusinessRules.RoleGroupModels.RoleGroupServicePg.GetRoleGroupByIdAsync(string,string).id'></a>

`id` [System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

角色ID

<a name='AlifeApi.BusinessRules.RoleGroupModels.RoleGroupServicePg.GetRoleGroupByIdAsync(string,string).siteCode'></a>

`siteCode` [System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

案場代碼

#### Returns
[System.Threading.Tasks.Task&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')[RoleGroupOutputPg](AlifeApi.BusinessRules.RoleGroupModels.RoleGroupOutputPg.md 'AlifeApi.BusinessRules.RoleGroupModels.RoleGroupOutputPg')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')  
角色權限詳細資訊

<a name='AlifeApi.BusinessRules.RoleGroupModels.RoleGroupServicePg.GetRoleGroupDropdownListAsync(AlifeApi.BusinessRules.RoleGroupModels.RoleGroupDropdownInputPg)'></a>

## RoleGroupServicePg.GetRoleGroupDropdownListAsync(RoleGroupDropdownInputPg) Method

取得角色群組下拉選單

```csharp
public System.Threading.Tasks.Task<System.Collections.Generic.List<AlifeApi.BusinessRules.RoleGroupModels.RoleGroupDropdownOutput>> GetRoleGroupDropdownListAsync(AlifeApi.BusinessRules.RoleGroupModels.RoleGroupDropdownInputPg input);
```
#### Parameters

<a name='AlifeApi.BusinessRules.RoleGroupModels.RoleGroupServicePg.GetRoleGroupDropdownListAsync(AlifeApi.BusinessRules.RoleGroupModels.RoleGroupDropdownInputPg).input'></a>

`input` [RoleGroupDropdownInputPg](AlifeApi.BusinessRules.RoleGroupModels.RoleGroupDropdownInputPg.md 'AlifeApi.BusinessRules.RoleGroupModels.RoleGroupDropdownInputPg')

查詢條件

#### Returns
[System.Threading.Tasks.Task&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')[System.Collections.Generic.List&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Collections.Generic.List-1 'System.Collections.Generic.List`1')[RoleGroupDropdownOutput](AlifeApi.BusinessRules.RoleGroupModels.RoleGroupDropdownOutput.md 'AlifeApi.BusinessRules.RoleGroupModels.RoleGroupDropdownOutput')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Collections.Generic.List-1 'System.Collections.Generic.List`1')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')  
角色群組下拉選單列表

<a name='AlifeApi.BusinessRules.RoleGroupModels.RoleGroupServicePg.GetRoleGroupListAsync(AlifeApi.BusinessRules.RoleGroupModels.RoleGroupListGetInputPg)'></a>

## RoleGroupServicePg.GetRoleGroupListAsync(RoleGroupListGetInputPg) Method

取得角色權限清單

```csharp
public System.Threading.Tasks.Task<AlifeApi.BusinessRules.Infrastructure.PagedListOutput<AlifeApi.BusinessRules.RoleGroupModels.RoleGroupOutputPg>> GetRoleGroupListAsync(AlifeApi.BusinessRules.RoleGroupModels.RoleGroupListGetInputPg input);
```
#### Parameters

<a name='AlifeApi.BusinessRules.RoleGroupModels.RoleGroupServicePg.GetRoleGroupListAsync(AlifeApi.BusinessRules.RoleGroupModels.RoleGroupListGetInputPg).input'></a>

`input` [RoleGroupListGetInputPg](AlifeApi.BusinessRules.RoleGroupModels.RoleGroupListGetInputPg.md 'AlifeApi.BusinessRules.RoleGroupModels.RoleGroupListGetInputPg')

查詢條件

#### Returns
[System.Threading.Tasks.Task&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')[AlifeApi.BusinessRules.Infrastructure.PagedListOutput&lt;](AlifeApi.BusinessRules.Infrastructure.PagedListOutput_TDetail_.md 'AlifeApi.BusinessRules.Infrastructure.PagedListOutput<TDetail>')[RoleGroupOutputPg](AlifeApi.BusinessRules.RoleGroupModels.RoleGroupOutputPg.md 'AlifeApi.BusinessRules.RoleGroupModels.RoleGroupOutputPg')[&gt;](AlifeApi.BusinessRules.Infrastructure.PagedListOutput_TDetail_.md 'AlifeApi.BusinessRules.Infrastructure.PagedListOutput<TDetail>')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')  
角色權限清單

#### Exceptions

[System.ArgumentNullException](https://docs.microsoft.com/en-us/dotnet/api/System.ArgumentNullException 'System.ArgumentNullException')  
input

<a name='AlifeApi.BusinessRules.RoleGroupModels.RoleGroupServicePg.UpdateRoleGroupPermissionAsync(AlifeApi.BusinessRules.RoleGroupModels.RoleGroupPermissionUpdateInputPg)'></a>

## RoleGroupServicePg.UpdateRoleGroupPermissionAsync(RoleGroupPermissionUpdateInputPg) Method

修改角色權限功能

```csharp
public System.Threading.Tasks.Task UpdateRoleGroupPermissionAsync(AlifeApi.BusinessRules.RoleGroupModels.RoleGroupPermissionUpdateInputPg input);
```
#### Parameters

<a name='AlifeApi.BusinessRules.RoleGroupModels.RoleGroupServicePg.UpdateRoleGroupPermissionAsync(AlifeApi.BusinessRules.RoleGroupModels.RoleGroupPermissionUpdateInputPg).input'></a>

`input` [RoleGroupPermissionUpdateInputPg](AlifeApi.BusinessRules.RoleGroupModels.RoleGroupPermissionUpdateInputPg.md 'AlifeApi.BusinessRules.RoleGroupModels.RoleGroupPermissionUpdateInputPg')

角色權限更新輸入資料

#### Returns
[System.Threading.Tasks.Task](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task 'System.Threading.Tasks.Task')

#### Exceptions

[System.ArgumentNullException](https://docs.microsoft.com/en-us/dotnet/api/System.ArgumentNullException 'System.ArgumentNullException')  
input

<a name='AlifeApi.BusinessRules.RoleGroupModels.RoleGroupServicePg.UpdateRoleGroupUserAsync(AlifeApi.BusinessRules.RoleGroupModels.RoleGroupUserUpdateInputPg)'></a>

## RoleGroupServicePg.UpdateRoleGroupUserAsync(RoleGroupUserUpdateInputPg) Method

修改角色權限使用者

```csharp
public System.Threading.Tasks.Task UpdateRoleGroupUserAsync(AlifeApi.BusinessRules.RoleGroupModels.RoleGroupUserUpdateInputPg input);
```
#### Parameters

<a name='AlifeApi.BusinessRules.RoleGroupModels.RoleGroupServicePg.UpdateRoleGroupUserAsync(AlifeApi.BusinessRules.RoleGroupModels.RoleGroupUserUpdateInputPg).input'></a>

`input` [RoleGroupUserUpdateInputPg](AlifeApi.BusinessRules.RoleGroupModels.RoleGroupUserUpdateInputPg.md 'AlifeApi.BusinessRules.RoleGroupModels.RoleGroupUserUpdateInputPg')

角色權限使用者更新輸入資料

#### Returns
[System.Threading.Tasks.Task](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task 'System.Threading.Tasks.Task')

#### Exceptions

[System.ArgumentNullException](https://docs.microsoft.com/en-us/dotnet/api/System.ArgumentNullException 'System.ArgumentNullException')  
input