#### [AlifeApi.BusinessRules](index.md 'index')
### [AlifeApi.BusinessRules.LogAuditModels](AlifeApi.BusinessRules.LogAuditModels.md 'AlifeApi.BusinessRules.LogAuditModels')

## LogAuditDetailsGetInput Class

日誌稽核列明細查詢

```csharp
public class LogAuditDetailsGetInput : AlifeApi.BusinessRules.Infrastructure.PagedListInput
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; [PagedListInput](AlifeApi.BusinessRules.Infrastructure.PagedListInput.md 'AlifeApi.BusinessRules.Infrastructure.PagedListInput') &#129106; LogAuditDetailsGetInput
### Properties

<a name='AlifeApi.BusinessRules.LogAuditModels.LogAuditDetailsGetInput.Dept'></a>

## LogAuditDetailsGetInput.Dept Property

部門代碼 (對照SysCode的Dept)

```csharp
public string Dept { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.LogAuditModels.LogAuditDetailsGetInput.Month'></a>

## LogAuditDetailsGetInput.Month Property

月份 (格式 yyyy-MM)

```csharp
public string Month { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')