#### [AlifeApi.BusinessRules](index.md 'index')
### [AlifeApi.BusinessRules.RoleGroupModels](AlifeApi.BusinessRules.RoleGroupModels.md 'AlifeApi.BusinessRules.RoleGroupModels')

## RoleGroupUserUpdateInputPg Class

修改角色權限使用者（PostgreSQL 版本）

```csharp
public class RoleGroupUserUpdateInputPg
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; RoleGroupUserUpdateInputPg
### Properties

<a name='AlifeApi.BusinessRules.RoleGroupModels.RoleGroupUserUpdateInputPg.Id'></a>

## RoleGroupUserUpdateInputPg.Id Property

角色ID

```csharp
public string Id { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.RoleGroupModels.RoleGroupUserUpdateInputPg.SiteCode'></a>

## RoleGroupUserUpdateInputPg.SiteCode Property

案場代碼，對應 Sites 表的 SiteCode

```csharp
public string SiteCode { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.RoleGroupModels.RoleGroupUserUpdateInputPg.UserIds'></a>

## RoleGroupUserUpdateInputPg.UserIds Property

使用者清單

```csharp
public System.Collections.Generic.IEnumerable<string> UserIds { get; set; }
```

#### Property Value
[System.Collections.Generic.IEnumerable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Collections.Generic.IEnumerable-1 'System.Collections.Generic.IEnumerable`1')[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Collections.Generic.IEnumerable-1 'System.Collections.Generic.IEnumerable`1')