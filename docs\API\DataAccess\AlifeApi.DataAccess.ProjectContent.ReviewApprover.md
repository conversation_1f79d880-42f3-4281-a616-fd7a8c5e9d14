#### [AlifeApi.DataAccess](index.md 'index')
### [AlifeApi.DataAccess.ProjectContent](AlifeApi.DataAccess.ProjectContent.md 'AlifeApi.DataAccess.ProjectContent')

## ReviewApprover Class

審核人員表，用於儲存每個審核步驟的負責人員資訊。

```csharp
public class ReviewApprover
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; ReviewApprover
### Properties

<a name='AlifeApi.DataAccess.ProjectContent.ReviewApprover.ApproverId'></a>

## ReviewApprover.ApproverId Property

審核人員流水號，主鍵，自動遞增，用於唯一識別審核人員記錄。

```csharp
public int ApproverId { get; set; }
```

#### Property Value
[System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')

<a name='AlifeApi.DataAccess.ProjectContent.ReviewApprover.IsRequired'></a>

## ReviewApprover.IsRequired Property

是否為必要審核人員，true 表示必須審核，false 表示可選，預設為 true。

```csharp
public System.Nullable<bool> IsRequired { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.Boolean](https://docs.microsoft.com/en-us/dotnet/api/System.Boolean 'System.Boolean')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

<a name='AlifeApi.DataAccess.ProjectContent.ReviewApprover.StepId'></a>

## ReviewApprover.StepId Property

步驟流水號，對應 ReviewSteps 表的 StepId。

```csharp
public System.Nullable<int> StepId { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

<a name='AlifeApi.DataAccess.ProjectContent.ReviewApprover.UserInfoId'></a>

## ReviewApprover.UserInfoId Property

使用者編號，對應 UserInfo 表的 UserInfoId。

```csharp
public string UserInfoId { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')