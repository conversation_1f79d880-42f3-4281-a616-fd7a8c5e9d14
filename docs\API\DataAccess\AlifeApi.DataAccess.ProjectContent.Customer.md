#### [AlifeApi.DataAccess](index.md 'index')
### [AlifeApi.DataAccess.ProjectContent](AlifeApi.DataAccess.ProjectContent.md 'AlifeApi.DataAccess.ProjectContent')

## Customer Class

客戶基本資料表

```csharp
public class Customer
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; Customer
### Properties

<a name='AlifeApi.DataAccess.ProjectContent.Customer.Address'></a>

## Customer.Address Property

詳細地址

```csharp
public string Address { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContent.Customer.ArchivePath'></a>

## Customer.ArchivePath Property

簽名檔在NAS上的存檔路徑

```csharp
public string ArchivePath { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContent.Customer.Birthday'></a>

## Customer.Birthday Property

生日

```csharp
public System.Nullable<System.DateOnly> Birthday { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.DateOnly](https://docs.microsoft.com/en-us/dotnet/api/System.DateOnly 'System.DateOnly')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

<a name='AlifeApi.DataAccess.ProjectContent.Customer.Budget'></a>

## Customer.Budget Property

購房預算範圍或數值 (文字描述或數值)

```csharp
public string Budget { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContent.Customer.City'></a>

## Customer.City Property

居住或感興趣的縣市

```csharp
public string City { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContent.Customer.CreatedTime'></a>

## Customer.CreatedTime Property

資料創建時間

```csharp
public System.DateTime CreatedTime { get; set; }
```

#### Property Value
[System.DateTime](https://docs.microsoft.com/en-us/dotnet/api/System.DateTime 'System.DateTime')

<a name='AlifeApi.DataAccess.ProjectContent.Customer.CreatedUserInfoId'></a>

## Customer.CreatedUserInfoId Property

資料創建人員的 UserInfo ID (VARCHAR(15) 外鍵)

```csharp
public string CreatedUserInfoId { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContent.Customer.CustomerId'></a>

## Customer.CustomerId Property

客戶唯一識別碼 (主鍵)

```csharp
public int CustomerId { get; set; }
```

#### Property Value
[System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')

<a name='AlifeApi.DataAccess.ProjectContent.Customer.District'></a>

## Customer.District Property

居住或感興趣的區域

```csharp
public string District { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContent.Customer.Email'></a>

## Customer.Email Property

電子郵件地址 (唯一)

```csharp
public string Email { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContent.Customer.FloorPreference'></a>

## Customer.FloorPreference Property

樓層偏好

```csharp
public string FloorPreference { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContent.Customer.Gender'></a>

## Customer.Gender Property

性別

```csharp
public string Gender { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContent.Customer.HasConsent'></a>

## Customer.HasConsent Property

是否同意個資使用或行銷 (True/False)

```csharp
public System.Nullable<bool> HasConsent { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.Boolean](https://docs.microsoft.com/en-us/dotnet/api/System.Boolean 'System.Boolean')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

<a name='AlifeApi.DataAccess.ProjectContent.Customer.LeadSource'></a>

## Customer.LeadSource Property

客戶來源或得知管道

```csharp
public string LeadSource { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContent.Customer.Name'></a>

## Customer.Name Property

客戶姓名

```csharp
public string Name { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContent.Customer.Occupation'></a>

## Customer.Occupation Property

職業

```csharp
public string Occupation { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContent.Customer.PhoneNumber'></a>

## Customer.PhoneNumber Property

聯絡電話

```csharp
public string PhoneNumber { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContent.Customer.PurchaseConditions'></a>

## Customer.PurchaseConditions Property

其他購屋條件

```csharp
public string PurchaseConditions { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContent.Customer.PurchasePurpose'></a>

## Customer.PurchasePurpose Property

購屋主要用途 (例如: 自住, 投資)

```csharp
public string PurchasePurpose { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContent.Customer.PurchaseTimeline'></a>

## Customer.PurchaseTimeline Property

預計購屋時間範圍

```csharp
public string PurchaseTimeline { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContent.Customer.RequiredLayout'></a>

## Customer.RequiredLayout Property

需求格局 (例如: 3房2廳)

```csharp
public string RequiredLayout { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContent.Customer.RequiredPingArea'></a>

## Customer.RequiredPingArea Property

需求坪數 (文字描述或數值)

```csharp
public string RequiredPingArea { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContent.Customer.RequiredPropertyType'></a>

## Customer.RequiredPropertyType Property

需求的房屋類型 (例如: 預售屋, 新成屋)

```csharp
public string RequiredPropertyType { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContent.Customer.SiteCode'></a>

## Customer.SiteCode Property

客戶主要關聯的案場號碼

```csharp
public string SiteCode { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContent.Customer.UpdatedTime'></a>

## Customer.UpdatedTime Property

資料最後更新時間

```csharp
public System.DateTime UpdatedTime { get; set; }
```

#### Property Value
[System.DateTime](https://docs.microsoft.com/en-us/dotnet/api/System.DateTime 'System.DateTime')

<a name='AlifeApi.DataAccess.ProjectContent.Customer.UpdatedUserInfoId'></a>

## Customer.UpdatedUserInfoId Property

資料最後更新人員的 UserInfo ID (VARCHAR(15) 外鍵)

```csharp
public string UpdatedUserInfoId { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.DataAccess.ProjectContent.Customer.WebPath'></a>

## Customer.WebPath Property

簽名檔的IIS訪問路徑 (指向 C:\alifeImg\...)

```csharp
public string WebPath { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')