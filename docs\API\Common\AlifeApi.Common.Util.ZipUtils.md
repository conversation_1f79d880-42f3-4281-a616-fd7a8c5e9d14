#### [AlifeApi.Common](index.md 'index')
### [AlifeApi.Common.Util](AlifeApi.Common.Util.md 'AlifeApi.Common.Util')

## ZipUtils Class

```csharp
public static class ZipUtils
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; ZipUtils
### Methods

<a name='AlifeApi.Common.Util.ZipUtils.CreateZip(System.Collections.Generic.IEnumerable_string_)'></a>

## ZipUtils.CreateZip(IEnumerable<string>) Method

根據檔案路徑列表創建 Zip 壓縮檔 (使用 SharpCompress)

```csharp
public static byte[] CreateZip(System.Collections.Generic.IEnumerable<string> filePaths);
```
#### Parameters

<a name='AlifeApi.Common.Util.ZipUtils.CreateZip(System.Collections.Generic.IEnumerable_string_).filePaths'></a>

`filePaths` [System.Collections.Generic.IEnumerable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Collections.Generic.IEnumerable-1 'System.Collections.Generic.IEnumerable`1')[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Collections.Generic.IEnumerable-1 'System.Collections.Generic.IEnumerable`1')

要壓縮的檔案路徑集合

#### Returns
[System.Byte](https://docs.microsoft.com/en-us/dotnet/api/System.Byte 'System.Byte')[[]](https://docs.microsoft.com/en-us/dotnet/api/System.Array 'System.Array')  
包含 Zip 檔案內容的 byte 陣列

<a name='AlifeApi.Common.Util.ZipUtils.CreateZipByBase64(System.Collections.Generic.IEnumerable_AlifeApi.Common.Util.ZipUtils.FileList_)'></a>

## ZipUtils.CreateZipByBase64(IEnumerable<FileList>) Method

根據檔名和 Base64 (byte[]) 內容列表創建 Zip 壓縮檔 (使用 SharpCompress)

```csharp
public static byte[] CreateZipByBase64(System.Collections.Generic.IEnumerable<AlifeApi.Common.Util.ZipUtils.FileList> fileContents);
```
#### Parameters

<a name='AlifeApi.Common.Util.ZipUtils.CreateZipByBase64(System.Collections.Generic.IEnumerable_AlifeApi.Common.Util.ZipUtils.FileList_).fileContents'></a>

`fileContents` [System.Collections.Generic.IEnumerable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Collections.Generic.IEnumerable-1 'System.Collections.Generic.IEnumerable`1')[FileList](AlifeApi.Common.Util.ZipUtils.FileList.md 'AlifeApi.Common.Util.ZipUtils.FileList')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Collections.Generic.IEnumerable-1 'System.Collections.Generic.IEnumerable`1')

包含檔名和檔案內容 (byte[]) 的集合

#### Returns
[System.Byte](https://docs.microsoft.com/en-us/dotnet/api/System.Byte 'System.Byte')[[]](https://docs.microsoft.com/en-us/dotnet/api/System.Array 'System.Array')  
包含 Zip 檔案內容的 byte 陣列