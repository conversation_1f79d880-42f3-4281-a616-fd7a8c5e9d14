#### [AlifeApi.BusinessRules](index.md 'index')
### [AlifeApi.BusinessRules.LoginModels](AlifeApi.BusinessRules.LoginModels.md 'AlifeApi.BusinessRules.LoginModels')

## UserLoginInput Class

登入使用的 Request 資料模型

```csharp
public class UserLoginInput
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; UserLoginInput
### Properties

<a name='AlifeApi.BusinessRules.LoginModels.UserLoginInput.Captcha'></a>

## UserLoginInput.Captcha Property

驗證碼

```csharp
public string Captcha { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.LoginModels.UserLoginInput.UserInfoId'></a>

## UserLoginInput.UserInfoId Property

使用者帳號唯一識別值

```csharp
public string UserInfoId { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.LoginModels.UserLoginInput.UserPw'></a>

## UserLoginInput.UserPw Property

使用者密碼

```csharp
public string UserPw { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')