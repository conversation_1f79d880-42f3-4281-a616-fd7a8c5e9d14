#### [AlifeApi.BusinessRules](index.md 'index')
### [AlifeApi.BusinessRules.SysCodeModels](AlifeApi.BusinessRules.SysCodeModels.md 'AlifeApi.BusinessRules.SysCodeModels')

## SysCodeOutput Class

單層 SysCode

```csharp
public class SysCodeOutput
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; SysCodeOutput

### Remarks
這裡的回傳值又部分變成小駝峰...
### Properties

<a name='AlifeApi.BusinessRules.SysCodeModels.SysCodeOutput.IsActive'></a>

## SysCodeOutput.IsActive Property

是否啟用

```csharp
public bool IsActive { get; set; }
```

#### Property Value
[System.Boolean](https://docs.microsoft.com/en-us/dotnet/api/System.Boolean 'System.Boolean')

<a name='AlifeApi.BusinessRules.SysCodeModels.SysCodeOutput.Label'></a>

## SysCodeOutput.Label Property

選項

```csharp
public string Label { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.SysCodeModels.SysCodeOutput.Order'></a>

## SysCodeOutput.Order Property

預設的排序

```csharp
public short Order { get; set; }
```

#### Property Value
[System.Int16](https://docs.microsoft.com/en-us/dotnet/api/System.Int16 'System.Int16')

<a name='AlifeApi.BusinessRules.SysCodeModels.SysCodeOutput.Value'></a>

## SysCodeOutput.Value Property

值

```csharp
public string Value { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')