﻿using AlifeApi.BusinessRules.Infrastructure;
using AlifeApi.BusinessRules.RoleGroupModels;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AlifeApi.BusinessRules.ProjectVersionModels
{
    public class ProjectVersionOutput : ProjectVersionBase,IApiMessage
    {
        /// <summary>
        /// 系統專案版號
        /// </summary>
        [MaxLength(256)]
        public string Version { get; set; }

        /// <summary>
        /// 版本發布時間(DateTime)
        /// </summary>
        public DateTime? LastUpdateTime { get; set; }

        /// <inheritdoc/>
        public Enum Response { get; set; }
    }
}
