﻿using System;
using System.Collections.Generic;

namespace AlifeApi.DataAccess.ProjectContent
{
    /// <summary>
    /// 系統設定表，用於儲存系統的各種設定鍵值對資訊。
    /// </summary>
    public partial class SysSystemSetting
    {
        /// <summary>
        /// 類別，用於區分不同的設定類型。
        /// </summary>
        public string Type { get; set; }
        /// <summary>
        /// 鍵，主鍵的一部分，用於唯一識別系統設定項目。
        /// </summary>
        public string Key { get; set; }
        /// <summary>
        /// 值，儲存設定的具體值，可為空。
        /// </summary>
        public string Value { get; set; }
        /// <summary>
        /// 詳細名稱，描述設定的具體用途或含義。
        /// </summary>
        public string Name { get; set; }
        /// <summary>
        /// 是否啟用，true 表示啟用，false 表示停用，預設為 true。
        /// </summary>
        public bool? IsEnabled { get; set; }
        /// <summary>
        /// 建立者，記錄創建此設定的員工編號，對應 UserInfo 表的 UserInfoId。
        /// </summary>
        public string CreatedUserInfoId { get; set; }
        /// <summary>
        /// 創建時間，記錄設定創建的時間。
        /// </summary>
        public DateTime CreatedTime { get; set; }
        /// <summary>
        /// 更新者，記錄最後更新此設定的員工編號，對應 UserInfo 表的 UserInfoId。
        /// </summary>
        public string UpdatedUserInfoId { get; set; }
        /// <summary>
        /// 更新時間，記錄設定最後更新的時間。
        /// </summary>
        public DateTime UpdatedTime { get; set; }
    }
}
