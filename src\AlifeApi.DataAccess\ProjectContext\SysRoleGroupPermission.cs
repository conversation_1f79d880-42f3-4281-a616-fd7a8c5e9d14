﻿using System;
using System.Collections.Generic;

namespace AlifeApi.DataAccess.ProjectContext
{
    public partial class SysRoleGroupPermission
    {
        /// <summary>
        /// 系統名稱
        /// </summary>
        public string System { get; set; }

        /// <summary>
        /// 角色群組識別編號
        /// </summary>
        public string RoleGroupId { get; set; }

        /// <summary>
        /// 系統項目識別編號
        /// </summary>
        public string FuncId { get; set; }

        public virtual SysMenuFunc SysMenuFunc { get; set; }

        public virtual SysRoleGroup SysRoleGroup { get; set; }

    }
}
