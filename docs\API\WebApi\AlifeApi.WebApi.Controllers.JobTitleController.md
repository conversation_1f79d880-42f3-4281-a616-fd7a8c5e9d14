#### [AlifeApi.WebApi](index.md 'index')
### [AlifeApi.WebApi.Controllers](AlifeApi.WebApi.Controllers.md 'AlifeApi.WebApi.Controllers')

## JobTitleController Class

職位管理控制器

```csharp
public class JobTitleController : AlifeApi.WebApi.Controllers.AuthenticatedController
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; [Microsoft.AspNetCore.Mvc.ControllerBase](https://docs.microsoft.com/en-us/dotnet/api/Microsoft.AspNetCore.Mvc.ControllerBase 'Microsoft.AspNetCore.Mvc.ControllerBase') &#129106; [Microsoft.AspNetCore.Mvc.Controller](https://docs.microsoft.com/en-us/dotnet/api/Microsoft.AspNetCore.Mvc.Controller 'Microsoft.AspNetCore.Mvc.Controller') &#129106; [AlifeApi.WebApi.Controllers.BasicController](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.WebApi.Controllers.BasicController 'AlifeApi.WebApi.Controllers.BasicController') &#129106; [AlifeApi.WebApi.Controllers.AuthenticatedController](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.WebApi.Controllers.AuthenticatedController 'AlifeApi.WebApi.Controllers.AuthenticatedController') &#129106; JobTitleController
### Constructors

<a name='AlifeApi.WebApi.Controllers.JobTitleController.JobTitleController(AlifeApi.BusinessRules.JobTitleModels.JobTitleService)'></a>

## JobTitleController(JobTitleService) Constructor

建構函數

```csharp
public JobTitleController(AlifeApi.BusinessRules.JobTitleModels.JobTitleService jobTitleService);
```
#### Parameters

<a name='AlifeApi.WebApi.Controllers.JobTitleController.JobTitleController(AlifeApi.BusinessRules.JobTitleModels.JobTitleService).jobTitleService'></a>

`jobTitleService` [AlifeApi.BusinessRules.JobTitleModels.JobTitleService](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.BusinessRules.JobTitleModels.JobTitleService 'AlifeApi.BusinessRules.JobTitleModels.JobTitleService')
### Methods

<a name='AlifeApi.WebApi.Controllers.JobTitleController.GetJobTitleDropdownListAsync(AlifeApi.BusinessRules.JobTitleModels.JobTitleDropdownInput)'></a>

## JobTitleController.GetJobTitleDropdownListAsync(JobTitleDropdownInput) Method

取得職位下拉選單

```csharp
public System.Threading.Tasks.Task<Microsoft.AspNetCore.Mvc.ActionResult<System.Collections.Generic.List<AlifeApi.BusinessRules.JobTitleModels.JobTitleDropdownOutput>>> GetJobTitleDropdownListAsync(AlifeApi.BusinessRules.JobTitleModels.JobTitleDropdownInput input);
```
#### Parameters

<a name='AlifeApi.WebApi.Controllers.JobTitleController.GetJobTitleDropdownListAsync(AlifeApi.BusinessRules.JobTitleModels.JobTitleDropdownInput).input'></a>

`input` [AlifeApi.BusinessRules.JobTitleModels.JobTitleDropdownInput](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.BusinessRules.JobTitleModels.JobTitleDropdownInput 'AlifeApi.BusinessRules.JobTitleModels.JobTitleDropdownInput')

查詢條件

#### Returns
[System.Threading.Tasks.Task&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')[Microsoft.AspNetCore.Mvc.ActionResult&lt;](https://docs.microsoft.com/en-us/dotnet/api/Microsoft.AspNetCore.Mvc.ActionResult-1 'Microsoft.AspNetCore.Mvc.ActionResult`1')[System.Collections.Generic.List&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Collections.Generic.List-1 'System.Collections.Generic.List`1')[AlifeApi.BusinessRules.JobTitleModels.JobTitleDropdownOutput](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.BusinessRules.JobTitleModels.JobTitleDropdownOutput 'AlifeApi.BusinessRules.JobTitleModels.JobTitleDropdownOutput')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Collections.Generic.List-1 'System.Collections.Generic.List`1')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/Microsoft.AspNetCore.Mvc.ActionResult-1 'Microsoft.AspNetCore.Mvc.ActionResult`1')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')  
職位下拉選單列表