using AlifeApi.BusinessRules.Infrastructure;
using System.Text.Json.Serialization;

namespace AlifeApi.BusinessRules.CategoryModels
{
    /// <summary>
    /// 大分類查詢輸入 DTO
    /// </summary>
    public class LargeCategoryQueryInput : PagedListInput
    {
        /// <summary>
        /// 大分類名稱 (模糊查詢)
        /// </summary>
        [JsonPropertyName("Name")]
        public string Name { get; set; }

        /// <summary>
        /// 是否啟用 (null表示不篩選)
        /// </summary>
        [JsonPropertyName("IsActive")]
        public bool? IsActive { get; set; }
    }
} 
