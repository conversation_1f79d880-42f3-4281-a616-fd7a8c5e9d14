﻿using System;
using System.Collections.Generic;

namespace AlifeApi.DataAccess.ProjectContent
{
    /// <summary>
    /// 系統功能選單表，用於儲存系統的功能項目及其層級結構。
    /// </summary>
    public partial class SysMenuFunc
    {
        /// <summary>
        /// 系統名稱，主鍵的一部分，用於區分不同系統。
        /// </summary>
        public string System { get; set; }
        /// <summary>
        /// 功能項目識別編號，主鍵的一部分，用於唯一識別功能項目。
        /// </summary>
        public string FuncId { get; set; }
        /// <summary>
        /// 功能項目名稱。
        /// </summary>
        public string FuncName { get; set; }
        /// <summary>
        /// 功能項目排序，用於控制選單顯示順序。
        /// </summary>
        public int FuncOrder { get; set; }
        /// <summary>
        /// 上層功能項目識別編號，用於表示功能項目的層級結構。
        /// </summary>
        public string ParentFuncId { get; set; }
    }
}
