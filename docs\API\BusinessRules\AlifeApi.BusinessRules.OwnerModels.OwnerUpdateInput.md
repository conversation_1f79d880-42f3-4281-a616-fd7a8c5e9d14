#### [AlifeApi.BusinessRules](index.md 'index')
### [AlifeApi.BusinessRules.OwnerModels](AlifeApi.BusinessRules.OwnerModels.md 'AlifeApi.BusinessRules.OwnerModels')

## OwnerUpdateInput Class

更新業主輸入模型

```csharp
public class OwnerUpdateInput
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; OwnerUpdateInput
### Properties

<a name='AlifeApi.BusinessRules.OwnerModels.OwnerUpdateInput.CompanyAddress'></a>

## OwnerUpdateInput.CompanyAddress Property

公司登記地址

```csharp
public string? CompanyAddress { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.OwnerModels.OwnerUpdateInput.CompanyName'></a>

## OwnerUpdateInput.CompanyName Property

公司名稱

```csharp
public string? CompanyName { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.OwnerModels.OwnerUpdateInput.CompanyPhone'></a>

## OwnerUpdateInput.CompanyPhone Property

公司登記電話

```csharp
public string? CompanyPhone { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.OwnerModels.OwnerUpdateInput.ContactPerson'></a>

## OwnerUpdateInput.ContactPerson Property

主要聯絡窗口人員姓名

```csharp
public string? ContactPerson { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.OwnerModels.OwnerUpdateInput.ContactPhone1'></a>

## OwnerUpdateInput.ContactPhone1 Property

主要聯絡電話

```csharp
public string? ContactPhone1 { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.OwnerModels.OwnerUpdateInput.ContactPhone2'></a>

## OwnerUpdateInput.ContactPhone2 Property

次要聯絡電話 (備用)

```csharp
public string? ContactPhone2 { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.OwnerModels.OwnerUpdateInput.Email'></a>

## OwnerUpdateInput.Email Property

聯絡電子郵件地址

```csharp
public string? Email { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.OwnerModels.OwnerUpdateInput.IdentificationNumber'></a>

## OwnerUpdateInput.IdentificationNumber Property

證號 (依人別決定是 統一編號 或 身分證ID)

```csharp
public string? IdentificationNumber { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.OwnerModels.OwnerUpdateInput.MailingAddress'></a>

## OwnerUpdateInput.MailingAddress Property

郵件通訊地址 (若與公司地址不同)

```csharp
public string? MailingAddress { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.OwnerModels.OwnerUpdateInput.PersonType'></a>

## OwnerUpdateInput.PersonType Property

人別 (值為 法人 或 自然人)

```csharp
public string? PersonType { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.OwnerModels.OwnerUpdateInput.ResponsiblePerson'></a>

## OwnerUpdateInput.ResponsiblePerson Property

負責人姓名

```csharp
public string? ResponsiblePerson { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')