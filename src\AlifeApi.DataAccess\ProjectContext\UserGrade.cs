﻿using System;
using System.Collections.Generic;

namespace AlifeApi.DataAccess.ProjectContext
{
    public partial class UserGrade
    {
        public UserGrade()
        {
            InverseParentGrade = new HashSet<UserGrade>();
            UserGradePermission = new HashSet<UserGradePermission>();
            UserInfo = new HashSet<UserInfo>();
        }

        /// <summary>
        /// 職稱代碼
        /// </summary>
        public string Id { get; set; }

        /// <summary>
        /// 職稱
        /// </summary>
        public string GradeName { get; set; }

        /// <summary>
        /// 上層職稱代碼
        /// </summary>
        public string ParentGradeId { get; set; }

        /// <summary>
        /// 是否刪除
        /// </summary>
        public bool IsDisabled { get; set; }

        /// <summary>
        /// 建立者
        /// </summary>
        public string CreatedUserId { get; set; }

        /// <summary>
        /// 建立時間
        /// </summary>
        public DateTime CreatedTime { get; set; }

        /// <summary>
        /// 更新者
        /// </summary>
        public string UpdatedUserId { get; set; }

        /// <summary>
        /// 更新時間
        /// </summary>
        public DateTime UpdatedTime { get; set; }

        public virtual UserGrade ParentGrade { get; set; }

        public virtual ICollection<UserGrade> InverseParentGrade { get; set; }

        public virtual ICollection<UserGradePermission> UserGradePermission { get; set; }

        public virtual ICollection<UserInfo> UserInfo { get; set; }

    }
}
