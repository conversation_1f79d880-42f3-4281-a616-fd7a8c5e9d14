﻿CREATE TABLE [dbo].[SYS_BulletinsAttach] (
    [BLA_Id]         INT             IDENTITY (1, 1) NOT NULL,
    [BL_Id]          INT             NOT NULL,
    [BLA_OName]      NVARCHAR (300)  NULL,
    [BLA_File]       VARBINARY (MAX) NULL,
    [BLA_FileType]   VARCHAR (100)   NULL,
    [BLA_FileSzie]   INT             NULL,
    [BLA_CrUser]     VARCHAR (15)    NULL,
    [BLA_CrDatetime] DATETIME        NULL,
    CONSTRAINT [PK_SYS_BulletinsAttach] PRIMARY KEY CLUSTERED ([BLA_Id] ASC),
    CONSTRAINT [FK_SYS_BulletinsAttach_SYS_Bulletins] FOREIGN KEY ([BL_Id]) REFERENCES [dbo].[SYS_Bulletins] ([BL_Id]) ON DELETE CASCADE ON UPDATE CASCADE
);


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'建立時間', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'SYS_BulletinsAttach', @level2type = N'COLUMN', @level2name = N'BLA_CrDatetime';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'建立者', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'SYS_BulletinsAttach', @level2type = N'COLUMN', @level2name = N'BLA_CrUser';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'檔案類型', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'SYS_BulletinsAttach', @level2type = N'COLUMN', @level2name = N'BLA_FileType';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'檔案', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'SYS_BulletinsAttach', @level2type = N'COLUMN', @level2name = N'BLA_File';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'檔案原名', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'SYS_BulletinsAttach', @level2type = N'COLUMN', @level2name = N'BLA_OName';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'公告流水號', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'SYS_BulletinsAttach', @level2type = N'COLUMN', @level2name = N'BL_Id';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'公告附檔流水號', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'SYS_BulletinsAttach', @level2type = N'COLUMN', @level2name = N'BLA_Id';

