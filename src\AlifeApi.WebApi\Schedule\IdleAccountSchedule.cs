﻿using AlifeApi.BusinessRules.UserInfoModels;
using Quartz;

namespace AlifeApi.WebApi.Schedule
{
    public class IdleAccountSchedule : IJob
    {
        private readonly IServiceProvider _serviceProvider;

        public IdleAccountSchedule(IServiceProvider serviceProvider)
        {
            _serviceProvider = serviceProvider;
        }

        public Task Execute(IJobExecutionContext context)
        {
            using (var scope = _serviceProvider.CreateScope())
            {
                //var Service =
                //    scope.ServiceProvider.GetRequiredService<UserInfoService>();
                //Service.ProcessIdleAccount();
            }

            return Task.CompletedTask;
        }
    }
}
