#### [AlifeApi.Common](index.md 'index')
### [AlifeApi.Common.Util](AlifeApi.Common.Util.md 'AlifeApi.Common.Util')

## JsonUtils Class

用於序列化和反序列化 JSON 工具

```csharp
public static class JsonUtils
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; JsonUtils
### Methods

<a name='AlifeApi.Common.Util.JsonUtils.Deserialize_T_(string)'></a>

## JsonUtils.Deserialize<T>(string) Method

將 JSON 字串反序列化為指定的物件型別

```csharp
public static T Deserialize<T>(string value);
```
#### Type parameters

<a name='AlifeApi.Common.Util.JsonUtils.Deserialize_T_(string).T'></a>

`T`

要反序列化的物件類型
#### Parameters

<a name='AlifeApi.Common.Util.JsonUtils.Deserialize_T_(string).value'></a>

`value` [System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

要反序列化的 JSON 字串

#### Returns
[T](AlifeApi.Common.Util.JsonUtils.md#AlifeApi.Common.Util.JsonUtils.Deserialize_T_(string).T 'AlifeApi.Common.Util.JsonUtils.Deserialize<T>(string).T')  
反序列化後的物件

#### Exceptions

[System.Text.Json.JsonException](https://docs.microsoft.com/en-us/dotnet/api/System.Text.Json.JsonException 'System.Text.Json.JsonException')  
反序列化過程中發生錯誤時擲出

<a name='AlifeApi.Common.Util.JsonUtils.Serialize_T_(T,bool,bool)'></a>

## JsonUtils.Serialize<T>(T, bool, bool) Method

將指定物件序列化為 JSON 字串

```csharp
public static string Serialize<T>(T value, bool isWriteIndented=false, bool isForLogging=false);
```
#### Type parameters

<a name='AlifeApi.Common.Util.JsonUtils.Serialize_T_(T,bool,bool).T'></a>

`T`

要序列化的物件類型
#### Parameters

<a name='AlifeApi.Common.Util.JsonUtils.Serialize_T_(T,bool,bool).value'></a>

`value` [T](AlifeApi.Common.Util.JsonUtils.md#AlifeApi.Common.Util.JsonUtils.Serialize_T_(T,bool,bool).T 'AlifeApi.Common.Util.JsonUtils.Serialize<T>(T, bool, bool).T')

要序列化的物件

<a name='AlifeApi.Common.Util.JsonUtils.Serialize_T_(T,bool,bool).isWriteIndented'></a>

`isWriteIndented` [System.Boolean](https://docs.microsoft.com/en-us/dotnet/api/System.Boolean 'System.Boolean')

if set to `true` [is write indented].

<a name='AlifeApi.Common.Util.JsonUtils.Serialize_T_(T,bool,bool).isForLogging'></a>

`isForLogging` [System.Boolean](https://docs.microsoft.com/en-us/dotnet/api/System.Boolean 'System.Boolean')

若設定為 `true`，則針對日誌記錄進行序列化，並將帶有 [LogIgnore] 屬性的值清空。

#### Returns
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')  
序列化後的 JSON 字串

#### Exceptions

[System.Text.Json.JsonException](https://docs.microsoft.com/en-us/dotnet/api/System.Text.Json.JsonException 'System.Text.Json.JsonException')  
序列化過程中發生錯誤時擲出