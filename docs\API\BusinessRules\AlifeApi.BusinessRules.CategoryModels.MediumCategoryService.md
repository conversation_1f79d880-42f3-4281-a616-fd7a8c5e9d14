#### [AlifeApi.BusinessRules](index.md 'index')
### [AlifeApi.BusinessRules.CategoryModels](AlifeApi.BusinessRules.CategoryModels.md 'AlifeApi.BusinessRules.CategoryModels')

## MediumCategoryService Class

中分類服務

```csharp
public class MediumCategoryService : AlifeApi.BusinessRules.Infrastructure.ServiceBase<AlifeApi.DataAccess.ProjectContent.alifeContext>
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; [AlifeApi.Common.InfrastructureBase](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.Common.InfrastructureBase 'AlifeApi.Common.InfrastructureBase') &#129106; [AlifeApi.BusinessRules.Infrastructure.ServiceBase&lt;](AlifeApi.BusinessRules.Infrastructure.ServiceBase_TDbContext_.md 'AlifeApi.BusinessRules.Infrastructure.ServiceBase<TDbContext>')[AlifeApi.DataAccess.ProjectContent.alifeContext](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.DataAccess.ProjectContent.alifeContext 'AlifeApi.DataAccess.ProjectContent.alifeContext')[&gt;](AlifeApi.BusinessRules.Infrastructure.ServiceBase_TDbContext_.md 'AlifeApi.BusinessRules.Infrastructure.ServiceBase<TDbContext>') &#129106; MediumCategoryService
### Methods

<a name='AlifeApi.BusinessRules.CategoryModels.MediumCategoryService.CreateMediumCategoryAsync(AlifeApi.BusinessRules.CategoryModels.MediumCategoryInput)'></a>

## MediumCategoryService.CreateMediumCategoryAsync(MediumCategoryInput) Method

建立中分類

```csharp
public System.Threading.Tasks.Task<long> CreateMediumCategoryAsync(AlifeApi.BusinessRules.CategoryModels.MediumCategoryInput input);
```
#### Parameters

<a name='AlifeApi.BusinessRules.CategoryModels.MediumCategoryService.CreateMediumCategoryAsync(AlifeApi.BusinessRules.CategoryModels.MediumCategoryInput).input'></a>

`input` [MediumCategoryInput](AlifeApi.BusinessRules.CategoryModels.MediumCategoryInput.md 'AlifeApi.BusinessRules.CategoryModels.MediumCategoryInput')

中分類建立輸入資料

#### Returns
[System.Threading.Tasks.Task&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')[System.Int64](https://docs.microsoft.com/en-us/dotnet/api/System.Int64 'System.Int64')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')  
新建中分類的ID

<a name='AlifeApi.BusinessRules.CategoryModels.MediumCategoryService.DeleteMediumCategoryAsync(long)'></a>

## MediumCategoryService.DeleteMediumCategoryAsync(long) Method

刪除中分類

```csharp
public System.Threading.Tasks.Task DeleteMediumCategoryAsync(long mediumCategoryId);
```
#### Parameters

<a name='AlifeApi.BusinessRules.CategoryModels.MediumCategoryService.DeleteMediumCategoryAsync(long).mediumCategoryId'></a>

`mediumCategoryId` [System.Int64](https://docs.microsoft.com/en-us/dotnet/api/System.Int64 'System.Int64')

中分類ID

#### Returns
[System.Threading.Tasks.Task](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task 'System.Threading.Tasks.Task')

<a name='AlifeApi.BusinessRules.CategoryModels.MediumCategoryService.GetMediumCategoryByIdAsync(long)'></a>

## MediumCategoryService.GetMediumCategoryByIdAsync(long) Method

根據ID取得中分類詳細資料

```csharp
public System.Threading.Tasks.Task<AlifeApi.BusinessRules.CategoryModels.MediumCategoryOutput?> GetMediumCategoryByIdAsync(long mediumCategoryId);
```
#### Parameters

<a name='AlifeApi.BusinessRules.CategoryModels.MediumCategoryService.GetMediumCategoryByIdAsync(long).mediumCategoryId'></a>

`mediumCategoryId` [System.Int64](https://docs.microsoft.com/en-us/dotnet/api/System.Int64 'System.Int64')

中分類ID

#### Returns
[System.Threading.Tasks.Task&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')[MediumCategoryOutput](AlifeApi.BusinessRules.CategoryModels.MediumCategoryOutput.md 'AlifeApi.BusinessRules.CategoryModels.MediumCategoryOutput')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')  
中分類詳細資料

<a name='AlifeApi.BusinessRules.CategoryModels.MediumCategoryService.GetMediumCategoryDropdownAsync(System.Nullable_long_)'></a>

## MediumCategoryService.GetMediumCategoryDropdownAsync(Nullable<long>) Method

取得中分類下拉選單列表

```csharp
public System.Threading.Tasks.Task<System.Collections.Generic.List<AlifeApi.BusinessRules.CategoryModels.MediumCategoryDropdownOutput>> GetMediumCategoryDropdownAsync(System.Nullable<long> largeCategoryId=null);
```
#### Parameters

<a name='AlifeApi.BusinessRules.CategoryModels.MediumCategoryService.GetMediumCategoryDropdownAsync(System.Nullable_long_).largeCategoryId'></a>

`largeCategoryId` [System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.Int64](https://docs.microsoft.com/en-us/dotnet/api/System.Int64 'System.Int64')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

可選的大分類ID，用於篩選特定大分類下的中分類

#### Returns
[System.Threading.Tasks.Task&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')[System.Collections.Generic.List&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Collections.Generic.List-1 'System.Collections.Generic.List`1')[MediumCategoryDropdownOutput](AlifeApi.BusinessRules.CategoryModels.MediumCategoryDropdownOutput.md 'AlifeApi.BusinessRules.CategoryModels.MediumCategoryDropdownOutput')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Collections.Generic.List-1 'System.Collections.Generic.List`1')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')  
啟用的中分類下拉選單列表

<a name='AlifeApi.BusinessRules.CategoryModels.MediumCategoryService.GetMediumCategoryListAsync(AlifeApi.BusinessRules.CategoryModels.MediumCategoryQueryInput)'></a>

## MediumCategoryService.GetMediumCategoryListAsync(MediumCategoryQueryInput) Method

取得中分類列表 (分頁)

```csharp
public System.Threading.Tasks.Task<AlifeApi.BusinessRules.Infrastructure.PagedListOutput<AlifeApi.BusinessRules.CategoryModels.MediumCategoryListOutput>> GetMediumCategoryListAsync(AlifeApi.BusinessRules.CategoryModels.MediumCategoryQueryInput input);
```
#### Parameters

<a name='AlifeApi.BusinessRules.CategoryModels.MediumCategoryService.GetMediumCategoryListAsync(AlifeApi.BusinessRules.CategoryModels.MediumCategoryQueryInput).input'></a>

`input` [MediumCategoryQueryInput](AlifeApi.BusinessRules.CategoryModels.MediumCategoryQueryInput.md 'AlifeApi.BusinessRules.CategoryModels.MediumCategoryQueryInput')

查詢條件

#### Returns
[System.Threading.Tasks.Task&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')[AlifeApi.BusinessRules.Infrastructure.PagedListOutput&lt;](AlifeApi.BusinessRules.Infrastructure.PagedListOutput_TDetail_.md 'AlifeApi.BusinessRules.Infrastructure.PagedListOutput<TDetail>')[MediumCategoryListOutput](AlifeApi.BusinessRules.CategoryModels.MediumCategoryListOutput.md 'AlifeApi.BusinessRules.CategoryModels.MediumCategoryListOutput')[&gt;](AlifeApi.BusinessRules.Infrastructure.PagedListOutput_TDetail_.md 'AlifeApi.BusinessRules.Infrastructure.PagedListOutput<TDetail>')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task-1 'System.Threading.Tasks.Task`1')  
分頁的中分類列表

<a name='AlifeApi.BusinessRules.CategoryModels.MediumCategoryService.UpdateMediumCategoryAsync(long,AlifeApi.BusinessRules.CategoryModels.MediumCategoryInput)'></a>

## MediumCategoryService.UpdateMediumCategoryAsync(long, MediumCategoryInput) Method

更新中分類資訊

```csharp
public System.Threading.Tasks.Task UpdateMediumCategoryAsync(long mediumCategoryId, AlifeApi.BusinessRules.CategoryModels.MediumCategoryInput input);
```
#### Parameters

<a name='AlifeApi.BusinessRules.CategoryModels.MediumCategoryService.UpdateMediumCategoryAsync(long,AlifeApi.BusinessRules.CategoryModels.MediumCategoryInput).mediumCategoryId'></a>

`mediumCategoryId` [System.Int64](https://docs.microsoft.com/en-us/dotnet/api/System.Int64 'System.Int64')

中分類ID

<a name='AlifeApi.BusinessRules.CategoryModels.MediumCategoryService.UpdateMediumCategoryAsync(long,AlifeApi.BusinessRules.CategoryModels.MediumCategoryInput).input'></a>

`input` [MediumCategoryInput](AlifeApi.BusinessRules.CategoryModels.MediumCategoryInput.md 'AlifeApi.BusinessRules.CategoryModels.MediumCategoryInput')

中分類更新輸入資料

#### Returns
[System.Threading.Tasks.Task](https://docs.microsoft.com/en-us/dotnet/api/System.Threading.Tasks.Task 'System.Threading.Tasks.Task')