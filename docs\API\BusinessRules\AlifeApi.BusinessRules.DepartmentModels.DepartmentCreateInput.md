#### [AlifeApi.BusinessRules](index.md 'index')
### [AlifeApi.BusinessRules.DepartmentModels](AlifeApi.BusinessRules.DepartmentModels.md 'AlifeApi.BusinessRules.DepartmentModels')

## DepartmentCreateInput Class

新增部門輸入資料

```csharp
public class DepartmentCreateInput
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; DepartmentCreateInput
### Properties

<a name='AlifeApi.BusinessRules.DepartmentModels.DepartmentCreateInput.CompanyId'></a>

## DepartmentCreateInput.CompanyId Property

公司ID

```csharp
public string CompanyId { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.DepartmentModels.DepartmentCreateInput.DepartmentId'></a>

## DepartmentCreateInput.DepartmentId Property

部門ID

```csharp
public string DepartmentId { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.DepartmentModels.DepartmentCreateInput.Name'></a>

## DepartmentCreateInput.Name Property

部門名稱

```csharp
public string Name { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')