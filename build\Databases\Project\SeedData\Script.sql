﻿INSERT [dbo].[UserDept] ([Id], [DeptName], [ParentDeptId], [LeaderUserId], [IsDisabled], [CreatedUserId], [CreatedTime], [UpdatedUserId], [UpdatedTime]) VALUES (N'001', N'系統廠商', NULL, NULL, 0, N'Administrator', GETDATE(), N'Administrator', GETDATE())
GO

INSERT [dbo].[UserGrade] ([Id], [GradeName], [ParentGradeId], [IsDisabled], [CreatedUserId], [CreatedTime], [UpdatedUserId], [UpdatedTime]) VALUES (N'M1', N'管理員', NULL, 0, N'Administrator', GETDATE(), N'Administrator', GETDATE())
GO

INSERT [dbo].[UserInfo] ([Id], [Name], [Pw], [IdNo], [GradeCode], [DeptId], [Email], [IsEnabled], [EnabledTtime], [LastLoginIP], [LastLoginTime], [LastLogoutTime], [LoginFailedCount], [CreatedUserId], [CreatedTime], [UpdatedUserId], [UpdatedTime]) VALUES (N'Administrator', N'系統管理員', N'811cf6758f4666cd8a8be584f5755bde8eee8e1a', N'A123456789', N'1', N'001', N'<EMAIL>', 1, GETDATE(), N'::1', NULL, NULL, 0, N'Administrator', GETDATE(), N'Administrator', GETDATE())
GO

INSERT [dbo].[SYS_RoleGroup] ([System], [RoleGroupId], [Name], [IsAdmin], [CreatedUserId], [CreatedTime], [UpdatedUserId], [UpdatedTime]) VALUES (N'DotNetCoreTemplate', N'Admin', N'最高管理員', 1, N'Administrator', GETDATE(), N'Administrator', GETDATE())
GO

INSERT [dbo].[SYS_RoleGroupUser] ([System], [RoleGroupId], [UserId]) VALUES (N'DotNetCoreTemplate', N'Admin', N'Administrator')
GO

INSERT [dbo].[SYS_MenuFunc] ([System], [FuncId], [FuncName], [FuncOrder], [ParentFuncId]) VALUES (N'DotNetCoreTemplate', N'AccountSetting', N'帳號設定', 1, N'Menu')
INSERT [dbo].[SYS_MenuFunc] ([System], [FuncId], [FuncName], [FuncOrder], [ParentFuncId]) VALUES (N'DotNetCoreTemplate', N'Menu', N'主菜單', 1, N'')
GO
INSERT [dbo].[SYS_RoleGroupPermission] ([System], [RoleGroupId], [FuncId]) VALUES (N'DotNetCoreTemplate', N'Admin', N'AccountSetting')
GO

INSERT [dbo].[SYS_SystemSetting] ([Type], [Key], [Value], [Name],[IsEnabled] , [CreatedUserId], [CreatedTime], [UpdatedUserId], [UpdatedTime]) VALUES (N'SecuritySetting', N'DefaultPassword', N'mJ8VQW52WTNVpL2+cR8t0g==', N'預設密碼',1, N'Administrator',GETDATE(), N'Administrator',GETDATE())
INSERT [dbo].[SYS_SystemSetting] ([Type], [Key], [Value], [Name],[IsEnabled] , [CreatedUserId], [CreatedTime], [UpdatedUserId], [UpdatedTime]) VALUES (N'SecuritySetting', N'AutoLogoutTime',N'15',N'自動登出時間',1, N'Administrator',GETDATE(), N'Administrator',GETDATE())
INSERT [dbo].[SYS_SystemSetting] ([Type], [Key], [Value], [Name],[IsEnabled] , [CreatedUserId], [CreatedTime], [UpdatedUserId], [UpdatedTime]) VALUES (N'SecuritySetting', N'ChangCycleDays',N'180',N'變更週期天數',1, N'Administrator',GETDATE(), N'Administrator',GETDATE())
INSERT [dbo].[SYS_SystemSetting] ([Type], [Key], [Value], [Name],[IsEnabled] , [CreatedUserId], [CreatedTime], [UpdatedUserId], [UpdatedTime]) VALUES (N'SecuritySetting', N'IdleAccountDays',N'15',N'帳號閒置天數',1, N'Administrator',GETDATE(), N'Administrator',GETDATE())
INSERT [dbo].[SYS_SystemSetting] ([Type], [Key], [Value], [Name],[IsEnabled] , [CreatedUserId], [CreatedTime], [UpdatedUserId], [UpdatedTime]) VALUES (N'SecuritySetting', N'IdleNewAccountDays',N'33',N'新帳號閒置天數',1, N'Administrator',GETDATE(), N'Administrator',GETDATE())
INSERT [dbo].[SYS_SystemSetting] ([Type], [Key], [Value], [Name],[IsEnabled] , [CreatedUserId], [CreatedTime], [UpdatedUserId], [UpdatedTime]) VALUES (N'SecuritySetting', N'LockAccountTime',N'100',N'鎖定時間',1, N'Administrator',GETDATE(), N'Administrator',GETDATE())
INSERT [dbo].[SYS_SystemSetting] ([Type], [Key], [Value], [Name],[IsEnabled] , [CreatedUserId], [CreatedTime], [UpdatedUserId], [UpdatedTime]) VALUES (N'SecuritySetting', N'LoginFailedCount',N'3',N'密碼錯誤次數',1, N'Administrator',GETDATE(), N'Administrator',GETDATE())
INSERT [dbo].[SYS_SystemSetting] ([Type], [Key], [Value], [Name],[IsEnabled] , [CreatedUserId], [CreatedTime], [UpdatedUserId], [UpdatedTime]) VALUES (N'SecuritySetting', N'PasswordUsedBeforeCount',N'3',N'密碼與前幾次相同',1, N'Administrator',GETDATE(), N'Administrator',GETDATE())
GO

INSERT [dbo].[SYS_Type] ([Type], [TypeDesc], [CanEdit]) VALUES (N'problemtype', N'問題種類', 0)
INSERT [dbo].[SYS_Type] ([Type], [TypeDesc], [CanEdit]) VALUES (N'processstatus', N'問題進度', 0)
GO

INSERT [dbo].[SYS_Code] ([Type], [Code], [ParentCode], [CodeDesc], [IsDisabled], [CodeOrder], [CreatedUserId], [CreatedTime], [UpdatedUserId], [UpdatedTime]) VALUES (N'FunctionName', N'CreateUserReport', NULL, N'新增問題回報', 0, 1, N'Administrator', GETDATE(), N'Administrator', GETDATE())
INSERT [dbo].[SYS_Code] ([Type], [Code], [ParentCode], [CodeDesc], [IsDisabled], [CodeOrder], [CreatedUserId], [CreatedTime], [UpdatedUserId], [UpdatedTime]) VALUES (N'FunctionName', N'GetProblemReportList', NULL, N'取得問題回報管理清單', 0, 2, N'Administrator', GETDATE(), N'Administrator', GETDATE())
INSERT [dbo].[SYS_Code] ([Type], [Code], [ParentCode], [CodeDesc], [IsDisabled], [CodeOrder], [CreatedUserId], [CreatedTime], [UpdatedUserId], [UpdatedTime]) VALUES (N'FunctionName', N'ReplyReport', NULL, N'回覆問題回報', 0, 4, N'Administrator', GETDATE(), N'Administrator', GETDATE())
INSERT [dbo].[SYS_Code] ([Type], [Code], [ParentCode], [CodeDesc], [IsDisabled], [CodeOrder], [CreatedUserId], [CreatedTime], [UpdatedUserId], [UpdatedTime]) VALUES (N'problemtype', N'1', NULL, N'問題', 0, 1, N'Administrator', GETDATE(), N'Administrator', GETDATE())
INSERT [dbo].[SYS_Code] ([Type], [Code], [ParentCode], [CodeDesc], [IsDisabled], [CodeOrder], [CreatedUserId], [CreatedTime], [UpdatedUserId], [UpdatedTime]) VALUES (N'problemtype', N'2', NULL, N'需求', 0, 2, N'Administrator', GETDATE(), N'Administrator', GETDATE())
INSERT [dbo].[SYS_Code] ([Type], [Code], [ParentCode], [CodeDesc], [IsDisabled], [CodeOrder], [CreatedUserId], [CreatedTime], [UpdatedUserId], [UpdatedTime]) VALUES (N'processstatus', N'1', NULL, N'未處理', 0, 1, N'Administrator', GETDATE(), N'Administrator', GETDATE())
INSERT [dbo].[SYS_Code] ([Type], [Code], [ParentCode], [CodeDesc], [IsDisabled], [CodeOrder], [CreatedUserId], [CreatedTime], [UpdatedUserId], [UpdatedTime]) VALUES (N'processstatus', N'2', NULL, N'受理中', 0, 2, N'Administrator', GETDATE(), N'Administrator', GETDATE())
INSERT [dbo].[SYS_Code] ([Type], [Code], [ParentCode], [CodeDesc], [IsDisabled], [CodeOrder], [CreatedUserId], [CreatedTime], [UpdatedUserId], [UpdatedTime]) VALUES (N'processstatus', N'3', NULL, N'已處理', 0, 3, N'Administrator', GETDATE(), N'Administrator', GETDATE())
INSERT [dbo].[SYS_Code] ([Type], [Code], [ParentCode], [CodeDesc], [IsDisabled], [CodeOrder], [CreatedUserId], [CreatedTime], [UpdatedUserId], [UpdatedTime]) VALUES (N'processstatus', N'4', NULL, N'不處理', 0, 4, N'Administrator', GETDATE(), N'Administrator', GETDATE())
GO

