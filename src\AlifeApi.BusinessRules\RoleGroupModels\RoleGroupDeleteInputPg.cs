using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;

namespace AlifeApi.BusinessRules.RoleGroupModels
{
    /// <summary>
    /// 刪除角色權限（PostgreSQL 版本）
    /// </summary>
    public class RoleGroupDeleteInputPg
    {
        /// <summary>
        /// 角色ID
        /// </summary>
        [Required]
        public string Id { get; set; }

        /// <summary>
        /// 案場代碼，對應 Sites 表的 SiteCode
        /// </summary>
        [JsonPropertyName("SiteCode")]
        [MaxLength(50)]
        public string SiteCode { get; set; }
    }
} 
