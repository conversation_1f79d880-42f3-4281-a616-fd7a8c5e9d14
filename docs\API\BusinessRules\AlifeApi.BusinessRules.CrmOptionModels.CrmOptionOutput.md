#### [AlifeApi.BusinessRules](index.md 'index')
### [AlifeApi.BusinessRules.CrmOptionModels](AlifeApi.BusinessRules.CrmOptionModels.md 'AlifeApi.BusinessRules.CrmOptionModels')

## CrmOptionOutput Class

CRM選項詳細輸出模型

```csharp
public class CrmOptionOutput
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; CrmOptionOutput
### Properties

<a name='AlifeApi.BusinessRules.CrmOptionModels.CrmOptionOutput.CreatedUserInfoId'></a>

## CrmOptionOutput.CreatedUserInfoId Property

建立者ID

```csharp
public string CreatedUserInfoId { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.CrmOptionModels.CrmOptionOutput.CreatedUserName'></a>

## CrmOptionOutput.CreatedUserName Property

建立者姓名

```csharp
public string? CreatedUserName { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.CrmOptionModels.CrmOptionOutput.CreateTime'></a>

## CrmOptionOutput.CreateTime Property

建立時間

```csharp
public System.DateTime CreateTime { get; set; }
```

#### Property Value
[System.DateTime](https://docs.microsoft.com/en-us/dotnet/api/System.DateTime 'System.DateTime')

<a name='AlifeApi.BusinessRules.CrmOptionModels.CrmOptionOutput.CrmOptionTypeId'></a>

## CrmOptionOutput.CrmOptionTypeId Property

CRM選項類型ID

```csharp
public long CrmOptionTypeId { get; set; }
```

#### Property Value
[System.Int64](https://docs.microsoft.com/en-us/dotnet/api/System.Int64 'System.Int64')

<a name='AlifeApi.BusinessRules.CrmOptionModels.CrmOptionOutput.CrmOptionTypeName'></a>

## CrmOptionOutput.CrmOptionTypeName Property

CRM選項類型名稱

```csharp
public string? CrmOptionTypeName { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.CrmOptionModels.CrmOptionOutput.IsActive'></a>

## CrmOptionOutput.IsActive Property

是否啟用

```csharp
public System.Nullable<bool> IsActive { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.Boolean](https://docs.microsoft.com/en-us/dotnet/api/System.Boolean 'System.Boolean')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

<a name='AlifeApi.BusinessRules.CrmOptionModels.CrmOptionOutput.OptionValue'></a>

## CrmOptionOutput.OptionValue Property

選項值

```csharp
public string OptionValue { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.CrmOptionModels.CrmOptionOutput.SiteCode'></a>

## CrmOptionOutput.SiteCode Property

案場代碼

```csharp
public string SiteCode { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.CrmOptionModels.CrmOptionOutput.SiteCrmOptionId'></a>

## CrmOptionOutput.SiteCrmOptionId Property

CRM選項ID

```csharp
public long SiteCrmOptionId { get; set; }
```

#### Property Value
[System.Int64](https://docs.microsoft.com/en-us/dotnet/api/System.Int64 'System.Int64')

<a name='AlifeApi.BusinessRules.CrmOptionModels.CrmOptionOutput.SiteName'></a>

## CrmOptionOutput.SiteName Property

案場名稱

```csharp
public string? SiteName { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.CrmOptionModels.CrmOptionOutput.SortOrder'></a>

## CrmOptionOutput.SortOrder Property

排序順序

```csharp
public int SortOrder { get; set; }
```

#### Property Value
[System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')

<a name='AlifeApi.BusinessRules.CrmOptionModels.CrmOptionOutput.UpdatedUserInfoId'></a>

## CrmOptionOutput.UpdatedUserInfoId Property

更新者ID

```csharp
public string UpdatedUserInfoId { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.CrmOptionModels.CrmOptionOutput.UpdatedUserName'></a>

## CrmOptionOutput.UpdatedUserName Property

更新者姓名

```csharp
public string? UpdatedUserName { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.CrmOptionModels.CrmOptionOutput.UpdateTime'></a>

## CrmOptionOutput.UpdateTime Property

更新時間

```csharp
public System.DateTime UpdateTime { get; set; }
```

#### Property Value
[System.DateTime](https://docs.microsoft.com/en-us/dotnet/api/System.DateTime 'System.DateTime')