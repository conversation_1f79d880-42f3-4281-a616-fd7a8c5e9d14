#### [AlifeApi.WebApi](index.md 'index')

## AlifeApi.WebApi.Controllers Namespace

| Classes | |
| :--- | :--- |
| [AuthorizationController](AlifeApi.WebApi.Controllers.AuthorizationController.md 'AlifeApi.WebApi.Controllers.AuthorizationController') | 權限驗證(Authorization) |
| [BuildingsController](AlifeApi.WebApi.Controllers.BuildingsController.md 'AlifeApi.WebApi.Controllers.BuildingsController') | 建築物管理 |
| [BulletinsController](AlifeApi.WebApi.Controllers.BulletinsController.md 'AlifeApi.WebApi.Controllers.BulletinsController') | 公告作業 |
| [BusinessDateUpdateInput](AlifeApi.WebApi.Controllers.BusinessDateUpdateInput.md 'AlifeApi.WebApi.Controllers.BusinessDateUpdateInput') | 業務日期更新輸入模型 |
| [CompanyController](AlifeApi.WebApi.Controllers.CompanyController.md 'AlifeApi.WebApi.Controllers.CompanyController') | 公司管理控制器 |
| [CrmOptionsController](AlifeApi.WebApi.Controllers.CrmOptionsController.md 'AlifeApi.WebApi.Controllers.CrmOptionsController') | CRM選項管理 |
| [CustomersController](AlifeApi.WebApi.Controllers.CustomersController.md 'AlifeApi.WebApi.Controllers.CustomersController') | 客戶管理 |
| [DepartmentController](AlifeApi.WebApi.Controllers.DepartmentController.md 'AlifeApi.WebApi.Controllers.DepartmentController') | 部門管理 |
| [DeptController](AlifeApi.WebApi.Controllers.DeptController.md 'AlifeApi.WebApi.Controllers.DeptController') | 部門作業 |
| [FloorsController](AlifeApi.WebApi.Controllers.FloorsController.md 'AlifeApi.WebApi.Controllers.FloorsController') | 樓層管理 |
| [GradeController](AlifeApi.WebApi.Controllers.GradeController.md 'AlifeApi.WebApi.Controllers.GradeController') | 職稱作業 |
| [JobTitleController](AlifeApi.WebApi.Controllers.JobTitleController.md 'AlifeApi.WebApi.Controllers.JobTitleController') | 職位管理控制器 |
| [LargeCategoriesController](AlifeApi.WebApi.Controllers.LargeCategoriesController.md 'AlifeApi.WebApi.Controllers.LargeCategoriesController') | 大分類管理 |
| [LogAuditController](AlifeApi.WebApi.Controllers.LogAuditController.md 'AlifeApi.WebApi.Controllers.LogAuditController') | 日誌稽核管理(LogAudit) |
| [MediumCategoriesController](AlifeApi.WebApi.Controllers.MediumCategoriesController.md 'AlifeApi.WebApi.Controllers.MediumCategoriesController') | 中分類管理 |
| [OwnerController](AlifeApi.WebApi.Controllers.OwnerController.md 'AlifeApi.WebApi.Controllers.OwnerController') | 業主資料管理 API |
| [ParkingSpacesController](AlifeApi.WebApi.Controllers.ParkingSpacesController.md 'AlifeApi.WebApi.Controllers.ParkingSpacesController') | 停車位管理 |
| [PaymentRecordsController](AlifeApi.WebApi.Controllers.PaymentRecordsController.md 'AlifeApi.WebApi.Controllers.PaymentRecordsController') | 收款記錄管理 |
| [ProblemReportController](AlifeApi.WebApi.Controllers.ProblemReportController.md 'AlifeApi.WebApi.Controllers.ProblemReportController') | 問題回報管理(ProblemReport) |
| [ProjectVersionController](AlifeApi.WebApi.Controllers.ProjectVersionController.md 'AlifeApi.WebApi.Controllers.ProjectVersionController') | 系統版本號作業 |
| [PurchaseOrdersController](AlifeApi.WebApi.Controllers.PurchaseOrdersController.md 'AlifeApi.WebApi.Controllers.PurchaseOrdersController') | 買賣預定單管理 |
| [ReviewTaskController](AlifeApi.WebApi.Controllers.ReviewTaskController.md 'AlifeApi.WebApi.Controllers.ReviewTaskController') | 審核流程控制器 |
| [RoleGroupController](AlifeApi.WebApi.Controllers.RoleGroupController.md 'AlifeApi.WebApi.Controllers.RoleGroupController') | 角色權限(RoleGroup) |
| [RoleGroupControllerPg](AlifeApi.WebApi.Controllers.RoleGroupControllerPg.md 'AlifeApi.WebApi.Controllers.RoleGroupControllerPg') | 角色權限管理（PostgreSQL 版本） |
| [SalesController](AlifeApi.WebApi.Controllers.SalesController.md 'AlifeApi.WebApi.Controllers.SalesController') | 銷控表管理 |
| [SecuritySettingController](AlifeApi.WebApi.Controllers.SecuritySettingController.md 'AlifeApi.WebApi.Controllers.SecuritySettingController') | 安全性設定 |
| [SiteController](AlifeApi.WebApi.Controllers.SiteController.md 'AlifeApi.WebApi.Controllers.SiteController') | 案場管理 |
| [SmallCategoriesController](AlifeApi.WebApi.Controllers.SmallCategoriesController.md 'AlifeApi.WebApi.Controllers.SmallCategoriesController') | 小分類管理 |
| [SuppliersController](AlifeApi.WebApi.Controllers.SuppliersController.md 'AlifeApi.WebApi.Controllers.SuppliersController') | 供應商管理 |
| [SysCodeController](AlifeApi.WebApi.Controllers.SysCodeController.md 'AlifeApi.WebApi.Controllers.SysCodeController') | 系統代碼(SysCode) |
| [UnitsController](AlifeApi.WebApi.Controllers.UnitsController.md 'AlifeApi.WebApi.Controllers.UnitsController') | 房屋與車位銷售管理控制器 |
| [UnitStatusUpdateInput](AlifeApi.WebApi.Controllers.UnitStatusUpdateInput.md 'AlifeApi.WebApi.Controllers.UnitStatusUpdateInput') | 房屋單位狀態更新輸入 |
| [UserInfoController](AlifeApi.WebApi.Controllers.UserInfoController.md 'AlifeApi.WebApi.Controllers.UserInfoController') | 使用者操作相關(UserInfo) |
