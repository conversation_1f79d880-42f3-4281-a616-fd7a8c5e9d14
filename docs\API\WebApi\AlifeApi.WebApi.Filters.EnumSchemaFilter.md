#### [AlifeApi.WebApi](index.md 'index')
### [AlifeApi.WebApi.Filters](AlifeApi.WebApi.Filters.md 'AlifeApi.WebApi.Filters')

## EnumSchemaFilter Class

Swagger schema filter to modify description of enum types so they  
show the XML docs attached to each member of the enum.

```csharp
public class EnumSchemaFilter :
Swashbuckle.AspNetCore.SwaggerGen.ISchemaFilter
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; EnumSchemaFilter

Implements [Swashbuckle.AspNetCore.SwaggerGen.ISchemaFilter](https://docs.microsoft.com/en-us/dotnet/api/Swashbuckle.AspNetCore.SwaggerGen.ISchemaFilter 'Swashbuckle.AspNetCore.SwaggerGen.ISchemaFilter')

### Remarks
ref: https://stackoverflow.com/questions/53282170/swaggerui-not-display-enum-summary-description-c-sharp-net-core
### Constructors

<a name='AlifeApi.WebApi.Filters.EnumSchemaFilter.EnumSchemaFilter(System.Xml.Linq.XDocument)'></a>

## EnumSchemaFilter(XDocument) Constructor

Initialize schema filter.

```csharp
public EnumSchemaFilter(System.Xml.Linq.XDocument xmlComments);
```
#### Parameters

<a name='AlifeApi.WebApi.Filters.EnumSchemaFilter.EnumSchemaFilter(System.Xml.Linq.XDocument).xmlComments'></a>

`xmlComments` [System.Xml.Linq.XDocument](https://docs.microsoft.com/en-us/dotnet/api/System.Xml.Linq.XDocument 'System.Xml.Linq.XDocument')

Document containing XML docs for enum members.
### Methods

<a name='AlifeApi.WebApi.Filters.EnumSchemaFilter.Apply(Microsoft.OpenApi.Models.OpenApiSchema,Swashbuckle.AspNetCore.SwaggerGen.SchemaFilterContext)'></a>

## EnumSchemaFilter.Apply(OpenApiSchema, SchemaFilterContext) Method

Apply this schema filter.

```csharp
public void Apply(Microsoft.OpenApi.Models.OpenApiSchema schema, Swashbuckle.AspNetCore.SwaggerGen.SchemaFilterContext context);
```
#### Parameters

<a name='AlifeApi.WebApi.Filters.EnumSchemaFilter.Apply(Microsoft.OpenApi.Models.OpenApiSchema,Swashbuckle.AspNetCore.SwaggerGen.SchemaFilterContext).schema'></a>

`schema` [Microsoft.OpenApi.Models.OpenApiSchema](https://docs.microsoft.com/en-us/dotnet/api/Microsoft.OpenApi.Models.OpenApiSchema 'Microsoft.OpenApi.Models.OpenApiSchema')

Target schema object.

<a name='AlifeApi.WebApi.Filters.EnumSchemaFilter.Apply(Microsoft.OpenApi.Models.OpenApiSchema,Swashbuckle.AspNetCore.SwaggerGen.SchemaFilterContext).context'></a>

`context` [Swashbuckle.AspNetCore.SwaggerGen.SchemaFilterContext](https://docs.microsoft.com/en-us/dotnet/api/Swashbuckle.AspNetCore.SwaggerGen.SchemaFilterContext 'Swashbuckle.AspNetCore.SwaggerGen.SchemaFilterContext')

Schema filter context.

Implements [Apply(OpenApiSchema, SchemaFilterContext)](https://docs.microsoft.com/en-us/dotnet/api/Swashbuckle.AspNetCore.SwaggerGen.ISchemaFilter.Apply#Swashbuckle_AspNetCore_SwaggerGen_ISchemaFilter_Apply_Microsoft_OpenApi_Models_OpenApiSchema,Swashbuckle_AspNetCore_SwaggerGen_SchemaFilterContext_ 'Swashbuckle.AspNetCore.SwaggerGen.ISchemaFilter.Apply(Microsoft.OpenApi.Models.OpenApiSchema,Swashbuckle.AspNetCore.SwaggerGen.SchemaFilterContext)')