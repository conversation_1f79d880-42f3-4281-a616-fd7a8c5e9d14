#### [AlifeApi.BusinessRules](index.md 'index')
### [AlifeApi.BusinessRules.ReviewModels](AlifeApi.BusinessRules.ReviewModels.md 'AlifeApi.BusinessRules.ReviewModels')

## ReviewHistoryOutput Class

審核歷史記錄輸出模型

```csharp
public class ReviewHistoryOutput
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; ReviewHistoryOutput
### Properties

<a name='AlifeApi.BusinessRules.ReviewModels.ReviewHistoryOutput.Action'></a>

## ReviewHistoryOutput.Action Property

操作類型

```csharp
public string Action { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.ReviewModels.ReviewHistoryOutput.Comment'></a>

## ReviewHistoryOutput.Comment Property

評論內容

```csharp
public string Comment { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.ReviewModels.ReviewHistoryOutput.HistoryId'></a>

## ReviewHistoryOutput.HistoryId Property

歷史記錄ID

```csharp
public int HistoryId { get; set; }
```

#### Property Value
[System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')

<a name='AlifeApi.BusinessRules.ReviewModels.ReviewHistoryOutput.StepId'></a>

## ReviewHistoryOutput.StepId Property

審核步驟ID

```csharp
public System.Nullable<int> StepId { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

<a name='AlifeApi.BusinessRules.ReviewModels.ReviewHistoryOutput.StepName'></a>

## ReviewHistoryOutput.StepName Property

審核步驟名稱

```csharp
public string StepName { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.ReviewModels.ReviewHistoryOutput.TaskId'></a>

## ReviewHistoryOutput.TaskId Property

審核任務ID

```csharp
public int TaskId { get; set; }
```

#### Property Value
[System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')

<a name='AlifeApi.BusinessRules.ReviewModels.ReviewHistoryOutput.Timestamp'></a>

## ReviewHistoryOutput.Timestamp Property

操作時間

```csharp
public System.Nullable<System.DateTime> Timestamp { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.DateTime](https://docs.microsoft.com/en-us/dotnet/api/System.DateTime 'System.DateTime')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

<a name='AlifeApi.BusinessRules.ReviewModels.ReviewHistoryOutput.UserInfoId'></a>

## ReviewHistoryOutput.UserInfoId Property

操作者ID

```csharp
public string UserInfoId { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.ReviewModels.ReviewHistoryOutput.UserName'></a>

## ReviewHistoryOutput.UserName Property

操作者姓名

```csharp
public string UserName { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')