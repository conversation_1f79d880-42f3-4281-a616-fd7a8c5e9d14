#### [AlifeApi.WebApi](index.md 'index')
### [AlifeApi.WebApi.Util](AlifeApi.WebApi.Util.md 'AlifeApi.WebApi.Util')

## CaptchaHelper Class

```csharp
public class CaptchaHelper :
AlifeApi.Common.DependencyInjection.ISingletonDependency
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; CaptchaHelper

Implements [AlifeApi.Common.DependencyInjection.ISingletonDependency](https://docs.microsoft.com/en-us/dotnet/api/AlifeApi.Common.DependencyInjection.ISingletonDependency 'AlifeApi.Common.DependencyInjection.ISingletonDependency')
### Fields

<a name='AlifeApi.WebApi.Util.CaptchaHelper.BackGroundColor'></a>

## CaptchaHelper.BackGroundColor Field

背景顏色

```csharp
private static readonly SKColor BackGroundColor;
```

#### Field Value
[SkiaSharp.SKColor](https://docs.microsoft.com/en-us/dotnet/api/SkiaSharp.SKColor 'SkiaSharp.SKColor')

<a name='AlifeApi.WebApi.Util.CaptchaHelper.fonts'></a>

## CaptchaHelper.fonts Field

隨機每個驗證碼字元的字體列表

```csharp
private static readonly List<SKTypeface> fonts;
```

#### Field Value
[System.Collections.Generic.List&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Collections.Generic.List-1 'System.Collections.Generic.List`1')[SkiaSharp.SKTypeface](https://docs.microsoft.com/en-us/dotnet/api/SkiaSharp.SKTypeface 'SkiaSharp.SKTypeface')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Collections.Generic.List-1 'System.Collections.Generic.List`1')

<a name='AlifeApi.WebApi.Util.CaptchaHelper.ImageHeight'></a>

## CaptchaHelper.ImageHeight Field

圖片高度

```csharp
private const int ImageHeight = 30;
```

#### Field Value
[System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')

<a name='AlifeApi.WebApi.Util.CaptchaHelper.ImageWidth'></a>

## CaptchaHelper.ImageWidth Field

圖片寬度

```csharp
private const int ImageWidth = 150;
```

#### Field Value
[System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')

<a name='AlifeApi.WebApi.Util.CaptchaHelper.InterferenceColorMaxDepth'></a>

## CaptchaHelper.InterferenceColorMaxDepth Field

干擾線最深深度，數值越高越亮 越低越暗 0-255

```csharp
private const byte InterferenceColorMaxDepth = 200;
```

#### Field Value
[System.Byte](https://docs.microsoft.com/en-us/dotnet/api/System.Byte 'System.Byte')

<a name='AlifeApi.WebApi.Util.CaptchaHelper.InterferenceColorMinDepth'></a>

## CaptchaHelper.InterferenceColorMinDepth Field

干擾線最淺深度，數值越高越亮 越低越暗 0-255

```csharp
private const byte InterferenceColorMinDepth = 100;
```

#### Field Value
[System.Byte](https://docs.microsoft.com/en-us/dotnet/api/System.Byte 'System.Byte')

<a name='AlifeApi.WebApi.Util.CaptchaHelper.TextColorDepth'></a>

## CaptchaHelper.TextColorDepth Field

文字顏色深度，數值越高越亮 越低越暗 0-255

```csharp
private const int TextColorDepth = 80;
```

#### Field Value
[System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')
### Methods

<a name='AlifeApi.WebApi.Util.CaptchaHelper.GetRandomColor(byte,byte)'></a>

## CaptchaHelper.GetRandomColor(byte, byte) Method

隨機產生顏色

```csharp
private static SkiaSharp.SKColor GetRandomColor(byte minDepth, byte maxDepth);
```
#### Parameters

<a name='AlifeApi.WebApi.Util.CaptchaHelper.GetRandomColor(byte,byte).minDepth'></a>

`minDepth` [System.Byte](https://docs.microsoft.com/en-us/dotnet/api/System.Byte 'System.Byte')

顏色最淺的深度

<a name='AlifeApi.WebApi.Util.CaptchaHelper.GetRandomColor(byte,byte).maxDepth'></a>

`maxDepth` [System.Byte](https://docs.microsoft.com/en-us/dotnet/api/System.Byte 'System.Byte')

顏色最深的深度

#### Returns
[SkiaSharp.SKColor](https://docs.microsoft.com/en-us/dotnet/api/SkiaSharp.SKColor 'SkiaSharp.SKColor')  
顏色

<a name='AlifeApi.WebApi.Util.CaptchaHelper.InterferenceLines(SkiaSharp.SKCanvas,int)'></a>

## CaptchaHelper.InterferenceLines(SKCanvas, int) Method

隨機劃出干擾線

```csharp
private static void InterferenceLines(SkiaSharp.SKCanvas canvas, int lineCount);
```
#### Parameters

<a name='AlifeApi.WebApi.Util.CaptchaHelper.InterferenceLines(SkiaSharp.SKCanvas,int).canvas'></a>

`canvas` [SkiaSharp.SKCanvas](https://docs.microsoft.com/en-us/dotnet/api/SkiaSharp.SKCanvas 'SkiaSharp.SKCanvas')

畫布

<a name='AlifeApi.WebApi.Util.CaptchaHelper.InterferenceLines(SkiaSharp.SKCanvas,int).lineCount'></a>

`lineCount` [System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')

干擾線數量