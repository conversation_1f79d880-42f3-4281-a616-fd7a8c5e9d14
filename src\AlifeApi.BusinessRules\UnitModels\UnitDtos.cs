using System.Collections.Generic;
using System.Text.Json.Serialization;

namespace AlifeApi.BusinessRules.UnitModels
{
    /// <summary>
    /// 房屋銷售統計
    /// </summary>
    public class UnitSalesStatistics
    {
        /// <summary>
        /// 案場編號
        /// </summary>
        public string SiteCode { get; set; } = null!;

        /// <summary>
        /// 可售數量
        /// </summary>
        public int AvailableCount { get; set; }

        /// <summary>
        /// 保留數量
        /// </summary>
        public int ReservedCount { get; set; }

        /// <summary>
        /// 已預訂數量
        /// </summary>
        public int BookedCount { get; set; }

        /// <summary>
        /// 已售數量
        /// </summary>
        public int SoldCount { get; set; }

        /// <summary>
        /// 總數量
        /// </summary>
        public int TotalCount { get; set; }

        /// <summary>
        /// 銷售率 (%)
        /// </summary>
        public decimal SalesRate { get; set; }

        /// <summary>
        /// 可售總表價
        /// </summary>
        public decimal TotalAvailableListPrice { get; set; }

        /// <summary>
        /// 保留總表價
        /// </summary>
        public decimal TotalReservedListPrice { get; set; }
    }

    public class ImportDataInput
    {
        public string SiteCode { get; set; }
        public List<Dictionary<string, object>> Data { get; set; }
    }

    public class ParkingSpaceImportDto
    {
        [JsonPropertyName("案場車位代碼")]
        public string SiteParkingSpaceCode { get; set; }

        [JsonPropertyName("車位代碼")]
        public string ParkingSpaceCode { get; set; }

        [JsonPropertyName("案場名稱")]
        public string SiteName { get; set; }

        [JsonPropertyName("車位層別")]
        public string FloorLabel { get; set; }

        [JsonPropertyName("車位代號")]
        public string SpaceNumber { get; set; }

        [JsonPropertyName("車位類型")]
        public string SpaceType { get; set; }

        [JsonPropertyName("車位模式")]
        public string Status { get; set; }

        [JsonPropertyName("車位產權")]
        public string Ownership { get; set; }

        [JsonPropertyName("車位表價")]
        public decimal? ListPrice { get; set; }

        [JsonPropertyName("車位底價")]
        public decimal? MinimumPrice { get; set; }
    }
} 
