﻿using AlifeApi.BusinessRules.CompanyModels;
using Microsoft.AspNetCore.Mvc;

namespace AlifeApi.WebApi.Controllers
{
    /// <summary>
    /// 公司管理控制器
    /// </summary>
    public class CompanyController : AuthenticatedController
    {
        private readonly CompanyService _companyService;

        /// <summary>
        /// 建構函數
        /// </summary>
        public CompanyController(CompanyService companyService)
        {
            _companyService = companyService;
        }

        /// <summary>
        /// 取得公司下拉選單
        /// </summary>
        /// <returns>公司下拉選單列表</returns>
        [HttpGet]
        public async Task<ActionResult<List<CompanyDropdownOutput>>> GetCompanyDropdownListAsync()
        {
            try
            {
                return await _companyService.GetCompanyDropdownListAsync();
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }
    }
} 
