#### [AlifeApi.BusinessRules](index.md 'index')
### [AlifeApi.BusinessRules.CategoryModels](AlifeApi.BusinessRules.CategoryModels.md 'AlifeApi.BusinessRules.CategoryModels')

## LargeCategoryListOutput Class

大分類列表輸出 DTO (摘要資訊)

```csharp
public class LargeCategoryListOutput
```

Inheritance [System.Object](https://docs.microsoft.com/en-us/dotnet/api/System.Object 'System.Object') &#129106; LargeCategoryListOutput
### Properties

<a name='AlifeApi.BusinessRules.CategoryModels.LargeCategoryListOutput.CreateTime'></a>

## LargeCategoryListOutput.CreateTime Property

建立時間

```csharp
public System.DateTime CreateTime { get; set; }
```

#### Property Value
[System.DateTime](https://docs.microsoft.com/en-us/dotnet/api/System.DateTime 'System.DateTime')

<a name='AlifeApi.BusinessRules.CategoryModels.LargeCategoryListOutput.Description'></a>

## LargeCategoryListOutput.Description Property

大分類描述

```csharp
public string Description { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.CategoryModels.LargeCategoryListOutput.IsActive'></a>

## LargeCategoryListOutput.IsActive Property

是否啟用

```csharp
public System.Nullable<bool> IsActive { get; set; }
```

#### Property Value
[System.Nullable&lt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')[System.Boolean](https://docs.microsoft.com/en-us/dotnet/api/System.Boolean 'System.Boolean')[&gt;](https://docs.microsoft.com/en-us/dotnet/api/System.Nullable-1 'System.Nullable`1')

<a name='AlifeApi.BusinessRules.CategoryModels.LargeCategoryListOutput.LargeCategoryId'></a>

## LargeCategoryListOutput.LargeCategoryId Property

大分類ID

```csharp
public long LargeCategoryId { get; set; }
```

#### Property Value
[System.Int64](https://docs.microsoft.com/en-us/dotnet/api/System.Int64 'System.Int64')

<a name='AlifeApi.BusinessRules.CategoryModels.LargeCategoryListOutput.Name'></a>

## LargeCategoryListOutput.Name Property

大分類名稱

```csharp
public string Name { get; set; }
```

#### Property Value
[System.String](https://docs.microsoft.com/en-us/dotnet/api/System.String 'System.String')

<a name='AlifeApi.BusinessRules.CategoryModels.LargeCategoryListOutput.SortOrder'></a>

## LargeCategoryListOutput.SortOrder Property

排序順序

```csharp
public int SortOrder { get; set; }
```

#### Property Value
[System.Int32](https://docs.microsoft.com/en-us/dotnet/api/System.Int32 'System.Int32')