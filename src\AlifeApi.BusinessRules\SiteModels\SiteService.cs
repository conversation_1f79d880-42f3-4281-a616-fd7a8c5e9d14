﻿using AlifeApi.BusinessRules.Infrastructure;
using AlifeApi.DataAccess.ProjectContent;
using Microsoft.EntityFrameworkCore;

namespace AlifeApi.BusinessRules.SiteModels
{
    /// <summary>
    /// 案場服務
    /// </summary>
    public class SiteService : ServiceBase<alifeContext>
    {
        /// <summary>
        /// 建構函數
        /// </summary>
        public SiteService(IServiceProvider serviceProvider, alifeContext dbContext) 
            : base(serviceProvider, dbContext)
        {
        }

        /// <summary>
        /// 取得案場列表
        /// </summary>
        /// <param name="input">查詢條件</param>
        /// <returns>案場列表</returns>
        public async Task<PagedListOutput<SiteOutput>> GetSiteListAsync(SiteListGetInput input)
        {
            ArgumentNullException.ThrowIfNull(input, nameof(input));

            var query = Db.Sites.AsQueryable();

            // 先套用投影 (Select)
            var projectedQuery = query
                .Select(x => new SiteOutput
                {
                    SiteCode = x.SiteCode,
                    CompanyId = x.CompanyId,
                    SiteName = x.SiteName,
                    PromotionType = x.PromotionType,
                    Chairman = x.Chairman,
                    ViceChairman = x.ViceChairman,
                    ProjectManager = x.ProjectManager,
                    DeputyProjectManager = x.DeputyProjectManager,
                    BusinessIds = x.BusinessIds,
                    RunnerIds = x.RunnerIds,
                    ReceptionCenter = x.ReceptionCenter,
                    SitePhone = x.SitePhone,
                    Broker = x.Broker,
                    City = x.City,
                    District = x.District,
                    Developer = x.Developer,
                    SiteLocation = x.SiteLocation,
                    Address = $"{x.City}{x.District}{x.SiteLocation}",
                    LandArea = x.LandArea,
                    AboveGroundFloors = x.AboveGroundFloors,
                    BelowGroundFloors = x.BelowGroundFloors,
                    Zoning = x.Zoning,
                    PublicFacilityRatio = x.PublicFacilityRatio,
                    Structure = x.Structure,
                    PlannedResidentialUnits = x.PlannedResidentialUnits,
                    PlannedStoreUnits = x.PlannedStoreUnits,
                    PlannedParkingSpaces = x.PlannedParkingSpaces,
                    UnitSize = x.UnitSize,
                    TotalSalePrice = x.TotalSalePrice,
                    ParkingType = x.ParkingType,
                    ContractPeriod = x.ContractPeriod,
                    ExtensionPeriod = x.ExtensionPeriod,
                    SellableTotalPrice = x.SellableTotalPrice,
                    ServiceFeeCalculation = x.ServiceFeeCalculation,
                    ServiceFeeRate = x.ServiceFeeRate,
                    ReserveAmount = x.ReserveAmount,
                    AdvertisingBudget = x.AdvertisingBudget,
                    AdvertisingBudgetRate = x.AdvertisingBudgetRate,
                    ExcessPriceAllocation = x.ExcessPriceAllocation,
                    ContractedAmount = x.ContractedAmount,
                    ControlReserveRate = x.ControlReserveRate,
                    PaidAmount = x.PaidAmount,
                    CreatedUserId = x.CreatedUserInfoId, // 保留 User ID 以供後續查詢
                    CreatedTime = x.CreatedTime,
                    UpdatedUserId = x.UpdatedUserInfoId, // 保留 User ID 以供後續查詢
                    UpdatedTime = x.UpdatedTime
                });

            // 對 IQueryable<SiteOutput> 呼叫 ToPagedListOutputAsync
            var pagedResult = await projectedQuery.ToPagedListOutputAsync(input);

            // 查詢並設定當前頁面資料的使用者名稱
            if (pagedResult.Details.Any())
            {
                var userIds = pagedResult.Details
                                        .SelectMany(x => new[] { x.CreatedUserId, x.UpdatedUserId })
                                        .Where(id => !string.IsNullOrEmpty(id))
                                        .Distinct()
                                        .ToList();

                if (userIds.Any())
                {
                    var users = await Db.UserInfos
                        .Where(x => userIds.Contains(x.UserInfoId))
                        .Select(x => new { x.UserInfoId, x.Name })
                        .ToListAsync();

                    var userMap = users.ToDictionary(u => u.UserInfoId, u => u.Name);

                    foreach (var site in pagedResult.Details)
                    {
                        if (!string.IsNullOrEmpty(site.CreatedUserId) && userMap.TryGetValue(site.CreatedUserId, out var createdName))
                        {
                            site.CreatedUserName = createdName;
                        }
                        if (!string.IsNullOrEmpty(site.UpdatedUserId) && userMap.TryGetValue(site.UpdatedUserId, out var updatedName))
                        {
                            site.UpdatedUserName = updatedName;
                        }
                    }
                }
            }

            return pagedResult;
        }

        /// <summary>
        /// 根據代碼取得案場
        /// </summary>
        /// <param name="siteCode">案場代碼</param>
        /// <returns>案場資訊</returns>
        public async Task<SiteOutput> GetSiteByCodeAsync(string siteCode)
        {
            var site = await Db.Sites
                .Where(x => x.SiteCode == siteCode)
                .Select(x => new SiteOutput
                {
                    SiteCode = x.SiteCode,
                    CompanyId = x.CompanyId,
                    SiteName = x.SiteName,
                    PromotionType = x.PromotionType,
                    Chairman = x.Chairman,
                    ViceChairman = x.ViceChairman,
                    ProjectManager = x.ProjectManager,
                    DeputyProjectManager = x.DeputyProjectManager,
                    BusinessIds = x.BusinessIds,
                    RunnerIds = x.RunnerIds,
                    ReceptionCenter = x.ReceptionCenter,
                    SitePhone = x.SitePhone,
                    Broker = x.Broker,
                    City = x.City,
                    District = x.District,
                    Developer = x.Developer,
                    SiteLocation = x.SiteLocation,
                    Address = $"{x.City}{x.District}{x.SiteLocation}",
                    LandArea = x.LandArea,
                    AboveGroundFloors = x.AboveGroundFloors,
                    BelowGroundFloors = x.BelowGroundFloors,
                    Zoning = x.Zoning,
                    PublicFacilityRatio = x.PublicFacilityRatio,
                    Structure = x.Structure,
                    PlannedResidentialUnits = x.PlannedResidentialUnits,
                    PlannedStoreUnits = x.PlannedStoreUnits,
                    PlannedParkingSpaces = x.PlannedParkingSpaces,
                    UnitSize = x.UnitSize,
                    TotalSalePrice = x.TotalSalePrice,
                    ParkingType = x.ParkingType,
                    ContractPeriod = x.ContractPeriod,
                    ExtensionPeriod = x.ExtensionPeriod,
                    SellableTotalPrice = x.SellableTotalPrice,
                    ServiceFeeCalculation = x.ServiceFeeCalculation,
                    ServiceFeeRate = x.ServiceFeeRate,
                    ReserveAmount = x.ReserveAmount,
                    AdvertisingBudget = x.AdvertisingBudget,
                    AdvertisingBudgetRate = x.AdvertisingBudgetRate,
                    ExcessPriceAllocation = x.ExcessPriceAllocation,
                    ContractedAmount = x.ContractedAmount,
                    ControlReserveRate = x.ControlReserveRate,
                    PaidAmount = x.PaidAmount,
                    CreatedUserId = x.CreatedUserInfoId,
                    CreatedTime = x.CreatedTime,
                    UpdatedUserId = x.UpdatedUserInfoId,
                    UpdatedTime = x.UpdatedTime
                })
                .FirstOrDefaultAsync();

            if (site == null)
            {
                return null;
            }

            // 獲取使用者名稱
            var userIds = new[] { site.CreatedUserId, site.UpdatedUserId }.Distinct().ToList();
            var users = await Db.UserInfos
                .Where(x => userIds.Contains(x.UserInfoId))
                .Select(x => new { x.UserInfoId, x.Name })
                .ToListAsync();

            site.CreatedUserName = users.FirstOrDefault(x => x.UserInfoId == site.CreatedUserId)?.Name;
            site.UpdatedUserName = users.FirstOrDefault(x => x.UserInfoId == site.UpdatedUserId)?.Name;

            return site;
        }

        /// <summary>
        /// 創建案場
        /// </summary>
        /// <param name="input">案場創建輸入</param>
        public async Task CreateSiteAsync(SiteCreateInput input)
        {
            ArgumentNullException.ThrowIfNull(input, nameof(input));

            // 檢查案場代碼是否已存在
            if (await Db.Sites.AnyAsync(x => x.SiteCode == input.SiteCode))
            {
                throw new Exception($"案場代碼 {input.SiteCode} 已存在");
            }

            var site = new Site
            {
                SiteCode = input.SiteCode,
                CompanyId = input.CompanyId,
                SiteName = input.SiteName,
                PromotionType = input.PromotionType,
                Chairman = input.Chairman,
                ViceChairman = input.ViceChairman,
                ProjectManager = input.ProjectManager,
                DeputyProjectManager = input.DeputyProjectManager,
                BusinessIds = input.BusinessIds,
                RunnerIds = input.RunnerIds,
                ReceptionCenter = input.ReceptionCenter,
                SitePhone = input.SitePhone,
                Broker = input.Broker,
                City = input.City,
                District = input.District,
                Developer = input.Developer,
                SiteLocation = input.SiteLocation,
                LandArea = input.LandArea,
                AboveGroundFloors = input.AboveGroundFloors,
                BelowGroundFloors = input.BelowGroundFloors,
                Zoning = input.Zoning,
                PublicFacilityRatio = input.PublicFacilityRatio,
                Structure = input.Structure,
                PlannedResidentialUnits = input.PlannedResidentialUnits,
                PlannedStoreUnits = input.PlannedStoreUnits,
                PlannedParkingSpaces = input.PlannedParkingSpaces,
                UnitSize = input.UnitSize,
                TotalSalePrice = input.TotalSalePrice,
                ParkingType = input.ParkingType,
                ContractPeriod = input.ContractPeriod,
                ExtensionPeriod = input.ExtensionPeriod,
                SellableTotalPrice = input.SellableTotalPrice,
                ServiceFeeCalculation = input.ServiceFeeCalculation,
                ServiceFeeRate = input.ServiceFeeRate,
                ReserveAmount = input.ReserveAmount,
                AdvertisingBudget = input.AdvertisingBudget,
                AdvertisingBudgetRate = input.AdvertisingBudgetRate,
                ExcessPriceAllocation = input.ExcessPriceAllocation,
                ContractedAmount = input.ContractedAmount,
                ControlReserveRate = input.ControlReserveRate,
                PaidAmount = input.PaidAmount,
                CreatedUserInfoId = CurrentUser.UserId,
                CreatedTime = DateTime.Now,
                UpdatedUserInfoId = CurrentUser.UserId,
                UpdatedTime = DateTime.Now
            };

            Db.Sites.Add(site);
            await Db.SaveChangesAsync();
        }

        /// <summary>
        /// 更新案場
        /// </summary>
        /// <param name="input">案場更新輸入</param>
        public async Task UpdateSiteAsync(SiteUpdateInput input)
        {
            ArgumentNullException.ThrowIfNull(input, nameof(input));

            var site = await Db.Sites.FirstOrDefaultAsync(x => x.SiteCode == input.SiteCode);
            if (site == null)
            {
                throw new Exception($"找不到案場代碼 {input.SiteCode}");
            }

            site.CompanyId = input.CompanyId;
            site.SiteName = input.SiteName;
            site.PromotionType = input.PromotionType;
            site.Chairman = input.Chairman;
            site.ViceChairman = input.ViceChairman;
            site.ProjectManager = input.ProjectManager;
            site.DeputyProjectManager = input.DeputyProjectManager;
            site.BusinessIds = input.BusinessIds;
            site.RunnerIds = input.RunnerIds;
            site.ReceptionCenter = input.ReceptionCenter;
            site.SitePhone = input.SitePhone;
            site.Broker = input.Broker;
            site.City = input.City;
            site.District = input.District;
            site.Developer = input.Developer;
            site.SiteLocation = input.SiteLocation;
            site.LandArea = input.LandArea;
            site.AboveGroundFloors = input.AboveGroundFloors;
            site.BelowGroundFloors = input.BelowGroundFloors;
            site.Zoning = input.Zoning;
            site.PublicFacilityRatio = input.PublicFacilityRatio;
            site.Structure = input.Structure;
            site.PlannedResidentialUnits = input.PlannedResidentialUnits;
            site.PlannedStoreUnits = input.PlannedStoreUnits;
            site.PlannedParkingSpaces = input.PlannedParkingSpaces;
            site.UnitSize = input.UnitSize;
            site.TotalSalePrice = input.TotalSalePrice;
            site.ParkingType = input.ParkingType;
            site.ContractPeriod = input.ContractPeriod;
            site.ExtensionPeriod = input.ExtensionPeriod;
            site.SellableTotalPrice = input.SellableTotalPrice;
            site.ServiceFeeCalculation = input.ServiceFeeCalculation;
            site.ServiceFeeRate = input.ServiceFeeRate;
            site.ReserveAmount = input.ReserveAmount;
            site.AdvertisingBudget = input.AdvertisingBudget;
            site.AdvertisingBudgetRate = input.AdvertisingBudgetRate;
            site.ExcessPriceAllocation = input.ExcessPriceAllocation;
            site.ContractedAmount = input.ContractedAmount;
            site.ControlReserveRate = input.ControlReserveRate;
            site.PaidAmount = input.PaidAmount;
            site.UpdatedUserInfoId = CurrentUser.UserId;
            site.UpdatedTime = DateTime.Now;

            await Db.SaveChangesAsync();
        }

        /// <summary>
        /// 刪除案場
        /// </summary>
        /// <param name="siteCode">案場代碼</param>
        public async Task DeleteSiteAsync(string siteCode)
        {
            var site = await Db.Sites.FirstOrDefaultAsync(x => x.SiteCode == siteCode);
            if (site == null)
            {
                throw new Exception($"找不到案場代碼 {siteCode}");
            }

            // 檢查是否有關聯的角色
            if (await Db.SysRoleGroups.AnyAsync(x => x.SiteCode == siteCode))
            {
                throw new Exception($"案場 {siteCode} 已被角色使用，無法刪除");
            }

            Db.Sites.Remove(site);
            await Db.SaveChangesAsync();
        }

        /// <summary>
        /// 取得案場下拉選單
        /// </summary>
        /// <returns>案場下拉選單列表</returns>
        public async Task<List<SiteDropdownOutput>> GetSiteDropdownListAsync()
        {
            return await Db.Sites
                .OrderBy(x => x.SiteCode)
                .Select(x => new SiteDropdownOutput
                {
                    Name = $"{x.SiteName}",
                    Value = x.SiteCode
                })
                .ToListAsync();
        }
    }
} 
